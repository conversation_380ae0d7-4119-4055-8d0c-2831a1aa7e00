package com.shuidihuzhu.cf.cfgrowthtoolapi.listeners;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.PreposeBindCaseEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IClewPreproseMaterialService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.BdTaskFactory;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolAsyncPoolConstants;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-09-25 2:08 下午
 **/
@Service
@Slf4j
public class ProposeBindCaseEventListener {

    @Autowired
    IClewPreproseMaterialService clewPreproseMaterialService;

    @Autowired
    private ICfBdCaseInfoService bdCaseInfoService;

    @Autowired
    private BdTaskFactory bdTaskFactory;

    @Async(GrowthtoolAsyncPoolConstants.SYNC_ORG_MEMBER)
    @EventListener
    public void preposeBindCase(PreposeBindCaseEvent preposeBindCase) {
        Long preposeId = preposeBindCase.getPreposeId();
        if (preposeId == null || preposeId <= 0) {
            return;
        }
        ClewCrowdfundingReportRelation preposeRelation = clewPreproseMaterialService.getByPreposeMaterialId(preposeId);
        if (preposeRelation == null || preposeRelation.getInfoId() == null || preposeRelation.getInfoId() <= 0) {
            return;
        }
        //确保是线下的顾问发起的
        if (StringUtils.isBlank(preposeRelation.getUniqueCode()) && preposeRelation.getInfoId() <= 0) {
            return;
        }
        //通过bd_case_info过滤下
        CfBdCaseInfoDo caseInfoDo = bdCaseInfoService.getBdCaseInfoByInfoId(preposeRelation.getInfoId().intValue());
        if (caseInfoDo == null) {
            log.info("案例:{}未归属", preposeRelation.getInfoId());
            return;
        }
        log.info("存在报备id绑定案例:{}", preposeRelation);
        //是否有代录入绑定案例
        bdTaskFactory.updateBdTaskWhenComplete(BdTaskContext.builder()
                .taskTypeEnumList(List.of(CrmBdTaskDO.TaskTypeEnum.scan_report))
                .cfBdCaseInfoDo(caseInfoDo)
                .reportRelation(preposeRelation)
                .caseId(caseInfoDo.getCaseId())
                .build());
    }


}
