package com.shuidihuzhu.cf.cfgrowthtoolapi.listeners;

import brave.Tracing;
import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.CrmForThirdPlatformFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.ICfVolunteerFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.bdcrm.ICfBdCrmObjectiveFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.bdcrm.target.TargetCycleFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfGrowthtoolApproveService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IQyWxUserInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCrmUserExtService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.IBdMemberSnapshotService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.SyncOrgMemberToQyWechatService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.volunteer.VolunteerSyncService;
import com.shuidihuzhu.cf.dao.bdcrm.CfClewTaskBdDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;


/**
 * 监听 顾问信息变更事件
 * @author: wanghui
 * @create: 2019/05/30 上午10:29
 */
@Component
@Slf4j
public class CrowdfundingVolunteerChangeListener implements ApplicationListener<CrowdfundingVolunteerChangeEvent> {

    @Autowired
    private ICfVolunteerService cfVolunteerServiceImpl;
    @Autowired
    private IQyWxUserInfoService qyWxUserInfoService;
    @Autowired
    private ICfCrmUserExtService cfCrmUserExtService;
    @Resource(name = "cfOlapCaseStat")
    private RedissonHandler cfOlapRedissonHandler;
    @Autowired
    private ICfVolunteerFacade cfVolunteerFacade;
    @Autowired
    private ApolloService apolloService;
    @Autowired
    private ICrmOrganizationRelationService organizationRelationService;
    @Autowired
    private CrmForThirdPlatformFacade crmForThirdPlatformFacade;
    @Autowired
    private IBdMemberSnapshotService bdMemberSnapshotService;
    @Autowired
    private IOfficialAnnounceService officialAnnounceService;
    @Autowired
    private VolunteerSyncService volunteerSyncService;
    @Autowired
    private ICfBdCrmObjectiveFacade cfBdCrmObjectiveFacade;
    @Autowired
    private ICfGrowthtoolApproveService cfGrowthtoolApproveService;
    @Autowired
    private CfClewTaskBdDao cfClewTaskBdDao;

    @Autowired
    private Tracing tracing;

    @Autowired
    private SyncOrgMemberToQyWechatService syncOrgMemberToQyWechatService;

    @Autowired
    private TargetCycleFacade targetCycleFacade;

    @Override
    public void onApplicationEvent(CrowdfundingVolunteerChangeEvent crowdfundingVolunteerChangeEvent) {
        CrowdfundingVolunteer crowdfundingVolunteer = crowdfundingVolunteerChangeEvent.getCrowdfundingVolunteer();
        // 修改 预分配顾问表是否is_delete 的逻辑
        CrowdfundingVolunteer crowdfundingVolunteerFromDb = cfVolunteerServiceImpl.getCrowdfundingVolunteerById(crowdfundingVolunteer.getId());
        log.info("人员信息有修改,信息为:{}", JSON.toJSONString(crowdfundingVolunteer));
        // 处理 登录异常
        qyWxUserInfoService.updateCfCrmLoginExceptionIsDelete(crowdfundingVolunteerFromDb);
        //通过事件中获取人员的入离职状态,不要通过db中获取
        int workStatus = crowdfundingVolunteer.getWorkStatus();
        //线下人员且前端传入的是离职状态
        if (crowdfundingVolunteerFromDb.getVolunteerType() == CrowdfundingVolunteerEnum.VolunteerType.TEAM.getValue()
                && workStatus == CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue()) {
            //修改离职时间
            bdMemberSnapshotService.updateWhenLevel(crowdfundingVolunteerFromDb.getUniqueCode(), crowdfundingVolunteer.getLeaveTime());
            bdMemberSnapshotService.saveOrUpdateWhenDimission(crowdfundingVolunteerChangeEvent);
            officialAnnounceService.updateWorkStatus(crowdfundingVolunteer.getUniqueCode(), crowdfundingVolunteer.getWorkStatus());
        }
        // 判断是否离职
        if (StringUtils.isNotBlank(crowdfundingVolunteerFromDb.getUniqueCode())
                && workStatus == CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue()) {
            log.info("人员:{}离职", crowdfundingVolunteerFromDb.getUniqueCode());
            targetCycleFacade.dimissionOrgMemberSnapshotByMember(crowdfundingVolunteerFromDb);
            // 离职需要 将cf_crm_user_ext表 逻辑删除
            cfCrmUserExtService.updateIsDelete(crowdfundingVolunteerFromDb.getUniqueCode(), BooleanUtils.toInteger(true));
            // 离职需要将缓存删掉，目的是不会出现在 gpd附近的顾问列表中
            cfOlapRedissonHandler.del(String.format(GeneralConstant.cfCrmBdLocationV2, crowdfundingVolunteerFromDb.getUniqueCode()));
            //离职需要自动解绑
            organizationRelationService.autoDeleteMemberId(crowdfundingVolunteerFromDb.getUniqueCode(), "由于人员离职自动解绑" + crowdfundingVolunteerFromDb.getVolunteerName());
            // 离职 自动把 mis解绑 （将mis设置 为空）
            cfVolunteerServiceImpl.updateMisBlankById(crowdfundingVolunteerFromDb.getId());
            if (StringUtils.isNotBlank(crowdfundingVolunteerFromDb.getUniqueCode())) {
                cfClewTaskBdDao.updateIsDeleteByUniqueCode(crowdfundingVolunteerFromDb.getUniqueCode());
            }
            // 将 待审核的数据 都置为已驳回
            cfGrowthtoolApproveService.autoRejectForDimission(crowdfundingVolunteerFromDb.getUniqueCode());
            cfGrowthtoolApproveService.notifyHasUndoApprove(crowdfundingVolunteerFromDb);
            //企业微信账号离职
            syncOrgMemberToQyWechatService.deleteMember(crowdfundingVolunteer);
        } else if (StringUtils.isNotBlank(crowdfundingVolunteerFromDb.getUniqueCode())
                && workStatus == CrowdfundingVolunteerEnum.WorkStatusEnum.ON_THE_JOB.getValue()) {
            // 在职需要 将cf_crm_user_ext表 恢复
            cfCrmUserExtService.updateIsDelete(crowdfundingVolunteerFromDb.getUniqueCode(), BooleanUtils.toInteger(false));
            //根据入职时间重新设置人员是新入职还是老员工
            bdMemberSnapshotService.updateSnapshotNewStaff(crowdfundingVolunteer);
            //判断是否需要添加或者新增
            CompletableFuture.runAsync(tracing.currentTraceContext().wrap(() -> syncOrgMemberToQyWechatService.addMemberWhenVolunteerChange(crowdfundingVolunteer)));
        }

        //mis非空,jobNum为空时
        if (StringUtils.isNotBlank(crowdfundingVolunteerFromDb.getMis()) && StringUtils.isBlank(crowdfundingVolunteerFromDb.getJobNum())) {
            log.info("触发实时同步xrxs信息,mis:{}", crowdfundingVolunteerFromDb.getMis());
            volunteerSyncService.syncOneVolunteerFromXrxs(crowdfundingVolunteerFromDb, false);
        }


        if (crowdfundingVolunteerFromDb.getVolunteerType() == CrowdfundingVolunteerEnum.VolunteerType.TEAM.getValue()){
            // 线下人员 离职  普通顾问 且 虚拟号不为空 则 回收虚拟号 并且将虚拟号绑定到省级经理头上
            if (apolloService.enableVirtualPhone()){
                cfVolunteerFacade.assignOrRecoverVEncryptMobiles(crowdfundingVolunteerFromDb);
            }else {
                // 线下顾问信息只要有变动 这里就 更新一下主页信息中的 手机号
                cfCrmUserExtService.updateEncryptPhoneC(crowdfundingVolunteerFromDb.getUniqueCode(),crowdfundingVolunteerFromDb.getMobile());
            }
        }
        crmForThirdPlatformFacade.callThirdInterfaceForMemberChange(crowdfundingVolunteerFromDb);

        List<BdCrmOrgUserRelationDO> userRelationDOList = organizationRelationService.listMemberOrgRelationNoMatterDelete(crowdfundingVolunteer.getUniqueCode());
        userRelationDOList.forEach(userRelation -> cfBdCrmObjectiveFacade.syncObjectiveOrgMemberSnapshotByMemberChange(userRelation));
    }
}
