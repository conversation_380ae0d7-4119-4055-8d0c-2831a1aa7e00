package com.shuidihuzhu.cf.cfgrowthtoolapi.listeners;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OrgOrMemberChangeEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.CrmForThirdPlatformFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.ICfWeaponManagerFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.bdcrm.ICfBdCrmObjectiveFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.bdcrm.target.TargetCycleFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.IBdMemberSnapshotService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolAsyncPoolConstants;
import com.shuidihuzhu.cf.dao.CrowdfundingVolunteerDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: fengxuan
 * @create 2020-07-23 2:21 下午
 **/
@Service
@Slf4j
public class OrgOrMemberChangeListener {

    @Autowired
    private CrmForThirdPlatformFacade crmForThirdPlatformFacade;

    @Autowired
    private CrowdfundingVolunteerDao volunteerDao;

    @Resource(name = "selfBuiltOrgForSea")
    private ICrmSelfBuiltOrgReadService readService;

    @Autowired
    private IBdMemberSnapshotService bdMemberSnapshotService;
    @Autowired
    private ICfBdCrmObjectiveFacade cfBdCrmObjectiveFacade;
    @Autowired
    private ICfWeaponManagerFacade cfWeaponManagerFacade;
    @Autowired
    private TargetCycleFacade targetCycleFacade;

    @Async(GrowthtoolAsyncPoolConstants.SYNC_ORG_MEMBER)
    @EventListener
    public void syncToThirdSystem(OrgOrMemberChangeEvent orgOrMemberChangeEvent) {
        log.info("存在组织或者人员变动");
        if (orgOrMemberChangeEvent.getCurrentOrganization() != null) {
            log.info("组织变动");
            BdCrmOrganizationDO currentOrganization = orgOrMemberChangeEvent.getCurrentOrganization();
            //查找组织
            BdCrmOrganizationDO currentOrg = readService.getCurrentOrgById(currentOrganization.getId());
            if (currentOrg != null) {
                crmForThirdPlatformFacade.callThirdInterfaceForOrgChange(Lists.newArrayList(currentOrg));
            } else {
                crmForThirdPlatformFacade.syncAllOrg();
            }
            //  组织变动 查询组织上级组织， 根据上级组织 去设置目标项
            cfBdCrmObjectiveFacade.syncObjectiveOrgMemberSnapshotByOrgChange(currentOrganization);
            cfWeaponManagerFacade.doInvalidBudget(currentOrganization, null);
            targetCycleFacade.changeTeamOrgMemberSnapshotByOrg(currentOrganization);
        }
        if (orgOrMemberChangeEvent.getRelationDO() != null) {
            //查找人员信息
            BdCrmOrgUserRelationDO relationDO = orgOrMemberChangeEvent.getRelationDO();
            String uniqueCode = relationDO.getUniqueCode();
            log.info("人员:{}变动", uniqueCode);
            CrowdfundingVolunteer volunteer = volunteerDao.getCrowdfundingVolunteerByUniqueCode(uniqueCode);
            if (volunteer != null) {
                crmForThirdPlatformFacade.callThirdInterfaceForMemberChange(volunteer);
            } else {
                log.info("人员:{}在volunteer中找不到", uniqueCode);
            }
            //增加人员快照表修改orgId逻辑
            String monthKey = DateUtil.getYYYYMMStr(DateUtil.getCurrentDate());
            bdMemberSnapshotService.updateOrgIdByUniqueCodeAndMonthKey(relationDO,monthKey);
            //  人员变动 查询上级组织， 根据上级组织 去设置目标项
            cfBdCrmObjectiveFacade.syncObjectiveOrgMemberSnapshotByMemberChange(relationDO);
            cfWeaponManagerFacade.doInvalidBudget(null, relationDO);
            targetCycleFacade.changeTeamOrgMemberSnapshotByMember(relationDO);
        }
        log.info("组织和人员变动同步完成");
    }

}
