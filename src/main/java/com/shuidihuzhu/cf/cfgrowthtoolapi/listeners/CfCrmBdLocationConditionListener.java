package com.shuidihuzhu.cf.cfgrowthtoolapi.listeners;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmBdInterviewSituationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmBdLocationConditionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CfCrmBdLocationConditionEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.PunchCardVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfCrmInterviewSituationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCrmCampaignMapService;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import static com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum.gpsRole;


/**
 * 监听 埋点事件  进行 服务端埋点
 * @author: wanghui
 * @create: 2019/05/30 上午10:29
 */
@Component
@Slf4j
public class CfCrmBdLocationConditionListener implements ApplicationListener<CfCrmBdLocationConditionEvent> {

    @Autowired
    private ICfCrmCampaignMapService cfCrmCampaignMapServiceImpl;
    @Autowired
    private ApolloService apolloService;
    @Autowired
    private CfCrmInterviewSituationService cfCrmInterviewSituationService;

    @Override
    public void onApplicationEvent(CfCrmBdLocationConditionEvent cfCrmBdLocationConditionEvent) {

        CfCrmBdLocationConditionDO cfCrmBdLocationConditionDO = cfCrmCampaignMapServiceImpl.getCfCrmBdLocationConditionDO(cfCrmBdLocationConditionEvent.getDateTime(),
                cfCrmBdLocationConditionEvent.getVolunteerUniqueCode());
        if (cfCrmBdLocationConditionDO==null){
            cfCrmBdLocationConditionDO = new CfCrmBdLocationConditionDO();
        }else if (apolloService.getPunchStatus()==PunchCardVo.PushStatusEnum.SIGN_OUT.getStatus()){
            // 如果当前时间已过下班时间  则无需要再更新记录
            cfCrmCampaignMapServiceImpl.updateCfCrmGpsStatus(DateUtil.getCurrentDateStr(),
                    cfCrmBdLocationConditionEvent.getMis(),
                    PunchCardVo.PushStatusEnum.SIGN_OUT.getStatus());
            return;
        }
        cfCrmBdLocationConditionDO.setMis(StringUtils.isEmpty(cfCrmBdLocationConditionEvent.getMis()) ? "" : cfCrmBdLocationConditionEvent.getMis());
        cfCrmBdLocationConditionDO.setDateTime(DateUtil.getCurrentDateStr());
        cfCrmBdLocationConditionDO.setVolunteerName(cfCrmBdLocationConditionEvent.getVolunteerName());
        cfCrmBdLocationConditionDO.setSceneType(cfCrmBdLocationConditionEvent.getSceneTypeEnum().getType());
        cfCrmBdLocationConditionDO.setType(cfCrmBdLocationConditionEvent.getSceneTypeEnum().getTypeEnum().getType());
        cfCrmBdLocationConditionDO.setRemark(CfCrmBdLocationConditionDO.getRemark(cfCrmBdLocationConditionEvent.getSceneTypeEnum()));
        cfCrmBdLocationConditionDO.setRole(cfCrmBdLocationConditionEvent.getRole());
        cfCrmBdLocationConditionDO.setUniqueCode(cfCrmBdLocationConditionEvent.getVolunteerUniqueCode());
        if (cfCrmBdLocationConditionDO.getId()!=null){
            cfCrmCampaignMapServiceImpl.updateCfCrmBdLocationConditionDO(cfCrmBdLocationConditionDO);
        }else {
            cfCrmCampaignMapServiceImpl.insertCfCrmBdLocationConditionDO(cfCrmBdLocationConditionDO);
        }
        // 只有业务经理和普通顾问 才需要 统计覆盖时长
        if (gpsRole.contains(cfCrmBdLocationConditionEvent.getRole())) {
            CfCrmBdInterviewSituationDO cfCrmBdInterviewSituationDO = CfCrmBdInterviewSituationDO.buildInsert(DateUtil.getCurrentDateStr(),
                    cfCrmBdLocationConditionEvent.getVolunteerUniqueCode(),
                    cfCrmBdLocationConditionEvent.getMis(),
                    cfCrmBdLocationConditionEvent.getVolunteerName(),
                    cfCrmBdLocationConditionEvent.getSceneTypeEnum().getType(),
                    cfCrmBdLocationConditionEvent.getSceneTypeEnum().getTypeEnum().getType(),
                    cfCrmBdLocationConditionEvent.getRole());
            cfCrmInterviewSituationService.batchInsertBdInterview(Lists.newArrayList(cfCrmBdInterviewSituationDO));
        }

    }
}
