package com.shuidihuzhu.cf.cfgrowthtoolapi.listeners;

import com.shuidihuzhu.cf.cfgrowthtoolapi.events.DepartmentModifyEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.department.DepartmentModifyModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.DepartmentHospitalSummaryService;
import com.shuidihuzhu.cf.dao.record.HospitalSubmitRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @author: fengxuan
 * @create 2022-12-07 16:43
 **/
@Slf4j
@Component
public class DepartmentModifyListener {

    @Autowired
    private DepartmentHospitalSummaryService departmentHospitalSummaryService;


    @EventListener
    public void handleEvent(DepartmentModifyEvent departmentModifyEvent) {
        DepartmentModifyModel departmentModifyModel = departmentModifyEvent.getDepartmentModifyModel();
        log.info("departmentModifyModel:{}", departmentModifyModel);
        if (departmentModifyModel == null) {
            return;
        }
        departmentHospitalSummaryService.maintainDepartmentHospital(departmentModifyModel);
    }
}
