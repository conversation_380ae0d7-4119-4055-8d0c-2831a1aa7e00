package com.shuidihuzhu.cf.cfgrowthtoolapi.listeners;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OrgAddMemberEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.IBdMemberSnapshotJobService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.IBdMemberSnapshotService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.dao.bdcrm.PreVolunteerOrgInfoRelationDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2020-07-09 12:23 下午
 **/
@Slf4j
@Service
public class OrgAddMemberListener implements ApplicationListener<OrgAddMemberEvent> {

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private ICrmSelfBuiltOrgReadService organizationService;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private IBdMemberSnapshotService bdMemberSnapshotService;

    @Autowired
    private IBdMemberSnapshotJobService bdMemberSnapshotJobService;

    @Autowired
    private PreVolunteerOrgInfoRelationDao preVolunteerOrgInfoRelationDao;


    @Override
    public void onApplicationEvent(OrgAddMemberEvent event) {
        BdCrmOrgUserRelationDO relationDO = event.getRelationDO();
        log.info("组织中新绑定了成员");
        if (relationDO == null) {
            return;
        }
        //更新bd_case_info中的orgId
        String orgPath = organizationService.listChainByOrgIdsWithDefaultSplitter(Lists.newArrayList(relationDO.getOrgId()))
                .getOrDefault(relationDO.getOrgId(), "");
        // 删除预绑定的组织，如果有的话
        if(StringUtils.isNotBlank(relationDO.getUniqueCode())) {
            preVolunteerOrgInfoRelationDao.deletePreOrgRelation(relationDO.getUniqueCode());
        }
        cfBdCaseInfoService.updateNoOrgIdMis(relationDO.getUniqueCode(), relationDO.getMis(), (int) relationDO.getOrgId(), orgPath);
        //添加到cf_crm_member_snapshot,由于快照是23
        //addToMemberSnapshot(relationDO);
    }


    /**
     * @param relationDO
     */
    private void addToMemberSnapshot(BdCrmOrgUserRelationDO relationDO) {
        //查看是否是新入职的员工
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = crmOrganizationRelationService.listMemberOrgRelationNoMatterDelete(relationDO.getUniqueCode());
        log.info("mis:{}对应的绑定历史个数:{}", relationDO.getMis(), bdCrmOrgUserRelationDOS.size());
        //从库查询可能有延时
        if (bdCrmOrgUserRelationDOS.size() <= 1) {
            CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(relationDO.getUniqueCode());
            CfCrmMemberSnapshotDO memberSnapshot = bdMemberSnapshotJobService.createMemberSnapshot(volunteer);
            List<CfCrmMemberSnapshotDO> snapshotDOList = Lists.newArrayList();
            if (memberSnapshot != null) {
                snapshotDOList.add(memberSnapshot);
            } else {
                memberSnapshot = bdMemberSnapshotJobService.buildOnlyShowMemberSnapshot(relationDO, volunteer);
                Optional.ofNullable(memberSnapshot).map(snapshotDOList::add);
            }
            bdMemberSnapshotService.addBatch(snapshotDOList);
        }
    }
}
