package com.shuidihuzhu.cf.cfgrowthtoolapi.listeners;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ISeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfOperatingRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020-08-10
 */
@Service
public class OperateLogListener implements ApplicationListener<OperateLogEvent> {

    @Autowired
    private IOperateLogService operateLogService;
    @Autowired
    private ISeaAccountServiceDelegate seaAccountServiceDelegate;

    @Override
    public void onApplicationEvent(OperateLogEvent operateLogEvent){
        CfOperatingRecordDO cfOperatingRecordDO = new CfOperatingRecordDO();
        cfOperatingRecordDO.setOperateUserId(operateLogEvent.getOperateUserId());
        cfOperatingRecordDO.setContent(operateLogEvent.getOperateContent());
        cfOperatingRecordDO.setOperateDesc(operateLogEvent.getOperateDesc());
        cfOperatingRecordDO.setOperateType(operateLogEvent.getOperateTypeEnum().getType());
        cfOperatingRecordDO.setOperateKey(operateLogEvent.getOperateKey());
        if (StringUtils.isBlank(operateLogEvent.getOperateName()) && Optional.ofNullable(operateLogEvent.getOperateUserId()).orElse(0L) > 0){
            AdminUserAccountModel adminUserAccountModel = seaAccountServiceDelegate.getValidUserAccountById(operateLogEvent.getOperateUserId());
            if (adminUserAccountModel != null){
                cfOperatingRecordDO.setOperateName(adminUserAccountModel.getName());
            }
        }else {
            cfOperatingRecordDO.setOperateName(operateLogEvent.getOperateName());
        }
        operateLogService.saveOperateLog(cfOperatingRecordDO);
    }
}
