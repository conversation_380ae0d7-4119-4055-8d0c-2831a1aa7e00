package com.shuidihuzhu.cf.cfgrowthtoolapi.partner.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.PepClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.FactCaseDetailModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.kpiv2.PerformanceResultExtendParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.pojo.CaseTimeDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.vo.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCaseBaseDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.PepKpiService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.BdCrmContextUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.calResult.*;
import com.shuidihuzhu.cf.client.performance.enums.BizType;
import com.shuidihuzhu.cf.client.performance.enums.FactResultSourceEnum;
import com.shuidihuzhu.cf.client.performance.query.PerformanceResultParam;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 兼职人员小鲸鱼绩效信息相关接口
 *
 * <AUTHOR>
 * @date 2021/9/16
 */
@Slf4j
@Controller
@RequestMapping(path = "/api/cf/growthtool/bdcrm-kpi/partner-v2", method = RequestMethod.POST)
public class PartnerBdCrmWhaleKpiControllerV2 {


    @Autowired
    private ICfKpiCaseBaseDataService cfKpiCaseBaseDataServiceImpl;

    @Autowired
    private AlarmClient alarmClient;

    @Autowired
    private PepClientDelegate pepClientDelegate;

    public static final String caseBonusFactName = "在筹预估奖励";

    public static final String hotCaseBonusFactName = "爆款案例激励";

    @Autowired
    private PepKpiService pepKpiService;


    @ApiOperation("个人绩效数据展示")
    @PostMapping("/query-partner-kpi-data")
    @ResponseBody
    public Response<CfPartnerKpiStatResultVO> queryPartnerKpiData() {
        CrowdfundingVolunteer crowdfundingVolunteer = BdCrmContextUtil.getCfVolunteer();
        DateTime now = DateTime.now();
        //查找数据
        List<ProcedureForCModel> listResponse = pepClientDelegate.listAllProcedure(BdCrmContextUtil.getCfVolunteer().getUniqueCode(), BizType.CF_WHALE.getCode());
        if (CollectionUtils.isEmpty(listResponse)) {
            return NewResponseUtil.makeSuccess(null);
        }
        List<ProcedureForCModel> data = listResponse
                .stream()
                .collect(Collectors.toList());
        List<ProcedureForCModel> procedureForCModelList = data.stream()
                .sorted(Ordering.natural().onResultOf(ProcedureForCModel::getShowStartTime).reversed())
                .limit(12)
                .collect(Collectors.toList());
        List<CfPartnerKpiStatVO> historyDataList = procedureForCModelList.stream()
                .filter(item -> new DateTime(item.getShowEndTime()).isBefore(now))
                .map(item -> convert(item, crowdfundingVolunteer))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        CfPartnerKpiStatVO toBeSettleData = procedureForCModelList.stream()
                .filter(item -> new DateTime(item.getShowEndTime()).isAfter(now) && new DateTime(item.getShowStartTime()).isBefore(now))
                .map(item -> convert(item, crowdfundingVolunteer))
                .findFirst()
                .orElse(null);
        CfPartnerKpiStatVO currentData = procedureForCModelList.stream()
                .filter(item -> new DateTime(item.getShowStartTime()).isAfter(now))
                .map(item -> convert(item, crowdfundingVolunteer))
                .findFirst()
                .orElse(null);
        CfPartnerKpiStatResultVO result = new CfPartnerKpiStatResultVO();
        result.setUseNewInterface(1);
        result.setHistoryKpiStats(historyDataList);
        result.setToBeSettledKpiStat(toBeSettleData);
        result.setCfPartnerKpiStatVOs(currentData == null ? Lists.newArrayList() : Lists.newArrayList(currentData));
        return NewResponseUtil.makeSuccess(result);
    }


    private CfPartnerKpiStatVO convert(ProcedureForCModel procedureForCModel, CrowdfundingVolunteer volunteer) {
        if (procedureForCModel == null) {
            return null;
        }
        PerformanceResultParam param = new PerformanceResultParam();
        param.setUserId(volunteer.getUniqueCode());
        param.setProcedureId(procedureForCModel.getProcedureId());
        UserCalResult data = pepClientDelegate.performanceResult(param);
        if (data == null) {
            return null;
        }
        Date startTime = procedureForCModel.getStartTime();
        CfPartnerKpiStatVO cfPartnerKpiStatVO = new CfPartnerKpiStatVO();
        cfPartnerKpiStatVO.setVolunteerName(volunteer.getVolunteerName());
        cfPartnerKpiStatVO.setUniqueCode(volunteer.getUniqueCode());


        DateTime lastDate = new DateTime(startTime)
                .plusMonths(1)
                .withDayOfMonth(1)
                .minusDays(1);

        cfPartnerKpiStatVO.setTimeKey(lastDate.toString(GrowthtoolUtil.ymdchinesefm));

        //截至4号的数据
        DateTime lastShowDate = new DateTime(startTime).withDayOfMonth(4).plusMonths(1);
        DateTime dateTime = DateTime.now().withTimeAtStartOfDay().minusDays(1);

        if (lastShowDate.isBefore(dateTime)) {
            dateTime = lastShowDate;
        }


        CaseTimeDO caseTimeDO = CaseTimeDO.builder()
                .startTime(lastDate.withDayOfMonth(1).toString(GrowthtoolUtil.ymdfmt))
                .endTime(lastDate.withDayOfMonth(1).plusMonths(1).toString(GrowthtoolUtil.ymdfmt))
                .dayKey(dateTime.toString(GrowthtoolUtil.ymdfmt))
                .build();
        List<CfKpiCaseBaseDataDO> caseBaseDataDOList = cfKpiCaseBaseDataServiceImpl.listPartnerCaseBase(volunteer.getUniqueCode(), caseTimeDO);
        Map<Long, CfKpiCaseBaseDataDO> caseBaseDataDOMap = caseBaseDataDOList.stream()
                .collect(Collectors.toMap(CfKpiCaseBaseDataDO::getCaseId, Function.identity(), (before, after) -> before));

        BigDecimal caseCommissionBonus = new BigDecimal(Optional.ofNullable(data.getCommissionResult()).map(SchemeCalResult::getAmount).orElse("0"));
        BigDecimal caseDonateCommissionBonus = new BigDecimal(Optional.ofNullable(data.getPerformanceResult()).map(SchemeCalResult::getAmount).orElse("0"));
        Long caseCommissionBonusExcludeImport = Long.valueOf(Optional.ofNullable(data.getCommissionResult()).map(SchemeCalResult::getAmountExcludeImport).orElse("0"));


        //捐单奖励
        cfPartnerKpiStatVO.setCaseDonateCommissionBonus(caseCommissionBonusExcludeImport);
        cfPartnerKpiStatVO.setKpiBonus(caseCommissionBonus.add(caseDonateCommissionBonus).stripTrailingZeros().toPlainString());
        cfPartnerKpiStatVO.setCaseNum((long) caseBaseDataDOMap.size());
        cfPartnerKpiStatVO.setCaseDonateNum(caseBaseDataDOMap.values().stream().mapToLong(CfKpiCaseBaseDataDO::getDonateNum).sum());
        cfPartnerKpiStatVO.setProcedureId(procedureForCModel.getProcedureId());
        cfPartnerKpiStatVO.setCalcUnique(data.getCalcUnique());
        if (data.getPerformanceResult() != null && CollectionUtils.isNotEmpty(data.getPerformanceResult().getFactDetails())) {
            List<FactCalResult> performanceFacts = data.getPerformanceResult().getFactDetails()
                    .stream()
                    .filter(item -> item.getFactSourceType() == FactResultSourceEnum.from_create.getCode())
                    .collect(Collectors.toList());

            FactCalResult caseFactCalResult = performanceFacts
                    .stream()
                    .filter(item -> Objects.equals(item.getShortDesc(), caseBonusFactName))
                    .findFirst()
                    .orElse(null);
            if (caseFactCalResult == null) {
                cfPartnerKpiStatVO.setShowCaseCommissionBonus(false);
            } else {
                cfPartnerKpiStatVO.setFactId(caseFactCalResult.getFactId());
                cfPartnerKpiStatVO.setCaseCommissionBonus(caseFactCalResult.getFactAmount());
            }
            FactCalResult hotCaseFact = performanceFacts
                    .stream()
                    .filter(item -> Objects.equals(item.getShortDesc(), hotCaseBonusFactName))
                    .findFirst()
                    .orElse(null);
            if (hotCaseFact == null) {
                cfPartnerKpiStatVO.setShowHotCaseBonus(false);
            } else {
                cfPartnerKpiStatVO.setHotCaseFactId(hotCaseFact.getFactId());
                cfPartnerKpiStatVO.setHotCaseBonus(hotCaseFact.getFactAmount());
                PerformanceResultExtendParam extendParam = new PerformanceResultExtendParam();
                extendParam.setQuerySource(PerformanceResultExtendParam.QuerySourceEnum.not_noraml.getCode());
                extendParam.setUserId(BdCrmContextUtil.getCfVolunteer().getUniqueCode());
                extendParam.setProcedureId(data.getProcedureId());
                extendParam.setFactId(hotCaseFact.getFactId());
                extendParam.setCalcUnique(data.getCalcUnique());
                Response<List<FactCaseDetailModel>> listResponse = pepKpiService.listFactCalDetail(extendParam);
                if (listResponse.ok() && listResponse.getData() != null) {
                    long count = listResponse.getData()
                            .stream()
                            .map(FactCaseDetailModel::getCfKpiCaseScoreVO)
                            .filter(Objects::nonNull)
                            .filter(item -> item.getIllegalCase() == null || Objects.equals(item.getIllegalCase(), 0))
                            .filter(item -> item.getCaseResult() != null && !Objects.equals(item.getCaseResult(), "0"))
                            .count();
                    cfPartnerKpiStatVO.setHotCaseNum(count);
                }
            }
        }
        //目标完整配置-要素
        List<FactCalResult> targetFactList = Lists.newArrayList();
        if (data.getCommissionResult() != null && CollectionUtils.isNotEmpty(data.getCommissionResult().getFactDetails())) {
            targetFactList = data.getCommissionResult().getFactDetails()
                    .stream()
                    .filter(item -> item.getFactSourceType() == FactResultSourceEnum.from_create.getCode())
                    .filter(item -> StringUtils.isNumeric(item.getShortDesc()))
                    .sorted(Comparator.comparingInt(a -> Integer.parseInt(a.getShortDesc())))
                    .collect(Collectors.toList());
            cfPartnerKpiStatVO.setNumberOfTargets(targetFactList.size());
            //必须保证所有的c端展示名称都为数字
            List<FactCalResult> donateAwardList = targetFactList.stream()
                    .filter(item -> Integer.parseInt(item.getFactAmount()) > 0)
                    .collect(Collectors.toList());
            List<Integer> allDonateFactList = targetFactList.stream()
                    .map(item -> Integer.parseInt(item.getShortDesc()))
                    .collect(Collectors.toList());
            //捐单只给一笔奖励
            if (donateAwardList.size() > 1) {
                //报警下
                alarmClient.sendByUser(Lists.newArrayList("fengxuan"), "小助理捐单奖励了多笔");
            }
            int numberOfTargetsCompleted = 0;
            if (CollectionUtils.isNotEmpty(donateAwardList)) {
                FactCalResult factCalResult = donateAwardList.get(0);
                if (StringUtils.isNumeric(factCalResult.getShortDesc())) {
                    int currentDonateFact = Integer.parseInt(factCalResult.getShortDesc());
                    for (int i = 0; i < allDonateFactList.size(); i++) {
                        if (currentDonateFact == allDonateFactList.get(i)) {
                            numberOfTargetsCompleted = i;
                            break;
                        }
                    }
                    cfPartnerKpiStatVO.setNumberOfTargetsCompleted(numberOfTargetsCompleted + 1);
                }
            }
            List<FactCalResult> needShow = targetFactList.subList(numberOfTargetsCompleted, targetFactList.size());
            List<PartnerDonateDetail> donateDetailList = Lists.newArrayList();
            for (FactCalResult factCalResult : needShow) {
                PartnerDonateDetail partnerDonateDetail = new PartnerDonateDetail();
                partnerDonateDetail.setDesc(factCalResult.getFactDesc());
                partnerDonateDetail.setReachStatus(Integer.parseInt(factCalResult.getFactAmount()) > 0 ? 1 : 0);
                partnerDonateDetail.setTarget(Integer.parseInt(factCalResult.getShortDesc()));
                partnerDonateDetail.setAward(Integer.parseInt(factCalResult.getFactAmount()));
                donateDetailList.add(partnerDonateDetail);
            }

            cfPartnerKpiStatVO.setDonateDetailList(donateDetailList);
        }

        if (CollectionUtils.isEmpty(targetFactList)) {
            cfPartnerKpiStatVO.setShowCaseDonateCommissionBonus(false);
        }

        List<FactCalResult> importResultList = Lists.newArrayList();
        importResultList.addAll(Optional.ofNullable(data.getCommissionResult())
                .map(SchemeCalResult::getFactDetails).orElse(Lists.newArrayList()));
        importResultList.addAll(Optional.ofNullable(data.getPerformanceResult())
                .map(SchemeCalResult::getFactDetails).orElse(Lists.newArrayList()));
        importResultList = importResultList.stream()
                .filter(item -> item.getFactSourceType() == FactResultSourceEnum.from_import.getCode())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(importResultList)) {
            cfPartnerKpiStatVO.setImportAwardMap(importResultList.stream()
                    .collect(Collectors.toMap(FactCalResult::getShortDesc, FactCalResult::getFactAmount)));
            int oneReward = importResultList.stream()
                    .filter(item -> StringUtils.isNumeric(item.getFactAmount()) && Integer.parseInt(item.getFactAmount()) > 0)
                    .mapToInt(item -> Integer.parseInt(item.getFactAmount()))
                    .sum();
            int oneDeductions = importResultList.stream()
                    .filter(item -> Integer.parseInt(item.getFactAmount()) < 0)
                    .mapToInt(item -> Integer.parseInt(item.getFactAmount()) * -1)
                    .sum();
            cfPartnerKpiStatVO.setOneReward(oneReward);
            cfPartnerKpiStatVO.setOneDeductions(oneDeductions);
            cfPartnerKpiStatVO.setSpecialBonus((long) (oneReward + oneDeductions));
        }
        return cfPartnerKpiStatVO;
    }


}
