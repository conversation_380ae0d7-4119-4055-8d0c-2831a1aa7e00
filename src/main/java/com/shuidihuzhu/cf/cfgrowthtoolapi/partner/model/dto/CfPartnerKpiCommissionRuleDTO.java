package com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.dto;

import com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.PartnerAmountRangeModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author：liuchangjun
 * @Date：2021/9/16
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Slf4j
public class CfPartnerKpiCommissionRuleDTO {

    private long id=-1; // id
    @NotNull(message = "考核时间标识")
    private String timeKey;// 考核月份

    @ApiModelProperty("捐单和金额混合区间")
    private List<PartnerAmountRangeModel> amountRanges;
    private int commissionType=-1; // 提成类型
    private int level; // 职级

    private int status;

    private String operator;

    @ApiModelProperty("捐单目标区间")
    private List<PartnerAmountRangeModel> targetDonate;
}
