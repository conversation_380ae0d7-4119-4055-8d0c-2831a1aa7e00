package com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author：liuchangjun
 * @Date：2021/9/16
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class PartnerAmountRangeModel {

    private Long minAmount;// 金额区间 小
    private Long minDonationAmount; //捐单区间  小
    private Long targetDonationAmount; // 目标捐单
    private Integer value; // 值 作为金额使用时 这里单位是：分
}
