package com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.vo;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */

@Data
@ApiModel("绩效提成统计结果")
public class CfPartnerKpiStatResultVO {

    @ApiModelProperty("当前周期数据统计")
    private List<CfPartnerKpiStatVO> cfPartnerKpiStatVOs = Lists.newArrayList();

    @ApiModelProperty("待结算数据统计")
    private CfPartnerKpiStatVO toBeSettledKpiStat;

    @ApiModelProperty("历史数据统计")
    private List<CfPartnerKpiStatVO> historyKpiStats = Lists.newArrayList();

    @ApiModelProperty("使用新接口返回数据,1:是,0:否")
    private int useNewInterface;
}
