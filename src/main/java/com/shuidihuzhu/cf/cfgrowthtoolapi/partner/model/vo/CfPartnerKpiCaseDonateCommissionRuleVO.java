package com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/9/18
 */

@Data
@ApiModel("兼职捐单绩效Vo")
public class CfPartnerKpiCaseDonateCommissionRuleVO {

    @ApiModelProperty("目标捐单量")
    private Long targetDonationAmount;

    @ApiModelProperty("奖励值, 单位：分")
    private Integer value;

    @ApiModelProperty("是否完成，0:未完成，1:完成")
    private Integer finish;
}
