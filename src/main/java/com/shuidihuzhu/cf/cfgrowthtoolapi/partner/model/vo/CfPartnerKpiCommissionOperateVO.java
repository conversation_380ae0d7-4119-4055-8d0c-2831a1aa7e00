package com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.vo;

import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfBdKpiEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class CfPartnerKpiCommissionOperateVO {

    @ApiModelProperty("计算周期标识")
    private String timeKey;

    /**
     * {@link CfBdKpiEnums.SpecialTypeEnum}
     */
    @ApiModelProperty("提成类型, 1:奖励 2:扣费")
    private Integer operateType;

    @ApiModelProperty("提成或扣费名称")
    private String operateName;

    @ApiModelProperty("提成或扣费金额 单位分")
    private Integer operateAmount;
}
