package com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 取案例通用
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2021/9/17 11:28 上午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaseTimeDO {

    /** COMMENT '案例发起开始日期', **/
    private String startTime;
    /** COMMENT '案例发起结束日期', **/
    private String endTime;
    /** COMMENT '当前日期', **/
    private String dayKey;

}
