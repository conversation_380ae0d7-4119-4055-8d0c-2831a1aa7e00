package com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 规则json的model
 * @Author：liuchangjun
 * @Date：2021/9/16
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class CfPartnerKpiCommissonRuleModel {


    @ApiModelProperty("捐单和金额混合区间")
    private List<PartnerAmountRangeModel> amountRanges;

    @ApiModelProperty("捐单目标区间")
    private List<PartnerAmountRangeModel> targetRanges ;

}
