package com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.vo;


import com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.CfPartnerKpiCommissonRuleModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


/**
 * @Author：liuchangjun
 * @Date：2021/9/16
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class CfPartnerKPICommissionRuleVO {

    private Long id;
    private Integer level; // 职级
    private String timeKey; // 考核时间段标识
    private Integer status; // 状态类型
    private Integer commissionType;
    private String caseStartTime; // 案例发起开始日期
    private String caseEndTime;// 案例发起结束日期
    private String calEndTime;

    //规则model
    private CfPartnerKpiCommissonRuleModel cfPartnerKpiCommissonRuleModel;


}
