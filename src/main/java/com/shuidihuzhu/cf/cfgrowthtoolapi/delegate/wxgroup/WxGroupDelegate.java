package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.wxgroup;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywx.BdRobotContactModel;
import com.shuidihuzhu.kratos.client.api.crm.dto.EnterpriseWxStaffVo;
import com.shuidihuzhu.kratos.client.api.group.dto.WwExternalGroupChatUserInfoResp;
import com.shuidihuzhu.kratos.client.api.wecom.dto.*;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2025-04-14 14:07
 **/
public interface WxGroupDelegate {


    /**
     * 获取企业微信群聊成员信息
     *
     * @param chatId 企业微信群聊ID
     * @return 成员信息列表
     */
    List<WwExternalGroupChatUserInfoResp> getWxGroupMembers(String chatId);

    /**
     * 转移群主
     *
     * @param requestId 请求ID
     * @param chatId 群聊ID
     * @param externalUserId 新群主外部用户ID
     */
    void transferGroupOwner(String requestId, String chatId, String externalUserId);

    /**
     * 获取机器人列表
     *
     * @param corpId 企业ID
     * @return 机器人联系人列表
     */
    List<EnterpriseWxStaffVo> getRobotList(String corpId);

    /**
     * 查询企业微信用户信息
     *
     * @param corpId 企业ID
     * @param userId 用户ID
     * @return 用户信息
     */
    WecomUserInfoResp queryWecomUserInfo(String corpId, String userId);

    WecomExternalUserDetailResp queryWecomExternalUserDetail(WecomExternalUserDetailReq req);

}
