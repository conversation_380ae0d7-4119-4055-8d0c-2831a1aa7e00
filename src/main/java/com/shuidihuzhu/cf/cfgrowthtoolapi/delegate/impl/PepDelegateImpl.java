package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IpepDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiBdScoreDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfBdKpiEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiBdScoreService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiBdScoreTempService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.PepLotObtainService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.client.performance.PepClient;
import com.shuidihuzhu.cf.performance.data.meta.PepGrowthtoolUserIdTemplate;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2022-06-15 16:40
 **/
@Slf4j
@Service
public class PepDelegateImpl implements IpepDelegate {

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private PepClient pepClient;

    @Autowired
    private PepLotObtainService pepLotObtainService;

    @Autowired
    private ICfKpiBdScoreTempService cfKpiBdScoreTempService;


    @Override
    public void syncScoreData(String userId, List<CustomPerformanceScoreModel> customPerformanceScoreModels, CfKPICommissionAwardCollectModel awardAmounts) {
        long scoreLot = getLotId();
        if (scoreLot == 0) {
            log.info("没有可推送的打分批次");
            return;
        }
        PepGrowthtoolUserIdTemplate userIdTemplate = new PepGrowthtoolUserIdTemplate();
        List<KPICustomRuleScoreModel> scoreModels = Optional.ofNullable(customPerformanceScoreModels)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(item -> item.getScoreBySea() == CfBdKpiEnums.ScoreSource.from_leader.getCode())
                .flatMap(item -> item.getKpiCustomRuleScoreModels().stream())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(scoreModels)) {
            Optional<KPICustomRuleScoreModel> leaderScore = scoreModels.stream()
                    .filter(item -> Objects.equals(item.getCustomContent(), "上级评分"))
                    .findFirst();
            if (leaderScore.isPresent()) {
                KPICustomRuleScoreModel kpiCustomRuleScoreModel = leaderScore.get();
                userIdTemplate.setLeaderScore(kpiCustomRuleScoreModel.getScore());
                userIdTemplate.setLeaderScoreComment(kpiCustomRuleScoreModel.getCustomizeDesc());
            }
            Optional<KPICustomRuleScoreModel> leaderCustomScore = scoreModels.stream()
                    .filter(item -> Objects.equals(item.getCustomContent(), "自定义上级评分"))
                    .findFirst();
            if (leaderCustomScore.isPresent()) {
                KPICustomRuleScoreModel kpiCustomRuleScoreModel = leaderCustomScore.get();
                userIdTemplate.setLeaderCustomScore(kpiCustomRuleScoreModel.getScore());
                userIdTemplate.setLeaderCustomScoreComment(kpiCustomRuleScoreModel.getCustomizeDesc());
            }
            Optional<KPICustomRuleScoreModel> crowdLeaderScore = scoreModels.stream()
                    .filter(item -> Objects.equals(item.getCustomContent(), "筹模块上级评分"))
                    .findFirst();
            if (crowdLeaderScore.isPresent()) {
                KPICustomRuleScoreModel kpiCustomRuleScoreModel = crowdLeaderScore.get();
                userIdTemplate.setCrowdLeaderScore(kpiCustomRuleScoreModel.getScore());
                userIdTemplate.setCrowdLeaderScoreComment(kpiCustomRuleScoreModel.getCustomizeDesc());
            }

            Optional<KPICustomRuleScoreModel> donateScore = scoreModels.stream()
                    .filter(item -> Objects.equals(item.getCustomContent(), "捐单完成"))
                    .findFirst();
            if (donateScore.isPresent()) {
                KPICustomRuleScoreModel kpiCustomRuleScoreModel = donateScore.get();
                userIdTemplate.setDonateScore(kpiCustomRuleScoreModel.getScore());
                userIdTemplate.setDonateScoreComment(kpiCustomRuleScoreModel.getCustomizeDesc());
            }
        }
        if (awardAmounts != null) {
            Optional<CfKPICommissionAwardAmountModel> incentiveOpt = awardAmounts.getAwardAmountModels()
                    .stream()
                    .filter(item -> item.getAwardType() == CfKPICommissionAwardModel.AwardTypeEnum.shifu_guli_jin.getType())
                    .findFirst();
            if (incentiveOpt.isPresent()) {
                CfKPICommissionAwardAmountModel kpiCustomRuleScoreModel = incentiveOpt.get();
                userIdTemplate.setIncentiveMoney(kpiCustomRuleScoreModel.getAmount() / 100);
            }
            Optional<CfKPICommissionAwardAmountModel> lovePartnerOpt = awardAmounts.getAwardAmountModels()
                    .stream()
                    .filter(item -> item.getAwardType() == CfKPICommissionAwardModel.AwardTypeEnum.jianzhi_koufei.getType())
                    .findFirst();
            if (lovePartnerOpt.isPresent()) {
                CfKPICommissionAwardAmountModel kpiCustomRuleScoreModel = lovePartnerOpt.get();
                userIdTemplate.setLovePartnerDeduct(kpiCustomRuleScoreModel.getAmount() / 100);
            }
        }
        userIdTemplate.setUserId(userId);
        //userIdTemplate.setScoreId();
        userIdTemplate.setLotId(scoreLot);
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(userIdTemplate);
        mqProducerService.pushBusinessData(jsonObject);
    }

    @Override
    public void syncScoreData(List<CfKpiBdScoreDO> cfKpiBdScoreDOS) {
        for (CfKpiBdScoreDO cfKpiBdScoreDO : cfKpiBdScoreDOS) {
            String uniqueCode = cfKpiBdScoreDO.getUniqueCode();
            List<CustomPerformanceScoreModel> scoreModels = cfKpiBdScoreDO.showAssessmentGradeIncludeImport();
            CfKPICommissionAwardCollectModel awardAmounts = cfKpiBdScoreDO.showAwardAmounts();
            syncScoreData(uniqueCode, scoreModels, awardAmounts);
        }
    }


    @Override
    public void syncScoreDataByLot() {
        syncScoreDataByLot(getLotId());
    }

    @Override
    public void syncScoreDataByLot(long scoreLot) {
        LotInfo lotInfo = getLotInfoById(scoreLot);
        if (lotInfo == null) {
            return;
        }
        String monthKey = new DateTime(lotInfo.getLotStartTime())
                .plusMonths(1)
                .withDayOfMonth(1)
                .minusDays(1)
                .toString(GrowthtoolUtil.ymfmt);
        List<CfKpiBdScoreDO> cfKpiBdScoreDOS = cfKpiBdScoreTempService.listKpiBdByMonthKey(monthKey);
        //List<CfKpiBdScoreDO> cfKpiBdScoreDOS = kpiBdScoreService.listAllKpiBdScore(monthKey);
        syncScoreData(cfKpiBdScoreDOS);
    }


    private long getLotId() {
        List<Long> lotIds = pepLotObtainService.getLotIds(PepPushEnum.direct_leader_score);
        if (CollectionUtils.isEmpty(lotIds)) {
            return 0;
        }
        //不能简单的取第一个，会存在一次配置了多个，需要判断当前时间和批次时间是否能吻合
        LotInfo lotInfo = Optional.ofNullable(pepClient.listLotInfoByIds(lotIds)
                        .getData())
                .orElse(Lists.newArrayList())
                .stream()
                .filter(item -> {
                    DateTime dateTime = new DateTime(item.getLotEndTime());
                    //打分天数限制
                    return dateTime
                            .plusDays(10)
                            .isAfterNow() && dateTime.isBeforeNow();
                }).findFirst()
                .orElse(null);

        return lotInfo != null ? lotInfo.getLotId() : 0;
    }

    private LotInfo getLotInfoById(long lotId) {
        Response<LotInfo> lotResponse = pepClient.getLotInfoById(lotId);
        log.info("获取业务批次,id:{},response:{}", lotId, lotResponse);
        return Optional.ofNullable(lotResponse).filter(Response::ok).map(Response::getData).orElse(null);
    }
}
