package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ApproveRemarkFeignClientDelegate;
import com.shuidihuzhu.client.cf.admin.client.ApproveRemarkFeignClient;
import com.shuidihuzhu.client.cf.admin.model.AdminApproveRemark;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2023-12-11 14:31
 **/
@Service
@Slf4j
public class ApproveRemarkFeignClientDelegateImpl implements ApproveRemarkFeignClientDelegate {

    @Autowired
    private ApproveRemarkFeignClient approveRemarkFeignClient;

    @Override
    public void addApproveRemark(AdminApproveRemark adminApproveRemark) {
        Response<Void> response = approveRemarkFeignClient.addApproveRemark(adminApproveRemark);
        if (response.notOk()) {
            log.warn("addApproveRemark fail, adminApproveRemark:{}, response:{}", adminApproveRemark, response);
        }
    }
}
