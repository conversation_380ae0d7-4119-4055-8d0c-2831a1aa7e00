package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.PayClientRpcDelegate;
import com.shuidihuzhu.client.baseservice.pay.model.*;
import com.shuidihuzhu.client.baseservice.pay.model.v3.PayInfoParamV3;
import com.shuidihuzhu.client.baseservice.pay.model.v3.RefundLaunchParamV3;
import com.shuidihuzhu.client.baseservice.pay.v1.PayClientV1;
import com.shuidihuzhu.client.baseservice.pay.v3.PayClientV3;
import com.shuidihuzhu.client.baseservice.pay.v3.RefundClientV3;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2020-06-29 10:38 上午
 **/
@Service
@Slf4j
public class PayClientRpcDelegateImpl implements PayClientRpcDelegate {

    @Autowired
    private PayClientV1 payClientV1;

    @Autowired
    private PayClientV3 payClientV3;

    @Autowired
    private RefundClientV3 refundClientV3;

    @Override
    public PayRpcResponse<PayResultV2> unifiedOrder(PayInfoParamV3 payInfoParamV3) {
        PayRpcResponse<PayResultV2> response = payClientV3.unifiedOrder(payInfoParamV3);
        log.info(this.getClass().getSimpleName() + " unifiedOrder param:{},response:{}", JSON.toJSONString(payInfoParamV3), JSON.toJSONString(response));
        return response;
    }

    @Override
    public PayInnerCallBack verifyCallback(PayInnerCallBackInfo payInnerCallBackInfo) {
        PayInnerCallBack response = payClientV1.verifyCallback(payInnerCallBackInfo);
        log.info(this.getClass().getSimpleName() + " verifyCallback param:{},response:{}", JSON.toJSONString(payInnerCallBackInfo), JSON.toJSONString(response));
        return response;
    }


    @Override
    public RefundResult refund(RefundLaunchParamV3 launchParamV3) {
        PayRpcResponse<RefundResult> launch = refundClientV3.launch(launchParamV3);
        log.info(this.getClass().getSimpleName() + " refund param:{},response:{}", JSON.toJSONString(launchParamV3), JSON.toJSONString(launch));
        if (!launch.isSuccess()) {
            log.warn("调用退款接口失败param:{}", JSON.toJSONString(launchParamV3));
        }
        return launch.getResult();
    }
}
