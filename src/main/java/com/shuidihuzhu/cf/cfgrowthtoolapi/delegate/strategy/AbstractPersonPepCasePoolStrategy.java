package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.strategy;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.HomeCaseRemindModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.BdCrmForwardingRecipesService;
import com.shuidihuzhu.cf.client.performance.model.GrowthtoolPepCaseScoreModel;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/12/11  14:57
 */
public abstract class AbstractPersonPepCasePoolStrategy implements IPersonPepCasePoolStrategy {

    @Autowired
    private BdCrmForwardingRecipesService bdCrmForwardingRecipesService;

    public void getCaseFeature(String dt, List<HomeCaseRemindModel> homeCaseRemindModels, List<GrowthtoolPepCaseScoreModel> growthtoolPepCaseScoreModelList) {
        bdCrmForwardingRecipesService.getCaseFeature(dt, homeCaseRemindModels, growthtoolPepCaseScoreModelList);
    }

    public void getDiagnosis(List<HomeCaseRemindModel> homeCaseRemindModels) {
        bdCrmForwardingRecipesService.getDiagnosis(homeCaseRemindModels);
    }

    public List<HomeCaseRemindModel> willValidAndDonateSpaceBigSort(List<HomeCaseRemindModel> homeCaseRemindModels, String dt) {
        return bdCrmForwardingRecipesService.willValidAndDonateSpaceBigSort(homeCaseRemindModels, dt);
    }

    public void setCaseType(int caseType, List<HomeCaseRemindModel> homeCaseRemindModels) {
        for (HomeCaseRemindModel homeCaseRemindModel : homeCaseRemindModels) {
            homeCaseRemindModel.setCaseType(caseType);
        }
    }

}
