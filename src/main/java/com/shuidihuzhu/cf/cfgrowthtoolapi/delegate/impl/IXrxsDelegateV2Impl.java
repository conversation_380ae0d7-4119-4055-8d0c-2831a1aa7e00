package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IXrxsDelegateV2;
import com.shuidihuzhu.information.organization.model.cf.UserInfo;
import com.shuidihuzhu.information.organization.model.cf.UserInfoResponse;
import com.shuidihuzhu.information.organization.client.OrgApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2020-11-17 10:23 上午
 **/
@Slf4j
@Service
public class IXrxsDelegateV2Impl implements IXrxsDelegateV2 {

    @Autowired
    private OrgApiClient orgApiClient;

    private final String sign = "20f3GUB+lz8eUsE0rVm4lXqFJD8=";


    @Override
    public UserInfo getUserInfoForCf(String userId, String jobNum) {
        UserInfoResponse userInfoForCf = null;
        //如果工号不为空,优先通过工号去取
        if (StringUtils.isNotBlank(jobNum)) {
            userInfoForCf = orgApiClient.getUserInfoForCfByJobNumber(sign, jobNum);
        }
        //为空的情况下才去通过mis取
        if (userInfoForCf == null || userInfoForCf.getCode() != 0) {
            log.info("mis:{}通过mis去取xrxs信息", userId);
            userInfoForCf = orgApiClient.getUserInfoForCf(sign, userId);
        }
        log.debug("xrxs request userId:{}response:{}", userId, userInfoForCf);
        if (userInfoForCf.getCode() == 0) {
            return userInfoForCf.getUserInfo();
        } else {
            log.info("UserInfoResponse:{}", userInfoForCf);
        }
        return null;
    }

    @Override
    public UserInfo getUserInfoByJobNum(String jobNum) {
        UserInfoResponse userInfoForCf = null;
        if (StringUtils.isNotBlank(jobNum)) {
            userInfoForCf = orgApiClient.getUserInfoForCfByJobNumber(sign, jobNum);
        }
        if (userInfoForCf != null && userInfoForCf.getCode() == 0) {
            return userInfoForCf.getUserInfo();
        }
        return null;
    }
}
