package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.*;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ISeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.SeaAdminUserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.*;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.*;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2019-05-15
 */
@Service
@Slf4j
@RefreshScope
public class SeaAccountServiceDelegate implements ISeaAccountServiceDelegate {

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private UserGroupFeignClient userGroupFeignClient;


    private static final int MAX_ORGANIZATION = 128;

    private LoadingCache<String, SeaAdminUserInfoModel> orgInfoByMisCache = CacheBuilder
            .newBuilder()
            .maximumSize(1000)
            .refreshAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, SeaAdminUserInfoModel>() {
                @Override
                public SeaAdminUserInfoModel load(String mis) throws Exception {
                    return getOrgInfoByMis(mis);
                }
            });


    @Override
    public OpResult<SeaAdminUserInfoModel> getUserAccountsByMis(String misId) {
        try {
            if (StringUtils.isBlank(misId)) {
                return OpResult.createSucResult(null);
            }
            Response<AuthUserDto> accountResult = userFeignClient.getByLoginName(misId);
            log.info("getUserAccountsByMis request misId:{},response:{}", misId, JSON.toJSON(accountResult));
            if (accountResult.ok() && accountResult.getData() != null) {
                AuthUserDto account = accountResult.getData();
                if (account == null) {
                    return OpResult.createFailResult(CfGrowthtoolErrorCode.USER_CAN_NOT_EXSITS);
                }
                SeaAdminUserInfoModel model = new SeaAdminUserInfoModel();
                model.setUserId(account.getUserId());
                model.setName(account.getUserName());
                model.setMis(account.getLoginName());
                model.setMail(account.getEmail());
                return OpResult.createSucResult(model);
            } else {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.RPC_ERROR);
            }
        } catch (Exception e) {
            log.error("getUserAccountsByMis exception misId:{}", misId);
        }
        return OpResult.createFailResult(CfGrowthtoolErrorCode.USER_CAN_NOT_EXSITS);
    }

    @Override
    public AdminOrganization getUserOrganization(String misId) {
        Response<AuthUserDto> accountResult = userFeignClient.getByLoginName(misId);
        log.info("getUserAccountsByMis request misId:{},response:{}", misId, JSON.toJSON(accountResult));
        AuthUserDto account = null;
        if (accountResult.ok()) {
            account = accountResult.getData();
        }
        if (account != null) {
            Response<AuthGroupDto>  userOrgInfo = userGroupFeignClient.selectByUserId(account.getUserId());
            log.info("getUserOrgInfo request misId:{},response:{}", misId, JSON.toJSON(userOrgInfo));
            AuthGroupDto userOrgResult = userOrgInfo.getData();
            if (null == userOrgResult || userOrgResult.getId() <= 0) {
                return null;
            }
            return AdminOrganization.convertByAuthGroupDto(userOrgResult);
        }
        return null;
    }



    @Override
    public AdminOrganization getAdminOrganizationByUserId(long userId) {
        try {
            Response<AuthGroupDto> userOrgInfo = userGroupFeignClient.selectByUserId(userId);
            log.debug("response:{}", userOrgInfo);
            return (userOrgInfo.ok() && userOrgInfo.getData() != null) ? AdminOrganization.convertByAuthGroupDto(userOrgInfo.getData()) : null;
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getAdminOrganizationByUserId err:", e);
        }
        return null;
    }


    @Override
    public List<AdminUserAccountModel> getUserAccountsByIds(List<Long> userIds) {
        Response<List<AuthUserDto>> response = userFeignClient.getAuthUserByIds(userIds);
        log.debug("response:{}", response);
        if (response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            return response.getData().stream().map(AdminUserAccountModel::convertByAuthUserDto).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public AdminUserAccountModel getValidUserAccountById(Long userId) {
        Response<AuthUserDto> response = userFeignClient.getValidAuthUserById(userId);
        log.debug("response:{}", response);
        if (response.ok() && response.getData() != null) {
            return AdminUserAccountModel.convertByAuthUserDto(response.getData());
        }
        return null;
    }

    @Override
    public AdminUserAccountModel getUserByMobile(String mobile) {
        Response<AuthUserDto> response = userFeignClient.getByMobile(mobile);
        if (response.ok() && response.getData() != null) {
            return AdminUserAccountModel.convertByAuthUserDto(response.getData());
        }
        return null;
    }

    @Override
    public SeaAdminUserInfoModel getOrgInfoByMisUseCache(String mis) {
        try {
            return orgInfoByMisCache.get(mis);
        } catch (ExecutionException e) {
            log.error(this.getClass().getSimpleName() + "getOrgInfoByMisUseCache mis:{} error", mis, e);
        }
        return this.getOrgInfoByMis(mis);
    }

    private SeaAdminUserInfoModel getOrgInfoByMis(String mis) {
        Response<AuthUserDto> accountResult = userFeignClient.getByLoginName(mis);
        log.info("getUserAccountsByMis request mis:{},response:{}", mis, JSON.toJSON(accountResult));
        AuthUserDto account = null;
        SeaAdminUserInfoModel model = new SeaAdminUserInfoModel();
        if (accountResult.ok() && accountResult.getData() != null) {
            account = accountResult.getData();
            model.setUserId(account.getUserId());
            model.setName(account.getUserName());
            model.setMis(account.getLoginName());
            model.setMail(account.getEmail());
            String org = "";
            try {
                Response<String> groupNameResponse = userGroupFeignClient.getGroupNameByUserId(account.getUserId());
                if (groupNameResponse.ok() && groupNameResponse.getData() != null) {
                    org = groupNameResponse.getData();
                }
            } catch (Exception e) {
                log.error(this.getClass().getSimpleName() + "getOrgInfoByMisId error", e);
            }
            if (StringUtils.length(org) > MAX_ORGANIZATION) {
                org = StringUtils.right(org, MAX_ORGANIZATION);
            }
            model.setOrgName(org);
        }
        return model;
    }
}
