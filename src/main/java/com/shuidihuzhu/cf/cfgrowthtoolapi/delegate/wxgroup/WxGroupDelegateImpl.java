package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.wxgroup;

import com.google.common.collect.Lists;
import com.shuidihuzhu.kratos.client.api.crm.dto.EnterpriseWxStaffVo;
import com.shuidihuzhu.kratos.client.api.group.CommonCreateGroupChatService;
import com.shuidihuzhu.kratos.client.api.group.WxWorkGroupChatService;
import com.shuidihuzhu.kratos.client.api.group.dto.WwExternalGroupChatUserInfoResp;
import com.shuidihuzhu.kratos.client.api.robot.RobotCommandService;
import com.shuidihuzhu.kratos.client.api.wecom.WecomExternalUserService;
import com.shuidihuzhu.kratos.client.api.wecom.WecomUserService;
import com.shuidihuzhu.kratos.client.api.wecom.dto.*;
import com.shuidihuzhu.kratos.client.common.KratosFeignResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2025-04-14 14:07
 **/
@Slf4j
@Service
public class WxGroupDelegateImpl implements WxGroupDelegate {

    @Autowired
    private WxWorkGroupChatService wxWorkGroupChatService;

    @Autowired
    private RobotCommandService robotCommandService;

    @Autowired
    private CommonCreateGroupChatService commonCreateGroupChatService;

    @Autowired
    private WecomUserService wecomUserService;

    @Autowired
    private WecomExternalUserService wecomExternalUserService;

    public static final String label = "common_create_group_choukuan";

    @Override
    public List<WwExternalGroupChatUserInfoResp> getWxGroupMembers(String chatId) {
        KratosFeignResponse<List<WwExternalGroupChatUserInfoResp>> chatUserListByChatId = wxWorkGroupChatService.getChatUserListByChatId(chatId);
        log.info("getWxGroupMembers chatId:{},response: {}", chatId, chatUserListByChatId);
        return Optional.ofNullable(chatUserListByChatId).map(KratosFeignResponse::getData)
                .orElse(Lists.newArrayList());
    }

    @Override
    public void transferGroupOwner(String requestId, String chatId, String externalUserId) {
        KratosFeignResponse<Boolean> booleanKratosFeignResponse = robotCommandService.transferGroupOwner(requestId, null, chatId, null, externalUserId);
        log.info("transferGroupOwner requestId:{},chatId:{},externalUserId:{},response: {}", requestId, chatId, externalUserId, booleanKratosFeignResponse);
    }

    @Override
    public List<EnterpriseWxStaffVo> getRobotList(String corpId) {
        KratosFeignResponse<List<EnterpriseWxStaffVo>> response = commonCreateGroupChatService.getRobotList(corpId, label);
        log.info("getRobotList response: {}", response);
        return Optional.ofNullable(response).map(KratosFeignResponse::getData).orElse(Lists.newArrayList());
    }

    @Override
    public WecomUserInfoResp queryWecomUserInfo(String corpId, String userId) {
        // 构建请求对象
        WecomUserInfoReq request = new WecomUserInfoReq();
        request.setCorpId(corpId);
        request.setStaffUserId(userId);
        KratosFeignResponse<WecomUserInfoResp> response = wecomUserService.queryWecomUserInfo(request);
        log.info("queryWecomUserInfo response: {}", response);
        return response.getData();

    }

    @Override
    public WecomExternalUserDetailResp queryWecomExternalUserDetail(WecomExternalUserDetailReq req) {
        req.setPage(1);
        req.setPageSize(100);
        KratosFeignResponse<WecomExternalUserDetailResp> wecomExternalUserDetailRespKratosFeignResponse = wecomExternalUserService.queryWecomExternalUserDetail(req);
        log.info("queryWecomExternalUserDetail req:{},response:{}", req, wecomExternalUserDetailRespKratosFeignResponse);
        return wecomExternalUserDetailRespKratosFeignResponse.getData();
    }

}
