package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IPreposeMaterialDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdReportLinkDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.PreposeMaterialForbidModFiledVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfReportShareService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl.CfReportShareServiceImpl;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.client.base.enums.BaseErrorCodeEnum;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialSimpleModel;
import com.shuidihuzhu.cf.client.material.model.authenticity.enumModel.*;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.ClewPreposeMaterialEnumsModel;
import com.shuidihuzhu.client.cf.admin.client.CfRecordClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/9/4 4:12 PM
 */
@Slf4j
@Service
@RefreshScope
public class PreposeMaterialDelegate implements IPreposeMaterialDelegate {

    @Autowired
    private PreposeMaterialClient preposeMaterialClient;
    @Autowired
    private CfMaterialReadClient cfMaterialReadClient;

    @Autowired
    private CfRecordClient cfRecordClient;
    @Autowired
    private ICfReportShareService cfReportShareServiceImpl;

    private static Map<Integer,String> accidentDutieMap = Maps.newHashMap();
    private static Map<Integer,String> accidentTypeMap = Maps.newHashMap();
    private static Map<Integer,String> carNumEnumMap = Maps.newHashMap();
    private static Map<Integer,String> carSellingAmountAreaEnumMap = Maps.newHashMap();
    private static Map<Integer,String> commercialInsuranceTypeMap = Maps.newHashMap();
    private static Map<Integer,String> FinancialAssetsAmountAreaEnumMap = Maps.newHashMap();
    private static Map<Integer,String> hasSellEnumMap = Maps.newHashMap();
    private static Map<Integer,String> homeIncomeAreaEnumMap = Maps.newHashMap();
    private static Map<Integer,String> houseNumEnumMap = Maps.newHashMap();
    private static Map<Integer,String> houseSellingAmountAreaEnumMap = Maps.newHashMap();
    private static Map<Integer,String> medicalInsuranceRateAreaEnumMap = Maps.newHashMap();
    private static Map<Integer,String> medicalInsuranceTypeMap = Maps.newHashMap();
    private static Map<Integer,String> patientIdCardTypeEnumMap = Maps.newHashMap();
    private static Map<Integer,String> payForStateMap = Maps.newHashMap();
    private static Map<Integer,String> relationVersionEnumMap = Maps.newHashMap();
    private static Map<Integer,String> versionEnumMap = Maps.newHashMap();
    private static Map<Integer,String> patientIdentityMapVersion1 = Maps.newHashMap();
    private static Map<Integer,String> patientIdentityMapVersion1_1 = Maps.newHashMap();
    private static Map<Integer,String> raiserPatientRelationMapVersion1 = Maps.newHashMap();
    private static Map<Integer,String> raiserPatientRelationMapVersion1_1 = Maps.newHashMap();
    private static Map<Integer,String> raiserPatientRelationMapVersion1_2 = Maps.newHashMap();
    private static Map<Integer,String> imageUrlTypeEnumMap = Maps.newHashMap();
    private static Map<Integer, String> payAmountAreaEnum = Maps.newHashMap();

    private static Map<Integer, String> illegalDrivingTypeEnumMap = Maps.newHashMap();

    private static Map<Integer, String> criminalTypeEnumMap = Maps.newHashMap();

    private static Map<Integer, String> bigAmountReasonEnumMap = Maps.newHashMap();


    static {
        PreposeMaterialModel.AccidentDuty[] accidentDuties = PreposeMaterialModel.AccidentDuty.values();
        for (PreposeMaterialModel.AccidentDuty accidentDuty :accidentDuties){
            accidentDutieMap.put(accidentDuty.getCode(),accidentDuty.getDesc());
        }
        PreposeMaterialModel.AccidentType[] accidentTypes = PreposeMaterialModel.AccidentType.values();
        for (PreposeMaterialModel.AccidentType accidentType :accidentTypes){
            accidentTypeMap.put(accidentType.getCode(),accidentType.getDesc());
        }
        PreposeMaterialModel.CarNumEnum[] carNumEnums = PreposeMaterialModel.CarNumEnum.values();
        for (PreposeMaterialModel.CarNumEnum carNumEnum :carNumEnums){
            carNumEnumMap.put(carNumEnum.getCode(),carNumEnum.getDesc());
        }
        PreposeMaterialModel.CarSellingAmountAreaEnum[] carSellingAmountAreaEnums = PreposeMaterialModel.CarSellingAmountAreaEnum.values();
        for (PreposeMaterialModel.CarSellingAmountAreaEnum carSellingAmountAreaEnum :carSellingAmountAreaEnums){
            carSellingAmountAreaEnumMap.put(carSellingAmountAreaEnum.getCode(),carSellingAmountAreaEnum.getDesc());
        }
        PreposeMaterialModel.CommercialInsuranceType[] commercialInsuranceTypes = PreposeMaterialModel.CommercialInsuranceType.values();
        for (PreposeMaterialModel.CommercialInsuranceType commercialInsuranceType:commercialInsuranceTypes){
            commercialInsuranceTypeMap.put(commercialInsuranceType.getCode(),commercialInsuranceType.getDesc());
        }
        PreposeMaterialModel.FinancialAssetsAmountAreaEnum[] financialAssetsAmountAreaEnums = PreposeMaterialModel.FinancialAssetsAmountAreaEnum.values();
        for (PreposeMaterialModel.FinancialAssetsAmountAreaEnum financialAssetsAmountAreaEnum:financialAssetsAmountAreaEnums){
            FinancialAssetsAmountAreaEnumMap.put(financialAssetsAmountAreaEnum.getCode(),financialAssetsAmountAreaEnum.getDesc());
        }
        PreposeMaterialModel.HasSellEnum[] hasSellEnums = PreposeMaterialModel.HasSellEnum.values();
        for (PreposeMaterialModel.HasSellEnum hasSellEnum:hasSellEnums){
            hasSellEnumMap.put(hasSellEnum.getCode(),hasSellEnum.getDesc());
        }
        PreposeMaterialModel.HomeIncomeAreaEnum[] homeIncomeAreaEnums = PreposeMaterialModel.HomeIncomeAreaEnum.values();
        for (PreposeMaterialModel.HomeIncomeAreaEnum homeIncomeAreaEnum:homeIncomeAreaEnums){
            homeIncomeAreaEnumMap.put(homeIncomeAreaEnum.getCode(),homeIncomeAreaEnum.getDesc());
        }
        PreposeMaterialModel.HouseNumEnum[] houseNumEnums = PreposeMaterialModel.HouseNumEnum.values();
        for (PreposeMaterialModel.HouseNumEnum houseNumEnum:houseNumEnums){
            houseNumEnumMap.put(houseNumEnum.getCode(),houseNumEnum.getDesc());
        }
        PreposeMaterialModel.HouseSellingAmountAreaEnum[] houseSellingAmountAreaEnums = PreposeMaterialModel.HouseSellingAmountAreaEnum.values();
        for (PreposeMaterialModel.HouseSellingAmountAreaEnum houseSellingAmountAreaEnum:houseSellingAmountAreaEnums){
            houseSellingAmountAreaEnumMap.put(houseSellingAmountAreaEnum.getCode(),houseSellingAmountAreaEnum.getDesc());
        }
        PreposeMaterialModel.MedicalInsuranceRateAreaEnum[] medicalInsuranceRateAreaEnums = PreposeMaterialModel.MedicalInsuranceRateAreaEnum.values();
        for (PreposeMaterialModel.MedicalInsuranceRateAreaEnum medicalInsuranceRateAreaEnum:medicalInsuranceRateAreaEnums){
            medicalInsuranceRateAreaEnumMap.put(medicalInsuranceRateAreaEnum.getCode(),medicalInsuranceRateAreaEnum.getDesc());
        }
        PreposeMaterialModel.MedicalInsuranceType[] medicalInsuranceTypes = PreposeMaterialModel.MedicalInsuranceType.values();
        for (PreposeMaterialModel.MedicalInsuranceType medicalInsuranceType:medicalInsuranceTypes){
            medicalInsuranceTypeMap.put(medicalInsuranceType.getCode(),medicalInsuranceType.getDesc());
        }
        PreposeMaterialModel.PatientIdCardTypeEnum[] patientIdCardTypeEnums = PreposeMaterialModel.PatientIdCardTypeEnum.values();
        for (PreposeMaterialModel.PatientIdCardTypeEnum patientIdCardTypeEnum:patientIdCardTypeEnums) {
            patientIdCardTypeEnumMap.put(patientIdCardTypeEnum.getCode(), patientIdCardTypeEnum.getDesc());
        }
        PreposeMaterialModel.PayForState[] payForStates = PreposeMaterialModel.PayForState.values();
        for (PreposeMaterialModel.PayForState payForState:payForStates){
            payForStateMap.put(payForState.getCode(),payForState.getDesc());
        }
        PreposeMaterialModel.RelationVersionEnum[] relationVersionEnums = PreposeMaterialModel.RelationVersionEnum.values();
        for (PreposeMaterialModel.RelationVersionEnum relationVersionEnum:relationVersionEnums){
            relationVersionEnumMap.put(relationVersionEnum.getVersionCode(),relationVersionEnum.getDesc());
        }
        PreposeMaterialModel.VersionEnum[] versionEnums = PreposeMaterialModel.VersionEnum.values();
        for (PreposeMaterialModel.VersionEnum versionEnum:versionEnums){
            versionEnumMap.put(versionEnum.getVersionCode(),versionEnum.getDesc());
        }
        PreposeMaterialModel.PatientIdentity[] patientIdentities = PreposeMaterialModel.PatientIdentity.values();
        for (PreposeMaterialModel.PatientIdentity patientIdentity:patientIdentities){
            if (patientIdentity.getVersionEnums().contains(PreposeMaterialModel.VersionEnum.VERSION_1)){
                patientIdentityMapVersion1.put(patientIdentity.getCode(),patientIdentity.getDesc());
            }
            if (patientIdentity.getVersionEnums().contains(PreposeMaterialModel.VersionEnum.VERSION_1_1)){
                if (patientIdentity == PreposeMaterialModel.PatientIdentity.MEDIA_GOVERNMENT_NOTICE){
                    continue;
                }
                patientIdentityMapVersion1_1.put(patientIdentity.getCode(),patientIdentity.getDesc());
            }
        }
        PreposeMaterialModel.RaiserPatientRelation[] raiserPatientRelations = PreposeMaterialModel.RaiserPatientRelation.values();
        for (PreposeMaterialModel.RaiserPatientRelation raiserPatientRelation:raiserPatientRelations){
            if (raiserPatientRelation.getRelationVersionEnum().equals(PreposeMaterialModel.RelationVersionEnum.VERSION_1)){
                raiserPatientRelationMapVersion1.put(raiserPatientRelation.getCode(),raiserPatientRelation.getDesc());
            }
            if (raiserPatientRelation.getRelationVersionEnum().equals(PreposeMaterialModel.RelationVersionEnum.VERSION_1_1)){
                raiserPatientRelationMapVersion1_1.put(raiserPatientRelation.getCode(),raiserPatientRelation.getDesc());
            }
            if (raiserPatientRelation.getRelationVersionEnum().equals(PreposeMaterialModel.RelationVersionEnum.VERSION_1_2)){
                raiserPatientRelationMapVersion1_2.put(raiserPatientRelation.getCode(),raiserPatientRelation.getDesc());
            }
        }
        PreposeMaterialModel.MedicalImageTypeEnum[] medicalImageTypeEnums = PreposeMaterialModel.MedicalImageTypeEnum.values();
        for (PreposeMaterialModel.MedicalImageTypeEnum medicalImageTypeEnum: medicalImageTypeEnums){
            imageUrlTypeEnumMap.put(medicalImageTypeEnum.getValue(),medicalImageTypeEnum.getDesc());
        }
        PreposeMaterialModel.FinancialPayAmountAreaEnum[] financialPayAmountAreaEnums = PreposeMaterialModel.FinancialPayAmountAreaEnum.values();
        for (PreposeMaterialModel.FinancialPayAmountAreaEnum financialPayAmountAreaEnum : financialPayAmountAreaEnums){
            payAmountAreaEnum.put(financialPayAmountAreaEnum.getCode(), financialPayAmountAreaEnum.getDesc());
        }
        PreposeMaterialModel.IllegalDrivingTypeEnum[] illegalDrivingTypeEnums = PreposeMaterialModel.IllegalDrivingTypeEnum.values();
        for (PreposeMaterialModel.IllegalDrivingTypeEnum illegalDrivingTypeEnum : illegalDrivingTypeEnums) {
            illegalDrivingTypeEnumMap.put(illegalDrivingTypeEnum.getValue(), illegalDrivingTypeEnum.getDesc());
        }
        PreposeMaterialModel.CriminalTypeEnum[] criminalTypeEnums = PreposeMaterialModel.CriminalTypeEnum.values();
        for (PreposeMaterialModel.CriminalTypeEnum criminalTypeEnum : criminalTypeEnums) {
            criminalTypeEnumMap.put(criminalTypeEnum.getValue(), criminalTypeEnum.getDesc());
        }
        PreposeMaterialModel.BigAmountReasonEnum[] bigAmountReasonEnums = PreposeMaterialModel.BigAmountReasonEnum.values();
        for (PreposeMaterialModel.BigAmountReasonEnum bigAmountReasonEnum : bigAmountReasonEnums) {
            bigAmountReasonEnumMap.put(bigAmountReasonEnum.getValue(), bigAmountReasonEnum.getDesc());
        }
    }

    @Override
    public Response<PreposeMaterialModel.MaterialInfoVo> addOrUpdatePreposeMaterial(PreposeMaterialModel.MaterialInfoVo data) {
        if (StringUtils.isNotBlank(data.getAddSickbed())) {
            data.setSickbed(data.getSickbed() + "@@" + data.getAddSickbed());
        }
        RpcResult<PreposeMaterialModel.MaterialSaveResult> rpcResult = preposeMaterialClient.addOrUpdatePreposeMaterial(data);
        log.info(this.getClass().getSimpleName() + " addOrUpdatePreposeMaterial result:{}", rpcResult);
        if (rpcResult.isSuccess()) {
            data.setId(rpcResult.getData().getId());
            data.setRiskLabels(rpcResult.getData().getRiskLabels());
            data.setRiskLevel(rpcResult.getData().getRiskLevel());
            return NewResponseUtil.makeSuccess(data);
        }
        return NewResponseUtil.makeResponse(rpcResult.getCode(),rpcResult.getMsg(),null);
    }

    @Override
    public RpcResult<PreposeMaterialModel.MaterialInfoVo> selectSnapshotMaterial(String uniqueCode) {
        RpcResult<PreposeMaterialModel.MaterialInfoVo> rpcResult = preposeMaterialClient.selectSnapshotMaterial(uniqueCode);
        log.info(this.getClass().getSimpleName()+" selectSnapshotMaterial result:{}",rpcResult);
        return this.handleMaterialInfoVo(rpcResult);
    }

    @Override
    public RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> selectAllMaterials(String uniqueCode) {
        RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> rpcResult = preposeMaterialClient.selectAllMaterials(uniqueCode);
        log.info(this.getClass().getSimpleName()+" selectAllMaterials result:{}",rpcResult);
        return handleMaterialInfoVoList(rpcResult);
    }

    @Override
    public RpcResult<PreposeMaterialModel.MaterialInfoVo> selectMaterialsById(long preposeMaterialId) {
        RpcResult<PreposeMaterialModel.MaterialInfoVo> rpcResult = preposeMaterialClient.selectMaterialsById(preposeMaterialId);
        log.info(this.getClass().getSimpleName()+" selectMaterialsById result:{}",rpcResult);
        return this.handleMaterialInfoVo(rpcResult);
    }

    @Override
    public RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> selectMaterialsByIds(List<Long> preposeMaterialIds) {
        RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> rpcResult = preposeMaterialClient.selectMaterialsByIds(preposeMaterialIds);
        log.info(this.getClass().getSimpleName()+" selectMaterialsByIds result:{}",rpcResult);
        return this.handleMaterialInfoVoList(rpcResult);
    }

    private RpcResult<PreposeMaterialModel.MaterialInfoVo> handleMaterialInfoVo(RpcResult<PreposeMaterialModel.MaterialInfoVo> rpcResult){
        if (rpcResult.isSuccess() && rpcResult.getData()!=null && StringUtils.isNotBlank(rpcResult.getData().getSickbed())){
            rpcResult.getData().setSickbed(rpcResult.getData().getSickbed().split("@@")[0]);
        }
        return rpcResult;
    }

    private RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> handleMaterialInfoVoList(RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> rpcResult){
        if (rpcResult.isSuccess() && CollectionUtils.isNotEmpty(rpcResult.getData())){
            rpcResult.getData().forEach(model -> {
                if (StringUtils.isNotBlank(model.getSickbed())) {
                    model.setSickbed(model.getSickbed().split("@@")[0]);
                }
            });
        }
        return rpcResult;
    }

    @Override
    public RpcResult<String> deleteMaterialById(long preposeMaterialId) {
        RpcResult<String> rpcResult = preposeMaterialClient.deleteMaterialById(preposeMaterialId);
        log.info(this.getClass().getSimpleName()+" deleteMaterialById result:{}",rpcResult);
        return rpcResult;
    }

    @Override
    public RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> selectMaterialByCaseId(int caseId) {
        RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> rpcResult = preposeMaterialClient.selectMaterialByCaseId(caseId);
        log.info(this.getClass().getSimpleName()+" selectMaterialByCaseId result:{}",rpcResult);
        return this.handleMaterialInfoVoList(rpcResult);
    }
    @Override
    public RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> selectMaterialsByBdCodeAndIdCard(String uniqueCode, String patientIdCard){
        if (StringUtils.isBlank(patientIdCard) || StringUtils.isBlank(uniqueCode)){
            RpcResult failResult = RpcResult.createFailResult(BaseErrorCodeEnum.FALL_BACK);
            failResult.setMsg("身份证号或报备人的唯一标识不能为空");
            return failResult;
        }
        RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> rpcResult = preposeMaterialClient.selectMaterialsByBdCodeAndIdCard(uniqueCode, patientIdCard);
        log.info(this.getClass().getSimpleName()+" selectMaterialsByBdCodeAndIdCard result:{}",rpcResult);
        return handleMaterialInfoVoList(rpcResult);
    }
    @Override
    public RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> selectMaterialsByMisAndIdCard(String mis, String patientIdCard){
        if (StringUtils.isBlank(patientIdCard)){
            RpcResult failResult = RpcResult.createFailResult(BaseErrorCodeEnum.FALL_BACK);
            failResult.setMsg("身份证号或报备人的唯一标识不能为空");
            return failResult;
        }
        RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> rpcResult = preposeMaterialClient.selectMaterialsByMisAndIdCard(mis, patientIdCard);
        log.info(this.getClass().getSimpleName()+" selectMaterialsByMisAndIdCard result:{}",rpcResult);
        return handleMaterialInfoVoList(rpcResult);
    }
    @Override
    /** 判断是否是 新发起的报备 **/
    public boolean isNewReport(PreposeMaterialModel.MaterialInfoVo materialInfoVo){
        return materialInfoVo.getId() == null || materialInfoVo.getId() <= 0;
    }

    @Override
    public void saveCaseId(Long recordId, Long caseId){
        Response<Integer> response = cfRecordClient.saveCaseId(recordId, caseId.intValue());
        log.info(this.getClass().getSimpleName()+" saveCaseId result:{}", JSON.toJSONString(response));
    }

    @Override
    public ClewPreposeMaterialEnumsModel getClewPreposeMaterialEnumsModel(int version, int relationVersion) {
        ClewPreposeMaterialEnumsModel model = new ClewPreposeMaterialEnumsModel();
        model.setAccidentDuty(accidentDutieMap);
        model.setAccidentType(accidentTypeMap);
        model.setCarNumEnum(carNumEnumMap);
        model.setCarSellingAmountAreaEnum(carSellingAmountAreaEnumMap);
        model.setCommercialInsuranceType(commercialInsuranceTypeMap);
        model.setFinancialAssetsAmountAreaEnum(FinancialAssetsAmountAreaEnumMap);
        model.setHasSellEnum(hasSellEnumMap);
        model.setHomeIncomeAreaEnum(homeIncomeAreaEnumMap);
        model.setHouseNumEnum(houseNumEnumMap);
        model.setHouseSellingAmountAreaEnum(houseSellingAmountAreaEnumMap);
        model.setMedicalInsuranceRateAreaEnum(medicalInsuranceRateAreaEnumMap);
        model.setMedicalInsuranceType(medicalInsuranceTypeMap);
        model.setPatientIdCardTypeEnum(patientIdCardTypeEnumMap);
        model.setPayForState(payForStateMap);
        model.setRelationVersionEnum(relationVersionEnumMap);
        model.setVersionEnum(versionEnumMap);
        model.setImageUrlTypeEnum(imageUrlTypeEnumMap);
        model.setPayAmountAreaEnum(payAmountAreaEnum);
        model.setIllegalDrivingTypeEnumMap(illegalDrivingTypeEnumMap);
        model.setCriminalTypeEnumMap(criminalTypeEnumMap);
        model.setBigAmountReasonEnumMap(bigAmountReasonEnumMap);
        model.setFinancialAssetsTypeEnum(FinancialAssetsTypeEnum.getEnumMap());
        model.setPatientLivingWayEnum(PatientLivingWayEnum.getEnumMap());
        model.setOwnershipEnum(OwnershipEnum.getEnumMap());
        model.setHouseTypeEnum(HouseTypeEnum.getEnumMap());
        model.setPatientMarriedEnum(PatientMarriedEnum.getEnumMap());
        model.setHouseSpecialEnum(HouseSpecialEnum.getEnumMap());
        model.setCommercialCarEnum(CommercialCarEnum.getEnumMap());
        model.setCarRangeEnum(CarRangeEnum.getEnumMap());
        model.setHouseDebtEnum(HouseDebtEnum.getEnumMap());
        model.setHouseIncomeEnum(HouseIncomeEnum.getEnumMap());
        model.setSelfHouseRangeEnum(SelfHouseRangeEnum.getEnumMap());
        model.setEconomicSituationEnum(EconomicSituationEnum.getEnumMap());

        if (version == PreposeMaterialModel.VersionEnum.VERSION_1.getVersionCode()) {
            model.setPatientIdentity(patientIdentityMapVersion1);
        } else if (version == PreposeMaterialModel.VersionEnum.VERSION_1_1.getVersionCode()) {
            model.setPatientIdentity(patientIdentityMapVersion1_1);
        }
        model.setRaiserPatientRelation(raiserPatientRelationMapVersion1_2);
        return model;
    }


    @Override
    public OpResult<List<Integer>> selectCaseIds(PreposeMaterialModel.MaterialInfoVo materialInfoVo){
        if (StringUtils.isNotBlank(materialInfoVo.getPatientIdCard())){
            RpcResult<List<Integer>> rpcResultByIdCard = cfMaterialReadClient.selectCaseIdsByIdCard(materialInfoVo.getPatientIdCard());
            if (rpcResultByIdCard.isSuccess()){
                return OpResult.createSucResult(rpcResultByIdCard.getData());
            }
        }else {
            RpcResult<List<Integer>> rpcResultByBirthDay = cfMaterialReadClient.selectCaseIdsByBirthDay(materialInfoVo.getPatientName(),materialInfoVo.getBirthday(),materialInfoVo.getSex());
            if (rpcResultByBirthDay.isSuccess()){
                return OpResult.createSucResult(rpcResultByBirthDay.getData());
            }
        }
        return OpResult.createFailResult(CfGrowthtoolErrorCode.RPC_ERROR);
    }
    @Override
    public PreposeMaterialModel.MaterialInfoVo selectPreposeByIdCard(String patientName, String patientIdCard, int patientIdType) {

        RpcResult<PreposeMaterialModel.MaterialInfoVo> rpcResult = preposeMaterialClient.selectLatelyMaterial(
                Strings.nullToEmpty(patientName),
                patientIdCard, patientIdType);
        log.info("用户发起案例前置报备信息查询.patientName:{} patientIdCard:{} patientIdType:{} result:{}",
                patientName, ShuidiCipherUtils.encrypt(patientIdCard), patientIdType, rpcResult);
        if (rpcResult == null || rpcResult.getData() == null) {
            return null;
        }
        if (rpcResult.isSuccess() && rpcResult.getData()!=null && StringUtils.isNotBlank(rpcResult.getData().getSickbed())){
            rpcResult.getData().setSickbed(rpcResult.getData().getSickbed().split("@@")[0]);
        }
        return rpcResult.getData();
    }

    @Override
    public RpcResult<List<PreposeMaterialSimpleModel>> selectPreposeMaterialSimpleModelByTimeAndRaiseMobile(String startTime, String endTime, String raiseMobile) {
        RpcResult<List<PreposeMaterialSimpleModel>> rpcResult = preposeMaterialClient.selectPreposeMaterialSimpleModelByTimeAndRaiseMobile(startTime, endTime, raiseMobile);
        log.info(this.getClass().getSimpleName()+" selectPreposeMaterialSimpleModelByTimeAndRaiseMobile result:{}",rpcResult);
        return rpcResult;
    }

    @Override
    public RpcResult<List<PreposeMaterialSimpleModel>> selectPreposeMaterialSimpleModelByRaiseMobile(String raiseMobile) {
        RpcResult<List<PreposeMaterialSimpleModel>> rpcResult = preposeMaterialClient.selectPreposeMaterialSimpleModelByRaiseMobile(raiseMobile);
        log.info(this.getClass().getSimpleName()+" selectPreposeMaterialSimpleModelByTimeAndRaiseMobile result:{}",rpcResult);
        return rpcResult;
    }

    @Override
    public RpcResult<List<PreposeMaterialSimpleModel>> selectPreposeMaterialSimpleModelByIds(List<Long> preposeMaterialIds) {
        if (CollectionUtils.isEmpty(preposeMaterialIds)) {
            return RpcResult.createSucResult(Lists.newArrayList());
        }
        RpcResult<List<PreposeMaterialSimpleModel>> rpcResult = preposeMaterialClient.selectPreposeMaterialSimpleModelByIds(preposeMaterialIds);
        log.info(this.getClass().getSimpleName()+" selectPreposeMaterialSimpleModelByIds result:{}",rpcResult);
        return rpcResult;
    }

    @Override
    public OpResult<PreposeMaterialForbidModFiledVo> forbidModificationFiled(int version, int relationVersion, Long preposeMaterialId) {
        OpResult<Boolean> opResult =cfReportShareServiceImpl.checkIsCanModFiled(preposeMaterialId);
        PreposeMaterialForbidModFiledVo preposeMaterialForbidModFiledVo = new PreposeMaterialForbidModFiledVo();
        if (opResult.isSuccess()){
            preposeMaterialForbidModFiledVo.setStatus(0);
        }else{
            preposeMaterialForbidModFiledVo.setStatus(1);
        }
        //getAppConfig不传入参数默认是application，可以传入namespace取指定命名空间下数据
        Config config = ConfigService.getAppConfig();
        String forbidModFileds = config.getProperty(GeneralConstant.APOLLOPREPOSEMATERIALFORBITFILED, GeneralConstant.FORBIDMODFILED);
        preposeMaterialForbidModFiledVo.setForbidModFileds( Splitter.on(",").splitToList(forbidModFileds));
        return OpResult.createSucResult(preposeMaterialForbidModFiledVo);
    }
    @Override
    public List<CfPropertyInsuranceInfoModel> selectCfPropertyInsuranceInfo(List<Integer> caseIds){
        if (CollectionUtils.isEmpty(caseIds)){
            return Lists.newArrayList();
        }
        return caseIds.parallelStream().map(caseId -> cfMaterialReadClient.selectCfPropertyInsuranceInfo(caseId).getData())
                .filter(data -> data != null).collect(Collectors.toList());
    }

    @Override
    public List<Long> listByPatientCardIdAndTime(String patientIdCard, String startTime) {
        if (StringUtils.isNotBlank(patientIdCard) && StringUtils.isNotBlank(startTime)) {
            RpcResult<List<Long>> rpcResult = preposeMaterialClient.listByPatientIdCard(patientIdCard, startTime);
            log.info("listByPatientCardIdAndTime result:{}", rpcResult);
            if (rpcResult.isSuccess()) {
                return rpcResult.getData();
            }
        }
        return List.of();
    }

    @Override
    public String getDiseaseNormalName(int caseId) {
        String key = MaterialExtKeyConst.RAISE_DISEASE_NAME_TAG;
        RpcResult<Map<String, List<String>>> mapRpcResult = cfMaterialReadClient.selectValueByFields(caseId, Lists.newArrayList(key));
        Map<String, String> infoMap = Maps.newHashMap();

        List<String> infos = Optional.ofNullable(mapRpcResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .map(map -> map.get(key))
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(infos)) {
            Gson gson = new Gson();
            Type type = Map.class;
            infoMap = gson.fromJson(infos.get(0), type);
        }
        return String.join(",", infoMap.values());
    }
}
