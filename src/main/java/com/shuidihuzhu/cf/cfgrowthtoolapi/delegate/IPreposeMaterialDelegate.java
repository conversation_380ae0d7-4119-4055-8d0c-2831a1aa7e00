package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.PreposeMaterialForbidModFiledVo;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialSimpleModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.ClewPreposeMaterialEnumsModel;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/9/4 4:07 PM
 */
public interface IPreposeMaterialDelegate {
    Response<PreposeMaterialModel.MaterialInfoVo> addOrUpdatePreposeMaterial(@RequestBody PreposeMaterialModel.MaterialInfoVo data);

    RpcResult<PreposeMaterialModel.MaterialInfoVo> selectSnapshotMaterial(@RequestParam("uniqueCode") String uniqueCode);

    RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> selectAllMaterials(@RequestParam("uniqueCode") String uniqueCode);

    RpcResult<PreposeMaterialModel.MaterialInfoVo> selectMaterialsById(@RequestParam("id") long preposeMaterialId);

    RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> selectMaterialsByIds(@RequestBody List<Long> preposeMaterialIds);

    RpcResult<String> deleteMaterialById(@RequestParam("id") long preposeMaterialId);

    RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> selectMaterialByCaseId(@RequestParam("caseId") int caseId);

    RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> selectMaterialsByBdCodeAndIdCard(String uniqueCode, String patientIdCard);

    RpcResult<List<PreposeMaterialModel.MaterialInfoVo>> selectMaterialsByMisAndIdCard(String mis, String patientIdCard);

    /** 判断是否是 新发起的报备 **/
    boolean isNewReport(PreposeMaterialModel.MaterialInfoVo materialInfoVo);

    void saveCaseId(Long recordId, Long caseId);

    RpcResult<List<PreposeMaterialSimpleModel>> selectPreposeMaterialSimpleModelByTimeAndRaiseMobile(String startTime,
                                                                                                     String endTime,
                                                                                                     String raiseMobile);

    RpcResult<List<PreposeMaterialSimpleModel>> selectPreposeMaterialSimpleModelByRaiseMobile(String raiseMobile);
    RpcResult<List<PreposeMaterialSimpleModel>> selectPreposeMaterialSimpleModelByIds(List<Long> preposeMaterialIds);

    ClewPreposeMaterialEnumsModel getClewPreposeMaterialEnumsModel(int version,int relationVersion);

    /**
     * 根据 报备信息中的 证件号 或 出生日期、姓名、性别 去查找案例 进行绑定
     * @param materialInfoVo
     * @return
     */
    OpResult<List<Integer>> selectCaseIds(PreposeMaterialModel.MaterialInfoVo materialInfoVo);

    PreposeMaterialModel.MaterialInfoVo selectPreposeByIdCard(String patientName, String patientIdCard, int patientIdType);

    /**
     * 获取禁止修改字段
     * @param version
     * @param relationVersion
     * @param preposeMaterialId
     * @return
     */
    OpResult<PreposeMaterialForbidModFiledVo> forbidModificationFiled(int version, int relationVersion, Long preposeMaterialId);

	List<CfPropertyInsuranceInfoModel> selectCfPropertyInsuranceInfo(List<Integer> caseIds);

    List<Long> listByPatientCardIdAndTime(String patientIdCard, String startTime);

    String getDiseaseNormalName(int caseId);
}
