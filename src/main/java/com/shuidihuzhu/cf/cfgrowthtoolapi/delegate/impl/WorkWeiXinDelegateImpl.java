package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IWorkWeiXinDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.api.chaifenbeta.wxgrpcdelegate.WeiXinFeginClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-15
 */
@Slf4j
@Service
public class WorkWeiXinDelegateImpl implements IWorkWeiXinDelegate {

    private static final String ERROR_MSG = "企业微信消息发送失败";

    @Resource
    private AlarmClient alarmClient;
    @Resource(name = "cfOlapCaseStat")
    private RedissonHandler redissonHandler;
    @Autowired
    private WeiXinFeginClient weiXinFeginClient;

    @Override
    public OpResult sendByUser(List<String> userList, String content) {
        log.info("userList: {}, content: {}", userList, content);

        if (CollectionUtils.isEmpty(userList)) {
            log.info("empty userList: {}, content: {}", userList, content);
            return OpResult.createSucResult();
        }
        try {
            alarmClient.sendByUser(userList, content);
        } catch (Exception e) {
            log.error(ERROR_MSG + content, e);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        return OpResult.createSucResult();
    }

    @Override
    public OpResult sendByUser(String operatorNameList, String content) {
        String[] split = operatorNameList.split(GeneralConstant.splitChar);
        return sendByUser(Lists.newArrayList(split), content);
    }

    /**
     * @param groupId
     * @see com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WorkWeiXinGroupEnums
     * @param content
     * @param frequencyLimitSec 频率限制 单位是秒
     * @param num 指定时间段内发送的次数
     * @return
     */
    @Override
    public OpResult sendByGroup(String groupId, String content, int frequencyLimitSec, int num) {
        log.info("groupId: {}, content: {}", groupId, content);
        try {
            MessageDigest instance = MessageDigest.getInstance("SHA-1");
            byte[] hash = instance.digest((groupId+content).getBytes("UTF-8"));
            String encode = Hex.encodeHexString(hash);
            boolean b = redissonHandler.setNX(encode, NumberUtils.INTEGER_ONE, frequencyLimitSec*1000);
            if (!b){
                long sendNum = redissonHandler.incrBy(encode, 1);
                long ttl = redissonHandler.getTTL(encode);
                log.info(" {} 发送消息:{} 当前准备第{}次发送 过期时间是:{}",this.getClass().getName(),content,
                        sendNum,ttl);
                if (ttl==-1){
                    redissonHandler.del(encode);
                }
                if (sendNum>num && num>0){ // 超过限制发送
                    log.info(" {} 发送消息:{} 当前准备第{}次发送 发送失败，超过限制",this.getClass().getName(),content,
                            sendNum,ttl);
                    return OpResult.createSucResult();
                }
            }
            alarmClient.sendByGroup(groupId, content);
        } catch (Exception e) {
            log.error(ERROR_MSG + content, e);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        return OpResult.createSucResult();
    }
    /**
     * @param groupId
     * @see com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WorkWeiXinGroupEnums
     * @param content
     * @return
     */
    @Override
    public OpResult sendByGroup(String groupId , String content) {

        log.info("groupId: {}, content: {}", groupId, content);
        try {
            alarmClient.sendByGroup(groupId, content);
        } catch (Exception e) {
            log.error(ERROR_MSG + content, e);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        return OpResult.createSucResult();
    }

    @Override
    public String generateQrcode(String scene) {
        Response<String> response = weiXinFeginClient.generateQrcode(scene);
        if (response.ok()) {
            return response.getData();
        }
        log.error("generateQrcode fegin接口返回失败:{}", JSON.toJSONString(response));
        return null;
    }
}
