package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.app.AppDeviceDto;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-01-09
 */
public interface IAccountServiceDelegate {

    UserInfoModel getUserInfoByOpenId(String openId);

    UserInfoModel getByUnionId(String unionId);

    UserInfoModel getUserInfoByMobile(String mobile);

    UserInfoModel getUserInfoModelByUserId(long userId);

    OpResult<UserInfoModel> getUserInfoByUserId(long userId);

    List<UserInfoModel> getUserInfoByUserIdBatch(List<Long> userIds);

    void reportingDevice(AppDeviceDto appDeviceDto);

    void unbindingDevice(AppDeviceDto appDeviceDto);

    List<UserInfoModel> getUserInfoByCryptoIdcards(List<String> cryptoIdcardList);

    List<UserInfoModel> listByMobiles(List<String> mobile);
}
