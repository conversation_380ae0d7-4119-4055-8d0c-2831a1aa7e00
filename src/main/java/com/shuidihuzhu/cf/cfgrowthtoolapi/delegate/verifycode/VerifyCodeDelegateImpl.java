package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.verifycode;

import com.shuidihuzhu.account.verifycode.client.VerifyCodeV4Client;
import com.shuidihuzhu.account.verifycode.model.VerifyCodeRequestV4;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.enhancer.model.response.EnhancerErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class VerifyCodeDelegateImpl implements IVerifyCodeDelegate {

    @Resource
    private VerifyCodeV4Client verifyCodeV4Client;

    @Override
    public Response<Void> checkVerifyCode(String key, String mobile, String verifyCode, String clientIp) {

        //手机号加密
        String cryptoMobile = ShuidiCipherUtils.encrypt(mobile);

        VerifyCodeRequestV4 dto = new VerifyCodeRequestV4();
        dto.setKey(key);
        dto.setCode(verifyCode);
        dto.setClientIp(clientIp);
        dto.setCryptoMobile(cryptoMobile);

        //校验验证码
        Response response = verifyCodeV4Client.checkVerifyCode(dto);
        return mapMsgResponseNew(response);
    }

    public Response<Void> mapMsgResponseNew(Response response) {
        if (Objects.isNull(response)) {
            return NewResponseUtil.makeError(EnhancerErrorCode.RPC_FAIL);
        }
        return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), null);
    }
}
