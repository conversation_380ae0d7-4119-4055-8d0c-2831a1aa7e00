package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.ai.algo.client.ClaimAiCharacterClient;
import com.shuidihuzhu.ai.algo.model.vo.IdCardRecognitionVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IClaimAiClientDelegate;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2021-07-22 3:06 下午
 **/
@Slf4j
@Service
public class ClaimAiClientDelegateImpl implements IClaimAiClientDelegate {

    @Autowired
    private ClaimAiCharacterClient claimAiCharacterClient;

    @Override
    public Response<IdCardRecognitionVo> idcardRecognition(String imageUrl, String side) {
        Response<IdCardRecognitionVo> response =  claimAiCharacterClient.idcardRecognition(imageUrl,"front");
        log.info("idRecognition imageUrl={},response={}",imageUrl,response);
        return response;
    }
}
