package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICaseLevelDelegate;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2022-06-27 16:40
 **/
@Slf4j
@Service
public class CaseLevelDelegateImpl implements ICaseLevelDelegate {

    @Resource
    private FaceApiClient faceApiClient;

    public static final String packeageId = "cf_case_prob_value";


    //todo：调用接口判断案例打分
    @Override
    public String caseLevel(String infoId) {
        if (StringUtils.isBlank(infoId)) {
            return null;
        }
        Response<Map<String, String>> response = faceApiClient.caseQuery(infoId, Lists.newArrayList(packeageId));
        log.info("caseLevel caseId:{}response:{}", infoId, response);
        Map<String, String> data = response.getData();
        return data.get(packeageId);
    }
}
