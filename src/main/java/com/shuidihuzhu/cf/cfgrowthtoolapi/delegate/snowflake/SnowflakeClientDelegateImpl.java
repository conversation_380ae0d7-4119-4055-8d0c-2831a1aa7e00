package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.snowflake;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.baseservice.snowflake.v1.SnowflakeClient;
import com.shuidihuzhu.client.baseservice.snowflake.v1.model.SnowflakeResult;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2025-04-16 11:18
 **/
@Service
public class SnowflakeClientDelegateImpl implements SnowflakeClientDelegate {

    @Autowired
    private SnowflakeClient snowflakeClient;

    @Override
    public long getSnowflakeId() {
        SnowflakeResult snowflakeResult = snowflakeClient.getId64();
        if (snowflakeResult != null && snowflakeResult.getId() != null) {
            return snowflakeResult.getId().longValue();
        }
        return -1;
    }

    @Override
    public List<Long> batchGetSnowflakeId(int size) {
        SnowflakeResult snowflakeResult = snowflakeClient.getId64();
        if (snowflakeResult != null && CollectionUtils.isNotEmpty(snowflakeResult.getIds())) {
            return snowflakeResult.getIds().stream()
                    .map(id -> Optional.ofNullable(id).map(Number::longValue).orElse(-1L))
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
}
