package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.response.OpResult;

/**
 * @author: wanghui
 * @create: 2019/12/3 7:30 PM
 */
public interface IVerifyCodeClientDelegate {
    OpResult<Boolean> checkVerifyCodeIsSuccess(String mobile, String verifyCode, Long userId, String ip);

    /**
     * 严格验证手机验证码,消息系统异常,也算认证失败
     * @param mobile
     * @param verifyCode
     * @param userId
     * @param clientIp
     * @return
     */
    OpResult<Void> strictCheckVerifyCodeIsSuccess(String mobile, String verifyCode, Long userId, String clientIp);
}
