package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.strategy;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.GrowthtoolCasePoolModelVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11  14:31
 */
@Service
public class PersonPepCasePoolContext {

    @Autowired
    private List<IPersonPepCasePoolStrategy> personPepCasePoolStrategies;

    public GrowthtoolCasePoolModelVo executeStrategyByPersonPepCasePool(String userId, int factBizType, int caseType) {
        IPersonPepCasePoolStrategy personPepCasePoolStrategy = getPersonPepCasePoolStrategy(caseType);
        if (personPepCasePoolStrategy == null) {
            return null;
        }
        return personPepCasePoolStrategy.executeStrategyByPersonPepCasePool(userId, factBizType);
    }

    private IPersonPepCasePoolStrategy getPersonPepCasePoolStrategy(int caseType) {
        return personPepCasePoolStrategies
                .stream()
                .filter(item -> item.support(caseType))
                .findFirst()
                .orElse(null);
    }

}
