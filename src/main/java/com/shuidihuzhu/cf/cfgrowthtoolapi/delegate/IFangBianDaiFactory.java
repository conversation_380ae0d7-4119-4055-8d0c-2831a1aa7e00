package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.OrderCallbackModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiOrderDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfJiekongDeviceDO;
import com.shuidihuzhu.cf.response.OpResult;


/**
 * @author: wanghui
 * @create: 2020/8/24 3:42 下午
 */
public interface IFangBianDaiFactory {
    OpResult<CfFangbiandaiOrderDO> createOrder(CfJiekongDeviceDO cfJiekongDeviceDO, String orderID, int priceInFen);

    OpResult<Boolean> handleCallbackForUpdate(String orderId, String supplierOrderStatus,String remark, String deviceSupplier);

    OpResult<Boolean> queryOrderForUpdate(CfFangbiandaiOrderDO orderFromDB);

    Integer parseSuccessStatus(String deviceSupplier);
}
