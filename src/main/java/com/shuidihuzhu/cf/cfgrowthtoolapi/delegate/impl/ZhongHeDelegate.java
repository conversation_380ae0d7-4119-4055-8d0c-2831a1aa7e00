package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.JieKongFeignClient;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.AccessTokenModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.DeviceModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.JieKongResponseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.OrderInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.zhonghe.ZhongHeFeignClient;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.zhonghe.model.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.configuration.CustomSensitiveConfig;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IFangBianDaiDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IWorkWeiXinDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiOrderDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfJiekongDeviceDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.FangBianDaiEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfFangbiandaiService;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.MD5Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2020-09-01 18:21
 */
@Component("ZhongHeDelegate")
@Slf4j
@RefreshScope
public class ZhongHeDelegate implements IFangBianDaiDelegate {


	@Resource(name = "cfOlapCaseStat")
	private RedissonHandler cfOlapRedissonHandler;
	@Autowired
	private ZhongHeFeignClient zhongHeFeignClient;
	@Value("${apollo.zhonghe.username:sdc}")
	private String username = "sdc";

	@Resource(name = "secret-zhong-he")
	private CustomSensitiveConfig.RmsSecret zhongHeSecret;

	@Autowired
	private IWorkWeiXinDelegate workWeiXinDelegateImpl;
	@Autowired
	private ICfFangbiandaiService cfFangbiandaiService;

	protected String getAccessToken() {
		ZhongHeResponseModel<String> accessToken = zhongHeFeignClient.getAccessToken(username, zhongHeSecret.getKey());
		log.info(this.getClass().getName()+" getAccessToken result:{}",accessToken);
		if (accessToken!=null && accessToken.getCode()==1){
			int timemills = 100*60 * 1000;
			cfOlapRedissonHandler.setEX(GeneralConstant.zhongheAccesstoken,accessToken.getData(),timemills);
			return accessToken.getData();
		}
		workWeiXinDelegateImpl.sendByUser("wanghui","获取中和的访问accessToken失败");
		return null;
	}

	public void single(String deviceID, String machineID) {
		ZhongHeResponseModel<ZhongHeDeviceModel> single = zhongHeFeignClient.single(getAccessTokenFromRedis(false),deviceID,machineID);
		if (single!=null && checkaccesstokenfail(Integer.valueOf(single.getCode()))){
			single = zhongHeFeignClient.single(getAccessTokenFromRedis(false),deviceID,machineID);
		}
	}

	@Override
	public OpResult<CfFangbiandaiOrderDO> createOrder(CfJiekongDeviceDO cfJiekongDeviceDO, String orderID, int priceInFen) {

		ZhongHeResponseModel<ZhongHeOrderModel> interfaceOrder = this.interfaceOrder(getAccessTokenFromRedis(false),cfJiekongDeviceDO.getDeviceId(),orderID,priceInFen);
		if (interfaceOrder!=null && checkaccesstokenfail(interfaceOrder.getCode())){
			interfaceOrder = this.interfaceOrder(getAccessTokenFromRedis(true),cfJiekongDeviceDO.getDeviceId(),orderID,priceInFen);
		}
		if (interfaceOrder!=null && interfaceOrder.getData()!=null){
			CfFangbiandaiOrderDO cfFangbiandaiOrderDO = new CfFangbiandaiOrderDO();
			ZhongHeStatusEnum zhongHeStatusEnum = ZhongHeStatusEnum.parse(interfaceOrder.getData().getStatus());
			cfFangbiandaiOrderDO.setStatus(zhongHeStatusEnum.statusEnum.getStatus());
			cfFangbiandaiOrderDO.setJiekongStatus(interfaceOrder.getData().getStatus());
			cfFangbiandaiOrderDO.setTransactionId(interfaceOrder.getData().getTransactionID());
			//  减库存操作
			if (cfFangbiandaiOrderDO.getJiekongStatus() == ZhongHeDelegate.ZhongHeStatusEnum.SUCCESS.getStatus()) {
				cfFangbiandaiService.decrStockNum(cfJiekongDeviceDO.getDeviceId(), cfJiekongDeviceDO.getDeviceSupplier());
			}
			return OpResult.createSucResult(cfFangbiandaiOrderDO);
		}
		// 业务不在需要提醒
		//workWeiXinDelegateImpl.sendByGroup(GeneralConstant.ALARMFANGBIANDAI,String.format("设备:%s 下单失败 原因：%s",
		//		cfJiekongDeviceDO.getMachineId() + " - " + cfJiekongDeviceDO.getDeviceId(),
		//		interfaceOrder==null?"请求中和失败":interfaceOrder.getCode()+","+interfaceOrder.getMsg()));
		log.info(this.getClass().getName()+" 供应商:{} 设备:{} 下单失败 :{}",cfJiekongDeviceDO.getDeviceSupplier(),cfJiekongDeviceDO.getDeviceId(),interfaceOrder);
		return OpResult.createFailResult(CfGrowthtoolErrorCode.FALLBACK);
	}

	/**
	 * post请求
	 * @param accessToken
	 * @param deviceId
	 * @param orderId
	 * @param price
	 * @return
	 */
	private ZhongHeResponseModel<ZhongHeOrderModel> interfaceOrder(String accessToken,String deviceId,String orderId, int price){
		try {
			long start = new Date().getTime();
			ZhongHeResponseModel<ZhongHeOrderModel> responseModel = zhongHeFeignClient.interfaceOrder(accessToken,deviceId,orderId,price);
			long end = new Date().getTime();
			log.info("interfaceOrder request:{},cost:{},result:{}",String.join("#",accessToken,deviceId,orderId,price+""),(end-start)+"ms",JSON.toJSON(responseModel));
			return responseModel;
		}catch (Exception e){
			log.warn("interfaceOrder Exception: param:{}",String.join("#",accessToken,deviceId,orderId,price+""));
			return null;
		}
	}

	@Override
	public OrderInfoModel query(CfFangbiandaiOrderDO orderFromDB) {
		ZhongHeResponseModel<ZhongHeQueryOrderModel> query = zhongHeFeignClient.query(getAccessTokenFromRedis(false), orderFromDB.getOrderId());
		if (query!=null && checkaccesstokenfail(query.getCode())){
			query = zhongHeFeignClient.query(getAccessTokenFromRedis(true),orderFromDB.getOrderId());
		}
		if (query!=null && query.getData()!=null){
			OrderInfoModel orderInfoModel = new OrderInfoModel();
			orderInfoModel.setCloseTime(query.getData().getCloseTime());
			orderInfoModel.setPayTime(query.getData().getPayTime());
			orderInfoModel.setShipTime(query.getData().getPayTime());
			orderInfoModel.setErrorMsg(query.getData().getStatusDesc());
			orderInfoModel.setErrorCode(query.getData().getStatus()+"");
			ZhongHeStatusEnum zhongHeStatusEnum = ZhongHeStatusEnum.parse(query.getData().getStatus());
			if (zhongHeStatusEnum==null){
				log.error(this.getClass().getSimpleName()+" ZhongHeStatusEnum.parse result is null  orderId:{} param:{}",orderFromDB.getOrderId(),query.getData().getStatus());
				return null;
			}
			orderInfoModel.setStatus(zhongHeStatusEnum.getStatus());
			return orderInfoModel;
		}
		return null;
	}
	@Override
	public OpResult<Boolean> handleCallbackForUpdate(String orderId, String supplierOrderStatus,String remark) {
		log.info(this.getClass().getSimpleName()+" zhonghe 没有回调  param:{}",String.join("#",orderId,supplierOrderStatus,remark));
		return OpResult.createSucResult(true);
	}


	@Override
	public OpResult<Boolean> queryOrderForUpdate(CfFangbiandaiOrderDO orderFromDB) {
		ZhongHeStatusEnum zhongHeStatusEnumFromDB = ZhongHeStatusEnum.parse(orderFromDB.getJiekongStatus());
		if (zhongHeStatusEnumFromDB.isEnd()) {
			log.info(this.getClass().getName() + " orderId:{} 该订单已结束 无需查询", orderFromDB.getOrderId());
			return OpResult.createSucResult(false);
		}
		OrderInfoModel query = this.query(orderFromDB);
		// 查询为空 或者 状态和db中的一样 则无需处理 对于mq消费来说 此时需要重新丢一个消息 继续查
		if (query == null || orderFromDB.getJiekongStatus().equals(query.getStatus()) || query.getStatus() == null) {
			return OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
		}
		orderFromDB.setPayTime(query.getPayTime());
		orderFromDB.setShipTime(query.getShipTime());
		orderFromDB.setCloseTime(query.getCloseTime());
		orderFromDB.setRemark(query.getErrorMsg());
		return fullAndUpdateOrderStatus(orderFromDB,query.getStatus());
	}

	private OpResult<Boolean> fullAndUpdateOrderStatus(CfFangbiandaiOrderDO orderFromDB, Integer jiekongStatus){
		ZhongHeStatusEnum zhognHeStatusEnumFromJieKong = ZhongHeStatusEnum.parse(jiekongStatus);
		if (zhognHeStatusEnumFromJieKong == null) {
			log.info(this.getClass().getName() + " ZhongHeStatusEnum.parse result is null. jiekongStatus:{}", jiekongStatus);
			return OpResult.createFailResult(CfGrowthtoolErrorCode.ILLEGALLY);
		}
		// 否则 需要更新db中的数据
		FangBianDaiEnums.StatusEnum statusEnum = zhognHeStatusEnumFromJieKong.statusEnum;
		if (statusEnum == null) {
			log.info(this.getClass().getName() + " FangBianDaiEnums.StatusEnum.parseByJiekongStatus result is null. zhognHeStatusEnumFromJieKong:{}", zhognHeStatusEnumFromJieKong.getStatus());
			return OpResult.createFailResult(CfGrowthtoolErrorCode.ILLEGALLY);
		}
		orderFromDB.setStatus(statusEnum.getStatus());
		orderFromDB.setJiekongStatus(zhognHeStatusEnumFromJieKong.getStatus());

		cfFangbiandaiService.updateOrder(orderFromDB);

		if (zhognHeStatusEnumFromJieKong.getStatus() == ZhongHeStatusEnum.SUCCESS.getStatus()) {
			cfFangbiandaiService.decrStockNum(orderFromDB.getDeviceId(), orderFromDB.getDeviceSupplier());
		}
		if (zhognHeStatusEnumFromJieKong.isEnd()) {
			if (zhognHeStatusEnumFromJieKong.getStatus() == ZhongHeStatusEnum.FAIL.getStatus()) {
				log.error(this.getClass().getName() + " 供应商:{}  设备:{} 订单被关闭 :{}", orderFromDB.getDeviceSupplier(),orderFromDB.getDeviceId(), jiekongStatus);
			}
			return OpResult.createSucResult();
		}
		return OpResult.createSucResult(true);
	}
	@Override
	public List<CfJiekongDeviceDO> list() {
		ZhongHeResponseModel<ZhongHeQueryDeviceListModel> zhongHeResponseModel= zhongHeFeignClient.list(getAccessTokenFromRedis(false),1,1000);
		if (zhongHeResponseModel!=null && checkaccesstokenfail(zhongHeResponseModel.getCode())){
			zhongHeResponseModel = zhongHeFeignClient.list(getAccessTokenFromRedis(true),1,1000);
		}
		if (zhongHeResponseModel!=null
				&& zhongHeResponseModel.getData()!=null
				&& CollectionUtils.isNotEmpty(zhongHeResponseModel.getData().getList())){
			return CfJiekongDeviceDO.buildUpdateDeviceStatusModelByZhongHe(zhongHeResponseModel.getData().getList());
		}
		return Lists.newArrayList();
	}

	@Override
	public int getSuccessStatus() {
		return ZhongHeStatusEnum.SUCCESS.getStatus();
	}

	public void reset(String deviceId){
		ZhongHeResponseModel<String> reset = zhongHeFeignClient.reset(getAccessTokenFromRedis(false), deviceId);
		if (reset!=null && checkaccesstokenfail(reset.getCode())){
			reset = zhongHeFeignClient.reset(getAccessTokenFromRedis(true), deviceId);
		}
		log.info(this.getClass().getSimpleName()+" reset result:{}",reset);
	}

	// 判断是否是 token错误
	private boolean checkaccesstokenfail(int code){
		return code==-9;
	}

	private String getAccessTokenFromRedis(boolean needRefresh){
		if (needRefresh){
			return getAccessToken();
		}
		String accessToken = cfOlapRedissonHandler.get(GeneralConstant.zhongheAccesstoken, String.class);
		if (StringUtils.isBlank(accessToken)){
			return getAccessToken();
		}
		return accessToken;
	}

	@Getter
	public enum ZhongHeStatusEnum {
		DEFAULT(-1, "默认",false,FangBianDaiEnums.StatusEnum.DEFAULT),
		PAID(0, "未出货",false,FangBianDaiEnums.StatusEnum.PAID),
		SUCCESS(1, "已出货",true,FangBianDaiEnums.StatusEnum.SUCCESS),
		FAIL(2, "出货失败",true,FangBianDaiEnums.StatusEnum.FAIL),
		UN_RESPONSE(3, "设备未响应",true,FangBianDaiEnums.StatusEnum.FAIL),
		;
		int status;

		String desc;

		boolean isEnd;

		FangBianDaiEnums.StatusEnum statusEnum;

		ZhongHeStatusEnum(int status, String desc,boolean isEnd,FangBianDaiEnums.StatusEnum statusEnum) {
			this.status = status;
			this.desc = desc;
			this.isEnd = isEnd;
			this.statusEnum = statusEnum;
		}
		public static ZhongHeStatusEnum parse(int status){
			for (ZhongHeStatusEnum e : ZhongHeStatusEnum.values()) {
				if (e.getStatus()==status) {
					return e;
				}
			}
			return null;
		}
		public static boolean success(int status){
			if (status==SUCCESS.getStatus() || status==PAID.getStatus()){
				return true;
			}
			return false;
		}
	}
}
