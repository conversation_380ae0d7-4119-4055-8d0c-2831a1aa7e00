package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 *拼接内容可以使用下面工具
 * @date 2019-05-15
 */
public interface IWorkWeiXinDelegate {
    OpResult sendByUser(List<String> operatorNameList, String content);

    OpResult sendByUser(String operatorNameList, String content);

    OpResult sendByGroup(String groupId, String content, int frequencyLimitOfSeconds, int num);

    OpResult sendByGroup(String groupId, String content);

    String generateQrcode(String scene);
}
