package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.client.tianrun.TianRunAXBFeignClient;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.tianrun.model.TianRunResponseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ITianRunAXBDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IWorkWeiXinDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2020-05-27 20:28
 */
@Service
@Slf4j
public class TianRunAXBDelegate implements ITianRunAXBDelegate {

	private static String appid = "5600591";
	private static String token = "99fd654958581a06";

	@Autowired
	private TianRunAXBFeignClient tianRunAXBFeignClient;

	@Override
	public OpResult outtransfer(String telX, String transferms) {
		long timestamp = System.currentTimeMillis() / 1000;
		TianRunResponseModel<String> responseModel = tianRunAXBFeignClient.outtransfer(appid, timestamp, getSign(timestamp), telX, transferms,
				1, 0, null, null, null);
		return handleResponseModel("outtransfer",responseModel);
	}

	@Override
	public OpResult outtransferDel(String telX) {
		long timestamp = System.currentTimeMillis() / 1000;
		TianRunResponseModel<String> responseModel = tianRunAXBFeignClient.outtransferDel(appid, timestamp, getSign(timestamp), telX);
		return handleResponseModel("outtransferDel",responseModel);
	}

	private OpResult handleResponseModel(String operateName,TianRunResponseModel<String> responseModel){
		if (responseModel.getResult()!=0){
			log.warn(this.getClass().getName()+" {} 操作失败 :{}",operateName,responseModel);
			return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL,responseModel.getDescription());
		}
		return OpResult.createSucResult();
	}

	private String getSign(long timestamp){
		return MD5Util.getMD5HashValue(appid + token + timestamp).toLowerCase();
	}
}
