package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pf.model.CityVo;
import com.shuidihuzhu.pr.common.model.dto.pinyin.PinyinAnalysis;
import com.shuidihuzhu.pr.patient.model.dto.patient.Patient;
import com.shuidihuzhu.pr.common.model.vo.PrPatientCommonMapVo;
import com.shuidihuzhu.pr.model.param.PrPermissionCheck;
import com.shuidihuzhu.pr.patient.model.dto.clew.outside.*;
import com.shuidihuzhu.pr.user.model.user.PrUserDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-05-11
 */
public interface IPatientRecruitClientDelegate {

    /**
     * 招募经理权限批量查询接口
     *
     * @param misList
     * @return
     */
    List<PrPermissionCheck> hasPermissionByMisList(List<String> misList, int permission);

    /**
     * 招募经理权限单个查询接口
     * @param mis
     * @return
     */
    PrPermissionCheck hasPermissionByMis(String mis);

    /**
     * 添加患者线索检查
     */
    Response<Void> checkParam(PatientRequestParam patientRequestParam);

    /**
     * 添加患者线索接口
     * @param patientRequestParam
     * @return
     */
    Response<Void> addClew(PatientRequestParam patientRequestParam);

    /**
     * 验证手机号是否重复
     * @param phone
     * @return
     */
    Response<Patient> getByPhone(String phone);

    /**
     * 患者线索流转信进度查询
     * @param patientId
     * @return
     */
    Response<PatientNodeInfo> getNodeInfoByPatientId(long patientId);

    /**
     * 提供枚举接口
     * @return
     */
    Response<PrPatientCommonMapVo> getCommonEnum();

    /**
     * 疾病分期接口查询
     * @param type
     * @return
     */
    Response<String> searchForStaging(int type);

    /**
     * 疾病分期接口查询V2
     * @param disease
     * @return
     */
    Response<String> searchForStagingV2(int disease);

    /**
     * 获取招募经理信息
     * @param mis
     * @return
     */
    PrUserDto getByMisId(String mis);

    /**
     * 获取省市接口
     * @return
     */
    Response<List<CityVo>> allCity();

    /**
     * 查询所有科室
     * @return
     */
    Response<Map<Character, List<PinyinAnalysis>>> allDepartmentOnSort();

    /**
     * 查看跟进记录
     * @param preassignedMisId
     * @param phone
     * @param pageNum
     * @param pageSize
     * @return
     */
    Response<List<PatientVisitDto>> visitList(String preassignedMisId, String phone, int pageNum, int pageSize);


    /**
     * @param simpleCheckParam
     * @return false:不需要展示, true:需要展示
     */
    boolean simpleCheck(PatientClewSimpleCheckParam simpleCheckParam);

    PatientRequestParam getByClewId(long clueId, int clueType);


}
