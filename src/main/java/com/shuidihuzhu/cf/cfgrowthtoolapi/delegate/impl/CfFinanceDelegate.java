package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfFinanceDelegate;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.feign.order.CfOrderTidbFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCash;
import com.shuidihuzhu.cf.finance.model.finance.drawcash.CfDrawCashApplyV2;
import com.shuidihuzhu.cf.finance.model.po.CrowdfundingDonateInfoPo;
import com.shuidihuzhu.cf.finance.model.vo.CrowdfundingDonateInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2020-01-08 21:28
 */
@Service
@Slf4j
public class CfFinanceDelegate implements ICfFinanceDelegate {
    @Autowired
    private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;

    @Autowired
    private CfOrderTidbFeignClient cfOrderTidbFeignClient;

    @Override
    public CfDrawCash getByInfoUuid(String infoUuid) {
        FeignResponse<CfDrawCash> feignResponse = cfFinanceDrawCashFeignClient.getByInfoUuid(infoUuid);
        return feignResponse.getData();
    }

    @Override
    public CfDrawCashApplyV2 getCaseApplyList(String infoUuid) {
        FeignResponse<List<CfDrawCashApplyV2>> caseApplyList = cfFinanceDrawCashFeignClient.getCaseApplyList(infoUuid);
        CfDrawCashApplyV2 cfDrawCashApplyV2 = null;
        if (CollectionUtils.isNotEmpty(caseApplyList.getData())) {
            cfDrawCashApplyV2 = caseApplyList.getData().stream().sorted(Comparator.comparing(CfDrawCashApplyV2::getApplyTime).reversed()).collect(Collectors.toList()).get(0);
        }
        return cfDrawCashApplyV2;
    }

    @Override
    public CrowdfundingDonateInfoVo getDonateInfoExclusionAdviser(int caseId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds) || caseId <= 0) {
            return null;
        }
        CrowdfundingDonateInfoPo donateInfoPo = new CrowdfundingDonateInfoPo();
        donateInfoPo.setCaseId(caseId);
        donateInfoPo.setUserIds(userIds);

        FeignResponse<CrowdfundingDonateInfoVo> donateInfoVoFeignResponse = cfOrderTidbFeignClient.getDonateInfoExclusionAdviser(donateInfoPo);
        log.info("caseId:{},response:{}", caseId, donateInfoVoFeignResponse);
        return donateInfoVoFeignResponse.getData();
    }


    public Map<Integer, CrowdfundingDonateInfoVo> getDonateCountExclusionMinAmount(List<Integer> caseIds, int minAmount) {
        FeignResponse<Map<Integer, CrowdfundingDonateInfoVo>> result = cfOrderTidbFeignClient.getDonateCountExclusionMinAmount(caseIds, minAmount);
        return Optional.ofNullable(result).map(FeignResponse::getData).orElse(new HashMap<>());
    }


}
