package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientpt.PatientShareData;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientpt.PatientShareDataAll;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.oneservice.PatientShareDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2022-11-28 10:50
 **/
@Slf4j
@Service
public class OneServiceDelegate {

    @Autowired
    private PatientShareDataService patientShareDataService;


    public PatientShareData getPatientShareData(String uniqueCode, int timeChoice) {

        String dt = DateTime.now().minusDays(1).toString(GrowthtoolUtil.ymdfmt);
        List<PatientShareDataAll> patientShareDataAllList = patientShareDataService.getPatientShareData(dt, uniqueCode);
        PatientShareData patientShareData = new PatientShareData();
        if (CollectionUtils.isNotEmpty(patientShareDataAllList)) {
            int shareCount = 0;
            int visitorNum = 0;
            int visitCount = 0;
            for (PatientShareDataAll patientShareDataAll : patientShareDataAllList) {
                if (timeChoice == 4) {
                    shareCount += patientShareDataAll.getShareCntTotalD();
                    visitorNum += patientShareDataAll.getVisitNumTotalD();
                    visitCount += patientShareDataAll.getVisitCntTotalD();
                } else if (timeChoice == 3) {
                    shareCount += patientShareDataAll.getShareCnt30d();
                    visitorNum += patientShareDataAll.getVisitNum30d();
                    visitCount += patientShareDataAll.getVisitCnt30d();
                } else if (timeChoice == 2) {
                    shareCount += patientShareDataAll.getShareCnt7d();
                    visitorNum += patientShareDataAll.getVisitNum7d();
                    visitCount += patientShareDataAll.getVisitCnt7d();
                } else {
                    shareCount += patientShareDataAll.getShareCntD();
                    visitorNum += patientShareDataAll.getVisitNumD();
                    visitCount += patientShareDataAll.getVisitCntD();
                }
            }
            patientShareData.setShareCount(shareCount);
            patientShareData.setVisitorNum(visitorNum);
            patientShareData.setVisitCount(visitCount);
        }
        return patientShareData;
    }
}
