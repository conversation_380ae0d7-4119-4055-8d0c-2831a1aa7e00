package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.baseservice.pay.model.*;
import com.shuidihuzhu.client.baseservice.pay.model.v3.PayInfoParamV3;
import com.shuidihuzhu.client.baseservice.pay.model.v3.RefundLaunchParamV3;

/**
 * @author: fengxuan
 * @create 2020-06-29 10:37 上午
 **/
public interface PayClientRpcDelegate {

    PayRpcResponse<PayResultV2> unifiedOrder(PayInfoParamV3 payInfoParamV3);

    PayInnerCallBack verifyCallback(PayInnerCallBackInfo payInnerCallBackInfo);

    RefundResult refund(RefundLaunchParamV3 launchParamV3);

}
