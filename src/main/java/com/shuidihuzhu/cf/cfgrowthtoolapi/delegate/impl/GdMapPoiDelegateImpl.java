package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IGdMapPoiDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.BaseGdMapModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.gdmap.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.outfeignclient.IGdMapFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RefreshScope
public class GdMapPoiDelegateImpl implements IGdMapPoiDelegate {
    @Autowired
    private IGdMapFeignClient gdMapFeignClient;

    private static String key1 = "fb77ea030e54b795624e8769a60b4831";

    @Override
    public BaseGdMapModel<List<GdMapPoiModel>> getHospitalPoiByNameAndCity(String hospitalName, String cityName,int pageSize) {
        String result = gdMapFeignClient.getPoiByKeyword(key1,hospitalName,
                "医疗保健服务",cityName,"json",pageSize,1,"all");
        log.info("getHospitalPoiByNameAndCity param:{},{} ,result:{}",hospitalName,cityName,result);
        BaseGdMapModel<List<GdMapPoiModel>> gdMapModel = new BaseGdMapModel<>();
        if(StringUtils.isBlank(result)){
            gdMapModel.setData(Lists.newArrayList());
            return gdMapModel;
        }
        try {
            GdMapPoiResult gdMapPoiResult = new Gson().fromJson(result,GdMapPoiResult.class);
            log.info(" result for poi:{}", JSON.toJSONString(gdMapPoiResult));
            gdMapModel.setData(gdMapPoiResult.getPoiModels());
            return gdMapModel;
        }catch (Exception e){
            log.error("syncGdmapHospital Exception {},{}",hospitalName,cityName,e);
        }
        return gdMapModel;
    }


    @Override
    public BaseGdMapModel<List<GdMapPoiModel>> listRecommendPlace(String keyWork, String city, int pageSize) {
        String result = gdMapFeignClient.getPoiByKeyword(key1, keyWork,
                null, city,"json", pageSize,1,"all");
        log.info("listRecommendPlace param:{},{} ,result:{}", keyWork, city, result);
        BaseGdMapModel<List<GdMapPoiModel>> gdMapModel = new BaseGdMapModel<>();
        if(StringUtils.isBlank(result)){
            gdMapModel.setData(Lists.newArrayList());
            return gdMapModel;
        }
        try {
            GdMapPoiResult gdMapPoiResult = new Gson().fromJson(result,GdMapPoiResult.class);
            log.info(" result for poi:{}", JSON.toJSONString(gdMapPoiResult));
            gdMapModel.setData(gdMapPoiResult.getPoiModels());
            return gdMapModel;
        }catch (Exception e){
            log.error("listRecommendPlace Exception {},{}", keyWork, city, e);
        }
        return gdMapModel;
    }

    @Override
    public GdMapRegeoResult getByGps(String longitude, String latitude) {
        String location = String.join(",", longitude, latitude);
        String result = gdMapFeignClient.regeo(key1, location);
        if(StringUtils.isBlank(result)){
            return null;
        }
        try {
            return new Gson().fromJson(result, GdMapRegeoResult.class);
        } catch (Exception e) {
            log.warn("regeo Exception {}",location,e);
            return null;
        }
    }

    /**
     * 获得该定位最近的一个位置
     * @param longitude 经度
     * @param latitude 纬度
     * @return
     */
    @Override
    public GdMapPoiModel getPoiByKeyword(String longitude, String latitude) {
        String location = String.join(",",longitude,latitude);
        String result = gdMapFeignClient.getPoiByKeyword(key1, location ,null,
                "医疗保健服务",null,1000,"distance",1,1);
        log.info("getPoiByKeyword param:{} ,result:{}",location,result);
        if(StringUtils.isBlank(result)){
            return null;
        }
        try {
            GdMapPoiResult gdMapPoiResult = new Gson().fromJson(result,GdMapPoiResult.class);
            log.info(" result for poi:{}", JSON.toJSONString(gdMapPoiResult));
            if (CollectionUtils.isNotEmpty(gdMapPoiResult.getPoiModels())) {
                return gdMapPoiResult.getPoiModels().get(0);
            }
        }catch (Exception e){
            log.error("getPoiByKeyword Exception {}",location,e);
        }
        return null;
    }

    @Override
    public String gpsConvertGdGps(String longitude, String latitude){
        String location = String.join(",",longitude, latitude);
        String result = gdMapFeignClient.convert(key1, location ,"gps");
        log.info("convert param:{} ,result:{}",location,result);
        if(StringUtils.isBlank(result)){
            return null;
        }
        try {
            GdMapConvertResult gdMapConvertResult= new Gson().fromJson(result,GdMapConvertResult.class);
            log.info(" result for poi:{}", JSON.toJSONString(gdMapConvertResult));
            if (StringUtils.isNotBlank(gdMapConvertResult.getLocations())) {
                return gdMapConvertResult.getLocations();
            }
        }catch (Exception e){
            log.error("convert Exception {}",location,e);
        }
        return null;
    }
    @Override
    public String getCityNameByLocation(String location){
        String result = gdMapFeignClient.regeo(key1, location);
        if(StringUtils.isBlank(result)){
            return null;
        }
        try {
            GdMapRegeoResult gdMapRegeoResult= new Gson().fromJson(result, GdMapRegeoResult.class);
            log.info("result for poi:{}", JSON.toJSONString(gdMapRegeoResult));
            if (gdMapRegeoResult.getRegeocode()!=null && gdMapRegeoResult.getRegeocode().getAddressComponent()!=null) {
                return gdMapRegeoResult.getRegeocode().getAddressComponent().getCity().toString().indexOf("[")>=0?
                        gdMapRegeoResult.getRegeocode().getAddressComponent().getProvince() :
                        gdMapRegeoResult.getRegeocode().getAddressComponent().getCity().toString();
            }
        }catch (Exception e){
            log.warn("regeo Exception {}",location,e);
        }
        return null;
    }

    @Override
    public String getLoctaionByCityAndAddress(String address, String city) {
        String result = gdMapFeignClient.geo(key1,address,city);
        if(StringUtils.isBlank(result)){
            return null;
        }
        try{
            GdMapGeoResult geoResult = new Gson().fromJson(result,GdMapGeoResult.class);
            if(geoResult.getGeocodes()==null){
                return null;
            }
            if(CollectionUtils.isEmpty(geoResult.getGeocodes())){
                return null;
            }

            if(geoResult.getGeocodes().size()!=1) {
                return null;
            }

            return geoResult.getGeocodes().get(0).getLocation();

        }catch (Exception e){
            log.error("geo Excepiton:{},{}",address,city,e);
        }
        return null;
    }

    @Override
    public Pair<String,Object> getProvinceAndCity(String address, String city){
        String result = gdMapFeignClient.geo(key1,address,city);
        log.info("gdMapFeignClient.geo param:{},{},result:{}",address,city,result);
        if(StringUtils.isBlank(result)){
            return null;
        }
        try{
            GdMapGeoResult geoResult = new Gson().fromJson(result,GdMapGeoResult.class);
            if(geoResult.getGeocodes()==null){
                return null;
            }
            if(CollectionUtils.isEmpty(geoResult.getGeocodes())){
                return null;
            }

            if(geoResult.getGeocodes().size()!=1) {
                return null;
            }

            return Pair.create(geoResult.getGeocodes().get(0).getProvince(),geoResult.getGeocodes().get(0).getDistrict());
        }catch (Exception e){
            log.error("geo Excepiton:{},{}",address,city,e);
        }
        return null;
    }
}
