package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.strategy;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.GrowthtoolCasePoolModelVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.HomeCaseRemindModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.BdCrmForwardingRecipesService;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/12/11  14:32
 */
@Service
public class NextValidCasePersonPepCasePoolStrategy extends AbstractPersonPepCasePoolStrategy {

    @Autowired
    private BdCrmForwardingRecipesService bdCrmForwardingRecipesService;

    @Override
    public GrowthtoolCasePoolModelVo executeStrategyByPersonPepCasePool(String userId, int factBizType) {
        List<HomeCaseRemindModel> willValidList = bdCrmForwardingRecipesService.willValidList(userId);
        if (CollectionUtils.isEmpty(willValidList)) {
            return GrowthtoolCasePoolModelVo.builder()
                    .grey(true)
                    .build();
        }
        String dt = DateUtil.getCurrentDateStr();
        //即将有效案例排序
        List<HomeCaseRemindModel> homeCaseRemindModelList = willValidAndDonateSpaceBigSort(willValidList, dt);
        setCaseType(HomeCaseRemindModel.CaseTypeEnum.NEXT_VALID_CASE.getCode(), homeCaseRemindModelList);
        //获取案例特征
        getCaseFeature(dt, homeCaseRemindModelList, Lists.newArrayList());
        //获取诊断
        getDiagnosis(homeCaseRemindModelList);
        return GrowthtoolCasePoolModelVo.builder()
                .grey(true)
                .caseScoreList(homeCaseRemindModelList)
                .build();
    }

    @Override
    public boolean support(int caseType) {
        return caseType == HomeCaseRemindModel.CaseTypeEnum.NEXT_VALID_CASE.getCode();
    }
}
