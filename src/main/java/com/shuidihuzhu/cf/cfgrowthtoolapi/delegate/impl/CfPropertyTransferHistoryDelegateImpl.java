package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.CfPropertyTransferHistoryDelegate;
import com.shuidihuzhu.cf.client.apipure.feign.CfPropertyTransferHistoryFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CfPropertyTransferHistoryVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.enums.crowdfunding.CfPropertyTransferEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/9/13  11:56
 */
@Service
public class CfPropertyTransferHistoryDelegateImpl implements CfPropertyTransferHistoryDelegate {

    @Autowired
    private CfPropertyTransferHistoryFeignClient cfPropertyTransferHistoryFeignClient;

    @Override
    public List<CfPropertyTransferHistoryVo> getListByBizTypeAndBizId(CfPropertyTransferEnum bizType, long bizId) {

        OperationResult<List<CfPropertyTransferHistoryVo>> operationResult = cfPropertyTransferHistoryFeignClient.getListByBizTypeAndBizId(bizType.getCode(), bizId);

        return Optional.ofNullable(operationResult)
                .filter(OperationResult::isSuccess)
                .map(OperationResult::getData)
                .orElse(Lists.newArrayList());
    }
}
