package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerCycleDo;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-09-06
 */
public interface ICfPartnerSnapshotDelegate {

    /**
     * 兼职计算周期
     * @param startTime
     * @param endTime
     * @param caseStartTime
     * @param caseEndTime
     * @param name
     * @return
     */
    CfPartnerCycleDo partnerCycle(Date startTime, Date endTime, Date caseStartTime, Date caseEndTime, String name);

    /**
     * 兼职人员快照
     * @param cfPartnerCycleDo
     */
    OpResult<Void> partnerSnapshot(CfPartnerCycleDo cfPartnerCycleDo);

    /**
     * 人员组织快照
     * @param cfPartnerCycleDo
     */
    void partnerOrgMemberSnapshot(CfPartnerCycleDo cfPartnerCycleDo);

    /**
     * 兼职案例明细数据
     * @param cfPartnerCycleDo
     */
    void partnerCaseBill(CfPartnerCycleDo cfPartnerCycleDo);

    /**
     * 兼职考情明细数据
     * @param cfPartnerCycleDo
     */
    void partnerAttendBill(CfPartnerCycleDo cfPartnerCycleDo);
}
