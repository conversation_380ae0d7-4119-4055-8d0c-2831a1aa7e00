package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfObtainChannelDelegate;
import com.shuidihuzhu.client.cf.admin.client.CfObtainChannelFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2019/11/13 10:44 AM
 */
@Service
@Slf4j
@RefreshScope
public class CfObtainChannelDelegate implements ICfObtainChannelDelegate {
    @Autowired
    private CfObtainChannelFeignClient cfObtainChannelFeignClient;
    @Value("${apollo.opencfObtainChannelFeignClient:true}")
    private Boolean opencfObtainChannelFeignClient;

    @Override
    public void getChannel(int caseId, String channel) {
        if (opencfObtainChannelFeignClient){
            Response<Void> response = cfObtainChannelFeignClient.getChannel(caseId, channel);
            log.info(this.getClass().getSimpleName()+" getChannel param  caseId:{}  channel:{}  result:{}",caseId,channel, JSONObject.toJSONString(response));
        }
    }
}
