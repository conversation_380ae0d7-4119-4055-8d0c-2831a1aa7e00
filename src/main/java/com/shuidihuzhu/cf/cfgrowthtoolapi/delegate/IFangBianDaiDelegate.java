package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.DeviceModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.OrderCallbackModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.OrderInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiOrderDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfJiekongDeviceDO;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-05-29 18:21
 */
public interface IFangBianDaiDelegate {

	OpResult<CfFangbiandaiOrderDO> createOrder(CfJiekongDeviceDO cfJiekongDeviceDO, String orderID, int priceInFen);

	OrderInfoModel query(CfFangbiandaiOrderDO orderFromDB);

	OpResult<Boolean> handleCallbackForUpdate(String orderId, String supplierOrderStatus,String remark);

	OpResult<Boolean> queryOrderForUpdate(CfFangbiandaiOrderDO orderFromDB);

	List<CfJiekongDeviceDO> list();

	int getSuccessStatus();
}
