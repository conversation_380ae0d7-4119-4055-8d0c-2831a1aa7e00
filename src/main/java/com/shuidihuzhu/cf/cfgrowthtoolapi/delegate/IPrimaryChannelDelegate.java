package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.api.model.PrimaryChannelRequestModel;

/**
 * @author: wanghui
 * @create: 2019/5/16 4:41 PM
 */

public interface IPrimaryChannelDelegate {
    /**
     * 获取 primaryChannel
     * @param primaryChannelRequestModel
     * @return
     */
    String getPrimaryChannelByActivity(PrimaryChannelRequestModel primaryChannelRequestModel);

    /**
     * 获取 primaryChannel
     * @param crowdfundingInfo
     * @return
     */
    String getCrowdfundingInfoPrimaryChannel(CrowdfundingInfo crowdfundingInfo);
}
