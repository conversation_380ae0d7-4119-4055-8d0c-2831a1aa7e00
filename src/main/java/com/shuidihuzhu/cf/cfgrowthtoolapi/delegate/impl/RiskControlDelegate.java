package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IRiskControlDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ListUtil;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.client.cf.risk.client.CfApiChaifenRiskClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2019/9/18 1:32 PM
 */
@Service
@Slf4j
public class RiskControlDelegate implements IRiskControlDelegate {

    @Autowired
    private CfApiChaifenRiskClient cfApiChaifenRiskClient;

    @Override
    public CfGrowthtoolErrorCode verifyIdCard(String selfRealName, String selfIdCard, long identifyId) {

        Response<String> response = cfApiChaifenRiskClient.verifyIdcard(selfRealName, selfIdCard,
                selfRealName, selfIdCard, NumberUtils.INTEGER_ZERO, UserRelTypeEnum.SELF, identifyId);
        if(!response.ok()){
            log.error("verifyIdCard fegin接口返回失败:{}", JSON.toJSONString(response));
            return CfGrowthtoolErrorCode.ID_CARD_FAIL;
        }
        CfErrorCode verifyIdcardResult = ListUtil.getModelFromResponse(response, CfErrorCode.class);
        log.info("verify fegin接口返回:{}", JSON.toJSONString(verifyIdcardResult));
        // 白名单 直接返回成功
        if (verifyIdcardResult == CfErrorCode.ADD_CROWDFUNDING_VERIFY_SUCCESS_IN_WHITELIST) {
            return CfGrowthtoolErrorCode.SUCCESS;
        }
        // 匹配成功
        if (verifyIdcardResult == CfErrorCode.SUCCESS) {
            return CfGrowthtoolErrorCode.SUCCESS;
        }
        // 匹配失败  提示 身份证不正确
        if (verifyIdcardResult == CfErrorCode.ADD_CROWDFUNDING_IDCARD_ERROR_OTHER) {
            return CfGrowthtoolErrorCode.ID_CARD_NO_VALID;
        }
        // 其他情况 都提示  请重试
        return CfGrowthtoolErrorCode.ID_CARD_FAIL;
    }
}
