package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.risk.model.risk.RiskRaiserMobileCheckParam;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

/**
 * @author: fengxuan
 * @create 2021-11-29 7:29 下午
 **/
public interface RiskRaiserMobileCheckDelegate {


    boolean checkWhenSavePrepose(PreposeMaterialModel.MaterialInfoVo materialInfoVo, CrowdfundingVolunteer cfVolunteer, String clewUser);


    boolean checkWhenConfirm(PreposeMaterialModel.MaterialInfoVo materialInfoVo, long userId);


    boolean riskMobileCheck(RiskRaiserMobileCheckParam raiserMobileCheckParam);
}
