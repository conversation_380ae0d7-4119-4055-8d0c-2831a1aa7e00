package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.CfMaterialPreProcessDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.client.feign.CfMaterialPreProcessClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.vo.initialaudit.InitialAuditInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2020-03-13 10:54
 */
@Service
@Slf4j
public class CfMaterialPreProcessDelegateImpl implements CfMaterialPreProcessDelegate {

    @Autowired
    private CfMaterialPreProcessClient cfMaterialPreProcessClient;

    @Override
    public OpResult<Void> saveInitialPreModify(int caseId, CrowdfundingInfoBaseVo material) {
        // 1 bd提交的  2、用户提交的
        material.setInitialModifyType(1);
        FeignResponse<String> response = cfMaterialPreProcessClient.savePreSubmitInitialMaterial(caseId, material);
        log.info(this.getClass().getName()+" saveInitialPreModify response:{}",response);
        return response.ok()?OpResult.createSucResult(null):OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
    }

    @Override
    public CrowdfundingInfoBaseVo selectInitialMaterial(int caseId) {
        FeignResponse<CrowdfundingInfoBaseVo> response = cfMaterialPreProcessClient.selectBdPreSubmitInitialMaterial(caseId);
        log.info(this.getClass().getName()+" selectInitialMaterial response:{}",response);
        if (response.isFailOrNullData()){
            return null;
        }
        CrowdfundingInfoBaseVo crowdfundingInfoBaseVo = response.getData();
        if (crowdfundingInfoBaseVo.getRelType()== UserRelTypeEnum.SELF){
            crowdfundingInfoBaseVo.setSelfIdCard(crowdfundingInfoBaseVo.getPatientIdCard());
            crowdfundingInfoBaseVo.setSelfRealName(crowdfundingInfoBaseVo.getPatientRealName());
        }
        return crowdfundingInfoBaseVo;
    }

    @Override
    public InitialAuditInfoVO selectInitialStatus(int caseId) {
        FeignResponse<InitialAuditInfoVO> response = cfMaterialPreProcessClient.selectInitialStatus(caseId);
        log.info(this.getClass().getName()+" selectInitialStatus response:{}",response);
        return response.isFailOrNullData()?null:response.getData();
    }
}
