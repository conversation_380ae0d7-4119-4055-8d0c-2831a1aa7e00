package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseVO;
import com.shuidihuzhu.client.cf.growthtool.model.vo.InfoReasonableAmountResultVO;
import com.shuidihuzhu.client.cf.growthtool.model.vo.SpecialDiseaseChoiceInfoVO;
import com.shuidihuzhu.client.cf.growthtool.param.DiseaseParam;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-27
 */
public interface IDiseaseTherapyInfoDelegate {

    /**
     * 疾病归一接口
     * @param param
     * @return
     */
    Response<DiseaseVO> diseaseNorm(DiseaseParam param);

    /**
     * 特殊治疗方式的疾病
     * @param diseaseNameList
     * @return
     */
    Response<SpecialDiseaseChoiceInfoVO> specialRaiseChoiceInfo(List<String> diseaseNameList);

    /**
     * 多种治疗方式的疾病
     * @param decideReasonableInfo
     * @return
     */
    Response<SpecialDiseaseChoiceInfoVO> specialChoiceInfo(DiseaseParam decideReasonableInfo);

    /**
     * 治疗方式+预计花费
     * @param decideReasonableInfo
     * @return
     */
    Response<InfoReasonableAmountResultVO> decideInfoAmountReasonable(DiseaseParam decideReasonableInfo);
}
