package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.message;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-09-11  16:53
 */
@Data
public class SmsParam {

    private long userId;

    private long templateId;

    private String templateParams;

    private int businessType;

    private int subBusinessType;

    private String cryptoMobile;

    private String businessInfo;

    public static Builder buildBasic(SmsTemplateEnum smsTemplateEnum, int subBusinessType, String cryptoMobile) {
        Builder builder = new Builder();
        builder.template(smsTemplateEnum);
        builder.subBusinessType(subBusinessType);
        builder.cryptoMobile(cryptoMobile);
        return builder;
    }

    public static class Builder {

        private SmsParam m = new SmsParam();
        // -----------------------------------------------

        private List<Object> paramList = Lists.newArrayList();

        private Builder() {
        }

        public Builder userId(long userId) {
            m.setUserId(userId);
            return this;
        }

        Builder templateId(long templateId) {
            m.setTemplateId(templateId);
            return this;
        }

        public Builder template(SmsTemplateEnum templateEnum) {
            m.setTemplateId(templateEnum.getValue());
            return this;
        }

        @Deprecated
        public Builder templateParams(String templateParams) {
            m.setTemplateParams(templateParams);
            return this;
        }

        Builder subBusinessType(int subBusinessType) {
            m.setSubBusinessType(subBusinessType);
            return this;
        }

        Builder cryptoMobile(String cryptoMobile) {
            m.setCryptoMobile(cryptoMobile);
            return this;
        }

        public Builder businessInfo(String businessInfo) {
            m.setBusinessInfo(businessInfo);
            return this;
        }

        public Builder param(Object value) {
            if (value == null) {
                value = "";
            }
            paramList.add(value);
            return this;
        }

        public Builder param(Object...values) {
            for (Object o: values) {
                param(o);
            }
            return this;
        }

        public SmsParam build(){
            if(CollectionUtils.isNotEmpty(paramList)) {
                m.setTemplateParams(StringUtils.join(paramList, ","));
            }
            if (StringUtils.isBlank(m.getBusinessInfo())) {
                m.setBusinessInfo("B-" + m.getTemplateId());
            }
            return m;
        }

    }

}
