package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.vo.initialaudit.InitialAuditInfoVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: wanghui
 * @create: 2020-03-13 10:51
 */
public interface CfMaterialPreProcessDelegate {
    /**
     * 保存顾问 修改的信息
     * @param caseId
     * @param material
     * @return
     */
    OpResult saveInitialPreModify(@RequestParam("caseId") int caseId, @RequestBody CrowdfundingInfoBaseVo material);

    /**
     * 根据案例id 查询初审驳回后的案例基本信息
     * @param caseId
     * @return
     */
    CrowdfundingInfoBaseVo selectInitialMaterial(@RequestParam("caseId") int caseId);

    /**
     * 根据案例id 查询初审驳回项
     * @param caseId
     * @return
     */
    InitialAuditInfoVO selectInitialStatus(@RequestParam("caseId") int caseId);
}
