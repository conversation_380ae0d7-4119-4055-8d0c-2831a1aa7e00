package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.SeaAdminUserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.*;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.auth.saas.model.dto.GroupMembersResultDto;

import java.util.List;

public interface ISeaAccountServiceDelegate {


    /**
     * 根据sea misname 获取用户信息
     * @param misId
     * @return
     */

    OpResult<SeaAdminUserInfoModel> getUserAccountsByMis(String misId);

    /**
     * 获取用户组织信息
     * @param misId
     * @return
     */
    AdminOrganization getUserOrganization(String misId);


    AdminOrganization getAdminOrganizationByUserId(long userId);

    List<AdminUserAccountModel> getUserAccountsByIds(List<Long> userIds);

    AdminUserAccountModel getValidUserAccountById(Long userId);

    AdminUserAccountModel getUserByMobile(String mobile);

    SeaAdminUserInfoModel getOrgInfoByMisUseCache(String mis);
}
