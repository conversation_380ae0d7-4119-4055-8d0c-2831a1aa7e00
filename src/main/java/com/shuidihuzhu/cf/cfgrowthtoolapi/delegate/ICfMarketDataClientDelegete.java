package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.risk.model.gatherInformation.CfMarketDataVO;
import com.shuidihuzhu.cf.risk.model.gatherInformation.market.CfMarketDataQueryParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020-07-05
 */
public interface ICfMarketDataClientDelegete {

    /**
     * 按天查询明细数据
     * @param cfMarketDataQueryParam
     * @return
     */
    OpResult<List<CfMarketDataVO>> queryDetailMarketData(CfMarketDataQueryParam cfMarketDataQueryParam,List<Integer> cityIdList);

    /**
     * 查询汇总数据
     * @param cfMarketDataQueryParam
     * @return
     */
    OpResult<Map<Long,CfMarketDataVO>> queryCollectMarketData(CfMarketDataQueryParam cfMarketDataQueryParam,Map<Long, Set<Integer>> subOrgActCityMap);
}
