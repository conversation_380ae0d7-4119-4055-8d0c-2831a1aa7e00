package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.client.cf.clewtrack.client.CfDorisDataFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.RptCfCaseShareDonateSumRpcModel;
import com.shuidihuzhu.client.cf.clewtrack.model.RptCfCaseUserShareDonateSumFullHRpcModel;
import com.shuidihuzhu.client.cf.clewtrack.model.RptCfUserShareDonateSumFull15mRpcModel;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class CfDorisDataFeignClientDelegate {

    @Autowired
    private CfDorisDataFeignClient dorisDataFeignClient;

    /**
     * 获取top捐单量好友转发数据
     * @param caseId 案例ID
     * @param limit 限制返回记录数
     * @return 好友转发数据列表
     */
    public List<RptCfCaseUserShareDonateSumFullHRpcModel> queryCaseUserShareData(Long caseId, Integer limit) {
        try {
            Response<List<RptCfCaseUserShareDonateSumFullHRpcModel>> response = dorisDataFeignClient.queryCaseUserShareData(caseId, limit);
            if (response != null && response.ok()) {
                return response.getData();
            }
            log.warn("获取top捐单量好友转发数据失败, caseId: {}, limit: {}, response: {}", caseId, limit, response);
        } catch (Exception e) {
            log.error("获取top捐单量好友转发数据异常, caseId: {}, limit: {}, error: {}", caseId, limit, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取本人转发数据
     * @param caseId 案例ID
     * @return 本人转发数据
     */
    public RptCfCaseUserShareDonateSumFullHRpcModel getSelfShareData(Long caseId) {
        try {
            Response<RptCfCaseUserShareDonateSumFullHRpcModel> response = dorisDataFeignClient.getSelfShareData(caseId);
            if (response != null && response.ok()) {
                return response.getData();
            }
            log.warn("获取本人转发数据失败, caseId: {}, response: {}", caseId, response);
        } catch (Exception e) {
            log.error("获取本人转发数据异常, caseId: {}, error: {}", caseId, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取证实人转发数据
     * @param caseId 案例ID
     * @return 证实人转发数据列表
     */
    public List<RptCfUserShareDonateSumFull15mRpcModel> getApproveUserShareData(Long caseId) {
        try {
            Response<List<RptCfUserShareDonateSumFull15mRpcModel>> response = dorisDataFeignClient.getApproveUserShareData(caseId);
            if (response != null && response.ok()) {
                return response.getData();
            }
            log.warn("获取证实人转发数据失败, caseId: {}, response: {}", caseId, response);
        } catch (Exception e) {
            log.error("获取证实人转发数据异常, caseId: {}, error: {}", caseId, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取案例转发汇总数据
     * @param caseId 案例ID
     * @return 案例转发汇总数据
     */
    public RptCfCaseShareDonateSumRpcModel getCaseShareData(Long caseId) {
        try {
            Response<RptCfCaseShareDonateSumRpcModel> response = dorisDataFeignClient.getCaseShareData(caseId);
            if (response != null && response.ok()) {
                return response.getData();
            }
            log.warn("获取案例转发汇总数据失败, caseId: {}, response: {}", caseId, response);
        } catch (Exception e) {
            log.error("获取案例转发汇总数据异常, caseId: {}, error: {}", caseId, e.getMessage(), e);
        }
        return null;
    }
}
