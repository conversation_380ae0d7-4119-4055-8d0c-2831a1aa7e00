package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IPrimaryChannelDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.api.chaifenbeta.growthtool.PrimaryChannelFeignClient;
import com.shuidihuzhu.client.cf.api.model.PrimaryChannelRequestModel;
import com.shuidihuzhu.client.cf.api.model.enums.PrimaryChannelObjectTypeEnum;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2019/5/16 4:44 PM
 */
@Service
@Slf4j
@RefreshScope
public class PrimaryChannelDelegate implements IPrimaryChannelDelegate {
    @Autowired
    private PrimaryChannelFeignClient primaryChannelFeignClient;

    @Override
    public String getPrimaryChannelByActivity(PrimaryChannelRequestModel primaryChannelRequestModel) {
        try{
            Response<String> primaryChannelByActivity = primaryChannelFeignClient.getPrimaryChannelByActivity(primaryChannelRequestModel);
            if (primaryChannelByActivity.ok()){
                return primaryChannelByActivity.getData();
            }
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" getPrimaryChannelByActivity  err:",e);
        }
        return "other";
    }

    @Override
    public String getCrowdfundingInfoPrimaryChannel(CrowdfundingInfo crowdfundingInfo) {
        PrimaryChannelRequestModel primaryChannelRequestModel = new PrimaryChannelRequestModel(0,
                JSON.toJSONString(crowdfundingInfo),
                PrimaryChannelObjectTypeEnum.CrowdfundingInfo);
        try{
            Response<String> primaryChannelByActivity = primaryChannelFeignClient.getCrowdfundingInfoPrimaryChannel(primaryChannelRequestModel);
            if (primaryChannelByActivity.ok()){
                return primaryChannelByActivity.getData();
            }
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" getCrowdfundingInfoPrimaryChannel  err:",e);
        }
        return "other";
    }
}
