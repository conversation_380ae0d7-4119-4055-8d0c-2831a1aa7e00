package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMasterForGrowthtoolDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrmHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrowdfundingVolunteerCreateCaseRecordExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.VolunteerAutoChangeModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffFriendDO;
import com.shuidihuzhu.cf.domain.dedicated.*;
import com.shuidihuzhu.cf.lion.client.risk.feign.CfMasterForGrowthtoolFeginClient;
import com.shuidihuzhu.cf.lion.client.risk.model.*;
import com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister;
import com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign;
import com.shuidihuzhu.cf.model.toufang.SDDataToufangSign;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class CfMasterForGrowthtoolDelegateImpl implements ICfMasterForGrowthtoolDelegate {

    @Autowired
    private CfMasterForGrowthtoolFeginClient cfMasterForGrowthtoolFeginClient;

    @Override
    public int insertCrmHospitalDO(CrmHospitalDO crmHospitalDO) {
        CrmHospitalFeignDO crmHospitalFeignDO = new CrmHospitalFeignDO();
        BeanUtils.copyProperties(crmHospitalDO,crmHospitalFeignDO);
        Response<CrmHospitalFeignDO> response = cfMasterForGrowthtoolFeginClient.insertCrmHospitalDO(crmHospitalFeignDO);
        log.info("insertCrmHospitalDO request:{},response:{}", JSON.toJSONString(crmHospitalDO),JSON.toJSONString(response));
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(crmHospitalDO.getId()==null){
            crmHospitalDO.setId(response.getData().getId());
        }
        return 1;
    }

    @Override
    public int updateCrmHospitalDO(CrmHospitalDO crmHospitalDO) {
        CrmHospitalFeignDO crmHospitalFeignDO = new CrmHospitalFeignDO();
        BeanUtils.copyProperties(crmHospitalDO,crmHospitalFeignDO);
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateCrmHospitalDO(crmHospitalFeignDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int deleteHospital(int hospitalId) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.deleteHospital(hospitalId);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int updateHospitalVHospitalCode(Long id, String vhospitalCode) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateHospitalVHospitalCode(id,vhospitalCode);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int updateStatusAndRstatusByHospitalId(Long id, Integer useStatus, Integer rStatus) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateStatusAndRstatusByHospitalId(id,useStatus,rStatus);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int updateHospitalVhospitalRalation(Long id, String misName, String timestamp) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateHospitalVhospitalRalation(id,misName,timestamp);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int insertByHospitalItem(String hospitalName, String cityName, String provinceName, String vhospitalCode, Integer checkStatus) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.insertByHospitalItem(hospitalName, cityName, provinceName, vhospitalCode, checkStatus);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int insertCfAdRegister(CfAdRegister cfAdRegister) {
        CfAdRegisterFeginModel cfAdRegisterFeginModel = new CfAdRegisterFeginModel();
        BeanUtils.copyProperties(cfAdRegister,cfAdRegisterFeginModel);
        Response<CfAdRegisterFeginModel>  response = cfMasterForGrowthtoolFeginClient.insertCfAdRegister(cfAdRegisterFeginModel);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(cfAdRegister.getId()==0){
            cfAdRegister.setId(response.getData().getId());
        }
        return 1;
    }

    @Override
    public int insertSDDataToufangSign(SDDataToufangSign sdDataToufangSign) {
        SDDataToufangSignFeginModel sdDataToufangSignFeginModel = new SDDataToufangSignFeginModel();
        BeanUtils.copyProperties(sdDataToufangSign,sdDataToufangSignFeginModel);
        Response<SDDataToufangSignFeginModel> response = cfMasterForGrowthtoolFeginClient.insertSDDataToufangSign(sdDataToufangSignFeginModel);
        if(response.notOk() || response.getData() == null){
            return 0;
        }
        return 1;
    }

    @Override
    public int addInviteRelation(CfToufangInviteCaseRelationDO caseRelationDO) {
        CfToufangInviteCaseRelationFeginDO toufangInviteCaseRelationFeginDO = new CfToufangInviteCaseRelationFeginDO();
        BeanUtils.copyProperties(caseRelationDO,toufangInviteCaseRelationFeginDO);
        Response<CfToufangInviteCaseRelationFeginDO> response = cfMasterForGrowthtoolFeginClient.addInviteRelation(toufangInviteCaseRelationFeginDO);
        if(response.notOk() || response.getData() == null){
            return 0;
        }
        caseRelationDO.setId(response.getData().getId());
        return 1;
    }

    @Override
    public int updateCaseHandleStatus(Integer id, Integer completeFlag, Integer taskFlag, Date expireTime) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateCaseHandleStatus(id,completeFlag,taskFlag,expireTime==null?0:expireTime.getTime());
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int addCfToufangInvitorVisitDO(CfToufangInvitorVisitDO cfToufangInvitorVisitDO) {
        CfToufangInvitorVisitFeignDO cfToufangInvitorVisitFeignDO = new CfToufangInvitorVisitFeignDO();
        BeanUtils.copyProperties(cfToufangInvitorVisitDO,cfToufangInvitorVisitFeignDO);
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.addCfToufangInvitorVisitDO(cfToufangInvitorVisitFeignDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int insertCfTouFangSign(CfTouFangSign cfTouFangSign) {
        CfTouFangSignFeginDO cfTouFangSignFeginDO = new CfTouFangSignFeginDO();
        BeanUtils.copyProperties(cfTouFangSign,cfTouFangSignFeginDO);
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.insertCfTouFangSign(cfTouFangSignFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int insertCfTouFangSignList(List<CfTouFangSign> cfTouFangSigns) {
        List<CfTouFangSignFeginDO> paramList = Lists.newArrayList();
        for(CfTouFangSign cfTouFangSign : cfTouFangSigns){
            CfTouFangSignFeginDO cfTouFangSignFeginDO = new CfTouFangSignFeginDO();
            BeanUtils.copyProperties(cfTouFangSign,cfTouFangSignFeginDO);
            paramList.add(cfTouFangSignFeginDO);
        }
        if(CollectionUtils.isEmpty(paramList)){
            return 0;
        }
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.insertCfTouFangSignList(paramList);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int addUserVolunteerRelationDO(CfUserVolunteerRelationDO volunteerRelationDO) {
        CfUserVolunteerRelationFeginDO volunteerRelationFeginDO = new CfUserVolunteerRelationFeginDO();
        BeanUtils.copyProperties(volunteerRelationDO,volunteerRelationFeginDO);
        Response<CfUserVolunteerRelationFeginDO> response = cfMasterForGrowthtoolFeginClient.addUserVolunteerRelationDO(volunteerRelationFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(volunteerRelationDO.getId()==null || volunteerRelationDO.getId()==0){
            volunteerRelationDO.setId(response.getData().getId());
        }
        return 1;
    }

    @Override
    public int addCfVolunteerMaterial(CfVolunteerMaterialDO materialDO) {
        CfVolunteerMaterialFeginDO materialFeginDO = new CfVolunteerMaterialFeginDO();
        BeanUtils.copyProperties(materialDO,materialFeginDO);
        Response<CfVolunteerMaterialFeginDO> response = cfMasterForGrowthtoolFeginClient.addCfVolunteerMaterial(materialFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(materialDO.getId()==null || materialDO.getId()==0){
            materialDO.setId(response.getData().getId());
        }
        return 1;
    }

    @Override
    public int updateCfVolunteerMaterial(CfVolunteerMaterialDO materialDO) {
        CfVolunteerMaterialFeginDO materialFeginDO = new CfVolunteerMaterialFeginDO();
        BeanUtils.copyProperties(materialDO,materialFeginDO);
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateCfVolunteerMaterial(materialFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int updateVerifyPictures(String uniqueCode, String personPic, String s, String s1) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateVerifyPictures(uniqueCode, personPic, s, s1);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int addVolunteer(CrowdfundingVolunteer crowdfundingVolunteer) {
        CrowdfundingVolunteerFeignModel crowdfundingVolunteerFeignModel = new CrowdfundingVolunteerFeignModel();
        BeanUtils.copyProperties(crowdfundingVolunteer,crowdfundingVolunteerFeignModel);
        Response<CrowdfundingVolunteerFeignModel> response = cfMasterForGrowthtoolFeginClient.addVolunteer(crowdfundingVolunteerFeignModel);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(crowdfundingVolunteer.getId()==0l){
            crowdfundingVolunteer.setId(response.getData().getId());
        }
        return 1;
    }

    @Override
    public int addNewVolunteer(CrowdfundingVolunteer crowdfundingVolunteer) {
        CrowdfundingVolunteerFeignModel crowdfundingVolunteerFeignModel = new CrowdfundingVolunteerFeignModel();
        BeanUtils.copyProperties(crowdfundingVolunteer,crowdfundingVolunteerFeignModel);
        Response<CrowdfundingVolunteerFeignModel> response = cfMasterForGrowthtoolFeginClient.addNewVolunteer(crowdfundingVolunteerFeignModel);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(crowdfundingVolunteer.getId()==0l){
            crowdfundingVolunteer.setId(response.getData().getId());
        }
        return 1;
    }

    @Override
    public int updateBaseInfoApplyStatus(String uniqueCode, Integer i, Integer verifyStatus, Integer applyStatus) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateBaseInfoApplyStatus(uniqueCode, i, verifyStatus, applyStatus);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int updateVolunteerInfo(String uniqueCode, String aesEncrypt, String name, int age) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateVolunteerInfo(uniqueCode, aesEncrypt, name, age);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public void updateVolunteerInfoById(CrowdfundingVolunteer crowdfundingVolunteer) {
        CrowdfundingVolunteerFeignModel crowdfundingVolunteerFeignModel = new CrowdfundingVolunteerFeignModel();
        BeanUtils.copyProperties(crowdfundingVolunteer, crowdfundingVolunteerFeignModel);
        if (crowdfundingVolunteerFeignModel.getId() == 0) {
            return;
        }
        cfMasterForGrowthtoolFeginClient.updateVolunteerInfoById(crowdfundingVolunteerFeignModel);
    }

    @Override
    public void updateQrCode(String qrCode, Long id) {
        cfMasterForGrowthtoolFeginClient.updateQrCode(qrCode, id);
    }

    @Override
    public void updateApplyStatusById(long id, int applyStatus, String operatorName, long operatorUserId, String angelUrl, String refuseReasons, String qrCode) {
        cfMasterForGrowthtoolFeginClient.updateApplyStatusById(id, applyStatus, operatorName, (int) operatorUserId, angelUrl, refuseReasons, qrCode);
    }

    @Override
    public int updateWorkStatus(long id, int workStatus, String operator, Date leaveTime) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateWorkStatus(id,workStatus,operator,leaveTime==null?0:leaveTime.getTime());
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int updateEntryTime(long id, Date entryTime, String system) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateEntryTime(id,entryTime==null?0l:entryTime.getTime(),system);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int updateMisBlankById(Long id) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateMisBlankById(id);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public void updateVEncryptMobiles(long id, String vEncryptMobiles) {
        cfMasterForGrowthtoolFeginClient.updateVEncryptMobiles(id, vEncryptMobiles);
    }

    @Override
    public int insertCrowdfundingVolunteerInviteUserRecordDO(CrowdfundingVolunteerInviteUserRecordDO crowdfundingVolunteerInviteUserRecordDO) {
        CrowdfundingVolunteerInviteUserRecordFeginDO crowdfundingVolunteerInviteUserRecordFeginDO = new CrowdfundingVolunteerInviteUserRecordFeginDO();
        BeanUtils.copyProperties(crowdfundingVolunteerInviteUserRecordDO,crowdfundingVolunteerInviteUserRecordFeginDO);
        Response<CrowdfundingVolunteerInviteUserRecordFeginDO> response = cfMasterForGrowthtoolFeginClient.insertCrowdfundingVolunteerInviteUserRecordDO(crowdfundingVolunteerInviteUserRecordFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(crowdfundingVolunteerInviteUserRecordDO.getId()==null || crowdfundingVolunteerInviteUserRecordDO.getId()==0){
            crowdfundingVolunteerInviteUserRecordDO.setId(crowdfundingVolunteerInviteUserRecordFeginDO.getId());
        }
        return 1;
    }

    @Override
    public int updatePrimaryChannel(long id, String primaryChannel) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updatePrimaryChannel(id,primaryChannel);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int insertCfServiceStaffDO(CfServiceStaffDO cfServiceStaffDO) {
        CfServiceStaffFeginDO cfServiceStaffFeginDO = new CfServiceStaffFeginDO();
        BeanUtils.copyProperties(cfServiceStaffDO,cfServiceStaffFeginDO);
        Response<CfServiceStaffFeginDO> response = cfMasterForGrowthtoolFeginClient.insertCfServiceStaffDO(cfServiceStaffFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(cfServiceStaffDO.getId()==null || cfServiceStaffDO.getId()==0){
            cfServiceStaffDO.setId(response.getData().getId());
        }
        return 1;
    }

    @Override
    public int updateServiceStaffUserInfo(String qywechatid, Integer serviceType, String qrCode, String headUrl, Integer id, Integer helpPatients, Integer raiseAmount, String labels, String favorableRate, String qyWechatQrCode) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateServiceStaffUserInfo(qywechatid, serviceType, qrCode, headUrl, id, helpPatients, raiseAmount, labels, favorableRate, qyWechatQrCode);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int updateShowTimeByQyWechatUserId(String qyWechatUserId, String showTime) {
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateShowTimeByQyWechatUserId(qyWechatUserId, showTime);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int updateCfServiceStaffById(CfServiceStaffDO cfServiceStaffDO) {
        CfServiceStaffFeginDO cfServiceStaffFeginDO = new CfServiceStaffFeginDO();
        BeanUtils.copyProperties(cfServiceStaffDO,cfServiceStaffFeginDO);
        if(cfServiceStaffFeginDO.getId()==null || cfServiceStaffFeginDO.getId()==0){
            return 0;
        }
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateCfServiceStaffById(cfServiceStaffFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int insertCfServiceStaffFriendDO(CfServiceStaffFriendDO cfServiceStaffFriendDO) {
        CfServiceStaffFriendFeginDO cfServiceStaffFriendFeginDO = new CfServiceStaffFriendFeginDO();
        BeanUtils.copyProperties(cfServiceStaffFriendDO,cfServiceStaffFriendFeginDO);
        Response<CfServiceStaffFriendFeginDO> response = cfMasterForGrowthtoolFeginClient.insertCfServiceStaffFriendDO(cfServiceStaffFriendFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(cfServiceStaffFriendDO.getId()==null || cfServiceStaffFriendDO.getId()==0){
            cfServiceStaffFriendDO.setId(response.getData().getId());
        }
        return 1;
    }

    @Override
    public int updateQyWechatUserIdByUserId(CfServiceStaffFriendDO cfServiceStaffFriendDO) {
        CfServiceStaffFriendFeginDO cfServiceStaffFriendFeginDO = new CfServiceStaffFriendFeginDO();
        BeanUtils.copyProperties(cfServiceStaffFriendDO,cfServiceStaffFriendFeginDO);
        if(cfServiceStaffFriendFeginDO.getUserId()==null){
            return 0;
        }
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateQyWechatUserIdByUserId(cfServiceStaffFriendFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int updateEntryLeaveTime(VolunteerAutoChangeModel changeModel) {
        VolunteerAutoChangeFeginModel changeFeginModel = new VolunteerAutoChangeFeginModel();
        BeanUtils.copyProperties(changeModel,changeFeginModel);
        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateEntryLeaveTime(changeFeginModel);
        if(response.notOk() || response.getData() == null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public int insertVolunteerCreateCaseRecord(CrowdfundingVolunteerCreateCaseRecordExtDO crowdfundingVolunteerCreateCaseRecordExtDO) {
        CrowdfundingVolunteerCreateCaseRecordExtFeginDO crowdfundingVolunteerCreateCaseRecordExtFeginDO = new CrowdfundingVolunteerCreateCaseRecordExtFeginDO();
        BeanUtils.copyProperties(crowdfundingVolunteerCreateCaseRecordExtDO,crowdfundingVolunteerCreateCaseRecordExtFeginDO);
        Response<CrowdfundingVolunteerCreateCaseRecordExtFeginDO> response = cfMasterForGrowthtoolFeginClient.insertVolunteerCreateCaseRecord(crowdfundingVolunteerCreateCaseRecordExtFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(crowdfundingVolunteerCreateCaseRecordExtDO.getId()==0){
            crowdfundingVolunteerCreateCaseRecordExtDO.setId(response.getData().getId());
        }
        return 1;
    }

    @Override
    public int insertVolunteerCreateCaseRecordWithPhone(CrowdfundingVolunteerCreateCaseRecordExtDO crowdfundingVolunteerCreateCaseRecordExtDO) {
        CrowdfundingVolunteerCreateCaseRecordExtFeginDO crowdfundingVolunteerCreateCaseRecordExtFeginDO = new CrowdfundingVolunteerCreateCaseRecordExtFeginDO();
        BeanUtils.copyProperties(crowdfundingVolunteerCreateCaseRecordExtDO,crowdfundingVolunteerCreateCaseRecordExtFeginDO);
        Response<CrowdfundingVolunteerCreateCaseRecordExtFeginDO> response = cfMasterForGrowthtoolFeginClient.insertVolunteerCreateCaseRecordWithPhone(crowdfundingVolunteerCreateCaseRecordExtFeginDO);
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        if(crowdfundingVolunteerCreateCaseRecordExtDO.getId()==0){
            crowdfundingVolunteerCreateCaseRecordExtDO.setId(response.getData().getId());
        }
        return 1;
    }

    @Override
    public int updateNoClewCreateDesc(long id, String noClewCreateDesc) {

        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateNoClewCreateDesc(id,noClewCreateDesc);
        log.info("updateNoClewCreateDesc request:{},response:{}", id+"_"+noClewCreateDesc,JSON.toJSONString(response));
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }


    @Override
    public int updateClewId(long id, long clewId) {

        Response<Integer> response = cfMasterForGrowthtoolFeginClient.updateClewId(id,clewId);
        log.info("insertCrmHospitalDO request:{},response:{}", id+"_"+clewId,JSON.toJSONString(response));
        if(response.notOk() || response.getData()==null){
            return 0;
        }
        return response.getData();
    }

    @Override
    public void updateGrTag(List<String> uniqueCodeList, int grTag) {
        if (CollectionUtils.isEmpty(uniqueCodeList)) {
            log.info("uniqueCodeList为空");
            return;
        }
        Response<Void> response = cfMasterForGrowthtoolFeginClient.updateGrTag(uniqueCodeList, grTag);
        log.info("updateQrTag param uniqueCodeList:{},grTag:{},result:{}", uniqueCodeList, grTag, response);
    }

    @Override
    public void updateCreateCaseRecord(CrowdfundingVolunteerCreateCaseRecordExtFeginDO createCaseRecordExtFeginDO) {
        Response<Void> response = cfMasterForGrowthtoolFeginClient.updateCreateCaseRecord(createCaseRecordExtFeginDO);
        log.info("updateCreateCaseRecord:{},result:{}", createCaseRecordExtFeginDO, response);
    }
}
