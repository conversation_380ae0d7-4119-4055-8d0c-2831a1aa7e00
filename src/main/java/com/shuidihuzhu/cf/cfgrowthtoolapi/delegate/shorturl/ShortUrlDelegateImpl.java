package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.shorturl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.account.ShuidiAppCodeUtil;
import com.shuidihuzhu.client.baseservice.zqurl.v1.TokenLinkClient;
import com.shuidihuzhu.client.baseservice.zqurl.v1.model.Response;
import com.shuidihuzhu.client.baseservice.zqurl.v1.model.UrlMatch;
import com.shuidihuzhu.client.baseservice.zqurl.v1.param.ZqurlParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * 短链服务封装
 */
@Slf4j
@Service
public class ShortUrlDelegateImpl implements ShortUrlDelegate {

//    @Autowired
//    private ZqUrlClient zqUrlClient;
    @Autowired
    TokenLinkClient tokenLinkClient;

    private static String shuidiappcode = "lfppupkp";

    @Override
    public String process(String url) {

        for(int i=0;i<3;i++) {
            ShuidiAppCodeUtil.setShuidiAppCode(shuidiappcode);
            ZqurlParam param = new ZqurlParam();
            param.setUrl(url);
            param.setDomainKey("cf");
            Response<UrlMatch> urlMatchResponse = tokenLinkClient.createOne(param);
            log.info("ShortUrlDelegate param:{},response:{}",url, JSON.toJSONString(urlMatchResponse));
            if (urlMatchResponse.ok() && urlMatchResponse.getData()!=null) {
                return urlMatchResponse.getData().getZqUrl();
            }
        }
        return url;
    }
}
