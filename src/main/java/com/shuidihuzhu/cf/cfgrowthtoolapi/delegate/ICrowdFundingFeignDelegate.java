package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CaseLifeCircleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.CfPatientRecruitClewVo;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.crowdfunding.vo.BdCrowfundingInfoVO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.admin.model.CfCaseApproveDetail;

import java.util.List;
import java.util.Map;

public interface ICrowdFundingFeignDelegate {
    /**
     *
     * @param infoIdList
     * @return
     */
    Map<Integer, CrowdfundingTreatment> getByInfoIdList(List<Integer> infoIdList);

    /**
     *
     * @param infoIdList
     * @param type
     * @return
     */
    Map<Integer, List<CrowdfundingAttachmentVo>> getByInfoIdList(List<Integer> infoIdList,
                                                                 AttachmentTypeEnum type);

    /**
     *
     * @param infoIdList
     * @return
     */
    Map<Integer, CrowdfundingAuthor> getCrowdfundingAuthorByInfoIdList( List<Integer> infoIdList);

    /**
     *
     * @param infoIds
     * @return
     */
    Map<Integer, CfFirsApproveMaterial> getMapByInfoIds(List<Integer> infoIds);

    /**
     *
     * @param startTime
     * @param endTime
     * @param uniqueCode
     * @param offset
     * @param pageSize
     * @return
     */
    List<CfInfoExt> getBdServiceInfoByTime(String startTime, String endTime,
                                                          String uniqueCode,
                                                          Integer offset,
                                                          Integer pageSize);

    /**
     * 返回 推荐列表
     * @return
     */
    List<CrowdfundingInfoView> getRecommendList(int count, int type);

    /**
     *
     * @param caseIds
     * @return
     */
    List<CrowdfundingInfo> getCrowdfundingListById(List<Integer> caseIds);

    String getRealName(int id, long userId);

    CfFirsApproveMaterial getAuthorInfoByInfoId(int infoId);

    CfInfoExt getCfInfoExtByuuid(String caseUuid);

    CfInfoExt getCfInfoExtById(int caseId);

    List<CfInfoSimpleModel> getSimpleCrowdfundingList(List<String> infoIdList);

    UserInfoModel getUserInfoByUserId(long userId);

    CrowdfundingInfo getCaseInfoById(long infoId);

    List<BdCrowfundingInfoVO> getCrowdfundingByuuids(List<String> caseUuidList);

    CrowdfundingInfo getCrowdfundingInfoByInfouuid(String infoUuid);

	boolean checkCaseEnd(int infoId);

	OpResult<CrowdfundingInfo> getUnEndCaseInfoByRaiseMobile(String mobile);

    OpResult<CrowdfundingInfo> getUnEndCaseByUserId(Long userId);

	List<CrowdfundingInfo> getCrowdfundingInfoListByUserId(Long userId);

	List<CaseLifeCircleModel> getCaseLifeCircle(int caseId);

    OpResult<CrowdfundingInfo> getUnEndCaseByCaseIds(List<Integer> caseIds);

    InitialAuditItem.RejectReasonSet parseRejectDetail(int caseId);

    CrowdfundingApprove getLastWithCommentByCrowdfundingId(Integer crowdfundingId);

    Map<Integer, CfCaseApproveDetail> getCaseApproveDetail(List<Integer> caseIds);

    Map<String, CrowdfundingInfo> getMapByInfoUuIds(List<String> infoUuids);

    Map<String, CfCaseCountStat> getUserStatMapByInfoUuIds(List<String> infoUuids);

    Map<String, CfCaseCommonInfo> getCaseCommonInfoByCaseIds(List<Integer> caseIdList);

    Integer getdonationCountByInfoUuId(Integer caseId);

	String generateQrcodeByThirdType(String scene, int thirdType);

    String getRaiseMobileByUserId(long userId);

    //从用户中心获取手机号码
    String getCryptoMobileByUserId(long userId);

    void saveRaiseMobileByUserId(long userId, String mobile);

    /**
     * 批量获取案例,只允许调用量不大的接口调用
     * @param startTime
     * @param endTime
     * @return
     */
    List<CrowdfundingInfo> listByTimeRange(long startTime, long endTime);

    CrowdfundingInfoHospitalPayee getCrowdfundingInfoHospitalPayeeByInfoUuid(String infoUuid);

    CrowdfundingInfoPayee getCrowdfundingInfoPayeeByInfoUuid(String infoUuid);

    OpResult<Integer> getCountByUseridAndInfoId(long userId, int infoId);

    OpResult<Integer> countTodayShare(int infoId, Long userId);

    OpResult<Integer> countTodayShareFriendFeed(int infoId, Long userId);

    CfPatientRecruitClewVo getCfPatientRecruitClewVoByCaseId(int caseId);

    //获取患者年龄
    int getPatientAge(int caseId);

    Map<Integer, Integer> getPatientAges(List<Integer> caseIds);
}
