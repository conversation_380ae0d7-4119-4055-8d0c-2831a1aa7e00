package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IPatientRecruitClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pf.model.CityVo;
import com.shuidihuzhu.pr.client.*;
import com.shuidihuzhu.pr.common.client.PatientDepartmentInfoClient;
import com.shuidihuzhu.pr.common.client.PatientEnumCommonClient;
import com.shuidihuzhu.pr.common.model.dto.pinyin.PinyinAnalysis;
import com.shuidihuzhu.pr.patient.model.enums.clew.PatientClewInfoVersion;
import com.shuidihuzhu.pr.common.model.enums.user.UserRolePermission;
import com.shuidihuzhu.pr.patient.model.dto.patient.Patient;
import com.shuidihuzhu.pr.common.model.vo.PrPatientCommonMapVo;
import com.shuidihuzhu.pr.model.param.PrPermissionCheck;
import com.shuidihuzhu.pr.patient.client.clew.PrClewOutsideClient;
import com.shuidihuzhu.pr.patient.client.patient.PrPatientClient;
import com.shuidihuzhu.pr.patient.model.dto.clew.outside.*;
import com.shuidihuzhu.pr.user.client.PrUserClient;
import com.shuidihuzhu.pr.user.model.user.PrUserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-05-11
 */

@Service
@Slf4j
public class PatientRecruitClientDelegate implements IPatientRecruitClientDelegate {

    
    @Autowired
    private UserRoleClient userRoleClient;

    @Autowired
    private PrPatientClient patientInfoClient;

    @Autowired
    private PatientEnumCommonClient patientEnumCommonClient;

    @Autowired
    private PrUserClient prUserClient;

    @Autowired
    private PatientDepartmentInfoClient patientDepartmentInfoClient;

    @Autowired
    private PrClewOutsideClient prClewOutsideClient;

    @Override
    public List<PrPermissionCheck> hasPermissionByMisList(List<String> misList, int permission) {
        try {
            RpcResult<List<PrPermissionCheck>> rpcResult = userRoleClient.hasPermissionByMisList(misList, permission);
            if (rpcResult.isSuccess()) {
                return rpcResult.getData();
            }
        }catch (Exception e){
            log.error("{} hasPermissionByMisList request:{} error",this.getClass().getSimpleName(), JSON.toJSONString(misList),e);
        }
        return Lists.newArrayList();
    }

    @Override
    public PrPermissionCheck hasPermissionByMis(String mis) {
        try{
            RpcResult<PrPermissionCheck> rpcResult = userRoleClient.hasPermissionByMis(mis, UserRolePermission.PART_TIME_COUNSELOR.getCode());
            if (rpcResult.isSuccess()){
                return rpcResult.getData();
            }
        }catch (Exception e){
            log.error("{} hasPermissionByMis request:{} error",this.getClass().getSimpleName(), mis,e);
        }
        return null;

    }

    @Override
    public Response<Void> checkParam(PatientRequestParam patientRequestParam) {
        Response<Void> response = NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        try{
            Response<Void> responseCheck = prClewOutsideClient.checkParam(patientRequestParam);
            log.info("responseCheck:{}", responseCheck);
            if (responseCheck.notOk()){
                response = NewResponseUtil.makeFail(responseCheck.getMsg());
            }else{
                response = responseCheck;
            }
        }catch (Exception e){
            log.error("{} checkParam param:{} error",this.getClass().getSimpleName(),JSON.toJSONString(patientRequestParam),e);
        }
        return response;
    }

    @Override
    public Response<Void> addClew(PatientRequestParam patientRequestParam){
        Response<Void> response = NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        try {
            response = prClewOutsideClient.addClew(patientRequestParam);
            log.info("addClew response:{}", response);
        }catch (Exception e){
            log.error("{} addClew request:{} error",this.getClass().getSimpleName(),JSON.toJSONString(patientRequestParam),e);
        }
        return response;
    }

    @Override
    public Response<Patient> getByPhone(String phone) {
        Response<Patient> response = NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        try {
            Response<Patient> responsePhone = patientInfoClient.getByPhone(phone);
            log.info("getByPhone phone:{} result:{}", phone, responsePhone);
            if (responsePhone.notOk()) {
                response = NewResponseUtil.makeFail(responsePhone.getMsg());
            } else {
                response = responsePhone;
            }
        } catch (Exception e) {
            log.error("{} getByPhone request:{} error", this.getClass().getSimpleName(), phone, e);
        }
        return response;
    }

    @Override
    public Response<PatientNodeInfo> getNodeInfoByPatientId(long patientId){
        Response<PatientNodeInfo> response = NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        try{
            Response<PatientNodeInfo> responseNodeInfo = prClewOutsideClient.getNodeInfoByPatientId(patientId, PatientClewInfoVersion.OFFLINE.getCode());
            if (responseNodeInfo.notOk()){
                response = NewResponseUtil.makeFail(responseNodeInfo.getMsg());
            }else{
                response = responseNodeInfo;
            }
        }catch (Exception e){
            log.error("{} getNodeInfoByPatientId request:{} error",this.getClass().getSimpleName(),patientId,e);
        }
        return response;
    }

    @Override
    public Response<PrPatientCommonMapVo> getCommonEnum(){
        Response<PrPatientCommonMapVo> response = NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        try{
            Response<PrPatientCommonMapVo> responseCommon = patientEnumCommonClient.getCommonEnum();
            if (responseCommon.notOk()){
                response = NewResponseUtil.makeFail(responseCommon.getMsg());
            }else{
                response = responseCommon;
            }
        }catch (Exception e){
            log.error("{} getCommonEnum error",this.getClass().getSimpleName(),e);
        }
        return response;
    }

    @Override
    public Response<String> searchForStaging(int type){
        Response<String> response = NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        try{
            Response<String> responseSearch = patientEnumCommonClient.searchForStaging(type);
            if (responseSearch.notOk()){
                response = NewResponseUtil.makeFail(responseSearch.getMsg());
            }else{
                response = responseSearch;
            }
        }catch (Exception e){
            log.error("{} getCommonEnum error",this.getClass().getSimpleName(),e);
        }
        return response;
    }

    @Override
    public Response<String> searchForStagingV2(int disease){
        Response<String> response = NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        try{
            Response<String> responseSearch = patientEnumCommonClient.searchForStagingV2(disease);
            if (responseSearch.notOk()){
                response = NewResponseUtil.makeFail(responseSearch.getMsg());
            }else{
                response = responseSearch;
            }
        }catch (Exception e){
            log.error("{} getCommonEnum error",this.getClass().getSimpleName(),e);
        }
        return response;
    }

    @Override
    public PrUserDto getByMisId(String mis){
        if (StringUtils.isEmpty(mis)){
            return null;
        }
        try{
            Response<PrUserDto> rpcResult = prUserClient.getByMis(mis);
            return rpcResult.getData();
        }catch (Exception e){
            log.error("{} getByMisId error",this.getClass().getSimpleName(),e);
        }
        return null;
    }

    @Override
    public Response<List<CityVo>> allCity() {
        return prClewOutsideClient.allCity();
    }

    @Override
    public Response<Map<Character, List<PinyinAnalysis>>> allDepartmentOnSort() {
        return patientDepartmentInfoClient.getDepartmentOnPinyinSort();
    }

    @Override
    public Response<List<PatientVisitDto>> visitList(String preassignedMisId, String phone, int pageNum, int pageSize) {
        Response<List<PatientVisitDto>> visitListResponse = prClewOutsideClient.visitList(preassignedMisId, phone, pageNum, pageSize);
        log.debug("param:{}phone:{}visitListResponse:{}", preassignedMisId, phone, visitListResponse);
        return visitListResponse;
    }

    @Override
    public boolean simpleCheck(PatientClewSimpleCheckParam simpleCheckParam) {
        try {
            Response<PatientClewCheckResultDto> checkResultDtoResponse = prClewOutsideClient.simpleCheck(simpleCheckParam);
            log.info("checkResultDtoResponse param:{} result:{}", simpleCheckParam, checkResultDtoResponse);
            if (checkResultDtoResponse.notOk() || checkResultDtoResponse.getData() == null) {
                return false;
            }
            return checkResultDtoResponse.getData().isValid();
        } catch (Exception e) {
            log.error("simpleCheck error", e);
            return false;
        }
    }


    @Override
    public PatientRequestParam getByClewId(long clueId, int clueType) {
        Response<PatientRequestParam> clewPatientInfo = prClewOutsideClient.getClewPatientInfo(clueType, clueId);
        log.debug("getByClewId clueId:{} response:{}", clueId, clewPatientInfo);
        if (clewPatientInfo.notOk()) {
            return null;
        }
        return clewPatientInfo.getData();
    }

}
