package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CaseLifeCircleModel;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-09-03 10:54 上午
 *
 * 通过节点的环节判断要调用哪个接口
 **/

public interface ICaseLifeCycleDelegate {

    /**
     * 增减备注信息
     * @param caseLifeCircleModels
     * @param caseId
     */
    void addLifeCycleComment(List<CaseLifeCircleModel> caseLifeCircleModels, int caseId);
}
