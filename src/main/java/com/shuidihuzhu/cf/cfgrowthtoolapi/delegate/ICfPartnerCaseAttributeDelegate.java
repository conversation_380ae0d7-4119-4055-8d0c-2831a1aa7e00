package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import org.apache.commons.lang3.tuple.ImmutablePair;

/**
 * <AUTHOR>
 * @date 2021-09-02
 */
public interface ICfPartnerCaseAttributeDelegate {

    /**
     * 确认案例归属于哪个兼职
     * 兼职线索与案例归属关系：
     * A、案例发起24小时内，顾问补录兼职推荐线索，归属为兼职推荐案例
     * B、推荐线索后72小时内发起案例，归属为兼职推荐案例
     * C、同一顾问的多个兼职推荐同一个手机号，谁先推荐算谁的
     * @param caseId
     * @param uniqueCode
     * @param raiserPhone
     * @return
     */
    ImmutablePair<String, Boolean> getAttributePartnerByCaseId(Integer caseId, String uniqueCode, String raiserPhone);
}
