package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.RiskRpcDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.SeaAdminUserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.client.adminpure.feign.ReportFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.report.ReportInfoVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.client.ugc.model.view.*;
import com.shuidihuzhu.cf.client.ugc.service.RiskControlWordCheckFeignV2Client;
import com.shuidihuzhu.cf.risk.client.admin.blacklist.BlacklistClient;
import com.shuidihuzhu.cf.risk.client.aegis.EngineAnalysisClient;
import com.shuidihuzhu.cf.risk.client.risk.AccidentCaseClient;
import com.shuidihuzhu.cf.risk.model.admin.blacklist.BlackListAddParam;
import com.shuidihuzhu.cf.risk.model.aegis.RiskObject;
import com.shuidihuzhu.cf.risk.model.risk.ParticipateCaseInfo;
import com.shuidihuzhu.client.cf.growthtool.model.PreposeAddRiskBlackModel;
import com.shuidihuzhu.common.web.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020-07-01
 */

@Service
@Slf4j
public class RiskRpcDelegateImpl implements RiskRpcDelegate {

    @Autowired
    private EngineAnalysisClient analysisClient;

    @Autowired
    private SeaAccountServiceDelegate accountServiceDelegate;

    @Autowired
    private ReportFeignClient reportFeignClient;

    @Autowired
    private AccidentCaseClient accidentCaseClient;
    
    @Autowired
    private BlacklistClient blacklistClient;

    @Autowired
    private RiskControlWordCheckFeignV2Client riskControlWordCheckFeignV2Client;


    private final static String expire_key = "case_repeat_expired_pre-review";

    private final static String same_period = "case_repeat_caseing_same_period";

    private final static String pretrial_same_period = "case_repeat_pretrial_same_period";

    private final static String repeat_raise_modelGuid = "8416765febc945b991f9ff947297881a";

    //恶劣涉刑
    private final static String bad_crime = "bad_crime";

    //嫌疑人
    private final static String suspect = "suspect";

    //驾驶违规
    private final static String driving_violations = "driving_violations";

    private final static List<PreposeMaterialModel.CriminalTypeEnum> suspectCriminalList = Lists.newArrayList(
            PreposeMaterialModel.CriminalTypeEnum.INMATES,
            PreposeMaterialModel.CriminalTypeEnum.DRUG_RELATED_PERSON,
            PreposeMaterialModel.CriminalTypeEnum.SUSPECT
    );

    @Override
    public String getRiskInfoByPatientId(int caseId, int patientIdType, String patientId, String mis) {
        String msg = "";
        Map<String, RiskObject> riskInfo = this.getRiskInfo(caseId, patientIdType, patientId, mis);
        log.info(this.getClass() + "返回结果:{}", JSON.toJSONString(riskInfo));
        if (MapUtils.isNotEmpty(riskInfo)) {
            RiskObject riskObject = riskInfo.get(expire_key);
            boolean risk1 = Objects.nonNull(riskObject) && riskObject.isRiskFlag();
            if (risk1) {
                msg = "";
                return msg;
            }
            RiskObject samePeriodRisk = riskInfo.get(same_period);
            RiskObject pretrialSamePeriod = riskInfo.get(pretrial_same_period);
            boolean risk2 = Objects.nonNull(samePeriodRisk) && samePeriodRisk.isRiskFlag();
            boolean risk3 = Objects.nonNull(pretrialSamePeriod) && pretrialSamePeriod.isRiskFlag();
            if (risk2 && risk3) {
                return "此患者已有在筹案例，代录入信息无法提交";
            }
            if (risk2) {
                return "此患者已有在筹案例，代录入信息无法提交";
            }
            if (risk3) {
                return "此患者已有预审中案例";
            }
        }
        return msg;
    }

    @Override
    public ReportInfoVO getReportInfoByCaseId(int caseId){
        ReportInfoVO reportInfoVO = null;
        try{
            OperationResult<ReportInfoVO> operationResult = reportFeignClient.getReportInfoByCaseId(caseId);
            reportInfoVO = operationResult.isSuccess() ? operationResult.getData() : null;
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" getReportInfoByCaseId:{} err",caseId,e);
        }
        return reportInfoVO;
    }


    private Map<String, RiskObject> getRiskInfo(int caseId, int patientIdType, String patientId, String mis) {
        InitialRepeatRiskParam param = new InitialRepeatRiskParam();

        SeaAdminUserInfoModel seaAdminUserInfoModel = accountServiceDelegate.getOrgInfoByMisUseCache(mis);
        if (Objects.nonNull(seaAdminUserInfoModel)) {
            param.setCallName(seaAdminUserInfoModel.getName());
            Long userId = seaAdminUserInfoModel.getUserId();
            param.setCallUserId(userId != null ? userId : 0L);
            param.setCallOrg(seaAdminUserInfoModel.getOrgName());
        }
        String reqId = "";
        if (caseId > 0) {
            param.setCaseId(caseId);
            reqId = String.valueOf(caseId);
        }
        param.setCurrTime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        param.setPatientIdType(patientIdType);
        if (patientIdType == 1) {
            param.setCryptoIdCard(ShuidiCipherUtils.encrypt(patientId));
        }
        if (patientIdType == 2) {
            param.setPatientBornCard(patientId);
        }
        String json = JSON.toJSONString(param);
        log.info(this.getClass() + ", reqId:{}, param,:{}", reqId, json);
        Response<Map<String, RiskObject>> analyze = analysisClient.analyze(repeat_raise_modelGuid, reqId, json);
        log.info("调用getRiskInfoByPatientId返回结果:{}", JSON.toJSONString(analyze));
        if (analyze.ok() && Objects.nonNull(analyze.getData())) {
            return analyze.getData();
        }
        return Maps.newHashMap();
    }


    @Override
    public boolean judgeAccidentCaseStrategy(String title, String content) {
        if (StringUtils.isBlank(title) || StringUtils.isBlank(content)) {
            return false;
        }
        ParticipateCaseInfo participateCaseInfo = ParticipateCaseInfo
                .builder()
                .title(title)
                .content(content)
                .build();
        Response<Boolean> response = accidentCaseClient.judgeAccidentCaseStrategy(participateCaseInfo);
        log.info("participateCaseInfo:{},result:{}", participateCaseInfo, response);
        return Optional.ofNullable(response.getData()).orElse(false);
    }


    public void addBackList(PreposeAddRiskBlackModel preposeAddBlackModel) {
        BlackListAddParam blackListAddParam = new BlackListAddParam();

        List<String> typeUuidList = Lists.newArrayList();
        Integer illegalDrivingType = preposeAddBlackModel.getIllegalDrivingType();
        String addReason = "";
        PreposeMaterialModel.IllegalDrivingTypeEnum illegalDrivingTypeEnum = PreposeMaterialModel.parseIllegalDrivingTypeEnum(illegalDrivingType);
        if (illegalDrivingTypeEnum != null && illegalDrivingTypeEnum != PreposeMaterialModel.IllegalDrivingTypeEnum.NOT_MATCH) {
            addReason = addReason + illegalDrivingTypeEnum.getDesc();
            typeUuidList.add(driving_violations);
        }
        Integer criminalType = preposeAddBlackModel.getCriminalType();
        PreposeMaterialModel.CriminalTypeEnum criminalTypeEnum = PreposeMaterialModel.parseCriminalTypeEnum(criminalType);
        if (criminalTypeEnum != null && criminalTypeEnum != PreposeMaterialModel.CriminalTypeEnum.NOT_MATCH) {
            if (StringUtils.isNotBlank(addReason)) {
                addReason = addReason + ";";
            }
            addReason = addReason + criminalTypeEnum.getDesc();
        }
        if (criminalTypeEnum == PreposeMaterialModel.CriminalTypeEnum.MURDERER) {
            typeUuidList.add(bad_crime);
        }
        if (suspectCriminalList.contains(criminalTypeEnum)) {
            typeUuidList.add(suspect);
        }
        if (CollectionUtils.isEmpty(typeUuidList)) {
            log.info("没找到对应的黑名单类型");
            return;
        }
        blackListAddParam.setTypeUuidList(typeUuidList);
        Integer patientIdCardType = preposeAddBlackModel.getPatientIdCardType();
        preposeAddBlackModel.setAddReason(addReason);
        blackListAddParam.setOperateReason(preposeAddBlackModel.getAddReason());
        if (Objects.equals(patientIdCardType, PreposeMaterialModel.PatientIdCardTypeEnum.IDENTITY_CARD.getCode())) {
            blackListAddParam.setIdCard(preposeAddBlackModel.getPatientIdCard());
        } else {
            blackListAddParam.setBornCard(preposeAddBlackModel.getPatientIdCard());
        }
        blackListAddParam.setMobile(preposeAddBlackModel.getRaiseMobile());
        blackListAddParam.setUserName(preposeAddBlackModel.getPatientName());
        Response<Void> addResult = blacklistClient.add(blackListAddParam);
        log.info("param:{},result:{}", blackListAddParam, addResult);
    }

    @Override
    public RiskWordResult checkHitRiskWord(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        RiskWordCheckContext riskWordCheckParam = new RiskWordCheckContext();
        riskWordCheckParam.setContent(content);
        riskWordCheckParam.setUseScenes(Lists.newArrayList(RiskControlWordCategoryDO.RiskWordUseScene.CASE_RAISE_TITLE.getCode(),
                RiskControlWordCategoryDO.RiskWordUseScene.PUBLISH_UGC.getCode(),
                RiskControlWordCategoryDO.RiskWordUseScene.TITLE_CONTENT.getCode()));
        RpcResult<RiskWordResult> response = riskControlWordCheckFeignV2Client.check(riskWordCheckParam);
        log.info("riskWordCheckParamV2:{},result:{}", riskWordCheckParam, response);
        return Optional.ofNullable(response).map(RpcResult::getData).orElse(null);
    }


    @Data
    public static class InitialRepeatRiskParam {
        private String currTime;
        private Integer caseId;
        private int patientIdType;
        private String cryptoIdCard;
        private String patientBornCard;
        private String callName = "";
        private long callUserId = 0;
        private String callOrg;
    }
}
