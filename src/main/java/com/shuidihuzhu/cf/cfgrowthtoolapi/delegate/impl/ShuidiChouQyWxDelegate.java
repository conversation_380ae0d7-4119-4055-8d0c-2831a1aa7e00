package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IShuidiChouQyWxDelegate;
import com.shuidihuzhu.client.cf.clewtrack.client.ShuidiChouQyWxFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2019/12/12 11:46 AM
 */
@Service
@Slf4j
public class ShuidiChouQyWxDelegate implements IShuidiChouQyWxDelegate {
    @Autowired
    private ShuidiChouQyWxFeignClient shuidiChouQyWxFeignClient;
    @Override
    public void sendShuidiChouMsg(String mis, String content) {
        Response<String> response = shuidiChouQyWxFeignClient.sendShuidiChouMsg(mis, content);
        log.info(this.getClass().getSimpleName()+" sendShuidiChouMsg result:{}", JSON.toJSONString(response));
    }
}
