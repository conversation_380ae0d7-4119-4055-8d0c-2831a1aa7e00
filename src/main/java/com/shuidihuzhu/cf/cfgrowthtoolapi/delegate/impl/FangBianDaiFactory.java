package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.OrderCallbackModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IFangBianDaiDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IFangBianDaiFactory;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiOrderDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfJiekongDeviceDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.FangBianDaiEnums;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;


/**
 * @author: wanghui
 * @create: 2020/8/24 3:50 下午
 */
@Slf4j
@Service
public class FangBianDaiFactory implements IFangBianDaiFactory {

    @Autowired
    private ApplicationContext applicationContext;

    private IFangBianDaiDelegate newInstanceByDeviceSupplier(String deviceSupplier){
        FangBianDaiEnums.DeviceSupplierEnum deviceSupplierEnum = FangBianDaiEnums.DeviceSupplierEnum.parse(deviceSupplier);
        try {
            return applicationContext.getBean(deviceSupplierEnum.getDeviceSupplier(),IFangBianDaiDelegate.class);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName()+" newInstanceByDeviceSupplier 失败 deviceSupplier:{} e",deviceSupplier,e);
        }
        return null;
    }

    @Override
    public OpResult<CfFangbiandaiOrderDO> createOrder(CfJiekongDeviceDO cfJiekongDeviceDO, String orderID, int priceInFen) {
        IFangBianDaiDelegate fangBianDaiDelegate = newInstanceByDeviceSupplier(cfJiekongDeviceDO.getDeviceSupplier());
        if (fangBianDaiDelegate==null){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_ERROR);
        }
        OpResult<CfFangbiandaiOrderDO> createOrderResult = fangBianDaiDelegate.createOrder(cfJiekongDeviceDO, orderID, priceInFen);
        log.info(this.getClass().getSimpleName()+" createOrder cfJiekongDeviceDO:{} orderID:{} priceInFen:{} result:{}",cfJiekongDeviceDO,orderID,priceInFen,createOrderResult);
        if (createOrderResult.isFailOrNullData()){
            return OpResult.createFailResult(createOrderResult.getErrorCode());
        }
        return OpResult.createSucResult(createOrderResult.getData());
    }

    @Override
    public OpResult<Boolean> handleCallbackForUpdate(String orderId, String supplierOrderStatus,String remark, String deviceSupplier){
        IFangBianDaiDelegate fangBianDaiDelegate = newInstanceByDeviceSupplier(deviceSupplier);
        if (fangBianDaiDelegate==null){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_ERROR);
        }
        return fangBianDaiDelegate.handleCallbackForUpdate(orderId, supplierOrderStatus, remark);
    }
    @Override
    public OpResult<Boolean> queryOrderForUpdate(CfFangbiandaiOrderDO orderFromDB){
        IFangBianDaiDelegate fangBianDaiDelegate = newInstanceByDeviceSupplier(orderFromDB.getDeviceSupplier());
        if (fangBianDaiDelegate==null){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_ERROR);
        }
        return fangBianDaiDelegate.queryOrderForUpdate(orderFromDB);
    }
    @Override
    public Integer parseSuccessStatus(String deviceSupplier){
        IFangBianDaiDelegate fangBianDaiDelegate = newInstanceByDeviceSupplier(deviceSupplier);
        if (fangBianDaiDelegate==null){
            return -1;
        }
        return fangBianDaiDelegate.getSuccessStatus();
    }
}
