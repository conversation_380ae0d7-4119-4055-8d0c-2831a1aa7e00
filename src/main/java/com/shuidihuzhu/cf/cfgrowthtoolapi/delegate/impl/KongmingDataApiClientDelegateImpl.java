package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IKongmingDataApiClientDelegate;
import com.shuidihuzhu.client.dataservice.kongmingdata.v1.KongmingDataApiClient;
import com.shuidihuzhu.client.dataservice.kongmingdata.v1.dto.ExternalLoginRequest;
import com.shuidihuzhu.client.dataservice.kongmingdata.v1.dto.GuanDataSSOLoginVO;
import com.shuidihuzhu.client.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
@Slf4j
@Component
public class KongmingDataApiClientDelegateImpl implements IKongmingDataApiClientDelegate {

    @Resource
    private KongmingDataApiClient kongmingDataApiClient;

    @Override
    public Optional<String> externalLogin(ExternalLoginRequest request) {

        Response<GuanDataSSOLoginVO> response = kongmingDataApiClient.externalLogin(request);
        log.debug("ExternalLoginRequest request:{}, response: {}", request, response);
        if (response != null && Objects.nonNull(response.getData()) ) {
            return Optional.ofNullable(response.getData().getRedirectUrl());
        }
        return Optional.empty();
    }
}
