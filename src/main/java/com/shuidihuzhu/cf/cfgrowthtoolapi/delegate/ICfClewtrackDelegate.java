package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.model.*;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/5/17 3:40 PM
 */
public interface ICfClewtrackDelegate {
    ImmutablePair<String, String> getChannelRefineResultByOtherChannel(Long infoId, Long userId);

    /**
     *
     * @param uniqueCode
     * @param phone
     * @param fundraisingObject
     * @param diseaseName
     * @param hosptialName
     * @param address  是json串  内容包括  department  sickroom  sickbed
     * @return
     */
    OpResult<CfBdClewForPreposeMaterialModel> checkPhoneIsExistInBDClewV2(String uniqueCode, String phone, String fundraisingObject, String diseaseName, String hosptialName, String address);

    List<String> getOnline111FuWuUser();

    List<String> getOnlineMenue1v1TestFuWuUser(int type);

    List<CfClewForCharityDayModel> getCfClewForCharityDayModelsByDisplayIds(List<String> displayIds);

    List<CfClewForCharityDayModel> getCfClewForCharityDayModelsByPhones(List<String> phones);

    void syncApproveStatus(Long clewId, Integer approveStatus);

    Long getNewClewCountByUniqueListWithTime(String startTime, String endTime, List<String> uniqueCodeList);

    Long getNewClewCountByOrgListWithTime(String startTime,
                                          String endTime,
                                          List<Integer> orgIdList);

    void updateClewStatusWithAttachInfo(Long clewId);

    CfClewBaseInfoDO getCfClewBaseInfoDOByUniqueCodeWithInfoUuid(String uniqueCode,
                                                                 Long infoId);

    List<GwClewCountGroupDayModel> listClewCountGroupByDay(String startTime,
                                                           String endTime,
                                                           List<String> uniqueCodeList);

    List<CfClewVolunteerFollowCountModel> getVolunteerTransformClewRecord(List<String> uniqueCodeList);


    FuwuMemberInfo getFuwuMemberInfo(String phone);

    List<ClewCrashSimpleInfo> listClewCrashInfo(List<String> encryptPhoneList);
}
