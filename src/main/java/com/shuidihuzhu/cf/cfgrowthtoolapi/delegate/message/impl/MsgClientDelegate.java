package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.message.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.message.IMsgClientDelegate;
import com.shuidihuzhu.cf.clinet.event.center.util.MsgUtil;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.msg.vo.MessageFeedBack;
import com.shuidihuzhu.msg.vo.MsgResponse;
import com.shuidihuzhu.msg.vo.rpc.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MsgClientDelegate implements IMsgClientDelegate {

    @Autowired
    private MsgClientV2 msgClientV2;

    @Autowired
    private MsgUtil msgUtil;
    private static final Set<String> blackList = Collections.unmodifiableSet(ImmutableSet.of("13366147185"));

    private boolean inBlackList(String mobile) {
        return blackList.contains(mobile);
    }

    @Override
    public void sendMessage(String modelNum, String message, List<String> mobiles) {
        if (StringUtils.isEmpty(message) || CollectionUtils.isEmpty(mobiles)) {
            return;
        }

        //过滤黑名单手机号
        Set<String> mobileSet = mobiles.stream().filter(v -> !inBlackList(v)).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mobileSet)) {
            return;
        }

        Map<Integer, String> params = Maps.newHashMap();
        params.put(1, message);

        Map<String, Map<Integer, String>> smsMsgMap = Maps.newHashMap();
        for (String mobile : mobileSet) {
            smsMsgMap.put(mobile, params);
        }

        msgUtil.sendSmsParamsMsg(modelNum, smsMsgMap, false);
    }


    @Override
    public void saveBatch(MsgRecordBatch msgRecordBatch) {
        MsgResponse<List<MessageFeedBack>> msgResponse = msgClientV2.saveBatchV2(msgRecordBatch);
        log.info("发送消息 msgRecordBatch:{}, msgResponse:{}", msgRecordBatch, JSON.toJSONString(msgResponse));
    }

    @Override
    public void sendSms(String modelNum, String cryptoMobile, Map<Integer, String> params) {
        Map<String, Map<Integer, String>> smsMsgMap = Maps.newHashMap();
        smsMsgMap.put(cryptoMobile, params);
        msgUtil.sendSmsParamsMsg(modelNum, smsMsgMap, true);
    }

    @Override
    public void sendWxMsg(String modelNum, List<Long> userIdList, int thirdType) {
        msgUtil.sendWxMsg(modelNum, userIdList, thirdType);
    }
}
