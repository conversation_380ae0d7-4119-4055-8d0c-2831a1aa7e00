package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfCrowdfundingCityDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.city.CityAliasModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ListUtil;
import com.shuidihuzhu.cf.dao.city.CityAliasDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.client.cf.api.client.CrowdfundingCityFeignClient;
import com.shuidihuzhu.client.cf.api.model.CrmCityModel;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/11/26 4:28 PM
 */
@Service
public class CfCrowdfundingCityDelegate implements ICfCrowdfundingCityDelegate {
    @Autowired
    private CrowdfundingCityFeignClient crowdfundingCityFeignClient;

    @Autowired
    private CityAliasDao cityAliasDao;


    @Override
    public List<CrowdfundingCity> getProvince() {
        Response<List<String>> response = crowdfundingCityFeignClient.getProvince();
        return response.ok()? ListUtil.getModelListFromListString(response.getData(),CrowdfundingCity.class): Lists.newArrayList();
    }

    @Override
    public List<CrowdfundingCity> getChildren(int parentId) {
        Response<List<String>> response = crowdfundingCityFeignClient.getChildren(parentId);
        return response.ok()? ListUtil.getModelListFromListString(response.getData(),CrowdfundingCity.class): Lists.newArrayList();
    }

    @Override
    public List<CrowdfundingCity> getListByIds(List<Integer> ids) {
        Response<List<String>> response = crowdfundingCityFeignClient.getListByIds(ids);
        return response.ok()? ListUtil.getModelListFromListString(response.getData(),CrowdfundingCity.class): Lists.newArrayList();
    }

    @Override
    public List<CrmCityModel> fuzzyByCityName(String cityName) {
        Response<List<CrmCityModel>> response = crowdfundingCityFeignClient.listByCityName(cityName, 20);
        return response.ok() ? response.getData() : Lists.newArrayList();
    }


    @Override
    public int getCityIdByCityName(String cityName) {
        if (StringUtils.isNotEmpty(cityName) && !"其他城市".equals(cityName)){
            Response<List<CrmCityModel>> response = crowdfundingCityFeignClient.listByCityName(cityName,100);
            if(response.notOk() || CollectionUtils.isEmpty(response.getData())){
                return 0;
            }
            List<CrmCityModel> cityModels = response.getData().stream().filter(s->s.getName().compareTo(cityName)==0).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(cityModels)){
                List<CityAliasModel> cityAliasModels = cityAliasDao.getByCityNames(Lists.newArrayList(cityName));
                if(CollectionUtils.isEmpty(cityAliasModels)){
                    return 0;
                }else{
                    Set<Integer> cityIds = cityAliasModels.stream().map(CityAliasModel::getMainCityId).collect(Collectors.toSet());
                    cityModels = response.getData().stream().filter(s->cityIds.contains(s.getId())).collect(Collectors.toList());;
                    if(CollectionUtils.isEmpty(cityModels)){
                        return 0;
                    }
                    return this.getRealCityId(cityModels.get(0));
                }
            }else{
                return this.getRealCityId(cityModels.get(0));
            }
        }else{
            return 0;
        }
    }


    public int getRealCityId(CrmCityModel crmCityModel){
        if (crmCityModel.getParentId() != crmCityModel.getRealParentId()){
            return crmCityModel.getRealParentId();
        }else{
            return crmCityModel.getId();
        }
    }
}
