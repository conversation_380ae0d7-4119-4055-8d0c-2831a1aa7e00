package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfPatientMaterialClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.CrowdFundingFeignDelegateImpl;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfPatientMaterialClient;
import com.shuidihuzhu.cf.client.material.model.CfPatientBaseInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-02-10
 */
@Service
@Slf4j
@RefreshScope
public class CfPatientMaterialClientDelegateImpl implements ICfPatientMaterialClientDelegate {

    @Autowired
    private CfPatientMaterialClient cfPatientMaterialClient;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private CrowdFundingFeignDelegateImpl crowdFundingFeignDelegate;

    @Override
    public OpResult<List<CfPatientBaseInfoVo.PatientResult>> queryPatientVoByIdCards(List<String> patientIdCards) {
        try {
            RpcResult<List<CfPatientBaseInfoVo.PatientResult>> rpcResult = cfPatientMaterialClient.queryPatientVoByIdCards(patientIdCards);
            if (!rpcResult.isSuccess()) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
            }
            log.debug(this.getClass().getSimpleName() + "queryPatientVoByIdCards request:{},response:{}", patientIdCards, JSON.toJSONString(rpcResult.getData()));
            return OpResult.createSucResult(rpcResult.getData());
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + "queryPatientVoByIdCards request:{} error", patientIdCards, e);
        }
        return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
    }

    @Override
    public OpResult<Boolean> checkIsFirstCaseByPatientCryptCard(String patientCryptIdcard) {
        String patientIdCard = shuidiCipher.decrypt(patientCryptIdcard);
        OpResult<List<CfPatientBaseInfoVo.PatientResult>> opResult = this.queryPatientVoByIdCards(Lists.newArrayList(patientIdCard));
        if (opResult.isFail()) {
            return OpResult.createFailResult(opResult.getErrorCode());
        } else {
            List<CfPatientBaseInfoVo.PatientResult> patientResultList = opResult.getData();
            if (CollectionUtils.isEmpty(patientResultList)) {
                return OpResult.createSucResult(false);
            }
            CfPatientBaseInfoVo.PatientResult patientResult = patientResultList.get(0);
            if (patientResult.getCaseList().size() == 1) {
                return OpResult.createSucResult(true);
            } else {
                return OpResult.createSucResult(false);
            }
        }
    }


    @Override
    public int caseDuplicateNum(int caseId) {
        CfFirsApproveMaterial firsApproveMaterial = crowdFundingFeignDelegate.getAuthorInfoByInfoId(caseId);
        if (firsApproveMaterial == null) {
            log.info("调用getAuthorInfoByInfoId接口失败");
            return 0;
        }
        String infoId = firsApproveMaterial.getInfoUuid();
        String patientCryptIdCard = firsApproveMaterial.getPatientCryptoIdcard();
        String patientIdCard = "";
        if (StringUtils.isEmpty(patientCryptIdCard)) {
            patientIdCard = firsApproveMaterial.getPatientBornCard();
        } else {
            patientIdCard = shuidiCipher.decrypt(patientCryptIdCard);
        }
        if (StringUtils.isBlank(patientIdCard)) {
            log.info("案例:{}找不到对应的身份证或者出生证", caseId);
            return 0;
        }
        OpResult<List<CfPatientBaseInfoVo.PatientResult>> opResult = this.queryPatientVoByIdCards(Lists.newArrayList(patientIdCard));
        if (opResult.isFail()) {
            return 0;
        } else {
            List<CfPatientBaseInfoVo.PatientResult> patientResultList = opResult.getData();
            if (CollectionUtils.isEmpty(patientResultList)) {
                return 0;
            }
            CfPatientBaseInfoVo.PatientResult patientResult = patientResultList.get(0);
            List<String> infoIds = Optional.ofNullable(patientResult)
                    .map(CfPatientBaseInfoVo.PatientResult::getCaseList)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .filter(item -> !ObjectUtils.nullSafeEquals(infoId, item.getInfoUuId()))
                    .map(CfPatientBaseInfoVo.CasePatientVo::getInfoUuId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(infoIds)) {
                return 0;
            }
            //查询非当前案例是否有筹款金额超过500的
            long count = crowdFundingFeignDelegate.getMapByInfoUuIds(infoIds)
                    .values()
                    .stream()
                    .filter(item -> item.getAmount() > 500 * 100)
                    .count();
            return (int) count;
        }
    }
}
