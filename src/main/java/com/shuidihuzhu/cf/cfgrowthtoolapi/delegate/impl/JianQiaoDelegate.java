package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jianqiao.JianQiaoFeignClient;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jianqiao.model.CreateOrderResponse;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jianqiao.model.QueryDeviceResponse;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jianqiao.model.QueryOrderResponse;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.OrderCallbackModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.jiekong.model.OrderInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IFangBianDaiDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IWorkWeiXinDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiOrderDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfJiekongDeviceDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.FangBianDaiEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfFangbiandaiService;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.MD5Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020/8/24 8:22 下午
 */
@Slf4j
@Component("JianQiaoDelegate")
@RefreshScope
public class JianQiaoDelegate implements IFangBianDaiDelegate {

    @Value("${apollo.jianqiao.key:AAr1gTsSA9sSw70CCD9v}")
    private String key = "AAr1gTsSA9sSw70CCD9v";
    @Value("${apollo.jianqiao.biz_id:1386}")
    private int biz_id = 1386;
    @Autowired
    private JianQiaoFeignClient jianQiaoFeignClient;
    @Autowired
    private IWorkWeiXinDelegate workWeiXinDelegateImpl;
    @Autowired
    private ICfFangbiandaiService cfFangbiandaiService;

    @Override
    public OpResult<CfFangbiandaiOrderDO> createOrder(CfJiekongDeviceDO cfJiekongDeviceDO, String orderID, int priceInFen) {
        if (priceInFen<=0){priceInFen = 1;}
        JSONObject bizContentJSON = new JSONObject();
        bizContentJSON.put("biz_order_no",orderID);
        bizContentJSON.put("dev_id",cfJiekongDeviceDO.getDeviceId());
        bizContentJSON.put("price",priceInFen);
        String method = "gw.order.create";
        String timestamp = DateUtil.getCurrentDateTimeStr();
        String sign = getSign(bizContentJSON.toJSONString(),method,timestamp);
        String response = jianQiaoFeignClient.requestURL(biz_id, method, sign, timestamp, bizContentJSON.toJSONString());
        log.info(this.getClass().getName()+" 供应商:{} 设备:{}  下单结果返回:{}",cfJiekongDeviceDO.getDeviceSupplier(),cfJiekongDeviceDO.getDeviceId(),response);
        if (StringUtils.isBlank(response) || !JSONObject.isValid(response)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.FALLBACK);
        }
        CreateOrderResponse createOrderResponse = new Gson().fromJson(response, CreateOrderResponse.class);
        if (createOrderResponse.getGw_response()!=null
                && "10000".equals(createOrderResponse.getGw_response().getCode())
                && createOrderResponse.getGw_response().getOrder_info()!=null){
            CreateOrderResponse.OrderInfoModel orderInfo = createOrderResponse.getGw_response().getOrder_info();
            CfFangbiandaiOrderDO cfFangbiandaiOrderDO = new CfFangbiandaiOrderDO();
            cfFangbiandaiOrderDO.setStatus(JianQiaoStatusEnum.PUSHING.statusEnum.getStatus());
            cfFangbiandaiOrderDO.setJiekongStatus(JianQiaoStatusEnum.PUSHING.getStatus());
            cfFangbiandaiOrderDO.setTransactionId(orderInfo.getOrder_no());
            cfFangbiandaiOrderDO.setOrderId(orderInfo.getBiz_order_no());
            return OpResult.createSucResult(cfFangbiandaiOrderDO);
        }
        // 业务不在需要提醒
        //workWeiXinDelegateImpl.sendByGroup(GeneralConstant.ALARMFANGBIANDAI,String.format("设备:%s 下单失败 原因：%s",
        //        cfJiekongDeviceDO.getMachineId() + " - " + cfJiekongDeviceDO.getDeviceId(),
        //        createOrderResponse.getGw_response()==null?"请求简巧超时":createOrderResponse.getGw_response().getMsg()));
        return OpResult.createFailResult(CfGrowthtoolErrorCode.FALLBACK);
    }

    @Override
    public OrderInfoModel query(CfFangbiandaiOrderDO orderFromDB) {
        JSONObject bizContentJSON = new JSONObject();
        bizContentJSON.put("biz_order_no",orderFromDB.getOrderId());
        String method = "gw.order.query";
        String timestamp = DateUtil.getCurrentDateTimeStr();
        String sign = getSign(bizContentJSON.toJSONString(),method,timestamp);
        String response = jianQiaoFeignClient.requestURL(biz_id, method, sign, timestamp, bizContentJSON.toJSONString());
        log.info(this.getClass().getName()+" 订单号:{}  查询订单结果返回:{}",orderFromDB.getOrderId(),response);
        if (StringUtils.isBlank(response) || !JSONObject.isValid(response)){
            return null;
        }
        QueryOrderResponse queryOrderResponse = new Gson().fromJson(response, QueryOrderResponse.class);
        if (queryOrderResponse.getGw_response()!=null
                && "10000".equals(queryOrderResponse.getGw_response().getCode())
                && queryOrderResponse.getGw_response().getOrder_status()!=null){
            QueryOrderResponse.orderStatusModel orderStatus = queryOrderResponse.getGw_response().getOrder_status();
            OrderInfoModel orderInfoModel = new OrderInfoModel();
            orderInfoModel.setCloseTime(orderStatus.getEnd_time());
            orderInfoModel.setPayTime(orderStatus.getCreate_time());
            orderInfoModel.setShipTime(orderStatus.getCreate_time());
            orderInfoModel.setErrorMsg(orderStatus.getOrder_msg());
            orderInfoModel.setErrorCode(orderStatus.getOrder_code());
            JianQiaoStatusEnum jianQiaoStatusEnum = JianQiaoStatusEnum.parse(orderStatus.getOrder_code());
            if (jianQiaoStatusEnum==null){
                log.error(this.getClass().getSimpleName()+" JianQiaoStatusEnum.parse result is null  orderId:{} param:{}",orderFromDB.getOrderId(),orderStatus.getOrder_code());
                return null;
            }
            orderInfoModel.setStatus(jianQiaoStatusEnum.getStatus());
            return orderInfoModel;
        }
        return null;
    }

    @Override
    public OpResult<Boolean> handleCallbackForUpdate(String orderId, String supplierOrderStatus,String remark) {
        CfFangbiandaiOrderDO orderFromDB = cfFangbiandaiService.getCfFangbiandaiOrderByOrderId(orderId);
        if (orderFromDB == null) {
            log.info(this.getClass().getName() + " getCfFangbiandaiOrderByOrderId param:{} result is null", orderId);
            return OpResult.createSucResult(false);
        }

        JianQiaoStatusEnum jianQiaoStatusEnum = JianQiaoStatusEnum.parse(orderFromDB.getJiekongStatus());
        if (jianQiaoStatusEnum.isEnd()) {
            log.info(this.getClass().getName() + " orderId:{} 该订单已结束 无需查询", orderFromDB.getOrderId());
            return OpResult.createSucResult(false);
        }

        jianQiaoStatusEnum = JianQiaoStatusEnum.parse(supplierOrderStatus);
        if (jianQiaoStatusEnum == null) {
            log.info(this.getClass().getName() + " JianQiaoStatusEnum.parse result is null. query:{}", supplierOrderStatus);
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ILLEGALLY);
        }
        orderFromDB.setRemark(remark);
        return fullAndUpdateOrderStatus(orderFromDB,jianQiaoStatusEnum.getStatus());
    }


    @Override
    public OpResult<Boolean> queryOrderForUpdate(CfFangbiandaiOrderDO orderFromDB) {
        JianQiaoStatusEnum jianQiaoStatusEnum = JianQiaoStatusEnum.parse(orderFromDB.getJiekongStatus());
        if (jianQiaoStatusEnum.isEnd()) {
            log.info(this.getClass().getName() + " orderId:{} 该订单已结束 无需查询", orderFromDB.getOrderId());
            return OpResult.createSucResult(false);
        }
        OrderInfoModel query = this.query(orderFromDB);
        // 查询为空 或者 状态和db中的一样 则无需处理 对于mq消费来说 此时需要重新丢一个消息 继续查
        if (query == null || orderFromDB.getJiekongStatus().equals(query.getStatus()) || query.getStatus() == null) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        }
        orderFromDB.setPayTime(query.getPayTime());
        orderFromDB.setShipTime(query.getShipTime());
        orderFromDB.setCloseTime(query.getCloseTime());
        orderFromDB.setRemark(query.getErrorMsg());
        return fullAndUpdateOrderStatus(orderFromDB,query.getStatus());
    }

    private OpResult<Boolean> fullAndUpdateOrderStatus(CfFangbiandaiOrderDO orderFromDB, Integer jianQiaoStatus){
        JianQiaoStatusEnum jianQiaoStatusEnum = JianQiaoStatusEnum.parse(jianQiaoStatus);
        log.info(this.getClass().getName() + " JianQiaoStatusEnum.parse result is null. jianQiaoStatus:{}", jianQiaoStatus);
        if (jianQiaoStatusEnum == null) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ILLEGALLY);
        }
        // 否则 需要更新db中的数据
        FangBianDaiEnums.StatusEnum statusEnum = jianQiaoStatusEnum.statusEnum;
        if (statusEnum == null) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ILLEGALLY);
        }
        orderFromDB.setStatus(statusEnum.getStatus());
        orderFromDB.setJiekongStatus(jianQiaoStatusEnum.getStatus());

        cfFangbiandaiService.updateOrder(orderFromDB);

        if (jianQiaoStatusEnum.getStatus() == JianQiaoStatusEnum.SUCCESS.getStatus()) {
            cfFangbiandaiService.decrStockNum(orderFromDB.getDeviceId(), orderFromDB.getDeviceSupplier());
        }
        if (jianQiaoStatusEnum.isEnd()) {
            if (jianQiaoStatusEnum.getStatus() == JieKongDelegate.JiekongStatusEnum.FAIL.getStatus()) {
                log.error(this.getClass().getName() + " 供应商:{} 设备:{} 订单被关闭 :{}", orderFromDB.getDeviceSupplier(),orderFromDB.getDeviceId(), jianQiaoStatus);
            }
            return OpResult.createSucResult();
        }
        return OpResult.createSucResult(true);
    }

    @Override
    public List<CfJiekongDeviceDO> list() {
        JSONObject bizContentJSON = new JSONObject();
        bizContentJSON.put("type","ALL");
        String method = "gw.vending.batchquery";
        String timestamp = DateUtil.getCurrentDateTimeStr();
        String sign = getSign(bizContentJSON.toJSONString(),method,timestamp);
        String response = jianQiaoFeignClient.requestURL(biz_id, method, sign, timestamp, bizContentJSON.toJSONString());
        log.info(this.getClass().getName()+" 供应商:jianqiao  查询设备信息结果返回:{}",response);
        if (StringUtils.isBlank(response) || !JSONObject.isValid(response)){
            return Lists.newArrayList();
        }
        QueryDeviceResponse queryDeviceResponse = new Gson().fromJson(response, QueryDeviceResponse.class);
        if (queryDeviceResponse.getGw_response()!=null
                && "10000".equals(queryDeviceResponse.getGw_response().getCode())
                && CollectionUtils.isNotEmpty(queryDeviceResponse.getGw_response().getDevice_info_list())){
            List<QueryDeviceResponse.DeviceInfoModel> deviceInfoList = queryDeviceResponse.getGw_response().getDevice_info_list();
            return CfJiekongDeviceDO.buildUpdateDeviceStatusModelByJianQiao(deviceInfoList);
        }
        return Lists.newArrayList();
    }

    @Override
    public int getSuccessStatus() {
        return JianQiaoStatusEnum.SUCCESS.getStatus();
    }

    @Getter
    public enum JianQiaoStatusEnum {
        DEFAULT(-1,"default" ,"默认",false, FangBianDaiEnums.StatusEnum.DEFAULT),
        PUSHING(20, "running","运行中",false,FangBianDaiEnums.StatusEnum.PUSHING),
        SUCCESS(100, "success","成功",true,FangBianDaiEnums.StatusEnum.SUCCESS),
        FAIL(70, "error","失败",true,FangBianDaiEnums.StatusEnum.FAIL),
        ;
        int status;

        String code;

        String desc;

        boolean isEnd;

        FangBianDaiEnums.StatusEnum statusEnum;

        JianQiaoStatusEnum(int status,String code, String desc,boolean isEnd,FangBianDaiEnums.StatusEnum statusEnum) {
            this.status = status;
            this.code = code;
            this.desc = desc;
            this.isEnd = isEnd;
            this.statusEnum = statusEnum;
        }
        public static JianQiaoStatusEnum parse(String code){
            for (JianQiaoStatusEnum e : JianQiaoStatusEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
            return null;
        }
        public static JianQiaoStatusEnum parse(int status){
            for (JianQiaoStatusEnum e : JianQiaoStatusEnum.values()) {
                if (status==e.getStatus()) {
                    return e;
                }
            }
            return null;
        }
        public static boolean success(int status){
            if (status==SUCCESS.getStatus() || status==PUSHING.getStatus()){
                return true;
            }
            return false;
        }
    }

    /**
     * 获得参数
     * @param bizContent
     * @param method
     * @return
     */
    private String getSign(String bizContent,String method,String timestamp){
        StringBuilder signParamsBuilder = new StringBuilder();
        signParamsBuilder.append("biz_content="+bizContent)
                .append("&biz_id="+biz_id)
                .append("&method="+method)
                .append("&timestamp="+ timestamp)
                .append("&key="+key);
        return MD5Util.getMD5HashValue(signParamsBuilder.toString()).toUpperCase();
    }
}
