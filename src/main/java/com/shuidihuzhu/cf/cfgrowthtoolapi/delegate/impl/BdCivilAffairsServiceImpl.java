package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.BdCivilAffairsService;
import com.shuidihuzhu.client.cf.api.client.MinistryCivilAffairsClient;
import com.shuidihuzhu.client.cf.api.model.CivilAffairsConfig;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/9/11 3:54 PM
 */
@Slf4j
@Service
public class BdCivilAffairsServiceImpl implements BdCivilAffairsService {

    @Resource
    private MinistryCivilAffairsClient ministryCivilAffairsClient;

    @Override
    public CivilAffairsConfig getCivilAffairsConfigByIp(String clientIp) {
        CivilAffairsConfig defaultConfig = CivilAffairsConfig.builder()
                .ipHitResult(false)
                .build();
        return Optional.ofNullable(clientIp)
                .filter(StringUtils::isNotBlank)
                .map(r -> {
                    Response<CivilAffairsConfig> response = ministryCivilAffairsClient.getCivilAffairsConfig(clientIp);
                    return Optional.ofNullable(response)
                            .filter(Response::ok)
                            .filter(resp -> Objects.nonNull(resp.getData()))
                            .map(Response::getData)
                            .orElse(defaultConfig);
                }).orElse(defaultConfig);
    }
}
