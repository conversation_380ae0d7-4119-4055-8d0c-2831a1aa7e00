package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.client.weilian.IMGroupInviteInfosData;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.weilian.ImGroupInfosData;
import com.shuidihuzhu.common.web.model.Response;

import java.util.Date;

public interface IWeiLianCrmClientDelegate {

    Response<ImGroupInfosData> getImGroupInfoList(Integer pageSize, Integer pageNo);

    Response<ImGroupInfosData> getImGroupInfoListByCTime(Integer pageSize, Integer pageNo, Date ctime);

    Response<IMGroupInviteInfosData> getImGroupInviteViews(Integer pageSize, Integer pageNo, String platformGid, Date ctime);

    Response<IMGroupInviteInfosData> getImGroupInviteViewsByCtime(Date ctime, Integer pageSize, Integer pageNo);
}
