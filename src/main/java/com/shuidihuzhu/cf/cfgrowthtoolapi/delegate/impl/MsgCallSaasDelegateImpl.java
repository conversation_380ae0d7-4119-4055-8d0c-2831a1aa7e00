package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IMsgCallSaasDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ISeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.SeaAdminUserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.baseservice.msg.call.MsgCallSaasClient;
import com.shuidihuzhu.msg.vo.MsgResponse;
import com.shuidihuzhu.msg.vo.rpc.MsgCallSaasSeatVO;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MsgCallSaasDelegateImpl implements IMsgCallSaasDelegate {

    @Autowired
    private MsgCallSaasClient msgCallSaasClient;

    @Autowired
    private ISeaAccountServiceDelegate seaAccountServiceDelegate;


    @Override
    public MsgCallSaasSeatVO querySeatInfo(String userId) {
        OpResult<SeaAdminUserInfoModel> userAccountsByMis = seaAccountServiceDelegate.getUserAccountsByMis(userId);
        if (userAccountsByMis.isFailOrNullData()) {
            return null;
        }
        SeaAdminUserInfoModel adminUserInfoModel = userAccountsByMis.getData();
        AdminUserAccountModel validUserAccountById = seaAccountServiceDelegate.getValidUserAccountById(adminUserInfoModel.getUserId());
        if (validUserAccountById == null) {
            return null;
        }
        String phoneSeatNum = validUserAccountById.getPhoneSeatNum();
        if (StringUtils.isBlank(phoneSeatNum)) {
            return null;
        }
        MsgResponse<MsgCallSaasSeatVO> msgCallSaasSeatResponse = msgCallSaasClient.querySeat(Long.parseLong(phoneSeatNum), "");
        if (msgCallSaasSeatResponse.getData() != null) {
            return msgCallSaasSeatResponse.getData();
        }
        return null;
    }
}
