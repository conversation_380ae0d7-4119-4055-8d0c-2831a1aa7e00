package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IVerifyCodeClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.baseservice.msg.v1.VerifyCodeClientV1;
import com.shuidihuzhu.msg.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2019/12/3 7:31 PM
 */
@Service
@Slf4j
public class VerifyCodeClientDelegate implements IVerifyCodeClientDelegate {
    @Autowired
    private VerifyCodeClientV1 verifyCodeClientV1;


    @Override
    public OpResult<Boolean> checkVerifyCodeIsSuccess(String mobile, String verifyCode, Long userId, String ip) {
        try{
            com.shuidihuzhu.msg.vo.Response response = verifyCodeClientV1.marketVerify(mobile, verifyCode, userId, ip);
            //只有当验证失败，且不是rpc调用失败时,才返回验证码错误
            if (!response.isSuccess() && response.getCode() != ErrorCode.SYSTEM_RPC_ERROR.getCode()){
                log.info("手机短信验证mobile:{},verifyCode:{},ip:{},response:{}", mobile, verifyCode, ip, response);
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_VERIFY_CODE_FAIL, response.getMsg());
            }else{
                return OpResult.createSucResult(true);
            }
        }catch (Exception e){
            log.error("verifyCodeClientV1.marketVerify error,mobile={},verifyCode={},userId={},ip={}",mobile,verifyCode,userId,ip,e);
            return OpResult.createSucResult(true);
        }
    }

    @Override
    public OpResult<Void> strictCheckVerifyCodeIsSuccess(String mobile, String verifyCode, Long userId, String clientIp) {
        try{
            com.shuidihuzhu.msg.vo.Response response = verifyCodeClientV1.marketVerify(mobile, verifyCode, userId, clientIp);
            //当验证失败 或者 rpc调用失败时,都返回验证码错误
            log.debug("VerifyCode_Response:{}",response);
            if (!response.isSuccess()){
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_VERIFY_CODE_FAIL);
            }else{
                return OpResult.createSucResult();
            }
        }catch (Exception e){
            log.error("verifyCodeClientV1.marketVerify error,mobile={},verifyCode={},userId={},ip={}",mobile,verifyCode,userId,clientIp,e);
            return OpResult.createFailResult(CfErrorCode.VOLUNTEER_VERIFY_CODE_FAIL);
        }
    }
}
