package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupDetailModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywx.UpdateExternalContactRemarkParam;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSimpleTrueOrFalseEnum;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewQyWxCorpDO;
import com.shuidihuzhu.client.model.qy.param.AddContactWayParam;
import com.shuidihuzhu.client.model.qy.response.AddContactWayResponse;
import com.shuidihuzhu.client.model.qy.response.CustomerAcquisitionResponse;
import com.shuidihuzhu.client.model.qy.response.DeleteLinkResponse;

import org.apache.commons.lang3.tuple.ImmutableTriple;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/9/12 4:01 PM
 */
public interface IQywxSdkDelegate {
    JSONObject getExternalContactResult(String accessToken, String externalUserid, CfClewQyWxCorpDO cfClewWxCorpMsgDO);

    String getUnionIdByExternalContactResult(JSONObject resultJsonObject);

    ImmutableTriple<String, String, String> getPhoneAndPassTimeByExternalContactResult(JSONObject resultJsonObject, String qyWechatUserId, Integer callBackId, String unionId);

    String getToken(CfSimpleTrueOrFalseEnum cfSimpleTrueOrFalseEnum);

    CfClewQyWxCorpDO getWxCorpMsgByCallbackId(int callbackId);

    List<CfClewQyWxCorpDO> listByCorpId(String corpId);

    String getAccessToken(CfSimpleTrueOrFalseEnum cfSimpleTrueOrFalseEnum, String corpId, String corpSecret);

    CfWorkImGroupDetailModel getGroupChatDetail(String chatId, CfClewQyWxCorpDO cfClewQyWxCorpDO);

    String getExternalUserNameByExternalContactResult(JSONObject resultJsonObject);

    String getTagsByExternalContactResult(JSONObject resultJsonObject, String userId);

    CfWorkImGroupInfoModel queryGroupInfo(Map<String, Object> param, CfClewQyWxCorpDO cfClewQyWxCorpDO);

    CfClewQyWxCorpDO getQyWxCorpSelfAppByCallbackId(int callbackId);

    AddContactWayResponse addContactWay(String cropId, String secret, AddContactWayParam param);

    CustomerAcquisitionResponse createLink(String cropId, String secret, List<String> userIds);

    DeleteLinkResponse deleteLink(String cropId, String secret, String linkId);

    void updateRemark(String cropId, String secret, UpdateExternalContactRemarkParam remarkParam);
}
