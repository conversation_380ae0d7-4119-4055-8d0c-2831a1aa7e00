package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.GrowthtoolCasePoolModelVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.HomeCaseRemindModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.HomeCaseRemindVo;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.client.performance.PepUserLotData;
import com.shuidihuzhu.cf.client.performance.calResult.*;
import com.shuidihuzhu.cf.client.performance.enums.BizType;
import com.shuidihuzhu.cf.client.performance.model.*;
import com.shuidihuzhu.cf.client.performance.param.PepUserLotParam;
import com.shuidihuzhu.cf.client.performance.query.*;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2022-07-28 21:08
 **/
public interface PepClientDelegate {

    UserCalResult performanceResult(PerformanceResultParam param);

    List<ProcedureForCModel> listAllProcedure(String userId, int bizType);

    List<ProcedureForCModel> listRecentProcedure(int bizType, int subBizType, int limit);

    ProcedureForCModel getByProduceId(long procedureId);

    List<Long> listLotId(LotQuery lotQuery);

    List<FactDetailModel> listFactCalDetail(PerformanceResultParam param);

    JSONObject getByLotData(String userId, long lotId, String lotUniqueValue);

    PepUserLotData listByLotIdAndUniqueValue(PepUserLotParam pepUserLotParam);

    LotInfo getLotInfo(long lotId);

    List<UserCalResult> teamUserResult(List<String> userIds, long procedureId);

    HomePerformanceBanner homePerformanceBanner(String userId);

    HomePerformanceBanner personPepBanner(String userId, long procedureId);

    HomeCaseRemindVo homeCaseRemind(String userId, int limit);

    List<GrowthtoolPepCaseScoreModel> caseProgress(PepCaseProgressParam param);

    GrowthtoolCasePoolModelVo personPepCasePool(String userId, int factBizType, int caseType);

    List<GrowthtoolUserCaseScoreModel> teamUserScore(PepTeamParam pepTeamParam);

    GrowthtoolCasePoolModel teamPepCasePool(PepTeamParam pepTeamParam);
}
