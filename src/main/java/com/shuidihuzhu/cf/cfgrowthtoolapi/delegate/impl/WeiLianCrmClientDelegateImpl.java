package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.weilian.IMGroupInviteInfosData;
import com.shuidihuzhu.cf.cfgrowthtoolapi.client.weilian.ImGroupInfosData;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IWeiLianCrmClientDelegate;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.Date;

@Component
@Slf4j
public class WeiLianCrmClientDelegateImpl implements IWeiLianCrmClientDelegate {

    private static String tenantId="f3c4337da98a4959b89545610babb845";
    private static OkHttpClient client;
    static {
        client = new OkHttpClient().newBuilder().build();
    }
    @Override
    public Response<ImGroupInfosData> getImGroupInfoList(Integer pageSize, Integer pageNo) {
        String timestamp = String.valueOf(System.currentTimeMillis()/1000);
        String token = getToken(timestamp);
        String uri = new StringBuilder()
                .append("http://121.37.161.19:7111/search-srv/im_group_views/page?")
                .append("tenantId="+tenantId+"&")
                .append("timestamp="+timestamp+"&")
                .append("token="+token)
                .toString();
        String jsonStr = "{" +
                "  \"filters\":{" +
                "    \"AND\": [" +
                "      {\"type\":{\"EQ\":\"1\"}}" +
                "    ]\n" +
                "  },\n" +
                "  \"pageSize\":"+pageSize+"," +
                "  \"pageNo\":1"+pageNo+"," +
                "  \"sort\": [{\"property\":\"ctime\", \"type\": \"DSC\"}]" +
                "}";
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType,jsonStr);
        Request request = new Request.Builder()
                .url(uri)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            okhttp3.Response response = client.newCall(request).execute();
            String responseStr = response.body().string();
            log.info("getImGroupInfoList timestamp:{},token:{},query:{},response:{}",timestamp,token,jsonStr,responseStr);
            Gson gson = new Gson();
            Type type = new TypeToken<Response<ImGroupInfosData>>(){}.getType();
            Response<ImGroupInfosData> imGroupInfosDataResponse = gson.fromJson(responseStr,type);
            return imGroupInfosDataResponse;
        } catch (Exception e) {
             log.info("getImGroupInfoList excpetion:{}",e);
        }
        return null;
    }

    @Override
    public Response<ImGroupInfosData> getImGroupInfoListByCTime(Integer pageSize, Integer pageNo, Date ctime){
        String timestamp = String.valueOf(System.currentTimeMillis()/1000);
        String token = getToken(timestamp);
        String uri = new StringBuilder()
                .append("http://121.37.161.19:7111/search-srv/im_group_views/page?")
                .append("tenantId="+tenantId+"&")
                .append("timestamp="+timestamp+"&")
                .append("token="+token)
                .toString();
        String jsonStr = "{\n" +
                "  \"filters\":{" +
                "    \"AND\": [" +
                "      {\"type\":{\"EQ\":\"1\"}," +
                "       \"ctime\":{\"GTE\":\""+String.valueOf(ctime.getTime())+"\"}" +
                "      }" +
                "    ]" +
                "  }," +
                "  \"pageSize\":"+pageSize+"," +
                "  \"pageNo\":"+pageNo+"," +
                "  \"sort\": [{\"property\":\"ctime\", \"type\": \"DSC\"}]" +
                "}";
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType,jsonStr);
        Request request = new Request.Builder()
                .url(uri)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            okhttp3.Response response = client.newCall(request).execute();
            String responseStr = response.body().string();
            log.info("getImGroupInfoList timestamp:{},token:{},query:{},response:{}",timestamp,token,jsonStr,responseStr);
            Gson gson = new Gson();
            Type type = new TypeToken<Response<ImGroupInfosData>>(){}.getType();
            Response<ImGroupInfosData> imGroupInfosDataResponse = gson.fromJson(responseStr,type);
            return imGroupInfosDataResponse;
        } catch (Exception e) {
            log.info("getImGroupInfoList excpetion:{}",e);
        }
        return null;
    }


    @Override
    public Response<IMGroupInviteInfosData> getImGroupInviteViews(Integer pageSize, Integer pageNo, String platformGid, Date ctime){
        String timestamp = String.valueOf(System.currentTimeMillis()/1000);
        String token = getToken(timestamp);
        String uri = new StringBuilder()
                .append("http://121.37.161.19:7111/search-srv/im_group_invite_views/page?")
                .append("tenantId="+tenantId+"&")
                .append("timestamp="+timestamp+"&")
                .append("token="+token)
                .toString();
        String jsonStr = "{" +
                "  \"filters\":{" +
                "    \"group.platformGid\":{\"EQ\":\""+platformGid+"\"}" +
                "  },\n" +
                "  \"pageSize\":"+pageSize+"," +
                "  \"pageNo\":"+pageNo+"," +
                "  \"sort\": [{\"property\":\"ctime\", \"type\": \"DSC\"}]" +
                "}";

        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType,jsonStr);
        Request request = new Request.Builder()
                .url(uri)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            okhttp3.Response response = client.newCall(request).execute();
            String responseStr = response.body().string();
            log.info("getImGroupInfoList timestamp:{},token:{},query:{},response:{}",timestamp,token,jsonStr,responseStr);
        } catch (Exception e) {
            log.info("getImGroupInfoList excpetion:{}",e);
        }
        return null;
    }

    @Override
    public Response<IMGroupInviteInfosData> getImGroupInviteViewsByCtime(Date ctime, Integer pageSize, Integer pageNo){
        String timestamp = String.valueOf(System.currentTimeMillis()/1000);
        String token = getToken(timestamp);
        String uri = new StringBuilder()
                .append("http://121.37.161.19:7111/search-srv/im_group_invite_views/page?")
                .append("tenantId="+tenantId+"&")
                .append("timestamp="+timestamp+"&")
                .append("token="+token)
                .toString();
        String jsonStr = "{" +
                "  \"filters\":{" +
                "    \"ctime\":{\"GTE\":\""+String.valueOf(ctime.getTime())+"\"}" +
                "  },\n" +
                "  \"pageSize\":"+pageSize+"," +
                "  \"pageNo\":"+pageNo+"," +
                "  \"sort\": [{\"property\":\"ctime\", \"type\": \"DSC\"}]" +
                "}";

        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType,jsonStr);
        Request request = new Request.Builder()
                .url(uri)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            okhttp3.Response response = client.newCall(request).execute();
            String responseStr = response.body().string();
            log.info("getImGroupInfoList timestamp:{},token:{},query:{},response:{}",timestamp,token,jsonStr,responseStr);
            Gson gson = new Gson();
            Type type = new TypeToken<Response<IMGroupInviteInfosData>>(){}.getType();
            Response<IMGroupInviteInfosData> imGroupInfosDataResponse = gson.fromJson(responseStr,type);
            return imGroupInfosDataResponse;
        } catch (Exception e) {
            log.info("getImGroupInfoList excpetion:{}",e);
        }
        return null;
    }

    public static String getToken(String timestamp){
        String secret = "t8ZkqcxvpXhw";
        String tenantId = "f3c4337da98a4959b89545610babb845";
        String token = MD5Util.getMD5HashValue(secret+timestamp+tenantId+secret);
        return token;
    }

    public static void main(String args[]){
        String timestamp = String.valueOf(System.currentTimeMillis()/1000);
        System.out.println("tenantId="+tenantId+"&timestamp="+timestamp+"&token="+getToken(timestamp));
//        IWeiLianCrmClientDelegate weiLianCrmClientDelegate = new WeiLianCrmClientDelegateImpl();
//        Response<ImGroupInfosData> response = weiLianCrmClientDelegate.getImGroupInfoList(10,1,new Date());
//        if(response.getCode()!=0){
//            return;
//        }
//        List<ImGroupInfoModel> dataList = response.getData().getContent();
//        for(ImGroupInfoModel groupInfoModel : dataList) {
//            String platGid = groupInfoModel.getGroup().getPlatformGid();
//            weiLianCrmClientDelegate.getImGroupInviteViews(10, 1,platGid, new Date());
//        }

//        IWeiLianCrmClientDelegate weiLianCrmClientDelegate = new WeiLianCrmClientDelegateImpl();
//        weiLianCrmClientDelegate.getImGroupInviteViewsByCtime( DateUtil.getStr2LDate2("yyyy-MM-dd","2019-01-01"),100,1);
    }
}
