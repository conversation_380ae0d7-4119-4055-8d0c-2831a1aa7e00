package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCash;
import com.shuidihuzhu.cf.finance.model.finance.drawcash.CfDrawCashApplyV2;
import com.shuidihuzhu.cf.finance.model.vo.CrowdfundingDonateInfoVo;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2020-01-08 21:28
 */
public interface ICfFinanceDelegate {

    // 查询案例提现审核状态
    CfDrawCash getByInfoUuid(String infoUuid);

    CfDrawCashApplyV2 getCaseApplyList(String infoUuid);

    //排除指定userId未退款金额
    CrowdfundingDonateInfoVo getDonateInfoExclusionAdviser(int caseId, List<Long> userIds);

    Map<Integer, CrowdfundingDonateInfoVo> getDonateCountExclusionMinAmount(List<Integer> caseIds, int minAmount);
}
