package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CfPropertyTransferHistoryVo;
import com.shuidihuzhu.cf.enums.crowdfunding.CfPropertyTransferEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/13  11:55
 */
public interface CfPropertyTransferHistoryDelegate {

    List<CfPropertyTransferHistoryVo> getListByBizTypeAndBizId(CfPropertyTransferEnum bizType, long bizId);

}
