package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.userplatform;

import com.google.common.collect.Lists;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.information.organization.client.OrgAppDepartmentClient;
import com.shuidihuzhu.information.organization.client.OrgAppEmployeeClient;
import com.shuidihuzhu.information.organization.model.org.standard.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2023-02-02 10:58
 **/
@Slf4j
@Service
public class OrgAppEmployeeDelegate {

    @Autowired
    private OrgAppEmployeeClient orgAppEmployeeClient;

    @Autowired
    private OrgAppDepartmentClient orgAppDepartmentClient;


    public FullEmployeeDetailVO getFullEmployeeDetailVO(String mis) {
        Response<List<FullEmployeeDetailVO>> response = orgAppEmployeeClient.employeeFullById(Lists.newArrayList(mis));
        log.debug("mis:{},result:{}", mis, response);
        if (response.ok() && CollectionUtils.isNotEmpty(response.getData())) {
            return response.getData().get(0);
        }
        return null;
    }

    /**
     * 获取psNumber
     */
    public EmployeeDetailVO getEmployeeDetailVO(String jobNum) {
        Response<EmployeeDetailVO> response = orgAppEmployeeClient.employeeById(jobNum);
        log.debug("jobNum:{},result:{}", jobNum, response);
        if (response.ok() && response.getData() != null) {
            return response.getData();
        }
        return null;
    }


    public String getOrgPath(String departmentId) {
        DepartmentVO department = getDepartment(departmentId);
        log.debug("jobNum:{},result:{}", departmentId, department);
        if (department != null) {
            return department.getPath();
        }
        return null;
    }

    /**
     * 通过psNumber获取对应的部门信息
     */
    public DepartmentVO getDepartment(String departmentId) {
        Response<DepartmentVO> departmentVOResponse = orgAppDepartmentClient.departmentById(departmentId);
        if (departmentVOResponse.ok() && departmentVOResponse.getData() != null) {
            return departmentVOResponse.getData();
        }
        return null;
    }

}
