package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IAppealWorkOrderDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrgConvertService;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.client.cf.riskadmin.AppealWorkOrderClient;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestFeedbackModel;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020/11/20 3:34 下午
 */
@Service
@Slf4j
public class AppealWorkOrderDelegate implements IAppealWorkOrderDelegate {
    @Autowired
    private AppealWorkOrderClient appealWorkOrderClient;
    @Autowired
    private ICrmOrgConvertService crmOrgConvertService;

    @Override
    public void normalQualitySpotAppeal(CfGwReplaceInputQualityTestFeedbackModel feedbackModel){
        List<BdCrmVolunteerOrgnizationSimpleModel> leaderOrgList = crmOrgConvertService.getBdCrmVolunteerOrgnizationSimpleModelByMisList(Lists.newArrayList(feedbackModel.getLeaderMis()));
        List<BdCrmVolunteerOrgnizationSimpleModel> gwOrgList = crmOrgConvertService.getByUniqueCodeList(Lists.newArrayList(feedbackModel.getMis()));
        feedbackModel.setName(CollectionUtils.isNotEmpty(gwOrgList)?gwOrgList.get(0).getOrgName()+" "+feedbackModel.getName():feedbackModel.getName());
        feedbackModel.setLeaderName(CollectionUtils.isNotEmpty(leaderOrgList)?leaderOrgList.get(0).getOrgName()+" "+feedbackModel.getLeaderName():feedbackModel.getLeaderName());
        Response<Void> response = appealWorkOrderClient.normalQualitySpotAppeal(feedbackModel);
        log.info("normalQualitySpotAppeal param:{} result:{}",feedbackModel,response);
    }
    @Override
    public void managerQualitySpotAppeal(CfGwReplaceInputQualityTestFeedbackModel feedbackModel){
        List<BdCrmVolunteerOrgnizationSimpleModel> gwOrgList = crmOrgConvertService.getByUniqueCodeList(Lists.newArrayList(feedbackModel.getMis()));
        feedbackModel.setName(CollectionUtils.isNotEmpty(gwOrgList)?gwOrgList.get(0).getOrgName()+" "+feedbackModel.getName():feedbackModel.getName());
        Response<Void> response = appealWorkOrderClient.managerQualitySpotAppeal(feedbackModel);
        log.info("managerQualitySpotAppeal param:{} result:{}",feedbackModel,response);
    }
}
