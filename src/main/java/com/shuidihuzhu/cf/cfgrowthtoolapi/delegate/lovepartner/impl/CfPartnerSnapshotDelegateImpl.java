package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.lovepartner.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.lovepartner.ICfPartnerSnapshotDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerCycleDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerOrgMemberSnapshotDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerSnapshotDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfBdCrmOrgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerAttendInfoDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-09-06
 */

@Service
@Slf4j
public class CfPartnerSnapshotDelegateImpl implements ICfPartnerSnapshotDelegate {

    @Autowired
    private CfPartnerCycleService partnerCycleService;

    @Autowired
    private CfPartnerInfoService partnerInfoService;

    @Autowired
    private CfPartnerSnapshotService cfPartnerSnapshotService;

    @Autowired
    private CfPartnerOrgMemberSnapshotService orgMemberSnapshotService;

    @Resource(name = "selfBuiltOrgForSea")
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private ICfVolunteerService cfVolunteerServiceImpl;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private PartnerAttendInfoService attendInfoService;

    @Autowired
    private CfPartnerCaseRelationService caseRelationService;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private CfPartnerCaseBillService partnerCaseBillService;

    @Autowired
    private CfPartnerAttendBillService partnerAttendBillService;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;

    @Autowired
    private ApolloService apolloService;

    @Override
    public CfPartnerCycleDo partnerCycle(Date startTime,Date endTime, Date caseStartTime, Date caseEndTime, String name) {
        CfPartnerCycleDo partnerCycleDo = CfPartnerCycleDo.builder()
                .year(DateUtil.year(startTime))
                .name(name)
                .startTime(startTime)
                .endTime(endTime)
                .caseStartTime(caseStartTime)
                .caseEndTime(caseEndTime)
                .operatorUserId(0L)
                .operatorUserName("SYSTEM")
                .build();
        //如果当期已经生成不需要重复生成
        Optional<CfPartnerCycleDo> cycleDoOptional = partnerCycleService.getCycleList()
                .stream()
                .filter(item -> Objects.equals(item.getName(), name) && Objects.equals(item.getYear(), DateUtil.year(startTime)))
                .findFirst();
        if (cycleDoOptional.isPresent()) {
            log.info("已经生成了改周期内的信息");
            return cycleDoOptional.get();
        }
        partnerCycleService.insertPartnerCycle(partnerCycleDo);
        return partnerCycleDo;
    }

    @Override
    public OpResult<Void> partnerSnapshot(CfPartnerCycleDo cfPartnerCycleDo) {
        //查看当期内的人员信息,如果已经存在直接标记删除
        int affectRow = cfPartnerSnapshotService.deleteByCycleId(cfPartnerCycleDo.getId());
        if (affectRow > 0) {
            log.info("之前存在快照人员,直接删除更新为新的人员");
        }
        List<CfPartnerInfoDo> partnerInfoList = partnerInfoService.listAllValidPartner();
        List<CfPartnerSnapshotDo> snapshotDoList = partnerInfoList.stream()
                .filter(item -> isGenerateSnapshot(item,cfPartnerCycleDo))
                .filter(item -> item.getAccountType() != PartnerTypeEnum.MEDICAL_SOCIAL_WORKER.getCode())
                .map(item -> {
                    ICrmMemberInfoService.BdCrmOrganizationDOWithChain rightBdCaseOrg = crmMemberInfoService.getRightBdCaseOrg(item.getLeaderUniqueCode());
                    if (rightBdCaseOrg == null) {
                        //AlarmBotService.sentText("a7021cd6-ea5b-4ba2-8d74-3ecc09bbb2d3", "顾问" + item.getLeaderName() + "离职无法生成对应的兼职人员账单", null, null);
                        return null;
                    }
                    return CfPartnerSnapshotDo.create(rightBdCaseOrg, item, cfPartnerCycleDo);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //if (snapshotDoList.stream().anyMatch(Objects::isNull)) {
        //    log.info("由于存在顾问离职先不生成对应的账单等信息");
        //    return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
        //}
        List<List<CfPartnerSnapshotDo>> listList = Lists.partition(snapshotDoList, 100);
        for (List<CfPartnerSnapshotDo> partnerSnapshotDoList : listList) {
            cfPartnerSnapshotService.batchInsert(partnerSnapshotDoList);
        }
        return OpResult.createSucResult(null);
    }

    /**
     * 判断是否可以生成snapshot
     * @param partnerInfoDo
     * @param cfPartnerCycleDo
     * @return
     */
    private boolean isGenerateSnapshot(CfPartnerInfoDo partnerInfoDo,CfPartnerCycleDo cfPartnerCycleDo){
        WorkStatusEnum workStatusEnum = WorkStatusEnum.parseEnum(partnerInfoDo.getWorkStatus());
        if (Objects.isNull(workStatusEnum)) {
            return false;
        }
        boolean result = false;
        switch (workStatusEnum) {
            case ON_OFFICE:
            case TO_BE_HIRED:
                result = true;
                break;
            case LEAVE_OFFICE:
                if (Objects.nonNull(partnerInfoDo.getLeaveTime()) && partnerInfoDo.getLeaveTime().after(cfPartnerCycleDo.getStartTime()) && partnerInfoDo.getLeaveTime().before(cfPartnerCycleDo.getEndTime())) {
                    result = true;
                }
                break;
            default:
                break;
        }
        return result;
    }

    @Override
    public void partnerOrgMemberSnapshot(CfPartnerCycleDo cfPartnerCycleDo) {
        try{
            //获取所有的城市
            List<BdCrmOrganizationDO> allOrg = crmSelfBuiltOrgReadService.getAllOrg();
            // orgId 映射 orgPath
            Map<Long, String> orgIdMapOrgPath = crmSelfBuiltOrgReadService.listChainByOrgIds(allOrg.stream().map(BdCrmOrganizationDO::getId).distinct().collect(Collectors.toList()), "-");
            // orgId 映射 组织基本信息
            Map<Long, CfBdCrmOrgModel> orgIdMapOrgInfo = allOrg.stream()
                    .map(bdCrmOrganizationDO -> new CfBdCrmOrgModel(bdCrmOrganizationDO.getId(), bdCrmOrganizationDO.getParentId(), bdCrmOrganizationDO.getOrgName(), Optional.ofNullable(orgIdMapOrgPath.get(bdCrmOrganizationDO.getId())).orElse(""), bdCrmOrganizationDO.getOrgAttribute()))
                    .collect(Collectors.toMap(CfBdCrmOrgModel::getOrgId, Function.identity()));
            // 同步 组织信息 到每个目标周期快照
            orgMemberSnapshotService.batchInsertOrgSnapshot(Lists.newArrayList(orgIdMapOrgInfo.values()), cfPartnerCycleDo.getId());
            // 同步 人员快照 到每个目标周期快照
            syncMemberSnapshot(orgIdMapOrgInfo, cfPartnerCycleDo.getId());
        }catch (Exception e) {
            log.error("syncObjectiveOrgMemberSnapshot err:", e);
        }
    }

    private List<CfPartnerOrgMemberSnapshotDo> syncMemberSnapshot(Map<Long, CfBdCrmOrgModel> orgIdMapOrgInfo, Long cycleId) {
        List<CfPartnerOrgMemberSnapshotDo> commonGwMemberSnapshot = Lists.newArrayList();
        long maxId = 0;
        List<BdCrmOrgUserRelationDO> userRelationList = crmOrganizationRelationService.listAllValidRelation(maxId, 100);
        while (CollectionUtils.isNotEmpty(userRelationList)) {
            List<String> uniqueCodeList = userRelationList.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).collect(Collectors.toList());
            Map<String, CrowdfundingVolunteer> uniqueCodeMapVolunteer = cfVolunteerServiceImpl.getCfVolunteerDOByUniqueCodes(uniqueCodeList).stream().collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity(), (oldObj, newObj)->newObj));
            // 过滤掉  超管 以及总部运营
            List<CfPartnerOrgMemberSnapshotDo> orgMemberSnapshotList = userRelationList.stream()
                    .map(userRelation -> CfPartnerOrgMemberSnapshotDo.convertInsertDbMemberModel(uniqueCodeMapVolunteer.get(userRelation.getUniqueCode()), orgIdMapOrgInfo.get(userRelation.getOrgId()), cycleId))
                    .filter(Objects::nonNull)
                    .filter(orgMemberSnapshot -> orgMemberSnapshot.getLevel()!= CrowdfundingVolunteerEnum.RoleEnum.SUPER_LEADER.getLevel() || orgMemberSnapshot.getLevel()!=CrowdfundingVolunteerEnum.RoleEnum.OPERATOR.getLevel())
                    .filter(GrowthtoolUtil.distinctByKey(CfPartnerOrgMemberSnapshotDo::showUniqueKey))
                    .collect(Collectors.toList());
            commonGwMemberSnapshot.addAll(orgMemberSnapshotList.stream().filter(memberSnapshot -> memberSnapshot.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel()).collect(Collectors.toList()));
            orgMemberSnapshotService.batchInsertMemberSnapshot(orgMemberSnapshotList, cycleId);
            maxId = userRelationList.stream().map(BdCrmOrgUserRelationDO::getId).max(Long::compare).get();
            userRelationList = crmOrganizationRelationService.listAllValidRelation(maxId, 100);
        }
        return commonGwMemberSnapshot;
    }

    @Override
    public void partnerCaseBill(CfPartnerCycleDo cfPartnerCycleDo) {
        List<CfPartnerSnapshotDo> casePartnerSnapshotList = cfPartnerSnapshotService.listByCycleId(cfPartnerCycleDo.getId())
                .stream()
                .filter(item -> PartnerEnums.isCaseSettleType(item.getSettleType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(casePartnerSnapshotList)){
            //TODO alarmPartner
            return;
        }
        List<Integer> caseIdList = caseRelationService.listCaseRelationWithCaseTimeAndUniqueCode(cfPartnerCycleDo.getCaseStartTime(),cfPartnerCycleDo.getCaseEndTime(),casePartnerSnapshotList.stream().map(CfPartnerSnapshotDo::getUniqueCode).collect(Collectors.toList()), PartnerEnums.CaseCalcEnums.NEED_CALC);
        Map<String,List<CfBdCaseInfoDo>> caseMapByUniqueCode = cfBdCaseInfoService.listCaseInfoByCaseIdsAndAmountAndDonate(caseIdList, apolloService.getValidAmount(),apolloService.getValidDonateNum())
                .stream()
                .collect(Collectors.groupingBy(CfBdCaseInfoDo::getPartnerUniqueCode));
        for (CfPartnerSnapshotDo partnerSnapshotDo : casePartnerSnapshotList){
            List<CfBdCaseInfoDo> bdCaseInfoList = caseMapByUniqueCode.get(partnerSnapshotDo.getUniqueCode());
            partnerCaseBillService.batchInsert(bdCaseInfoList,partnerSnapshotDo,cfPartnerCycleDo);
        }

    }

    @Override
    public void partnerAttendBill(CfPartnerCycleDo cfPartnerCycleDo) {
        List<CfPartnerSnapshotDo> attendPartnerSnapshotList = cfPartnerSnapshotService.listByCycleId(cfPartnerCycleDo.getId())
                .stream()
                .filter(item -> PartnerEnums.isAttendSettleType(item.getSettleType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(attendPartnerSnapshotList)){
            //TODO alarmPartner
            return;
        }
        //兼职顾问Map
        Map<String,List<PartnerAttendInfoDTO>> attendMapByUniqueCode = attendInfoService.listAttendInfoWithCycleTimeAndUniqueCode(
                cfPartnerCycleDo.getStartTime(),cfPartnerCycleDo.getEndTime(),attendPartnerSnapshotList.stream().map(CfPartnerSnapshotDo::getUniqueCode).collect(Collectors.toList()))
                .stream()
                .filter(item -> item.getApproveStatus() == ApproveStatusEnum.APPROVE.getCode())
                .collect(Collectors.groupingBy(PartnerAttendInfoDTO::getUniqueCode));

        for (CfPartnerSnapshotDo cfPartnerSnapshotDo : attendPartnerSnapshotList) {
            List<PartnerAttendInfoDTO> list = attendMapByUniqueCode.get(cfPartnerSnapshotDo.getUniqueCode());
            partnerAttendBillService.batchInsert(list, cfPartnerSnapshotDo, cfPartnerCycleDo);
        }
    }
}
