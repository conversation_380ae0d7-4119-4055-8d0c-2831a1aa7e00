package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.BaseGdMapModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.gdmap.GdMapPoiModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.gdmap.GdMapRegeoResult;
import org.apache.commons.math3.util.Pair;

import java.util.List;
import java.util.Map;

public interface IGdMapPoiDelegate {

    BaseGdMapModel<List<GdMapPoiModel>> getHospitalPoiByNameAndCity(String hospitalName, String cityName,int pageSize);

	BaseGdMapModel<List<GdMapPoiModel>> listRecommendPlace(String keyWork, String city, int pageSize);

	/**
	 * 根据经纬度获取位置信息
	 */
	GdMapRegeoResult getByGps(String longitude, String latitude);

	GdMapPoiModel getPoiByKeyword(String longitude, String latitude);

	String gpsConvertGdGps(String longitude, String latitude);

	String getCityNameByLocation(String location);

	String getLoctaionByCityAndAddress(String address,String city);

    Pair<String,Object> getProvinceAndCity(String address, String city);
}
