package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMarketDataClientDelegete;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfMarketRatioCityModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.repository.CfCompetitionBaseDataRepository;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.risk.client.gatherInformation.CfMarketDataClient;
import com.shuidihuzhu.cf.risk.model.gatherInformation.CfMarketDataVO;
import com.shuidihuzhu.cf.risk.model.gatherInformation.market.CfMarketDataQueryParam;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020-07-05
 */
@Service
@Slf4j
public class CfMarketDataClientDelegete implements ICfMarketDataClientDelegete {

    @Autowired
    private CfMarketDataClient cfMarketDataClient;
    @Autowired
    private CfCompetitionBaseDataRepository cfCompetitionBaseDataRepository;
    @Autowired
    private ApolloService apolloService;

    @Override
    public OpResult<List<CfMarketDataVO>> queryDetailMarketData(CfMarketDataQueryParam cfMarketDataQueryParam,List<Integer> queryCityIds) {
        if(!apolloService.getQscDataSwith()) {
            try {
                Response<List<CfMarketDataVO>> response = cfMarketDataClient.queryDetailMarketData(cfMarketDataQueryParam);
                log.debug("queryDetailMarketData_response:{},{}", JSONObject.toJSONString(cfMarketDataQueryParam), JSONObject.toJSONString(response));
                if (response.ok()) {
                    return OpResult.createSucResult(response.getData());
                } else {
                    return OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
                }
            } catch (Exception e) {
                log.error("queryDetailMarketData error", e);
            }
            return OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        }else{
            if(CollectionUtils.isEmpty(queryCityIds)){
                return OpResult.createSucResult(Lists.newArrayList());
            }
            List<CfMarketRatioCityModel> retList = cfCompetitionBaseDataRepository.getMarketByDateWithCityIds(cfMarketDataQueryParam.getStartTime(),
                    cfMarketDataQueryParam.getEndTime(),queryCityIds);
            if(CollectionUtils.isEmpty(retList)){
                return OpResult.createSucResult(Lists.newArrayList());
            }
            List<CfMarketDataVO> responseList = Lists.newArrayList();
            for(CfMarketRatioCityModel cityModel : retList){
                CfMarketDataVO vo = new CfMarketDataVO();
                vo.setDateTime(cityModel.getThedate());
                if(cityModel.getQscCaseNum()!=null) {
                    vo.setMarketCount(Float.valueOf(cityModel.getQscCaseNum()).intValue());
                }
                responseList.add(vo);
            }
            return OpResult.createSucResult(responseList);
        }
    }

    @Override
    public OpResult<Map<Long, CfMarketDataVO>> queryCollectMarketData(CfMarketDataQueryParam cfMarketDataQueryParam,Map<Long, Set<Integer>> subOrgActCityMap) {
        if(!apolloService.getQscDataSwith()) {
            try {
                Response<Map<Long, CfMarketDataVO>> response = cfMarketDataClient.queryCollectMarketData(cfMarketDataQueryParam);
                log.debug("queryCollectMarketData_response:{},{}", JSONObject.toJSONString(cfMarketDataQueryParam),
                        JSONObject.toJSONString(response));
                if (response.ok()) {
                    return OpResult.createSucResult(response.getData());
                } else {
                    return OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
                }
            } catch (Exception e) {
                log.error("queryCollectMarketData error", e);
            }
            return OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        }else{
            if(subOrgActCityMap.size()==0){
                return OpResult.createSucResult(Maps.newConcurrentMap());
            }
            Map<Long,CfMarketDataVO> cfMarketDataVOMap = Maps.newHashMap();
            for(Map.Entry<Long,Set<Integer>> entry : subOrgActCityMap.entrySet()){
                Long key = entry.getKey();
                Set<Integer> value = entry.getValue();
                if(CollectionUtils.isEmpty(value)){
                    continue;
                }
                Float count = cfCompetitionBaseDataRepository.getMarketCountByDateWithCity(cfMarketDataQueryParam.getStartTime(),cfMarketDataQueryParam.getEndTime(),value);
                if(count==null){
                    continue;
                }
                CfMarketDataVO marketDataVO = new CfMarketDataVO();
                marketDataVO.setMarketCount(count.intValue());
                cfMarketDataVOMap.put(key,marketDataVO);
            }
            return OpResult.createSucResult(cfMarketDataVOMap);
        }
    }
}
