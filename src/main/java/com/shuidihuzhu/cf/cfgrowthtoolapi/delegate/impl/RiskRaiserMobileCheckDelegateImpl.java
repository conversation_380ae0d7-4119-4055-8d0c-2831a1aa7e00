package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.RiskRaiserMobileCheckDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IClewPreproseMaterialService;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.risk.client.risk.RiskRaiserMobileIsInnerStaffFeignClient;
import com.shuidihuzhu.cf.risk.model.risk.RiskRaiserMobileCheckParam;
import com.shuidihuzhu.cf.risk.model.risk.RiskRaiserMobileCheckResult;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2021-11-30 9:31 下午
 **/
@Slf4j
@Service
public class RiskRaiserMobileCheckDelegateImpl implements RiskRaiserMobileCheckDelegate {

    @Autowired
    private RiskRaiserMobileIsInnerStaffFeignClient staffFeignClient;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;


    @Autowired
    private IClewPreproseMaterialService clewPreproseMaterialService;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;


    @Override
    public boolean checkWhenSavePrepose(PreposeMaterialModel.MaterialInfoVo materialInfoVo, CrowdfundingVolunteer cfVolunteer, String clewUser) {
        RiskRaiserMobileCheckParam checkParam = new RiskRaiserMobileCheckParam();
        checkParam.setEncryptRaiserMobile(oldShuidiCipher.aesEncrypt(materialInfoVo.getRaiseMobile()));
        checkParam.setRaiserName(materialInfoVo.getRaiseName());
        checkParam.setClewId(String.valueOf(materialInfoVo.getId()));
        if (cfVolunteer != null) {
            checkParam.setVolunteerName(cfVolunteer.getVolunteerName());
            checkParam.setChannel(RiskRaiserMobileCheckParam.Channel.DAI_LU_RU_BD);
        } else {
            checkParam.setVolunteerName(clewUser);
            checkParam.setChannel(RiskRaiserMobileCheckParam.Channel.DAI_LU_RU_1V1);
        }
        return riskMobileCheck(checkParam);
    }

    @Override
    public boolean checkWhenConfirm(PreposeMaterialModel.MaterialInfoVo materialInfoVo, long userId) {
        ClewCrowdfundingReportRelation reportRelation = clewPreproseMaterialService.getByPreposeMaterialId(materialInfoVo.getId());
        if (reportRelation == null) {
            log.info("没有找到对应的代录入信息");
            return false;
        }
        String cryptoMobile = crowdFundingFeignDelegate.getCryptoMobileByUserId(userId);
        RiskRaiserMobileCheckParam checkParam = new RiskRaiserMobileCheckParam();
        if (StringUtils.isNotBlank(cryptoMobile)) {
            checkParam.setEncryptRaiserMobile(cryptoMobile);
        }
        checkParam.setRaiserName(materialInfoVo.getRaiseName());
        checkParam.setClewId(String.valueOf(materialInfoVo.getId()));
        checkParam.setVolunteerName(reportRelation.getVolunteerName());
        checkParam.setChannel(RiskRaiserMobileCheckParam.Channel.CONFIRM_LINK);

        return riskMobileCheck(checkParam);
    }

    @Override
    public boolean riskMobileCheck(RiskRaiserMobileCheckParam raiserMobileCheckParam) {
        try {
            Response<RiskRaiserMobileCheckResult> check = staffFeignClient.check(raiserMobileCheckParam);
            log.info("riskMobileCheck param:{},result:{}", raiserMobileCheckParam, check);
            if (check.notOk() && check.getData() == null) {
                return false;
            }
            return check.getData().getHit();
        } catch (Exception e) {
            log.error("riskMobileCheck调用失败", e);
            return false;
        }
    }
}
