package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfPartnerCaseAttributeDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.LeaderPermissionInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerCaseRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerClewRecordService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerCaseRelationDo;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerClewRecordDo;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-09-02
 */

@Service
@Slf4j
public class CfPartnerCaseAttributeDelegateImpl implements ICfPartnerCaseAttributeDelegate {

    @Autowired
    private CfPartnerCaseRelationService caseRelationService;

    @Autowired
    private CfPartnerClewRecordService clewRecordService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Override
    public ImmutablePair<String, Boolean> getAttributePartnerByCaseId(Integer caseId, String uniqueCode, String raiserPhone) {
        //先获取案例关联记录
        if (Objects.nonNull(caseId) && caseId > 0) {
            CfPartnerCaseRelationDo cfPartnerCaseRelationDo = caseRelationService.getCaseRelationByCaseId(caseId);
            if (Objects.nonNull(cfPartnerCaseRelationDo)) {
                return ImmutablePair.of(cfPartnerCaseRelationDo.getUniqueCode(), false);
            }
        }
        //根据顾问和发起人手机号找兼职录入的线索
        if (StringUtils.isNotBlank(uniqueCode) && StringUtils.isNotBlank(raiserPhone)) {
            Date endTime = DateUtil.nowDate();
            Date startTime = DateUtils.addHours(endTime, -72);
            List<CfPartnerClewRecordDo> list = clewRecordService.listPartnerClewByEncryptPhoneWithTime(raiserPhone, startTime, endTime);
            if (CollectionUtils.isEmpty(list)) {
                return ImmutablePair.of(null, false);
            } else {
                CfPartnerClewRecordDo cfPartnerClewRecordDo = list.stream().min(Comparator.comparing(CfPartnerClewRecordDo::getCreateTime)).get();
                //判断是否存在上下级关系
                String leaderUniqueCode = cfPartnerClewRecordDo.getLeaderUniqueCode();
                if (Objects.equals(uniqueCode, leaderUniqueCode)) {
                    return ImmutablePair.of(cfPartnerClewRecordDo.getUniqueCode(), true);
                }
                if (StringUtils.isBlank(leaderUniqueCode)) {
                    return ImmutablePair.of(null, false);
                }
                CrowdfundingVolunteer leaderVolunteer = cfVolunteerService.getVolunteerByUniqueCode(uniqueCode);
                LeaderPermissionInfo permission = memberInfoService.getLeaderPermissionByVolunteer(leaderVolunteer);
                if (permission.getPermissionUniqueCodes() != null && permission.getPermissionUniqueCodes().contains(uniqueCode)) {
                    log.info("上级爱心伙伴线索,raiserPhone:{}", raiserPhone);
                    return ImmutablePair.of(cfPartnerClewRecordDo.getUniqueCode(), true);
                }
            }
        }
        return ImmutablePair.of(null, false);
    }
}
