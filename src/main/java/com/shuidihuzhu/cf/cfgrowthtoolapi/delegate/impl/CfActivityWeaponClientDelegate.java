package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfActivityWeaponClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.ActivityJoinModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponAccessModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.ICfWeaponBudgetService;
import com.shuidihuzhu.cf.client.apipure.feign.CfActivityWeaponFeignClient;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.dao.CrowdfundingVolunteerDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.charity.client.api.BudgetManagementClient;
import com.shuidihuzhu.charity.client.api.ThemeActivityClient;
import com.shuidihuzhu.charity.client.model.activity.theme.ThemeActivityDetail;
import com.shuidihuzhu.charity.client.model.activity.theme.ThemeActivityInfo;
import com.shuidihuzhu.charity.client.vo.budgetmanagement.BudgetManagementVo;
import com.shuidihuzhu.client.cf.growthtool.enums.WeaponActivityTypeEnum;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020-10-16
 */

@Service
@Slf4j
public class CfActivityWeaponClientDelegate implements ICfActivityWeaponClientDelegate {

    @Autowired
    private CfActivityWeaponFeignClient cfActivityWeaponFeignClient;
    @Autowired
    private ICfWeaponBudgetService budgetService;
    @Autowired
    private ThemeActivityClient themeActivityClient;
    @Autowired
    private CrowdfundingVolunteerDao crowdfundingVolunteerDao;
    @Autowired
    private BudgetManagementClient budgetManagementClient;


    @Override
    public OpResult<WeaponAccessModel> checkCaseAllowJoin(int caseId, int activityClass, Integer hugeSubsidyType, int serviceChargeId, int activityId) {
        if (caseId <= 0) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CF_INFO_NOT_VALID);
        }
        WeaponAccessModel weaponAccessModel = new WeaponAccessModel();
        if (hugeSubsidyType <= 0) {
            hugeSubsidyType = null;
        }
        //需要把爆款案例转化为对应的hugeSubsidyType
        if (activityClass == WeaponActivityTypeEnum.HOT_CASE.getCode()) {
            activityClass = WeaponActivityTypeEnum.ACTIVITY_DONATE.getCode();
            hugeSubsidyType = WeaponEnums.HugeSubsidyTypeEnum.level_3000_1.getCode();
        }
        if (activityClass == WeaponActivityTypeEnum.HIGH_POTENTIAL.getCode()) {
            activityClass = WeaponActivityTypeEnum.ACTIVITY_DONATE.getCode();
            hugeSubsidyType = WeaponEnums.HugeSubsidyTypeEnum.level_3000_2.getCode();
        }
        try{
            OperationResult<Boolean> operationResult = cfActivityWeaponFeignClient.checkActivityAllowJoin(caseId, activityClass, DateTime.now().getMillis(), hugeSubsidyType, serviceChargeId, activityId);
            log.debug("checkCaseAllowJoin caseId:{},activityClass:{},response:{}", caseId, activityClass, operationResult);
            return getWeaponAccessModelOpResult(weaponAccessModel, operationResult);
        }catch (Exception e){
            log.error("checkCaseAllowJoin error",e);
        }
        weaponAccessModel.setSuccess(false);
        weaponAccessModel.setFailDesc(CfGrowthtoolErrorCode.FEIGN_TIMEOUT.getMsg());
        return OpResult.createSucResult(weaponAccessModel);
    }


    @Override
    public OpResult<WeaponAccessModel> requireJoinActivity(ActivityJoinModel activityJoinModel) {
        int caseId = activityJoinModel.getCaseId();
        if (caseId <= 0) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CF_INFO_NOT_VALID);
        }
        WeaponAccessModel weaponAccessModel = new WeaponAccessModel();
        try {
            //预算无效
            weaponAccessModel = budgetService.checkValidBudget(activityJoinModel.getBudgetId());
            if (!weaponAccessModel.isSuccess()) {
                return OpResult.createSucResult(weaponAccessModel);
            }
            Integer hugeSubsidyType = activityJoinModel.getHugeSubsidyType();
            int activityType = activityJoinModel.getActivityType();
            if (hugeSubsidyType <= 0) {
                hugeSubsidyType = null;
            }
            //需要把爆款案例转化为对应的hugeSubsidyType
            if (activityType == WeaponActivityTypeEnum.HOT_CASE.getCode()) {
                activityType = WeaponActivityTypeEnum.ACTIVITY_DONATE.getCode();
                hugeSubsidyType = WeaponEnums.HugeSubsidyTypeEnum.level_3000_1.getCode();
            }
            if (activityType == WeaponActivityTypeEnum.HIGH_POTENTIAL.getCode()) {
                activityType = WeaponActivityTypeEnum.ACTIVITY_DONATE.getCode();
                hugeSubsidyType = WeaponEnums.HugeSubsidyTypeEnum.level_3000_2.getCode();

            }
            long userId = 0;
            String uniqueCode = activityJoinModel.getUniqueCode();
            if (StringUtils.isNotBlank(uniqueCode)) {
                CrowdfundingVolunteer crowdfundingVolunteerInfo = crowdfundingVolunteerDao.getByUniqueCode(uniqueCode);
                userId = crowdfundingVolunteerInfo.getId();
            }
            OperationResult<Boolean> operationResult = cfActivityWeaponFeignClient.requireJoinActivity(caseId, activityType, activityJoinModel.getApplyTime(), hugeSubsidyType, userId, activityJoinModel.getRemoveConfigId(), activityJoinModel.getActivityId(), activityJoinModel.getWeaponApplyId());
            log.info("调用活动方接口param caseId:{}, activityClass:{},result:{}", caseId, activityType, operationResult);
            if (operationResult.isSuccess()) {
                return getWeaponAccessModelOpResult(weaponAccessModel, operationResult);
            }
        }catch (Exception e){
            log.error("requireJoinActivity error", e);
        }
        weaponAccessModel.setSuccess(false);
        weaponAccessModel.setFailDesc(CfGrowthtoolErrorCode.FEIGN_TIMEOUT.getMsg());
        return OpResult.createSucResult(weaponAccessModel);
    }

    @Override
    public String getActivityName(int caseId, int activityClass) {

        if (caseId <= 0) {
            return "";
        }

        try {
            OperationResult<String> operationResult = cfActivityWeaponFeignClient.getJoinLoveHomeActivityName(caseId, activityClass);
            log.info("调用活动方接口param caseId:{}, activityClass:{},result:{}", caseId, activityClass, operationResult);
            if (!operationResult.isSuccess()) {
                return "";
            }
            return operationResult.getData() == null ? "" : operationResult.getData();
        } catch (Exception e) {
            log.error("getJoinLoveHomeActivityName error", e);
        }
        return "";
    }

    @Override
    public List<ThemeActivityDetail> getSimpleDetailList() {
        Response<List<ThemeActivityDetail>> simpleDetailList = themeActivityClient.getSimpleDetailList();
        log.info("simpleDetailList:{}", simpleDetailList);
        if (simpleDetailList.notOk() || simpleDetailList.getData() == null) {
            return Lists.newArrayList();
        }
        return simpleDetailList.getData();
    }

    @Override
    public ThemeActivityDetail getSimpleDetail(Integer activityId) {
        if (activityId == null) {
            return null;
        }
        Response<ThemeActivityDetail> simpleDetail = themeActivityClient.getSimpleDetail(activityId);
        log.info("simpleDetail:{}", simpleDetail);
        if (simpleDetail.notOk() || simpleDetail.getData() == null) {
            return null;
        }
        return simpleDetail.getData();
    }

    @Override
    public List<ThemeActivityInfo> getThemeActivityInfoList() {

        return Optional.ofNullable(themeActivityClient.getThemeActivityList())
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
    }

    @Override
    public List<BudgetManagementVo> getBudgetManagementList() {
        return Optional.ofNullable(budgetManagementClient.getList())
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(Lists.newArrayList());
    }

    private OpResult<WeaponAccessModel> getWeaponAccessModelOpResult(WeaponAccessModel weaponAccessModel, OperationResult<Boolean> operationResult) {
        if (Objects.isNull(operationResult)) {
            weaponAccessModel.setSuccess(false);
            weaponAccessModel.setFailDesc(CfGrowthtoolErrorCode.FEIGN_TIMEOUT.getMsg());
            return OpResult.createSucResult(weaponAccessModel);
        }
        if (operationResult.isSuccess()) {
            weaponAccessModel.setSuccess(operationResult.getData());
            if (!operationResult.getData()) {
                weaponAccessModel.setFailDesc(operationResult.getMsg());
            }
        }
        return OpResult.createSucResult(weaponAccessModel);
    }
}
