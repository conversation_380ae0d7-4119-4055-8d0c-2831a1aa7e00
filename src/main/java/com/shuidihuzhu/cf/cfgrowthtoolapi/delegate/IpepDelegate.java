package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiBdScoreDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKPICommissionAwardCollectModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CustomPerformanceScoreModel;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2022-06-15 16:35
 **/
public interface IpepDelegate {

    void syncScoreData(String userId, List<CustomPerformanceScoreModel> customPerformanceScoreModels, CfKPICommissionAwardCollectModel awardAmounts);

    void syncScoreData(List<CfKpiBdScoreDO> cfKpiBdScoreDOS);

    void syncScoreDataByLot();

    void syncScoreDataByLot(long lotId);
}
