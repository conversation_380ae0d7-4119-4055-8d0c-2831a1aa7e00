package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrmHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrowdfundingVolunteerCreateCaseRecordExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.VolunteerAutoChangeModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffFriendDO;
import com.shuidihuzhu.cf.domain.dedicated.*;
import com.shuidihuzhu.cf.lion.client.risk.model.CrowdfundingVolunteerCreateCaseRecordExtFeginDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister;
import com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign;
import com.shuidihuzhu.cf.model.toufang.SDDataToufangSign;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.Date;
import java.util.List;

public interface ICfMasterForGrowthtoolDelegate {
    int insertCrmHospitalDO(CrmHospitalDO crmHospitalDO);

    int updateCrmHospitalDO(CrmHospitalDO crmHospitalDO);

    int deleteHospital(int hospitalId);

    int updateHospitalVHospitalCode(Long id, String vhospitalCode);

    int updateStatusAndRstatusByHospitalId(Long id, Integer useStatus, Integer rStatus);

    int updateHospitalVhospitalRalation(Long id, String misName, String timestamp);

    int insertByHospitalItem(String hospitalName, String cityName, String provinceName, String vhospitalCode, Integer checkStatus);

    int insertCfAdRegister(CfAdRegister cfAdRegister);

    int insertSDDataToufangSign(SDDataToufangSign sdDataToufangSign);

    int addInviteRelation(CfToufangInviteCaseRelationDO caseRelationDO);

    int updateCaseHandleStatus(Integer id, Integer completeFlag, Integer taskFlag, Date expireTime);

    int addCfToufangInvitorVisitDO(CfToufangInvitorVisitDO cfToufangInvitorVisitDO);

    int insertCfTouFangSign(CfTouFangSign cfTouFangSign);

    int insertCfTouFangSignList(List<CfTouFangSign> cfTouFangSigns);

    int addUserVolunteerRelationDO(CfUserVolunteerRelationDO volunteerRelationDO);

    int addCfVolunteerMaterial(CfVolunteerMaterialDO materialDO);

    int updateCfVolunteerMaterial(CfVolunteerMaterialDO materialDO);

    int updateVerifyPictures(String uniqueCode, String personPic, String s,String s1);

    int addVolunteer(CrowdfundingVolunteer crowdfundingVolunteer);

    int addNewVolunteer(CrowdfundingVolunteer crowdfundingVolunteer);

    int updateBaseInfoApplyStatus(String uniqueCode, Integer i, Integer verifyStatus, Integer applyStatus);

    int updateVolunteerInfo(String uniqueCode, String aesEncrypt, String name, int age);

    void updateVolunteerInfoById(CrowdfundingVolunteer crowdfundingVolunteer);

    void updateQrCode(String qrCode, Long id);

    void updateApplyStatusById(long id, int applyStatus, String operatorName, long operatorUserId, String angelUrl, String refuseReasons, String qrCode);

    int updateWorkStatus(long id,int workStatus, String operator, Date leaveTime);

    int updateEntryTime(long id, Date entryTime, String system);

    int updateMisBlankById(Long id);

    void updateVEncryptMobiles(long id, String vEncryptMobiles);

    int insertCrowdfundingVolunteerInviteUserRecordDO(CrowdfundingVolunteerInviteUserRecordDO crowdfundingVolunteerInviteUserRecordDO);

    int updatePrimaryChannel(long id, String primaryChannel);

    int insertCfServiceStaffDO(CfServiceStaffDO cfServiceStaffDO);

    int updateServiceStaffUserInfo(String qywechatid, Integer serviceType,
                                   String qrCode, String headUrl,
                                   Integer id,Integer helpPatients,
                                   Integer raiseAmount,
                                   String labels,
                                   String favorableRate,
                                   String qyWechatQrCode);
    int updateShowTimeByQyWechatUserId(String qyWechatUserId,String showTime);

    int updateCfServiceStaffById(CfServiceStaffDO cfServiceStaffDO);


    int insertCfServiceStaffFriendDO(CfServiceStaffFriendDO cfServiceStaffFriendDO);


    int updateQyWechatUserIdByUserId(CfServiceStaffFriendDO cfServiceStaffFriendDO);

    int updateEntryLeaveTime(VolunteerAutoChangeModel changeModel);

    int insertVolunteerCreateCaseRecord(CrowdfundingVolunteerCreateCaseRecordExtDO crowdfundingVolunteerCreateCaseRecordExtDO);

    int insertVolunteerCreateCaseRecordWithPhone(CrowdfundingVolunteerCreateCaseRecordExtDO crowdfundingVolunteerCreateCaseRecordExtDO);

    int updateNoClewCreateDesc(long id, String noClewCreateDesc);

    int updateClewId(long id, long clewId);

    void updateGrTag(List<String> uniqueCodeList, int grTag);

    void updateCreateCaseRecord(CrowdfundingVolunteerCreateCaseRecordExtFeginDO createCaseRecordExtFeginDO);
}
