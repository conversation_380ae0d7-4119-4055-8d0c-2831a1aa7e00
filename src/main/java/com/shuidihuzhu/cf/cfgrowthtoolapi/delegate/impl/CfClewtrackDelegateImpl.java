package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfClewtrackDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.*;
import com.shuidihuzhu.client.cf.clewtrack.model.*;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.Optional;

/**
 * @author: wanghui
 * @create: 2019/5/17 3:41 PM
 */
@Service
@Slf4j
public class CfClewtrackDelegateImpl implements ICfClewtrackDelegate {

    @Autowired
    private CfClewtrackChannelFeignClient cfClewtrackChannelFeignClient;
    @Autowired
    private CfClewtrackBdCrmFeignClient cfClewtrackBdCrmFeignClient;
    @Autowired
    private CfClewtrackUserFeignClient cfClewtrackUserFeignClient;
    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;
    @Autowired
    private CfClewtrackApiClient cfClewtrackApiClient;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public ImmutablePair<String, String> getChannelRefineResultByOtherChannel(Long infoId, Long userId) {
        ImmutablePair<String, String> immutablePair = null;
        try {
            Response<CfUserInvitedLaunchCaseRecordModel> response = cfClewtrackChannelFeignClient.getChannelRefineResultByOtherChannel(infoId, userId);
            if (response.ok()) {
                immutablePair = new ImmutablePair(response.getData().getChannel(), response.getData().getServiceUserInfo(shuidiCipher));
            }
            log.info(this.getClass().getSimpleName() + "  getChannelRefineResultByOtherChannel result:{}", JSON.toJSONString(response));
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getChannelRefineResultByOtherChannel err:", e);
        }
        return immutablePair;
    }


    /**
     * @param uniqueCode
     * @param phone
     * @param fundraisingObject
     * @param diseaseName
     * @param hosptialName
     * @param address           是json串  内容包括  department  sickroom  sickbed
     * @return
     */
    @Override
    public OpResult<CfBdClewForPreposeMaterialModel> checkPhoneIsExistInBDClewV2(String uniqueCode, String phone, String fundraisingObject, String diseaseName, String hosptialName, String address) {
        Response<CfBdClewForPreposeMaterialModel> response = cfClewtrackBdCrmFeignClient.checkPhoneIsExistInBDClewV2(uniqueCode, phone, fundraisingObject, diseaseName, hosptialName, address);
        if (response.ok() && response.getData() != null) {
            return OpResult.createSucResult(response.getData());
        }
        if (response.ok() && response.getData() == null) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
        }
        if (response.getCode() == CfGrowthtoolErrorCode.FALLBACK.getCode()) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        }
        //如果是返回放入待解锁线索池,需要返回对应的信息
        if (response.getCode() == CfGrowthtoolErrorCode.CRASH_NEED_APPLY.getCode()) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CRASH_NEED_APPLY);
        }
        return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL, response.getMsg());
    }

    @Override
    public List<String> getOnline111FuWuUser() {
        return cfClewtrackUserFeignClient.getOnline111FuWuUser().getData();
    }

    @Override
    public List<String> getOnlineMenue1v1TestFuWuUser(int type) {
        return cfClewtrackUserFeignClient.getOnlineMenue1v1TestFuWuUser(type).getData();
    }

    @Override
    public List<CfClewForCharityDayModel> getCfClewForCharityDayModelsByDisplayIds(List<String> displayIds) {
        return cfClewtrackFeignClient.getCfClewForCharityDayModelsByDisplayIds(displayIds).getData();
    }

    @Override
    public List<CfClewForCharityDayModel> getCfClewForCharityDayModelsByPhones(List<String> phones) {
        List<List<String>> phonesList = Lists.partition(phones, GeneralConstant.MAX_PAGE_SIZE);
        List<CfClewForCharityDayModel> cfClewForCharityDayModels = phonesList.stream()
                .map(list -> cfClewtrackFeignClient.getCfClewForCharityDayModelsByPhones(list).getData())
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                })
                .get();
        return cfClewForCharityDayModels;
    }

    @Override
    public Long getNewClewCountByUniqueListWithTime(String startTime, String endTime, List<String> uniqueCodeList) {
        Response<Long> response = cfClewtrackBdCrmFeignClient.getNewClewCountByUniqueListWithTime(startTime, endTime, uniqueCodeList);
        return response.ok() ? response.getData() : 0L;
    }

    @Override
    public Long getNewClewCountByOrgListWithTime(String startTime, String endTime, List<Integer> orgIdList) {
        //todo: 线索需要增加按照组织维度查询的接口
        return null;
    }

    @Override
    public void syncApproveStatus(Long clewId, Integer approveStatus) {
        cfClewtrackApiClient.syncApproveStatus(clewId, approveStatus);
    }


    @Override
    public void updateClewStatusWithAttachInfo(Long clewId) {
        cfClewtrackBdCrmFeignClient.updateClewStatusWithAttachInfo(clewId);
    }

    @Override
    public CfClewBaseInfoDO getCfClewBaseInfoDOByUniqueCodeWithInfoUuid(String uniqueCode,
                                                                        Long infoId) {
        Response<CfClewBaseInfoDO> response = cfClewtrackApiClient.getCfClewBaseInfoDOByUniqueCodeWithInfoUuid(uniqueCode, infoId);
        return response.getData();
    }

    @Override
    public List<GwClewCountGroupDayModel> listClewCountGroupByDay(String startTime, String endTime, List<String> uniqueCodeList) {
        Response<List<GwClewCountGroupDayModel>> clewCountGroupByDay = cfClewtrackBdCrmFeignClient.listClewCountGroupByDay(startTime, endTime, uniqueCodeList);
        if (clewCountGroupByDay.notOk() || CollectionUtils.isEmpty(clewCountGroupByDay.getData())) {
            return Lists.newArrayList();
        }
        return clewCountGroupByDay.getData();
    }

    @Override
    public List<CfClewVolunteerFollowCountModel> getVolunteerTransformClewRecord(List<String> uniqueCodeList) {
        uniqueCodeList = uniqueCodeList.stream().distinct().collect(Collectors.toList());
        Response<List<CfClewVolunteerFollowCountModel>> volunteerTransformClewRecord = cfClewtrackBdCrmFeignClient.getVolunteerTransformClewRecord(uniqueCodeList);
        if (volunteerTransformClewRecord.notOk() || CollectionUtils.isEmpty(volunteerTransformClewRecord.getData())) {
            return Lists.newArrayList();
        }
        return volunteerTransformClewRecord.getData();
    }

    @Override
    public FuwuMemberInfo getFuwuMemberInfo(String phone) {
        if (StringUtils.isBlank(phone)) {
            return null;
        }
        return Optional.ofNullable(cfClewtrackChannelFeignClient.getFuwuMemberInfo(phone))
                .map(Response::getData)
                .orElse(null);
    }

    @Override
    public List<ClewCrashSimpleInfo> listClewCrashInfo(List<String> encryptPhoneList) {
        if (CollectionUtils.isEmpty(encryptPhoneList)) {
            return Lists.newArrayList();
        }
        return Optional.ofNullable(cfClewtrackBdCrmFeignClient.listClewCrashInfo(encryptPhoneList))
                .map(Response::getData)
                .orElse(Lists.newArrayList());
    }
}
