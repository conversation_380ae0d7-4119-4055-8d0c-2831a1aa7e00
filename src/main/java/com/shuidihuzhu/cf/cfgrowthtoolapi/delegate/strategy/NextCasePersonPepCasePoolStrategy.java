package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.strategy;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mapper.HomeCaseRemindModelMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.GrowthtoolCasePoolModelVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.HomeCaseRemindModel;
import com.shuidihuzhu.cf.client.performance.PepTaskClient;
import com.shuidihuzhu.cf.client.performance.enums.FactBizTypeEnum;
import com.shuidihuzhu.cf.client.performance.model.GrowthtoolCasePoolModel;
import com.shuidihuzhu.cf.client.performance.model.GrowthtoolPepCaseScoreModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/12/11  14:32
 */
@Service
public class NextCasePersonPepCasePoolStrategy extends AbstractPersonPepCasePoolStrategy {

    @Autowired
    private PepTaskClient pepTaskClient;
    @Autowired
    private HomeCaseRemindModelMapper homeCaseRemindModelMapper;

    @Override
    public GrowthtoolCasePoolModelVo executeStrategyByPersonPepCasePool(String userId, int factBizType) {
        GrowthtoolCasePoolModelVo growthtoolCasePoolModelVo = getGrowthtoolCasePoolModelVo(userId, factBizType);
        if (growthtoolCasePoolModelVo == null || CollectionUtils.isEmpty(growthtoolCasePoolModelVo.getCaseScoreList())) {
            return GrowthtoolCasePoolModelVo.builder()
                    .grey(true)
                    .build();
        }
        String dt = DateUtil.getCurrentDateStr();
        //查询当前顾问的案例信息
        List<HomeCaseRemindModel> caseScoreList = growthtoolCasePoolModelVo.getCaseScoreList();
        setCaseType(HomeCaseRemindModel.CaseTypeEnum.NEXT_CASE.getCode(), caseScoreList);
        //获取案例特征
        getCaseFeature(dt, caseScoreList, getCaseScoreList(userId, FactBizTypeEnum.commission_hot_case.getCode()));
        //获取诊断
        getDiagnosis(caseScoreList);
        return GrowthtoolCasePoolModelVo.builder()
                .grey(true)
                .caseScoreList(caseScoreList)
                .build();
    }

    @Override
    public boolean support(int caseType) {
        return caseType == HomeCaseRemindModel.CaseTypeEnum.NEXT_CASE.getCode();
    }

    private GrowthtoolCasePoolModelVo getGrowthtoolCasePoolModelVo(String userId, int factBizType) {
        List<HomeCaseRemindModel> homeCaseRemindList = homeCaseRemindModelMapper.toList(getCaseScoreList(userId, factBizType));
        return GrowthtoolCasePoolModelVo.builder()
                .caseScoreList(homeCaseRemindList)
                .build();
    }

    private List<GrowthtoolPepCaseScoreModel> getCaseScoreList(String userId, int factBizType) {
        Response<GrowthtoolCasePoolModel> growthtoolCasePoolModelResponse = pepTaskClient.personPepCasePool(userId, factBizType);
        return Optional.ofNullable(growthtoolCasePoolModelResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .map(GrowthtoolCasePoolModel::getCaseScoreList)
                .orElse(Lists.newArrayList());
    }
}
