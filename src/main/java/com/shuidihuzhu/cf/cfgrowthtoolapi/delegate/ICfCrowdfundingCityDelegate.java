package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.client.cf.api.model.CrmCityModel;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/11/26 4:27 PM
 */
public interface ICfCrowdfundingCityDelegate {

    List<CrowdfundingCity> getProvince();

    List<CrowdfundingCity> getChildren(int parentId);

    List<CrowdfundingCity> getListByIds(List<Integer> ids);

    List<CrmCityModel> fuzzyByCityName(String cityName);

    int getCityIdByCityName(String cityName);

}
