package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywx.UpdateExternalContactRemarkParam;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.aop.QywxClientAspect;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IQywxSdkDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupDetailModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSimpleTrueOrFalseEnum;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.ShuidiChouQyWxFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewQyWxCorpDO;
import com.shuidihuzhu.client.cf.qywechat.QywxSdkClient;
import com.shuidihuzhu.client.feign.CfClewtrackClient;
import com.shuidihuzhu.client.model.qy.param.AddContactWayParam;
import com.shuidihuzhu.client.model.qy.param.CustomerAcquisitionCreateParam;
import com.shuidihuzhu.client.model.qy.param.DeleteLinkParam;
import com.shuidihuzhu.client.model.qy.param.CustomerAcquisitionCreateParam.Range;
import com.shuidihuzhu.client.model.qy.response.AddContactWayResponse;
import com.shuidihuzhu.client.model.qy.response.CustomerAcquisitionResponse;
import com.shuidihuzhu.client.model.qy.response.DeleteLinkResponse;
import com.shuidihuzhu.common.web.model.Response;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: wanghui
 * @create: 2019/9/12 4:03 PM
 */
@Service
@Slf4j
public class QywxSdkDelegate implements IQywxSdkDelegate {
    public static final Pattern pMobile = Pattern.compile("(\\d{11})");
    @Autowired
    private QywxSdkClient qywxSdkClient;

    @Autowired
    private CfClewtrackClient cfClewtrackClient;
    @Autowired
    private ShuidiChouQyWxFeignClient shuidiChouQyWxFeignClient;
    @Autowired
    private QywxClientAspect qywxClientAspect;

    @Autowired
    private ApolloService apolloService;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Resource
    private AccountServiceDelegate accountServiceDelegate;

    private LoadingCache<String, Map<Integer, CfClewQyWxCorpDO>> cfClewQyWxDoMapCache = CacheBuilder.newBuilder()
            .maximumSize(100)
            .refreshAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Map<Integer,CfClewQyWxCorpDO>>() {
                @Override
                public Map<Integer,CfClewQyWxCorpDO> load(String key) throws Exception {
                    return getCfClewWxCorpMsgDOUseFeign(key);
                }
            });

    @Override
    public JSONObject getExternalContactResult(String accessToken, String externalUserid, CfClewQyWxCorpDO cfClewQyWxCorpDO) {
        JSONObject resultJsonObject = null;
        try{
            String result = qywxSdkClient.getExternalContact(accessToken, externalUserid);
            resultJsonObject = JSONObject.parseObject(result);
            if (resultJsonObject!=null && "40014".equals(resultJsonObject.getString("errcode"))){
                //   如果 access_token 失效  则从新拉取 access_token
                if (cfClewQyWxCorpDO == null){
                    accessToken = this.getToken(CfSimpleTrueOrFalseEnum.FALSE);
                }else{
                    accessToken = this.getAccessToken(CfSimpleTrueOrFalseEnum.FALSE,cfClewQyWxCorpDO.getCorpId(),cfClewQyWxCorpDO.getAppSecret());
                }
                result = qywxSdkClient.getExternalContact(accessToken, externalUserid);
                resultJsonObject = JSONObject.parseObject(result);
            }
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+"  getExternalContactResult err:",e);
        }
        log.debug(this.getClass().getSimpleName()+" getExternalContactResult result:{}",resultJsonObject);
        if (null!=resultJsonObject
                && "0".equals(resultJsonObject.getString("errcode"))){
            return resultJsonObject;
        }
        return null;
    }

    @Override
    public String getUnionIdByExternalContactResult(JSONObject resultJsonObject) {
        JSONObject externalContactJsonObject = resultJsonObject.getJSONObject("external_contact");
        return externalContactJsonObject.getString("unionid")==null?"":externalContactJsonObject.getString("unionid");
    }

    @Override
    public String getExternalUserNameByExternalContactResult(JSONObject resultJsonObject) {
        JSONObject externalContactJsonObject = resultJsonObject.getJSONObject("external_contact");
        return StringUtils.isEmpty(externalContactJsonObject.getString("name")) ? StringUtils.EMPTY : externalContactJsonObject.getString("name");
    }

    @Override
    public String getTagsByExternalContactResult(JSONObject resultJsonObject, String userId) {
        JSONArray followUserJsonArray = resultJsonObject.getJSONArray("follow_user");
        if (followUserJsonArray != null && !followUserJsonArray.isEmpty()) {
            JSONArray array = new JSONArray();
            for (JSONObject jsonObject : followUserJsonArray.toJavaList(JSONObject.class)) {
                if (jsonObject.getString("userid").equals(userId)) {
                    array = jsonObject.getJSONArray("tags");
                    break;
                }
            }
            return array.isEmpty() ? StringUtils.EMPTY : JSON.toJSONString(array);
        }
        return StringUtils.EMPTY;
    }

    /**
     *
     *  通过企业微信的 外部联系人详情 获得 follow_user.remark
     *  通过follow_user.remark  解析出备注的手机号 （正则匹配11位手机号）
     *  @param resultJsonObject  外部联系人详情接口的返回值
     *  @param qyWechatUserId  企业成员的userid
     *  @return
     */
    @Override
    public ImmutableTriple<String, String, String> getPhoneAndPassTimeByExternalContactResult(JSONObject resultJsonObject, String qyWechatUserId, Integer callBackId, String unionId) {
        String phone = "";
        String passTime = "";
        String remark = "";
        JSONArray followUsers = resultJsonObject.getJSONArray("follow_user");
        for (Object followUserObj : followUsers){
            JSONObject followUser = (JSONObject) followUserObj;
            if (qyWechatUserId.equals(followUser.getString("userid"))){
                passTime = DateUtil.getYmdhmsFromTimestamp(Long.valueOf(followUser.getString("createtime") + "000"));
                remark = followUser.getString("remark");
                Matcher matcher = pMobile.matcher(remark);
                phone = matcher.find()?matcher.group():"";
                if (apolloService.getWeChatFriendInfoChangeMsgToMdc_callbackIds().contains(callBackId) && StringUtils.isEmpty(phone)){
                    JSONArray mobiles = followUser.getJSONArray("remark_mobiles");
                    log.info("remark_mobiles:{}",mobiles);
                    phone = CollectionUtils.isNotEmpty(mobiles) ? mobiles.getString(0) : "";
                }
            }
        }
        if (apolloService.getWeChatFriendInfoChangeMsgToMdc_callbackIds().contains(callBackId) && StringUtils.isEmpty(phone) ){
            phone = Optional.ofNullable(
                    accountServiceDelegate.getByUnionId(unionId)
            ).map(UserInfoModel::getCryptoMobile)
                    .map(shuidiCipher::decrypt).orElse("");
            log.info("phone_by_unionId:{}",phone);
        }
        return new ImmutableTriple(phone,remark,passTime);
    }

    @Override
    public String getToken(CfSimpleTrueOrFalseEnum cfSimpleTrueOrFalseEnum) {
        try {
            com.shuidihuzhu.common.web.model.Response<String> response = cfClewtrackClient.getAccessToken(cfSimpleTrueOrFalseEnum.value());
            if (null==response){
                return "";
            }
            qywxClientAspect.pushAccessToken2Redis(response.getData(), "wwb8cc2f5c0fc58917");
            return response.getData();
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" getToken err:",e);
        }
        return "";
    }

    @Override
    public CfClewQyWxCorpDO getWxCorpMsgByCallbackId(int callbackId) {
        CfClewQyWxCorpDO cfClewQyWxCorpDO = null;
        try{
            Map<Integer,CfClewQyWxCorpDO> cfClewWxCorpMsgDOMap = cfClewQyWxDoMapCache.get("shuidichou");
            if (!MapUtils.isEmpty(cfClewWxCorpMsgDOMap)){
                cfClewQyWxCorpDO = cfClewWxCorpMsgDOMap.getOrDefault(callbackId,null);
            }
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" getWxCorpMsgByCallbackId callbackId:{} err:",callbackId,e);
        }
        return cfClewQyWxCorpDO;
    }

    @Override
    public List<CfClewQyWxCorpDO> listByCorpId(String corpId) {
        try {
            Map<Integer, CfClewQyWxCorpDO> cfClewWxCorpMsgDOMap = cfClewQyWxDoMapCache.get("shuidichou");
            return cfClewWxCorpMsgDOMap.values().stream()
                    .filter(item -> Objects.equals(item.getCorpId(), corpId))
                    .collect(Collectors.toList());
        } catch (ExecutionException e) {
            log.error(this.getClass().getSimpleName() + " listByCorpId corpId:{} err:", corpId, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public String getAccessToken(CfSimpleTrueOrFalseEnum trueOrFalseEnum, String corpId, String corpSecret) {
        try{
            Response<String> response = shuidiChouQyWxFeignClient.getQyWechatAccessToken(trueOrFalseEnum.value(),corpId,corpSecret);
            if (null == response || response.notOk()){
                return "";
            }
            qywxClientAspect.pushAccessToken2Redis(response.getData(), corpId);
            return response.getData();
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" getAccessToken corpId:{},corpSecret:{} err:",corpId,corpSecret,e);
        }
        return "";
    }

    private Map<Integer, CfClewQyWxCorpDO> getCfClewWxCorpMsgDOUseFeign(String key){
        try{
            Response<Map<Integer,CfClewQyWxCorpDO>> cfClewQyWxCorpDOResponse = shuidiChouQyWxFeignClient.getQyWxCorp();
            if (cfClewQyWxCorpDOResponse.notOk()){
                return null;
            }
            return cfClewQyWxCorpDOResponse.getData();
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" getCfClewWxCorpMsgDOUseFeign error",e);
        }
        return null;
    }

    private String getToken(CfClewQyWxCorpDO cfClewQyWxCorpDO) {
        String accessToken;
        if (cfClewQyWxCorpDO == null){
            accessToken = getToken(CfSimpleTrueOrFalseEnum.FALSE);
        }else{
            accessToken = getAccessToken(CfSimpleTrueOrFalseEnum.FALSE,cfClewQyWxCorpDO.getCorpId(),cfClewQyWxCorpDO.getAppSecret());
        }
        return accessToken;
    }

    @Override
    public CfWorkImGroupDetailModel getGroupChatDetail(String chatId, CfClewQyWxCorpDO cfClewQyWxCorpDO) {
        try {
            String accessToken = getToken(cfClewQyWxCorpDO);
            if (StringUtils.isEmpty(accessToken)) {
                return null;
            }

            Map<String, Object> param = Maps.newHashMap();
            param.put("chat_id", chatId);
            param.put("need_name",1);
            String response = qywxSdkClient.getGroupChatDetail(accessToken, JSONObject.toJSONString(param));
            log.info(this.getClass().getSimpleName() + " getGroupChatDetail accessToken:{}, json:{}, response:{}",
                    accessToken, JSONObject.toJSONString(param), response);
            if (StringUtils.isEmpty(response)) {
                return null;
            }

            JSONObject resultJsonObject = JSONObject.parseObject(response);
            if (null == resultJsonObject || !"0".equals(resultJsonObject.getString("errcode"))) {
                return null;
            }

            String content = resultJsonObject.getString("group_chat");
            if (StringUtils.isEmpty(content)) {
                return null;
            }

            return JSONObject.parseObject(content, CfWorkImGroupDetailModel.class);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " getGroupChatDetail error, chatId:{}", chatId, e);
            return null;
        }
    }

    @Override
    public CfWorkImGroupInfoModel queryGroupInfo(Map<String, Object> param, CfClewQyWxCorpDO cfClewQyWxCorpDO) {
        try {
            String accessToken = getToken(cfClewQyWxCorpDO);
            if (StringUtils.isEmpty(accessToken)) {
                return null;
            }

            String response = qywxSdkClient.getGroupChatList(accessToken, JSONObject.toJSONString(param));
            log.info(this.getClass().getSimpleName() + " queryGroupInfo accessToken:{}, json:{}, response:{}",
                    accessToken, JSONObject.toJSONString(param), response);
            if (StringUtils.isEmpty(response)) {
                return null;
            }

            return JSONObject.parseObject(response, CfWorkImGroupInfoModel.class);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " queryGroupInfo error, param:{}", param, e);
            return null;
        }
    }

    @Override
    public CfClewQyWxCorpDO getQyWxCorpSelfAppByCallbackId(int callbackId) {
        CfClewQyWxCorpDO cfClewQyWxCorpDO = null;
        try{
            Response<Map<Integer,CfClewQyWxCorpDO>> cfClewQyWxCorpDOResponse = shuidiChouQyWxFeignClient.getQyWxCorpSelfApp();
            if (cfClewQyWxCorpDOResponse.notOk()){
                return null;
            }
            Map<Integer,CfClewQyWxCorpDO> cfClewWxCorpMsgDOMap = cfClewQyWxCorpDOResponse.getData();
            if (!MapUtils.isEmpty(cfClewWxCorpMsgDOMap)){
                cfClewQyWxCorpDO = cfClewWxCorpMsgDOMap.getOrDefault(callbackId,null);
            }
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" getQyWxCorpSelfAppByCallbackId callbackId:{} err:",callbackId,e);
        }
        return cfClewQyWxCorpDO;
    }

    @Data
    public static class TokenResult {
        private String access_token;
        private int expires_in;
        private String errmsg;
        private int errcode;
    }

    public TokenResult getWxToken(String cropId, String secret) {
        //生成 token
        String token = qywxSdkClient.getToken(cropId, secret);
        if (StringUtils.isEmpty(token)) {
            log.warn(this.getClass().getSimpleName() + " addContactWay getToken error, cropId:{}, secret:{}", cropId, secret);
            return null;
        }
        TokenResult tokenResult = JSONObject.parseObject(token, TokenResult.class);
        if (tokenResult == null || StringUtils.isBlank(tokenResult.access_token)) {
            return null;
        }
        return tokenResult;
    }

    @Override
    public AddContactWayResponse addContactWay(String cropId, String secret, AddContactWayParam param) {
        //生成 token
        TokenResult wxToken = getWxToken(cropId, secret);
        if (wxToken == null) {
            return null;
        }
        AddContactWayResponse addContactWayResponse = qywxSdkClient.addContactWay(wxToken.getAccess_token(), param);
        log.info(this.getClass().getSimpleName() + " addContactWay cropId:{}, secret:{}, param:{}, response:{}",
                cropId, secret, JSONObject.toJSONString(param), addContactWayResponse);
        return addContactWayResponse;
    }




    @Override
    public CustomerAcquisitionResponse createLink(String cropId, String secret, List<String> userIds) {
        //生成 token
        TokenResult wxToken = getWxToken(cropId, secret);
        if (wxToken == null) {
            return null;
        }
        CustomerAcquisitionCreateParam customerAcquisitionCreateParam = new CustomerAcquisitionCreateParam();
        customerAcquisitionCreateParam.setLink_name("活码-"+ Joiner.on("-").join(userIds));
        Range range = new CustomerAcquisitionCreateParam.Range();
        range.setUser_list(userIds);
        customerAcquisitionCreateParam.setRange(range);
        customerAcquisitionCreateParam.setSkip_verify(true);

        CustomerAcquisitionResponse customerAcquisitionLink = qywxSdkClient.createCustomerAcquisitionLink(wxToken.getAccess_token(), customerAcquisitionCreateParam);
        log.info(this.getClass().getSimpleName() + " createLink cropId:{}, secret:{}, userIds:{}, response:{}",
                cropId, secret, userIds, customerAcquisitionLink);
        return customerAcquisitionLink;
    }

    @Override
    public DeleteLinkResponse deleteLink(String cropId, String secret, String linkId) {
        //生成 token
        TokenResult wxToken = getWxToken(cropId, secret);
        if (wxToken == null) {
            return null;
        }
        DeleteLinkParam deleteLinkParam = new DeleteLinkParam();
        deleteLinkParam.setLink_id(linkId);
        DeleteLinkResponse deleteLink = qywxSdkClient.deleteLink(wxToken.getAccess_token(), deleteLinkParam);
        log.info(this.getClass().getSimpleName() + " deleteContactWay cropId:{}, secret:{}, linkId:{}, response:{}", 
                cropId, secret, linkId, deleteLink);
        return deleteLink;
    }

    @Override
    public void updateRemark(String cropId, String secret, UpdateExternalContactRemarkParam remarkParam) {
        //生成 token
        TokenResult wxToken = getWxToken(cropId, secret);
        if (wxToken == null) {
            return;
        }
        String remark = qywxSdkClient.remark(wxToken.getAccess_token(), JSON.toJSONString(remarkParam));
        log.info(this.getClass().getSimpleName() + " updateRemark token:{}, remarkParam:{}, response:{}",
                wxToken.getAccess_token(), JSON.toJSONString(remarkParam), remark);
    }

}
