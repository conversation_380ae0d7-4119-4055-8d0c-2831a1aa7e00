package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.message;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.msg.model.PushRecord;
import com.shuidihuzhu.msg.model.WxRecord;
import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;

import java.util.List;
import java.util.Map;

public interface IMsgClientDelegate {

    void sendMessage(String modelNum, String message, List<String> mobiles);

    void saveBatch(MsgRecordBatch msgRecordBatch);

    void sendSms(String modelNum, String cryptoMobile, Map<Integer, String> params);

    void sendWxMsg(String modelNum, List<Long> userIdList, int thirdType);
}
