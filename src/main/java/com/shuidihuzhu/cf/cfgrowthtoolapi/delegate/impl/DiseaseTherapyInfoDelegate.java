package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IDiseaseTherapyInfoDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CopyMapper;
import com.shuidihuzhu.cf.client.ugc.feign.DiseaseClassifyFeignClient;
import com.shuidihuzhu.cf.client.ugc.model.view.DiseaseClassifyVO;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClassifyFeignClientV2;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.request.DecideReasonableInfo;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseClassifyVOV2;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseVO;
import com.shuidihuzhu.client.cf.growthtool.model.vo.InfoReasonableAmountResultVO;
import com.shuidihuzhu.client.cf.growthtool.model.vo.SpecialDiseaseChoiceInfoVO;
import com.shuidihuzhu.client.cf.growthtool.param.DiseaseParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-06-27
 */
@Service
@Slf4j
@RefreshScope
public class DiseaseTherapyInfoDelegate implements IDiseaseTherapyInfoDelegate {

    @Autowired
    private DiseaseClassifyFeignClient diseaseClassifyFeignClient;
    @Autowired
    private DiseaseClient diseaseClient;
    @Autowired
    private DiseaseClassifyFeignClientV2 diseaseClassifyFeignClientV2;
    @Autowired
    private ApolloService apolloService;
    @Resource
    private CopyMapper copyMapper;

    @Override
    public Response<DiseaseVO> diseaseNorm(DiseaseParam param) {
        try{
            if (apolloService.getDiseaseNormVersion() == 1){
                return this.diseaseNormV1(param);
            }else{
                return this.diseaseNormV2(param);
            }
        }catch (Exception e){
            log.error("diseaseNorm error",e);
        }
        return NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
    }

    @Override
    public Response<SpecialDiseaseChoiceInfoVO> specialRaiseChoiceInfo(List<String> diseaseNameList) {
        try{
            Response<SpecialDiseaseChoiceInfoVo> response = diseaseClient.specialRaiseChoiceInfo(diseaseNameList);
            log.debug("specialRaiseChoiceInfo_response:{}", JSONObject.toJSONString(response));
            SpecialDiseaseChoiceInfoVO target = new SpecialDiseaseChoiceInfoVO();
            if (Objects.nonNull(response.getData())){
                SpecialDiseaseChoiceInfoVo source = response.getData();
                target = copyMapper.toSpecialDiseaseChoiceInfoVO(source);
            }
            return NewResponseUtil.makeSuccess(target);
        }catch (Exception e){
            log.error("specialRaiseChoiceInfo error",e);
        }
        return NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
    }

    @Override
    public Response<SpecialDiseaseChoiceInfoVO> specialChoiceInfo(DiseaseParam param) {
        try{
            Response<SpecialDiseaseChoiceInfoVo> response = diseaseClient.specialChoiceInfo(this.buildDecideReasonableInfo(param));
            log.debug("specialChoiceInfo_response:{}", JSONObject.toJSONString(response));
            SpecialDiseaseChoiceInfoVO target = new SpecialDiseaseChoiceInfoVO();
            if (Objects.nonNull(response.getData())){
                target = copyMapper.toSpecialDiseaseChoiceInfoVO(response.getData());
            }
            return NewResponseUtil.makeSuccess(target);
        }catch (Exception e){
            log.error("specialChoiceInfo error",e);
        }
        return NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
    }

    @Override
    public Response<InfoReasonableAmountResultVO> decideInfoAmountReasonable(DiseaseParam param) {
        try{
            Response<InfoReasonableAmountResultVo> response = diseaseClient.decideInfoAmountReasonable(this.buildDecideReasonableInfo(param));
            log.debug("decideInfoAmountReasonable_response:{}", JSONObject.toJSONString(response));
            InfoReasonableAmountResultVO target = new InfoReasonableAmountResultVO();
            if (Objects.nonNull(response.getData())){
                target = copyMapper.toInfoReasonableAmountResultVO(response.getData());
                target.convertUnit();
            }
            return NewResponseUtil.makeSuccess(target);
        }catch (Exception e){
            log.error("decideInfoAmountReasonable error",e);
        }
        return NewResponseUtil.makeError(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
    }

    private DiseaseClassifyFeignClient.Param buildParam(DiseaseParam diseaseParam){
        DiseaseClassifyFeignClient.Param param  = new DiseaseClassifyFeignClient.Param();
        param.setDisease(diseaseParam.getSubmitDiseases());
        return param;
    }

    private DecideReasonableInfo buildDecideReasonableInfo(DiseaseParam param){
        DecideReasonableInfo decideReasonableInfo = new DecideReasonableInfo();
        decideReasonableInfo.setDiseaseNameList(param.getClassifyDiseases());
        if (StringUtils.isNotEmpty(param.getSpecialRaiseChoiceInfo())){
            decideReasonableInfo.setSpecialRaiseChoiceInfo(param.getSpecialRaiseChoiceInfo());
        }
        if (StringUtils.isNotEmpty(param.getSpecialDiseaseInfo())){
            decideReasonableInfo.setSpecialDiseaseInfo(param.getSpecialDiseaseInfo());
        }
        return decideReasonableInfo;
    }

    /**
     * 疾病归一V1
     * @param param
     * @return
     */
    private Response<DiseaseVO> diseaseNormV1(DiseaseParam param) {
        Response<List<DiseaseClassifyVO>> response = diseaseClassifyFeignClient.diseaseNorm(this.buildParam(param));
        log.debug("diseaseNorm_response:{}", JSONObject.toJSONString(response));
        DiseaseVO diseaseVO = new DiseaseVO();
        List<com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseClassifyVO> result = Lists.newArrayList();
        if (Objects.isNull(response.getData())){
            return NewResponseUtil.makeSuccess(null);
        }
        response.getData().forEach(item -> {
            com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseClassifyVO diseaseClassifyVO = new com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseClassifyVO();
            BeanUtils.copyProperties(item,diseaseClassifyVO);
            result.add(diseaseClassifyVO);
        });
        diseaseVO.setDiseaseClassifyList(result);
        return NewResponseUtil.makeSuccess(diseaseVO);
    }

    /**
     * 疾病归一V2
     * @param param
     * @return
     */
    private Response<DiseaseVO> diseaseNormV2(DiseaseParam param) {
        List<String> submitDiseases = param.getSubmitDiseases();
        Response<List<DiseaseClassifyVOV2>> response = diseaseClassifyFeignClientV2.diseaseNorm(submitDiseases);
        log.info("diseaseNormV2_response:{}", JSONObject.toJSONString(response));
        DiseaseVO diseaseVO = new DiseaseVO();
        List<com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseClassifyVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(response.getData())){
            return NewResponseUtil.makeSuccess(null);
        }
        List<DiseaseClassifyVOV2> classifyV2List = response.getData();
        for (DiseaseClassifyVOV2 diseaseClassifyV2 : classifyV2List){
            String disease =  diseaseClassifyV2.getDisease();
            List<String> normList =diseaseClassifyV2.getNorm();
            normList.forEach(item -> {
                com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseClassifyVO diseaseClassifyVO = new com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseClassifyVO();
                diseaseClassifyVO.setDisease(disease);
                diseaseClassifyVO.setNorm(item);
                result.add(diseaseClassifyVO);
            });
        }
        diseaseVO.setDiseaseClassifyList(result);
        return NewResponseUtil.makeSuccess(diseaseVO);
    }
}
