package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.client.adminpure.model.report.ReportInfoVO;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.client.cf.growthtool.model.PreposeAddRiskBlackModel;

/**
 * <AUTHOR>
 * @date 2020-07-01
 */
public interface RiskRpcDelegate {

    String getRiskInfoByPatientId(int caseId, int patientIdType, String patientId, String mis);

    /**
     * 根据caseId获取举报信息
     * @param caseId
     * @return
     */
    ReportInfoVO getReportInfoByCaseId(int caseId);

    boolean judgeAccidentCaseStrategy(String title, String content);

    void addBackList(PreposeAddRiskBlackModel preposeAddBlackModel);

    RiskWordResult checkHitRiskWord(String content);
}
