package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.ActivityJoinModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponAccessModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.charity.client.model.activity.theme.ThemeActivityDetail;
import com.shuidihuzhu.charity.client.model.activity.theme.ThemeActivityInfo;
import com.shuidihuzhu.charity.client.vo.budgetmanagement.BudgetManagementVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-10-16
 */
public interface ICfActivityWeaponClientDelegate {

    OpResult<WeaponAccessModel> checkCaseAllowJoin(int caseId, int activityClass, Integer hugeSubsidyType, int serviceChargeId, int activityId);

    OpResult<WeaponAccessModel> requireJoinActivity(ActivityJoinModel activityJoinModel);

    String getActivityName(int caseId, int activityClass);

    /**
     * 爱心主题需要这样处理
     * @return
     */
    List<ThemeActivityDetail> getSimpleDetailList();

    ThemeActivityDetail getSimpleDetail(Integer activityId);

    List<ThemeActivityInfo> getThemeActivityInfoList();

    List<BudgetManagementVo> getBudgetManagementList();
}
