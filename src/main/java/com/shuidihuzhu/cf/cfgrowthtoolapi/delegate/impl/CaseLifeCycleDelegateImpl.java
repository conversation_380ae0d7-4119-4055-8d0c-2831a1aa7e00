package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfStatusEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfStatusEnums.CaseLifeCycleEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CaseLifeCircleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IClewPreproseMaterialService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.client.cf.admin.client.CfHospitalAuditClient;
import com.shuidihuzhu.client.cf.admin.model.HospitalAudit;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingOperationFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.QueryListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OneTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Case;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalTime;
import java.util.*;

/**
 * @author: fengxuan
 * @create 2020-09-03 12:03 下午
 * <p>
 * 根据环节和状态获取comment
 **/
@Slf4j
@Service
public class CaseLifeCycleDelegateImpl implements ICaseLifeCycleDelegate {
    public static final String DEFAULT_TEXT = "预计提交5分钟内能有审核结果，请耐心等待";
    public static final String CLIENT_SERVICE_TEXT = "预计10分钟内能有审核结果，超出审核时效，请联系小鲸鱼客服（值班时间9:00-21:30）";

    //医疗工单审核待审核文案
    public static final String PROVINCE_LEADER_TEXT_V1 = "已为你申请医学组复核，预计10分钟内能有审核结果，超出审核时效，请至飞书专群【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）";
    public static final String DIRECT_VOLUNTEER_TEXT_V1 = "已为你申请医学组复核，预计10分钟内能有审核结果，超出审核时效，请联系业务经理至飞书专群【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）";
    public static final String PROXY_VOLUNTEER_TEXT_V1 = "已为你申请医学组复核，预计10分钟内能有审核结果，超出审核时效，请联系上级至飞书专群【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00";

    //医疗工单审核驳回文案
    public static final String DIRECT_VOLUNTEER_TEXT_V2 = "如对审核结果有疑问，请联系业务经理至飞书专群【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）";
    public static final String PROVINCE_LEADER_TEXT_V2 = "如对审核结果有疑问，请至飞书专群【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）";
    public static final String PROXY_VOLUNTEER_TEXT_V2 = "如对审核结果有疑问，请联系上级至飞书专群【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）";

    //目标金额审核工单处理中文案
    public static final String PROVINCE_LEADER_TEXT_V4 = "预计10分钟内能有审核结果，请耐心等待。如有疑问，请至飞书【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）";
    public static final String DIRECT_VOLUNTEER_TEXT_V4 = "预计10分钟内能有审核结果，请耐心等待。如有疑问，请联系业务经理至飞书【业务风控对接群】，按群内要求反馈医学组值班人员（值班时间：9:00-23:00）";
    public static final String PROXY_VOLUNTEER_TEXT_V4 = "预计10分钟内能有审核结果，请耐心等待。如有疑问，请联系上级至飞书【业务风控对接群】，按群内要求反馈医学组值班人员（值班时间：9:00-23:00）";
    public static final String DIRECT_VOLUNTEER_TEXT_AT_NIGHT_V4 = "请您耐心等待，审核人员将于早9：00后优先进行审核";
    public static final String PROXY_VOLUNTEER_TEXT_AT_NIGHT_V4 = "请您耐心等待，审核人员将于早9：00后优先进行审核";

    //目标金额审核工单处理完成文案
    public static final String PROVINCE_LEADER_TEXT_V5 = "如对目标金额审核结果有疑问，请至飞书专群【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）";
    public static final String DIRECT_VOLUNTEER_TEXT_V5 = "如对目标金额审核结果有疑问，请联系业务经理至飞书专群【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）";
    public static final String PROXY_VOLUNTEER_TEXT_V5 = "如对目标金额审核结果有疑问，请联系上级至飞书专群【业务风控对接群】联系医学组值班人员（值班时间：9:00-23:00）";
    public static final String HIGH_RISK_AMOUNT_REJECT = "如对目标金额审核结果有疑问，请联系小鲸鱼客服（值班时间9:00-21:30）";

    // 高风险工单处理中文案
    public static final String CREATE_AT_NIGHT_TEXT = "请您耐心等待，审核人员将于早8:30后优先进行审核";
    public static final String DIRECT_VOLUNTEER_TEXT_V8 = "预计30分钟内能有审核结果，超出审核时效，9:00-21:30期间请联系小鲸鱼客服；晚21：30-次日0:00可联系业务经理至飞书群【夜间催初审工牌/名牌报备群】联系值班人员";
    public static final String PROXY_VOLUNTEER_TEXT_V8 = "预计30分钟内能有审核结果，超出审核时效，9:00-21:30期间请联系小鲸鱼客服；晚21：30-次日0:00可联系上级至飞书群【夜间催初审工牌/名牌报备群】联系值班人员";
    public static final String NOT_EMERGENCY_NIGHT_TEXT = "请您耐心等待，审核人员将于早8：00后优先进行审核";
    public static final String DIRECT_VOLUNTEER_TEXT_V10 = "预计40分钟内能有审核结果，超出审核时效请联系业务经理至飞书【业务风控对接群】，按群内要求反馈应急组值班人员（值班时间：8:30-23:00）";
    public static final String PROXY_VOLUNTEER_TEXT_V10 = "预计40分钟内能有审核结果，超出审核时效请联系上级至飞书【业务风控对接群】，按群内要求反馈应急组值班人员（值班时间：8:30-23:00）";

    //高风险工单被驳回文案
    public static final String DIRECT_VOLUNTEER_TEXT_V9 = "如有疑问，9:00-21:30期间请联系小鲸鱼客服；晚21：30-次日0:00可联系业务经理至飞书群【夜间催初审工牌/名牌报备群】联系值班人员";
    public static final String PROXY_VOLUNTEER_TEXT_V9 = "如有疑问，9:00-21:30期间请联系小鲸鱼客服；晚21：30-次日0:00可联系上级至飞书群【夜间催初审工牌/名牌报备群】联系值班人员";
    public static final String PROXY_VOLUNTEER_TEXT_V11 = "如有问题，请联系上级至飞书【业务风控对接群】，按群内要求反馈应急组值班人员（值班时间：8:30-23:00）";
    public static final String DIRECT_VOLUNTEER_TEXT_V11 = "如有问题，请联系业务经理至飞书【业务风控对接群】，按群内要求反馈应急组值班人员（值班时间：8:30-23:00）";

    // 二次审核工单待审核文案
    public static final String NIGHT_PROCESSING_TEXT = "请您耐心等待，审核人员将于早7：00后优先进行审核";
    public static final String DIRECT_VOLUNTEER_TEXT_V6 = "预计10分钟内审核完成，超出审核时效，9:00-21:30期间请联系小鲸鱼客服；晚21：30后如需催审或特殊报备可联系业务经理至飞书群【夜间催初审工牌/名牌报备群】联系值班人员（值班时间：21：30-次日1：00）";
    public static final String PROXY_VOLUNTEER_TEXT_V6 = "预计10分钟内审核完成，超出审核时效，9:00-21:30期间请联系小鲸鱼客服；晚21：30后如需催审或特殊报备可联系上级至飞书群【夜间催初审工牌/名牌报备群】联系值班人员（值班时间：21：30-次日1：00）";

    // 图片录入工单、文章录入待审核文案
    public static final String NIGHT_PROCESSING_TEXT_V2 = "请您耐心等待，审核人员将于早7：00后优先进行审核";
    public static final String DIRECT_VOLUNTEER_TEXT_V14 = "预计10分钟内审核完成，超出审核时效，9:00-21:30期间请联系小鲸鱼客服；晚21：30后如需催审或特殊报备可联系业务经理至飞书群【夜间催初审工牌/名牌报备群】联系值班人员（值班时间：21：30-次日1：00）";
    public static final String PROXY_VOLUNTEER_TEXT_V14 = "预计10分钟内审核完成，超出审核时效，9:00-21:30期间请联系小鲸鱼客服；晚21：30后如需催审或特殊报备可联系上级至飞书群【夜间催初审工牌/名牌报备群】联系值班人员（值班时间：21：30-次日1：00）";

    // 二次审核工单被驳回文案
    public static final String DIRECT_VOLUNTEER_TEXT_V7 = "如有疑问，9:00-21:30期间请联系小鲸鱼客服；晚21：30-次日1:00可联系业务经理至飞书群【夜间催初审工牌/名牌报备群】联系值班人员";
    public static final String PROXY_VOLUNTEER_TEXT_V7 = "如有疑问，9:00-21:30期间请联系小鲸鱼客服；晚21：30-次日1:00可联系上级至飞书群【夜间催初审工牌/名牌报备群】联系值班人员";

    // 图文处理待审核文案
    public static final String DIRECT_VOLUNTEER_TEXT_V12 = "预计10分钟内审核完成，超出审核时效，请联系小鲸鱼客服（值班时间：9:00-21:30）";
    public static final String PROXY_VOLUNTEER_TEXT_V12 = "预计10分钟内审核完成，超出审核时效，请联系小鲸鱼客服（值班时间：9:00-21:30）";
    public static final String TU_WEN_AT_NIGHT = "请您耐心等待，审核人员将于早7：00后优先进行审核";

    // 图文处理被驳回文案
    public static final String TU_WEN_REJECT_TEXT = "如有疑问，请联系小鲸鱼客服（值班时间9:00-21:30）";

    // 材料审核待审核文案
    public static final String CAI_LIAO_NIGHT_TEXT = "请您耐心等待，审核人员将于早7：00后优先进行审核";
    public static final String PROXY_VOLUNTEER_TEXT_V13 = "预计10分钟内能有审核结果，超出审核时效，请联系小鲸鱼客服（值班时间9:00-21:30）";
    public static final String DIRECT_VOLUNTEER_TEXT_V13 = "预计10分钟内能有审核结果，超出审核时效，请联系小鲸鱼客服（值班时间9:00-21:30）";

    // 材料审核被驳回文案
    public static final String CAI_LIAO_REJECTED_TEXT = "如有疑问，请联系小鲸鱼客服（值班时间9:00-21:30）";

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private ISeaAccountServiceDelegate seaAccountServiceDelegate;

    @Autowired
    private CfHospitalAuditClient cfHospitalAuditClient;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private CrowdfundingOperationFeignClient crowdfundingOperationFeignClient;

    @Override
    public void addLifeCycleComment(List<CaseLifeCircleModel> caseLifeCircleModels, int caseId) {
        if (CollectionUtils.isEmpty(caseLifeCircleModels)) {
            return;
        }
        CrowdfundingVolunteer volunteer = getVolunteerByCaseId(caseId);
        //只要点亮了就按照状态展示
        for (CaseLifeCircleModel caseLifeCircleModel : caseLifeCircleModels) {
            if (caseLifeCircleModel.getNodeStatus() != null && !ObjectUtils.nullSafeEquals(caseLifeCircleModel.getNodeStatus(), "TODO")) {
                CaseLifeCycleEnum caseLifeCycleEnum = CfStatusEnums.parseCaseLifeCycle(caseLifeCircleModel.getName());
                if (caseLifeCycleEnum != null) {
                    //设置对应的comment
                    switch (caseLifeCycleEnum) {
                        case chushen:
                        case qianzhishenghen:
                            //初审
                            createCommentWhenChushen(caseLifeCircleModel, caseId, volunteer);
                            break;
                        case cailiao:
                            //材料审核
                            createCommentWhenCailiao(caseLifeCircleModel, volunteer);
                            break;
                        case yiyuanheshi:
                            //医院核实
                            createCommentWhenYiyuan(caseLifeCircleModel, caseId);
                            break;
                        case jubao:
                            //举报环节
                            createCommentWhenJubao(caseLifeCircleModel, caseId);
                            break;
                        case targetamount:
                            //目标金额审核
                            createCommentWhenTargetAmount(caseLifeCircleModel, caseId, volunteer);
                            break;
                        case tuwenchuli:
                            //图文处理
                            createCommentWhenTuWen(caseLifeCircleModel, caseId, volunteer);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    private void createCommentWhenTuWen(CaseLifeCircleModel caseLifeCircleModel, int caseId, CrowdfundingVolunteer volunteer) {
        Response<WorkOrderVO> lastOrderResp = cfWorkOrderClient.getLastWorkOrderByTypes(caseId,
                Lists.newArrayList(WorkOrderType.content.getType()));
        if (lastOrderResp == null || lastOrderResp.getData() == null) {
            return;
        }
        WorkOrderVO workOrder = lastOrderResp.getData();
        HandleResultEnum handleResult = HandleResultEnum.getFromType(workOrder.getHandleResult());
        if (handleResult == HandleResultEnum.undoing || handleResult == HandleResultEnum.doing) {
            if (workOrderCreateBetween(workOrder, 0, 58, 7, 0)) {
                caseLifeCircleModel.setComment(TU_WEN_AT_NIGHT);
            } else {
                if (isVolunteerDirect(volunteer)) {
                    caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V12);
                } else if (isVolunteerProxy(volunteer)) {
                    caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V12);
                }
            }
        } else if (handleResult == HandleResultEnum.audit_reject) {
            caseLifeCircleModel.setComment(TU_WEN_REJECT_TEXT);
        }
    }


    private void createCommentWhenChushen(CaseLifeCircleModel caseLifeCircleModel, int caseId, CrowdfundingVolunteer volunteer) {
        String status = caseLifeCircleModel.getStatus();
        Set<String> approveMsg = Sets.newHashSet(FirstApproveStatusEnum.APPLYING.getApproveMsg(), FirstApproveStatusEnum.APPLY_FAIL.getApproveMsg());
        if (!approveMsg.contains(status)) {
            return;
        }
        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, Lists.newArrayList(WorkOrderType.highriskshenhe.getType(),
                WorkOrderType.yiliaoshenhe.getType(),
                WorkOrderType.ai_erci.getType(),
                WorkOrderType.ai_content.getType(),
                WorkOrderType.ai_photo.getType()));
        if (response == null || response.getData() == null) {
            if (FirstApproveStatusEnum.APPLYING.getApproveMsg().equals(status)) {
                caseLifeCircleModel.setComment(DEFAULT_TEXT);
            }
            return;
        }

        WorkOrderVO workOrderVO = response.getData();
        int orderType = workOrderVO.getOrderType();
        if (orderType == WorkOrderType.ai_erci.getType()) {
            processErCiWhenChushen(caseLifeCircleModel, volunteer, workOrderVO);
        } else if (orderType == WorkOrderType.highriskshenhe.getType()) {
            processHighRiskWhenChushen(caseLifeCircleModel, workOrderVO, volunteer);
        } else if (orderType == WorkOrderType.yiliaoshenhe.getType()) {
            processYiLiaoWhenChushen(workOrderVO, caseLifeCircleModel, volunteer);
        } else if (orderType == WorkOrderType.ai_photo.getType()) {
            processAiPhoto(workOrderVO, caseLifeCircleModel, volunteer);
        } else if (orderType == WorkOrderType.ai_content.getType()) {
            processAiContent(workOrderVO, caseLifeCircleModel, volunteer);
        } else if (FirstApproveStatusEnum.APPLYING.getApproveMsg().equals(status)) {    // 默认兜底
            caseLifeCircleModel.setComment(DEFAULT_TEXT);
        }
    }

    // 二次审核工单的备注
    private void processErCiWhenChushen(CaseLifeCircleModel caseLifeCircleModel, CrowdfundingVolunteer crowdfundingVolunteer, WorkOrderVO workOrderVO) {
        String status = caseLifeCircleModel.getStatus();
        int caseId = workOrderVO.getCaseId();
        if (FirstApproveStatusEnum.APPLYING.getApproveMsg().equals(status)) {
            if (isVolunteerDirect(crowdfundingVolunteer)) {
                caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V6);
            } else if (isVolunteerProxy(crowdfundingVolunteer)) {
                caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V6);
            } else {
                caseLifeCircleModel.setComment(CLIENT_SERVICE_TEXT);
            }

            if (workOrderCreateBetween(workOrderVO, 0, 58, 7, 0)) {
                caseLifeCircleModel.setComment(NIGHT_PROCESSING_TEXT);
            }
        } else if (FirstApproveStatusEnum.APPLY_FAIL.getApproveMsg().equals(status)) {
            if (isVolunteerDirect(crowdfundingVolunteer)) {
                caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V7);
            } else if (isVolunteerProxy(crowdfundingVolunteer)) {
                caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V7);
            }

            if (targetAmountIsRejected(caseId) && checkTargetAmountWorkOrderIfRejected(caseId)) {
                if (isVolunteerDirect(crowdfundingVolunteer)) {
                    caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V5);
                } else if (isVolunteerProxy(crowdfundingVolunteer)) {
                    caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V5);
                }
            }
        }
    }

    // 判断目标金额审核工单是否被驳回
    private boolean checkTargetAmountWorkOrderIfRejected(int caseId) {
        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrder(caseId, WorkOrderType.target_amount_reasonable_audit.getType());
        if (response == null || response.getData() == null) {
            return false;
        }
        WorkOrderVO workOrderVO = response.getData();
        return workOrderVO.getHandleResult() == HandleResultEnum.audit_reject.getType();
    }

    // 图片录入工单的备注
    private void processAiPhoto(WorkOrderVO workOrderVO, CaseLifeCircleModel caseLifeCircleModel, CrowdfundingVolunteer volunteer) {
        Set<Integer> handlingIds = Sets.newHashSet(HandleResultEnum.undoing.getType(), HandleResultEnum.doing.getType());
        if (!handlingIds.contains(workOrderVO.getHandleResult())) {
            return;
        }
        if (workOrderCreateBetween(workOrderVO, 0, 58, 7, 0)) {
            caseLifeCircleModel.setComment(NIGHT_PROCESSING_TEXT_V2);
        } else {
            if (isVolunteerDirect(volunteer)) {
                caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V14);
            } else if (isVolunteerProxy(volunteer)) {
                caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V14);
            }
        }
    }
    // 文章录入工单的备注
    private void processAiContent(WorkOrderVO workOrderVO, CaseLifeCircleModel caseLifeCircleModel, CrowdfundingVolunteer volunteer) {
        Set<Integer> handlingIds = Sets.newHashSet(HandleResultEnum.undoing.getType(), HandleResultEnum.doing.getType());
        if (!handlingIds.contains(workOrderVO.getHandleResult())) {
            return;
        }
        if (workOrderCreateBetween(workOrderVO, 0, 58, 7, 0)) {
            caseLifeCircleModel.setComment(NIGHT_PROCESSING_TEXT_V2);
        } else {
            if (isVolunteerDirect(volunteer)) {
                caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V14);
            } else if (isVolunteerProxy(volunteer)) {
                caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V14);
            }
        }
    }
    // 医疗审核工单的备注
    private void processYiLiaoWhenChushen(WorkOrderVO workOrderVO, CaseLifeCircleModel caseLifeCircleModel, CrowdfundingVolunteer volunteer) {
        Set<Integer> handlingIds = Sets.newHashSet(HandleResultEnum.undoing.getType(), HandleResultEnum.doing.getType(), HandleResultEnum.not_auto_assign.getType());
        Set<Integer> handledIds = Sets.newHashSet(HandleResultEnum.audit_reject.getType(), HandleResultEnum.stop_case.getType());
        if (handlingIds.contains(workOrderVO.getHandleResult())) {
            if (isProvinceLeader(volunteer)) {
                caseLifeCircleModel.setComment(PROVINCE_LEADER_TEXT_V1);
            } else if (isVolunteerDirect(volunteer)) {
                caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V1);
            }
            if (isVolunteerProxy(volunteer)) {
                caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V1);
            }
        } else if (handledIds.contains(workOrderVO.getHandleResult())) {
            if (isProvinceLeader(volunteer)) {
                caseLifeCircleModel.setComment(PROVINCE_LEADER_TEXT_V2);
            } else if (isVolunteerDirect(volunteer)) {
                caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V2);
            }
            if (isVolunteerProxy(volunteer)) {
                caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V2);
            }
        }
    }
    // 高风险工单的备注
    private void processHighRiskWhenChushen(CaseLifeCircleModel caseLifeCircleModel, WorkOrderVO workOrderVO, CrowdfundingVolunteer volunteer) {
        String status = caseLifeCircleModel.getStatus();
        int caseId = workOrderVO.getCaseId();
        if (workOrderVO.getAssignGroupId() == 1) { // 应急组
            if (FirstApproveStatusEnum.APPLYING.getApproveMsg().equals(status)) {
                if (workOrderCreateBetween(workOrderVO, 22, 50, 8, 30)) {
                    caseLifeCircleModel.setComment(CREATE_AT_NIGHT_TEXT);
                } else {
                    if (isVolunteerDirect(volunteer)) {
                        caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V10);
                    } else if (isVolunteerProxy(volunteer)) {
                        caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V10);
                    }
                }
            }

            if (FirstApproveStatusEnum.APPLY_FAIL.getApproveMsg().equals(status)) {
                if (isVolunteerDirect(volunteer)) {
                    caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V11);
                } else if (isVolunteerProxy(volunteer)) {
                    caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V11);
                }
                if (targetAmountIsRejected(caseId) && checkTargetAmountWorkOrderIfRejected(caseId)) {
                    if (isVolunteerDirect(volunteer)) {
                        caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V5);
                    } else if (isVolunteerProxy(volunteer)) {
                        caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V5);
                    }
                }
            }
        } else {
            if (FirstApproveStatusEnum.APPLYING.getApproveMsg().equals(status)) {
                if (workOrderCreateBetween(workOrderVO, 23, 58, 8, 0)) {
                    caseLifeCircleModel.setComment(NOT_EMERGENCY_NIGHT_TEXT);
                } else {
                    if (isVolunteerDirect(volunteer)) {
                        caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V8);
                    } else if (isVolunteerProxy(volunteer)) {
                        caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V8);
                    }
                }
            } else if (FirstApproveStatusEnum.APPLY_FAIL.getApproveMsg().equals(status)) {
                if (isVolunteerDirect(volunteer)) {
                    caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V9);
                } else if (isVolunteerProxy(volunteer)) {
                    caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V9);
                }

                if (targetAmountIsRejected(caseId) && checkTargetAmountWorkOrderIfRejected(caseId)) {
                    if (isVolunteerDirect(volunteer)) {
                        caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V5);
                    } else if (isVolunteerProxy(volunteer)) {
                        caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V5);
                    }
                }
            }
        }
    }
    private boolean workOrderCreateBetween(Date date, int fromHour, int fromMinute, int toHour, int toMinute) {
        if (date == null) {
            return false;
        }
        LocalTime begin = LocalTime.of(fromHour, fromMinute);
        LocalTime end = LocalTime.of(toHour, toMinute);
        LocalTime time = LocalTime.of(DateUtil.hour(date, true), DateUtil.minute(date));

        // 处理跨午夜的情况
        if (begin.isBefore(end)) {
            return !time.isBefore(begin) && !time.isAfter(end);
        } else {
            // 跨午夜情况：例如 23:00 - 05:00
            return !time.isBefore(begin) || !time.isAfter(end);
        }
    }

    private boolean workOrderCreateBetween(WorkOrderVO workOrderVO, int fromHour, int fromMinute, int toHour, int toMinute) {
        return workOrderCreateBetween(workOrderVO.getCreateTime(), fromHour, fromMinute, toHour, toMinute);
    }

    private void createCommentWhenCailiao(CaseLifeCircleModel caseLifeCircleModel, CrowdfundingVolunteer volunteer) {
        String status = caseLifeCircleModel.getStatus();
        Date time = null;
        if (StringUtils.isNotEmpty(caseLifeCircleModel.getTime())) {
            time = DateUtil.parse(caseLifeCircleModel.getTime(), "yyyy-MM-dd HH:mm:ss");
        }

        if (CrowdfundingStatus.SUBMITTED.getApproveMsg().equals(status)) { // 待审核
            if (workOrderCreateBetween(time, 23, 58, 7, 0)) {
                caseLifeCircleModel.setComment(CAI_LIAO_NIGHT_TEXT);
            } else {
                if (isVolunteerDirect(volunteer)) {
                    caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V13);
                } else if (isVolunteerProxy(volunteer)) {
                    caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V13);
                }
            }
        } else if (CrowdfundingStatus.APPROVE_DENIED.getApproveMsg().equals(status)) { //审核驳回
            caseLifeCircleModel.setComment(CAI_LIAO_REJECTED_TEXT);
        }
    }

    private void createCommentWhenYiyuan(CaseLifeCircleModel caseLifeCircleModel, int caseId) {
        String status = caseLifeCircleModel.getStatus();
        if (ObjectUtils.nullSafeEquals(status, "下发")) {
            log.debug("获取医院核实下发信息");
            CrowdfundingInfo caseInfoById = crowdFundingFeignDelegate.getCaseInfoById(caseId);
            if (caseInfoById == null) {
                return;
            }
            //下发
            Response<HospitalAudit> hospitalAudit = cfHospitalAuditClient.getHospitalAudit(caseInfoById.getInfoId());
            log.debug("医院核实下发信息:{}", JSON.toJSONString(hospitalAudit));
            if (hospitalAudit.ok() && hospitalAudit.getData() != null) {
                caseLifeCircleModel.setComment(Objects.requireNonNull(hospitalAudit.getData()).getSupplyReason());
            }
        }
    }

    private void createCommentWhenJubao(CaseLifeCircleModel caseLifeCircleModel, int caseId) {
        log.debug("填充举报处理人");
        //不管是哪个状态都取对应的举报处理人
        List<Integer> reportTypes = ClassifyTypeEnum.getByOneLevel(OneTypeEnum.report.getType());
        Response<WorkOrderVO> lastWorkOrder = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, reportTypes);
        log.debug("举报工单信息:{}", JSON.toJSONString(lastWorkOrder));
        if (lastWorkOrder.getData() == null) {
            return;
        }
        WorkOrderVO data = lastWorkOrder.getData();
        if (data.getOperatorId() > 0) {
            List<Long> userIds = Lists.newArrayList();
            userIds.add(data.getOperatorId());
            //获取处理人信息
            List<AdminUserAccountModel> accounts = seaAccountServiceDelegate.getUserAccountsByIds(userIds);
            if (CollectionUtils.isNotEmpty(accounts)) {
                AdminUserAccountModel accountModel = accounts.get(0);
                log.debug("人员信息:{}", JSON.toJSONString(accountModel));
                caseLifeCircleModel.setComment("处理人: " + accountModel.getName());
            }
        }
    }

    private void createCommentWhenTargetAmount(CaseLifeCircleModel caseLifeCircleModel, int caseId, CrowdfundingVolunteer volunteer) {
        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrder(caseId, WorkOrderType.target_amount_reasonable_audit.getType());
        if (response == null || response.getData() == null) {
            return;
        }
        WorkOrderVO workOrderVO = response.getData();
        if (CaseLifeCircleModel.NodeStatusEnum.PROCESSING.name().equals(caseLifeCircleModel.getNodeStatus())) { // 目标金额工单待审核
            targetAmountProcessing(caseLifeCircleModel, volunteer, caseId);
            if (workOrderCreateBetween(workOrderVO, 22, 50, 9, 0)) {
                nightTargetAmountProcessing(caseLifeCircleModel, volunteer);
            }
        }
    }
    // 夜间高风险工单待审核
    private void nightTargetAmountProcessing(CaseLifeCircleModel caseLifeCircleModel, CrowdfundingVolunteer crowdfundingVolunteer) {
        if (isVolunteerDirect(crowdfundingVolunteer)) {
            caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_AT_NIGHT_V4);
        }
        if (isVolunteerProxy(crowdfundingVolunteer)) {
            caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_AT_NIGHT_V4);
        }
    }

    private void tartgetAmountRejected(CaseLifeCircleModel caseLifeCircleModel, WorkOrderVO targetAmountWorkOrder, CrowdfundingVolunteer volunteer) {
        int caseId = targetAmountWorkOrder.getCaseId();
        caseLifeCircleModel.setStatus("驳回");
        caseLifeCircleModel.setNodeStatus("REJECT");
        Response<WorkOrderVO> response = cfWorkOrderClient.getLastWorkOrderByTypes(caseId, Lists.newArrayList(WorkOrderType.highriskshenhe.getType(), WorkOrderType.ai_erci.getType()));
        if (response == null || response.getData() == null) {
            return;
        }
        WorkOrderVO initialWorkOrder = response.getData();
        int orderType = initialWorkOrder.getOrderType();
        int handleResult = initialWorkOrder.getHandleResult();
        if (handleResult != HandleResultEnum.audit_reject.getType()) {
            return;
        }
        boolean amountIsRejected = targetAmountIsRejected(caseId);
        if (orderType == WorkOrderType.ai_erci.getType() && amountIsRejected) {
            if (isProvinceLeader(volunteer)) {
                caseLifeCircleModel.setComment(PROVINCE_LEADER_TEXT_V5);
            } else if (isVolunteerDirect(volunteer)) {
                caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V5);
            }

            if (isVolunteerProxy(volunteer)) {
                caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V5);
            }
        } else if (orderType == WorkOrderType.highriskshenhe.getType() && amountIsRejected) {
            caseLifeCircleModel.setComment(HIGH_RISK_AMOUNT_REJECT);
        }
    }

    // 判断驳回项是否有「目标金额」
    private boolean targetAmountIsRejected(int caseId) {
        Response<String> response = crowdfundingOperationFeignClient.selectCrowdfundingInitialAuditInfoByCaseId(caseId);
        if (response == null || response.notOk()) {
            return false;
        }

        String data = response.getData();
        CrowdfundingInitialAuditInfo crowdfundingInitialAuditInfo = JSON.parseObject(data, CrowdfundingInitialAuditInfo.class);
        String rejectDetail = crowdfundingInitialAuditInfo.getRejectDetail();
        if (StringUtils.isEmpty(rejectDetail)) {
            return false;
        }
        Map<Integer, List<InitialAuditItem.RejectReason>> rejectDetailMap = JSON.parseObject(crowdfundingInitialAuditInfo.getRejectDetail(), new TypeReference<Map<Integer, List<InitialAuditItem.RejectReason>>>() {});
        return rejectDetailMap != null && rejectDetailMap.containsKey(10);
    }
    private void targetAmountProcessing(CaseLifeCircleModel caseLifeCircleModel, CrowdfundingVolunteer volunteer, int caseId) {
        CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegate.getCaseInfoById(caseId);
        int amount = Optional.ofNullable(crowdfundingInfo).map(CrowdfundingInfo::getTargetAmount).orElse(0);
        int threshold = 50000000;
        if (amount <= threshold) {
            caseLifeCircleModel.setComment(CLIENT_SERVICE_TEXT);
        } else {
            if (isProvinceLeader(volunteer)) {
                caseLifeCircleModel.setComment(PROVINCE_LEADER_TEXT_V4);
            } else if (isVolunteerDirect(volunteer)) {
                caseLifeCircleModel.setComment(DIRECT_VOLUNTEER_TEXT_V4);
            }

            if (isVolunteerProxy(volunteer)) {
                caseLifeCircleModel.setComment(PROXY_VOLUNTEER_TEXT_V4);
            }
        }
    }

    // 是否业务经理
    private boolean isProvinceLeader(CrowdfundingVolunteer volunteer) {
        if (volunteer == null) {
            return false;
        }
        return volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel();
    }
    // 顾问是否直营
    private boolean isVolunteerDirect(CrowdfundingVolunteer volunteer) {
        if (volunteer == null) {
            return false;
        }

        Set<Integer> set = Sets.newHashSet(CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel(),
                CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel(),
                CrowdfundingVolunteerEnum.RoleEnum.PRACTICE_AID.getLevel(),
                CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AID.getLevel());

        return set.contains(volunteer.getLevel());
    }

    // 顾问是否渠道
    private boolean isVolunteerProxy(CrowdfundingVolunteer volunteer) {
        if (volunteer == null) {
            return false;
        }
        Set<Integer> set = Sets.newHashSet(CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_BOSS.getLevel(),
                CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_PROVINCE.getLevel(),
                CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_COMMON.getLevel());

        return set.contains(volunteer.getLevel());
    }

    private CrowdfundingVolunteer getVolunteerByCaseId(Integer caseId) {
        CfBdCaseInfoDo bdCase = cfBdCaseInfoService.getBdCaseInfoByInfoId(caseId);
        if (bdCase == null) {
            return null;
        }
        // 获取顾问信息
        String uniqueCode = bdCase.getUniqueCode();
        CrowdfundingVolunteer volunteer;
        volunteer = cfVolunteerService.getByUniqueCode(uniqueCode);

        return volunteer;
    }
}
