package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.PepClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.strategy.PersonPepCasePoolContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mapper.HomeCaseRemindModelMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.GrowthtoolCasePoolModelVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.HomeCaseRemindModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.HomeCaseRemindVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.BdCrmForwardingRecipesService;
import com.shuidihuzhu.cf.client.performance.*;
import com.shuidihuzhu.cf.client.performance.calResult.*;
import com.shuidihuzhu.cf.client.performance.enums.BizType;
import com.shuidihuzhu.cf.client.performance.enums.PepEnums;
import com.shuidihuzhu.cf.client.performance.model.*;
import com.shuidihuzhu.cf.client.performance.param.PepUserLotParam;
import com.shuidihuzhu.cf.client.performance.query.*;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2022-07-28 21:10
 **/
@Slf4j
@Service
public class PepClientDelegateImpl implements PepClientDelegate {

    @Autowired
    private PepClient pepClient;

    @Autowired
    private PepTaskClient pepTaskClient;

    @Autowired
    private HomeCaseRemindModelMapper homeCaseRemindModelMapper;

    @Autowired
    private BdCrmForwardingRecipesService bdCrmForwardingRecipesService;

    @Autowired
    private PersonPepCasePoolContext personPepCasePoolContext;


    @Override
    public UserCalResult performanceResult(PerformanceResultParam param) {
        Response<UserCalResult> userCalResultResponse = pepClient.performanceResult(param);
        log.info("performanceResult param:{} result:{}", param, userCalResultResponse);
        if (userCalResultResponse.notOk() || userCalResultResponse.getData() == null) {
            return null;
        }
        return userCalResultResponse.getData();
    }

    @Override
    public List<ProcedureForCModel> listAllProcedure(String userId, int bizType) {
        Response<List<ProcedureForCModel>> listResponse = pepClient.listAllProcedure(userId, bizType);
        log.info("listAllProcedure param:{} result:{}", userId, listResponse);
        if (CollectionUtils.isEmpty(listResponse.getData())) {
            return Lists.newArrayList();
        }
        return listResponse.getData();
    }

    @Override
    public List<ProcedureForCModel> listRecentProcedure(int bizType, int subBizType, int limit) {
        Response<List<ProcedureForCModel>> listResponse = pepClient.listRecentProcedure(bizType, subBizType, limit);
        log.info("listRecentProcedure result:{}", listResponse);
        if (CollectionUtils.isEmpty(listResponse.getData())) {
            return Lists.newArrayList();
        }
        return listResponse.getData();
    }

    @Override
    public ProcedureForCModel getByProduceId(long procedureId) {
        Response<ProcedureForCModel> procedureResponse = pepClient.getByProduceId(procedureId);
        log.info("getByProduceId param:{} result:{}", procedureId, procedureResponse);
        if (procedureResponse.notOk() || procedureResponse.getData() == null) {
            return null;
        }
        return procedureResponse.getData();
    }

    @Override
    public List<Long> listLotId(LotQuery lotQuery) {
        Response<List<Long>> listResponse = pepClient.listLotId(lotQuery);
        log.info("listLotId param:{} result:{}", lotQuery, listResponse);
        if (listResponse.notOk() || CollectionUtils.isEmpty(listResponse.getData())) {
            return null;
        }
        return listResponse.getData();
    }

    @Override
    public List<FactDetailModel> listFactCalDetail(PerformanceResultParam param) {
        Response<List<FactDetailModel>> listResponse = pepClient.listFactCalDetail(param);
        log.info("listFactCalDetail param:{} result:{}", param, listResponse);
        if (listResponse.notOk() || CollectionUtils.isEmpty(listResponse.getData())) {
            return null;
        }
        return listResponse.getData();
    }

    @Override
    public JSONObject getByLotData(String userId, long lotId, String lotUniqueValue) {
        Response<JSONObject> lotDataResponse = pepClient.getByLotData(userId, lotId, lotUniqueValue);
        log.info("getByLotData param:{} result:{}", lotUniqueValue, lotDataResponse);
        if (lotDataResponse.notOk() || lotDataResponse.getData() == null) {
            return null;
        }
        return lotDataResponse.getData();
    }

    @Override
    public PepUserLotData listByLotIdAndUniqueValue(PepUserLotParam pepUserLotParam) {
        Response<PepUserLotData> lotDataResponse = pepClient.listByLotIdAndUniqueValue(pepUserLotParam);
        log.debug("listByLotIdAndUniqueValue param:{} result:{}", pepUserLotParam, lotDataResponse);
        if (lotDataResponse.notOk() || lotDataResponse.getData() == null) {
            return null;
        }
        return lotDataResponse.getData();
    }


    @Override
    public LotInfo getLotInfo(long lotId) {
        Response<LotInfo> lotInfoById = pepClient.getLotInfoById(lotId);
        log.info("getLotInfo:{}", lotInfoById);
        return lotInfoById.getData();
    }

    @Override
    public List<UserCalResult> teamUserResult(List<String> userIds, long procedureId) {
        TeamUserResultParam teamUserResultParam = new TeamUserResultParam();
        teamUserResultParam.setUserIds(userIds);
        teamUserResultParam.setProcedureId(procedureId);
        Response<List<UserCalResult>> listResponse = pepClient.teamUserResult(teamUserResultParam);
        return listResponse.getData();
    }

    @Override
    public HomePerformanceBanner homePerformanceBanner(String userId) {
        Response<HomePerformanceBanner> homePerformanceBannerResponse = pepTaskClient.homePerformanceBanner(userId);
        log.info("userId:{},homePerformanceBannerResponse:{}", userId, homePerformanceBannerResponse);
        return homePerformanceBannerResponse.getData();
    }

    @Override
    public HomePerformanceBanner personPepBanner(String userId, long procedureId) {
        Response<HomePerformanceBanner> homePerformanceBannerResponse = pepTaskClient.personPepBanner(userId, procedureId);
        log.info("userId:{},procedureId:{},personPepBanner:{}", userId, procedureId, homePerformanceBannerResponse);
        return homePerformanceBannerResponse.getData();
    }

    @Override
    public HomeCaseRemindVo homeCaseRemind(String userId, int limit) {
        boolean permission = bdCrmForwardingRecipesService.permission(userId);
        if (!permission) {
            Response<List<GrowthtoolPepCaseScoreModel>> homeCaseRemindResponse = pepTaskClient.homeCaseRemind(userId);
            Optional<List<GrowthtoolPepCaseScoreModel>> optionalList = Optional.ofNullable(homeCaseRemindResponse).filter(Response::ok).map(Response::getData);
            if (optionalList.isEmpty()) {
                return null;
            }
            HomeCaseRemindVo result = new HomeCaseRemindVo();
            List<HomeCaseRemindModel> homeCaseRemindList = homeCaseRemindModelMapper.toList(optionalList.get());
            result.setGrey(false);
            if (CollectionUtils.isNotEmpty(homeCaseRemindList)) {
                homeCaseRemindList = homeCaseRemindList.stream().limit(limit).collect(Collectors.toList());
            }
            result.setHomeCaseRemindModels(homeCaseRemindList);
            return result;
        }
        //V1版本
        return bdCrmForwardingRecipesService.homeCaseRemindV1(userId,limit);
    }

    @Override
    public List<GrowthtoolPepCaseScoreModel> caseProgress(PepCaseProgressParam param) {
        Response<List<GrowthtoolPepCaseScoreModel>> listResponse = pepTaskClient.caseProgress(param);
        log.info("param:{}, caseProgress:{}", param, listResponse);
        return Optional.ofNullable(listResponse.getData()).orElse(Lists.newArrayList());
    }

    @Override
    public GrowthtoolCasePoolModelVo personPepCasePool(String userId, int factBizType, int caseType) {
        boolean permission = bdCrmForwardingRecipesService.permission(userId);
        if (!permission) {
            Response<GrowthtoolCasePoolModel> growthtoolCasePoolModelResponse = pepTaskClient.personPepCasePool(userId, factBizType);
            GrowthtoolCasePoolModel growthtoolCasePoolModel = Optional.ofNullable(growthtoolCasePoolModelResponse).filter(Response::ok).map(Response::getData).orElse(null);
            if(growthtoolCasePoolModel == null){
                return null;
            }
            List<HomeCaseRemindModel> homeCaseRemindList = homeCaseRemindModelMapper.toList(growthtoolCasePoolModel.getCaseScoreList());
            return GrowthtoolCasePoolModelVo.builder()
                    .grey(false)
                    .calDate(growthtoolCasePoolModel.getCalDate())
                    .caseNum(growthtoolCasePoolModel.getCaseNum())
                    .factAmount(growthtoolCasePoolModel.getFactAmount())
                    .caseScoreList(homeCaseRemindList)
                    .build();
        }
        //V1版本
        return personPepCasePoolContext.executeStrategyByPersonPepCasePool(userId, factBizType, caseType);
    }

    @Override
    public List<GrowthtoolUserCaseScoreModel> teamUserScore(PepTeamParam pepTeamParam) {
        Response<List<GrowthtoolUserCaseScoreModel>> listResponse = pepTaskClient.teamUserScore(pepTeamParam);
        log.info("param:{}, teamUserScore:{}", pepTeamParam, listResponse);
        return Optional.ofNullable(listResponse.getData()).orElse(Lists.newArrayList());
    }

    @Override
    public GrowthtoolCasePoolModel teamPepCasePool(PepTeamParam pepTeamParam) {
        Response<GrowthtoolCasePoolModel> listResponse = pepTaskClient.teamPepCasePool(pepTeamParam);
        log.info("param:{}, teamPepCasePool:{}", pepTeamParam, listResponse);
        return listResponse.getData();
    }
}
