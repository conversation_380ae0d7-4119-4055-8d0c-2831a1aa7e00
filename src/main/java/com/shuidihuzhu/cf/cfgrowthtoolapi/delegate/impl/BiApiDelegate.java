package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IBiApiDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.dataservice.bi.v1.BiApiClient;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.client.model.SearchDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2019/12/13 3:15 PM
 */
@Service
@Slf4j
public class BiApiDelegate implements IBiApiDelegate {
    @Autowired
    private BiApiClient biApiClient;

    @Override
    public OpResult<String> esQueryCustom(String sql) {
        SearchDto searchDto = new SearchDto();
        searchDto.setQuerySql(sql);
        Response response = biApiClient.esQueryCustom(searchDto);
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        String method = stackTrace[2].getMethodName();
        log.info(this.getClass().getSimpleName()+"  "+method+" elastic-sql:{}",sql);
        log.info(this.getClass().getSimpleName()+"  "+method+" esQueryCustom result:{}",response);
        if (response==null || response.getCode()!=CfGrowthtoolErrorCode.SUCCESS.getCode()){
            log.warn(this.getClass().getSimpleName()+"  "+method+" esQueryCustom fail 正在使用降级方案 dao查询");
            return OpResult.createFailResult(CfGrowthtoolErrorCode.RPC_ERROR);
        }
        return OpResult.createSucResult(JSON.toJSONString(response.getData()));
    }
}
