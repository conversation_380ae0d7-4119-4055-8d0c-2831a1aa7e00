package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserRpcResponse;
import com.shuidihuzhu.account.model.app.AppDeviceDto;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.feign.AppDeviceFeignClient;
import com.shuidihuzhu.client.feign.UserAccountWhaleFeignClient;
import com.shuidihuzhu.client.grpc.account.v1.feign.UserInfoServiceClient;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020-01-09
 */
@Service
@Slf4j
public class AccountServiceDelegate implements IAccountServiceDelegate {

    @Autowired
    private UserInfoServiceClient userInfoServiceClient;

    @Autowired
    private AppDeviceFeignClient appDeviceFeignClient;

    @Autowired
    private UserAccountWhaleFeignClient userAccountWhaleFeignClient;

    @Override
    public UserInfoModel getUserInfoByOpenId(String openId) {
        return userInfoServiceClient.getUserInfoByOpenId(openId);
    }

    @Override
    public UserInfoModel getByUnionId(String unionId) {
        return userInfoServiceClient.getByUnionId(unionId);
    }

    @Override
    public UserInfoModel getUserInfoByMobile(String mobile) {
        return userInfoServiceClient.getUserInfoByMobile(mobile);
    }

    @Override
    public UserInfoModel getUserInfoModelByUserId(long userId) {
        return userInfoServiceClient.getUserInfoByUserId(userId);
    }

    @Override
    public OpResult<UserInfoModel> getUserInfoByUserId(long userId) {
        try {
            UserInfoModel userInfoModel = userInfoServiceClient.getUserInfoByUserId(userId);
            log.info("AccountServiceDelegate getUserInfoByUserId :{},response:{}",userId,userInfoModel);
            if (userInfoModel!=null && StringUtils.isBlank(userInfoModel.getCryptoMobile())){
                log.info(" 用户{}未绑定手机号  userInfo:{}",userId,userInfoModel);
            }
            return OpResult.createSucResult(userInfoModel);
        }catch (Exception e){
            log.error("AccountServiceDelegate getUserInfoByUserId :{}",userId,e);
        }
        return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_ERROR);
    }

    @Override
    public List<UserInfoModel> getUserInfoByUserIdBatch(List<Long> userIds) {

        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }

        List<UserInfoModel> userModelResult = Lists.newArrayList();

        List<List<Long>> lists = Lists.partition(userIds, 1000);
        for (List<Long> list : lists) {
            userModelResult.addAll(userInfoServiceClient.getUserInfoByUserIdBatchV2(list));
        }

        return userModelResult;

    }
    @Override
    public void reportingDevice(AppDeviceDto appDeviceDto) {
        UserRpcResponse userRpcResponse1 = appDeviceFeignClient.bindingDevice(appDeviceDto);
        log.info("{} bindingDevice param:{} result:{}", this.getClass().getSimpleName(), JSON.toJSONString(appDeviceDto), JSON.toJSONString(userRpcResponse1));
    }
    @Override
    public void unbindingDevice(AppDeviceDto appDeviceDto) {
        UserRpcResponse userRpcResponse = appDeviceFeignClient.unbindingDevice(appDeviceDto);
        log.info("{} unbindingDevice param:{} result:{}", this.getClass().getSimpleName(), JSON.toJSONString(appDeviceDto), JSON.toJSONString(userRpcResponse));
    }

    @Override
    public List<UserInfoModel> getUserInfoByCryptoIdcards(List<String> cryptoIdcardList) {
        return userInfoServiceClient.getUserInfoByCryptoIdcards(cryptoIdcardList);
    }

    @Override
    public List<UserInfoModel> listByMobiles(List<String> mobile) {
        if (CollectionUtils.isEmpty(mobile)) {
            return Lists.newArrayList();
        }
        UserRpcResponse<List<UserInfoModel>> listUserRpcResponse = userAccountWhaleFeignClient.listByMobiles(mobile);
        return listUserRpcResponse.getData();
    }


}
