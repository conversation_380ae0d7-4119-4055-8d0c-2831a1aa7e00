package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;
import com.shuidihuzhu.cf.client.material.model.CfPatientBaseInfoVo;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-10
 */
public interface ICfPatientMaterialClientDelegate {

    /**
     * 根据身份证查询是否有过发起
     * @param patientIdCards
     * @return
     */
    OpResult<List<CfPatientBaseInfoVo.PatientResult>> queryPatientVoByIdCards(List<String> patientIdCards);

    OpResult<Boolean> checkIsFirstCaseByPatientCryptCard(String patientCryptIdcard);

    int caseDuplicateNum(int caseId);
}
