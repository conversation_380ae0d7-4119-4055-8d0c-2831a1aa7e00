package com.shuidihuzhu.cf.cfgrowthtoolapi.delegate;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.ai.alps.cupid.feign.CupidAPIFeignClient;
import com.shuidihuzhu.ai.alps.cupid.model.*;
import com.shuidihuzhu.client.cf.admin.client.CfAdminAiGenerateFeignClient;
import com.shuidihuzhu.client.cf.admin.model.AiGenerateForwardParam;
import com.shuidihuzhu.client.cf.admin.model.AiGenerateForwardResult;
import com.shuidihuzhu.common.web.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/11/21 16:26
 */
@Service
@Slf4j
public class AIDelegate {
    public static final String token = "dd1d0ba85fd57c15";

    public static final String pid = "100026";

    private static final int dataLimit = 10;

    @Autowired
    private CupidAPIFeignClient cupidAPIFeignClient;

    @Autowired
    private CfAdminAiGenerateFeignClient cfAdminAiGenerateFeignClient;


    //https://wdh.feishu.cn/wiki/wikcneBU8KppALs7WwUr8oS8olh
    public List<CupidItem> getCaseScore(List<Integer> caseIds) {
        List<CupidItem> cupidItemResults = Lists.newArrayList();
        List<CaseDate> caseDateList = Lists.newArrayList();
        for (Integer caseId : caseIds) {
            CaseDate caseDate = new CaseDate();
            caseDate.setId(caseId);
            caseDateList.add(caseDate);
        }
        // 数据量大于ai的limit限制截取请求处理
        List<List<CaseDate>> partitionCaseList = Lists.partition(caseDateList, dataLimit);
        for (List<CaseDate> caseDates : partitionCaseList) {
            cupidItemResults.addAll(requestAiScore(caseDates));
        }
        return cupidItemResults;
    }

    public List<CupidItem> requestAiScore(List<CaseDate> caseDateList) {
        CupidRequest cupidRequest = new CupidRequest();
        cupidRequest.setRc(JSON.toJSONString(caseDateList));
        cupidRequest.setCha("cf");
        cupidRequest.setUuid("0");
        cupidRequest.setLimit(caseDateList.size());
        cupidRequest.setPin("0");
        cupidRequest.setToken(token);
        cupidRequest.setPid(pid);
        CupidResponse<List<CupidItem>> recommendationsv2 = cupidAPIFeignClient.getRecommendationsv2(cupidRequest);
        log.info("getCaseScore param:{},result:{}", cupidRequest, recommendationsv2);
        if (recommendationsv2 != null && CollectionUtils.isNotEmpty(recommendationsv2.getData())) {
            return recommendationsv2.getData();
        }
        return Lists.newArrayList();
    }


    //生成转发语
    public String createShareContent(AiGenerateForwardParam aiGenerateForwardParam) {
        //打印下耗时
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Response<AiGenerateForwardResult> forwardResultResponse = cfAdminAiGenerateFeignClient.forwardContent(aiGenerateForwardParam);
        stopWatch.stop();
        log.info("forwardContent param:{},result:{},cost:{}ms", aiGenerateForwardParam, forwardResultResponse, stopWatch.getTotalTimeMillis());
        return Optional.ofNullable(forwardResultResponse).map(Response::getData).map(AiGenerateForwardResult::getForwardContent).orElse("");
    }


    @Data
    public class CaseDate {
        private int id;
    }
}
