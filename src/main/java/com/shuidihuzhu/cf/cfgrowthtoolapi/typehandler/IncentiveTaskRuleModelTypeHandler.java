package com.shuidihuzhu.cf.cfgrowthtoolapi.typehandler;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.incentive.IncentiveTaskRuleModel;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @author: wanghui
 * @create: 2021/4/26 下午4:26
 */
public class IncentiveTaskRuleModelTypeHandler extends BaseTypeHandler<IncentiveTaskRuleModel> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, IncentiveTaskRuleModel parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }
    @Override
    public IncentiveTaskRuleModel getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String resultString = resultSet.getString(s);
        if (StringUtils.isBlank(resultString)){
            return null;
        }
        return JSON.parseObject(resultString,IncentiveTaskRuleModel.class);
    }

    @Override
    public IncentiveTaskRuleModel getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String resultString = resultSet.getString(i);
        if (StringUtils.isBlank(resultString)){
            return null;
        }
        return JSON.parseObject(resultString,IncentiveTaskRuleModel.class);
    }

    @Override
    public IncentiveTaskRuleModel getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String resultString = callableStatement.getString(i);
        if (StringUtils.isBlank(resultString)){
            return null;
        }
        return JSON.parseObject(resultString,IncentiveTaskRuleModel.class);
    }
}
