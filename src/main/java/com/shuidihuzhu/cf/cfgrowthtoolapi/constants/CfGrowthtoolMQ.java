package com.shuidihuzhu.cf.cfgrowthtoolapi.constants;

/**
 * @author: wanghui
 * @create: 2019/5/17 5:56 PM
 */
public class CfGrowthtoolMQ {
    public static final String CF = "CF"; //水滴筹
    public static final String CLEW_NOTICE_GROWTHTOOL_CHANNELREFINE = "CLEW_NOTICE_GROWTHTOOL_CHANNELREFINE"; // clewtrack 通知 cf-growthtool-api 渠道拆分

    public static final String CF_ORDER_REFUND_SUCCESS = "CF_ORDER_REFUND_SUCCESS"; // 订单退款成功

    public static final String CF_DONATION_SUCCESS_MSG = "CF_DONATION_SUCCESS_MSG"; // "捐款成功发送消息"

    public static final String INITIAL_AUDIT_TWICE_END_CASE_MSG = "INITIAL_AUDIT_TWICE_END_CASE_MSG"; // "二次停止筹款消息"

    // 用于延迟发送消息 24时的 mq队列
    public static final String GROWTHTOOL_MSG_NOTICE_DELAY_24_HOUR = "GROWTHTOOL_MSG_NOTICE_DELAY_24_HOUR";


    // 自定义模版消息mq
    public static final String SELFBUILT_CF_MODEL_MSG = "SELFBUILT_CF_MODEL_MSG";

    //绿色通道审批延时消息
    public static final String GROWTHTOOL_GREEN_CHANNEL_DELAY_MSG = "GROWTHTOOL_GREEN_CHANNEL_DELAY_MSG";

    // 小善日推送线索 信息接收mq
    public static final String CF_GOOD_DAY_CLEW_RECEIVE_MSG = "CF_GOOD_DAY_CLEW_RECEIVE_MSG";

    //武器申请审批mq
    public static final String WEAPON_APPROVE_DELAY_MSG = "WEAPON_APPROVE_DELAY_MSG";

    //
    public static final String CF_INCREASE_CRON_TASK_MSG = "CF_INCREASE_CRON_TASK_MSG";

    //申请视频号链接mq
    public static final String CF_APPLY_WX_URL_MSG = "CF_APPLY_WX_URL_MSG";

    //检查申请视频号mq
    public static final String CF_CHECK_APPLY_WX_URL_MSG = "CF_CHECK_APPLY_WX_URL_MSG";

    /**
     * 方便袋延迟消息
     */
    public static final String GROWTHTOOL_FANGBIANDAI_DELAY_MSG = "GROWTHTOOL_FANGBIANDAI_DELAY_MSG";

    //申请视频号跳转链接mq
    public static final String CF_GET_AUTO_CREATE_URl_MSG = "CF_GET_AUTO_CREATEUrl_MSG";
    //微信好友信息变更事件
    public static final String WE_CHAT_FRIEND_INFO_CHANGE_MSG = "WE_CHAT_FRIEND_INFO_CHANGE_MSG";


    //代录入生成给招募的信息
    public static final String PREPOSE_COMMIT_CREATE_RECRUIT = "PREPOSE_COMMIT_CREATE_RECRUIT";

    //招募消息
    public static final String WHALE_CLEW_ENTRY_CREATE_SUCCESS = "WHALE_CLEW_ENTRY_CREATE_SUCCESS";

    //无效线索
    public static final String CLEW_IS_VALID = "CLEW_IS_VALID";

    //法律服务
    public static final String GROWTHTOOL_LEGAL_AID_RECOMMEND = "GROWTHTOOL_LEGAL_AID_RECOMMEND";

    //异地转介绍待办提醒
    public static final String GROWTHTOOL_REMOTE_CASE_REMIND = "GROWTHTOOL_REMOTE_CASE_REMIND";

    //24h延时查看顾问是否跟进任务
    public static final String GROWTHTOOL_REFUND_CASE_TASK_24 = "GROWTHTOOL_REFUND_CASE_TASK";

    //3h延时查看好友最新信息
    public static final String FRIEND_EDIT_INFO_3 = "FRIEND_EDIT_INFO_3";

    //更新群活码及群关联关系
    public static final String GROUP_INFO_CHECK_CONFIG_EITH_CHAT = "GROUP_INFO_CHECK_CONFIG_EITH_CHAT";

    //群成员信息变更
    public static final String GROUP_CHANGE_MSG = "GROUP_CHANGE_MSG";

    // 案例归属顾问离职消息
    public static final String BD_CASE_DISMISS_VOLUNTEER_MSG = "BD_CASE_DISMISS_VOLUNTEER_MSG";

    //确权消息提醒
    public static final String GROWTHTOOL_CONFIRM_REMIND = "GROWTHTOOL_CONFIRM_REMIND";

    //扫码报备提醒
    public static final String GROWTHTOOL_SCAN_CODE_PREPOSE_REMIND = "GROWTHTOOL_SCAN_CODE_PREPOSE_REMIND";

    //初审通过24小时后，校验任务情况
    public static final String ONE_DAY_AFTER_THE_FIRST_TRIAL_TASK_CHECK = "ONE_DAY_AFTER_THE_FIRST_TRIAL_TASK_CHECK";

    //有效案例配置生效
    public static final String VALID_CASE_CONFIG_EFFECTIVE = "VALID_CASE_CONFIG_EFFECTIVE";

    //检查群成员状态
    public static final String QYWX_GROUP_ADD_MEMBER_REMIND_MSG = "QYWX_GROUP_ADD_MEMBER_REMIND_MSG";

    //发送企业微信群消息
    public static final String WXWORK_GROUP_MESSAGE_RECORD_AUTO_REPLY = "wxwork_group_message_record_auto_reply";

    //发送企业微信群消息
    public static final String GROWTHTOOL_BD_QYWX_GROUP_INNER_MSG = "GROWTHTOOL_BD_QYWX_GROUP_INNER_MSG";

    public static final String GROWTHTOOL_QYWX_CHAT_OWNER_CHANGE_MSG = "GROWTHTOOL_QYWX_CHAT_OWNER_CHANGE_MSG";

    public static final String WX_WORK_MESSAGE_DOWN_ACK = "wx_work_message_down_ack";

    //联系方式回调延时处理
    public static final String AUTO_CREATE_CONTACT_CODE_DELAY_MSG = "AUTO_CREATE_CONTACT_CODE_DELAY_MSG";

}
