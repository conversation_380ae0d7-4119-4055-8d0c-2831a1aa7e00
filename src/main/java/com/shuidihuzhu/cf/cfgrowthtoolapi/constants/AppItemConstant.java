package com.shuidihuzhu.cf.cfgrowthtoolapi.constants;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AppItemModel;

public class AppItemConstant {

    public static final AppItemModel clew = new AppItemModel("clew","线索模块图标","线索",100);

    public static final AppItemModel dlr = new AppItemModel("dlr","待录入模块图标","待录入",100);

    public static final AppItemModel cfcase = new AppItemModel("cfcase","案例模块图标","案例",100);

    public static  final AppItemModel kefu = new AppItemModel("kefu","客服模块图标","客服",100);

    public static final AppItemModel zjs = new AppItemModel("zjs","转介绍模块图标","转介绍",100);

    public static final AppItemModel zfgj = new AppItemModel("zfgj","走访工具模块","走访工具",100);

    public static final AppItemModel fwsm = new AppItemModel("fwsm","服务声明模块","服务声明",100);

}
