package com.shuidihuzhu.cf.cfgrowthtoolapi.constants;

/**
 * <AUTHOR>
 * @date 2019-05-16
 */
public interface MQTagCons {

    /**
     * 案例发起mq
     */
    String CF_OPERATION_MSG = "CF_OPERATION_MSG";

    /**
     * 发送案例发起延时24H的mq
     */
    String MQ_TAG_CASE_UP_DELAY_24H = "MQ_TAG_CASE_UP_DELAY_24H";

    /**
     * 发送百度信息流消息
     */
    String SD_DATA_AD_BAIDU_CF_TAGS = "SD_DATA_AD_BAIDU_CF_TAGS";

    /**
     * 用户发起线索后发送的消息
     */
    String CF_CLEW_RECEIVE_MSG = "CF_CLEW_RECEIVE_MSG";

    /**
     * 用户扫二维码后发起  ||  关注水滴筹等公众号   后通知cf-clewtrack  新建bdcrm线索
     */
    String NOTICE_CLEW_TRACK_ADD_BD_CRM_CLEW = "NOTICE_CLEW_TRACK_ADD_BD_CRM_CLEW";

    /**
     * 用户扫二维码后发起
     */
    String ACCOUNT_MQTAG_BIND_MOBILE = "ACCOUNT_MQTAG_BIND_MOBILE";

    /**
     * 投放统计数据库
     */
    String TOUFANG_REGISTERDAY_MSG = "TOUFANG_REGISTERDAY_MSG";
    /**
     * 投放统计数据库_另一个实例
     */
    String CF_TOUFANG_SIGN_GROWTH_MSG = "CF_TOUFANG_SIGN_GROWTH_MSG";
    /**
     * 上报广点通
     */
    String REPORT_GDT_MSG = "REPORT_GDT_MSG";
    /**
     * 上报广点通
     */
    String REPORT_SM_MSG = "REPORT_SM_MSG";
    /**
     * 内部渠道投放统计
     */
    String CF_ADREGISTER_MSG = "CF_ADREGISTER_MSG" ;
    /**
     * 基金会录入线索
     */
    String FOUNDATION_CLEW_MSG_FROM_GROWTHTOOL = "FOUNDATION_CLEW_MSG_FROM_GROWTHTOOL";
    /**
     * 案例恢复筹款
     */
    String CF_RECOVER_CASE = "CF_RECOVER_CASE";
    /**
     * 案例快照数据同步
     */
    String CF_KPI_CASE_BASE_DATA_SYNC = "CF_KPI_CASE_BASE_DATA_SYNC";

    /**
     * 陪访消息
     */
    String CF_KPI_VISIT_BASE_DATA_SYNC = "CF_KPI_VISIT_BASE_DATA_SYNC";

    /**
     * 月捐单数据
     */
    String CF_KPI_MONTH_DONATE_DATA_SYNC = "CF_KPI_MONTH_DONATE_DATA_SYNC";

    /**
     * 月人员管理数据
     */
    String CF_KPI_VOLUNTEER_MANAGER_CNT = "CF_KPI_VOLUNTEER_MANAGER_CNT";

    /**
     * 月老员工人效数据
     */
    String CF_KPI_PERSONAL_EFFICIENCY = "CF_KPI_PERSONAL_EFFICIENCY";

    //团队捐单达成和首发有效案例
    String CF_KPI_DELEGATE_CASE_INFO = "CF_KPI_DELEGATE_CASE_INFO";

    String CF_KPI_DELEGATE_HC_BASE_DATA = "CF_KPI_DELEGATE_HC_BASE_DATA";

    /**
     * 合作团队案例明细
     */
    String KPI_PARTNER_CASE_DETAIL = "KPI_PARTNER_CASE_DETAIL";

    /**
     * 合作团队城市信息
     */
    String KPI_PARTNER_CITY_INFO = "KPI_PARTNER_CITY_INFO";

    /**
     * 患者招募推送数据
     */
    String CF_KPI_RECRUIT_BASE_DATA_SYNC = "CF_KPI_RECRUIT_BASE_DATA_SYNC";

    /**
     * 企业微信好友备注提取手机号MQ
     */
    String GROWTHTOOL_GET_DATA_FROM_QY_WECHAT = "GROWTHTOOL_GET_DATA_FROM_QY_WECHAT";

    /**
     * 新建 目标周期mq
     */
    String CF_BD_CRM_OBJECTIVE_CYCLE_NEW_MSG = "CF_BD_CRM_OBJECTIVE_CYCLE_NEW_MSG";

    String SYNC_WORK_WX_IM_USER_INFO_MSG="SYNC_WORK_WX_IM_USER_INFO_MSG";


    /**
     * 科室变更
     */
    String GROWTHTOOL_DEPARTMENT_CHANGE_APPROVE = "GROWTHTOOL_DEPARTMENT_CHANGE_APPROVE";


    /**
     * 案例数据转移
     */
    String GROWTHTOOL_MOVE_CASE_DATA = "GROWTHTOOL_MOVE_CASE_DATA";

    /**
     * 爱心伙伴离职延时消息
     */
    String LOVER_RESIGN_DELAY_MSG = "LOVER_RESIGN_DELAY_MSG";

    /**
     * 提醒顾问维护代录入医院/科室延时消息
     */
    String REMIND_VOLUNTEER_MAINTAIN_HOSPITAL_DEPARTMENT_DELAY_MSG = "REMIND_VOLUNTEER_MAINTAIN_HOSPITAL_DEPARTMENT_DELAY_MSG";

    /**
     * 企业微信事件解密后数据
     */
    String QY_WECHAT_EVENT_DECRYPT_MSG = "QY_WECHAT_EVENT_DECRYPT_MSG";


    /**
     * 企业微信事件解密后数据实时消息
     */
    String QY_WECHAT_EVENT_DECRYPT_REAL_TIME_MSG = "QY_WECHAT_EVENT_DECRYPT_REAL_TIME_MSG";

    /**
     * 企业微信事件解密后GROWTHTOOL自消费消息
     */
    String QW_CALLBACK_DECRYPT_EVENT_GROWTHTOOL_MSG = "QW_CALLBACK_DECRYPT_EVENT_GROWTHTOOL_MSG";

    /**
     * 小鲸鱼弹窗活动下线消息
     */
    String XJY_POPUP_ACTIVITY_OFFLINE_MSG = "XJY_POPUP_ACTIVITY_OFFLINE_MSG";

    /**
     * 高潜意向记录
     */
    String HIGH_INTENTION_RECORD = "HIGH_INTENTION_RECORD";

    /**
     * 案例代理消息
     */
    String CF_CASE_AGENCY_MSG = "CF_CASE_AGENCY_MSG";

}
