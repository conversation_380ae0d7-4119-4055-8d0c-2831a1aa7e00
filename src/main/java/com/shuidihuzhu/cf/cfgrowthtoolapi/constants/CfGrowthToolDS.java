package com.shuidihuzhu.cf.cfgrowthtoolapi.constants;
/**
 * @author: wanghui
 * @create: 2019/5/16 8:52 PM
 */
public class CfGrowthToolDS {
    public static final String CF_SLAVE2_DATA_SOURCE = "cfSlave2DataSource";
    public static final String STAT_CF_DATA_SOURCE = "statCfDataSource";
    public static final String STAT_CF_SLAVE_DATA_SOURCE = "statCfSlaveDataSource";
    public static final String CLEWTRACK_MASTER = "clewtrackSource";
    public static final String CLEW_TRACK_SLAVE_1 = "clewtrackSlaveDataSource";
    public static final String CLEW_TRACk_SLAVE_2 = "clewtrackSlaveDataSource2";

    public static final String SHUIDI_CF_CASE_STAT_SLAVE = "shuidiCfCaseStatSlave";

    //公用doris
    public static final String STAR_ROCKS_DATASOURCE = "growthtoolStarRocksDataSource";

    //筹业务线专用doris
    public static final String CF_STAR_ROCKS_DATASOURCE = "cfStarRocksDataSource";

    // es 数据源
    public static final String GROWTHTOOL_ES="growthtoolSourceES";

    /**
     * clewtrackDataSource事务管理器
     */
    public static final String CLEWTRACK_DATA_SOURECE_TRANSACTION_MANAGER = "clewtrackDataSourceTransactionManage";

    // tdsql
    public static final String TD_SHUIDI_CF_CLEWTRACK = "tdShuidiCfClewtrack";
    public static final String TD_SHUIDI_CF_CASE_STAT = "tdShuidiCfCaseStat";
    public static final String TD_SHUIDI_CF_PATIENT = "tdShuidiCfPatient";


    // one-service
    public static final String CF_ONE_SERVICE_DATA_SOURCE = "cfOneServiceDataSource";
}
