package com.shuidihuzhu.cf.cfgrowthtoolapi.controller.api.fuwuexperiment;

/**
 * <AUTHOR>
 * @date 2019-09-06
 */

import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IFuwuMenuExperiment;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping(path = "/api/cf/growthtool/menu/fuwu" , method = RequestMethod.POST)
public class FuwuMenuExperimentController {

    @Autowired
    private IFuwuMenuExperiment fuwuMenuExperimentImpl;

    @RequestMapping(path = "/query", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response queryPage(){
        long userId = ContextUtil.getUserId();
        if(userId <= 0){
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        OpResult<Integer> result = fuwuMenuExperimentImpl.query(userId);
        if (result.isSuccess()){
            log.info("FuwuMenuExperimentController_query_result:{}",result.getData());
            return NewResponseUtil.makeSuccess(result.getData());
        }else {
            log.info("FuwuMenuExperimentController_querypage_errorcode:{}",result.getErrorCode());
            return NewResponseUtil.makeError(result.getErrorCode());
        }
    }
}
