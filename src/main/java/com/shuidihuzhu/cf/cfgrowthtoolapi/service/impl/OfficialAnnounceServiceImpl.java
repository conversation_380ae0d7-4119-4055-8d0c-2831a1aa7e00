package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdcrmOfficialAnnountStatisticModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdcrmOfficialAnnounceSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.OfficialAnnounceModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdcrmKnowledgeCommentVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdcrmOfficialAnnounceSendRecordVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdcrmOfficialAnnounceVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfSeaOfficialAnnounceVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IOfficialAnnounceService;
import com.shuidihuzhu.cf.dao.bdcrm.*;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2020/12/25 上午10:43
 */
@Service
@Slf4j
public class OfficialAnnounceServiceImpl implements IOfficialAnnounceService {
    @Autowired
    private CfBdcrmOfficialAnnounceDao cfBdcrmOfficialAnnounceDao;
    @Autowired
    private CfBdcrmOfficialAnnounceStatDao cfBdcrmOfficialAnnounceStatDao;
    @Autowired
    private CfBdcrmOfficialAnnounceKnowledgeRelDao cfBdcrmOfficialAnnounceKnowledgeRelDao;
    @Autowired
    private CfBdcrmKnowledgeCommentDao cfBdcrmKnowledgeCommentDao;
    @Autowired
    private CfBdcrmOfficialAnnounceSendRecordDao cfBdcrmOfficialAnnounceSendRecordDao;
    @Autowired
    private CfBdcrmLikeRecordDao cfBdcrmLikeRecordDao;
    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;
    @Autowired
    private CfCrmUserExtDao cfCrmUserExtDao;

    @Override
    public void saveOfficialAnnounce(CfBdcrmOfficialAnnounceDO cfBdcrmOfficialAnnounceDO) {
        cfBdcrmOfficialAnnounceDao.insert(cfBdcrmOfficialAnnounceDO);
        // cf_bdcrm_official_announce_knowledge_rel 插入记录
        List<OfficialAnnounceModel> officialAnnounceModels = cfBdcrmOfficialAnnounceDO.getOfficialAnnounceModels();
        if (CollectionUtils.isEmpty(officialAnnounceModels)){
            return;
        }
        officialAnnounceModels = officialAnnounceModels.stream().filter(officialAnnounceModel -> officialAnnounceModel.getKnowledgeId() != null && officialAnnounceModel.getKnowledgeId() != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(officialAnnounceModels)){
            return;
        }
        List<CfBdcrmOfficialAnnounceKnowledgeRelDO> knowledgeRelDOS = officialAnnounceModels.stream().map(officialAnnounceModel -> new CfBdcrmOfficialAnnounceKnowledgeRelDO(cfBdcrmOfficialAnnounceDO.getId(), officialAnnounceModel.getKnowledgeId())).collect(Collectors.toList());
        cfBdcrmOfficialAnnounceKnowledgeRelDao.insertBatch(knowledgeRelDOS);
    }

    @Override
    public void updateOfficialAnnounce(CfBdcrmOfficialAnnounceDO cfBdcrmOfficialAnnounceDO) {
        CfBdcrmOfficialAnnounceDO cfBdcrmOfficialAnnounceDOInDB = cfBdcrmOfficialAnnounceDao.queryById(cfBdcrmOfficialAnnounceDO.getId());
        if (cfBdcrmOfficialAnnounceDOInDB==null){
            return;
        }
        Set<Long> knowledgeIdsInDB = cfBdcrmOfficialAnnounceDOInDB.getOfficialAnnounceModels().stream().map(OfficialAnnounceModel::getKnowledgeId).filter(knowledgeId -> knowledgeId != null && knowledgeId != 0).collect(Collectors.toSet());
        Set<Long> newKnowledgeIds = cfBdcrmOfficialAnnounceDO.getOfficialAnnounceModels().stream().map(OfficialAnnounceModel::getKnowledgeId).filter(knowledgeId -> knowledgeId != null && knowledgeId != 0).collect(Collectors.toSet());
        List<Long> diffKnowledgeIds = Sets.difference(knowledgeIdsInDB, newKnowledgeIds).stream().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(diffKnowledgeIds)){
            cfBdcrmOfficialAnnounceKnowledgeRelDao.updateIsDelete(cfBdcrmOfficialAnnounceDO.getId(),diffKnowledgeIds);
        }
        cfBdcrmOfficialAnnounceDao.update(cfBdcrmOfficialAnnounceDO);
    }

    @Override
    public long getListCountForSea(CfBdcrmOfficialAnnounceSearchModel searchModel) {
        return cfBdcrmOfficialAnnounceDao.getListCountForSea(searchModel);
    }

    @Override
    public List<CfSeaOfficialAnnounceVO> getListForSea(CfBdcrmOfficialAnnounceSearchModel searchModel) {
        return cfBdcrmOfficialAnnounceDao.getListForSea(searchModel);
    }

    @Override
    public OpResult withdrawSendMsg(Long officialAnnounceId) {
        int i = cfBdcrmOfficialAnnounceSendRecordDao.updateIsDelete(officialAnnounceId);
        log.info("withdrawSendMsg param:{} result:{}",officialAnnounceId,i);
        if (i==0){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.NOT_FOUND_SEND_OFFICIAL_ANNOUNCE_MSG);
        }
        cfBdcrmOfficialAnnounceDao.updateStatus(officialAnnounceId, CfBdcrmOfficialAnnounceDO.StatusEnum.WITHDRAWN.getStatus());
        return OpResult.createSucResult();
    }

    @Override
    public void delOfficialAnnounce(Long officialAnnounceId) {
        cfBdcrmOfficialAnnounceDao.updateIsDelete(officialAnnounceId);
    }

    @Override
    public void sendOfficialAnnounceMsg2Gw(Long officialAnnounceId, List<BdCrmVolunteerOrgnizationSimpleModel> simpleModelList, Map<String, CrowdfundingVolunteer> uniqueCodeMapVolunteer, long operatorId, String operatorName) {
        /**
         * 1、cf_bdcrm_official_announce  status改为 1 已发送
         * 2、cf_bdcrm_official_announce_stat  send_record_json 记录方法记录  send_num
         * 3、cf_bdcrm_official_announce_send_record 记录 每个人的发放记录
         */
        CfBdcrmOfficialAnnounceDO cfBdcrmOfficialAnnounceDO = cfBdcrmOfficialAnnounceDao.queryById(officialAnnounceId);
        if (cfBdcrmOfficialAnnounceDO==null){
            log.info("{} cfBdcrmOfficialAnnounceDao.queryById param:{} result is null",this.getClass().getSimpleName(),officialAnnounceId);
            return;
        }
        cfBdcrmOfficialAnnounceDao.updateStatus(officialAnnounceId, CfBdcrmOfficialAnnounceDO.StatusEnum.SEND.getStatus());
        List<CfBdcrmOfficialAnnounceSendRecordDO> sendRecordDOList = convert(officialAnnounceId, simpleModelList, uniqueCodeMapVolunteer, operatorId, operatorName);
        saveOrUpdateOfficialAnnounceStat(sendRecordDOList,officialAnnounceId);

        List<List<CfBdcrmOfficialAnnounceSendRecordDO>> sendRecordListList = ListUtils.partition(sendRecordDOList, GeneralConstant.MAX_PAGE_SIZE);
        Integer insertNum = sendRecordListList.parallelStream()
                .map(list -> cfBdcrmOfficialAnnounceSendRecordDao.insertBatch(list))
                .reduce(Integer::sum)
                .orElse(0);
        log.info("{} sendOfficialAnnounceMsg2Gw 发放人数:{}",this.getClass().getSimpleName(),insertNum);
        ListUtils.partition(Lists.newArrayList(uniqueCodeMapVolunteer.values()),GeneralConstant.MAX_PAGE_SIZE).parallelStream().forEach(list-> appPushCrmCaseMsgService.sendOfficialAnnounceMsg(cfBdcrmOfficialAnnounceDO,list));
    }

    public void saveOrUpdateOfficialAnnounceStat(List<CfBdcrmOfficialAnnounceSendRecordDO> sendRecordDOList, Long officialAnnounceId){
        CfBdcrmOfficialAnnounceStatDO cfBdcrmOfficialAnnounceStatDO = cfBdcrmOfficialAnnounceStatDao.queryByOfficialAnnounceId(officialAnnounceId);
        if (cfBdcrmOfficialAnnounceStatDO==null){
            cfBdcrmOfficialAnnounceStatDO = new CfBdcrmOfficialAnnounceStatDO();
            cfBdcrmOfficialAnnounceStatDO.setOfficialAnnounceId(officialAnnounceId);
            cfBdcrmOfficialAnnounceStatDO.setSendNum(sendRecordDOList.size());
            CfBdcrmOfficialAnnounceStatDO.SendRecordModel sendRecordModel = new CfBdcrmOfficialAnnounceStatDO.SendRecordModel();
            sendRecordModel.setSendNum(sendRecordDOList.size());
            cfBdcrmOfficialAnnounceStatDO.setSendRecordJson(JSON.toJSONString(Lists.newArrayList(sendRecordModel)));
            cfBdcrmOfficialAnnounceStatDao.insert(cfBdcrmOfficialAnnounceStatDO);
        }else {
            cfBdcrmOfficialAnnounceStatDO.setSendNum(cfBdcrmOfficialAnnounceStatDO.getSendNum()+sendRecordDOList.size());
            CfBdcrmOfficialAnnounceStatDO.SendRecordModel sendRecordModel = new CfBdcrmOfficialAnnounceStatDO.SendRecordModel();
            sendRecordModel.setSendNum(sendRecordDOList.size());
            List<CfBdcrmOfficialAnnounceStatDO.SendRecordModel> sendRecordModels = cfBdcrmOfficialAnnounceStatDO.showSendRecordJson();
            sendRecordModels.add(sendRecordModel);
            cfBdcrmOfficialAnnounceStatDO.setSendRecordJson(JSON.toJSONString(sendRecordModels));
            cfBdcrmOfficialAnnounceStatDao.update(cfBdcrmOfficialAnnounceStatDO);
        }

    }

    @Override
    public long getSendRecordsCount(Long officialAnnounceId, int readStatus) {
        return cfBdcrmOfficialAnnounceSendRecordDao.getSendRecordsCount(officialAnnounceId,readStatus);
    }

    @Override
    public List<CfBdcrmOfficialAnnounceSendRecordVO> getSendRecords(Long officialAnnounceId, int readStatus, int pageNo, int pageSize) {
        int offset = (pageNo-1)*pageSize;
        return cfBdcrmOfficialAnnounceSendRecordDao.getSendRecords(officialAnnounceId,readStatus,offset,pageSize);
    }

    @Override
    public BdcrmOfficialAnnountStatisticModel getSendStatistic(Long officialAnnounceId) {
        CfBdcrmOfficialAnnounceStatDO cfBdcrmOfficialAnnounceStatDO = cfBdcrmOfficialAnnounceStatDao.queryByOfficialAnnounceId(officialAnnounceId);
        return cfBdcrmOfficialAnnounceStatDO==null?new BdcrmOfficialAnnountStatisticModel():cfBdcrmOfficialAnnounceStatDO.convert(new BdcrmOfficialAnnountStatisticModel());
    }

    @Override
    public long getUnReadNum(CrowdfundingVolunteer cfVolunteer) {
        return cfBdcrmOfficialAnnounceSendRecordDao.getUnReadNum(cfVolunteer.getUniqueCode());
    }

    @Override
    public void saveKnowledgeComment(Long knowledgeId, Long commentId, String comment, CrowdfundingVolunteer cfVolunteer) {
        CfBdcrmKnowledgeCommentDO cfBdcrmKnowledgeCommentDO = new CfBdcrmKnowledgeCommentDO();
        cfBdcrmKnowledgeCommentDO.setKnowledgeId(knowledgeId);
        cfBdcrmKnowledgeCommentDO.setCommentContent(comment);
        cfBdcrmKnowledgeCommentDO.setParentId(commentId);
        cfBdcrmKnowledgeCommentDO.setLikeNum(0);
        cfBdcrmKnowledgeCommentDO.setUniqueCode(cfVolunteer.getUniqueCode());
        cfBdcrmKnowledgeCommentDO.setMis(cfVolunteer.getMis());
        cfBdcrmKnowledgeCommentDO.setName(cfVolunteer.getVolunteerName());
        CfCrmUserExtDO baseInfo = cfCrmUserExtDao.getBaseInfo(cfVolunteer.getUniqueCode());
        cfBdcrmKnowledgeCommentDO.setHeadUrl(baseInfo==null?"":baseInfo.getHeadUrl());
        cfBdcrmKnowledgeCommentDao.insert(cfBdcrmKnowledgeCommentDO);
    }

    @Override
    public OpResult<List<Long>> checkPermissionToComment(Long knowledgeId, CrowdfundingVolunteer cfVolunteer) {
        // 判断该知识是否 属于他
        List<Long> officialAnnounceIds = cfBdcrmOfficialAnnounceKnowledgeRelDao.queryOfficialAnnounceIdByKnowledgeId(knowledgeId);
        if (CollectionUtils.isEmpty(officialAnnounceIds)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.UNAUTHORIZED);
        }
        List<CfBdcrmOfficialAnnounceSendRecordDO> cfBdcrmOfficialAnnounceSendRecordDOs = cfBdcrmOfficialAnnounceSendRecordDao.queryByUniqueCodeWithOfficialAnnounceId(officialAnnounceIds,cfVolunteer.getUniqueCode());
        return CollectionUtils.isEmpty(cfBdcrmOfficialAnnounceSendRecordDOs)?OpResult.createFailResult(CfGrowthtoolErrorCode.UNAUTHORIZED)
                : OpResult.createSucResult(cfBdcrmOfficialAnnounceSendRecordDOs.stream().map(CfBdcrmOfficialAnnounceSendRecordDO::getOfficialAnnounceId).collect(Collectors.toList()));
    }

    @Override
    public long getMsgListCount(Integer readStatus, CrowdfundingVolunteer cfVolunteer) {
        return cfBdcrmOfficialAnnounceSendRecordDao.getMsgListCount(readStatus, cfVolunteer.getUniqueCode());
    }
    @Override
    public List<Long> getUnReadOfficialAnnounceId(String uniqueCode){
        return cfBdcrmOfficialAnnounceSendRecordDao.getUnReadOfficialAnnounceId(uniqueCode);
    }

    @Override
    public CfBdcrmOfficialAnnounceVO getLastNoPicAnnounce(List<Long> announceIds) {
        List<CfBdcrmOfficialAnnounceDO> cfBdcrmOfficialAnnounceDOS = cfBdcrmOfficialAnnounceDao.queryByIdList(announceIds);
        Optional<CfBdcrmOfficialAnnounceDO> showAnnounceDo = cfBdcrmOfficialAnnounceDOS.stream().filter(item -> item.getType() == 1).max(Ordering.natural().onResultOf(CfBdcrmOfficialAnnounceDO::getId));
        if (showAnnounceDo.isEmpty()) {
            return null;
        }
        CfBdcrmOfficialAnnounceDO cfBdcrmOfficialAnnounceDO = showAnnounceDo.get();
        CfBdcrmOfficialAnnounceVO cfBdcrmOfficialAnnounceVO = new CfBdcrmOfficialAnnounceVO();
        cfBdcrmOfficialAnnounceVO.setOfficialAnnounceModels(cfBdcrmOfficialAnnounceDO.getOfficialAnnounceModels());
        cfBdcrmOfficialAnnounceVO.setTitle(cfBdcrmOfficialAnnounceDO.getTitle());
        cfBdcrmOfficialAnnounceVO.setType(cfBdcrmOfficialAnnounceDO.getType());
        cfBdcrmOfficialAnnounceVO.setCreateTime(cfBdcrmOfficialAnnounceDO.getCreateTime());
        return cfBdcrmOfficialAnnounceVO;
    }

    @Override
    public List<CfBdcrmOfficialAnnounceVO> getMsgList(Integer readStatus, CrowdfundingVolunteer cfVolunteer, int pageNo, int pageSize) {
        int offset = (pageNo-1)*pageSize;
        List<CfBdcrmOfficialAnnounceVO> cfBdcrmOfficialAnnounceVOList = cfBdcrmOfficialAnnounceSendRecordDao.getOfficialAnnounceIdList(readStatus, cfVolunteer.getUniqueCode(), offset, pageSize);
        if (CollectionUtils.isEmpty(cfBdcrmOfficialAnnounceVOList)){
            return Lists.newArrayList();
        }
        Map<Long, CfBdcrmOfficialAnnounceDO> officialAnnounceIdMap = cfBdcrmOfficialAnnounceDao.queryByIdList(cfBdcrmOfficialAnnounceVOList.stream().map(CfBdcrmOfficialAnnounceVO::getOfficialAnnounceId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(CfBdcrmOfficialAnnounceDO::getId, Function.identity(),(oldObj, newObj)->newObj));
        return cfBdcrmOfficialAnnounceVOList.stream().map(cfBdcrmOfficialAnnounceVO -> {
            CfBdcrmOfficialAnnounceDO cfBdcrmOfficialAnnounceDO = officialAnnounceIdMap.get(cfBdcrmOfficialAnnounceVO.getOfficialAnnounceId());
            if (cfBdcrmOfficialAnnounceDO==null) return cfBdcrmOfficialAnnounceVO;
            cfBdcrmOfficialAnnounceVO.setOfficialAnnounceModels(cfBdcrmOfficialAnnounceDO.getOfficialAnnounceModels());
            cfBdcrmOfficialAnnounceVO.setTitle(cfBdcrmOfficialAnnounceDO.getTitle());
            cfBdcrmOfficialAnnounceVO.setType(cfBdcrmOfficialAnnounceDO.getType());
            cfBdcrmOfficialAnnounceVO.setCreateTime(cfBdcrmOfficialAnnounceDO.getCreateTime());
            return cfBdcrmOfficialAnnounceVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CfBdcrmKnowledgeCommentVO> getKnowledgeCommentList(Long knowledgeId, int type, String uniqueCode, int pageNo, int pageSize) {
        int offset = (pageNo-1)*pageSize;
        List<CfBdcrmKnowledgeCommentVO> cfBdcrmKnowledgeCommentVOS = cfBdcrmKnowledgeCommentDao.queryByKnowledgeId(knowledgeId, type, offset, pageSize);
        if (CollectionUtils.isEmpty(cfBdcrmKnowledgeCommentVOS)) {
            return Lists.newArrayList();
        }
        List<Long> commentIds = cfBdcrmKnowledgeCommentVOS.stream().map(CfBdcrmKnowledgeCommentVO::getId).collect(Collectors.toList());
        Map<Long, CfBdcrmLikeRecordDO> commentIdMap = cfBdcrmLikeRecordDao.queryByCommentIdsWithUniqueCode(commentIds, uniqueCode).stream().collect(Collectors.toMap(CfBdcrmLikeRecordDO::getTargetId,Function.identity(),(oldObj, newObj)->newObj));
        return cfBdcrmKnowledgeCommentVOS.stream().map(cfBdcrmKnowledgeCommentVO -> {
            CfBdcrmLikeRecordDO cfBdcrmLikeRecordDO = commentIdMap.get(cfBdcrmKnowledgeCommentVO.getId());
            cfBdcrmKnowledgeCommentVO.setLike(cfBdcrmLikeRecordDO!=null);
            CommonResultModel<CfBdcrmKnowledgeCommentVO> resultModel = new CommonResultModel<>();
            long total = this.querySubCommentByCommentIdCount(cfBdcrmKnowledgeCommentVO.getId());
            resultModel.setTotal(total);
            if (total>0){
                resultModel.setModelList(this.querySubCommentByCommentId(cfBdcrmKnowledgeCommentVO.getId(),1,pageSize));
            }
            cfBdcrmKnowledgeCommentVO.setSubComment(resultModel);
            return cfBdcrmKnowledgeCommentVO;
        }).collect(Collectors.toList());
    }
    @Override
    public long querySubCommentByCommentIdCount(long commentId){
        return cfBdcrmKnowledgeCommentDao.querySubCommentByCommentIdCount(commentId);
    }
    @Override
    public List<CfBdcrmKnowledgeCommentVO> querySubCommentByCommentId(long commentId, int pageNo, int pageSize){
        int offset = (pageNo-1)*pageSize;
        return cfBdcrmKnowledgeCommentDao.querySubCommentByCommentId(commentId,offset,pageSize);
    }

    @Override
    public long getKnowledgeCommentListCount(Long knowledgeId) {
        return cfBdcrmKnowledgeCommentDao.getKnowledgeCommentListCount(knowledgeId);
    }

    @Override
    public OpResult likeKnowledgeComment(Long knowledgeId, Long commentId, int type, CrowdfundingVolunteer cfVolunteer) {
        if (type==1){ // type=1 点赞
            cfBdcrmKnowledgeCommentDao.incrLikeNum(knowledgeId,commentId);
            cfBdcrmLikeRecordDao.insert(new CfBdcrmLikeRecordDO(cfVolunteer.getMis(),cfVolunteer.getUniqueCode(), CfBdcrmLikeRecordDO.TypeEnum.COMMENT.getType(), commentId));
        }else { // 取消点赞
            List<CfBdcrmLikeRecordDO> cfBdcrmLikeRecordDOS = cfBdcrmLikeRecordDao.queryByCommentIdsWithUniqueCode(Lists.newArrayList(commentId), cfVolunteer.getUniqueCode());
            if (CollectionUtils.isEmpty(cfBdcrmLikeRecordDOS)) return OpResult.createFailResult(CfGrowthtoolErrorCode.UNAUTHORIZED);
            cfBdcrmKnowledgeCommentDao.decLikeNum(knowledgeId,commentId);
            cfBdcrmLikeRecordDao.updateIsDelete(cfBdcrmLikeRecordDOS.get(0).getId());
        }
        return OpResult.createSucResult();
    }

    @Override
    public void readOfficialAnnounce(String uniqueCode,List<Long> officialAnnounceIds) {
        if (CollectionUtils.isEmpty(officialAnnounceIds)){
            return;
        }
        /**
         * cf_bdcrm_official_announce_stat  reader_num
         * cf_bdcrm_official_announce_send_record  read_status
         */
        int i = cfBdcrmOfficialAnnounceSendRecordDao.updateReadStatus(uniqueCode, officialAnnounceIds);
        if (i>0){
            cfBdcrmOfficialAnnounceStatDao.updateReaderNum(officialAnnounceIds);
        }
    }
    @Override
    public void updateWorkStatus(String uniqueCode,int workStatus){
        cfBdcrmOfficialAnnounceSendRecordDao.updateWorkStatus(uniqueCode,workStatus);
    }

    @Override
    public CfBdcrmOfficialAnnounceDO getOfficialAnnounceById(Long id) {
        return cfBdcrmOfficialAnnounceDao.queryById(id);
    }

    @Override
    public List<Long> getOfficialAnnounceIdOfImage(List<Long> unReadOfficialAnnounceId) {
        if (CollectionUtils.isEmpty(unReadOfficialAnnounceId)){
            return Lists.newArrayList();
        }
        return cfBdcrmOfficialAnnounceDao.queryByIdListWithImageType(unReadOfficialAnnounceId);
    }

    @Override
    public List<CfBdcrmOfficialAnnounceVO> listReadForHomePageScroll(String uniqueCode, int offset, int limit) {
        if (StringUtils.isBlank(uniqueCode)) {
            return Lists.newArrayList();
        }
        List<CfBdcrmOfficialAnnounceVO> result =Lists.newArrayList();
        cfBdcrmOfficialAnnounceDao.listReadForHomePageScroll(uniqueCode, offset, limit)
                .forEach(item -> {
                    CfBdcrmOfficialAnnounceVO cfBdcrmOfficialAnnounceVO = new CfBdcrmOfficialAnnounceVO();
                    cfBdcrmOfficialAnnounceVO.setOfficialAnnounceModels(item.getOfficialAnnounceModels());
                    cfBdcrmOfficialAnnounceVO.setTitle(item.getTitle());
                    cfBdcrmOfficialAnnounceVO.setType(item.getType());
                    cfBdcrmOfficialAnnounceVO.setCreateTime(item.getCreateTime());
                    cfBdcrmOfficialAnnounceVO.setReadStatus(1);
                    result.add(cfBdcrmOfficialAnnounceVO);
                });
        return result;
    }

    private List<CfBdcrmOfficialAnnounceSendRecordDO> convert(Long officialAnnounceId, List<BdCrmVolunteerOrgnizationSimpleModel> simpleModelList, Map<String, CrowdfundingVolunteer> uniqueCodeMapVolunteer, long operatorId, String operatorName) {
        Map<String,CfBdcrmOfficialAnnounceSendRecordDO> result = Maps.newHashMap();
        for (BdCrmVolunteerOrgnizationSimpleModel simpleModel:simpleModelList){
            CfBdcrmOfficialAnnounceSendRecordDO sendRecordDO = result.get(simpleModel.getUniqueCode());
            if (sendRecordDO != null) {
                if (sendRecordDO.getOrgPath().length() > simpleModel.getOrgName().length()) {
                    sendRecordDO.setOrgPath(simpleModel.getOrgName());
                }
            } else {
                sendRecordDO = new CfBdcrmOfficialAnnounceSendRecordDO();
                sendRecordDO.setOfficialAnnounceId(officialAnnounceId);
                sendRecordDO.setOperatorUserId(operatorId);
                sendRecordDO.setOperatorUserName(operatorName);
                sendRecordDO.setUniqueCode(Optional.ofNullable(uniqueCodeMapVolunteer.get(simpleModel.getUniqueCode())).map(CrowdfundingVolunteer::getUniqueCode).orElse(""));
                sendRecordDO.setMis(simpleModel.getMis());
                sendRecordDO.setName(simpleModel.getMisName());
                sendRecordDO.setWorkStatus(CrowdfundingVolunteerEnum.WorkStatusEnum.ON_THE_JOB.getValue());
                sendRecordDO.setOrgPath(simpleModel.getOrgName());
            }
            result.put(sendRecordDO.getUniqueCode(), sendRecordDO);
        }
        return Lists.newArrayList(result.values());
    }
}
