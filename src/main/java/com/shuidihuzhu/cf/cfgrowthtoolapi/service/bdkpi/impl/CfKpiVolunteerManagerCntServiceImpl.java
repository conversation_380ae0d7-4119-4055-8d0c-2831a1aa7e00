package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiVolunteerManagerCntDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiVolunteerManagerCntService;
import com.shuidihuzhu.cf.dao.CfKpiVolunteerManagerCntDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * kpi-人效数据(CfKpiVolunteerManagerCnt)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-24 14:21:07
 */
@Service("cfKpiVolunteerManagerCntService")
public class CfKpiVolunteerManagerCntServiceImpl implements CfKpiVolunteerManagerCntService {

    @Resource
    private CfKpiVolunteerManagerCntDao cfKpiVolunteerManagerCntDao;


    @Override
    public int insert(CfKpiVolunteerManagerCntDO cfKpiVolunteerManagerCntDO) {
        List<CfKpiVolunteerManagerCntDO> dbDataList = cfKpiVolunteerManagerCntDao.listByDayKey(cfKpiVolunteerManagerCntDO.getDayKey());
        for (CfKpiVolunteerManagerCntDO dbData : dbDataList) {
            //数据相同不处理
            if (Objects.equals(dbData.getVolunteerCnt(), cfKpiVolunteerManagerCntDO.getVolunteerCnt())
                    && Objects.equals(dbData.getRegionGl(), cfKpiVolunteerManagerCntDO.getRegionGl())
                    && Objects.equals(dbData.getBdArea(), cfKpiVolunteerManagerCntDO.getBdArea())) {
                return 0;
            }
        }
        return cfKpiVolunteerManagerCntDao.insert(cfKpiVolunteerManagerCntDO);
    }

    @Override
    public int update(CfKpiVolunteerManagerCntDO cfKpiVolunteerManagerCntDO) {
        return cfKpiVolunteerManagerCntDao.update(cfKpiVolunteerManagerCntDO);
    }

    @Override
    public List<CfKpiVolunteerManagerCntDO> listByDayKey(String dayKey) {
        return cfKpiVolunteerManagerCntDao.listByDayKey(dayKey);
    }

}
