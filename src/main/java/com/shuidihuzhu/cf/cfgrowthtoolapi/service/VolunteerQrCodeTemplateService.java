package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.VolunteerQrCodeTemplate;

/**
 * 新二维码暂存表(VolunteerQrCodeTemplate)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-09 18:42:29
 */
public interface VolunteerQrCodeTemplateService {

    VolunteerQrCodeTemplate queryById(long id);

    VolunteerQrCodeTemplate queryByUniqueCode(String uniqueCode);

    int insert(VolunteerQrCodeTemplate volunteerQrCodeTemplate);

    int update(VolunteerQrCodeTemplate volunteerQrCodeTemplate);

    boolean deleteById(long id);

}
