package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCrmHospitalGpsAnalysisDO;
import com.shuidihuzhu.cf.dao.CfCrmHospitalGpsAnalysisDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfCrmHospitalGpsAnalysisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 线下顾问医院时长(CfCrmHospitalGpsAnalysis)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-15 21:10:37
 */
@Service
public class CfCrmHospitalGpsAnalysisServiceImpl implements CfCrmHospitalGpsAnalysisService {
   
    @Resource
    private CfCrmHospitalGpsAnalysisDao cfCrmHospitalGpsAnalysisDao;

    @Override
    public CfCrmHospitalGpsAnalysisDO queryById(long id) {
        return cfCrmHospitalGpsAnalysisDao.queryById(id);
    }
    

    @Override
    public int insert(CfCrmHospitalGpsAnalysisDO cfCrmHospitalGpsAnalysis) {
        return cfCrmHospitalGpsAnalysisDao.insert(cfCrmHospitalGpsAnalysis);
    }

}
