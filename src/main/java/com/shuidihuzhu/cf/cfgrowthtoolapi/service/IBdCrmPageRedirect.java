package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.response.OpResult;

/**
 * <AUTHOR>
 * @date 2019-07-10
 */
public interface IBdCrmPageRedirect {

    /**
     * 获取是否跳转鹰眼
     * 1、不调起鹰眼实验
     * 2、调鹰眼实验
     * @param userId
     * @param clientIp
     * @return
     */
    OpResult<Integer> queryPage(long userId, String channel, String clientIp);

    /**
     * 登记线索发送mq消息至clewtrack
     * @param userId
     * @param phone
     * @param clientIp
     * @return
     */
    OpResult registerPhone(long userId, String phone, String clientIp);

    OpResult registerPhone(long userId, String phone, String clientIp, String introducerEncryMobile);
}
