package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKpiBdScoreSubmitModel;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
public interface ICfKpiStatDataService {

    //查看打分进度
    OpResult<List<CfKpiBdScoreSubmitModel>> queryKpiScoreStatus(String monthKey, String name, int commitStatus, int level);

    //添加一个报警信息
    void addAlarmInfo(String monthKey);

}
