package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdGpsMessageReportDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.GpsReportSearchParam;

import java.util.List;

/**
 * bd上报gps分析(BdGpsMessageReport)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-08 15:05:40
 */
public interface BdGpsMessageReportService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    BdGpsMessageReportDO queryById(long id);


    /**
     * 新增数据
     *
     * @param bdGpsMessageReportDO 实例对象
     * @return 实例对象
     */
    int insert(BdGpsMessageReportDO bdGpsMessageReportDO);


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);


    List<BdGpsMessageReportDO> listByAdminGpsSearch(GpsReportSearchParam searchParam);


    long countAdminGpsSearch(GpsReportSearchParam searchParam);

}