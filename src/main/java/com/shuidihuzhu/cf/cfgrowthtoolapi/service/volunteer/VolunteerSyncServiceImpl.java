package com.shuidihuzhu.cf.cfgrowthtoolapi.service.volunteer;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IXrxsDelegateV2;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.shorturl.ShortUrlDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.VolunteerAutoChangeModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.VolunteerOrgRelationVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.excel.NoActivityVolunteerModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.IBdMemberSnapshotJobService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.IBdMemberSnapshotService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.*;
import com.shuidihuzhu.cf.dao.CrowdfundingVolunteerDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCaseInfoDao;
import com.shuidihuzhu.cf.dao.bdcrm.PreVolunteerOrgInfoRelationDao;
import com.shuidihuzhu.cf.dao.lovepartner.CfPartnerInfoDao;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.enums.VolunteerEnums;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.information.organization.model.cf.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-11-17 5:48 下午
 **/
@Slf4j
@Service
public class VolunteerSyncServiceImpl implements VolunteerSyncService {


    @Autowired
    private ICrmSelfBuiltOrgReadService crmOrganizationService;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private CrowdfundingVolunteerDao volunteerDao;

    @Autowired
    private CfBdCaseInfoDao cfBdCaseInfoDao;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private IXrxsDelegateV2 xrxsDelegateV2;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Autowired
    private IBdMemberSnapshotService snapshotService;

    @Autowired
    private ShortUrlDelegate shortUrlDelegate;

    @Autowired
    private IBdMemberSnapshotJobService snapshotJobService;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private ICrmOrganizationRelationService relationService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private PreVolunteerOrgInfoRelationDao preVolunteerOrgInfoRelationDao;

    @Autowired
    private CfPartnerInfoDao cfPartnerInfoDao;

    @Autowired
    private ApplicationService applicationService;

    @Override
    public void syncBDCaseInfo() {
        String startDate = DateTime.now().minusDays(40).toString("yyyy-MM-dd");
        String endDate = DateTime.now().minusDays(2).toString("yyyy-MM-dd");
        List<CrowdfundingVolunteer> allVolunteerList = listAllOfflineVolunteer();
        //筛选出来在职的
        allVolunteerList = allVolunteerList.stream().filter(item -> item.getWorkStatus() == 2).collect(Collectors.toList());
        for (CrowdfundingVolunteer volunteer : allVolunteerList) {
            if (StringUtils.isBlank(volunteer.getMis())) {
                log.info("volunteer:{}名称:{}没有对应的mis信息", volunteer.getUniqueCode(), volunteer.getVolunteerName());
            }
            CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoDao.checkExistNoOrgIdByUniqueCode(volunteer.getUniqueCode(), startDate, endDate);
            boolean isExistNeedUpdateData = cfBdCaseInfoDo != null;
            //查看有没有bd_case_info数据org_id = 0 的数据,如果有才更新
            if (isExistNeedUpdateData) {
                log.info("volunteer:{}存在orgId为0的数据", volunteer.getUniqueCode());
                ICrmMemberInfoService.BdCrmOrganizationDOWithChain rightBdCaseOrg = memberInfoService.getRightBdCaseOrg(volunteer.getUniqueCode());
                int orgId = 0;
                String orgPath = "";
                if (rightBdCaseOrg != null) {
                    orgId = (int) rightBdCaseOrg.getId();
                    orgPath = rightBdCaseOrg.getChain();
                }
                if (orgId > 0) {
                    cfBdCaseInfoService.updateNoOrgIdMis(volunteer.getUniqueCode(), volunteer.getMis(), orgId, orgPath);
                }
            }
        }
    }

    @Override
    public void syncMemberInfoFromXrxs() {
        //获取所有在职
        List<CrowdfundingVolunteer> allVolunteerList = listAllNeedCheckVolunteer();
        //找到mis或者工号
        for (CrowdfundingVolunteer volunteer : allVolunteerList) {
            syncOneVolunteerFromXrxs(volunteer, true);
        }
    }


    @Override
    public void syncOneVolunteerFromXrxs(CrowdfundingVolunteer volunteer, Boolean needPublishEvent) {
        //如果是渠道直营可以继续获取对应的工号和入离职信息
        boolean directChannel = CrowdfundingVolunteerUtil.isDirectChannel(volunteer);
        if (!directChannel && Objects.equals(volunteer.getPartnerTag(), CrowdfundingVolunteerEnum.PartnerTagEnum.partner.getCode())) {
            log.info("兼职无需通过xrxs同步账户信息volunteer:{}", volunteer);
            return;
        }
        UserInfo userInfoForCf = xrxsDelegateV2.getUserInfoForCf(volunteer.getMis(), volunteer.getJobNum());
        if (userInfoForCf != null) {
            //判断是否需要更新
            VolunteerAutoChangeModel changeModel = needUpdate(userInfoForCf, volunteer);
            if (StringUtils.isBlank(changeModel.getLeaveTime())
                    && StringUtils.isBlank(changeModel.getEntryTime())
                    && StringUtils.isBlank(changeModel.getJobNum())) {
                return;
            }
            log.info("准备修改数据,changeModel:{},员工状态:{}", JSON.toJSONString(changeModel), volunteer.getWorkStatus());
            if (ObjectUtils.nullSafeEquals(changeModel.getWorkStatus(), CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue())) {
                //当员工离职时，自动将二维码状态，改为冻结
                changeModel.setQrCodeStatus(1);
            }
            cfVolunteerService.updateEntryLeaveTime(changeModel);

            if (ObjectUtils.nullSafeEquals(changeModel.getWorkStatus(), CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue())
                    && !ObjectUtils.nullSafeEquals(changeModel.getWorkStatus(), volunteer.getWorkStatus())) {
                //更新下volunteer中的状态和离职时间
                volunteer.setWorkStatus(CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue());
                if (StringUtils.isNotBlank(changeModel.getLeaveTime())) {
                    //离职时间取23:59  手动填写和xrxs的都需要这么处理
                    Date date = GrowthtoolUtil.dateTimeFormatter.parseDateTime(changeModel.getLeaveTime()).plusDays(1).minusMinutes(1).toDate();
                    volunteer.setLeaveTime(date);
                }
                Date realDate = new Date();
                boolean sendAlarm = this.haveLover(volunteer.getUniqueCode());
                boolean isProduction = applicationService.isProduction();
                if (sendAlarm && isProduction) {
                    String levelDesc = Objects.requireNonNull(CrowdfundingVolunteerEnum.RoleEnum.parse(volunteer.getLevel())).getDesc();
                    this.alarmByFeiShu(volunteer.getVolunteerName(), volunteer.getUniqueCode(), levelDesc, realDate);
                }
                if (needPublishEvent) {
                    customEventPublisher.publish(new CrowdfundingVolunteerChangeEvent(this, volunteer));
                }
            }
            //如果是要添加操作记录
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(volunteer.getId()), OperateTypeEnum.EDIT_BY_SYSTEM.getDesc(),
                    OperateTypeEnum.EDIT_BY_SYSTEM, JSON.toJSONString(volunteer), 0L, "xrxs-system"));
        }
    }


    public void alarmByFeiShu(String name, String uniqueCode, String levelDesc, Date leaveTime) {
        StringBuilder content = new StringBuilder();
        String orgPath = "";
        VolunteerOrgRelationVO orgRelationVO = this.getOrgPath(uniqueCode);
        if (CollectionUtils.isNotEmpty(orgRelationVO.getRelatedOrg())) {
            orgPath = orgRelationVO.getRelatedOrg().get(0);
        }

        content.append("离职人员：").append(name).append("(").append(levelDesc).append(orgPath).append(")").append("\n");
        content.append("离职时间：").append(DateUtil.formatDateTime(leaveTime)).append("\n");
        content.append("操作路径：sea后台-线下管理-兼职系统-爱心伙伴列表；").append("\n");
        content.append("为不影响账单生成，请及时处理！");
        String msg = content.toString();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(leaveTime);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        //当日22点后至次日8点间的消息延时至次日8点后发送，其他时间立刻发送,计算到次日八点需要延时几小时
        if (hour >= 22) {
            long delayTime = DateUtils.MILLIS_PER_HOUR * (32 - hour);
            mqProducerService.sendLoverResignDelayMsg(msg, delayTime);
        } else if (hour < 8) {
            long delayTime = DateUtils.MILLIS_PER_HOUR * (8 - hour);
            mqProducerService.sendLoverResignDelayMsg(msg, delayTime);
        } else {
            //推送给飞书
            AlarmBotService.sentText("b2872974-a620-4ee6-a428-14172b1490fc", msg, null, null);
        }
    }

    //判断是否有合作中的爱心伙伴
    public boolean haveLover(String uniqueCode) {
        int myPartnerCount = cfPartnerInfoDao.getMyPartenrCountByWorkStatus(uniqueCode, WorkStatusEnum.ON_OFFICE.getCode());
        return myPartnerCount > 0;
    }

    public VolunteerOrgRelationVO getOrgPath(String uniqueCode) {
        VolunteerOrgRelationVO volunteerOrgRelationVO = new VolunteerOrgRelationVO();
        volunteerOrgRelationVO.setRelatedOrg(Lists.newArrayList());
        List<BdCrmOrgUserRelationDO> relationDOS = relationService.listMemberOrgRelationByUniqueCode(uniqueCode);
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(relationDOS)){
            volunteerOrgRelationVO.setPreBinding(false);
            List<Long> orgIdList = relationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList());
            Map<Long, String> mapStr = crmSelfBuiltOrgReadService.listChainByOrgIds(orgIdList,"/");
            for(Map.Entry<Long,String> entry: mapStr.entrySet()){
                volunteerOrgRelationVO.getRelatedOrg().add(entry.getValue());
            }
            return volunteerOrgRelationVO;
        } else {
            PreVolunteerOrgInfoRelationDO relationDO = preVolunteerOrgInfoRelationDao.getPreVolunteerOrgInfoRelationDO(uniqueCode);
            if(relationDO != null){
                volunteerOrgRelationVO.setPreBinding(true);
                Map<Long, String> mapStr = crmSelfBuiltOrgReadService.listChainByOrgIds(Lists.newArrayList(Long.valueOf(relationDO.getOrgId())),"/");
                for(Map.Entry<Long,String> entry: mapStr.entrySet()){
                    volunteerOrgRelationVO.getRelatedOrg().add(entry.getValue());
                }
            }
            return volunteerOrgRelationVO;
        }
    }


    private VolunteerAutoChangeModel needUpdate(UserInfo userInfo, CrowdfundingVolunteer volunteer) {
        VolunteerAutoChangeModel volunteerChangeModel = new VolunteerAutoChangeModel();
        String entryTimeStr = "";
        String leaveTimeStr = "";
        Date entryTime = volunteer.getEntryTime();
        Date leaveTime = volunteer.getLeaveTime();
        volunteerChangeModel.setId(volunteer.getId());
        volunteerChangeModel.setUniqueCode(volunteer.getUniqueCode());
        if (entryTime != null) {
            entryTimeStr = new DateTime(entryTime).toString(GrowthtoolUtil.ymdfmt);
        }
        if (leaveTime != null) {
            leaveTimeStr = new DateTime(leaveTime).toString(GrowthtoolUtil.ymdfmt);
        }
        String entryDateFormXrxs = userInfo.getEntryDate();
        if (StringUtils.isNotBlank(userInfo.getTwiceEntryDate()) && !Objects.equals(userInfo.getTwiceEntryDate(), entryDateFormXrxs)) {
            entryDateFormXrxs = userInfo.getTwiceEntryDate();
        }
        //正常模式下,如果人员数据和xrxs不一致需要更新
        if (volunteer.getEntryModel() == VolunteerEnums.EntryLeaveModel.normal.getCode() && !ObjectUtils.nullSafeEquals(entryTimeStr, entryDateFormXrxs)) {
            volunteerChangeModel.setEntryTime(entryDateFormXrxs);
        }
        if (volunteer.getLeaveModel() == VolunteerEnums.EntryLeaveModel.normal.getCode() && !ObjectUtils.nullSafeEquals(leaveTimeStr, userInfo.getDismissionDate())) {
            volunteerChangeModel.setLeaveTime(userInfo.getDismissionDate());
            if (StringUtils.isNotBlank(userInfo.getDismissionDate())) {
                volunteerChangeModel.setWorkStatus(CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue());
            }
        }
        if (!ObjectUtils.nullSafeEquals(userInfo.getJobNumber(), volunteer.getJobNum())) {
            volunteerChangeModel.setJobNum(userInfo.getJobNumber());
        }
        return volunteerChangeModel;
    }


    private List<CrowdfundingVolunteer> listAllNeedCheckVolunteer() {
        //所有在职的
        List<CrowdfundingVolunteer> volunteers = listAllOfflineVolunteer();
        //最近一个月离职的
        List<CrowdfundingVolunteer> nearlyLeaveVolunteer = getNearlyLeaveVolunteer();
        volunteers.addAll(nearlyLeaveVolunteer);
        return volunteers;
    }

    @NotNull
    private List<CrowdfundingVolunteer> listAllOfflineVolunteer() {
        //获取所有的线下人员信息
        int size = 500, pageNum = 0;
        List<CrowdfundingVolunteer> allVolunteerList = Lists.newArrayList();
        //找到所有uniqueCode
        while (size == 500) {
            List<CrowdfundingVolunteer> allOfflineVolunteer = volunteerDao.getAllOfflineVolunteer(pageNum * size, size);
            size = allOfflineVolunteer.size();
            if (CollectionUtils.isNotEmpty(allOfflineVolunteer)) {
                pageNum++;
            }
            allVolunteerList.addAll(allOfflineVolunteer);
        }
        return allVolunteerList;
    }


    @Override
    public List<CrowdfundingVolunteer> listAllOnWorkVolunteer() {
        //获取所有的线下人员信息
        int size = 500, pageNum = 0;
        List<CrowdfundingVolunteer> allVolunteerList = Lists.newArrayList();
        //找到所有uniqueCode
        while (size == 500) {
            List<CrowdfundingVolunteer> allOfflineVolunteer = volunteerDao.listVolunteer(pageNum * size, size);
            size = allOfflineVolunteer.size();
            if (CollectionUtils.isNotEmpty(allOfflineVolunteer)) {
                pageNum++;
            }
            allVolunteerList.addAll(allOfflineVolunteer);
        }
        return allVolunteerList;
    }

    @Override
    public List<CrowdfundingVolunteer> getNearlyLeaveVolunteer() {
        DateTime startDate = DateTime.now().withTimeAtStartOfDay().minusMonths(1);
        //获取所有的线下人员信息
        int size = 500, pageNum = 0;
        List<CrowdfundingVolunteer> allVolunteerList = Lists.newArrayList();
        //找到所有uniqueCode
        while (size == 500) {
            List<CrowdfundingVolunteer> nearlyLeaveVolunteerList = volunteerDao.getNearlyLeaveVolunteer(startDate.toDate(), pageNum * size, size);
            size = nearlyLeaveVolunteerList.size();
            if (CollectionUtils.isNotEmpty(nearlyLeaveVolunteerList)) {
                pageNum++;
            }
            allVolunteerList.addAll(nearlyLeaveVolunteerList);
        }
        return allVolunteerList;
    }


    @Override
    public void syncMemberWorkStatus() {
        //获取所有的人员
        List<BdCrmOrganizationDO> allOrg = crmOrganizationService.getAllOrg();
        for (BdCrmOrganizationDO bdCrmOrganizationDO : allOrg) {
            List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = crmOrganizationRelationService.listRelationByOrgId(bdCrmOrganizationDO.getId());
            for (BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO : bdCrmOrgUserRelationDOS) {
                CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(bdCrmOrgUserRelationDO.getUniqueCode());
                if (volunteer != null && volunteer.getWorkStatus() == 1) {
                    log.info("组织上没有自动离职的人员:{}", volunteer.getUniqueCode());
                    crmOrganizationRelationService.autoDeleteMemberId(bdCrmOrgUserRelationDO.getMis(), "同步任务-人员离职自动同步" + volunteer.getVolunteerName());
                }
            }
        }
    }



    @Override
    public void syncAllGrMember() {
        //找到组织下的所有人员
        int orgId = apolloService.getGrOrgId();
        if (orgId == 0) {
            return;
        }
        log.info("gr组织:{}", orgId);
        List<BdCrmOrganizationDO> organizationDOS = crmOrganizationService.listAllSubOrgIncludeSelf(orgId);
        List<Long> orgIdList = organizationDOS.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
        //找到所有qr成员
        List<BdCrmOrgUserRelationDO> relationDOList = crmOrganizationRelationService.listByOrgIds(orgIdList);
        Set<String> uniqueCodeInGr = relationDOList.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).collect(Collectors.toSet());
        //对比数据
        List<CrowdfundingVolunteer> qrInDB = volunteerDao.listAllGrMember();
        Set<String> uniqueCodeHasTag = qrInDB.stream().map(CrowdfundingVolunteer::getUniqueCode).collect(Collectors.toSet());
        //删除 在uniqueCodeHasTag 不在uniqueCodeInGr
        List<String> needDeleteGrTagUniqueList = Lists.newArrayList(Sets.difference(uniqueCodeHasTag, uniqueCodeInGr).immutableCopy());
        if (CollectionUtils.isNotEmpty(needDeleteGrTagUniqueList)) {
            log.info("需要删除gr标识的uniqueCodeList:{}", needDeleteGrTagUniqueList);
            cfVolunteerService.updateGrTag(needDeleteGrTagUniqueList, 0);
        }
        //需要添加标识 在uniqueCodeInQr 不在uniqueCodeHasTag
        List<String> needAddGrTagUniqueList = Lists.newArrayList(Sets.difference(uniqueCodeInGr, uniqueCodeHasTag).immutableCopy());
        if (CollectionUtils.isNotEmpty(needAddGrTagUniqueList)) {
            log.info("需要添加gr标识的uniqueCodeList:{}", needAddGrTagUniqueList);
            cfVolunteerService.updateGrTag(needAddGrTagUniqueList, 1);
        }
    }

    @Override
    public void alarmNoRaiseVolunteer() {
        DateTime today = DateTime.now().withTimeAtStartOfDay();
        DateTime monthAgo = today.minusDays(30);
        DateTime halfYearAgo = today.minusDays(180);
        List<NoActivityVolunteerModel> noActivityVolunteerModels = Lists.newArrayList();
        listAllOfflineVolunteer().stream()
                .filter(item -> CrowdfundingVolunteerEnum.commonRoles.contains(item.getLevel()))
                .filter(item -> new DateTime(item.getCreateTime()).isBefore(halfYearAgo))
                .forEach(item -> {
                    long caseCount = cfBdCaseInfoService.getCountByUniqueCodeList(Lists.newArrayList(item.getUniqueCode()), monthAgo.toString(GrowthtoolUtil.ymdfmt), today.toString(GrowthtoolUtil.ymdfmt));
                    if (caseCount <= 5) {
                        noActivityVolunteerModels.add(new NoActivityVolunteerModel(item.getMis(), item.getVolunteerName()));
                    }
                });

        String fileName = halfYearAgo.toString(GrowthtoolUtil.ymdfmt) + "至" + today.toString(GrowthtoolUtil.ymdfmt) + "在职顾问未发起案例" + ".xlsx";
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        log.info("volunteer excel data size:{}", noActivityVolunteerModels.size());
        EasyExcel.write(out, NoActivityVolunteerModel.class)
                .sheet("0")
                .doWrite(noActivityVolunteerModels);

        //上传资源
        String url = CosUploadUtil.uploadCosFile(fileName, out);
        log.info("科室数据CosUploadUtil.uploadCosFile url:{}", url);
        //生成短链接
        url = shortUrlDelegate.process(url);
        //给对应的申请人发消息
        String content = halfYearAgo.toString(GrowthtoolUtil.ymdfmt) + "至" + today.toString(GrowthtoolUtil.ymdfmt) + "在职顾问未发起案例（or发起小于5）名单:" + url;
        AlarmBotService.sentText("f2ff70a7-2430-43a6-9ce2-2e13bedadb36", content, null, null);
    }

    @Override
    public void addPartnerToSnapshot(String monthKey) {

        //获取所有的线下人员信息
        List<CrowdfundingVolunteer> allVolunteerList = listAllOnWorkVolunteer();
        List<CrowdfundingVolunteer> partnerVolunteerList = allVolunteerList
                .stream()
                .filter(item -> item.getPartnerTag() == CrowdfundingVolunteerEnum.PartnerTagEnum.partner.getCode())
                .collect(Collectors.toList());
        List<String> uniqueCodeList = snapshotService.listAllMemberSnapshot(monthKey)
                .stream()
                .map(CfCrmMemberSnapshotDO::getUniqueCode)
                .collect(Collectors.toList());
        List<CrowdfundingVolunteer> needAddPartnerList = partnerVolunteerList.stream()
                .filter(item -> !uniqueCodeList.contains(item.getUniqueCode()))
                .collect(Collectors.toList());
        List<CfCrmMemberSnapshotDO> needAdd = Lists.newArrayList();
        for (CrowdfundingVolunteer volunteer : needAddPartnerList) {
            CfCrmMemberSnapshotDO memberSnapshot = snapshotJobService.createMemberSnapshotV2(volunteer);
            if (memberSnapshot != null) {
                memberSnapshot.setDateKey(monthKey);
                log.info("添加兼职:{}", memberSnapshot);
                needAdd.add(memberSnapshot);
            }
        }
        snapshotService.addBatch(needAdd);
    }

}
