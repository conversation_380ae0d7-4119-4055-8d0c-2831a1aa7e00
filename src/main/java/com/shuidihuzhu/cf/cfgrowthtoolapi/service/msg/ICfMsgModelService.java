package com.shuidihuzhu.cf.cfgrowthtoolapi.service.msg;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.CfMsgModelVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.MsgQueryParam;
import com.shuidihuzhu.cf.response.OpResult;

/**
 * <AUTHOR>
 * @date 2020-08-10
 */
public interface ICfMsgModelService {

    OpResult<CommonResultModel<CfMsgModelVO>> listMsgModel(MsgQueryParam msgQueryParam);

    OpResult<Long> saveOrUpdateMsgModel(CfMsgModelVO cfMsgModelVO);

    OpResult<Void> saveModelSendMethod(CfMsgModelVO cfMsgModelVO);

    OpResult<Void> saveModelStatus(CfMsgModelVO cfMsgModelVO);

    CfMsgModelVO getMsgModelById(Long id);

    int updateSendComplete(Long id,Integer sendStatus);

    OpResult<CfMsgModelVO> getMsgModel(MsgQueryParam msgQueryParam);
}
