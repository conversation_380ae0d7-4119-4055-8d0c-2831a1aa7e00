package com.shuidihuzhu.cf.cfgrowthtoolapi.service;


import com.shuidihuzhu.client.cf.clewtrack.model.CfClewQyWxCorpDO;
import com.shuidihuzhu.client.cf.growthtool.model.MarkTagModel;
import com.shuidihuzhu.mdc.client.model.MdcQyWechatFriendModel;

public interface IPatientSheQunTagService {
    String addOrRemoveTag(MarkTagModel markTagModel);

    String getOpenId(long userId, int miaoYiThirdType);

    MarkTagModel buildMarkTagModel(MdcQyWechatFriendModel mdcQyWechatFriendModel);

    MarkTagModel handleMarkTagModel(MarkTagModel markTagModel,Boolean miaoYiSubscribeFlag);

    String handleMarkTagModelByGroupName(String groupName,String tagName,CfClewQyWxCorpDO cfClewWxCorpMsgDO,String userID, String externalUserID);

    String addOrRemoveTagByCorp(MarkTagModel markTagModel, CfClewQyWxCorpDO cfClewWxCorpMsgDO);
}
