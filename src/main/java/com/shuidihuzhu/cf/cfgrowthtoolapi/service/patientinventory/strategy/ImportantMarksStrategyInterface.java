package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory.strategy;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.PatientInventoryPatientFollowUpRecordDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.PatientInventoryPatientInfoDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20  17:18
 */
public interface ImportantMarksStrategyInterface {


    boolean support(int departmentType);


    boolean getNeedToFollowUpTodayLabel(PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo,
                                        String dateline,
                                        int volunteerLevel);

    boolean getPayCloseAttentionToLabel(List<PatientInventoryPatientFollowUpRecordDo> patientInventoryPatientFollowUpRecordDoList,
                                        PatientInventoryPatientInfoDo patientInventoryPatientInfoDo,
                                        String dateline,
                                        int volunteerLevel);
}
