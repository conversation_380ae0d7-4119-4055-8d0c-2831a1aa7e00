package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgAndCityRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.BdOrgAndCityRelationModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IBdCrmOrgAndCityRelationService;
import com.shuidihuzhu.cf.dao.bdcrm.BdCrmOrgAndCityRelationDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Component
@Slf4j
@RefreshScope
public class BdCrmOrgAndCityRelationServiceImpl implements IBdCrmOrgAndCityRelationService {

    @Autowired
    private BdCrmOrgAndCityRelationDao bdCrmOrgAndCityRelationDao;

    @Override
    public List<BdCrmOrgAndCityRelationDO> getByOrgId(long orgId) {
        return bdCrmOrgAndCityRelationDao.getByOrgId(orgId);
    }

    @Override
    public void batchAdd(long orgId, List<BdOrgAndCityRelationModel.BindCityModel> bindList) {
        if(CollectionUtils.isEmpty(bindList)){
            return;
        }
        List<BdCrmOrgAndCityRelationDO> addList = Lists.newArrayList();
        Set<Integer> cityIds = Sets.newHashSet();
        String marketShareObjective = Optional.ofNullable(bdCrmOrgAndCityRelationDao.getMarketShareObjectiveByOrderId(orgId)).orElse("");
        for(BdOrgAndCityRelationModel.BindCityModel cityModel : bindList){
            if(cityIds.contains(cityModel.getId())){
                continue;
            }
            BdCrmOrgAndCityRelationDO cityRelationDO = new BdCrmOrgAndCityRelationDO();
            cityRelationDO.setOrgId(orgId);
            cityRelationDO.setBindType(cityModel.getLevel());
            cityRelationDO.setCityId(cityModel.getId());
            cityRelationDO.setCityName(cityModel.getName());
            cityRelationDO.setMarketShareObjective(marketShareObjective);
            addList.add(cityRelationDO);
            cityIds.add(cityModel.getId());
        }
        bdCrmOrgAndCityRelationDao.batchAdd(addList);
    }

    @Override
    public int batchDeleteByIds(long orgId, List<Integer> cityIdList) {
        if(CollectionUtils.isEmpty(cityIdList)){
            return 0;
        }
        return bdCrmOrgAndCityRelationDao.batchDeleteByIds(orgId,cityIdList);
    }

    @Override
    public List<BdCrmOrgAndCityRelationDO> getAll() {
        return bdCrmOrgAndCityRelationDao.getAll();
    }

    @Override
    public List<BdCrmOrgAndCityRelationDO> getByOrgIds(List<Long> orgIdList) {
        if(CollectionUtils.isEmpty(orgIdList)){
            return Lists.newArrayList();
        }
        return bdCrmOrgAndCityRelationDao.getByOrgIds(orgIdList);
    }

    @Override
    public void updateMarketShareObjective(Long orgId, String marketShareObjective) {
        if (orgId<=0 || marketShareObjective==null) return;
        bdCrmOrgAndCityRelationDao.updateMarketShareObjectiveByOrgId(orgId, marketShareObjective);
    }
}
