package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.KpiImportDataDO;

import java.util.Date;
import java.util.List;

/**
 * 筹绩效导入数据(KpiImportData)表服务接口
 *
 * <AUTHOR>
 * @since 2022-09-29 10:49:08
 */
public interface IKpiImportDataService {

    KpiImportDataDO queryById(long id);

    void batchInsert(List<KpiImportDataDO> importDataList, int importType);

    int update(KpiImportDataDO kpiImportData);

    boolean deleteById(long id);

    int countAll(int importType, Date beginTime);

    List<KpiImportDataDO> pageList(int importType, Date beginTime, int offset, int limit);


    List<KpiImportDataDO> listByMonthKey(String monthKey, int importType);

}
