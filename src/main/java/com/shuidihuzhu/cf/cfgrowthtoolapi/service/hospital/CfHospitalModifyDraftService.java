package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfHospitalModifyInfoDo;

import java.util.List;

/**
 * 医院审核草稿信息
 *
 * @since 2020-11-09 15:46:07
 */
public interface CfHospitalModifyDraftService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CfHospitalModifyInfoDo queryById(long id);


    /**
     * 根据审核查询填写信息
     * @param approveId
     * @return
     */
    List<CfHospitalModifyInfoDo> listByApproveId(int approveId);

    /**
     * 新增数据
     *
     * @param modifyList 实例对象
     */
    void insertBatch(List<CfHospitalModifyInfoDo> modifyList);

    /**
     * 修改数据
     *
     * @param cfHospitalModifyInfoDo 实例对象
     * @return 实例对象
     */
    CfHospitalModifyInfoDo update(CfHospitalModifyInfoDo cfHospitalModifyInfoDo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);

    /**
     * 院区地址是否存在
     *
     * @param areaAddress
     * @return
     */
    boolean isExistByAreaAddress(String areaAddress);

}