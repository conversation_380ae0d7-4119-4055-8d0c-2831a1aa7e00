package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgOptLogDO;
import com.shuidihuzhu.cf.dao.bdcrm.BdCrmOptLogDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-05-18 10:48
 **/
@Service
@Slf4j
public class CrmOptLogServiceImpl implements ICrmOptLogService {


    @Autowired
    private BdCrmOptLogDao bdCrmOptLogDao;


    @Override
    public void addOptLog(BdCrmOrgOptLogDO bdCrmOrgOptLogDO) {
        if (bdCrmOrgOptLogDO == null) {
            return;
        }
        bdCrmOptLogDao.insert(bdCrmOrgOptLogDO);
    }

    @Override
    public int countByOrgId(long orgId) {
        if (orgId <= 0) {
            return 0;
        }
        return bdCrmOptLogDao.countByOrgId(orgId);
    }

    @Override
    public List<BdCrmOrgOptLogDO> pageByOrgId(long orgId, int offset, int limit) {
        if (orgId <= 0) {
            return Lists.newArrayList();
        }
        return bdCrmOptLogDao.pageByOrgId(orgId, offset, limit);
    }

    @Override
    public int countByKeyWord(int optType, String keyWord) {
        return bdCrmOptLogDao.countByKeyWord(optType, keyWord);
    }

    @Override
    public List<BdCrmOrgOptLogDO> pageByKeyWord(int optType, String keyWord, int offset, int limit) {
        return bdCrmOptLogDao.pageByKeyWord(optType, keyWord, offset, limit);
    }

    @Override
    public List<BdCrmOrgOptLogDO> listOptLogByOrgId(long orgId) {
        if (orgId <= 0) {
            return Lists.newArrayList();
        }
        return bdCrmOptLogDao.listOptLogByOrgId(orgId);
    }

    @Override
    public List<BdCrmOrgOptLogDO> listByTimeRange(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return Lists.newArrayList();
        }
        return bdCrmOptLogDao.listByTimeRange(startTime, endTime);
    }
}
