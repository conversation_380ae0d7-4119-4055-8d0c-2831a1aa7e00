package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmLegalAidDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.LegalAidSearchParam;

import java.util.List;

/**
 * 法律援助(BdCrmLegalAid)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-02 16:16:23
 */
public interface BdCrmLegalAidService {

    BdCrmLegalAidDO queryById(long id);

    int countBySearchParam(LegalAidSearchParam legalAidSearchParam);

    List<BdCrmLegalAidDO> listBySearchParam(LegalAidSearchParam legalAidSearchParam);

    BdCrmLegalAidDO getByPhone(String phone);

    BdCrmLegalAidDO getByIdCard(String idCard);

    int insert(BdCrmLegalAidDO bdCrmLegalAidDO);

    int update(BdCrmLegalAidDO bdCrmLegalAidDO);

    int updatePushArea(BdCrmLegalAidDO bdCrmLegalAidDO);

    boolean deleteById(long id);

}
