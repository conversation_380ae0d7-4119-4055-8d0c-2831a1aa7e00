package com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.campaignv2.CrmOrgPreStatisticsDO;

import java.util.List;

/**
 * 每日-每个组织当天累计数据(CrmOrgPreStatistics)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-11 15:48:58
 */
public interface CrmOrgPreStatisticsService {

    void batchInsertOrUpdate(List<CrmOrgPreStatisticsDO> crmOrgPreStatistics, String dateStr, int hourNum);

    int update(CrmOrgPreStatisticsDO crmOrgPreStatistics);

    List<CrmOrgPreStatisticsDO> listByDateKeysOrgId(long orgId, List<String> dateKeys, int maxHour);

    List<CrmOrgPreStatisticsDO> listTrend(long orgId, List<String> dateKeys);

    CrmOrgPreStatisticsDO getHourDataByOrgId(String dateKey, int maxHour, long orgId);

}