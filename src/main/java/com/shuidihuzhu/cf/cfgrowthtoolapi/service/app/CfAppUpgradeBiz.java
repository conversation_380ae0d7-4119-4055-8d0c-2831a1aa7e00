package com.shuidihuzhu.cf.cfgrowthtoolapi.service.app;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfAppUpgrade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfAppUpgradeLog;
import com.shuidihuzhu.cf.model.crowdfunding.app.AppUpgradeInfo;

import java.util.List;

/**
 * Created by lixurui on 18/12/02
 */

public interface CfAppUpgradeBiz {

    AppUpgradeInfo queryVersionByPlatform(String appPlatform);


    List<AppUpgradeInfo> listByPlatform(String appPlatform);

    List<String> queryLogById(int upgradeId);


    //插入升级的信息
    void cfAppUpgradeInsert(CfAppUpgrade cfAppUpgrade);

    //通过ID删除升级相关的信息
    void cfAppUpgradeDelete(int id);

    //修改升级的信息
    void cfAppUpgradeUpdate(CfAppUpgrade cfAppUpdate);

    //查日升级信息对应的更新日志
    void cfAppUpgradeLogInsert(CfAppUpgradeLog cfAppUpgradeLog);

    //通过平台和版本，查询对应升级信息的ID
    Integer cfAppUpgradeIdByVersion(String platform, String version);

    //通过ID删除升级信息对应的更新日志
    void cfAppUpgradeLogDelete(int id);

    //修改更新日志信息
    void cfAppUpgradeLogUpdate(CfAppUpgradeLog cfAppUpgradeLog);

    //查询版本是否已存在
    Integer cfAppUpgradeSelectVersion(String version);

    //返回所有的升级信息
    List<CfAppUpgrade> cfAppUpgradeSelect();

    //通过ID查询对应的更新日志
    List<CfAppUpgradeLog> cfApplogSelect(int id);

    //通过ID删除对应的更新日志
    void cfApplogDelete(int id);

}
