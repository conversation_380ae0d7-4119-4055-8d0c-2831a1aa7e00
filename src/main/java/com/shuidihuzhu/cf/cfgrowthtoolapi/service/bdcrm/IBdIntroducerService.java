package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdIntroducerDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdIntroducerSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdIntroducerVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfIntroducerInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;

/**
 * @author: wanghui
 * @create: 2019/10/17 2:32 PM
 */
public interface IBdIntroducerService {
    /**
     * @author: wanghui
     * @time: 2019/10/17 11:10 AM
     * @description: getInfoByEncryMobile  根据加密的手机号 封装信息返回
     * @param: [introducerEncryMobile]
     * @return: CfBdIntroducerDO
     */
    CfBdIntroducerDO getInfoByEncryMobile(String introducerEncryMobile);

    CfBdIntroducerDO getCfBdIntroducerDOByEncryMobile(String introducerEncryMobile);

    void insert(CfBdIntroducerDO cfBdIntroducerDO);

    void delCfBdIntroducerDOById(long id);

    void updateIntroducerNameById(String introducerName, String operatorName,long id);

    CommonResultModel<CfBdIntroducerVo> getCfBdIntroducerDoList(CfBdIntroducerSearchModel cfBdIntroducerSearchModel);
}
