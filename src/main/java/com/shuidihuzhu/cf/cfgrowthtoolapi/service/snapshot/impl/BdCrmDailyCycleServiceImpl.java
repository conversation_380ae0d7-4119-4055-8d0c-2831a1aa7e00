package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdCrmDailyCycleDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.PageSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.BdCrmDailyCycleService;
import com.shuidihuzhu.cf.dao.snapshot.BdCrmDailyCycleDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 日目标周期信息(BdCrmDailyCycle)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-02 14:59:41
 */
@Service("bdCrmDailyCycleService")
public class BdCrmDailyCycleServiceImpl implements BdCrmDailyCycleService {
   
    @Resource
    private BdCrmDailyCycleDao bdCrmDailyCycleDao;

    @Override
    public BdCrmDailyCycleDO queryById(long id) {
        return bdCrmDailyCycleDao.queryById(id);
    }

    @Override
    public int batchInsert(List<BdCrmDailyCycleDO> bdCrmDailyCycleDOS) {
        if (CollectionUtils.isEmpty(bdCrmDailyCycleDOS)) {
            return 0;
        }
        return bdCrmDailyCycleDao.batchInsert(bdCrmDailyCycleDOS);
    }

    @Override
    public boolean deleteById(long id) {
        return bdCrmDailyCycleDao.deleteById(id) > 0;
    }

    @Override
    public void updateCreateSnapshot(String dateKey) {
        bdCrmDailyCycleDao.updateCreateSnapshot(dateKey);
    }

    @Override
    public List<BdCrmDailyCycleDO> listByDateKey(List<String> dateKeys) {
        if (CollectionUtils.isEmpty(dateKeys)) {
            return List.of();
        }
        return bdCrmDailyCycleDao.listByDateKey(dateKeys);
    }

    @Override
    public int countAllDailyCycle() {
        return Optional.ofNullable(bdCrmDailyCycleDao.countAllDailyCycle()).orElse(0);
    }

    @Override
    public List<BdCrmDailyCycleDO> listAllDailyCycle(PageSearchModel pageSearchModel) {
        return bdCrmDailyCycleDao.listAllDailyCycle(pageSearchModel);
    }

}
