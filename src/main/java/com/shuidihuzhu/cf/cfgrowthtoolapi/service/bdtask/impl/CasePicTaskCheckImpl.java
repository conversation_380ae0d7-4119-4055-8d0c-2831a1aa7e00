package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCaseBaseDataDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.IBdTaskCheckService;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2023-09-20 15:59
 **/
@Service
public class CasePicTaskCheckImpl implements IBdTaskCheckService {

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Override
    public CrmBdTaskDO.TaskTypeEnum getTaskType() {
        return CrmBdTaskDO.TaskTypeEnum.case_pic;
    }

    @Override
    public boolean checkNeedCreateTask(BdTaskContext bdTaskContext) {
        CfCaseBaseDataDo cfCaseBaseDataDo = bdTaskContext.getCfCaseBaseDataDo();
        if (cfCaseBaseDataDo == null) {
            return false;
        }
        Map<Integer, List<CrowdfundingAttachmentVo>> infoMap = crowdFundingFeignDelegate.getByInfoIdList(Lists.newArrayList(bdTaskContext.getCaseId()), AttachmentTypeEnum.ATTACH_CF);
        List<CrowdfundingAttachmentVo> crowdfundingAttachmentVos = infoMap.get(bdTaskContext.getCaseId());
        if (infoMap.get(bdTaskContext.getCaseId()) == null) {
            return false;
        }
        return crowdfundingAttachmentVos.size() < 8;
    }

    @Override
    public boolean checkTaskComplete(BdTaskContext bdTaskContext) {
        return !checkNeedCreateTask(bdTaskContext);
    }

}
