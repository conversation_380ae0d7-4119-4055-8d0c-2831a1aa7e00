package com.shuidihuzhu.cf.cfgrowthtoolapi.service.greenchannel;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.greenchannel.CfClewGreenChannelApplyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.greenchannel.CfGreenChannelApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGreenChannelEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.greenchannel.ApproveBaseParamModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.greenchannel.ApproveRejectParamModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.GreenChannelApplyService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.ICfWeaponApplyHandleService;
import com.shuidihuzhu.cf.dao.greenchannel.CfGreenChannelApproveDao;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.growthtool.model.WeaponApplyCallBackModel;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-10-23 5:30 下午
 **/
@Service
@Slf4j
public class GreenChannelApproveServiceImpl implements IGreenChannelApproveService {

    @Autowired
    private CfGreenChannelApproveDao cfGreenChannelApproveDao;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private GreenChannelApplyService applyService;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Autowired
    private IGreenChannelRouter greenChannelRouter;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private ICfWeaponApplyHandleService weaponApplyHandleService;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public int create(CfGreenChannelApproveDO greenChannelApproveDO) {
        if (greenChannelApproveDO == null || greenChannelApproveDO.getApplyId() <= 0) {
            log.info("插入的数据:{}为空或者没有对应的申请id", JSON.toJSONString(greenChannelApproveDO));
            return 0;
        }
        //确保 applyId + stepCode只有一条记录
        CfGreenChannelEnums.ApplyStatusEnum applyStatusWhenCreate = greenChannelRouter.getApplyStatusWhenCreate(greenChannelApproveDO.getApplyType(), greenChannelApproveDO.getStepCode());
        String lockName = "growthtool_green_channel_create" + greenChannelApproveDO.getApplyId() + "_" + greenChannelApproveDO.getStepCode();
        RLock lock = null;
        int insert = 0;
        try {
            lock = redissonHandler.getLock(lockName);
            if (!lock.tryLock()) {
                log.info("GreenChannelApproveServiceImpl create get lock fail,key:{}", lockName);
                return 0;
            }
            //更新申请记录中的状态
            applyService.modifyApplyStatusById((long) greenChannelApproveDO.getApplyId(), applyStatusWhenCreate, null);
            CfGreenChannelApproveDO approveDO = getByApplyIdAndStepCode(greenChannelApproveDO.getApplyId(), greenChannelApproveDO.getStepCode());
            if (approveDO != null) {
                log.info("已经存在审核记录:{}", JSON.toJSONString(approveDO));
                return insert;
            }
            insert = cfGreenChannelApproveDao.insert(greenChannelApproveDO);
        } finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        pushMsgToApproveHandler(greenChannelApproveDO, ActionType.create, null, "");
        if (applicationService.isProduction()) {
            //发送提醒消息
            mqProducerService.sendGreenChannelApproveDelayMsg(greenChannelApproveDO, getTargetTime(greenChannelApproveDO));
        } else {
            //发送提醒消息
            mqProducerService.sendGreenChannelApproveDelayMsg(greenChannelApproveDO, System.currentTimeMillis() + 2 * 60 * 1000);
        }
        return insert;
    }


    //当超时未处理时，现在是24小时提醒，能否针对这个环节，改为4个工作日？排除周末，从到这个环节开始时计算，按照4个工作日进行提醒
    private long getTargetTime(CfGreenChannelApproveDO greenChannelApproveDO) {
        DateTime now = DateTime.now();
        if (ObjectUtils.nullSafeEquals(greenChannelApproveDO.getStepCode(), CfGreenChannelEnums.StepCodeEnum.four.getCode())
                && ObjectUtils.nullSafeEquals(greenChannelApproveDO.getApplyType(), CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode())) {
            DateTime target = null;
            int dayOfWeek = now.getDayOfWeek();
            if (dayOfWeek == 1 || dayOfWeek == 7) {
                target = now.plusDays(4);
            } else if (dayOfWeek == 6) {
                target = now.plusDays(5);
            } else {
                target = now.plusDays(6);
            }
            return target.getMillis();
        }
        return now.getMillis() + 24 * 3600 * 1000;
    }


    @Getter
    public static enum ActionType {
        //超时
        time_out(0),
        //打回上一级
        back_to_last(1),
        //新建
        create(2),
        ;
        int code;

        ActionType(int code) {
            this.code = code;
        }
    }


    /**
     * 给审核操作人发送消息
     */
    @Override
    public void pushMsgToApproveHandler(CfGreenChannelApproveDO greenChannelApproveDO, ActionType actionType, String backLastComment, String optOperator) {
        StringBuilder msgString = new StringBuilder();
        int applyId = greenChannelApproveDO.getApplyId();
        String title = "";
        switch (actionType) {
            case create:
                title = "【" + CfGreenChannelEnums.StepCodeEnum.parseByCode(greenChannelApproveDO.getStepCode()).getDesc() + "待审核】";
                break;
            case time_out:
                title = "【超时未处理" + "-" + CfGreenChannelEnums.StepCodeEnum.parseByCode(greenChannelApproveDO.getStepCode()).getDesc() + "】";
                break;
            case back_to_last:
                title = "【被打回" + "-" + CfGreenChannelEnums.StepCodeEnum.parseByCode(greenChannelApproveDO.getStepCode()).getDesc() + "待审核】";
                break;
            default:
                break;
        }
        CfClewGreenChannelApplyDO applyDO = applyService.queryApplyById((long) applyId);
        int applyType = greenChannelApproveDO.getApplyType();
        String url = "";
        if (applyType == CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode()) {
            url = "https://sdchou.shuiditech.com/cf#/recommend-act-audit/cf-change-pf-detail/" + applyId + "?type=" + applyType + "&entry=" +  "/recommend-act-audit/cf-change-pf";
        } else if (applyType == CfGreenChannelEnums.ApplyResourceTypeEnum.DOCTOR_RECOMMEND.getCode()) {
            url = "https://sdchou.shuiditech.com/cf#/recommend-act-audit/doctor-recommend-detail/" + applyId + "?type=" + applyType + "&entry=" +  "/recommend-act-audit/doctor-recommend";
        }
        //找到对应的案例信息
        CrowdfundingInfo caseInfo = crowdFundingFeignDelegate.getCaseInfoById(applyDO.getCaseId());
        String time = actionType == ActionType.create ? DateTime.now().toString("yyyy-MM-dd HH:mm:ss") : new DateTime(applyDO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss");
        msgString.append("" + title + "\n");
        if (actionType == ActionType.time_out) {
            msgString.append("您有一个审核超24时未审批，请及时审批。" + "\n");
        }
        if (actionType == ActionType.back_to_last) {
            msgString.append(String.format("打回原因:%s", backLastComment) + "\n");
        }
        List<String> operatorList = greenChannelRouter.listOperator(applyType, greenChannelApproveDO.getStepCode());
        operatorList.forEach(item -> item = "@" + item);
        msgString.append(String.format("后台序列ID:%d", greenChannelApproveDO.getApplyId()) + "\n");
        msgString.append(String.format("案例ID:%d", applyDO.getCaseId()) + "\n");
        msgString.append(String.format("提报时间:%s", time) + "\n");
        msgString.append(String.format("案例标题:%s", caseInfo.getTitle()) + "\n");
        msgString.append(String.format("推荐人:%s", applyDO.getReferrerName()) + "\n");
        msgString.append(String.format("推荐人组织:%s", applyDO.getReferrerOrg()) + "\n");
        msgString.append("当前节点审批人:");
        for (String operator : operatorList) {
            msgString.append("@" + operator + " ");
        }
        msgString.append("\n");
        if (actionType == ActionType.back_to_last) {
            msgString.append(String.format("打回人:%s", optOperator) + "\n");
        }
        msgString.append(String.format("立即查看:%s", "<a href=\"" + url + "\">" + "</a>"));
        String robot = greenChannelRouter.getRobot(applyType);
        if (StringUtils.isNotBlank(robot)) {
            //待审批消息
            AlarmBotService.sentText(robot, msgString.toString(), null, null);
        }
    }

    @Override
    public void pass(CfGreenChannelApproveDO approveDO, String operator, String operatorMis, String fillInfo) {
        //审核通过需要生成一条新的审核
        cfGreenChannelApproveDao.agree(approveDO.getId(), operator, operatorMis, fillInfo);
        log.info("审核:{}通过,填写信息:{}", JSON.toJSONString(approveDO), fillInfo);
    }


    @Override
    public void reject(ApproveRejectParamModel rejectParamModel, String operator, String operatorMis) {
        log.info("审核驳回");
        cfGreenChannelApproveDao.reject(rejectParamModel.getApplyId(), rejectParamModel.getStepCode(), operator, operatorMis, rejectParamModel.getRejectReason());
        CfClewGreenChannelApplyDO channelApplyDO = applyService.queryApplyById((long) rejectParamModel.getApplyId());
        if (Objects.nonNull(channelApplyDO)){
            WeaponApplyCallBackModel weaponApplyCallBackModel = new WeaponApplyCallBackModel();
            weaponApplyCallBackModel.setCaseId(channelApplyDO.getCaseId());
            weaponApplyCallBackModel.setActivityType(CfGreenChannelEnums.parseByApplyResourceType(channelApplyDO.getApplyResourceType()).getActivityTypeEnum().getCode());
            weaponApplyCallBackModel.setApplyResult(false);
            weaponApplyCallBackModel.setComment(rejectParamModel.getRejectReason());
            weaponApplyCallBackModel.setApplyId(rejectParamModel.getApplyId());
            weaponApplyHandleService.handleByActivityCallBack(weaponApplyCallBackModel);
        }
    }


    @Override
    public void resetStepHandleStatus(int applyId, int stepCode, String backLastComment, String operator) {
        //清空填写的信息和驳回原因,操作人也置空
        CfGreenChannelApproveDO channelApproveDO = cfGreenChannelApproveDao.getByApplyIdAndStepCode(applyId, stepCode);
        if (channelApproveDO == null) {
            return;
        }
        cfGreenChannelApproveDao.resetToWait(channelApproveDO.getId());
        //发送消息
        pushMsgToApproveHandler(channelApproveDO, ActionType.back_to_last, backLastComment, operator);
    }

    @Override
    public CfGreenChannelApproveDO getCurrentStepCode(int applyId) {
        if (applyId < 0) {
            return null;
        }
        List<CfGreenChannelApproveDO> approveDOList = listByApplyId(applyId);
        return greenChannelRouter.getCurrentStepCode(approveDOList);
    }

    @Override
    public boolean canBackToLastStep(ApproveBaseParamModel approveBaseParamModel) {
        //当前为第一个环节不能回到上一级
        if (approveBaseParamModel.getStepCode() <= 1) {
            return false;
        }
        List<CfGreenChannelApproveDO> approveDOList = listByApplyId(approveBaseParamModel.getApplyId());
        if (CollectionUtils.isEmpty(approveDOList)) {
            return false;
        }
        return greenChannelRouter.canBackToLastStep(approveDOList, approveBaseParamModel.getStepCode());
    }

    @Override
    public CfGreenChannelApproveDO getByApplyIdAndStepCode(int applyId, int stepCode) {
        if (applyId <= 0 || CfGreenChannelEnums.StepCodeEnum.parseByCode(stepCode) == null) {
            log.info("传参有问题");
            return null;
        }
        return cfGreenChannelApproveDao.getByApplyIdAndStepCode(applyId, stepCode);
    }

    @Override
    public List<CfGreenChannelApproveDO> listByApplyId(int applyId) {
        return cfGreenChannelApproveDao.listByApplyId(applyId);
    }

    @Override
    public void handleCaseEnd(int caseId) {
        //找到对应的申请记录,案例关闭展示不需要修改审核状态
        //List<CfClewGreenChannelApplyDO> applyDOList = applyService.queryApplyByCaseId(caseId);
        //for (CfClewGreenChannelApplyDO applyDO : applyDOList) {
        //    //找到当前申请环节
        //    CfGreenChannelApproveDO currentStepCode = getCurrentStepCode(applyDO.getId().intValue());
        //    if (currentStepCode != null) {
        //        ApproveRejectParamModel approveRejectParamModel = new ApproveRejectParamModel();
        //        approveRejectParamModel.setRejectReason("案例结束关闭");
        //        approveRejectParamModel.setApplyId(currentStepCode.getApplyId());
        //        approveRejectParamModel.setStepCode(currentStepCode.getStepCode());
        //        approveRejectParamModel.setType(currentStepCode.getApplyType());
        //        reject(approveRejectParamModel, "system", "");
        //    }
        //}
    }

    @Override
    public CfGreenChannelApproveDO getById(int id) {
        return cfGreenChannelApproveDao.getById(id);
    }

    @Override
    public Map<Long, List<CfGreenChannelApproveDO>> listByApplyIds(List<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Maps.newHashMap();
        }
        return cfGreenChannelApproveDao.listByApplyIds(applyIds)
                .stream().collect(Collectors.groupingBy(item -> (long)item.getApplyId()));
    }
}
