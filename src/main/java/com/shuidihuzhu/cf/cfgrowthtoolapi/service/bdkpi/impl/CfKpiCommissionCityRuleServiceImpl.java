package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCommissionCityRuleDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPerformanceCityRuleDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfBdKpiEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKPICommissionDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKPICommissionVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.KpiSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCommissionCityRuleService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiCommissionCityRuleDao;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2020-08-03 17:07
 */
@Service
@Slf4j
public class CfKpiCommissionCityRuleServiceImpl implements ICfKpiCommissionCityRuleService {
    @Autowired
    private CfKpiCommissionCityRuleDao cfKpiCommissionCityRuleDao;

    @Override
    public List<CfKpiCommissionCityRuleDO> listAllCommissionRule(String monthKey) {
        return cfKpiCommissionCityRuleDao.listAllCommissionRule(monthKey);
    }

    @Override
    public CfKpiCommissionCityRuleDO getCommissionCityname(String monthKey, Integer cityType, List<Integer> commissionTypeList) {
        return cfKpiCommissionCityRuleDao.getCommissionCityname(monthKey,cityType,commissionTypeList);
    }

    @Override
    public CfKpiCommissionCityRuleDO getCommission(CfKPICommissionDTO cfKPICommission, Integer commissionType) {
        CfKpiCommissionCityRuleDO result = null;
        if (cfKPICommission.getId()>0){
            result = this.getCommission(cfKPICommission.getId());
        }
        if (result!=null){
            return result;
        }
        if (!CfBdKpiEnums.CityTypeEnum.DAN_GE.getType().equals(cfKPICommission.getCityType())){
            result = cfKpiCommissionCityRuleDao.getCommission(cfKPICommission.getMonthKey(),cfKPICommission.getLevel(),cfKPICommission.getCityType(),commissionType);
        }
        log.info(this.getClass().getName()+" getPerformance param:{} result:{}",cfKPICommission,result);
        return result;
    }

    @Override
    public OpResult<Long> saveOrUpdateCommission(CfKpiCommissionCityRuleDO data,List<Integer> commissionTypeList) {
        data.setStatus(CfBdKpiEnums.RuleStatusEnum.WEI_SHENGXIAO.getStatus());
        log.info(this.getClass().getName()+" saveOrUpdateCommission param:{}",String.join(GeneralConstant.splitChar,data.toString(), JSON.toJSONString(commissionTypeList)));
        if (data.getId() > 0) {
            cfKpiCommissionCityRuleDao.update(data);
            return OpResult.createSucResult(data.getId());
        }
        cfKpiCommissionCityRuleDao.insert(data);
        //if (!CfBdKpiEnums.CityTypeEnum.DAN_GE.getType().equals(data.getCityType())){
        //    return OpResult.createSucResult(data.getId());
        //}
        // 如果是单个城市   同时修改成功后，需要将 其他城市类型中 该城市名去除
        handleCityNameExist(data, commissionTypeList);
        return OpResult.createSucResult(data.getId());
    }

    /**
     * @author: wanghui
     * @time: 2020-08-05 22:06
     * @description: handleCityNameExist 处理城市名已经设置规则的情况
     * @param: [ruleDO, data]
     * @return: void
     */
    private void handleCityNameExist(CfKpiCommissionCityRuleDO data,List<Integer> commissionTypeList){
        // 针对 单个城市  渠道 线下 、线上 要特殊处理
        if (data.getCommissionType()== CfBdKpiEnums.CommissionTypeEnum.COMMISSION_TYPE_7.getType()){
            commissionTypeList.removeAll(Lists.newArrayList(CfBdKpiEnums.CommissionTypeEnum.COMMISSION_TYPE_8.getType()));
        }
        if (data.getCommissionType()== CfBdKpiEnums.CommissionTypeEnum.COMMISSION_TYPE_8.getType()){
            commissionTypeList.removeAll(Lists.newArrayList(CfBdKpiEnums.CommissionTypeEnum.COMMISSION_TYPE_7.getType()));
        }
        if (data.getCommissionType()== CfBdKpiEnums.CommissionTypeEnum.COMMISSION_TYPE_21.getType()){
            commissionTypeList.removeAll(Lists.newArrayList(CfBdKpiEnums.CommissionTypeEnum.COMMISSION_TYPE_22.getType()));
        }
        if (data.getCommissionType()== CfBdKpiEnums.CommissionTypeEnum.COMMISSION_TYPE_22.getType()){
            commissionTypeList.removeAll(Lists.newArrayList(CfBdKpiEnums.CommissionTypeEnum.COMMISSION_TYPE_21.getType()));
        }
        if (CollectionUtils.isEmpty(commissionTypeList)) {
            log.info("没有对应的需要互斥的规则");
            return;
        }
        for (String cityName : data.getCityNameStr().split(GeneralConstant.splitChar)){
            List<CfKpiCommissionCityRuleDO> commissionLikeCityName = cfKpiCommissionCityRuleDao.getCommissionLikeCityName(data.getLevel(), data.getMonthKey(), cityName, data.getId(), commissionTypeList);
            // 处理 重复的数据
            commissionLikeCityName.stream().forEach(ruleDO -> {
                ArrayList<String> cityNameList = Lists.newArrayList(ruleDO.getCityNameStr().split(GeneralConstant.splitChar));
                cityNameList.remove(cityName);
                if (CollectionUtils.isEmpty(cityNameList)){
                    log.info(this.getClass().getName()+" needDeleteRuleIdList result:{}",JSON.toJSONString(ruleDO));
                    cfKpiCommissionCityRuleDao.updateIsDelete(Lists.newArrayList(ruleDO.getId()),BooleanUtils.toInteger(true));
                }else {
                    log.info(this.getClass().getName()+" needUpdateRuleDOList result:{}",JSON.toJSONString(ruleDO));
                    ruleDO.setCityNameStr(cityNameList.stream().collect(Collectors.joining(GeneralConstant.splitChar)));
                    cfKpiCommissionCityRuleDao.batchUpdateCityNameStr(Lists.newArrayList(ruleDO));
                }
            });
        }
    }
    @Override
    public void changeStatusOtherMonthKey(String monthKey) {
        cfKpiCommissionCityRuleDao.updateStatusByMonthKey(monthKey,CfBdKpiEnums.RuleStatusEnum.SHENGXIAO.getStatus());
        List<Long> ids = cfKpiCommissionCityRuleDao.getStatusOf1(monthKey);
        log.info(this.getClass().getName()+" getStatusOf1 result:{}",JSON.toJSONString(ids));
        if (CollectionUtils.isNotEmpty(ids)){
            cfKpiCommissionCityRuleDao.updateStatus(ids, CfBdKpiEnums.RuleStatusEnum.WEI_SHENGXIAO.getStatus());
        }
    }

    @Override
    public CfKpiCommissionCityRuleDO getCommission(Long id) {
        return cfKpiCommissionCityRuleDao.getCommissionById(id);
    }

    @Override
    public CommonResultModel<CfKPICommissionVO> getCommissionList(KpiSearchParam searchParam) {
        CommonResultModel<CfKPICommissionVO> commonResultModel = new CommonResultModel();
        int offset = (searchParam.getPageNo()-1)*searchParam.getPageSize();
        commonResultModel.setModelList(cfKpiCommissionCityRuleDao.getCommissionList(searchParam.getLevel(),
                searchParam.getCityType(),
                searchParam.getCityName(),searchParam.getStatus(),
                searchParam.getMonthKey(),offset,searchParam.getPageSize()));
        commonResultModel.setTotal(cfKpiCommissionCityRuleDao.getCommissionListCount(searchParam.getLevel(),
                searchParam.getCityType(),
                searchParam.getCityName(),searchParam.getStatus(),
                searchParam.getMonthKey()));
        return commonResultModel;
    }

    @Override
    public List<Integer> getCityTypeByMonthKeyWithCommissionType(String monthKey, List<Integer> commissionTypeList){
        return cfKpiCommissionCityRuleDao.getCityTypeByMonthKeyWithCommissionType(monthKey, commissionTypeList);
    }
}
