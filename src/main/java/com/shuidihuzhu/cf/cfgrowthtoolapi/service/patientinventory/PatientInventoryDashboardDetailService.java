package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.param.DailyEntryDetailParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.param.DailyInventoryDetailParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.param.DailyVisitDetailParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WashBaseParam;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.Date;
import java.util.List;

public interface PatientInventoryDashboardDetailService {
    void saveDashboardData(PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo);

    List<PatientInventoryDashboardDetail> listAllNoResultData(long id, Integer limit, String limitTime);
    
    Long getMaxIdOfNoResultData(String limitTime);

    Integer insert(PatientInventoryDashboardDetail overdueDetail);

    Integer overDueDataByIds(List<Long> ids, Long resultId);

    DepartmentDashboardOverviewVO getDepartmentDashboardOverview(Long orgId,CrowdfundingVolunteer cfVolunteer);

    BindDepartmentVO getBindDepartment(String uniqueCode,List<Long> orgIds);

    DailyEntryDetailVO getDailyEntryDetail(DailyEntryDetailParam param);

    DailyVisitDetailVO getDailyVisitDetail(DailyVisitDetailParam param);

    DailyInventoryDetailVO getDailyInventoryDetail(DailyInventoryDetailParam param);

    void washPatientInventoryDashboardDetail(WashBaseParam washBaseParam);
}
