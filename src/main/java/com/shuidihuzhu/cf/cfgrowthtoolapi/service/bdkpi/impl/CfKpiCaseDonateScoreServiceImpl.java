package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseDonateScoreDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiCaseScoreVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCaseDonateScoreService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiCaseDonateScoreDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-08-04
 */

@Service
@Slf4j
public class CfKpiCaseDonateScoreServiceImpl implements ICfKpiCaseDonateScoreService {

    @Autowired
    private CfKpiCaseDonateScoreDao cfKpiCaseDonateScoreDao;

    @Override
    public long queryCaseDonateScoreCount(String monthKey, String uniqueCode, long validAmount, int validDonateNum) {
        return cfKpiCaseDonateScoreDao.queryCaseDonateScoreCount(monthKey, uniqueCode, validAmount, validDonateNum);
    }

    @Override
    public List<CfKpiCaseScoreVO> queryCaseDonateScoreDetail(String monthKey, String uniqueCode, long validAmount, int validDonateNum, int pageNo, int pageSize) {
        int offSet = (pageNo - 1) * pageSize;
        return cfKpiCaseDonateScoreDao.queryCaseDonateScoreDetail(monthKey, uniqueCode, validAmount, validDonateNum, offSet, pageSize);
    }

    @Override
    public OpResult<List<CfKpiCaseScoreVO>> queryCaseScoreDetail(String monthKey, String uniqueCode) {
        if (StringUtils.isEmpty(monthKey)){
            monthKey = DateUtil.getYYYYMMStr(DateUtil.getCurrentDate());
        }
        return OpResult.createSucResult(cfKpiCaseDonateScoreDao.queryCaseScoreDetail(monthKey,uniqueCode));
    }

    @Override
    public int batchSaveOrUpdate(List<CfKpiCaseDonateScoreDo> kpiCaseDonateScoreList, String monthKey, String uniqueCode) {
        List<CfKpiCaseDonateScoreDo> caseScoreListFromDb = cfKpiCaseDonateScoreDao.queryCaseScoreList(monthKey,uniqueCode);
        Map<Long,CfKpiCaseDonateScoreDo> caseScoreMap = caseScoreListFromDb.stream().collect(Collectors.toMap(CfKpiCaseDonateScoreDo::getCaseId, Function.identity()));
        Map<Long,CfKpiCaseDonateScoreDo> inputCaseSocreMap = kpiCaseDonateScoreList.stream().collect(Collectors.toMap(CfKpiCaseDonateScoreDo::getCaseId, Function.identity()));
        List<CfKpiCaseDonateScoreDo> insertList = Lists.newArrayList();
        List<CfKpiCaseDonateScoreDo> updateList = Lists.newArrayList();
        List<CfKpiCaseDonateScoreDo> delList = Lists.newArrayList();
        for (CfKpiCaseDonateScoreDo caseScoreDO : kpiCaseDonateScoreList){
            CfKpiCaseDonateScoreDo singleDataFromDb = caseScoreMap.get(caseScoreDO.getCaseId());
            if (Objects.isNull(singleDataFromDb)){
                insertList.add(caseScoreDO);
            }else{
                caseScoreDO.setId(singleDataFromDb.getId());
                updateList.add(caseScoreDO);
            }
        }

        for (CfKpiCaseDonateScoreDo cfKpiCaseDonateScoreDo: caseScoreListFromDb){
            if (Objects.isNull(inputCaseSocreMap.get(cfKpiCaseDonateScoreDo.getCaseId()))){
                delList.add(cfKpiCaseDonateScoreDo);
            }
        }

        if (CollectionUtils.isNotEmpty(insertList)){
            List<List<CfKpiCaseDonateScoreDo>> listList = Lists.partition(insertList, GeneralConstant.MAX_PAGE_SIZE);
            listList.forEach(list -> cfKpiCaseDonateScoreDao.batchInsert(list));
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            List<List<CfKpiCaseDonateScoreDo>> listList = Lists.partition(updateList, GeneralConstant.MAX_PAGE_SIZE);
            listList.forEach(list -> cfKpiCaseDonateScoreDao.batchUpdate(list));
        }
        if (CollectionUtils.isNotEmpty(delList)){
            List<List<CfKpiCaseDonateScoreDo>> listList = Lists.partition(delList, GeneralConstant.MAX_PAGE_SIZE);
            listList.forEach(list -> cfKpiCaseDonateScoreDao.batchUpdateDelete(list));
        }
        return 0;
    }

    @Override
    public int batchDeleteByUniqueCode(String uniqueCode, String monthKey) {
        if (StringUtils.isAnyBlank(uniqueCode, monthKey)) {
            return 0;
        }
        return cfKpiCaseDonateScoreDao.batchDeleteByUniqueCode(uniqueCode, monthKey);
    }
}
