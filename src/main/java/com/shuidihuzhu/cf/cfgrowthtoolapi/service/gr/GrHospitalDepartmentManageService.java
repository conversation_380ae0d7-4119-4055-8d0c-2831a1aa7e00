package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrHospitalDepartmentManageDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.gr.GrHospitalDepartmentManageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.GrHospitalDepartmentManageParam;

import java.util.List;

public interface GrHospitalDepartmentManageService {

    int insert(GrHospitalDepartmentManageDO hospitalDepartmentManage);

    int update(GrHospitalDepartmentManageDO hospitalDepartmentManage);


    List<GrHospitalDepartmentManageDO> getDepartmentManageListByParam(GrHospitalDepartmentManageParam param);

    List<GrHospitalDepartmentManageDO> getDepartmentManageListByParamExcel(GrHospitalDepartmentManageParam param);

    int getDepartmentManageCount(GrHospitalDepartmentManageParam param);

    GrHospitalDepartmentManageDO getDepartmentManageById(Long id);

    void updateManageModel(GrHospitalDepartmentManageModel model);

    List<GrHospitalDepartmentManageDO> listByCustomerId(Integer customerId);

    List<GrHospitalDepartmentManageDO> listByDepartmentIds(List<Integer> departmentIds);

    /**
     * 根据科室名称模糊查询科室信息
     *
     * @param departmentName
     * @param grCustomerId
     * @return GrHospitalDepartmentManageDO
     */
    List<GrHospitalDepartmentManageDO> getDepartmentManageListByDepartmentName(String departmentName, Integer grCustomerId);
}
