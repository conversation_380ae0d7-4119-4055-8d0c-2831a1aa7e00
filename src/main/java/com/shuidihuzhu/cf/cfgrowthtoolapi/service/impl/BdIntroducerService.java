package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdIntroducerDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdIntroducerSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdIntroducerVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfIntroducerInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IBdIntroducerService;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdIntroducerDao;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/10/17 2:32 PM
 */
@Service
public class BdIntroducerService implements IBdIntroducerService {
    @Autowired
    private CfBdIntroducerDao cfBdIntroducerDao;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public CfBdIntroducerDO getInfoByEncryMobile(String introducerEncryMobile) {
        return cfBdIntroducerDao.getCfIntroducerInfoModelByIntroducerEncryMobile(introducerEncryMobile);
    }
    @Override
    public CfBdIntroducerDO getCfBdIntroducerDOByEncryMobile(String introducerEncryMobile) {
        return cfBdIntroducerDao.getCfBdIntroducerDOByEncryMobile(introducerEncryMobile);
    }
    @Override
    public void insert(CfBdIntroducerDO cfBdIntroducerDO){
        cfBdIntroducerDao.insert(cfBdIntroducerDO);
    }

    @Override
    public void delCfBdIntroducerDOById(long id){
        cfBdIntroducerDao.delCfBdIntroducerDOById(id);
    }

    @Override
    public void updateIntroducerNameById(String introducerName, String operatorName, long id) {
        cfBdIntroducerDao.updateIntroducerNameById(introducerName, operatorName, id);
    }

    @Override
    public CommonResultModel<CfBdIntroducerVo> getCfBdIntroducerDoList(CfBdIntroducerSearchModel cfBdIntroducerSearchModel) {
        int offset = (cfBdIntroducerSearchModel.getPageNo()-1)*cfBdIntroducerSearchModel.getPageSize();
        List<CfBdIntroducerDO> cfBdIntroducerDoList = cfBdIntroducerDao.getCfBdIntroducerDoList(cfBdIntroducerSearchModel,offset,cfBdIntroducerSearchModel.getPageSize());
        long count = cfBdIntroducerDao.getCountCfBdIntroducerDoList(cfBdIntroducerSearchModel);
        CommonResultModel commonResultModel = new CommonResultModel();
        commonResultModel.setTotal(count);
        List<CfBdIntroducerVo> cfBdIntroducerVos = cfBdIntroducerDoList.stream().map(cfBdIntroducerDO -> cfBdIntroducerDO.buildCfBdIntroducerVo(shuidiCipher)).collect(Collectors.toList());
        commonResultModel.setModelList(cfBdIntroducerVos);
        return commonResultModel;
    }
}
