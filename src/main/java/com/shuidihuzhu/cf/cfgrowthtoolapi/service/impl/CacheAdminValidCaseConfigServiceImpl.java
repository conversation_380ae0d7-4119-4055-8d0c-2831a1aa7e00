package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OrgValidCaseConfig;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.CaseValidCommonSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CacheAdminValidCaseConfigService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.dao.AdminValidCaseConfigDao;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CacheAdminValidCaseConfigServiceImpl implements CacheAdminValidCaseConfigService {

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private AdminValidCaseConfigDao adminValidCaseConfigDao;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    private final static String REDIS_KEY = "valid_case_config_redis_key";

    @Override
    public void delRedis() {
        cfRedissonHandler.del(REDIS_KEY);
    }

    @Override
    public Map<Integer, AdminValidCaseConfigDO> getValidCaseConfigMap() {
        List<AdminValidCaseConfigDO> value = cfRedissonHandler.getList(REDIS_KEY, AdminValidCaseConfigDO.class);
        if (CollectionUtils.isNotEmpty(value)) {
            return buildMap(value);
        }
        List<AdminValidCaseConfigDO> adminValidCaseConfigDOList = getAllValidCaseConfigByDB();
        if (CollectionUtils.isEmpty(adminValidCaseConfigDOList)) {
            return Maps.newHashMap();
        }
        cfRedissonHandler.addListEX(REDIS_KEY, adminValidCaseConfigDOList, TimeUnit.HOURS.toMillis(1));
        return buildMap(adminValidCaseConfigDOList);
    }

    private Map<Integer, AdminValidCaseConfigDO> buildMap(List<AdminValidCaseConfigDO> adminValidCaseConfigDOList) {
        Map<Integer, List<AdminValidCaseConfigDO>> map = adminValidCaseConfigDOList.stream().collect(Collectors.groupingBy(AdminValidCaseConfigDO::getOrgId));
        Map<Integer, AdminValidCaseConfigDO> result = Maps.newHashMap();
        for (Map.Entry<Integer, List<AdminValidCaseConfigDO>> entry : map.entrySet()) {
            AdminValidCaseConfigDO adminValidCaseConfigDO = entry.getValue().stream().max(Comparator.comparing(AdminValidCaseConfigDO::getId)).get();
            result.put(adminValidCaseConfigDO.getOrgId(), adminValidCaseConfigDO);
        }
        return result;
    }

    @Override
    public AdminValidCaseConfigDO getAdminValidCaseConfigDO(long orgId) {
        Map<Integer, AdminValidCaseConfigDO> validCaseConfigMap = getValidCaseConfigMap();
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = crmSelfBuiltOrgReadService.listParentOrgAsChainOrder(orgId);
        for (BdCrmOrganizationDO bdCrmOrganizationDO : bdCrmOrganizationDOList) {
            AdminValidCaseConfigDO adminValidCaseConfigDO = validCaseConfigMap.get((int) bdCrmOrganizationDO.getId());
            if (Objects.nonNull(adminValidCaseConfigDO)) {
                return AdminValidCaseConfigDO.builder()
                        .validAmount(adminValidCaseConfigDO.getValidAmount())
                        .validDonateNum(adminValidCaseConfigDO.getValidDonateNum())
                        .build();
            }
        }
        return AdminValidCaseConfigDO.builder()
                .validAmount(apolloService.getValidAmount())
                .validDonateNum(apolloService.getValidDonateNum())
                .build();
    }

    @Override
    public List<OrgValidCaseConfig> partitionOrgIds(List<Long> orgIdList) {
        List<OrgValidCaseConfig> result = Lists.newArrayList();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(orgIdList)) {
            return result;
        }
        List<Long> distinctOrgIds = orgIdList.stream().distinct().collect(Collectors.toList());
        Map<String, OrgValidCaseConfig> map = Maps.newHashMap();
        for (Long orgId : distinctOrgIds) {
            AdminValidCaseConfigDO adminValidCaseConfigDO = getAdminValidCaseConfigDO(orgId);
            String key = adminValidCaseConfigDO.getValidAmount() + "_" + adminValidCaseConfigDO.getValidDonateNum();
            OrgValidCaseConfig orgValidCaseConfig = map.get(key);
            if (orgValidCaseConfig == null) {
                orgValidCaseConfig = new OrgValidCaseConfig();
                orgValidCaseConfig.setOrgIds(Lists.newArrayList(orgId));
                orgValidCaseConfig.setSearchParam(CaseValidCommonSearchParam.buildCaseValidSearch(adminValidCaseConfigDO.getValidAmount(), adminValidCaseConfigDO.getValidDonateNum()));
                map.put(key, orgValidCaseConfig);
            } else {
                List<Long> orgIds = Optional.ofNullable(orgValidCaseConfig.getOrgIds()).orElse(Lists.newArrayList());
                orgIds.add(orgId);
                orgValidCaseConfig.setOrgIds(orgIds);
            }
        }
        if (MapUtils.isEmpty(map)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(map.values());
    }

    private List<AdminValidCaseConfigDO> getAllValidCaseConfigByDB() {
        List<AdminValidCaseConfigDO> all = Lists.newArrayList();
        long beginId = 0L;
        int limit = 200;
        boolean hasMore = true;
        while (hasMore) {
            List<AdminValidCaseConfigDO> adminValidCaseConfigDOList = adminValidCaseConfigDao.listValidAllCursor(beginId, limit);
            all.addAll(adminValidCaseConfigDOList);
            if (CollectionUtils.isNotEmpty(adminValidCaseConfigDOList)) {
                beginId = adminValidCaseConfigDOList.stream().min(Ordering.natural().reverse().onResultOf(AdminValidCaseConfigDO::getId)).get().getId();
            }
            hasMore = adminValidCaseConfigDOList.size() == limit;
        }
        return all;
    }

}
