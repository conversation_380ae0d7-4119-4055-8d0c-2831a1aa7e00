package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.shorturl.ShortUrlDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.snowflake.SnowflakeClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.wxgroup.WxGroupDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCaseDayDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCaseTagDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bigdata.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WxGroupTemplateEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.QywxGroupDelayMsgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.BdCaseTagService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfCaseDayDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IClewPreproseMaterialService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.bigdata.*;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.baseservice.pay.enums.BankCardVerifyEnum;
import com.shuidihuzhu.client.cf.admin.model.AiGenerateBaseInfo;
import com.shuidihuzhu.client.cf.admin.model.AiGenerateForwardParam;
import com.shuidihuzhu.client.cf.api.chaifenbeta.ugc.CfUgcServiceFeignClient;
import com.shuidihuzhu.client.cf.growthtool.enums.QyWxMsgTypeEnum;
import com.shuidihuzhu.client.cf.growthtool.model.BdQywxGroupMessageModel;
import com.shuidihuzhu.common.web.model.IdcardInfoExtractor;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.kratos.client.api.group.dto.WwExternalGroupChatUserInfoResp;
import com.shuidihuzhu.kratos.wxwork.model.WxWorkGroupMessageRecordModel;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 企业微信消息服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-11 11:33:18
 */
@Service
@Slf4j
public class QyWxGroupMessageServiceImpl implements QyWxGroupMessageService {

    // ===================== 依赖注入 =====================
    @Autowired
    private BdQyWxRobotService bdQyWxRobotService;

    @Autowired
    private BdQyWxCaseGroupMappingService bdQyWxCaseGroupMappingService;

    @Autowired
    private BdQyWxMsgRecordService bdQyWxMsgRecordService;

    @Autowired
    private CfUgcServiceFeignClient cfUgcServiceFeignClient;

    @Autowired
    private IAccountServiceDelegate accountServiceDelegate;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Autowired
    private CfCaseDayDataService cfCaseDayDataService;

    @Autowired
    private WxGroupDelegate wxGroupDelegate;

    @Autowired
    private CaseSampleTypeDao cfCaseSampleTypeDao;

    @Autowired
    private CaseFeaturesDao caseFeaturesDao;

    @Autowired
    private SnowflakeClientDelegate snowflakeClientDelegate;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private CaseFriendShareDao cfCaseFriendShareDao;

    @Autowired
    private ShortUrlDelegate shortUrlDelegate;

    @Autowired
    private QywxGroupAiShareContentService qywxGroupAiShareContentService;

    @Autowired
    private AIDelegate aiDelegate;

    @Autowired
    private IPreposeMaterialDelegate preposeMaterialDelegate;

    @Autowired
    private IClewPreproseMaterialService clewPreproseMaterialService;

    private static final int REQUIRED_VERIFY_COUNT = 9;

    private static final int REQUIRED_VERIFY_COUNT_ONLINE = 11;

    /**
     * 大额捐款消息每日发送限制数量
     */
    private static final int LARGE_DONATION_DAILY_LIMIT = 6;

    @Autowired
    private CfFirstApproveFeignClient cfFirstApproveFeignClient;

    @Autowired
    private BdCaseTagService bdCaseTagService;


    public static final List<String> SHARE_CONTENT_LIST = Lists.newArrayList(
            "打扰了，您的转发帮我带来了很多好心人的关注，真的感谢！麻烦您再次点开我的朋友圈第一个链接转发一下，您的每一次转发、每一句关怀、每一次帮助，都让我感受到了无尽的温暖和力量。希望您可以再次帮忙转发，帮我呼吁更多好心人关注和帮扶，万分感谢🙏🙏🙏",
            "感谢您持续的关心和帮助，您的转发帮我带来了很多好心人士的关注！收到那么多祝福，我真的很感动，我一定努力与病魔抗争底！！请您再帮忙转发一下，我万分感谢，谢谢您🙏🙏🙏",
            "千言万语,也表达不了我的感激之情,我只能说,我已铭记在心。感谢你们，你们让我再一次理解了感恩、爱的真谛!还请再多多帮忙【转发扩散】🙏🙏🙏感恩",
            "您上次帮我转发后，就有更多好心人来帮我了，真的感激不尽！！有您的支持，我就多一份希望，可以的话请再帮我转发一下，让更多人看到～ 真心感谢！"
    );

    public static final String FIX_START_EVENING = "@筹款人【筹款小结】晚上好！您今日一共得到{今日捐单次数}次爱心帮助。\n\n";
    public static final String FIX_START_EVENING_2 = "@筹款人【筹款小结】晚上好！截止到目前，您共计获得{本案例捐单次数}次爱心帮助。\n\n";

    public static final String SPECIAL_CARD_HEAD = "请打开\uD83D\uDC49【筹款攻略，筹款更高效】";

    private static final String CONFIRM_PIC_URL = "https://image.shuidichou.com/img/ck/20250423/e3a6a1f0-6984-4275-a8e4-805ceb600e2a.jpeg";

    public static final String APPLY_CASH_OUT_URL = "https://www.shuidichou.com/raise/supplement/list?infoUuid=";

    public static final String DEFAULT_SHARE_CONTENT = "以前看到朋友圈里有人发水滴筹，我都会捐款，也会帮忙转发，因为我知道每一个发起水滴筹的家庭一定都有着旁人无法想象的困难。没想到，我现在也经历着同样的事情。面对如此高昂的治疗费用，万般无奈，只能求助于社会上的好心人帮帮我们，给您添麻烦了，隔着屏幕给各位深深的鞠躬了\uD83D\uDE4F\uD83C\uDFFB";

    public static final String ONLINE_WELCOME = "@筹款人您好，您的审核已经通过，多按我教您的方法配上标题在朋友圈转发。除了转发，也记得要找亲朋好友做下证实，对您筹款也会有很大帮助的。\n" +
            "为了避免私聊回复不及时，您后续有任何问题都可以随时在群内直接咨询或@我们，群内会有人跟您及时解答的。";

    // 如果是线上的需要过滤，不发送 爱心首页和筹款集合页
    private static final List<Integer> ONLINE_FILTER_MSG_TYPES = Lists.newArrayList(QyWxMsgTypeEnum.LOVE_PAGE.getCode(), QyWxMsgTypeEnum.FUNDRAISING_PAGE.getCode()); 

    /**
     * WEN_XIN(1, "文心一言"),
     * DOU_BAO(2, "豆包"),
     * ZHI_PU(3, "智谱清言"),
     * MOON_SHOT(4, "moonshot-v1-128k"),
     * TONG_YI(5, "通义千问"),
     * DEEPSEEK_R1(6, "deepseekr1"),
     */
    public static final List<Integer> AI_MODEL_LIST = Lists.newArrayList(
            AiModelEnum.TONG_YI.getModelId());
            //AiModelEnum.DEEPSEEK_R1.getModelId());

    @Getter
    enum AiModelEnum {
        WEN_XIN(1, "文心一言"),
        DOU_BAO(2, "豆包"),
        ZHI_PU(3, "智谱清言"),
        MOON_SHOT(4, "moonshot-v1-128k"),
        TONG_YI(5, "通义千问"),
        DEEPSEEK_R1(6, "deepseekr1");

        private int modelId;
        private String modelName;

        AiModelEnum(int modelId, String modelName) {
            this.modelId = modelId;
            this.modelName = modelName;
        }
    }

    public AiModelEnum getAiModelEnum(int modelId) {
        for (AiModelEnum aiModelEnum : AiModelEnum.values()) {
            if (aiModelEnum.modelId == modelId) {
                return aiModelEnum;
            }
        }
        return null;
    }

    /**
     * 相似案例结果模型类
     */
    @Data
    static class SimilarCaseResult {
        private String title; // 案例标题
        private int amount; // 筹款金额(元)
        private int shareCount; // 转发次数

        public SimilarCaseResult(String title, int amount, int shareCount) {
            this.title = title;
            this.amount = amount;
            this.shareCount = shareCount;
        }
    }

    /**
     * 模板内容结果模型类
     */
    @Data
    static class TemplateContentResult {
        private String content; // 消息内容
        private WxGroupTemplateEnum template; // 使用的模板

        public TemplateContentResult(String content, WxGroupTemplateEnum template) {
            this.content = content;
            this.template = template;
        }
    }

    /**
     * 消息处理器接口，用于不同类型消息的处理
     */
    @FunctionalInterface
    private interface CaseMessageHandler {
        /**
         * 处理案例消息
         *
         * @param group    群聊绑定信息
         * @param caseInfo 案例信息
         */
        void handle(BdQyWxCaseGroupMappingDO group, CrowdfundingInfo caseInfo);
    }

    // ===================== 接口实现方法 =====================

    @Override
    public void remindAddMember(BdQyWxCaseGroupMappingDO groupMapping) {
        boolean isCaseEnd = crowdFundingFeignDelegate.checkCaseEnd(groupMapping.getCaseId());
        if (isCaseEnd) {
            log.info("案例已结束,caseId:{}", groupMapping.getCaseId());
            return;
        }

        // 获取患者姓名和发起人姓名
        String patientName = "患者";
        String initiatorName = "筹款人";

        CfFirsApproveMaterial approveMaterial = crowdFundingFeignDelegate
                .getAuthorInfoByInfoId(groupMapping.getCaseId());
        if (approveMaterial != null) {
            patientName = approveMaterial.getPatientRealName();
            // 获取发起人姓名，如果为空则使用患者姓名
            initiatorName = StringUtils.isNotEmpty(approveMaterial.getSelfRealName())
                    ? approveMaterial.getSelfRealName()
                    : patientName;
        }
        WxGroupTemplateEnum templateEnum = groupMapping.getCaseSourceType() == 0
                ? WxGroupTemplateEnum.NO_FUNDRAISER_IN_GROUP
                : WxGroupTemplateEnum.NO_FUNDRAISER_IN_GROUP_ONLINE;
        // 使用枚举模板替代硬编码文案，替换占位符
        String reminderMsg = templateEnum.getTemplateInfo()
                .replace("{患者姓名}", patientName)
                .replace("{筹款人姓名}", initiatorName);

        sendAndRecordMessage(groupMapping, QyWxMsgTypeEnum.ADD_CASE_REMIND.getCode(), reminderMsg,
                templateEnum.getCode(), "");
    }

    @Override
    public void welcomeMessage(BdQyWxCaseGroupMappingDO groupMapping) {
        boolean isCaseEnd = crowdFundingFeignDelegate.checkCaseEnd(groupMapping.getCaseId());
        if (isCaseEnd) {
            log.info("案例已结束,caseId: {}", groupMapping.getCaseId());
            return;
        }
        // 欢迎语只发送一次
        List<BdQyWxMsgRecordDO> msgRecords = bdQyWxMsgRecordService.queryByCaseIdAndMsgType(groupMapping.getCaseId(),
                WxGroupTemplateEnum.WELCOME_TO_FUNDRAISER.getCode());
        if (CollectionUtils.isNotEmpty(msgRecords)) {
            log.info("欢迎语已发送, caseId: {}", groupMapping.getCaseId());
            return;
        }

        String robotName = "筹款小助手";
        BdQyWxRobotDO bdQyWxRobotDO = bdQyWxRobotService.queryByExternalUserId(groupMapping.getRobotExternalUserId());
        if (bdQyWxRobotDO != null && StringUtils.isNotBlank(bdQyWxRobotDO.getRobotName())) {
            robotName = bdQyWxRobotDO.getRobotName();
        }

        String welcomeMsg = "";

        int caseSourceType = groupMapping.getCaseSourceType();
        if (caseSourceType == 0) {
            // 使用枚举模板替代硬编码文案，并替换机器人名称
            WxGroupTemplateEnum templateEnum = WxGroupTemplateEnum.WELCOME_TO_FUNDRAISER;
            welcomeMsg = templateEnum.getTemplateInfo()
                    .replace("XX", robotName); // 替换机器人名称
        } else {
            welcomeMsg = ONLINE_WELCOME;
        }

        sendAndRecordMessage(groupMapping, QyWxMsgTypeEnum.WELCOME_MESSAGE.getCode(), welcomeMsg,
                WxGroupTemplateEnum.WELCOME_TO_FUNDRAISER.getCode(), "");
    }


    @Override
    public void donateSuccess(CrowdfundingInfo crowdfundingInfo, CrowdfundingOrder crowdfundingOrder, long donateUserId) {
        long donateAmount = crowdfundingOrder.getAmount() / 100;
        int caseId = crowdfundingInfo.getId();
        // 检查时间频次限制：晚上22:00-早上6:00不发送动态消息
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        if (hour >= 22 || hour < 6) {
            log.info("当前时间{}在限制发送时段(22:00-06:00)内，不发送消息", hour);
            return;
        }

        if (donateAmount <= 0) {
            log.info("捐款金额小于0元，不发送消息, caseId: {}, donateAmount: {}", caseId, donateAmount);
            return;
        }

        // 获取案例信息和证实人数
        BdQyWxCaseGroupMappingDO caseGroup = bdQyWxCaseGroupMappingService.queryByCaseId(caseId);
        if (caseGroup == null || !caseGroup.canSendMsg()) {
            log.warn("未找到案例对应的群聊信息, caseId: {}", caseId);
            return;
        }
        log.info("处理捐款成功通知, caseId: {}, donateAmount: {}", caseId, donateAmount);

        String content;
        int msgType;

        // 1. 处理第一笔捐款
        if (isFirstDonation(caseId)) {
            String applyCashOutUrl = APPLY_CASH_OUT_URL + crowdfundingInfo.getInfoId() + "&channel=qiwei";
            applyCashOutUrl = shortUrlDelegate.process(applyCashOutUrl);
            int verifyCount = getVerifyCount(caseGroup.getInfoId());

            msgType = QyWxMsgTypeEnum.FIRST_DONATION.getCode();
            int needVerifyCount = caseGroup.getCaseSourceType() == 0 ? REQUIRED_VERIFY_COUNT : REQUIRED_VERIFY_COUNT_ONLINE;
            if (verifyCount > needVerifyCount) {
                String content1 = "@筹款人 有人给您捐款了，提现材料可点击填写：" + applyCashOutUrl + " \n"
                        + "\uD83D\uDC9D 每一份证实都是对您案例的信任加分，越多人证实案例的筹款效果也会更好 \n "
                        + "\uD83D\uDC49快将案例转发给亲友进行证实，您可以把下面的指引图片发送给他们，让他们更了解该如何帮您证实~";
                BdQyWxMsgRecordDO bdQyWxMsgRecordDO = sendAndRecordMessage(caseGroup, msgType, content1, "");
                String preMsgId = Optional.ofNullable(bdQyWxMsgRecordDO).map(BdQyWxMsgRecordDO::getMsgId).orElse("");
                sendAndRecordPicMessage(caseGroup, msgType, CONFIRM_PIC_URL, preMsgId);
            }
            if (verifyCount <= needVerifyCount && verifyCount > 3) {
                String content1 = "@筹款人 有人给您捐款了，提现材料可点击填写：" + applyCashOutUrl + " \n"
                        + "\uD83D\uDC9D每一份证实都是对您案例的信任加分，我们建议您至少获得" + needVerifyCount + "位亲友的证实，以提升筹款效果。\n"
                        + "\uD83D\uDC49请将案例和下面的指引图片分享给亲友，让他们了解该如何帮您证实~";
                BdQyWxMsgRecordDO bdQyWxMsgRecordDO = sendAndRecordMessage(caseGroup, msgType, content1, "");
                String preMsgId = Optional.ofNullable(bdQyWxMsgRecordDO).map(BdQyWxMsgRecordDO::getMsgId).orElse("");
                sendAndRecordPicMessage(caseGroup, msgType, CONFIRM_PIC_URL, preMsgId);
            }
            if (verifyCount <= 3) {
                String content1 = "@筹款人 有人给您捐款了，快将案例转发给亲友，并发送下面的指引图片，邀请他们帮您证实。需要获得3位亲友的证实，才可以提现。\n"
                        + "👉提现材料可点击填写:" + applyCashOutUrl + " \n"
                        + "\uD83D\uDC9D如有疑问，请随时联系我们的筹款顾问，我们的筹款顾问将为您提供全程支持。";
                BdQyWxMsgRecordDO bdQyWxMsgRecordDO = sendAndRecordMessage(caseGroup, msgType, content1, "");
                String preMsgId = Optional.ofNullable(bdQyWxMsgRecordDO).map(BdQyWxMsgRecordDO::getMsgId).orElse("");
                sendAndRecordPicMessage(caseGroup, msgType, CONFIRM_PIC_URL, preMsgId);
            }

            return;
        }

        // 2. 处理大额捐款 (>=500元)
        if (donateAmount >= 500) {
            boolean anonymous = crowdfundingOrder.isAnonymous();
            if (anonymous) {
                log.info("大额捐款匿名，caseId: {}, donateAmount: {}", caseId, donateAmount);
                return;
            }
            // 检查是否达到发送限制
            if (hasReachedLargeDonationLimit(caseId, LARGE_DONATION_DAILY_LIMIT)) {
                log.info("案例{}今日大额捐款消息已达上限{}条，不再发送", caseId, LARGE_DONATION_DAILY_LIMIT);
                return;
            }

            String donorName = getDonateName(donateUserId);
            if (StringUtils.isBlank(donorName)) {
                return;
            }
            log.info("检测到大额捐款, caseId: {}, amount: {}", caseId, donateAmount);

            content = String.format("@筹款人，\"%s\"给您捐助了%d 元！\n"
                            + "👉快去感谢他，并邀请他再在微信群或者朋友圈转发一下吧，每一份传播都是希望的延续！",
                    donorName, donateAmount);
            BdQyWxMsgRecordDO bdQyWxMsgRecordDO = sendAndRecordMessage(caseGroup, QyWxMsgTypeEnum.LARGE_DONATION.getCode(), content, "");
            if (bdQyWxMsgRecordDO != null) {
                sendDonateThankContent(caseGroup, QyWxMsgTypeEnum.LARGE_DONATION.getCode(), bdQyWxMsgRecordDO.getMsgId(), crowdfundingInfo);
            }
            return;
        }

    }


    @Override
    public void sendCommonMessage(BdQywxGroupMessageModel messageModel) {
        // 查看是否拉群了
        BdQyWxCaseGroupMappingDO groupMapping = bdQyWxCaseGroupMappingService.queryByCaseId(messageModel.getCaseId());
        if (groupMapping == null) {
            log.info("未找到案例对应的群聊信息, caseId: {}", messageModel.getCaseId());
            return;
        }
        // 是否能发送
        if (!groupMapping.canSendMsg()) {
            log.info("还不能发送消息, caseId: {}", messageModel.getCaseId());
            return;
        }
        if (groupMapping.getCaseSourceType() == 1 && ONLINE_FILTER_MSG_TYPES.contains(messageModel.getMsgType())) {
            log.info("线上的案例，不发送消息, caseId: {}, msgType: {}", messageModel.getCaseId(), messageModel.getMsgType());
            return;
        }

        BdQyWxMsgRecordDO contentMsgRecord = null;
        if (StringUtils.isNotBlank(messageModel.getMsgContent())) {
            contentMsgRecord = sendAndRecordMessage(groupMapping, messageModel.getMsgType(), messageModel.getMsgContent(), "");
        }
        if (StringUtils.isNotBlank(messageModel.getPicUr())) {
            String preMsgId = contentMsgRecord == null ? "" : contentMsgRecord.getMsgId();
            sendAndRecordPicMessage(groupMapping, messageModel.getMsgType(), messageModel.getPicUr(), preMsgId);
        }
    }


    /**
     * 记录消息发送结果（避免重复查询群信息）
     *
     * @param caseGroup 案例群聊绑定信息
     * @return 是否成功
     */
    @Override
    public void sendCreateGroupSucMsg(BdQyWxCaseGroupMappingDO caseGroup, String content) {
        // 调用带templateCode参数的方法，但使用默认值0
        sendAndRecordMessage(caseGroup, QyWxMsgTypeEnum.CREATE_GROUP_SUCCESS.getCode(), content, "");
    }


    /**
     * 发送卡片消息（链接类型）
     *
     * @param caseGroup 案例群聊绑定信息
     * @param msgType   消息类型
     * @return 是否成功
     */
    @Override
    public BdQyWxMsgRecordDO sendAndRecordCardMessage(BdQyWxCaseGroupMappingDO caseGroup, int msgType, CrowdfundingInfo crowdfundingInfo, String channel, String preMsgId) {
        return sendAndSpecialCardMessage(caseGroup, msgType, crowdfundingInfo, channel, "", preMsgId);
    }


    /**
     * 创建AI分享内容
     * @param caseInfo 案例信息
     * @param generateType 生成类型 3 转发语 4 感谢语
     * @param gratitudeReason 感谢理由
     * @return 创建的AI分享内容记录
     */
    @Override
    public QywxGroupAiShareContentDO createAiShareContent(BdQyWxCaseGroupMappingDO caseGroup, CrowdfundingInfo caseInfo, int generateType, String gratitudeReason) {
        int retryCount = 3;
        AiGenerateForwardParam aiGenerateForwardParam = getAiGenerateForwardParam(caseGroup, caseInfo, generateType, gratitudeReason);

        QywxGroupAiShareContentDO shareContentDO = null;
        for (int i = 0; i < retryCount; i++) {
            shareContentDO = saveShareContent(caseGroup, aiGenerateForwardParam);
            if (StringUtils.isBlank(shareContentDO.getFailReason())) {
                return shareContentDO;
            }
        }
        shareContentDO.setShareContent(DEFAULT_SHARE_CONTENT);
        return shareContentDO;
    }


    private BdQyWxMsgRecordDO sendAndSpecialCardMessage(BdQyWxCaseGroupMappingDO caseGroup, int msgType, CrowdfundingInfo crowdfundingInfo, String channel, String specialContent, String preMsgId) {
        try {
            // 创建Map存储卡片信息
            Map<String, String> cardMap = new HashMap<>();
            cardMap.put("cardUrl", "https://www.shuidichou.com/cf/contribute/" + crowdfundingInfo.getInfoId() + "?channel=" + channel);
            if (StringUtils.isBlank(specialContent)) {
                specialContent = "【水滴筹】";
            }
            cardMap.put("cardTitle", specialContent + crowdfundingInfo.getTitle());
            //求助人故事前 60 字， 移除换行
            String content = crowdfundingInfo.getContent();
            String cardDigest = content.length() > 60 ? content.substring(0, 60).replace("\n", "") : content.replace("\n", "");
            cardMap.put("cardDigest", cardDigest);
            //取第一张图
            cardMap.put("cardDigestImgUrl", crowdfundingInfo.getTitleImg() + "!cf_mtr_200");

            // 将Map转换为JSON
            String cardContent = JSON.toJSONString(cardMap);

            // 获取群成员
            List<WwExternalGroupChatUserInfoResp> members = wxGroupDelegate.getWxGroupMembers(caseGroup.getChatId());
            if (CollectionUtils.isEmpty(members)) {
                log.error("获取成员信息失败,caseId:{}", caseGroup.getCaseId());
                return null;
            }

            // 发送链接类型消息，contentType=13
            return sendMessageCore(caseGroup, msgType, cardContent, 0, 13, members, preMsgId);
        } catch (Exception e) {
            log.error("发送卡片消息异常, caseId:{}", caseGroup.getCaseId(), e);
            return null;
        }
    }
    // ===================== 定时消息发送入口 =====================

    @Override
    public void sendMorningPromotionMessage() {
        // D1-D5
        Date todayStart = DateTime.now().toDate();
        // 获取昨天的日期
        Date yesterday = DateUtil.addDay(todayStart, -1);
        // 获取近6天内拉群案例（今天+前5天）
        Date sixDaysAgo = DateUtil.addDay(todayStart, -5);

        // 使用通用方法处理早间促转消息
        processCaseMessages(sixDaysAgo, yesterday, "日常促转:", this::sendMorningPromotionMessage);
    }

    @Override
    public void sendEveningFundraisingSummary() {
        Date today = DateTime.now().toDate();
        // 获取近6天内拉群案例（今天+前5天）
        Date sixDaysAgo = DateUtil.addDay(today, -5);

        // 使用通用方法处理晚间汇总消息
        processCaseMessages(sixDaysAgo, today, "晚间汇总:", this::sendEveningFundraisingSummary);
    }

    @Override
    public void sendFundraisingWeeklyReport() {
        Date todayStart = DateTime.now().withTimeAtStartOfDay().toDate();

        // 定义多个时间段
        List<Date[]> timeRanges = new ArrayList<>();

        // 第7天创建的案例
        timeRanges.add(new Date[]{
                DateUtil.addDay(todayStart, -7),
                DateUtil.addDay(todayStart, -6)
        });

        // 第14天创建的案例
        timeRanges.add(new Date[]{
                DateUtil.addDay(todayStart, -14),
                DateUtil.addDay(todayStart, -13)
        });

        // 第21天创建的案例
        timeRanges.add(new Date[]{
                DateUtil.addDay(todayStart, -21),
                DateUtil.addDay(todayStart, -20)
        });

        // 使用通用方法处理周报消息
        processCaseMessagesMultiRanges(timeRanges, "筹款周报:", this::sendWeeklyReport);
    }

    // ===================== 消息处理通用框架 =====================

    /**
     * 通用案例消息处理方法，根据时间范围和处理器处理案例消息
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param logPrefix      日志前缀
     * @param messageHandler 消息处理器
     */
    private void processCaseMessages(Date startTime, Date endTime, String logPrefix,
                                     CaseMessageHandler messageHandler) {
        // 根据时间范围获取案例群聊信息
        List<BdQyWxCaseGroupMappingDO> caseGroups = bdQyWxCaseGroupMappingService.queryByCreateTimeRange(startTime,
                endTime);
        if (CollectionUtils.isEmpty(caseGroups)) {
            return;
        }

        // 调用通用处理方法
        processCaseGroupsWithHandler(caseGroups, logPrefix, messageHandler);
    }

    /**
     * 批量处理多个时间段的案例消息
     *
     * @param timeRanges     时间范围列表，每个元素为一个长度为2的数组，[0]为开始时间，[1]为结束时间
     * @param logPrefix      日志前缀
     * @param messageHandler 消息处理器
     */
    private void processCaseMessagesMultiRanges(List<Date[]> timeRanges, String logPrefix,
                                                CaseMessageHandler messageHandler) {
        // 收集所有时间范围内的群组
        List<BdQyWxCaseGroupMappingDO> allGroups = new ArrayList<>();

        for (Date[] range : timeRanges) {
            Date startTime = range[0];
            Date endTime = range[1];
            List<BdQyWxCaseGroupMappingDO> groups = bdQyWxCaseGroupMappingService.queryByCreateTimeRange(startTime,
                    endTime);
            if (!CollectionUtils.isEmpty(groups)) {
                allGroups.addAll(groups);
            }
        }

        if (CollectionUtils.isEmpty(allGroups)) {
            return;
        }

        // 调用通用处理方法
        processCaseGroupsWithHandler(allGroups, logPrefix, messageHandler);
    }

    /**
     * 处理案例群组的共用逻辑
     *
     * @param caseGroups     案例群组列表
     * @param logPrefix      日志前缀
     * @param messageHandler 消息处理器
     */
    private void processCaseGroupsWithHandler(List<BdQyWxCaseGroupMappingDO> caseGroups, String logPrefix,
                                              CaseMessageHandler messageHandler) {
        // 批量查询所有案例信息
        List<Integer> caseIds = caseGroups.stream()
                .map(BdQyWxCaseGroupMappingDO::getCaseId)
                .collect(Collectors.toList());
        List<CrowdfundingInfo> allCaseInfos = crowdFundingFeignDelegate.getCrowdfundingListById(caseIds);
        if (CollectionUtils.isEmpty(allCaseInfos)) {
            log.warn("{}批量获取案例信息失败", logPrefix);
            return;
        }

        // 将案例信息构建成map，方便通过caseId快速查找
        Map<Integer, CrowdfundingInfo> caseInfoMap = new HashMap<>();
        for (CrowdfundingInfo caseInfo : allCaseInfos) {
            caseInfoMap.put(caseInfo.getId(), caseInfo);
        }
        Date now = new Date();

        // 处理每个案例
        for (BdQyWxCaseGroupMappingDO group : caseGroups) {
            try {
                CrowdfundingInfo caseInfo = caseInfoMap.get(group.getCaseId());
                if (caseInfo == null) {
                    log.warn("{}未找到案例信息, caseId: {}", logPrefix, group.getCaseId());
                    continue;
                }
                // 如果案例已经结束了，则不发送消息
                if (now.after(caseInfo.getEndTime())) {
                    log.info("{}案例已结束, caseId: {}", logPrefix, group.getCaseId());
                    continue;
                }
                // 调用处理器处理消息
                messageHandler.handle(group, caseInfo);
            } catch (Exception e) {
                log.error("{}案例消息处理异常, caseId: {}", logPrefix, group.getCaseId(), e);
            }
        }
    }

    // ===================== 具体消息类型处理 =====================

    /**
     * 发送日常促转消息（避免重复查询群信息）
     *
     * @param caseGroup 案例群聊绑定信息
     * @param caseInfo  案例信息
     */
    private void sendMorningPromotionMessage(BdQyWxCaseGroupMappingDO caseGroup, CrowdfundingInfo caseInfo) {
        // 判断金额是否为0 (假设 CrowdfundingInfo 中的金额字段是 amount)
        if (caseInfo.getAmount() <= 0) {
            sendZeroDonationPromotionMessage(caseGroup, caseInfo);
            return;
        }

        // 获取0步&1步好友贡献占比
        int firstDegreePercent = calculateFriendContributionPercent(caseInfo.getId());

        // 构建早间促转消息及获取使用的模板
        TemplateContentResult result = buildMorningPromotionContent(caseInfo, firstDegreePercent);
        if (result == null) {
            return;
        }
        String content = result.getContent();
        WxGroupTemplateEnum selectedTemplate = result.getTemplate();

        // 使用重载方法发送消息，避免重复查询群信息，并传递模板编号
        BdQyWxMsgRecordDO bdQyWxMsgRecordDOFirst = null;
        if (selectedTemplate != null) {
            bdQyWxMsgRecordDOFirst = sendAndRecordMessage(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(), content, selectedTemplate.getCode(), "");
        } else {
            bdQyWxMsgRecordDOFirst = sendAndRecordMessage(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(), content, 0, "");
        }
        //生成转发语
        BdQyWxMsgRecordDO bdQyWxMsgRecordDOSecond = null;
        if (bdQyWxMsgRecordDOFirst != null) {
            bdQyWxMsgRecordDOSecond = sendShareContent(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(), bdQyWxMsgRecordDOFirst.getMsgId(), caseInfo);
        }
        if (bdQyWxMsgRecordDOSecond != null) {
            String channel = Optional.ofNullable(selectedTemplate).map(WxGroupTemplateEnum::getChannel).orElse("morning_default");
            String specialContent = WxGroupTemplateEnum.SIMILAR_CASE.contains(selectedTemplate) ? SPECIAL_CARD_HEAD : "";
            sendAndSpecialCardMessage(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(), caseInfo, channel, specialContent, bdQyWxMsgRecordDOSecond.getMsgId());
        }

    }

    /**
     * 构建日常促转消息内容
     *
     * @param caseInfo           案例信息
     * @param firstDegreePercent 0步&1步好友贡献占比
     * @return 包含消息内容和使用的模板的结果对象
     */
    private TemplateContentResult buildMorningPromotionContent(CrowdfundingInfo caseInfo, int firstDegreePercent) {
        if (caseInfo == null) {
            return new TemplateContentResult("", null);
        }

        // 获取已筹金额（单位：元）
        int amountYuan = caseInfo.getAmount() / 100;

        // 获取相似案例数据
        SimilarCaseResult similarCase = getSimilarCaseData(caseInfo);

        boolean hasSimilarCase = similarCase != null;

        // 选择适合的模板
        List<WxGroupTemplateEnum> templateEnums = getMorningTemplateEnumsByContribution(firstDegreePercent,
                hasSimilarCase);

        // 使用公共方法选择模板
        WxGroupTemplateEnum selectedTemplate = selectTemplateByCase(
                caseInfo.getId(),
                QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(),
                templateEnums);

        if (selectedTemplate == null) {
            return null;
        }

        // 替换模板中的变量
        String content = selectedTemplate.getTemplateInfo()
                .replace("{本案例已筹金额}", String.valueOf(amountYuan));

        // 如果是相似案例模板，替换相关变量
        if (hasSimilarCase) {
            content = content
                    .replace("{相似案例标题}", similarCase.getTitle())
                    .replace("{相似案例转发次数}", String.valueOf(similarCase.getShareCount()))
                    .replace("{相似案例已筹金额}", String.valueOf(similarCase.getAmount()));
        }

        return new TemplateContentResult(content, selectedTemplate);
    }

    /**
     * 根据好友贡献度选择适合的早间消息模板
     *
     * @param firstDegreePercent 0步&1步好友贡献占比
     * @return 匹配的模板枚举列表
     */
    private List<WxGroupTemplateEnum> getMorningTemplateEnumsByContribution(int firstDegreePercent,
                                                                            boolean hasSimilarCase) {
        List<WxGroupTemplateEnum> templateEnums = new ArrayList<>();

        if (firstDegreePercent >= 50) {
            // 高贡献度模板（案例需要更多扩散）
            templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_GOLDEN_TIME_1_HIGH_CONTRIBUTION);
            templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_GOLDEN_TIME_2_HIGH_CONTRIBUTION);
            templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_GOLDEN_TIME_3_HIGH_CONTRIBUTION);
            templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_ENCOURAGEMENT_4);
            templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_ENCOURAGEMENT_5);
            if (hasSimilarCase) {
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_SIMILAR_CASE_6);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_SIMILAR_CASE_7);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_SIMILAR_CASE_8);
            }
        } else {
            // 低贡献度模板（案例已经有一定扩散）
            templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_GOLDEN_TIME_1_LOW_CONTRIBUTION);
            templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_GOLDEN_TIME_2_LOW_CONTRIBUTION);
            templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_GOLDEN_TIME_3_LOW_CONTRIBUTION);
            templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_ENCOURAGEMENT_12);
            templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_ENCOURAGEMENT_13);
            if (hasSimilarCase) {
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_SIMILAR_CASE_14);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_SIMILAR_CASE_15);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_TIPS_SIMILAR_CASE_16);
            }
        }

        return templateEnums;
    }

    /**
     * 发送晚间筹款汇总消息（避免重复查询群信息）
     *
     * @param caseGroup 案例群聊绑定信息
     * @param caseInfo  案例信息
     */
    private void sendEveningFundraisingSummary(BdQyWxCaseGroupMappingDO caseGroup, CrowdfundingInfo caseInfo) {
        boolean hasShared = Optional
                .of(crowdFundingFeignDelegate.countTodayShareFriendFeed(caseGroup.getCaseId(), caseGroup.getUserId()))
                .filter(OpResult::isSuccess)
                .map(OpResult::getData)
                .orElse(0) > 0;
        // 如果已经已筹金额为0，则不发送消息
        if (caseInfo.getAmount() <= 0) {
            return;
        }

        // 获取0步&1步好友贡献占比
        int firstDegreePercent = calculateFriendContributionPercent(caseInfo.getId());

        // 构建晚间汇总消息
        TemplateContentResult result = buildEveningFundraisingSummaryContent(caseInfo, hasShared,
                firstDegreePercent);
        String content = result.getContent();
        WxGroupTemplateEnum selectedTemplate = result.getTemplate();

        // 使用重载方法发送消息，避免重复查询群信息，并传递模板编号
        BdQyWxMsgRecordDO bdQyWxMsgRecordDOFirst = null;
        if (selectedTemplate != null) {
            bdQyWxMsgRecordDOFirst = sendAndRecordMessage(caseGroup, QyWxMsgTypeEnum.EVENING_SUMMARY.getCode(), content,
                    selectedTemplate.getCode(), "");
        } else {
            bdQyWxMsgRecordDOFirst = sendAndRecordMessage(caseGroup, QyWxMsgTypeEnum.EVENING_SUMMARY.getCode(), content, 0, "");
        }

        BdQyWxMsgRecordDO bdQyWxMsgRecordDOSecond = null;
        // 发送转发语
        if (!hasShared && bdQyWxMsgRecordDOFirst != null) {
            bdQyWxMsgRecordDOSecond = sendShareContent(caseGroup, QyWxMsgTypeEnum.EVENING_SUMMARY.getCode(), bdQyWxMsgRecordDOFirst.getMsgId(), caseInfo);
        }
        if (hasShared && bdQyWxMsgRecordDOFirst != null) {
            bdQyWxMsgRecordDOSecond = sendEveningThankContent(caseGroup, QyWxMsgTypeEnum.EVENING_SUMMARY.getCode(), bdQyWxMsgRecordDOFirst.getMsgId(), caseInfo);
        }
        if (bdQyWxMsgRecordDOSecond != null) {
            // 发送成功
            String channel = Optional.ofNullable(selectedTemplate).map(WxGroupTemplateEnum::getChannel).orElse("evening_default");
            sendAndRecordCardMessage(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(), caseInfo, channel, bdQyWxMsgRecordDOSecond.getMsgId());
        }
    }

    /**
     * 构建晚间筹款汇总消息内容
     *
     * @param caseInfo           案例信息
     * @param hasShared          是否已分享
     * @param firstDegreePercent 0步&1步好友贡献占比
     * @return 包含消息内容和使用的模板的结果对象
     */
    private TemplateContentResult buildEveningFundraisingSummaryContent(CrowdfundingInfo caseInfo, boolean hasShared,
                                                                        int firstDegreePercent) {
        // 获取当日捐单数据（一次查询同时获取次数和金额）
        CfCaseDayDataDO donateStats = getTodayDonateStats(caseInfo.getId());
        int todayDonateCount = donateStats != null ? donateStats.getDonateCount() : 0;
        int todayDonateAmount = donateStats != null ? donateStats.getDonateAmount() / 100 : 0; // 转换为元

        // 根据是否分享和好友贡献度选择不同的模板集
        List<WxGroupTemplateEnum> templateEnums = getEveningTemplateEnumsByCondition(hasShared, firstDegreePercent);

        // 使用公共方法选择模板
        WxGroupTemplateEnum selectedTemplate = selectTemplateByCase(
                caseInfo.getId(),
                QyWxMsgTypeEnum.EVENING_SUMMARY.getCode(),
                templateEnums);

        // 替换模板中的变量
        String content = selectedTemplate.getTemplateInfo();

        // 特殊处理：如果是筹款关键人模版，需要替换关键人信息
        if (WxGroupTemplateEnum.FRIEND_EVENING_PROMOTION.contains(selectedTemplate)) {
            // 获取关键筹款人微信昵称列表
            List<String> keyPersons = getKeyPersons(caseInfo.getId());
            if (keyPersons.size() >= 3) {
                content = content
                        .replace("{XX（微信昵称1）}", keyPersons.get(0))
                        .replace("{XX（微信昵称2）}", keyPersons.get(1))
                        .replace("{XX（微信昵称3）}", keyPersons.get(2));
            } else {
                // 如果关键人不足3人，使用兜底文案
                if (firstDegreePercent >= 50) {
                    selectedTemplate = WxGroupTemplateEnum.FUNDRAISING_SUMMARY_NO_KEY_PERSON_HIGH_CONTRIBUTION;
                    content = selectedTemplate.getTemplateInfo();
                } else {
                    selectedTemplate = WxGroupTemplateEnum.FUNDRAISING_SUMMARY_NO_KEY_PERSON_LOW_CONTRIBUTION;
                    content = selectedTemplate.getTemplateInfo();
                }
            }
        }
        String appendContent = FIX_START_EVENING_2 + content;
        appendContent = appendContent
                .replace("{本案例捐单次数}", String.valueOf(caseInfo.getDonationCount()));
        return new TemplateContentResult(appendContent, selectedTemplate);
    }

    /**
     * 发送周报
     */
    private void sendWeeklyReport(BdQyWxCaseGroupMappingDO group, CrowdfundingInfo caseInfo) {
        if (caseInfo == null || caseInfo.getAmount() <= 0) {
            return;
        }

        int caseId = caseInfo.getId();

        String today = DateUtil.formatDate(new Date());
        String sevenDaysAgo = DateUtil.formatDate(DateUtil.addDay(new Date(), -7));
        List<String> dayKeys = GrowthtoolUtil.getDateTimeByStartTimeWithEndTime(sevenDaysAgo, today);

        // 获取捐款数据
        List<CfCaseDayDataDO> dayDataList = cfCaseDayDataService.getByCaseIdAndDayKeys(caseId, dayKeys);
        if (CollectionUtils.isEmpty(dayDataList)) {
            return;
        }

        // 获取总捐单和捐单次数
        int totalAmount = dayDataList.stream().mapToInt(CfCaseDayDataDO::getDonateAmount).sum();
        int totalDonateCount = dayDataList.stream().mapToInt(CfCaseDayDataDO::getDonateCount).sum();

        // 判断是否有捐款
        if (totalAmount <= 0) {
            return;
        }

        WxGroupTemplateEnum templateEnum = WxGroupTemplateEnum.FUNDRAISING_WEEKLY_REPORT;
        if (Objects.equals(totalDonateCount, caseInfo.getDonationCount())) {
            templateEnum = WxGroupTemplateEnum.FUNDRAISING_WEEKLY_REPORT_V2;
        }

        // 构建周报内容
        String weeklyReport = templateEnum.getTemplateInfo()
                .replace("{本案例捐单次数}", String.valueOf(caseInfo.getDonationCount()))
                .replace("{本案例筹款金额}", String.valueOf(caseInfo.getAmount() / 100))
                .replace("{7日捐单次数}", String.valueOf(totalDonateCount))
                .replace("{7日筹款金额}", String.valueOf(totalAmount / 100));

        // 使用重载方法发送消息，避免重复查询群信息
        BdQyWxMsgRecordDO bdQyWxMsgRecordDOFirst = sendAndRecordMessage(group, QyWxMsgTypeEnum.WEEKLY_REPORT.getCode(), weeklyReport, templateEnum.getCode(), "");

        //发送转发语
        BdQyWxMsgRecordDO bdQyWxMsgRecordDOSecond = null;
        if (bdQyWxMsgRecordDOFirst != null) {
            bdQyWxMsgRecordDOSecond = sendShareContent(group, QyWxMsgTypeEnum.WEEKLY_REPORT.getCode(), bdQyWxMsgRecordDOFirst.getMsgId(), caseInfo);
        }
        // 固定发送
        if (bdQyWxMsgRecordDOSecond != null) {
            sendAndRecordCardMessage(group, QyWxMsgTypeEnum.WEEKLY_REPORT.getCode(), caseInfo, "weekpaper35", bdQyWxMsgRecordDOSecond.getMsgId());
        }
    }

    // ===================== 消息发送通用方法 =====================

    /**
     * 记录消息发送结果（避免重复查询群信息）
     *
     * @param caseGroup 案例群聊绑定信息
     * @param msgType   消息类型
     * @param content   消息内容
     * @return 是否成功
     */
    private BdQyWxMsgRecordDO sendAndRecordMessage(BdQyWxCaseGroupMappingDO caseGroup, int msgType, String content, String preMsgId) {
        // 调用带templateCode参数的方法，但使用默认值0
        return sendAndRecordMessage(caseGroup, msgType, content, 0, preMsgId);
    }



    /**
     * 记录消息发送结果（避免重复查询群信息）
     *
     * @param caseGroup    案例群聊绑定信息
     * @param msgType      消息类型
     * @param content      消息内容
     * @param templateCode 模板编号，对应WxGroupTemplateEnum中的code
     * @return 是否成功
     */
    private BdQyWxMsgRecordDO sendAndRecordMessage(BdQyWxCaseGroupMappingDO caseGroup, int msgType, String content,
                                                   int templateCode, String preMsgId) {
        try {
            // 替换下 content 信息，将 @筹款人修改 #{客户的hookId},{客户微信昵称}# 示例:#7881303427130810,妞奶#
            // @顾问 修改为 #{顾问的hookId},{顾问微信昵称}#
            List<WwExternalGroupChatUserInfoResp> members = wxGroupDelegate.getWxGroupMembers(caseGroup.getChatId());
            if (CollectionUtils.isEmpty(members)) {
                log.error("获取成员信息失败,caseId:{},content:{}", caseGroup.getCaseId(), content);
            }

            // 获取筹款人信息
            String uniqueExternalUserId = caseGroup.getVolunteerExternalUserId();
            long userId = caseGroup.getUserId();
            long hookUserId = 0;
            String nickName = "";
            Optional<WwExternalGroupChatUserInfoResp> memberOpt = members.stream()
                    .filter(member -> Objects.equals(userId, member.getShuidiUserId()))
                    .findFirst();
            if (memberOpt.isPresent()) {
                hookUserId = memberOpt.get().getHookUserId();
                nickName = memberOpt.get().getGroupNickname();
            }

            long hookUniqueUserId = 0;
            String uniqueNickName = "";
            Optional<WwExternalGroupChatUserInfoResp> memberUniqueOpt = members.stream()
                    .filter(member -> uniqueExternalUserId.equals(member.getExternalUserId()))
                    .findFirst();
            if (memberUniqueOpt.isPresent()) {
                hookUniqueUserId = memberUniqueOpt.get().getHookUserId();
                uniqueNickName = memberUniqueOpt.get().getGroupNickname();
            }

            // 处理@标记，替换为特定格式
            String processedContent = content;
            if (hookUserId > 0) {
                processedContent = processedContent.replace("@筹款人", "#" + hookUserId + "," + nickName + "#");
            }
            if (hookUniqueUserId > 0) {
                processedContent = processedContent.replace("@顾问", "#" + hookUniqueUserId + "," + uniqueNickName + "#");
            }
            // @筹款人兜底，修改为at所有人
            processedContent = processedContent.replace("@筹款人", "#0,所有人" + "#");

            // 发送文本类型消息，contentType=2
            return sendMessageCore(caseGroup, msgType, processedContent, templateCode, 2, members, preMsgId);
        } catch (Exception e) {
            log.error("记录消息发送结果异常, caseId: {}, msgType: {}", caseGroup.getCaseId(), msgType, e);
            return null;
        }
    }

    private void sendAndRecordPicMessage(BdQyWxCaseGroupMappingDO caseGroup, int msgType, String picUrl, String preMsgId) {
        List<WwExternalGroupChatUserInfoResp> members = wxGroupDelegate.getWxGroupMembers(caseGroup.getChatId());
        sendMessageCore(caseGroup, msgType, picUrl, 0, 101, members, preMsgId);
    }


    /**
     * 消息发送核心方法，处理共同逻辑
     *
     * @param caseGroup    案例群聊绑定信息
     * @param msgType      消息类型
     * @param content      消息内容
     * @param templateCode 模板编号
     * @param contentType  内容类型（2=文本，13=链接，101=图片）
     * @param members      群成员列表
     * @return 是否成功 msgId
     */
    private BdQyWxMsgRecordDO sendMessageCore(BdQyWxCaseGroupMappingDO caseGroup, int msgType, String content,
                                              int templateCode, int contentType, List<WwExternalGroupChatUserInfoResp> members, String preMsgId) {
        // 生成消息ID
        long msgId = snowflakeClientDelegate.getSnowflakeId();
        if (msgId <= 0) {
            log.info("获取雪花ID失败,caseId:{},content:{}", caseGroup.getCaseId(), content);
            return null;
        }

        // 检查群聊ID
        if (caseGroup.getHookChatId() <= 0) {
            log.info("获取群聊ID失败,caseId:{},content:{}", caseGroup.getCaseId(), content);
            return null;
        }

        // 找到机器人对应的hookId
        long robotHookId = caseGroup.getHookChatId();
        Optional<WwExternalGroupChatUserInfoResp> memberRobotOpt = members.stream()
                .filter(member -> Objects.equals(caseGroup.getRobotExternalUserId(), member.getExternalUserId()))
                .findFirst();
        if (memberRobotOpt.isPresent()) {
            robotHookId = memberRobotOpt.get().getHookUserId();
        }

        // 如果机器人的 hookUserId 为0，先不要发送了
        if (robotHookId == 0) {
            log.info("机器人hookId为0，不发送消息");
            return null;
        }

        // 创建消息记录
        BdQyWxMsgRecordDO msgRecord = new BdQyWxMsgRecordDO();
        msgRecord.setCaseId(caseGroup.getCaseId());
        msgRecord.setMsgId(String.valueOf(msgId));
        msgRecord.setMsgType(msgType);
        msgRecord.setMsgContent(content);
        msgRecord.setTemplateCode(templateCode);
        msgRecord.setChatId(caseGroup.getChatId());
        msgRecord.setCaseSourceType(caseGroup.getCaseSourceType());
        bdQyWxMsgRecordService.insert(msgRecord);

        // 创建并发送消息
        WxWorkGroupMessageRecordModel wxWorkGroupMessageRecordModel = new WxWorkGroupMessageRecordModel();
        wxWorkGroupMessageRecordModel.setMsgId(msgId);
        wxWorkGroupMessageRecordModel.setContentType(contentType);
        wxWorkGroupMessageRecordModel.setContent(content);
        wxWorkGroupMessageRecordModel.setCorpId(caseGroup.getCorpId());
        wxWorkGroupMessageRecordModel.setChatId(caseGroup.getHookChatId());
        wxWorkGroupMessageRecordModel.setSenderId(robotHookId);

        // 发送消息
        if (StringUtils.isNotBlank(preMsgId)) {
            QywxGroupDelayMsgModel qywxGroupDelayMsgModel = new QywxGroupDelayMsgModel();
            qywxGroupDelayMsgModel.setWxWorkGroupMessageRecordModel(wxWorkGroupMessageRecordModel);
            qywxGroupDelayMsgModel.setPreMsgId(preMsgId);
            qywxGroupDelayMsgModel.setMsgId(msgRecord.getId());
            qywxGroupDelayMsgModel.setCaseId(caseGroup.getCaseId());
            mqProducerService.sendQywxDelayMsg(qywxGroupDelayMsgModel, TimeUnit.SECONDS.toMillis(10));
        } else {
            bdQyWxMsgRecordService.updateRealSendTime(msgRecord.getId(), new Date());
            mqProducerService.sendQywxMsg(wxWorkGroupMessageRecordModel);
        }
        return msgRecord;
    }




    // ===================== 工具方法 =====================

    /**
     * 获取案例证实人数
     */
    private int getVerifyCount(String infoUuid) {
        Response<Integer> response = cfUgcServiceFeignClient.countCrowdFundingVerificationByInfoUuid(infoUuid);
        return Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(0);
    }

    /**
     * 判断是否是首笔捐款
     */
    private boolean isFirstDonation(int caseId) {
        // 查询消息发送记录是否存在过首笔捐款消息
        List<BdQyWxMsgRecordDO> msgRecords = bdQyWxMsgRecordService.queryByCaseIdAndMsgType(caseId,
                QyWxMsgTypeEnum.FIRST_DONATION.getCode());
        return CollectionUtils.isEmpty(msgRecords);
    }

    /**
     * 获取捐款人姓名
     */
    private String getDonateName(long donateUserId) {
        OpResult<UserInfoModel> response = accountServiceDelegate.getUserInfoByUserId(donateUserId);
        if (response.isFail() || response.getData() == null) {
            log.info("获取捐款人姓名异常, donateUserId: {}", donateUserId);
            return "";
        }
        return response.getData().getNickname();
    }

    /**
     * 计算好友贡献占比（0步&1步好友）
     *
     * @param caseId 案例ID
     * @return 好友贡献占比（0-100）
     */
    private int calculateFriendContributionPercent(int caseId) {
        // 获取运营案例
        String dt = DateUtil.getCurrentDateStr();
        CfCaseFeaturesDo caseFeaturesDo = caseFeaturesDao.getByInfoId((long) caseId, dt);
        if (caseFeaturesDo != null) {
            return Optional.ofNullable(caseFeaturesDo.getFriendContributions()).orElse(-1);
        }
        // 这里是一个默认实现，后续接入数仓数据
        return -1;
    }

    /**
     * 获取相似案例数据
     *
     * @param caseInfo 当前案例信息
     * @return 相似案例结果模型，如果没有找到则返回null
     */
    private SimilarCaseResult getSimilarCaseData(CrowdfundingInfo caseInfo) {
        if (caseInfo == null) {
            return null;
        }

        int caseId = caseInfo.getId();

        // 获取案例样本类型数据
        List<CfCaseSampleTypeDo> sampleTypes = cfCaseSampleTypeDao
                .listByInfoId((long) caseId, LocalDate.now().minusDays(1).toString());

        if (CollectionUtils.isEmpty(sampleTypes)) {
            return null;
        }

        // 获取最佳相似案例
        CfCaseSampleTypeDo bestSample = findBestSimilarCase(sampleTypes);
        if (bestSample == null) {
            return null;
        }

        return createSimilarCaseResult(caseInfo, bestSample);
    }

    /**
     * 从样本列表中查找最佳相似案例
     *
     * @param sampleTypes 样本类型列表
     * @return 最佳相似案例样本，如果没有找到则返回null
     */
    private CfCaseSampleTypeDo findBestSimilarCase(List<CfCaseSampleTypeDo> sampleTypes) {
        if (CollectionUtils.isEmpty(sampleTypes)) {
            return null;
        }

        // 按照捐赠金额从大到小排序，获取金额最大的样本
        Optional<CfCaseSampleTypeDo> topSample = sampleTypes.stream()
                .filter(sample -> sample.getDonateAmt() != null)
                .sorted((a, b) -> b.getDonateAmt().compareTo(a.getDonateAmt()))
                .findFirst();

        return topSample.orElse(null);
    }

    /**
     * 根据当前案例和样本创建相似案例结果
     *
     * @param caseInfo 当前案例信息
     * @param sample   样本数据
     * @return 相似案例结果
     */
    private SimilarCaseResult createSimilarCaseResult(CrowdfundingInfo caseInfo, CfCaseSampleTypeDo sample) {
        if (sample == null) {
            return null;
        }

        int caseId = caseInfo.getId();
        String title;
        int amount;
        int shareCount;

        // 如果样本ID与当前案例ID相同，直接使用当前案例信息
        if (sample.getSampleTypeInfoId() == caseId) {
            return null;
        } else {
            // 只在必要时才调用远程服务获取案例信息
            CrowdfundingInfo similarCaseInfo = crowdFundingFeignDelegate.getCaseInfoById(sample.getSampleTypeInfoId());
            title = similarCaseInfo != null ? similarCaseInfo.getTitle() : "相似病例案例";

            // 优先使用样本类型中的数据
            amount = sample.getDonateAmt().intValue();
            shareCount = sample.getCkrShareCnt().intValue();
        }

        // 创建结果对象
        return new SimilarCaseResult(title, amount, shareCount);
    }

    /**
     * 获取当日捐单统计数据（同时获取捐单次数和金额，减少查询次数）
     *
     * @param caseId 案例ID
     * @return 当日捐单统计数据
     */
    private CfCaseDayDataDO getTodayDonateStats(int caseId) {
        String today = DateUtil.formatDate(new Date());
        List<Integer> caseIds = Collections.singletonList(caseId);
        List<CfCaseDayDataDO> dayDataList = cfCaseDayDataService.getByCaseIdAndDayKey(caseIds, today);

        if (CollectionUtils.isEmpty(dayDataList)) {
            return null;
        }
        return dayDataList.get(0);
    }

    /**
     * 获取关键筹款人微信昵称列表
     *
     * @param caseId 案例ID
     * @return 关键筹款人微信昵称列表
     */
    private List<String> getKeyPersons(int caseId) {
        // 根据shareDonateAmt排序，取前10名最大值
        List<CfCaseFriendShareDo> cfCaseFriendShareDos = cfCaseFriendShareDao.listByInfoId((long) caseId,
                DateUtil.getCurrentDateStr());
        if (CollectionUtils.isEmpty(cfCaseFriendShareDos)) {
            return new ArrayList<>();
        }
        // 根据shareDonateAmt排序，取前10名最大值
        cfCaseFriendShareDos = cfCaseFriendShareDos.stream()
                .sorted(Comparator.comparing(CfCaseFriendShareDo::getShareDonateAmt).reversed()).limit(10)
                .collect(Collectors.toList());

        List<Long> friendUserIdList = cfCaseFriendShareDos.stream().map(CfCaseFriendShareDo::getFriendUserId)
                .collect(Collectors.toList());
        // 获取用户信息
        return accountServiceDelegate.getUserInfoByUserIdBatch(friendUserIdList)
                .stream()
                .filter(userInfoModel -> StringUtils.isNotBlank(userInfoModel.getNickname()))
                .map(UserInfoModel::getNickname)
                .distinct()
                .limit(3)
                .collect(Collectors.toList());
    }

    /**
     * 根据条件选择适合的晚间消息模板
     *
     * @param hasShared          当日是否有分享到朋友圈
     * @param firstDegreePercent 0步&1步好友贡献占比
     * @return 匹配的模板枚举列表
     */
    private List<WxGroupTemplateEnum> getEveningTemplateEnumsByCondition(boolean hasShared, int firstDegreePercent) {
        List<WxGroupTemplateEnum> templateEnums = new ArrayList<>();

        if (!hasShared) {
            // 当日未分享到朋友圈，使用黄金时间模版和感谢语模版
            if (firstDegreePercent >= 50) {
                // 高贡献度模板
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_GOLDEN_TIME_1_HIGH_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_GOLDEN_TIME_2_HIGH_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_GOLDEN_TIME_3_HIGH_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_GOLDEN_TIME_4_HIGH_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_THANKS_5_HIGH_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_THANKS_6_HIGH_CONTRIBUTION);
            } else {
                // 低贡献度模板
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_GOLDEN_TIME_1_LOW_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_GOLDEN_TIME_2_LOW_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_GOLDEN_TIME_3_LOW_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_GOLDEN_TIME_4_LOW_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_THANKS_5_LOW_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_SUMMARY_THANKS_6_LOW_CONTRIBUTION);
            }
        } else {
            // 当日已分享到朋友圈，使用筹款关键人模版
            if (firstDegreePercent >= 50) {
                // 高贡献度模板
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_KEY_PERSON_7_HIGH_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_KEY_PERSON_8_HIGH_CONTRIBUTION);
            } else {
                // 低贡献度模板
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_KEY_PERSON_7_LOW_CONTRIBUTION);
                templateEnums.add(WxGroupTemplateEnum.FUNDRAISING_KEY_PERSON_8_LOW_CONTRIBUTION);
            }
        }

        return templateEnums;
    }

    /**
     * 发送零捐款促转消息
     *
     * @param caseGroup 案例群聊绑定信息
     * @param caseInfo  案例信息
     */
    private void sendZeroDonationPromotionMessage(BdQyWxCaseGroupMappingDO caseGroup, CrowdfundingInfo caseInfo) {
        // 判断金额是否为0 (假设 CrowdfundingInfo 中的金额字段是 amount)
        if (caseInfo.getAmount() > 0) {
            return;
        }

        // 检查案例发起时间是否超过24小时
        Date createTime = caseGroup.getCreateTime();
        long currentTime = System.currentTimeMillis();
        boolean isOver24Hours = currentTime - createTime.getTime() > 24 * 60 * 60 * 1000;
        if (!isOver24Hours) {
            // 如果案例发起时间未超过24小时，则不发送消息
            return;
        }

        // 判断是否发送了模板消息
        int msgType = QyWxMsgTypeEnum.MORNING_PROMOTION.getCode();
        List<BdQyWxMsgRecordDO> msgRecords = bdQyWxMsgRecordService.queryByCaseIdAndMsgType(
                caseGroup.getCaseId(), msgType);

        // 如果在24小时内且已经发送过消息，则不重复发送
        boolean hasSentMessage = msgRecords.stream()
                .anyMatch(record -> {
                    // 判断是否是零元案例促转消息
                    int templateCode = record.getTemplateCode();
                    return WxGroupTemplateEnum.ZERO_MORNING_PROMOTION.contains(templateCode);
                });
        if (hasSentMessage) {
            return;
        }


        // 发送第一次模板
        SimilarCaseResult similarCase = getSimilarCaseData(caseInfo);
        WxGroupTemplateEnum selectedTemplate;
        String specialHead = "";
        if (similarCase != null) {
            selectedTemplate = WxGroupTemplateEnum.ZERO_AMOUNT_CASE_PROMOTION_FIRST_SIMILAR;
            specialHead = SPECIAL_CARD_HEAD;
        } else {
            selectedTemplate = WxGroupTemplateEnum.ZERO_AMOUNT_CASE_PROMOTION_FIRST_NO_SIMILAR;
        }

        // 获取模板内容并替换变量
        String msgContent = selectedTemplate.getTemplateInfo();

        // 替换相似案例信息
        if (selectedTemplate == WxGroupTemplateEnum.ZERO_AMOUNT_CASE_PROMOTION_FIRST_SIMILAR) {
            msgContent = msgContent.replace("{相似案例筹款金额}",
                    String.valueOf(similarCase.getAmount()));
        }

        if (similarCase != null) {
            BdQyWxMsgRecordDO bdQyWxMsgRecordDO = sendAndSpecialCardMessage(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(),
                    caseInfo, "zerocase", specialHead, "");
            BdQyWxMsgRecordDO promoteMsg = null;
            if (bdQyWxMsgRecordDO != null) {
                // 发送0元促转消息
                promoteMsg = sendAndRecordMessage(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(),
                        msgContent, selectedTemplate.getCode(), bdQyWxMsgRecordDO.getMsgId());
            }
            //生成转发语
            if (promoteMsg != null) {
                sendShareContent(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(), promoteMsg.getMsgId(), caseInfo);
            }
        } else {
            BdQyWxMsgRecordDO promoteMsg = sendAndRecordMessage(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(),
                        msgContent, selectedTemplate.getCode(), "");
            //生成转发语
            BdQyWxMsgRecordDO bdQyWxMsgRecordDO = null;
            if (promoteMsg != null) {
                bdQyWxMsgRecordDO = sendShareContent(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(), promoteMsg.getMsgId(), caseInfo);
            }
            if (bdQyWxMsgRecordDO != null) {
                sendAndSpecialCardMessage(caseGroup, QyWxMsgTypeEnum.MORNING_PROMOTION.getCode(),
                        caseInfo, "zerocase", specialHead, bdQyWxMsgRecordDO.getMsgId());
            }
        }
    }

    /**
     * 根据案例ID和消息类型从模板列表中选择一个模板（随机选择未发送过的模板）
     *
     * @param caseId        案例ID
     * @param msgType       消息类型
     * @param templateEnums 可选模板列表
     * @return 选中的模板枚举
     */
    private WxGroupTemplateEnum selectTemplateByCase(int caseId, int msgType, List<WxGroupTemplateEnum> templateEnums) {
        if (CollectionUtils.isEmpty(templateEnums)) {
            log.warn("模板列表为空，无法选择模板");
            return null;
        }

        // 1. 查询这个案例在指定消息类型下已发送过的模板
        List<BdQyWxMsgRecordDO> msgRecords = bdQyWxMsgRecordService.queryByCaseIdAndMsgType(caseId, msgType);

        // 如果没有发送记录，直接随机返回一个模板
        if (CollectionUtils.isEmpty(msgRecords)) {
            return getRandomTemplate(templateEnums);
        }

        // 获取已发送过的模板code集合
        Set<Integer> sentTemplateCodes = msgRecords.stream()
                .filter(record -> record.getTemplateCode() > 0)
                .map(BdQyWxMsgRecordDO::getTemplateCode)
                .collect(Collectors.toSet());

        // 2. 筛选出未发送过的模板
        List<WxGroupTemplateEnum> availableTemplates = templateEnums.stream()
                .filter(template -> !sentTemplateCodes.contains(template.getCode()))
                .collect(Collectors.toList());

        // 3. 如果都发送过了，使用原来的列表
        if (CollectionUtils.isEmpty(availableTemplates)) {
            log.info("案例 {} 已在消息类型 {} 下发送过所有可选模板，使用原始模板列表", caseId, msgType);
            availableTemplates = templateEnums;
        }

        // 4. 随机返回一个模板
        return getRandomTemplate(availableTemplates);
    }

    /**
     * 从模板列表中随机选择一个模板
     *
     * @param templates 模板列表
     * @return 随机选择的模板
     */
    private WxGroupTemplateEnum getRandomTemplate(List<WxGroupTemplateEnum> templates) {
        if (CollectionUtils.isEmpty(templates)) {
            return null;
        }
        // 使用Random生成随机索引
        int randomIndex = new Random().nextInt(templates.size());
        return templates.get(randomIndex);
    }

    /**
     * 检查大额捐款消息发送限制
     *
     * @param caseId     案例ID
     * @param dailyLimit 每日限制次数
     * @return 是否已达到发送限制
     */
    private boolean hasReachedLargeDonationLimit(int caseId, int dailyLimit) {
        // 获取今天的开始时间和结束时间
        Date today = DateTime.now().withTimeAtStartOfDay().toDate();
        Date now = DateTime.now().toDate();

        // 查询该案例的所有大额捐款消息
        List<BdQyWxMsgRecordDO> allMsgs = bdQyWxMsgRecordService.queryByCaseIdAndMsgTypeAndTimeRange(
                caseId, QyWxMsgTypeEnum.LARGE_DONATION.getCode(), today, now);

        // 如果消息数量大于等于限制数量，则返回true
        return allMsgs.size() >= dailyLimit;
    }


    /**
     * 发送分享内容
     * 该方法封装了创建分享内容和发送消息的过程
     * 
     * @param caseGroup 案例群聊绑定信息
     * @param msgType 消息类型
     * @param preMsgId 前一个消息ID
     * @param caseInfo 案例信息
     * @return 消息记录对象
     */
    private BdQyWxMsgRecordDO sendShareContent(BdQyWxCaseGroupMappingDO caseGroup, int msgType, String preMsgId, CrowdfundingInfo caseInfo) {
        // 创建分享内容并获取记录
        QywxGroupAiShareContentDO aiShareContent = createAiShareContent(caseGroup, caseInfo, 3, "");
        return saveAndSendAiContent(aiShareContent, caseGroup, msgType, preMsgId);
    }


    private BdQyWxMsgRecordDO sendDonateThankContent(BdQyWxCaseGroupMappingDO caseGroup, int msgType, String preMsgId, CrowdfundingInfo caseInfo) {
        // 创建捐赠感謝内容并获取记录
        QywxGroupAiShareContentDO aiShareContent = createAiShareContent(caseGroup, caseInfo, 4, "捐款");
        // 发送消息
        return saveAndSendAiContent(aiShareContent, caseGroup, msgType, preMsgId);
    }

    private BdQyWxMsgRecordDO sendEveningThankContent(BdQyWxCaseGroupMappingDO caseGroup, int msgType, String preMsgId, CrowdfundingInfo caseInfo) {
        // 创建捐赠感謝内容并获取记录
        QywxGroupAiShareContentDO aiShareContent = createAiShareContent(caseGroup, caseInfo, 4, "转发");
        // 发送消息
        return saveAndSendAiContent(aiShareContent, caseGroup, msgType, preMsgId);
    }


    private BdQyWxMsgRecordDO saveAndSendAiContent(QywxGroupAiShareContentDO aiShareContent, BdQyWxCaseGroupMappingDO caseGroup, int msgType, String preMsgId) {
        // 发送消息
        BdQyWxMsgRecordDO bdQyWxMsgRecordDO = sendAndRecordMessage(caseGroup, msgType, aiShareContent.getShareContent(), 0, preMsgId);
        if (bdQyWxMsgRecordDO != null) {
            // 记录分享内容
            qywxGroupAiShareContentService.bindMsgId(aiShareContent.getId(), bdQyWxMsgRecordDO.getId());
        }
        return bdQyWxMsgRecordDO;
    }


    private  QywxGroupAiShareContentDO saveShareContent(BdQyWxCaseGroupMappingDO caseGroup, AiGenerateForwardParam aiGenerateForwardParam) {
        // 随机选择AI模型
        int randomIndex = new Random().nextInt(AI_MODEL_LIST.size());
        int modelType = AI_MODEL_LIST.get(randomIndex);
        aiGenerateForwardParam.setModelType(modelType);

        // 创建并保存分享内容记录
        QywxGroupAiShareContentDO shareContentDO = new QywxGroupAiShareContentDO();
        shareContentDO.setCaseId(caseGroup.getCaseId());
        shareContentDO.setParam(JSON.toJSONString(aiGenerateForwardParam));
        shareContentDO.setAiModel(Optional.ofNullable(getAiModelEnum(modelType)).map(AiModelEnum::getModelName).orElse(""));

        // 设置消息ID并创建分享内容
        aiGenerateForwardParam.setMsgId(String.valueOf(shareContentDO.getId()));
        if (caseGroup.getCaseSourceType() == 0) {
            aiGenerateForwardParam.setBizType(1);
        } else {
            aiGenerateForwardParam.setBizType(0);
        }
        String shareContent = aiDelegate.createShareContent(aiGenerateForwardParam);
        //过下风控
        //RiskWordResult riskWordResult = riskRpcDelegate.checkHitRiskWord(shareContent);
        String failReason = "";
        if (StringUtils.isBlank(shareContent)) {
            failReason = "ai返回结果为空";
        }
        if (StringUtils.isNotBlank(shareContent) && shareContent.length() > 500) {
            failReason = "分享内容长度超过500字";
            shareContent = shareContent.substring(0, 490) + "..";
        }
        shareContentDO.setShareContent(shareContent);
        shareContentDO.setFailReason(failReason);
        shareContentDO.setContentCreateTime(new Date());
        shareContentDO.setCaseSourceType(caseGroup.getCaseSourceType());
        qywxGroupAiShareContentService.insert(shareContentDO);
        return shareContentDO;
    }


    @Override
    public AiGenerateForwardParam getAiGenerateForwardParam(BdQyWxCaseGroupMappingDO caseGroup, CrowdfundingInfo caseInfo, int generateType, String gratitudeReason) {
        int caseId = caseGroup.getCaseId();
        // 创建AI参数对象
        AiGenerateForwardParam aiGenerateForwardParam = new AiGenerateForwardParam();
        aiGenerateForwardParam.setCaseId(caseId);

        // 创建并配置基础信息
        AiGenerateBaseInfo aiGenerateBaseInfo = new AiGenerateBaseInfo();
        aiGenerateBaseInfo.setTargetAmount(caseInfo.getTargetAmount() / 100);
        aiGenerateBaseInfo.setAmount(caseInfo.getAmount() / 100);
        // 获取疾病名称
        String diseaseNormalName = preposeMaterialDelegate.getDiseaseNormalName(caseId);
        aiGenerateBaseInfo.setMedicalDisease(diseaseNormalName);
        aiGenerateBaseInfo.setGratitudeReason(gratitudeReason);
        aiGenerateForwardParam.setGenerateType(generateType);
        if (generateType == 4) {
            // 设置AI生成参数
            aiGenerateForwardParam.setAiGenerateBaseInfo(aiGenerateBaseInfo);
            return aiGenerateForwardParam;
        }

        aiGenerateBaseInfo.setForwardForWho("亲朋好友们");

        PreposeMaterialModel.MaterialInfoVo materialInfoVo = null;
        //从代录入中获取
        //查找对应的报备信息
        ClewCrowdfundingReportRelation reportRelation = clewPreproseMaterialService.getLatelyReportedRelationByInfoId(caseId);
        if (reportRelation != null) {
            materialInfoVo = Optional.ofNullable(preposeMaterialDelegate.selectMaterialsById(reportRelation.getPreposeMaterialId()))
                    .map(RpcResult::getData)
                    .orElse(null);
        }
        if (caseGroup.getCaseSourceType() == 0) {
            // 处理学生标签
            BdCaseTagDO bdCaseTagDO = bdCaseTagService.queryByCaseId(caseId);
            if (bdCaseTagDO != null && bdCaseTagDO.getStudentCase() == 1) {
                aiGenerateBaseInfo.setPatientIdentity("学生");
            }
        } else {
            //从代录入中获取
            if (materialInfoVo != null) {
                String patientType = getPatientType(Optional.ofNullable(materialInfoVo.getPatientIdentity()).orElse(0));
                aiGenerateBaseInfo.setPatientIdentity(patientType);
            }
        }

        // 获取案例审批材料信息
        CfFirsApproveMaterial firsApproveMaterial = Optional.ofNullable(cfFirstApproveFeignClient.getCfFirstApproveMaterialByCaseId(caseId))
                .map(FeignResponse::getData)
                .orElse(null);

        if (firsApproveMaterial != null) {
            // 处理关系类型
            BaseInfoTemplateConst.CfBaseInfoRelationshipEnum cfBaseInfoRelationshipEnum =
                    BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.getByCode(firsApproveMaterial.getUserRelationTypeForC());
            UserRelTypeEnum raiseRealType = cfBaseInfoRelationshipEnum.getRaiseRealType();

            // 处理身份证信息
            String patientIdCard = firsApproveMaterial.getPatientCryptoIdcard();
            String raiserIdCard = "";

            // 设置筹款人信息
            if (raiseRealType == UserRelTypeEnum.SELF) {
                raiserIdCard = patientIdCard;
                aiGenerateBaseInfo.setRaiseName(firsApproveMaterial.getPatientRealName());
            } else {
                raiserIdCard = firsApproveMaterial.getSelfCryptoIdcard();
                aiGenerateBaseInfo.setRaiseName(firsApproveMaterial.getSelfRealName());
            }

            // 身份类型检查
            if (firsApproveMaterial.getPatientIdType() != BankCardVerifyEnum.UserIdentityType.IDENTITY.getCode()) {
                patientIdCard = "";
            }

            // 设置筹款人与患者关系
            aiGenerateBaseInfo.setRaisePatientRelation(cfBaseInfoRelationshipEnum.getWord());

            // 计算筹款人年龄
            if (StringUtils.isNotBlank(raiserIdCard)) {
                String decryptRaiserIdCard = ShuidiCipherUtils.decrypt(raiserIdCard);
                int age = GrowthtoolUtil.idNOToAge(decryptRaiserIdCard);
                aiGenerateBaseInfo.setRaiserAge(age);
            }

            // 计算患者年龄和性别
            if (StringUtils.isNotBlank(patientIdCard)) {
                String decryptPatientIdCard = ShuidiCipherUtils.decrypt(patientIdCard);
                int age = GrowthtoolUtil.idNOToAge(decryptPatientIdCard);
                aiGenerateBaseInfo.setPatientAge(age);
                IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor(decryptPatientIdCard);
                aiGenerateBaseInfo.setPatientGender(idcardInfoExtractor.getGender());
            }

            // 设置转发关系和患者姓名
            aiGenerateBaseInfo.setForwardRelation(raiseRealType.getMsg());
            aiGenerateBaseInfo.setPatientName(firsApproveMaterial.getPatientRealName());
        }

        //案例发起第几天,案例发起当天算筹款第一日
        Date caseCreateTime = caseInfo.getCreateTime();
        if (caseCreateTime != null) {
            Date currentDate = new Date();
            int daysBetween = DateUtil.getIntervalDays(DateUtil.getFirstMsOfThisDay(caseCreateTime), DateUtil.getFirstMsOfThisDay(currentDate));
            // 发起当天算第一天，所以需要+1
            int fundraisingDays = daysBetween + 1;
            aiGenerateBaseInfo.setDayOfFundraising(String.valueOf(fundraisingDays));
        }
        
        //发起医院
        if (materialInfoVo != null) {
            aiGenerateBaseInfo.setRaiseHospital(materialInfoVo.getHospital());
        }

        //案例文章
        aiGenerateBaseInfo.setContent(caseInfo.getContent());

        //案例标题
        aiGenerateBaseInfo.setTitle(caseInfo.getTitle());

        // 设置AI生成参数
        aiGenerateForwardParam.setAiGenerateBaseInfo(aiGenerateBaseInfo);
        return aiGenerateForwardParam;
    }


    private String getPatientType(int code) {
        if (code <= 0) {
            return null;
        }
        for (PreposeMaterialModel.PatientIdentity value : PreposeMaterialModel.PatientIdentity.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return null;
    }
}