package com.shuidihuzhu.cf.cfgrowthtoolapi.service.legal.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.legal.BdCrmLegalContractDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.legal.LegalContractParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.legal.BdCrmLegalContractDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.legal.BdCrmLegalContractService;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 法律援助合同(BdCrmLegalContract)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-19 16:48:18
 */
@Service("bdCrmLegalContractService")
public class BdCrmLegalContractServiceImpl implements BdCrmLegalContractService {

    @Resource
    private BdCrmLegalContractDao bdCrmLegalContractDao;

    @Override
    public BdCrmLegalContractDO queryById(long id) {
        return bdCrmLegalContractDao.queryById(id);
    }


    @Override
    public int insert(BdCrmLegalContractDO bdCrmLegalContract) {
        return bdCrmLegalContractDao.insert(bdCrmLegalContract);
    }

    @Override
    public int updatePayInfo(BdCrmLegalContractDO bdCrmLegalContract) {
        return bdCrmLegalContractDao.updatePayInfo(bdCrmLegalContract);
    }

    @Override
    public boolean cancelById(long id) {
        String cancelTime = DateTime.now().toString(GrowthtoolUtil.ymdhmsfmt);
        return bdCrmLegalContractDao.cancelById(id, cancelTime) > 0;
    }

    @Override
    public List<BdCrmLegalContractDO> getByNameAndPhone(String contractName, String phone) {
        if (StringUtils.isBlank(contractName) || StringUtils.isBlank(phone)) {
            return Lists.newArrayList();
        }
        return bdCrmLegalContractDao.getByNameAndPhone(contractName, phone);
    }

    @Override
    public int count(LegalContractParam legalContractParam) {
        return Optional.ofNullable(bdCrmLegalContractDao.count(legalContractParam)).orElse(0);
    }

    @Override
    public List<BdCrmLegalContractDO> page(LegalContractParam legalContractParam) {
        return bdCrmLegalContractDao.page(legalContractParam);
    }
}
