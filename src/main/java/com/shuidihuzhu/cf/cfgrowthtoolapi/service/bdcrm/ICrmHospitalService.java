package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrmHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrmHospitalDepartmentDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CrmHospitalDepartmentModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CrmHospitalModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CrmHDDepartmentStatisticModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CrmHospitalDepartmentSimpleModel;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ICrmHospitalService {

    List<CrmHospitalDO> getHospitalByFlow(String hospitalName,Integer provinceId,Integer cityId);

    List<CrmHospitalDO> searchHospital(String hospitalName, String city, String province, int offset, int pageSize);

    CrmHospitalDO getHospitalById(int hospitalId);

    int saveHospital(CrmHospitalDO crmHospitalDO);

    int updateHospital(CrmHospitalDO crmHospitalDO);


    boolean checkDepartmentIsDuplicate(int hospitalId, String name);

    Map<Integer, List<CrmHospitalDepartmentModel>> getHospitalDeparmentByHospitalIds(List<Integer> hospitalIds);

    List<CrmHospitalDO> getHospitalByCodes(List<String> hospitalCodeList);

    CrmHospitalDO getHospitalByName(String name);

    CrmHospitalDepartmentDO getHospitalDeparmentByName(String name,int hospitalId);

    int deleteHospital(Integer hospitalId);

    List<CrmHospitalDO> searchHospitalByDefault(Integer offset, Integer pageSize);

    List<CrmHospitalModel> getHospitalByProvinceIdWithCityId(int provinceId, int cityId);

    List<CrmHospitalModel> getHospitalByProvinceId(List<Integer> provinceIdList);

    List<CrmHospitalDepartmentSimpleModel> getCrmHospitalDepartmentSimpleModelByHospitalIds(List<Integer> hospitalIds);

    CrmHospitalDepartmentModel getHospitalDeparmentByDepartmentId(int departmentId);

    List<CrmHDDepartmentStatisticModel> getHDCountStatistic(List<Integer> hospitalIds);

    int updateHospitalVHospitalCode(Long id, String vhospitalCode);

    Map<String, Integer> getVhospitalCountVo(Set<String> vhospitalCodes,Integer rStatus);

    List<CrmHospitalDO> useDatabaseHospitalSearch(String provinceName, String cityName,
                                                  String hospitalName, String vhospitalCode,
                                                  Integer useStatus, int offset, Integer pageSize);

    int useDatabaseHospitalSearchCount(String provinceName, String cityName, String hospitalName,
                                       String vhospitalCode, Integer useStatus);

    CrmHospitalDO getHospitalByNameAndProvinceCity(String province, String city, String name);

    int updateStatusAndRstatusById(Long id, Integer useStatus, Integer rStatus);

    void updateHospitalVhospitalRalation(Long valueOf, String misName, String ymdhmsFromTimestamp);

    //根据医院名称精确查询
    CrmHospitalDO preciseMatchHospital(String hospitalName, int provinceId, int cityId);

    List<CrmHospitalDO> selectByCityId(int cityId);

    //如果hospitalName不为空,模糊匹配
    List<CrmHospitalDO> listFuzzyMatchHospital(String hospitalName, int provinceId, int cityId);

    List<CrmHospitalDO> getHospitalByNameAndCity(String hospitalName, String cityName);

    int insert(String hospitalName, String cityName, String provinceName, String vhospitalCode, Integer checkStatus);
}
