package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.MarketingPosterSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.MarketingPosterVO;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdcrmMarketingPosterDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdcrmMarketingPosterDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdcrmMarketingPosterService;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/1/8 下午5:56
 */
@Service
public class ICfBdcrmMarketingPosterServiceImpl implements ICfBdcrmMarketingPosterService {

    @Resource
    private CfBdcrmMarketingPosterDao cfBdcrmMarketingPosterDao;


    @Override
    public int insertSelective(CfBdcrmMarketingPosterDO record) {
        return cfBdcrmMarketingPosterDao.insertSelective(record);
    }

    @Override
    public CfBdcrmMarketingPosterDO selectByPrimaryKey(Long id) {
        return cfBdcrmMarketingPosterDao.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CfBdcrmMarketingPosterDO record) {
        return cfBdcrmMarketingPosterDao.updateByPrimaryKeySelective(record);
    }

    @Override
    public long getListCountForSea(MarketingPosterSearchModel marketingPosterSearchModel) {
        return cfBdcrmMarketingPosterDao.getListCountForSea(marketingPosterSearchModel);
    }

    @Override
    public List<CfBdcrmMarketingPosterDO> getListForSea(MarketingPosterSearchModel marketingPosterSearchModel) {
        return cfBdcrmMarketingPosterDao.getListForSea(marketingPosterSearchModel);
    }

    @Override
    public long getUpCountForSea(MarketingPosterSearchModel marketingPosterSearchModel) {
        return cfBdcrmMarketingPosterDao.getUpCountForSea(marketingPosterSearchModel);
    }

    @Override
    public void upOrDown(Long id, int publishStatus) {
        cfBdcrmMarketingPosterDao.upOrDown(id,publishStatus);
    }

    @Override
    public void updateIsDelete(Long id, int isDelete) {
        cfBdcrmMarketingPosterDao.updateIsDelete(id,isDelete);
    }

    @Override
    public long getListCountForXiaoJingYu(MarketingPosterSearchModel marketingPosterSearchModel, CrowdfundingVolunteer cfVolunteer) {
        return cfBdcrmMarketingPosterDao.getListCountForXiaoJingYu(marketingPosterSearchModel, cfVolunteer);
    }

    @Override
    public List<MarketingPosterVO> getListForXiaoJingYu(MarketingPosterSearchModel marketingPosterSearchModel, CrowdfundingVolunteer cfVolunteer) {
        return cfBdcrmMarketingPosterDao.getListForXiaoJingYu(marketingPosterSearchModel, cfVolunteer);
    }


}
