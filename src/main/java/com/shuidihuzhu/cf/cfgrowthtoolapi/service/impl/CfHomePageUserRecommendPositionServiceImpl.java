package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.PageSearchModel;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfHomePageUserRecommendPositionDO;
import com.shuidihuzhu.cf.dao.bdcrm.CfHomePageUserRecommendPositionMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfHomePageUserRecommendPositionService;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/6/2 下午6:00
 */
@Service
public class CfHomePageUserRecommendPositionServiceImpl implements CfHomePageUserRecommendPositionService {

    @Resource
    private CfHomePageUserRecommendPositionMapper cfHomePageUserRecommendPositionMapper;

    @Override
    public int insert(CfHomePageUserRecommendPositionDO record) {
        return cfHomePageUserRecommendPositionMapper.insert(record);
    }

    @Override
    public CfHomePageUserRecommendPositionDO selectByPrimaryKey(Long id) {
        return cfHomePageUserRecommendPositionMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CfHomePageUserRecommendPositionDO record) {
        return cfHomePageUserRecommendPositionMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public long getListCountForSea(PageSearchModel pageSearchModel) {
        return cfHomePageUserRecommendPositionMapper.getListCountForSea(pageSearchModel);
    }

    @Override
    public List<CfHomePageUserRecommendPositionDO> getListForSea(PageSearchModel pageSearchModel) {
        return cfHomePageUserRecommendPositionMapper.getListForSea(pageSearchModel);
    }

    @Override
    public void upOrDown(Long id, int publishStatus) {
        if (publishStatus==1){
            CfHomePageUserRecommendPositionDO upStatusDo = cfHomePageUserRecommendPositionMapper.getByUpStatus();
            if (upStatusDo!=null) cfHomePageUserRecommendPositionMapper.downSelf(upStatusDo.getId(), 0, DateUtil.getCurrentDateTimeStr());
            cfHomePageUserRecommendPositionMapper.upSelf(id, 1, DateUtil.getCurrentDateTimeStr());
        }else {
            cfHomePageUserRecommendPositionMapper.downSelf(id, publishStatus, DateUtil.getCurrentDateTimeStr());
        }
    }
    @Override
    public CfHomePageUserRecommendPositionDO getByUpStatus() {
        return cfHomePageUserRecommendPositionMapper.getByUpStatus();
    }

}
