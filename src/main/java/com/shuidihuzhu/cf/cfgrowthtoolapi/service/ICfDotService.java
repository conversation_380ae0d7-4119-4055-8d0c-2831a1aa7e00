package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.DotTagContentBuilder;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInviteCaseRelationDO;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInvitorVisitDO;

/**
 * <AUTHOR>
 * @date 2020-02-11
 */
public interface ICfDotService {

    void report(DotTagContentBuilder dotTagContentBuilder);

    void reportCfToufangInvitorVisitDO(CfToufangInvitorVisitDO cfToufangInvitorVisitDO);

    void reportCfToufangInviteCaseRelationDO(CfToufangInviteCaseRelationDO caseRelationDO, UserInfoModel userInfoModel);
}
