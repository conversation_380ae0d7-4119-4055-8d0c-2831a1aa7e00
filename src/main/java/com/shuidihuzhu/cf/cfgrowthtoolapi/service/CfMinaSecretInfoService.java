package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.model.mina.CfMinaSecretInfo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/9/21
 */
@Service
public class CfMinaSecretInfoService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfMinaSecretInfoService.class);

    private static String cacheKey = "mina-secret-list-";

    private static final Map<Integer, CfMinaSecretInfo> minaMap = Maps.newConcurrentMap();

    private static final Object lock = new Object();

    @Autowired
    private CfMinaSecretInfoBizInterface cfMinaSecretInfoBiz;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    private void setCache() {
        minaMap.clear();
        minaMap.putAll(cfMinaSecretInfoBiz.selectAll().stream().collect(Collectors.toMap(CfMinaSecretInfo::getRank, Function.identity())));
        try {
            long leaseTimeSeconds = 60 * 60L;
            minaMap.forEach((key, value) -> cfRedissonHandler.setNX(cacheKey + String.valueOf(key), value, leaseTimeSeconds * 1000L));
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }

    private CfMinaSecretInfo getCache(int rank) {
            CfMinaSecretInfo cfMinaSecretInfo = cfRedissonHandler.get(cacheKey + String.valueOf(rank), CfMinaSecretInfo.class);
            LOGGER.info("get cfMinaSecretInfo from redis : {}", cfMinaSecretInfo);
            if (cfMinaSecretInfo != null) {
                return cfMinaSecretInfo;
            }
            setCache();
            CfMinaSecretInfo minaSecretInfo = minaMap.get(rank);
            LOGGER.info("get cfMinaSecretInfo from local : {}", minaSecretInfo);
            if (minaSecretInfo != null) {
                return minaSecretInfo;
            }
            minaSecretInfo = minaMap.get(rank);
            return minaSecretInfo;
    }

    public CfMinaSecretInfo getValue(int rank) {
        return getCache(rank);
    }

}
