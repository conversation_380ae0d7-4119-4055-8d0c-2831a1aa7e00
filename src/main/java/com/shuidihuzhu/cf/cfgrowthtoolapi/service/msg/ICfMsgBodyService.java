package com.shuidihuzhu.cf.cfgrowthtoolapi.service.msg;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.CfMsgBodyVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.MsgQueryParam;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-10
 */
public interface ICfMsgBodyService {
    OpResult<List<CfMsgBodyVO>> listMsgBody(MsgQueryParam msgQueryParam);

    OpResult<Long> saveOrUpdateMsg(CfMsgBodyVO cfMsgBodyVO);

    OpResult<Void> deleteMsg(CfMsgBodyVO cfMsgBodyVO);

    List<CfMsgBodyVO> listMsgBodyByModelId(Long modelId);

    OpResult<CfMsgBodyVO> getMsgBody(CfMsgBodyVO cfMsgBodyVO);
}
