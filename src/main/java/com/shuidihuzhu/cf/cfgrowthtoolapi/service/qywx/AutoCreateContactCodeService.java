package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IQywxSdkDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.wxgroup.WxGroupDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum.VolunteerCropTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywx.UpdateExternalContactRemarkParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.model.qy.param.AddContactWayParam;
import com.shuidihuzhu.client.model.qy.response.*;
import com.shuidihuzhu.client.model.qy.response.CustomerAcquisitionResponse.Link;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.kratos.client.api.wecom.dto.WecomExternalUserDetailReq;
import com.shuidihuzhu.kratos.client.api.wecom.dto.WecomExternalUserDetailResp;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AutoCreateContactCodeService {

    String corpId = VolunteerCropTypeEnum.shuidi_chou_service.getCorpId(); // 从配置或参数中获取
    String secret = "E59DzSNcT2-KU9XnhLpOOipzMlmP2pohUfZKTnLC0zw"; // 从配置或参数中获取

    @Autowired
    private IQywxSdkDelegate qywxSdkDelegate;

    @Autowired
    private BdCreateContactUrlRecordService bdCreateContactUrlRecordService;

    @Autowired
    private BdQyWxBindMappingService bdQyWxBindMappingService;

    @Autowired
    private BdQyWxRobotService bdQyWxRobotService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private WxGroupDelegate wxGroupDelegate;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired
    private IMqProducerService mqProducerService;

    public void addContactWay(CrowdfundingVolunteer cfVolunteer, String robotUserId) {
        try {
            log.info(this.getClass().getSimpleName() + " 开始创建联系我链接, uniqueCode:{}, robotUserId:{}",
                    cfVolunteer.getUniqueCode(), robotUserId);

            if (StringUtils.isBlank(corpId) || StringUtils.isBlank(secret)) {
                log.error(this.getClass().getSimpleName() + " 企业微信配置信息缺失, corpId:{}, secret:{}", corpId, secret);
                return;
            }

            // 构建AddContactWayParam参数
            AddContactWayParam param = new AddContactWayParam();
            param.setType(1); // 联系方式类型，1-单人, 2-多人
            param.setScene(2); // 场景，1-在小程序中联系，2-通过二维码联系
            param.setRemark(""); // 联系方式的备注信息，用于助记，不超过30个字符
            param.setIs_temp(false); // 外部客户添加时是否无需验证，默认为true
            param.setSkip_verify(true); // 外部客户添加时是否无需验证，默认为true

            // 设置使用该联系方式的用户userID列表
            List<String> userList = new ArrayList<>();
            userList.add(robotUserId);
            param.setUser(userList);

            // 设置state参数，用于区分不同的添加渠道，此处使用uniqueCode
            param.setState(cfVolunteer.getUniqueCode());

            // 调用qywxSdkDelegate.addContactWay
            AddContactWayResponse response = qywxSdkDelegate.addContactWay(corpId, secret, param);
            log.info(this.getClass().getSimpleName() + ",创建联系我链接返回结果, response:{}", response);

            if (response == null || response.getErrcode() != 0) {
                return;
            }

            String contactUrl = response.getQr_code();
            String configId = response.getConfig_id();

            if (StringUtils.isBlank(contactUrl) || StringUtils.isBlank(configId)) {
                log.error(this.getClass().getSimpleName() + ",创建联系我链接返回结果异常, contactUrl:{}, configId:{}", contactUrl,
                        configId);
                return;
            }

            // 保存记录到数据库
            BdCreateContactUrlRecordDO bdCreateContactUrlRecord = new BdCreateContactUrlRecordDO();
            bdCreateContactUrlRecord.setUniqueCode(cfVolunteer.getUniqueCode());
            bdCreateContactUrlRecord.setVolunteerName(cfVolunteer.getVolunteerName());
            bdCreateContactUrlRecord.setContactUrl(contactUrl);
            bdCreateContactUrlRecord.setRobotUserId(robotUserId);
            bdCreateContactUrlRecord.setConfigId(configId);

            int insertResult = bdCreateContactUrlRecordService.insert(bdCreateContactUrlRecord);
            if (insertResult > 0) {
                log.info(this.getClass().getSimpleName() + " 成功保存联系我链接记录, recordId:{}, uniqueCode:{}, robotUserId:{}",
                        bdCreateContactUrlRecord.getId(), cfVolunteer.getUniqueCode(), robotUserId);
            } else {
                log.error(this.getClass().getSimpleName() + " 保存联系我链接记录失败, uniqueCode:{}, robotUserId:{}",
                        cfVolunteer.getUniqueCode(), robotUserId);
            }

        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " 创建联系我链接异常, uniqueCode:{}, robotUserId:{}, error:{}",
                    cfVolunteer.getUniqueCode(), robotUserId, e.getMessage(), e);
        }
    }

    // 处理回调，绑定微信账号和 uniqueCode
    public void addContactCallback(QyWechatEventCommonModel qyWechatEventCommonModel) {
        try {
            Class aClass = QyWechatEventEnum.parseByType(qyWechatEventCommonModel.getEvent(),
                    qyWechatEventCommonModel.getChangeType());
            if (aClass == null) {
                log.warn(this.getClass().getSimpleName() + " event:{}   changeType:{} 还未支持",
                        qyWechatEventCommonModel.getEvent(), qyWechatEventCommonModel.getChangeType());
                return;
            }
            // 暂时只处理ADD_EXTERNAL_CONTACT_MODEL
            AddExternalContactModel addExternalContactModel = (AddExternalContactModel) qyWechatEventCommonModel;
            // 识别state信息
            String uniqueCode = addExternalContactModel.getState();
            if (StringUtils.isBlank(uniqueCode)) {
                log.warn(this.getClass().getSimpleName() + " state为空,event:{}   changeType:{}",
                        qyWechatEventCommonModel.getEvent(), qyWechatEventCommonModel.getChangeType());
                return;
            }

            // 获取人员,从bdCreateContactUrlRecordService中获取state = uniqueCode, userID =
            // robotUserId,通过uniqueCode + robotUserId 查询
            String robotUserId = addExternalContactModel.getUserID();
            
            // 使用延时消息处理，延时1分钟
            AutoCreateContactCodeDelayMsgModel delayMsgModel = new AutoCreateContactCodeDelayMsgModel();
            delayMsgModel.setRobotUserId(robotUserId);
            delayMsgModel.setUniqueCode(uniqueCode);
            delayMsgModel.setExternalUserId(addExternalContactModel.getExternalUserID());
            
            mqProducerService.sendAutoCreateContactCodeDelayMsg(delayMsgModel, TimeUnit.MINUTES.toMillis(2));
            log.info(this.getClass().getSimpleName() + " 已发送延时消息处理联系方式回调, uniqueCode:{}, robotUserId:{}", 
                    uniqueCode, robotUserId);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " addContactCallback error.", e);
        }
    }



    public void handCallBack(String robotUserId, String uniqueCode, String externalUserId) {
        String lockName = "growthtool_add_contact_callback:" + robotUserId + ":" + uniqueCode;
        RLock lock = null;
        try {
            lock = redissonHandler.getLock(lockName);
            if (!lock.tryLock()) {
                log.info("handCallBack，key: {}", lockName);
                return;
            }
            BdCreateContactUrlRecordDO record = bdCreateContactUrlRecordService.queryByUniqueCodeAndRobotUserId(uniqueCode, robotUserId);
            if (record == null) {
                log.warn(this.getClass().getSimpleName() + " 未找到对应记录,uniqueCode:{}, robotUserId:{}", uniqueCode,
                        robotUserId);
                return;
            }

            // 获取之后更新信息，使用externalUserID填充external_userId
            bdCreateContactUrlRecordService.updateExternalUserIdById(record.getId(), externalUserId);
            BdQyWxRobotDO bdQyWxRobotDO = bdQyWxRobotService.queryByExternalUserId(robotUserId);
            if (bdQyWxRobotDO == null) {
                log.warn(this.getClass().getSimpleName() + " 未找到对应机器人信息,externalUserId:{}", robotUserId);
                return;
            }
            // 查询对应的账号信息，填充到绑定记录中
            CrowdfundingVolunteer volunteer = cfVolunteerService.getVolunteerByUniqueCode(uniqueCode);
            if (volunteer == null) {
                log.warn(this.getClass().getSimpleName() + " 未找到对应人员信息,uniqueCode:{}", uniqueCode);
                return;
            }
            WecomExternalUserDetailReq wecomExternalUserDetailReq = new WecomExternalUserDetailReq();
            wecomExternalUserDetailReq.setStaffUserId(robotUserId);
            wecomExternalUserDetailReq.setCorpId(corpId);
            wecomExternalUserDetailReq.setExternalUserIdList(Lists.newArrayList(externalUserId));
            wecomExternalUserDetailReq.setStaffUserId(bdQyWxRobotDO.getExternalUserId());
            WecomExternalUserDetailResp wecomExternalUserDetailResp = wxGroupDelegate.queryWecomExternalUserDetail(wecomExternalUserDetailReq);

            BdQyWxBindMappingDO byRobotUserIdAndUniqueCode = bdQyWxBindMappingService.getByRobotUserIdAndUniqueCode(robotUserId, uniqueCode);
            if (byRobotUserIdAndUniqueCode != null) {
                //更新下昵称和头像
                if (wecomExternalUserDetailResp != null && CollectionUtils.isNotEmpty(wecomExternalUserDetailResp.getDetailDataList())) {
                    bdQyWxBindMappingService.updateNickName(byRobotUserIdAndUniqueCode.getId(), wecomExternalUserDetailResp.getDetailDataList().get(0).getNickname(),
                            wecomExternalUserDetailResp.getDetailDataList().get(0).getAvatar());
                }
            } else {
                // 生成一条绑定记录BdQyWxBindMappingService
                BdQyWxBindMappingDO bdQyWxBindMapping = new BdQyWxBindMappingDO();
                bdQyWxBindMapping.setUniqueCode(uniqueCode);
                bdQyWxBindMapping.setVolunteerName(volunteer.getVolunteerName());
                if (wecomExternalUserDetailResp != null && CollectionUtils.isNotEmpty(wecomExternalUserDetailResp.getDetailDataList())) {
                    bdQyWxBindMapping.setNickname(wecomExternalUserDetailResp.getDetailDataList().get(0).getNickname());
                    bdQyWxBindMapping.setAvatarUrl(wecomExternalUserDetailResp.getDetailDataList().get(0).getAvatar());
                } else {
                    bdQyWxBindMapping.setNickname("");
                    bdQyWxBindMapping.setAvatarUrl("");
                }
                bdQyWxBindMapping.setExternalUserId(externalUserId);
                bdQyWxBindMapping.setRobotExternalUserId(bdQyWxRobotDO.getExternalUserId());
                bdQyWxBindMapping.setCorpId(corpId);
                bdQyWxBindMapping.setAccountType(bdQyWxRobotDO.getAccountType());
                bdQyWxBindMappingService.insert(bdQyWxBindMapping);
            }
            cancelContactCode(robotUserId, uniqueCode);
            UpdateExternalContactRemarkParam updateExternalContactRemarkParam = new UpdateExternalContactRemarkParam();
            updateExternalContactRemarkParam.setUserid(robotUserId);
            updateExternalContactRemarkParam.setExternal_userid(externalUserId);
            //解密
            String remark = volunteer.getVolunteerName();
            if (StringUtils.isNotBlank(volunteer.getMobile()) && remark.length() <= 7) {
                String decrypt = ShuidiCipherUtils.decrypt(volunteer.getMobile());
                remark = remark + "-" + decrypt;
            }
            //remark 限制20字
            if (remark.length() > 20) {
                remark = remark.substring(0, 20);
            }
            updateExternalContactRemarkParam.setRemark(remark);
            qywxSdkDelegate.updateRemark(corpId, secret, updateExternalContactRemarkParam);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " handCallBack error.", e);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }
    }

    // 生成获客链接
    public void createLink(CrowdfundingVolunteer cfVolunteer, String robotUserId) {
        // 查看是否已经添加过
        BdCreateContactUrlRecordDO record = bdCreateContactUrlRecordService.queryByUniqueCodeAndRobotUserId(cfVolunteer.getUniqueCode(), robotUserId);
        if (record != null) {
            log.warn(this.getClass().getSimpleName() + " 已经添加过,uniqueCode:{}, robotUserId:{}", cfVolunteer.getUniqueCode(), robotUserId);
            return;
        }
        // 生成链接
        CustomerAcquisitionResponse customerAcquisitionResponse = qywxSdkDelegate.createLink(corpId, secret, Lists.newArrayList(robotUserId));
        log.info(this.getClass().getSimpleName() + " 创建获客链接成功, link:{}", customerAcquisitionResponse);
        if (customerAcquisitionResponse == null || customerAcquisitionResponse.getErrcode() != 0) {
            log.error(this.getClass().getSimpleName() + " 创建获客链接失败, errcode:{}", customerAcquisitionResponse.getErrcode());
            return;
        }
        Link link = customerAcquisitionResponse.getLink();
        String contactUrl = link.getUrl() + "?customer_channel=" + cfVolunteer.getUniqueCode();
        String configId = link.getLink_id();
        // 保存记录到数据库
        BdCreateContactUrlRecordDO bdCreateContactUrlRecord = new BdCreateContactUrlRecordDO();
        bdCreateContactUrlRecord.setUniqueCode(cfVolunteer.getUniqueCode());
        bdCreateContactUrlRecord.setVolunteerName(cfVolunteer.getVolunteerName());
        bdCreateContactUrlRecord.setContactUrl(contactUrl);
        bdCreateContactUrlRecord.setRobotUserId(robotUserId);
        bdCreateContactUrlRecord.setConfigId(configId);

        int insertResult = bdCreateContactUrlRecordService.insert(bdCreateContactUrlRecord);
        if (insertResult > 0) {
            log.info(this.getClass().getSimpleName() + " 成功保存联系我链接记录, recordId:{}, uniqueCode:{}, robotUserId:{}",
                    bdCreateContactUrlRecord.getId(), cfVolunteer.getUniqueCode(), robotUserId);
        } else {
            log.error(this.getClass().getSimpleName() + " 保存联系我链接记录失败, uniqueCode:{}, robotUserId:{}", cfVolunteer.getUniqueCode(), robotUserId);
        }

    }

    @Getter
    public static enum LinkCancelReasonEnum {
        ROBOT_OFFLINE(1, "机器人下线"),
        CHANGE_BIND(2, "换绑"),
        ;

        private int code;
        private String desc;

        LinkCancelReasonEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    // 使用场景：
    // 1. 机器人下线
    // 2. 添加完成，保证活码只使用一次
    public boolean cancelContactCode(String robotUserId, String uniqueCode) {
        // 获取记录
        BdCreateContactUrlRecordDO record = bdCreateContactUrlRecordService.queryByUniqueCodeAndRobotUserId(uniqueCode, robotUserId);
        if (record == null) {
            log.warn(this.getClass().getSimpleName() + " 未找到对应记录,uniqueCode:{}, robotUserId:{}", uniqueCode, robotUserId);
            return false;
        }
        String configId = record.getConfigId();
        // 删除记录
        DeleteLinkResponse deleteLink = qywxSdkDelegate.deleteLink(corpId, secret, configId);
        if (deleteLink == null || deleteLink.getErrcode() != 0) {
            log.error(this.getClass().getSimpleName() + " 删除联系我链接失败,configId:{}, result:{}", configId, deleteLink);
            return false;
        }
        log.info(this.getClass().getSimpleName() + " 删除联系我链接成功,configId:{}", configId);
        return true;
    }

    /**
     * 机器人下线处理：批量失效活码并解除绑定关系
     *
     * @param robotUserId 机器人企业微信账号ID
     * @return 处理结果
     */
    public boolean handleRobotOffline(String robotUserId) {
        log.info(this.getClass().getSimpleName() + " 开始处理机器人下线,robotUserId:{}", robotUserId);

        // 参数校验
        if (StringUtils.isBlank(robotUserId)) {
            log.error(this.getClass().getSimpleName() + " robotUserId为空");
            return false;
        }

        // 1. 查找该机器人生成的所有活码记录
        List<BdCreateContactUrlRecordDO> contactRecords = bdCreateContactUrlRecordService
                .queryByRobotUserId(robotUserId);
        if (CollectionUtils.isEmpty(contactRecords)) {
            log.info(this.getClass().getSimpleName() + " 未找到该机器人的活码记录,robotUserId:{}", robotUserId);
        } else {
            log.info(this.getClass().getSimpleName() + " 找到{}条活码记录,robotUserId:{}", contactRecords.size(), robotUserId);

            // 2. 批量使活码失效
            int successCount = 0;
            for (BdCreateContactUrlRecordDO record : contactRecords) {
                // 检查记录是否已经被删除
                if (record.getIsDelete() == 1) {
                    log.info(this.getClass().getSimpleName() + " 记录已被删除,跳过处理,recordId:{}", record.getId());
                    successCount++;
                    continue;
                }

                // 调用已有的cancelContactCode方法处理单个活码
                cancelContactCode(robotUserId, record.getUniqueCode());
                // 更新记录
                bdCreateContactUrlRecordService.cancelLink(record.getId(), LinkCancelReasonEnum.ROBOT_OFFLINE.getDesc());
                successCount++;
                log.info(this.getClass().getSimpleName() + " 成功处理活码,uniqueCode:{}", record.getUniqueCode());
            }
            log.info(this.getClass().getSimpleName() + " 活码失效处理完成,总数:{}, 成功数:{}, robotUserId:{}",
                    contactRecords.size(), successCount, robotUserId);
            bdQyWxBindMappingService.deleteByRobotUserId(robotUserId);
        }

        log.info(this.getClass().getSimpleName() + " 机器人下线处理完成,robotUserId:{}", robotUserId);
        return true;
    }

}
