package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.VolunteerQrCodeTemplate;
import com.shuidihuzhu.cf.dao.VolunteerQrCodeTemplateDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 新二维码暂存表(VolunteerQrCodeTemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-09 18:42:30
 */
@Service("volunteerQrCodeTemplateService")
public class VolunteerQrCodeTemplateServiceImpl implements VolunteerQrCodeTemplateService {
   
    @Resource
    private VolunteerQrCodeTemplateDao volunteerQrCodeTemplateDao;

    @Override
    public VolunteerQrCodeTemplate queryById(long id) {
        return volunteerQrCodeTemplateDao.queryById(id);
    }

    @Override
    public VolunteerQrCodeTemplate queryByUniqueCode(String uniqueCode) {
        return volunteerQrCodeTemplateDao.queryByUniqueCode(uniqueCode);
    }


    @Override
    public int insert(VolunteerQrCodeTemplate volunteerQrCodeTemplate) {
        return volunteerQrCodeTemplateDao.insert(volunteerQrCodeTemplate);
    }

    @Override
    public int update(VolunteerQrCodeTemplate volunteerQrCodeTemplate) {
        return volunteerQrCodeTemplateDao.update(volunteerQrCodeTemplate);
    }

    @Override
    public boolean deleteById(long id) {
        return volunteerQrCodeTemplateDao.deleteById(id) > 0;
    }
}
