package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmVolunteerPreposeMaterialCountDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CrmVolunteerPreposeMaterialCaseCountModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/11/6 7:20 PM
 */
public interface ICfCrmVolunteerPreposeMaterialCountService {
    /**
     * 查询 指定时间段内  一些线下筹款顾问 发起的案例 和 报备的案例数
     * @param dateTimes
     * @param uniqueCodeList
     * @return
     */
    List<CrmVolunteerPreposeMaterialCaseCountModel> getCountByParam(List<String> dateTimes, List<String> uniqueCodeList);

    CfCrmVolunteerPreposeMaterialCountDO getCfCrmVolunteerPreposeMaterialCountDOByParam(String dateTime, String volunteerUniqueCode);

    void batchInsert(List<CfCrmVolunteerPreposeMaterialCountDO> needInsertList);

    void batchUpdateCaseAmount(List<CfCrmVolunteerPreposeMaterialCountDO> needUpdateList);

    /**
     * 先发起案例，后报备时 调用该方法用于 计算顾问的代录入统计
     * @param crowdfundingVolunteer
     */
    void handlePreposeMaterialCount(CrowdfundingVolunteer crowdfundingVolunteer);

    CfCrmVolunteerPreposeMaterialCountDO buildCfCrmVolunteerPreposeMaterialCountDO(String startTimeStr, CrowdfundingVolunteer crowdfundingVolunteer, Map<String, List<Long>> uniqueCodeMapInfoIds);
}
