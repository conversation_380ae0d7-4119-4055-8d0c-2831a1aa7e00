package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdCropUserMappingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.QyWechatHandlerModel;
import com.shuidihuzhu.client.account.wecom.feign.AccessTokenClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.qywechat.QywxSdkClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-03-15 19:47
 **/
@Service
public class QyWechatHandler {

    @Autowired
    private QywxSdkClient qywxSdkClient;

    @Autowired
    private AccessTokenClient accessTokenClient;

    @Autowired
    private BdCropUserMappingService bdCropUserMappingService;

    //主要为了区分不同的账号做不同的事
    public <T> void handle(List<CrowdfundingVolunteer> volunteerList, T object, Consumer<QyWechatHandlerModel<T>> consumer) {
        //判断人员是否在支持主体下
        if (CollectionUtils.isEmpty(volunteerList)) {
            return;
        }
        List<String> uniqueCodes = volunteerList.stream().map(CrowdfundingVolunteer::getUniqueCode)
                .collect(Collectors.toList());
        List<BdCropUserMappingDO> supportUserIds = bdCropUserMappingService.listByUniqueCodeList(uniqueCodes,
                CrowdfundingVolunteerEnum.VolunteerCropTypeEnum.shuidi_chou_support.getType());
        if (CollectionUtils.isNotEmpty(supportUserIds)) {
            Map<String, CrowdfundingVolunteer> crowdfundingVolunteerMap = Maps.newHashMap();
            List<String> userIds = supportUserIds.stream().map(BdCropUserMappingDO::getUserId).collect(Collectors.toList());
            for (BdCropUserMappingDO supportUserId : supportUserIds) {
                volunteerList.stream()
                        .filter(item -> supportUserId.getUniqueCode().equals(item.getUniqueCode()))
                        .findFirst()
                        .ifPresent(volunteer -> crowdfundingVolunteerMap.put(supportUserId.getUserId(), volunteer));
            }
            QyWechatHandlerModel<T> qyWechatHandlerModel = QyWechatHandlerModel
                    .<T>builder()
                    .content(object)
                    .volunteerCropTypeEnum(CrowdfundingVolunteerEnum.VolunteerCropTypeEnum.shuidi_chou_support)
                    .userIds(userIds)
                    .volunteerMap(crowdfundingVolunteerMap)
                    .build();
            consumer.accept(qyWechatHandlerModel);
        }
        List<String> supportUniqueCodes = supportUserIds.stream().map(BdCropUserMappingDO::getUniqueCode).collect(Collectors.toList());
        Map<String, CrowdfundingVolunteer> misToVolunteer = volunteerList.stream()
                .filter(item -> !supportUniqueCodes.contains(item.getUniqueCode()))
                .filter(item -> StringUtils.isNotBlank(item.getMis()))
                .collect(Collectors.toMap(CrowdfundingVolunteer::getMis, Function.identity(), (before, after) -> before));
        //如果是给直营发送,直接通过mis即可
        if (MapUtils.isNotEmpty(misToVolunteer)) {
            QyWechatHandlerModel<T> qyWechatHandlerModel = QyWechatHandlerModel
                    .<T>builder()
                    .content(object)
                    .volunteerCropTypeEnum(CrowdfundingVolunteerEnum.VolunteerCropTypeEnum.shuidi_chou_service)
                    .userIds(Lists.newArrayList(misToVolunteer.keySet()))
                    .volunteerMap(misToVolunteer)
                    .build();
            consumer.accept(qyWechatHandlerModel);
        }
    }

}
