package com.shuidihuzhu.cf.cfgrowthtoolapi.service.experiment;

import com.shuidihuzhu.client.model.ExperimentHitResult;
import com.shuidihuzhu.client.model.ExperimentParam;

/**
 * @Description: 场景策略
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/7/31 2:51 PM
 */
public interface SceneStrategy {

    ExperimentHitResult isHit(ExperimentParam experimentParam);

    Integer getSceneId();

}
