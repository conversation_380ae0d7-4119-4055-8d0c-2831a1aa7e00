package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdReportLinkDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IClewPreproseMaterialService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.dao.CrowdfundingVolunteerCardConfigDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCaseInfoDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdReportLinkDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseCommonInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.enums.VolunteerCardEnums;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteerCardConfigDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrowdfundingVolunteerCardConfigServiceImpl implements CrowdfundingVolunteerCardConfigService{

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegateImpl;

    @Autowired
    private IClewPreproseMaterialService clewPreproseMaterialService;

    @Autowired
    private ICfVolunteerService cfVolunteerServiceImpl;

    @Autowired
    private CrowdfundingVolunteerCardConfigDao crowdfundingVolunteerCardConfigDao;

    @Autowired
    private CfBdReportLinkDao cfBdReportLinkDao;

    @Autowired
    private CfBdCaseInfoDao cfBdCaseInfoDao;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Override
    public CrowdfundingVolunteerCardConfigDO getCardConfig(CrowdfundingVolunteerCardConfigDO cardConfig) {
        CrowdfundingVolunteerCardConfigDO config = crowdfundingVolunteerCardConfigDao.getVolunteerCardConfig(cardConfig.getConfigType(), cardConfig.getCaseId(), cardConfig.getUniqueCode());
        //获取案例信息
        CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegateImpl.getCaseInfoById(cardConfig.getCaseId());
        if (crowdfundingInfo == null){
            log.info("crowdfundingInfoIsNull");
            return null;
        }
        boolean isPartnerAid = isPartnerAid(crowdfundingInfo);
        if (config != null){
            if (!isPartnerAid) {
                log.info("thisVltNotAid {}", isPartnerAid);
                return null;
            }
            log.info("cardConfig {}", config);
            return config;
        }
        CrowdfundingVolunteerCardConfigDO defaultConfig = new CrowdfundingVolunteerCardConfigDO();
        defaultConfig.setCaseId(cardConfig.getCaseId());
        defaultConfig.setConfigType(cardConfig.getConfigType());
        defaultConfig.setUniqueCode(cardConfig.getUniqueCode());
        defaultConfig.setStatus(VolunteerCardEnums.VolunteerCardStatusEnums.show.getCode());
        defaultConfig.setReasonDesc("");
        if (cardConfig.getConfigType() == VolunteerCardEnums.VolunteerCardTypeEnums.single_card.getCode()){

            //不是线下顾问案例不展示按钮
            boolean isVolunteerReport = isVolunteerReport(crowdfundingInfo);
            if (!isVolunteerReport){
                log.info("isVolunteerReport {}", isVolunteerReport);
                return null;
            }

            //小助理案例不展示按钮
            if (!isPartnerAid){
                log.info("thisNotAid {}", isPartnerAid);
                return null;
            }
            String reasonDesc = "";
            int cardStatus = 0;

            //查找举报信息
            CfCaseCommonInfo cfCaseCommonInfo = crowdFundingFeignDelegateImpl.getCaseCommonInfoByCaseIds(Lists.newArrayList(crowdfundingInfo.getId())).getOrDefault(crowdfundingInfo.getInfoId(),null);
            if (cfCaseCommonInfo != null && CollectionUtils.isNotEmpty(cfCaseCommonInfo.getCrowdfundingReports())){
                //有被举报信息
                cardStatus = VolunteerCardEnums.VolunteerCardStatusEnums.hide.getCode();
                reasonDesc += "外部举报,";
            }

            boolean volunteerReport = isVolunteerReport(crowdfundingInfo);

            boolean volunteerNormal = isVolunteerNormal(crowdfundingInfo);

            if (!volunteerReport){
                cardStatus = VolunteerCardEnums.VolunteerCardStatusEnums.hide.getCode();
                reasonDesc += "不是顾问代录入,";
            }
            if (!volunteerNormal){
                cardStatus = VolunteerCardEnums.VolunteerCardStatusEnums.hide.getCode();
                reasonDesc += "顾问账号异常（冻结，离职）";
            }
            defaultConfig.setStatus(cardStatus);
            defaultConfig.setReasonDesc(reasonDesc);
        }
        return defaultConfig;
    }

    @Override
    public Response<String> updateCardConfig(CrowdfundingVolunteerCardConfigDO cardConfig) {

        if (cardConfig.getConfigType() == VolunteerCardEnums.VolunteerCardTypeEnums.single_card.getCode() &&
                cardConfig.getStatus() == VolunteerCardEnums.VolunteerCardStatusEnums.show.getCode()) {
            CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegateImpl.getCaseInfoById(cardConfig.getCaseId());

            boolean volunteerReport = isVolunteerReport(crowdfundingInfo);

            boolean volunteerNormal = isVolunteerNormal(crowdfundingInfo);

            String reasonDesc = "";
            if (!volunteerReport){
                reasonDesc += "不是顾问代录入,";
            }
            if (!volunteerNormal){
                reasonDesc += "顾问账号异常（冻结，离职）";
            }

            if (StringUtils.isNotBlank(reasonDesc)){
                return NewResponseUtil.makeFail(reasonDesc);
            }
        }
        CrowdfundingVolunteerCardConfigDO volunteerCardConfig = crowdfundingVolunteerCardConfigDao.getVolunteerCardConfig(cardConfig.getConfigType(), cardConfig.getCaseId(), cardConfig.getUniqueCode());
        int update;
        if (volunteerCardConfig == null){
            update = crowdfundingVolunteerCardConfigDao.insert(cardConfig);
        } else {
            update = crowdfundingVolunteerCardConfigDao.update(cardConfig);
        }
        if (update > 0){
        String opt = cardConfig.getStatus() == VolunteerCardEnums.VolunteerCardStatusEnums.show.getCode() ?
                VolunteerCardEnums.VolunteerCardStatusEnums.show.getMsg() :
                VolunteerCardEnums.VolunteerCardStatusEnums.hide.getMsg();
            if (cardConfig.getConfigType() == VolunteerCardEnums.VolunteerCardTypeEnums.single_card.getCode()){
                customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(cardConfig.getCaseId()), OperateTypeEnum.CASE_CAED.getDesc(),
                        OperateTypeEnum.CASE_CAED, opt + cardConfig.getReasonDesc(), cardConfig.getOperateUserId(), cardConfig.getOperateUserName()));
            } else {
                customEventPublisher.publish(new OperateLogEvent(this, cardConfig.getUniqueCode(), OperateTypeEnum.VOLUNTEER_CAED.getDesc(),
                        OperateTypeEnum.VOLUNTEER_CAED, opt + cardConfig.getReasonDesc(), cardConfig.getOperateUserId(), cardConfig.getOperateUserName()));
            }
        }

        return NewResponseUtil.makeSuccess("","修改成功");
    }

    //是否是顾问代录入
    private boolean isVolunteerReport(CrowdfundingInfo crowdfundingInfo){
        //查找报备信息
        List<ClewCrowdfundingReportRelation> crowdfundingReportRelations = clewPreproseMaterialService.getReportedRelationsByInfoId(crowdfundingInfo.getId());
        List<String> uniqueCodeList = crowdfundingReportRelations.stream()
                .filter(relation -> relation.getType()==ClewCrowdfundingReportRelation.TypeEnum.XIAN_XIA.getType())
                .filter(relation -> StringUtils.isNotBlank(relation.getUniqueCode()))
                .map(ClewCrowdfundingReportRelation::getUniqueCode)
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(uniqueCodeList) && crowdfundingInfo.getId()>0){
            //查找是否有代录入链接确认信息
            List<CfBdReportLinkDO> reportLinkDOS = cfBdReportLinkDao.listByCaseId(Lists.newArrayList(crowdfundingInfo.getId()));
            uniqueCodeList = reportLinkDOS.stream()
                    .filter(reportLink -> reportLink.getReportType() == ClewCrowdfundingReportRelation.TypeEnum.XIAN_XIA.getType())
                    .filter(reportLink->StringUtils.isNotBlank(reportLink.getUniqueCode()))
                    .map(CfBdReportLinkDO::getUniqueCode)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(uniqueCodeList)){
            return false;
        }
        CfBdCaseInfoDo bdCaseInfoByInfoUuid = cfBdCaseInfoDao.getBdCaseInfoByInfoUuid(crowdfundingInfo.getInfoId());
        if (bdCaseInfoByInfoUuid==null || !uniqueCodeList.contains(bdCaseInfoByInfoUuid.getUniqueCode())){
            return false;
        }
        return true;
    }

    //顾问账号是否正常
    private boolean isVolunteerNormal(CrowdfundingInfo crowdfundingInfo){
        CfBdCaseInfoDo bdCaseInfoByInfoUuid = cfBdCaseInfoDao.getBdCaseInfoByInfoUuid(crowdfundingInfo.getInfoId());
        if (bdCaseInfoByInfoUuid == null) {
            return false;
        }
        CrowdfundingVolunteer crowdfundingVolunteer = cfVolunteerServiceImpl.getByUniqueCode(bdCaseInfoByInfoUuid.getUniqueCode());
        if (crowdfundingVolunteer == null){
            return false;
        } else {
            OpResult<Void> checkIsShowForC = cfVolunteerServiceImpl.checkIsShowForC(crowdfundingVolunteer);
            if (checkIsShowForC.isFail()) {
                return false;
            }
        }
        return true;
    }

    //判断是否由小助理发起案例
    private boolean isPartnerAid (CrowdfundingInfo crowdfundingInfo) {
        CfBdCaseInfoDo bdCaseInfoByInfoUuid = cfBdCaseInfoDao.getBdCaseInfoByInfoUuid(crowdfundingInfo.getInfoId());
        if (bdCaseInfoByInfoUuid == null) {
            return false;
        }
        CrowdfundingVolunteer crowdfundingVolunteer = cfVolunteerServiceImpl.getByUniqueCode(bdCaseInfoByInfoUuid.getUniqueCode());
        if (crowdfundingVolunteer == null){
            return false;
        }
        if (CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AID.getLevel().equals(crowdfundingVolunteer.getLevel())) {
            log.info("crowdfundingVolunteer {}", crowdfundingVolunteer);
            return false;
        }
        return true;
    }
}
