package com.shuidihuzhu.cf.cfgrowthtoolapi.service.prepose.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.prepose.CfMaterialReportRelationDO;
import com.shuidihuzhu.cf.dao.prepose.CfMaterialReportRelationDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.prepose.CfMaterialReportRelationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 材审代录入关联信息(CfMaterialReportRelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-07 11:00:03
 */
@Service("cfMaterialReportRelationService")
public class CfMaterialReportRelationServiceImpl implements CfMaterialReportRelationService {
   
    @Resource
    private CfMaterialReportRelationDao cfMaterialReportRelationDao;

    @Override
    public CfMaterialReportRelationDO queryByInfoId(String infoId) {
        if (StringUtils.isBlank(infoId)) {
            return null;
        }
        return cfMaterialReportRelationDao.queryByInfoId(infoId);
    }

    @Override
    public CfMaterialReportRelationDO queryByEncryptInfo(String encryptInfo) {
        if (StringUtils.isBlank(encryptInfo)) {
            return null;
        }
        return cfMaterialReportRelationDao.queryByEncryptInfo(encryptInfo);
    }
    

    @Override
    public int insert(CfMaterialReportRelationDO cfMaterialReportRelation) {
        return cfMaterialReportRelationDao.insert(cfMaterialReportRelation);
    }

    @Override
    public int updateReportStatus(CfMaterialReportRelationDO cfMaterialReportRelation) {
        return cfMaterialReportRelationDao.updateReportStatus(cfMaterialReportRelation);
    }

    @Override
    public boolean deleteById(long id) {
        return cfMaterialReportRelationDao.deleteById(id) > 0;
    }
}
