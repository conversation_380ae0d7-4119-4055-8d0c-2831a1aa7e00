package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pay;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.PayClientRpcDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolPayUtil;
import com.shuidihuzhu.client.baseservice.pay.enums.*;
import com.shuidihuzhu.client.baseservice.pay.model.PayResultV2;
import com.shuidihuzhu.client.baseservice.pay.model.PayRpcResponse;
import com.shuidihuzhu.client.baseservice.pay.model.v3.PayInfoParamV3;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2022-08-17 20:37
 **/
@Slf4j
@Service
public class PayService {

    @Autowired
    private PayClientRpcDelegate payClientRpcDelegate;
    @Autowired
    private GrowthtoolPayUtil growthtoolPayUtil;


    //预支付
    public PayResultV2 prePay(UserThirdModel userThirdModel, int thirdType, int amountInFen, String payUid) {
        GrowthtoolPayUtil.WxAppEnum wxAppEnum = growthtoolPayUtil.findWxAppEnum(thirdType);
        String apiDomain = growthtoolPayUtil.getApiDomain();
        //获取用户的微信信息
        PayInfoParamV3 payInfoParamV3 = PayInfoParamV3.buildWxAndAli(wxAppEnum.getClientId(), userThirdModel.getUserId(), payUid, wxAppEnum.getPayTypeEnum(), wxAppEnum.getBizType(), wxAppEnum.getPlatform(),
                        amountInFen, wxAppEnum.getProduceName(), userThirdModel.getOpenId(),  wxAppEnum.getProduceName() + "订单", wxAppEnum.getAppId(),
                        wxAppEnum.getBizType(), apiDomain + wxAppEnum.getCallBackUrl())
                .buildCommission(true); //true 是水滴垫付手续费 false 是用户垫付手续费
        log.info("下单参数:payInfoParamV3:{}", JSONObject.toJSONString(payInfoParamV3));
        PayResultV2 payResult = null;
        try {
            PayRpcResponse<PayResultV2> payRpcResponse = payClientRpcDelegate.unifiedOrder(payInfoParamV3);
            if (payRpcResponse != null) {
                payResult = payRpcResponse.getResult();
            }
        } catch (Exception e) {
            log.error("下单payInfoByNewV3 error:", e);
        }
        log.info("下单结果:payResult:{}", JSONObject.toJSONString(payResult));
        return payResult;
    }



    public PayResultV2 prePayWithConfCode(UserThirdModel userThirdModel, int thirdType, int amountInFen, String payUid, int payChannelType) {
        GrowthtoolPayUtil.WxAppEnum wxAppEnum = growthtoolPayUtil.findWxAppEnum(thirdType);
        String apiDomain = growthtoolPayUtil.getApiDomain();
        GrowthtoolPayUtil.PayChannelTypeEnum payChannelTypeEnum = GrowthtoolPayUtil.parseByType(payChannelType);
        //获取用户的微信信息
        PayInfoParamV3 payInfoParamV3 = PayInfoParamV3.buildWxAndAli(wxAppEnum.getClientId(), userThirdModel.getUserId(), payUid, wxAppEnum.getPayTypeEnum(), wxAppEnum.getBizType(), wxAppEnum.getPlatform(),
                        amountInFen, wxAppEnum.getProduceName(), userThirdModel.getOpenId(),  wxAppEnum.getProduceName() + "订单", wxAppEnum.getAppId(),
                        wxAppEnum.getBizType(), apiDomain + wxAppEnum.getCallBackUrl())
                .buildCommission(true); //true 是水滴垫付手续费 false 是用户垫付手续费
        payInfoParamV3.buildQuickBizType(CardBizType.CHOU);
        payInfoParamV3.buildConfCode(payChannelTypeEnum.getConfCode());
        log.info("下单参数:payInfoParamV3:{}", JSONObject.toJSONString(payInfoParamV3));
        PayResultV2 payResult = null;
        try {
            PayRpcResponse<PayResultV2> payRpcResponse = payClientRpcDelegate.unifiedOrder(payInfoParamV3);
            if (payRpcResponse != null) {
                payResult = payRpcResponse.getResult();
            }
        } catch (Exception e) {
            log.error("下单payInfoByNewV3 error:", e);
        }
        log.info("下单结果:payResult:{}", JSONObject.toJSONString(payResult));
        return payResult;
    }
}
