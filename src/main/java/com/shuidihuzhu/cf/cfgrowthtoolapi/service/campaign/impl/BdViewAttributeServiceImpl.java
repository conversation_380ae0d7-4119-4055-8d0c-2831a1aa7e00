package com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign.BdViewAttributeService;
import com.shuidihuzhu.cf.dao.campaign.BdViewAttributeDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.campaignv2.BdViewAttributeDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 个人配置表(BdViewAttribute)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-26 23:53:51
 */
@Service
public class BdViewAttributeServiceImpl implements BdViewAttributeService {

    @Resource
    private BdViewAttributeDao bdViewAttributeDao;

    /**
     * @param uniqueCode 主键
     * @return 实例对象
     */
    @Override
    public BdViewAttributeDO queryByUniqueCode(String uniqueCode) {
        return bdViewAttributeDao.queryByUniqueCode(uniqueCode);
    }


    /**
     * 新增数据
     * @param bdViewAttribute 实例对象
     * @return 实例对象
     */
    @Override
    public int insertOrUpdate(BdViewAttributeDO bdViewAttribute) {
        BdViewAttributeDO dataInDB = queryByUniqueCode(bdViewAttribute.getUniqueCode());
        if (dataInDB != null) {
            return update(bdViewAttribute);
        } else {
            return bdViewAttributeDao.insert(bdViewAttribute);
        }
    }

    /**
     * 修改数据
     * @param bdViewAttribute 实例对象
     * @return 实例对象
     */
    @Override
    public int update(BdViewAttributeDO bdViewAttribute) {
        return bdViewAttributeDao.update(bdViewAttribute);
    }

}