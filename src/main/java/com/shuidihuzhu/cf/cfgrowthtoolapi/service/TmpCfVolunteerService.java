package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IKongmingDataApiClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.TmpCfVolunteer;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.dao.TmpCfVolunteerDao;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.dataservice.kongmingdata.v1.dto.ExternalLoginRequest;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TmpCfVolunteerService {

    @Resource
    private TmpCfVolunteerDao tmpCfVolunteerDao;

    @Resource
    private AlarmClient alarmClient;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private ICrmSelfBuiltOrgReadService organizationService;

    @Autowired
    private ICrmOrganizationRelationService organizationRelationService;

    @Resource(name = "selfBuiltOrgForSea")
    private ICrmSelfBuiltOrgReadService crmOrganizationService;

    @Autowired
    private IKongmingDataApiClientDelegate kongmingDataApiClientDelegate;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;

    //筛选器id来源:https://wdh.feishu.cn/wiki/WTfJwf9WFi1wP9kDouicVIFkn7c
    private static final String AREA_FILTER_V1 = "&v2138e74d39954fb6baa42f2=";

    private static final String AREA_FILTER_V2 = "&b2e499eaa87714bea94c12a4=";

    private static final String AREA_FILTER_V3 = "&m7b587483aaa042a9a65de56=";

    private static final String AREA_FILTER_V4 = "&c6d4234084edf4f07a97af86=";

    private static final String REGION_FILTER_V1 = "&od6fa744516be412ea4e9af5=";

    private static final String REGION_FILTER_V2 = "&e13104792a52e4a3eaa52111=";

    private static final String REGION_FILTER_V3 = "&o584920a7936444b6a97156c=";

    private static final String GROUP_FILTER_V1 = "&t8ead5c86d1f2478181a0cb5=";

    private static final String GROUP_FILTER_V2 = "&g3db46ab6342047339b0a39d=";



    public TmpCfVolunteer getUniqueCode(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return null;
        }
        return tmpCfVolunteerDao.getByUniqueCode(uniqueCode);
    }

    public void sendPushMsg(CrowdfundingVolunteer volunteer) {
        appPushCrmCaseMsgService.sendCaseStatMsg(volunteer);
    }

    public ExternalLoginRequest buildExternalLoginRequest(CrowdfundingVolunteer volunteer) {
        String pageUrl = this.buildPageUrl(volunteer);
        log.info("pageUrl:{}", pageUrl);
        if (StringUtils.isBlank(pageUrl)) {
            return null;
        }
        ExternalLoginRequest request = new ExternalLoginRequest();
        // biz由孔明BI系统开发人员分配提供
        request.setBiz("cf");
        // appKey由孔明BI系统开发人员分配提供
        String appKey = "747c6f5f93450208";
        // 接入方系统的用户账号（员工账号或者手机号），例如sea后台嵌入时，就传登录手机号
        request.setUsername(volunteer.getMis());
        request.setTimestamp(System.currentTimeMillis());
        // 在孔明BI中已配置好的数据看板页面地址(PC端嵌入时加上?ps=iframe2)
        // 在孔明BI中已配置好的数据看板页面地址(移动端嵌入时加上?pref.HostNavOnly=true)
        request.setPageUrl(pageUrl);
        // 加密签名不区分大小写（32位加密）
        String sign = MD5Util.getMD5HashValue(appKey + request.getBiz() + request.getUsername() + request.getTimestamp() + request.getPageUrl());
        request.setAuthSign(sign);
        return request;
    }

    public List<BdCrmOrganizationDO> getMaxLevelOrgs(List<BdCrmOrgUserRelationDO> orgUserRelationDOS) {

        //查找到水滴测试大区所有下级组织包含本身
        List<Long> subordinateOrgIds = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(GeneralConstant.testAreaOrgId)
                .stream()
                .map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toList());

        List<Long> orgIds = orgUserRelationDOS.stream()
                .map(BdCrmOrgUserRelationDO::getOrgId)
                .filter(orgId -> !subordinateOrgIds.contains(orgId))
                .collect(Collectors.toList());

        List<BdCrmOrganizationDO> bdCrmOrganizationDOS = organizationService.getOrgInfoList(orgIds);

        //orgLevel为-1且为叶子节点的组织，认为是属于顾问级别
        //orgLevel为-1且非叶子节点的组织，认为是属于业务经理级别
        bdCrmOrganizationDOS.stream()
                .filter(item -> item.getOrgLevel() == -1)
                .forEach(org -> {
                    if (org.getOrgAttribute() == 0) {
                        org.setOrgLevel(3);
                    } else if (org.getOrgAttribute() == 1) {
                        org.setOrgLevel(2);
                    }
                });

        //取出组织级别最高的组织
        int maxSortLevel = bdCrmOrganizationDOS.stream()
                .map(BdCrmOrganizationDO::getOrgLevel)
                .map(CrowdfundingVolunteerEnum.RoleEnum::parse)
                .filter(Objects::nonNull)
                .mapToInt(CrowdfundingVolunteerEnum.RoleEnum::getSortLevel)
                .max()
                .orElse(-1);
        if (maxSortLevel < 0) {
            return Lists.newArrayList();
        }

        return bdCrmOrganizationDOS.stream()
                .filter(item -> CrowdfundingVolunteerEnum.RoleEnum.parse(item.getOrgLevel()).getSortLevel() == maxSortLevel)
                .collect(Collectors.toList());
    }

    public String buildPageUrl(CrowdfundingVolunteer volunteer) {

        List<BdCrmOrgUserRelationDO> orgUserRelationDOS = organizationRelationService.listMemberOrgRelationByUniqueCode(volunteer.getUniqueCode());
        List<BdCrmOrganizationDO> maxLevelOrgs = this.getMaxLevelOrgs(orgUserRelationDOS);
        log.info("urlMaxLevelOrgs:{}", JSON.toJSONString(maxLevelOrgs));
        if (CollectionUtils.isEmpty(maxLevelOrgs)) {
            return "";
        }
        List<Long> orgIds = maxLevelOrgs.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
        int maxLevel = maxLevelOrgs.get(0).getOrgLevel();

        Map<Long, String> orgIdTPath = crmOrganizationService.listChainByOrgIdsWithDefaultSplitter(orgIds);
        log.info("urlOrgIdTPath:{}", JSON.toJSONString(orgIdTPath));
        if (MapUtils.isEmpty(orgIdTPath)) {
            return "";
        }
        List<String> orgPathList = Lists.newArrayList();
        orgIdTPath.forEach((orgId, path) -> {
            orgPathList.add(path);
        });
        log.info("urlOrgPathList:{}", orgPathList);

        Set<String> bdAreaSet = Sets.newHashSet();
        Set<String> bdRegionSet = Sets.newHashSet();
        Set<String> bdGroupSet = Sets.newHashSet();
        for (String str : orgPathList) {
            // 使用 split 方法按照 '-' 进行分割，得到一个字符串数组
            String[] parts = str.split("-");
            // 根据数组长度依次将每个子串赋值给 bdArea、bdRegion 和 bdGroup 变量
            bdAreaSet.add(parts.length > 3 ? parts[3] : "");
            bdRegionSet.add(parts.length > 4 ? parts[4] : "");
            bdGroupSet.add(parts.length > 5 ? parts[5] : "");
        }


        //线上：
        //大区：
        String bigAreaUrl = "";
        //大区经理
        if (maxLevel == CrowdfundingVolunteerEnum.RoleEnum.BIG_AREA_LEADER.getLevel()
                || maxLevel == CrowdfundingVolunteerEnum.RoleEnum.SUPER_LEADER.getLevel()) {
            bigAreaUrl = "https://kongming.shuiditech.com/m/page/bbf6c20c686194d11821f3eb?pref.HostNavOnly=true";
        }
        //省区 区域：
        String areaUrl = "https://kongming.shuiditech.com/m/page/bbf6c20c686194d11821f3eb?pref.HostNavOnly=true%s";
        //分区：
        String reasonUrl = "https://kongming.shuiditech.com/m/page/t8439152a48894433aa13aa7?pref.HostNavOnly=true%s%s";
        //小组：
        String groupUrl = "https://kongming.shuiditech.com/m/page/j843f1abb1ebb4062857b579?pref.HostNavOnly=true%s%s%s";

        //顾问：
        String bdUrl = "https://kongming.shuiditech.com/m/page/a32c0d279f3fd4a4c83685e7?pref.HostNavOnly=true%s%s%s&t03a7bb5f97334ff8961cffa=%s";

        String pageUrl = "";
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(maxLevel);
        if (Objects.isNull(roleEnum)) {
            return "";
        }
        switch (roleEnum) {
            case SUPER_LEADER:
            case BIG_AREA_LEADER:
                pageUrl = bigAreaUrl;
                break;
            case PROVINCE_LEADER:
                pageUrl = String.format(groupUrl, this.getFilterParam(bdAreaSet, AREA_FILTER_V3), this.getFilterParam(bdRegionSet, REGION_FILTER_V2), this.getFilterParam(bdGroupSet, GROUP_FILTER_V1));
                break;
            case COMMON_LEADER:
                pageUrl = String.format(bdUrl, this.getFilterParam(bdAreaSet, AREA_FILTER_V4), this.getFilterParam(bdRegionSet, REGION_FILTER_V3), this.getFilterParam(bdGroupSet, GROUP_FILTER_V2), volunteer.getVolunteerName());
                break;
            case PARTITION_LEADER:
                pageUrl = String.format(reasonUrl, this.getFilterParam(bdAreaSet, AREA_FILTER_V2), this.getFilterParam(bdRegionSet, REGION_FILTER_V1));
                break;
            case AREA_LEADER:
            case AREA_PROVINCE:
                pageUrl = String.format(areaUrl, this.getFilterParam(bdAreaSet, AREA_FILTER_V1));
                break;
            default:
                break;
        }
        log.info("buildPageUrl:{}", pageUrl);
        return pageUrl;
    }

    public String getFilterParam(Set<String> orgNameSet, String filterStr) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(orgNameSet)) {
            builder.append(filterStr);
            return builder.toString();
        }
        for (String item : orgNameSet) {
            builder.append(filterStr).append(item);
        }
        return builder.toString();
    }

    public void pushBdUrl() {
        List<CrowdfundingVolunteer> volunteers = cfVolunteerService.listAllOnWorkVolunteerByLevels(CrowdfundingVolunteerEnum.bdBroadcastRoles);

        if (CollectionUtils.isEmpty(volunteers)) {
            return;
        }
        volunteers.forEach(volunteer -> {
            List<BdCrmOrgUserRelationDO> relationDOList = crmMemberInfoService.listNotInTestRelation(volunteer.getUniqueCode());
            if (CollectionUtils.isNotEmpty(relationDOList)) {
                //发送飞书消息
                this.sendByVolunteer(volunteer);
                //发送push消息
                this.sendPushMsg(volunteer);
            }
        });

    }

    public void pushOneBdUrl(String uniqueCode) {
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(uniqueCode);
        if (Objects.isNull(volunteer)) {
            return;
        }
        List<BdCrmOrgUserRelationDO> relationDOList = crmMemberInfoService.listNotInTestRelation(volunteer.getUniqueCode());
        if (CollectionUtils.isEmpty(relationDOList)) {
            return;
        }
        //发送飞书消息
        this.sendByVolunteer(volunteer);
        //发送push消息
        this.sendPushMsg(volunteer);
    }

    private void sendByVolunteer(CrowdfundingVolunteer volunteer) {
        if (Objects.isNull(volunteer)) {
            return;
        }
        alarmClient.sendByUser(Lists.newArrayList(volunteer.getMis()), buildContent());
    }

    private String buildContent() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(DateUtil.getDate2YMDStr(new Date()));
        stringBuilder.append("[顾问帮助筹款人做转发]最新完成情况，前往【鲸小胖】-【案例运营】查看详情>>>");
        return stringBuilder.toString();
    }

}
