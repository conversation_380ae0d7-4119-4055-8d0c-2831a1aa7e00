package com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign.BdFollowHospitalService;
import com.shuidihuzhu.cf.dao.campaign.BdFollowHospitalDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.campaignv2.BdFollowHospitalDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 个人关注医院表(BdFollowHospital)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-26 23:54:18
 */
@Service("bdFollowHospitalService")
public class BdFollowHospitalServiceImpl implements BdFollowHospitalService {

    @Resource
    private BdFollowHospitalDao bdFollowHospitalDao;

    @Override
    public List<BdFollowHospitalDO> listByUniqueCode(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return Lists.newArrayList();
        }
        return bdFollowHospitalDao.listByUniqueCode(uniqueCode);
    }

    @Override
    public BdFollowHospitalDO queryByUniqueAndHosp(String uniqueCode, String hospitalCode) {
        if (StringUtils.isBlank(uniqueCode) || StringUtils.isBlank(hospitalCode)) {
            return null;
        }
        return bdFollowHospitalDao.queryByUniqueAndHosp(uniqueCode, hospitalCode);
    }


    @Override
    public int insert(BdFollowHospitalDO bdFollowHospital) {
        return bdFollowHospitalDao.insert(bdFollowHospital);
    }

    @Override
    public int update(BdFollowHospitalDO bdFollowHospital) {
        return bdFollowHospitalDao.update(bdFollowHospital);
    }

    @Override
    public int updateDeleteStatus(long id, int deleteCode) {
        return bdFollowHospitalDao.updateDeleteStatus(id, deleteCode);
    }
}