package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfBdPartnerBillOrgSummaryDO;

import java.util.List;

/**
 * 爱心伙伴兼职-组织总账单(CfBdPartnerBillOrgSummary)表服务接口
 *
 * <AUTHOR>
 * @since 2021-09-02 19:49:45
 */
public interface CfBdPartnerBillOrgSummaryService {

    CfBdPartnerBillOrgSummaryDO queryById(long id);

    int insertOrUpdate(CfBdPartnerBillOrgSummaryDO cfBdPartnerBillOrgSummary);

    int update(CfBdPartnerBillOrgSummaryDO cfBdPartnerBillOrgSummary);

    int updateApproveStatus(int approveStatus, long orgId, int cycleId);

    boolean deleteById(long id);

    List<CfBdPartnerBillOrgSummaryDO> listByOrgIdsAndCycleId(List<Long> orgIds, int cycleId);

    int deleteByCycleId(long cycleId);
}