package com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.campaign.CrmOrgPreStatisticsDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.campaignv2.CrmOrgPreStatisticsDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign.CrmOrgPreStatisticsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 每日-每个组织当天累计数据(CrmOrgPreStatistics)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-11 15:48:59
 */
@Service
public class CrmOrgPreStatisticsServiceImpl implements CrmOrgPreStatisticsService {

    @Resource
    private CrmOrgPreStatisticsDao crmOrgPreStatisticsDao;

    public Function<CrmOrgPreStatisticsDO, String> statisticsFunction = item -> item.getOrgId() + "-" + item.getDateKey() + "-" + item.getHourNum();


    @Override
    public void batchInsertOrUpdate(List<CrmOrgPreStatisticsDO> crmOrgPreStatistics, String dateStr, int hourNum) {
        if (CollectionUtils.isEmpty(crmOrgPreStatistics)) {
            return;
        }
        //org_id + day_time + hour唯一
        List<CrmOrgPreStatisticsDO> crmOrgPreStatisticsDOS = crmOrgPreStatisticsDao.listByDateAndHour(dateStr, hourNum);
        List<Long> orgIds = crmOrgPreStatisticsDOS.stream().map(CrmOrgPreStatisticsDO::getOrgId).collect(Collectors.toList());

        Map<String, CrmOrgPreStatisticsDO> uniqueKeyMap = crmOrgPreStatisticsDOS.stream()
                .collect(Collectors.toMap(statisticsFunction, Function.identity(), (before, after) -> before));

        List<CrmOrgPreStatisticsDO> needUpdate = crmOrgPreStatistics.stream()
                .filter(item -> orgIds.contains(item.getOrgId()))
                .map(item -> {
                    CrmOrgPreStatisticsDO crmOrgPreStatisticsDO = uniqueKeyMap.get(item.getOrgId() + "-" + item.getDateKey() + "-" + item.getHourNum());
                    if (crmOrgPreStatisticsDO != null) {
                        item.setId(crmOrgPreStatisticsDO.getId());
                        return item;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());


        needUpdate.forEach(this::update);

        List<CrmOrgPreStatisticsDO> needAdd = crmOrgPreStatistics.stream()
                .filter(item -> !orgIds.contains(item.getOrgId()))
                .filter(CrmOrgPreStatisticsDO::cannotIgnoreData)
                .collect(Collectors.toList());

        //需要更新的
        Lists.partition(needAdd, 100)
                .forEach(item -> crmOrgPreStatisticsDao.batchInsert(item));
    }

    @Override
    public int update(CrmOrgPreStatisticsDO crmOrgPreStatistics) {
        return crmOrgPreStatisticsDao.update(crmOrgPreStatistics);
    }

    @Override
    public List<CrmOrgPreStatisticsDO> listByDateKeysOrgId(long orgId, List<String> dateKeys, int maxHour) {
        if (CollectionUtils.isEmpty(dateKeys) || orgId <= 0) {
            return Lists.newArrayList();
        }
        return crmOrgPreStatisticsDao.listByDateKeysOrgId(orgId, dateKeys, maxHour);
    }

    @Override
    public List<CrmOrgPreStatisticsDO> listTrend(long orgId, List<String> dateKeys) {
        if (CollectionUtils.isEmpty(dateKeys) || orgId <= 0) {
            return Lists.newArrayList();
        }
        return crmOrgPreStatisticsDao.listTrend(orgId, dateKeys);
    }

    @Override
    public CrmOrgPreStatisticsDO getHourDataByOrgId(String dateKey, int maxHour, long orgId) {
        return crmOrgPreStatisticsDao.getHourDataByOrgId(dateKey, maxHour, orgId);
    }


}