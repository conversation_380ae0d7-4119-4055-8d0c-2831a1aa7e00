package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.cache.*;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.SeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdcrmVolunteerOrgnizationCopyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.PreVolunteerOrgInfoRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.SeaAdminUserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.OrgMembersResult;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCrmVolunteerOrgnization;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrgConvertService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.dao.bdcrm.PreVolunteerOrgInfoRelationDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class CfCrmVolunteerOrgnizationImpl implements ICfCrmVolunteerOrgnization {
    @Autowired
    private SeaAccountServiceDelegate seaAccountServiceDelegate;

    @Autowired
    private ICrmOrgConvertService crmOrgConvertService;

    @Autowired
    private PreVolunteerOrgInfoRelationDao preVolunteerOrgInfoRelationDao;

    private LoadingCache<Integer,List<OrgInfoModel>> subOrgInfoModelCache = CacheBuilder
            .newBuilder()
            .maximumSize(500)
            .refreshAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, List<OrgInfoModel>>() {
                @Override
                public List<OrgInfoModel> load(Integer orgId) throws Exception {
                    List<OrgInfoModel> orgInfoModels = getSubOrgByOrgId(orgId);
                    return orgInfoModels;
                }
            });



    @Override
    public PreposeMaterialCommitNoticeModel buildPreposeMaterialCommitNoticeModel(PreposeMaterialModel.MaterialInfoVo materialInfoVo,
                                                                                  ClewCrowdfundingReportRelation clewCrowdfundingReportRelation,
                                                                                  String mis,
                                                                                  String uniqueCode,
                                                                                  ClewCrowdfundingReportRelation.TypeEnum typeEnum){
        try {
            PreposeMaterialCommitNoticeModel model = new PreposeMaterialCommitNoticeModel();
            model.setCreateTime(new Date());
            model.setInfoId(clewCrowdfundingReportRelation.getInfoId());
            model.setPatientIdCard(materialInfoVo.getPatientIdCard());
            model.setPreposeMaterialId(materialInfoVo.getId());
            model.setServiceChannel(typeEnum.getDesc());
            model.setUserEncryptPhone(ShuidiCipherUtils.encrypt(materialInfoVo.getRaiseMobile()));
            model.setSubmitter(clewCrowdfundingReportRelation.getVolunteerName());
            // 如果是线下顾问  则需要 通过 开关 控制获取组织的来源
            if (typeEnum == ClewCrowdfundingReportRelation.TypeEnum.XIAN_XIA){
                AdminOrganization userOrganization = crmOrgConvertService.getUserOrganizationByUniqueCode(uniqueCode);
                if (userOrganization!=null){
                    model.setOrgName(userOrganization.getName());
                }
                return model;
            }else {
                // 如果是 线上服务  则直接从sea-auth拉取组织关系即可
                OpResult<SeaAdminUserInfoModel> opResult = seaAccountServiceDelegate.getUserAccountsByMis(mis);
                if (opResult.isFailOrNullData()) {
                    log.warn(this.getClass().getSimpleName() + "  buildPreposeMaterialCommitNoticeModel getUserAccountsByMis({}) fail", mis);
                    return model;
                }
                model.setOrgName(opResult.getData().getOrgName());
                return model;
            }
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" buildPreposeMaterialCommitNoticeModel exception:",e);
        }
        return null;
    }


    /**
     * 根据orgId 获取下所有组织的人员
     * @param orgId
     * @return
     */
    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> getAllUserByOrgId(int orgId) {
        return crmOrgConvertService.getAllUserByOrgId(orgId);
    }


    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> getAllUserByOrgIds(List<Long> orgIds) {
        return crmOrgConvertService.getAllUserByOrgIds(orgIds);
    }



    @Override
    public List<OrgInfoModel> getSubOrgByOrgIdFromLoadingCache(int orgId){
        try {
            return subOrgInfoModelCache.get(orgId);
        } catch (ExecutionException e) {
            log.error(this.getClass().getName()+" getSubOrgByOrgIdFromLoadingCache err:",e);
        }
        return Lists.newArrayList();
    }


    @Override
    public List<OrgInfoModel> getSubOrgByOrgId(int orgId) {
        List<OrgInfoModel> orgByOrgIdList = crmOrgConvertService.getSubOrgByOrgId(orgId);
        return orgByOrgIdList.stream().map(orgInfoModel -> {
            String orgName = orgInfoModel.getOrgName();
            orgInfoModel.setOrgName(orgName.substring(orgName.lastIndexOf("-")+1));
            return orgInfoModel;
        }).collect(Collectors.toList());
    }


    @Override
    public OrgInfoModel getOrgByOrgId(int orgId) {
        List<OrgInfoModel> orgByOrgIdList = crmOrgConvertService.getOrgListByOrgId(Lists.newArrayList(orgId));
        return orgByOrgIdList.stream().map(orgInfoModel -> {
            String orgName = orgInfoModel.getOrgName();
            orgInfoModel.setOrgName(orgName.substring(orgName.lastIndexOf("-")+1));
            return orgInfoModel;
        }).collect(Collectors.toList()).get(0);
    }


    @Override
    public List<OrgInfoModel> getOrgListByOrgId(List<Integer> orgIdList) {
        List<OrgInfoModel> orgByOrgIdList = crmOrgConvertService.getOrgListByOrgId(orgIdList);
        if (CollectionUtils.isEmpty(orgByOrgIdList)){
            return null;
        }
        return orgByOrgIdList.stream().map(orgInfoModel -> {
            String orgName = orgInfoModel.getOrgName();
            orgInfoModel.setOrgName(orgName.substring(orgName.lastIndexOf("-")+1));
            return orgInfoModel;
        }).collect(Collectors.toList());
    }


    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> getBdCrmVolunteerOrgnizationSimpleModelByMisList(List<String> misList) {
        if (CollectionUtils.isEmpty(misList)){
            return Lists.newArrayList();
        }
        return crmOrgConvertService.getBdCrmVolunteerOrgnizationSimpleModelByMisList(misList);
    }

    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> getBdCrmVolunteerOrgnizationSimpleModelByMisNameList(List<String> misNameList) {
        if (CollectionUtils.isEmpty(misNameList)){
            return Lists.newArrayList();
        }
        List<List<String>> listList = Lists.partition(misNameList, GeneralConstant.MAX_PAGE_SIZE);

        List<BdCrmVolunteerOrgnizationSimpleModel> result = listList.parallelStream().map(list -> crmOrgConvertService.getSimpleModelByMisNameList(list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).get();
        return result;
    }

    /**
     * 获得线下所有的组织
     * @return
     */
    @Override
    public List<OrgInfoModel> getOfflineAllOrg() {
        return crmOrgConvertService.getAllOrg();
    }


    /**
     * 使用的自建组织
     * 注意：仅用于线下组织获取
     * 根据misId获得当前组织信息
     * @param mis
     * @return
     */
    @Override
    public AdminOrganization getUserOrganization(String mis) {
        if(StringUtils.isBlank(mis)){
            return null;
        }
        return crmOrgConvertService.getUserOrganization(mis);
    }

    /**
     * 注意：仅用于线下组织获取
     * 根据上一级orgId 获得 OrgMembersResult
     * @param parentOrgId
     * @return
     */
    @Override
    public OrgMembersResult getOrgMembers(int parentOrgId) {
        return crmOrgConvertService.getOrgMembers(parentOrgId);
    }


    /**
     * 根据orgId获得下面所有人员的 uniqueCode
     * @param orgId
     * @return
     */
    @Override
    public List<String> getUniqueCodeListByOrgId(int orgId){
        List<BdCrmVolunteerOrgnizationSimpleModel> simpleModels = this.getAllUserByOrgId(orgId);
        if (CollectionUtils.isEmpty(simpleModels)){
            return Lists.newArrayList();
        }
        List<String> uniqueCodeList = simpleModels.stream().map(BdCrmVolunteerOrgnizationSimpleModel::getUniqueCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uniqueCodeList)){
            return Lists.newArrayList();
        }
        return uniqueCodeList;
    }


    @Override
    public OrgInfoModel getParentOrgByOrgId(int orgId){
        return crmOrgConvertService.getParentOrgByOrgId(orgId);
    }

    @Override
    public PreVolunteerOrgInfoRelationDO getPreVolunteerOrgInfoRelationDO(String uniqueCode) {
        return  preVolunteerOrgInfoRelationDao.getPreVolunteerOrgInfoRelationDO(uniqueCode);
    }
}
