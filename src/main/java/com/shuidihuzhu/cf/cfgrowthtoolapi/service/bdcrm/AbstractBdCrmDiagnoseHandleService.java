package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseBaseDataModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseForCalcModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IBdCrmOrgAndCityRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2021/4/16 下午1:58
 */
@Slf4j
public abstract class AbstractBdCrmDiagnoseHandleService extends AbstractBdCrmDiagnoseService{
    @Autowired
    protected CfBdCrmDiagnoseDataService cfBdCrmDiagnoseDataService;
    @Autowired
    private CfBdCrmDiagnoseResultService cfBdCrmDiagnoseResultService;
    @Autowired
    protected ICfCompetitioMarketDataService cfCompetitioMarketDataService;
    @Autowired
    protected IBdCrmOrgAndCityRelationService bdCrmOrgAndCityRelationService;
    @Autowired
    protected ICrmSelfBuiltOrgReadService crmOrganizationService;

    protected Map<String, CfBdCrmDiagnoseDataDO> uniqueKeyMapCurData;
    protected Map<String, CfBdCrmDiagnoseBaseDataModel> orgIdWithCityIdMapPreData;
    protected Map<Long, CfBdCrmDiagnoseDataDO> provinceIdMapCurData;
    protected Map<Long, CfBdCrmDiagnoseDataDO> cityIdMapCurData;
    protected Set<String> unConfigObjectiveMarketShareOrg;
    protected Set<String> unConfigProvinceOrCityForOrg;
    protected Set<String> unConfigHierarchyCity;


    // 诊断数据
    public void diagnoseData(CfBdCrmDiagnoseForCalcModel calcModel) {
        if (calcModel ==null) return;
        this.calcModel = calcModel;
        this.configUniqueKeyMapData();
        CfBdCrmDiagnoseResultDO diagnoseResult = diagnoseOrg(new BdCrmOrganizationDO(GeneralConstant.CRM_ORG_ID, "全国",0L), 0L);
        cfBdCrmDiagnoseResultService.insert(diagnoseResult);
        for (BdCrmOrganizationDO organizationDO : this.calcModel.getAllAreaOrgList()) {
            if ((calcModel.getOrgId()!=null && calcModel.getOrgId().intValue()==organizationDO.getId()) || calcModel.getOrgId()==null || calcModel.getOrgId().intValue()==GeneralConstant.CRM_ORG_ID) {
                diagnoseAreaOrg(organizationDO, diagnoseResult.getId());
            }
        }
        for (BdCrmOrganizationDO organizationDO : this.calcModel.getAllAreaOrgWithSubOrgList()) {
            if (this.calcModel.getAllAreaOrgList().contains(organizationDO)) continue; // 区域组织 无需再次诊断
            if ((calcModel.getOrgId()!=null && calcModel.getOrgId().intValue()==organizationDO.getId()) || calcModel.getOrgId()==null) {
                diagnoseOtherOrg(organizationDO);
            }
        }
        alarm();
    }

    private void configUniqueKeyMapData(){
        this.uniqueKeyMapCurData = cfBdCrmDiagnoseDataService.listByDateTime(calcModel.getCurDateTime(),calcModel.getCurDayRange(),calcModel.getPreDateTime(),calcModel.getPreDayRange()).stream()
                .collect(Collectors.toMap(CfBdCrmDiagnoseDataDO::showUniqueKey, Function.identity(), (oldObj, newObj) -> newObj));
        this.orgIdWithCityIdMapPreData = Maps.newHashMapWithExpectedSize(this.uniqueKeyMapCurData.size());
        for (CfBdCrmDiagnoseDataDO diagnoseDataDO : uniqueKeyMapCurData.values()) {
            orgIdWithCityIdMapPreData.put(this.getKeyForOrgIdWithCityIdMapPreData(diagnoseDataDO.getOrgId(), diagnoseDataDO.getCityId()), diagnoseDataDO.getPreDiagnoseBaseDataModel());
        }
        Map<Long, CfBdCrmDiagnoseDataDO> cityIdMapData = uniqueKeyMapCurData.values().stream().collect(Collectors.toMap(CfBdCrmDiagnoseDataDO::getCityId, Function.identity(), (oldObj, newObj) -> newObj));
        List<Long> provinceIdList = Lists.newArrayList(calcModel.getProvinceIdMapSdCityList().keySet());
        List<Long> cityList = calcModel.getProvinceIdMapSdCityList().values().stream().flatMap(list->list.stream()).map(SdCityHierarchyDO::getCityId).distinct().collect(Collectors.toList());
        provinceIdMapCurData = provinceIdList.stream().map(provinceId -> cityIdMapData.get(provinceId)).filter(item -> item!=null)
                .collect(Collectors.toMap(CfBdCrmDiagnoseDataDO::getCityId,Function.identity(),(oldObj,newObj)->newObj));
        cityIdMapCurData = cityList.stream().map(cityId -> cityIdMapData.get(cityId)).filter(item -> item!=null)
                .collect(Collectors.toMap(CfBdCrmDiagnoseDataDO::getCityId,Function.identity(),(oldObj,newObj)->newObj));
        this.unConfigObjectiveMarketShareOrg = Sets.newHashSet();
        this.unConfigProvinceOrCityForOrg = Sets.newHashSet();
        this.unConfigHierarchyCity = Sets.newHashSet();
    }
    // 报警
    protected abstract void alarm();
    // 诊断其他组织
    protected abstract void diagnoseOtherOrg(BdCrmOrganizationDO organizationDO);
    // 诊断区域组织
    protected abstract void diagnoseAreaOrg(BdCrmOrganizationDO organizationDO, Long parentId);
    // 诊断全国
    protected abstract CfBdCrmDiagnoseResultDO diagnoseOrg(BdCrmOrganizationDO organizationDO, long parentId);

}
