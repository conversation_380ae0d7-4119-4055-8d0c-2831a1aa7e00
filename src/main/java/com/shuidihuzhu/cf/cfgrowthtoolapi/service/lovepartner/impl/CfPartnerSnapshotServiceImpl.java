package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerSnapshotDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerSnapshotService;
import com.shuidihuzhu.cf.dao.lovepartner.CfPartnerSnapshotDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */

@Service
@Slf4j
public class CfPartnerSnapshotServiceImpl implements CfPartnerSnapshotService {

    @Autowired
    private CfPartnerSnapshotDao partnerSnapshotDao;


    @Override
    public int batchInsert(List<CfPartnerSnapshotDo> partnerSnapshotDoList) {
        if (CollectionUtils.isEmpty(partnerSnapshotDoList)){
            return 0;
        }
        return partnerSnapshotDao.batchInsert(partnerSnapshotDoList);
    }

    @Override
    public int deleteByCycleId(long cycleId) {
        return partnerSnapshotDao.deleteByCycleId(cycleId);
    }

    @Override
    public List<CfPartnerSnapshotDo> listByCycleId(Long cycleId) {
        return partnerSnapshotDao.listByCycleId(cycleId);
    }
}
