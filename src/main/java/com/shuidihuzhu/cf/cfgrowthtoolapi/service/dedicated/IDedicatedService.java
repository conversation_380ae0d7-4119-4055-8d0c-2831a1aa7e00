package com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.AddExternalContactModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.DelExternalContactModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffFriendDO;
import com.shuidihuzhu.client.cf.api.model.CfServiceStaffFriendModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewQyWxCorpDO;
import com.shuidihuzhu.client.cf.growthtool.model.CustomerModel;
import com.shuidihuzhu.wx.grpc.model.WxMpSubscribeModel;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/1/3 11:05 PM
 */
public interface IDedicatedService {
    /**
     * 通过客户的userid 查询
     * @author: wanghui
     * @time: 2019/9/12 4:32 PM
     * @description: getCfServiceStaffFriendByUserId
     * @param: [userId]
     * @return: com.shuidihuzhu.cf.response.OpResult<com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffFriendDO>
     */
    OpResult<CfServiceStaffFriendDO> getCfServiceStaffFriendByUserId(Long userId);

    OpResult<List<CfServiceStaffFriendDO>> getAllCfServiceStaffFriendsByUserId(Long userId);

    /**
     * 通过客户的unionId 查询
     * @author: wanghui
     * @time: 2019/9/12 4:32 PM
     * @description: getCfServiceStaffFriendByUnionId
     * @param: [unionId]
     * @return: com.shuidihuzhu.cf.response.OpResult<java.util.List<com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffFriendDO>>
     */
    OpResult<List<CfShuidichouWechatFriendDO>> getCfServiceStaffFriendByUnionId(String unionId);
    /**
     * 保存 cf_service_staff_friend 记录
     * @author: wanghui
     * @time: 2019/9/12 4:32 PM
     * @description: saveCfServiceStaffFriend
     * @param: [cfServiceStaffFriendDO]
     * @return: int
     */
    int saveCfServiceStaffFriend(CfServiceStaffFriendDO cfServiceStaffFriendDO);

    int saveCfShuidichouWechatFriend(CfShuidichouWechatFriendDO cfShuidichouWechatFriendDO);

    /**
     * 根据 commonUserId 更新 cf_service_staff_friend 记录
     * @author: wanghui
     * @time: 2019/9/12 4:32 PM
     * @description: updateCfServiceStaffFriendByCommonUserId
     * @param: [cfServiceStaffFriendDO]
     * @return: int
     */
    int updateCfServiceStaffFriendByCommonUserId(CfServiceStaffFriendDO cfServiceStaffFriendDO);

    /**
     * 查询专属客服
     * @author: wanghui
     * @time: 2019/9/12 4:32 PM
     * @description: getCfServiceStaffList
     * @param: []
     * @return: com.shuidihuzhu.cf.response.OpResult<java.util.List<com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffDO>>
     */
    OpResult<List<CfServiceStaffDO>> getCfServiceStaffList(Integer type);

    /**
     * 根据服务人员userid  查询
     * @author: wanghui
     * @time: 2019/9/12 4:32 PM
     * @description: getCfServiceStaffByQyWechatUserId
     * @param: [qyWechatUserId]
     * @return: com.shuidihuzhu.cf.response.OpResult<com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffDO>
     */
    OpResult<CfServiceStaffDO> getCfServiceStaffByQyWechatUserId(String qyWechatUserId);


    OpResult<List<CfServiceStaffDO>> getCfServiceStaffByQyWechatUserIds(List<String> qyWechatUserIds);

    /**
     * 查询 专属客服 添加好友数
     * @author: wanghui
     * @time: 2019/9/12 4:32 PM
     * @description: getCfServiceStaffFriendAmount
     * @param: [time, dedicatedCfServiceStaff]
     * @return: java.util.List<com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffFriendCountDO>
     */
    List<CfServiceStaffFriendCountDO> getCfServiceStaffFriendAmount(Date time,List<CfServiceStaffDO> dedicatedCfServiceStaff);

    /**
     * 查询 专属客服 添加好友数
     * @author: wanghui
     * @time: 2019/9/12 4:32 PM
     * @description: getCfServiceStaffFriendAmountNew
     * @param: []
     * @return: java.util.List<com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffFriendCountDO>
     * @param isFromApp
     */
    List<CfServiceStaffFriendCountDO> getCfServiceStaffFriendAmountNew(Boolean isFromApp);

    List<String> getExternalUseridByQyWechatUserIdWithPhone(String qyWechatUserId, String phone);

    List<CfServiceStaffFriendModel> getCfServiceStaffFriendByUnionIdWithQyWechatUserId(String unionId,
                                                                                       String qyWechatUserId);

    CfServiceStaffDO getByQyWechatUserId(String qyWechatUserId);


    List<CfShuidichouWechatFriendDO> getCfShuidichouWechatFriendByExternalUseridWithQyWechatUserId(String externalUserid,
                                                                                                   String qyWechatUserId);
    List<CfShuidichouWechatFriendDO> getWechatFriendByExternalUseridWithQyWechatUserIdIgnoreDelete(String externalUserid,
                                                                                                   String qyWechatUserId);

    int saveCfServiceStaffDO(CfServiceStaffDO cfServiceStaffDO);

    List<WxMpSubscribeModel> listUserSubscribeOfMajor(long userId);

    void updateServiceStaffUserInfo(String qywechatid, Integer serviceType, String qrCode, String headUrl, Integer id, Integer helpPatients, Integer raiseAmount, String labels,String qyWechatQrCode);

    int updateShowTimeByQyWechatUserId(String qywechatid, String showTime);

    OpResult<CfServiceStaffFriendDO> getCfServiceStaffFriendByUserIdFromApp(long userId, Date startDate, Date endDate,Boolean isFromApp);

    void fullPhoneForCfServiceStaffFriendInDB(Date startDay,Date endDay,Integer callBackId);

    /**
     * 获取前一天被1v1服务人员成功添加为好友的记录，并给这批用户发送客服消息
     */
    int handleServiceFriendsSendMsg();

    List<String> getAllQyWechatUserId();


    /**
     * 获取前一天被1v1 服务人员成功添加为好友的记录
     * @return
     */
    List<CfShuidichouWechatFriendDO> getYesterdayServiceStaffFriends();

    /**
     * 获取被1v1 服务人员成功添加为好友的记录
     * @param begin
     * @param end
     * @return
     */
    List<CfShuidichouWechatFriendDO> getServiceStaffFriendsByDate(Date begin, Date end);

    /**
     * @author: wanghui
     * @time: 2019/10/16 4:26 PM
     * @description: getQyWechatUserIdByuserEncryPhone  根据 加密手机号 查询 cf_service_staff_friend中的记录
     * @param: [currentTime, userEncryPhone]
     * @return: java.lang.String
     */
    String getQyWechatUserIdByuserEncryPhone(Date currentTime,
                                             String userEncryPhone);

    /**
     * @author: wanghui
     * @time: 2019/10/16 4:26 PM
     * @description: getQyWechatUserIdByuserId  根据 userid 查询 cf_service_staff_friend中的记录
     * @param: [currentTime, userId]
     * @return: java.lang.String
     */
    String getQyWechatUserIdByuserId(Date currentTime, long userId);

    /**
     * @author: wanghui
     * @time: 2019/10/16 4:26 PM
     * @description: getCfClewForRecallModelByClewDisplayId  根据 displayid 查询cf_clew_for_recall表中的记录
     * @param: [clewDisplayId]
     * @return: com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfClewForRecallModel
     */
    CfClewForRecallModel getCfClewForRecallModelByClewDisplayId(String clewDisplayId);

    /**
     * @author: wanghui
     * @time: 2019/10/16 4:27 PM
     * @description: updateIsHandledById  根据id  更新是否处理
     * @param: [ids]
     * @return: void
     */
    void updateIsHandledById(List<Integer> ids);

    /**
     * @author: wanghui
     * @time: 2019/10/16 4:27 PM
     * @description: getModelForSendByNoHandled  查询 未处理的记录 用于发消息
     * @param: [id, limit]
     * @return: java.util.List<com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfClewForRecallModel>
     */
    List<CfClewForRecallModel> getModelForSendByNoHandled(int id, int limit);

    /**
     * @author: wanghui
     * @time: 2019/10/16 4:27 PM
     * @description: batchUpdateClewDisplayIdWithEagleStatus  批量更新cf_clew_for_recall中 eagle_status display_id
     * @param: [cfClewForRecallModels]
     * @return: int
     */
    int batchUpdateClewDisplayIdWithEagleStatus(List<CfClewForRecallModel> cfClewForRecallModels);

    List<CfClewForRecallModel> getModelByClewPhone(String clewPhone);

    List<CfServiceStaffDO> getCfServiceStaffByShowTime(List<CfServiceStaffDO> cfServiceStaffDOS, Date yesterdayEndTime,Date currentDayStartTime);

    CustomerModel buildCustomerModel(CfServiceStaffDO cfServiceStaffDO);

    int updateServiceStaffInfo(CfServiceStaffDO cfServiceStaffDO);

    /**
     * 修改好友备注手机号后门
     * @param qyWechatUserId
     * @param externalUserid
     * @param phone
     * @return
     */
    OpResult<Long> modStaffFriendPhoneBackDoor(String qyWechatUserId, String externalUserid, String phone);

    int updateEncryptPhoneByIds(List<Integer> idList, String encryptPhone);

    int updateUnionid(List<Integer> ids, String unionid);

    List<String> getExternalUseridByQyWechatUserIdsWithPhone(List<String> qyWechatUserIds, String phone);

    int insert(CfWorkImGroupInfoDO cfWorkImGroupInfoDO);

    int updateToDelte(String chatId);

    int insert(CfWorkImGroupChangeRecordDO cfWorkImGroupChangeRecordDO);

    CfWorkImGroupChangeRecordDO queryByChatIdAndDateStr(String chatId, String dateStr);

    int updateChangeCount(long id, int changeIn, int changeOut);

    int updateToDelte(String userID, String externalUserID,int type);

    int updateExternalUserName(List<CfShuidichouWechatFriendDO> friendList, String externalUserName, String externalUserTags);

    int updateRemark(List<Integer> idList, String remark);

    int update(CfWorkImGroupInfoDO cfWorkImGroupInfoDO);

    void syncGroupInfo(String cursor, CfClewQyWxCorpDO cfClewQyWxCorpDO);

    void syncGroupUser(List<String> chatIds,String corpId);

    void fixGroupUser(List<String> chatIds, String corpId);

    void insertGroupInfoByChatId(List<String> chatIds, String corpId);

    void addDefaultTag(AddExternalContactModel model, CfClewQyWxCorpDO cfClewQyWxCorpDO);

    void saveFriendChannelInfo(CfShuidichouWechatFriendDO cfShuidichouWechatFriendDO);
}
