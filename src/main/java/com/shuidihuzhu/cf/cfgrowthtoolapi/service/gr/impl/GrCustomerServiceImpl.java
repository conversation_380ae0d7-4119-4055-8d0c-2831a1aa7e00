package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrCustomerDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.GrCustomerPageParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.GrCustomerService;
import com.shuidihuzhu.cf.dao.gr.GrCustomerDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * gr客户信息(GrCustomer)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-09 15:32:51
 */
@Service
public class GrCustomerServiceImpl implements GrCustomerService {

    @Resource
    private GrCustomerDao grCustomerDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public GrCustomerDO queryById(long id) {
        return grCustomerDao.queryById(id);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    @Override
    public List<GrCustomerDO> queryAllByLimit(int offset, int limit) {
        return grCustomerDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param grCustomer 实例对象
     * @return 实例对象
     */
    @Override
    public GrCustomerDO insert(GrCustomerDO grCustomer) {
        grCustomerDao.insert(grCustomer);
        return grCustomer;
    }

    /**
     * 修改数据
     *
     * @param grCustomer 实例对象
     */
    @Override
    public void update(GrCustomerDO grCustomer) {
        grCustomerDao.update(grCustomer);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return grCustomerDao.deleteById(id) > 0;
    }

    @Override
    public List<GrCustomerDO> queryAll(GrCustomerDO grCustomer) {
        if (grCustomer == null) {
            return Lists.newArrayList();
        }
        return grCustomerDao.queryAll(grCustomer);
    }

    @Override
    public List<GrCustomerDO> queryByName(String customerName, String city) {
        if (StringUtils.isBlank(customerName) || StringUtils.isBlank(city)) {
            return Lists.newArrayList();
        }
        return grCustomerDao.queryByName(customerName, city);
    }

    @Override
    public int countCustomer(GrCustomerPageParam customerPageParam) {
        if (customerPageParam == null) {
            return 0;
        }
        return grCustomerDao.countCustomer(customerPageParam);
    }

    @Override
    public List<GrCustomerDO> pageCustomer(GrCustomerPageParam customerPageParam) {
        if (customerPageParam == null) {
            return Lists.newArrayList();
        }
        return grCustomerDao.pageCustomer(customerPageParam);
    }

    @Override
    public List<GrCustomerDO> listAllImportCustomer() {
        return grCustomerDao.listAllImportCustomer();
    }

    @Override
    public void updateStatusChangeTime(int id, Date date) {
        if (date == null) {
            return;
        }
        grCustomerDao.updateStatusChangeTime(id, date);
    }

    @Override
    public void updateWhenAddRecord(int id, Date date, String purpose, String connectResult) {
        if (date == null) {
            return;
        }
        grCustomerDao.updateWhenAddRecord(id, date, purpose, connectResult);
    }

    @Override
    public void updateAddFeedbackTime(int id, Date date, Integer modifyConnectStatus) {
        if (date == null) {
            return;
        }
        grCustomerDao.updateAddFeedbackTime(id, date, modifyConnectStatus);
    }

    @Override
    public void updateBySea(int id, Integer priorityLevel, String uniqueCode, Integer connectStatus, String mis) {
        if (priorityLevel == null && StringUtils.isBlank(uniqueCode) && StringUtils.isBlank(mis)) {
            return;
        }
        grCustomerDao.updateBySea(id, priorityLevel, uniqueCode, connectStatus, mis);
    }

    @Override
    public GrCustomerDO getCustomerByCityAndHospital(String cityName, String hospital) {
        if (StringUtils.isBlank(cityName) || StringUtils.isBlank(hospital)) {
            return new GrCustomerDO();
        }
        return grCustomerDao.getCustomerByCityAndHospital(cityName, hospital);
    }

    @Override
    public void updateByDelete(GrCustomerDO grCustomer) {
        grCustomerDao.updateByDelete(grCustomer);
    }

    @Override
    public List<GrCustomerDO> getCustomerByIds(Set<Integer> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Lists.newArrayList();
        }
        return grCustomerDao.getCustomerByIds(customerIds);
    }

}