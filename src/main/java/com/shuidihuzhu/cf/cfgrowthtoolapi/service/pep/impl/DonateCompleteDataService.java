package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfKpiCaseMonthDonateData;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiCaseMonthDonateDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepRealTimeDonateModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 捐赠完成推送数据
 *
 * @author: fengxuan
 * @create 2023-03-20 19:25
 **/
@Slf4j
@Service
public class DonateCompleteDataService extends AbstractPushDataService {

    @Autowired
    private CfKpiCaseMonthDonateDataService cfKpiCaseMonthDonateDataService;


    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.donate_complete_data;
    }


    @Override
    protected List<PepRealTimeDonateModel> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        //找到对应的月份
        List<CfKpiCaseMonthDonateData> cfKpiCaseMonthDonateData = cfKpiCaseMonthDonateDataService.listDonateDataByDayKey(pushWhichDay.toString(GrowthtoolUtil.ymdfmt),
                new DateTime(lotInfo.getLotStartTime()).toString(GrowthtoolUtil.ymfmt));
        List<PepRealTimeDonateModel> result = Lists.newArrayList();
        for (CfKpiCaseMonthDonateData item : cfKpiCaseMonthDonateData) {
            //捐单小于等于0的不推送
            if (item.getDonateMonthCnt() <= 0) {
                continue;
            }
            if (StringUtils.isNotBlank(item.getAreaGl())) {
                PepRealTimeDonateModel pepRealTimeDonateModel = new PepRealTimeDonateModel();
                pepRealTimeDonateModel.setCase_id(item.getCaseId());
                pepRealTimeDonateModel.setLotId(lotInfo.getLotId());
                pepRealTimeDonateModel.setDonate_num(item.getDonateMonthCnt());
                pepRealTimeDonateModel.setUserId(item.getAreaGl());
                result.add(pepRealTimeDonateModel);
            }
            if (StringUtils.isNotBlank(item.getGroupGl())) {
                PepRealTimeDonateModel pepRealTimeDonateModel = new PepRealTimeDonateModel();
                pepRealTimeDonateModel.setCase_id(item.getCaseId());
                pepRealTimeDonateModel.setLotId(lotInfo.getLotId());
                pepRealTimeDonateModel.setDonate_num(item.getDonateMonthCnt());
                pepRealTimeDonateModel.setUserId(item.getGroupGl());
                result.add(pepRealTimeDonateModel);
            }
            if (StringUtils.isNotBlank(item.getRegionGl())) {
                PepRealTimeDonateModel pepRealTimeDonateModel = new PepRealTimeDonateModel();
                pepRealTimeDonateModel.setCase_id(item.getCaseId());
                pepRealTimeDonateModel.setLotId(lotInfo.getLotId());
                pepRealTimeDonateModel.setDonate_num(item.getDonateMonthCnt());
                pepRealTimeDonateModel.setUserId(item.getRegionGl());
                result.add(pepRealTimeDonateModel);
            }
        }
        return result;
    }

}
