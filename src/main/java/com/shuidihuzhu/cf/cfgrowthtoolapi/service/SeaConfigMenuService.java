package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRoleCarteConfigModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRoleConfigVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRoleJurisdictionConfigModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.SeaGroupVo;
import com.shuidihuzhu.cf.response.OpResult;
import io.swagger.models.Model;

import java.util.List;

public interface SeaConfigMenuService {


    List<CfRoleCarteConfigModel> getGroupList();


    OpResult<Long> saveOrUpdateGroup(CfRoleCarteConfigModel roleGroupVo);

    List<CfRoleCarteConfigModel> getCarteList(Long groupId);

    OpResult<Long> saveOrUpdateCarteInfo(CfRoleCarteConfigModel roleCarteModel);

    CfRoleConfigVo getRoleJurisdictionInfo(Integer level);

    OpResult<Long> updateRoleJurisdictionInfo(String[] carteIds,Integer level);
}
