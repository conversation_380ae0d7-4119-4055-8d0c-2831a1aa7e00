package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.PreVolunteerOrgInfoRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.bdcrm.IBdCrmPreposeReportFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmPermissionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.LeaderPermissionInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IBdCrmOrgAndCityRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCrmPermissionService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IClewPreproseMaterialService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.BdCrmContextUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.dao.bdcrm.BdCrmOrganizationDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmPermissionDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IWorkWeiXinDelegate;
import com.shuidihuzhu.cf.dao.bdcrm.PreVolunteerOrgInfoRelationDao;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: wanghui
 * @create: 2020/9/29 3:13 下午
 */
@Service
@Slf4j
public class CfBdCrmPermissionServiceImpl implements ICfBdCrmPermissionService {

    @Autowired
    private CfBdCrmPermissionDao cfBdCrmPermissionDao;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;

    @Autowired
    private IClewPreproseMaterialService materialService;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private IBdCrmOrgAndCityRelationService cityRelationService;

    @Autowired
    private BdCrmOrganizationDao bdCrmOrganizationDao;

    @Autowired
    private IBdCrmPreposeReportFacade bdCrmPreposeReportFacade;

    @Autowired
    private IWorkWeiXinDelegate workWeiXinDelegate;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private PreVolunteerOrgInfoRelationDao preVolunteerOrgInfoRelationDao;

    @Override
    public OpResult checkPermission(int level, String permission) {
        CfBdCrmPermissionDO permissionDo = cfBdCrmPermissionDao.getPermission(level, permission, CfBdCrmPermissionDO.TypeEnum.DEFAULT.getType());
        if (permissionDo==null){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.NO_ALLOW_OPERATE);
        }
        return OpResult.createSucResult();
    }

    @Override
    public OpResult checkTimeRange(int level, String startTime, String endTime) {
        CfBdCrmPermissionDO permissionDo = cfBdCrmPermissionDao.getTimeRangeLimit(level);
        if (permissionDo==null){
            return OpResult.createSucResult();
        }
        try {
            String msg = GrowthtoolUtil.checkTimeIsTooLong(BdCrmContextUtil.getLevel(), startTime, endTime,Integer.valueOf(permissionDo.getPermission()));
            if (StringUtils.isNotBlank(msg)){
                return OpResult.createFailResult(CfGrowthtoolErrorCode.TIME_TOO_LONG,msg);
            }
        }catch (Exception e){
            log.warn(this.getClass().getSimpleName()+" getTimeRangeLimit({},{})  返回结果不合法",level, CfBdCrmPermissionDO.TypeEnum.TIME_PERMISSION.getType());
        }
        return OpResult.createSucResult();
    }
    @Override
    public List<String> getDataPermissionsByRole(int roleCode){
        return cfBdCrmPermissionDao.getDataPermissionsByRole(roleCode);
    }

    @Override
    public List<Integer> getRoleListByPermissions(String permission) {
        return cfBdCrmPermissionDao.getRoleListByPermissions(permission);
    }

    @Override
    public boolean reportPermission(CrowdfundingVolunteer volunteer, ClewCrowdfundingReportRelation reportRelation) {
        if (reportRelation == null || volunteer == null) {
            log.error(this.getClass().getSimpleName()+"reportPermission error,reportRelation:{},volunteer:{}",reportRelation,volunteer);
            return false;
        }
        return checkUniqueCodePermission(volunteer, reportRelation.getUniqueCode());

    }

    @Override
    public boolean reportPermissionByReportId(CrowdfundingVolunteer volunteer, Long reportId) {
        PreposeMaterialModel.MaterialInfoVo completeDraft = bdCrmPreposeReportFacade.getCompleteDraft(reportId);
        ClewCrowdfundingReportRelation reportRelation = null;
        if (Objects.nonNull(completeDraft)){
            reportRelation = new ClewCrowdfundingReportRelation();
            reportRelation.setUniqueCode(completeDraft.getVolunteerUniqueCode());
        }
        return reportPermission(volunteer, reportRelation);
    }


    @Override
    public boolean casePermissionByInfoId(CrowdfundingVolunteer volunteer, String infoId) {
        CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoUuid(infoId);
        String uniqueCode = cfBdCaseInfoDo.getUniqueCode();
        return checkUniqueCodePermission(volunteer, uniqueCode);
    }


    /**
     * 检查志愿者是否有权限访问指定的 uniqueCode
     *
     * @param volunteer 志愿者信息
     * @param expectUniqueCode 期望访问的 uniqueCode
     * @return true: 有权限, false: 无权限
     */
    private boolean checkUniqueCodePermission(CrowdfundingVolunteer volunteer, String expectUniqueCode) {
        // 参数校验
        if (volunteer == null || StringUtils.isBlank(expectUniqueCode)) {
            log.warn("Invalid parameters - volunteer: {}, expectUniqueCode: {}", volunteer, expectUniqueCode);
            return false;
        }

        String tokenUniqueCode = volunteer.getUniqueCode();
        if (StringUtils.isBlank(tokenUniqueCode)) {
            log.warn("Invalid token uniqueCode for volunteer: {}", volunteer);
            return false;
        }

        // 检查是否是本人
        if (Objects.equals(tokenUniqueCode, expectUniqueCode)) {
            return true;
        }

        // 检查是否有领导权限
        LeaderPermissionInfo leaderPermission = crmMemberInfoService.getLeaderPermissionByVolunteer(volunteer);
        if (leaderPermission != null && leaderPermission.getPermissionUniqueCodes().contains(expectUniqueCode)) {
            return true;
        }

        // 获取被查询用户信息，检查是否是离职员工（兜底逻辑）
        CrowdfundingVolunteer beQueryVolunteer = cfVolunteerService.getByUniqueCode(expectUniqueCode);
        if (beQueryVolunteer != null && Objects.equals(beQueryVolunteer.getWorkStatus(), CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue())) {
            return true;
        }
        //如果是预绑定组织的员工，允许访问
        PreVolunteerOrgInfoRelationDO preVolunteerOrgInfoRelationDO = preVolunteerOrgInfoRelationDao.getPreVolunteerOrgInfoRelationDO(expectUniqueCode);
        if (preVolunteerOrgInfoRelationDO != null) {
            return true;
        }

        // 发送权限拒绝通知
        String content = String.format("权限拒绝通知\n志愿者: %s\n尝试访问: %s\n当前token: %s\n可查看组织: %s\n可查看人员: %s",
            volunteer, expectUniqueCode, tokenUniqueCode,
            leaderPermission != null ? leaderPermission.getPermissionOrgIds() : "无",
            leaderPermission != null ? leaderPermission.getPermissionUniqueCodes() : "无");
        workWeiXinDelegate.sendByUser("fengxuan", content);

        log.info("Permission denied - Volunteer: {}, UniqueCode: {}, TokenUniqueCode: {}, PermissionOrgIds: {}, PermissionUniqueCodes: {}",
                volunteer, expectUniqueCode, tokenUniqueCode,
                leaderPermission != null ? leaderPermission.getPermissionOrgIds() : "无",
                leaderPermission != null ? leaderPermission.getPermissionUniqueCodes() : "无");

        // 如果数据权限开关关闭，即使没有权限也放行
        if (!apolloService.getDataPermissionSwitch()) {
            log.info("Permission check is disabled by Apollo switch, proceeding with request despite permission check results");
            return true;
        }

        return false;
    }
}
