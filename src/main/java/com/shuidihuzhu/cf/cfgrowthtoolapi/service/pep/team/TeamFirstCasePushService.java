package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.team;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.KpiManagerDataTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.team.TeamCasePerformanceModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiManagerDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepTeamValidFristCaseModel;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队 hc 数据推送
 *
 * @author: fengxuan
 * @create 2023-03-20 19:25
 **/
@Slf4j
@Service("teamFirstCasePushService")
public class TeamFirstCasePushService extends AbstractPushDataService {

    @Autowired
    private CfKpiManagerDataService kpiManagerDataService;

    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.team_first_case_detail;
    }

    @Override
    protected List<PepTeamValidFristCaseModel> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        KpiManagerDataTypeEnum dataTypeEnum = KpiManagerDataTypeEnum.getByPepPushEnum(getPushEnum());
        //找到对应的月份
        List<TeamCasePerformanceModel> dataList = kpiManagerDataService.listByDayKeyAndType(pushWhichDay.toString(GrowthtoolUtil.ymdfmt), dataTypeEnum.getCode())
                .stream()
                .map(item -> JSON.parseObject(item.getContent(), TeamCasePerformanceModel.class))
                .collect(Collectors.toList());
        List<PepTeamValidFristCaseModel> result = Lists.newArrayList();
        for (TeamCasePerformanceModel data : dataList) {
            //过滤只查询首发有效案例
            if (data.getFirst_valid_tag() >= 1) {
                continue;
            }
            PepTeamValidFristCaseModel firstCaseModel = new PepTeamValidFristCaseModel();
            firstCaseModel.setUserId(data.getUnique_code());
            firstCaseModel.setBelong_unique_code(data.getBelong_unique_code());
            firstCaseModel.setCase_id(data.getCase_id());
            firstCaseModel.setDonate_num(data.getDonate_num());
            firstCaseModel.setCase_amount(data.getAmount());
            firstCaseModel.setLotId(lotInfo.getLotId());
            firstCaseModel.setFirst_valid_tag(data.getFirst_valid_tag());
            result.add(firstCaseModel);
        }
        return result;
    }

}
