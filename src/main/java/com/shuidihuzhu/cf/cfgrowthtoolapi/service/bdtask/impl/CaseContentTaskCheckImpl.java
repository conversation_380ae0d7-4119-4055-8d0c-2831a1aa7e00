package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCaseBaseDataDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.IBdTaskCheckService;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2023-09-20 15:59
 **/
@Service
public class CaseContentTaskCheckImpl implements IBdTaskCheckService {

    @Override
    public CrmBdTaskDO.TaskTypeEnum getTaskType() {
        return CrmBdTaskDO.TaskTypeEnum.case_content;
    }

    @Override
    public boolean checkNeedCreateTask(BdTaskContext bdTaskContext) {
        //文章<800字
        return bdTaskContext.getCrowdfundingInfo().getContent().replaceAll("\n", "").length() < 800;
    }

    @Override
    public boolean checkTaskComplete(BdTaskContext bdTaskContext) {
        return !checkNeedCreateTask(bdTaskContext);
    }

}
