package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.organization;


import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.OrgOptParam;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-05-09 16:52
 * 组织操作限制
 **/
public interface IOptOrgLimit {

    Response<Boolean> checkCanOptOrg(OrgOptParam orgOptParam);

    List<OrganizationMemberOptEnum> getNeedCheckOptEnums();

}
