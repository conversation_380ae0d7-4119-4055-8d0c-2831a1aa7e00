package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OrgValidCaseConfig;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseHospitalData;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CampaignMapTrendView;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.HospDataStatBaseVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OrgDataStatVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDataStatPageParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDataStatParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.CaseValidCommonSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CacheAdminValidCaseConfigService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CopyMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfHospInterviewCaseService;
import com.shuidihuzhu.cf.repository.CfCaseDayDataRepository;
import com.shuidihuzhu.cf.repository.CfHospitalInterviewCaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-07-03
 */
@Service
@Slf4j
public class CfHospInterviewCaseServiceImpl implements ICfHospInterviewCaseService {

    @Autowired
    private CfHospitalInterviewCaseRepository cfHospitalInterviewCaseRepository;

    @Autowired
    private CfCaseDayDataRepository cfCaseDayDataRepository;

    @Autowired
    private ApolloService apolloService;

    @Resource
    private CopyMapper copyMapper;

    @Autowired
    private CacheAdminValidCaseConfigService validCaseConfigService;


    @Override
    public List<OrgDataStatVO> getOrgOverviewData4Hosp(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList) {
        List<OrgDataStatVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orgIdList)){
            return result;
        }
        List<List<Long>> queryOrgIdList = Lists.partition(orgIdList,100);
        List<OrgDataStatVO> listDataFromDb = queryOrgIdList.parallelStream().map(
                list -> cfHospitalInterviewCaseRepository.getOrgOverviewData4Hosp(crmDataStatParam,list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
        result = OrgDataStatVO.reOrgDataByDateTime(result, listDataFromDb);
        return result;
    }

    @Override
    public List<OrgDataStatVO> getOrgDetailData4Person(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList) {
        List<OrgDataStatVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orgIdList)){
            return result;
        }
        return cfHospitalInterviewCaseRepository.getOrgDetailData4Person(crmDataStatParam,orgIdList);
    }

    @Override
    public List<OrgDataStatVO> getOrgDetailData4Org(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList) {
        List<OrgDataStatVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orgIdList)){
            return result;
        }
        List<List<Long>> queryOrgIdList = Lists.partition(orgIdList,100);
        result = queryOrgIdList.parallelStream().map(
                list -> cfHospitalInterviewCaseRepository.getOrgDetailData4Org(crmDataStatParam,list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
        return result;
    }

    @Override
    public List<HospDataStatBaseVO> getHospDetailData4Person(BdCrmDataStatParam crmDataStatParam) {
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        return cfHospitalInterviewCaseRepository.getHospDetailData4Person(crmDataStatParam,caseValidSearch);
    }

    @Override
    public List<HospDataStatBaseVO> getVisitSubHospData4Person(BdCrmDataStatParam crmDataStatParam) {
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        return cfHospitalInterviewCaseRepository.getVisitSubHospData4Person(crmDataStatParam,caseValidSearch);
    }

    @Override
    public List<HospDataStatBaseVO> getVisitHospData4Person(BdCrmDataStatParam crmDataStatParam) {
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        return cfHospitalInterviewCaseRepository.getVisitHospData4Person(crmDataStatParam,caseValidSearch);
    }

    @Override
    public List<HospDataStatBaseVO> getVisitData4PersonGroupByHospitalAndDate(BdCrmDataStatParam crmDataStatParam) {
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        return cfHospitalInterviewCaseRepository.getVisitData4PersonGroupByHospitalAndDate(crmDataStatParam,caseValidSearch);
    }

    @Override
    public long hospitalCount(BdCrmDataStatPageParam crmDataStatParam) {
        if (crmDataStatParam == null || CollectionUtils.isEmpty(crmDataStatParam.getOrgIdList())) {
            log.debug("hospitalCount传入参数不满足查询");
            return 0L;
        }
        return cfHospitalInterviewCaseRepository.distinctHospitalByVvhospitalCode(crmDataStatParam);
    }

    @Override
    public long hospitalAreaCount(BdCrmDataStatPageParam crmDataStatParam) {
        if (crmDataStatParam == null || CollectionUtils.isEmpty(crmDataStatParam.getOrgIdList())) {
            log.debug("hospitalCount传入参数不满足查询");
            return 0L;
        }
        return cfHospitalInterviewCaseRepository.distinctHospitalArea(crmDataStatParam);
    }

    @Override
    public List<HospDataStatBaseVO> findByOrgIdGroupByHospital(BdCrmDataStatPageParam crmDataStatParam) {
        if (crmDataStatParam == null || CollectionUtils.isEmpty(crmDataStatParam.getOrgIdList())) {
            log.debug("findByOrgIdGroupByHospital传入参数不满足查询");
            return Lists.newArrayList();
        }
        List<HospDataStatBaseVO> statList = Lists.newArrayList();
        List<HospDataStatBaseVO> vvhospcodeHosp = cfHospitalInterviewCaseRepository.listByOrgIdGroupByHospital(crmDataStatParam);
        List<String> vvhospcodeList = vvhospcodeHosp.stream().map(HospDataStatBaseVO::getVvhospitalCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vvhospcodeList)){
            return statList;
        }
        List<HospDataStatBaseVO> listDataFromDb = Lists.newArrayList();
        List<HospDataStatBaseVO> donateByVvHospital = Lists.newArrayList();
        List<HospDataStatBaseVO> listDistinctCount = Lists.newArrayList();

        try {
            CompletableFuture<List<HospDataStatBaseVO>> listDataFromDbFuture = CompletableFuture.supplyAsync(() -> {
                List<HospDataStatBaseVO> result = Lists.newArrayList();
                List<Long> orgIdList = crmDataStatParam.getOrgIdList();
                List<OrgValidCaseConfig> orgValidCaseConfigList = validCaseConfigService.partitionOrgIds(orgIdList);
                for (OrgValidCaseConfig orgValidCaseConfig : orgValidCaseConfigList) {
                    crmDataStatParam.setOrgIdList(orgValidCaseConfig.getOrgIds());
                    CaseValidCommonSearchParam caseValidSearch = orgValidCaseConfig.getSearchParam();
                    List<HospDataStatBaseVO> hospDataStatBaseVOS = cfHospitalInterviewCaseRepository.findByOrgIdGroupByHospital(crmDataStatParam, vvhospcodeList, caseValidSearch);
                    result.addAll(hospDataStatBaseVOS);
                }
                return result;
            });
            CompletableFuture<List<HospDataStatBaseVO>> donateByVvHospitalFuture = CompletableFuture.supplyAsync(() -> cfCaseDayDataRepository.listDonateGroupByVvHospital(crmDataStatParam, vvhospcodeList));
            CompletableFuture<List<HospDataStatBaseVO>> listDistinctCountFuture = CompletableFuture.supplyAsync(() -> cfHospitalInterviewCaseRepository.listDistinctCount(crmDataStatParam, vvhospcodeList));
            CompletableFuture.allOf(listDataFromDbFuture, donateByVvHospitalFuture, listDataFromDbFuture);
            listDataFromDb = listDataFromDbFuture.get();
            //医院对应的捐单数 只有vvhospitalCode和捐单数据两个属性
            donateByVvHospital = donateByVvHospitalFuture.get();
            listDistinctCount = listDistinctCountFuture.get();
        } catch (Exception e) {
            log.error("异步获取组织-医院数据异常", e);
        }

        List<HospDataStatBaseVO> hospDataStatBaseVOS = HospDataStatBaseVO.reOrgDistinctDataGroupByString(listDataFromDb, listDistinctCount, crmDataStatParam, donateByVvHospital, HospDataStatBaseVO::getVvhospitalCode);
        return fullOfflineCaseCountRateForVvHospitalCode(hospDataStatBaseVOS, crmDataStatParam, vvhospcodeList);
    }

    private List<HospDataStatBaseVO> fullOfflineCaseCountRateForVvHospitalCode(List<HospDataStatBaseVO> hospDataStatBaseVOS, BdCrmDataStatPageParam crmDataStatParam, List<String> vvhospcodeList) {
        Map<String, HospDataStatBaseVO> vvhospitalCodeMap = cfHospitalInterviewCaseRepository.findByGroupByHospital(crmDataStatParam, vvhospcodeList).stream().map(hospDataStatBaseVO -> {
            hospDataStatBaseVO.calcRatio();
            return hospDataStatBaseVO;
        }).collect(Collectors.toMap(HospDataStatBaseVO::getVvhospitalCode, Function.identity(), (oldObj, newObj) -> newObj));
        return hospDataStatBaseVOS.stream().map(item -> {
            item.setOfflineCaseCountRate(Optional.ofNullable(vvhospitalCodeMap.get(item.getVvhospitalCode())).map(HospDataStatBaseVO::getOfflineCaseCountRate).orElse(0D));
            return item;
        }).collect(Collectors.toList());
    }
    @Override
    public List<CfBdCrmDiagnoseHospitalData> findByCityIdListGroupByHospital(List<Long> cityIdList,
                                                                             List<String> dateTimes) {
        return cfHospitalInterviewCaseRepository.findByCityIdListGroupByHospital(cityIdList, dateTimes);
    }

    @Override
    public void setStatList(List<HospDataStatBaseVO> statList, List<String> vvhospcodeList, List<HospDataStatBaseVO> statDataFromDb) {
        Map<String,List<HospDataStatBaseVO>> statListMap = statDataFromDb.stream().collect(Collectors.groupingBy(HospDataStatBaseVO::getVvhospitalCode));
        for (String key : vvhospcodeList){
            List<HospDataStatBaseVO> statBaseList = statListMap.get(key);
            if (CollectionUtils.isEmpty(statBaseList)){
                continue;
            }
            HospDataStatBaseVO hospDataStatBaseVO;
            if (statBaseList.size() > 1){
                hospDataStatBaseVO = HospDataStatBaseVO.aggregatData(statBaseList);
            }else{
                hospDataStatBaseVO = statBaseList.get(0);
            }
            hospDataStatBaseVO.calcRatio();
            statList.add(hospDataStatBaseVO);
        }
    }

    @Override
    public List<HospDataStatBaseVO> findAreaByOrgIdGroupByHospital(BdCrmDataStatPageParam crmDataStatParam) {
        if (crmDataStatParam == null || CollectionUtils.isEmpty(crmDataStatParam.getOrgIdList())) {
            log.debug("findByOrgIdGroupByHospital传入参数不满足查询");
            return Lists.newArrayList();
        }

        List<HospDataStatBaseVO> listDataFromDb = Lists.newArrayList();
        List<HospDataStatBaseVO> listDistinctData = Lists.newArrayList();
        List<HospDataStatBaseVO> donateStatBase = Lists.newArrayList();
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        try {
            CompletableFuture<List<HospDataStatBaseVO>> listDataFromDbFuture = CompletableFuture.supplyAsync(() -> cfHospitalInterviewCaseRepository.findAreaByOrgIdGroupByHospital(crmDataStatParam,caseValidSearch));
            CompletableFuture<List<HospDataStatBaseVO>> listDistinctDataFuture = CompletableFuture.supplyAsync(() -> cfHospitalInterviewCaseRepository.listDistinctDataByVhospitalCode(crmDataStatParam));
            CompletableFuture<List<HospDataStatBaseVO>> donateStatBaseFuture = CompletableFuture.supplyAsync(() -> cfCaseDayDataRepository.listDonateGroupVHospital(crmDataStatParam));

            listDataFromDb = listDataFromDbFuture.get();
            listDistinctData = listDistinctDataFuture.get();
            donateStatBase = donateStatBaseFuture.get();
        } catch (Exception e) {
            log.error("异步获取组织-院区数据异常", e);
        }

        List<HospDataStatBaseVO> hospDataStatBaseVOS = HospDataStatBaseVO.reOrgDistinctDataGroupByString(listDataFromDb, listDistinctData, crmDataStatParam, donateStatBase, HospDataStatBaseVO::getVhospitalCode);
        return fullOfflineCaseCountRateForVHospitalCode(hospDataStatBaseVOS, crmDataStatParam);
    }

    private List<HospDataStatBaseVO> fullOfflineCaseCountRateForVHospitalCode(List<HospDataStatBaseVO> hospDataStatBaseVOS, BdCrmDataStatPageParam crmDataStatParam) {
        BdCrmDataStatPageParam crmDataStatParamNew = new BdCrmDataStatPageParam();

        crmDataStatParamNew = copyMapper.toBdCrmDataStatPageParam(crmDataStatParam);

        crmDataStatParamNew.setOrgIdList(null);
        crmDataStatParamNew.setHospitalName(null);
        crmDataStatParamNew.setTarget(null);
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        Map<String, List<HospDataStatBaseVO>> vhospitalCodeMap = cfHospitalInterviewCaseRepository.findByOrgIdGroupByHospital(crmDataStatParamNew,Lists.newArrayList(crmDataStatParam.getVvhospitalCode()),caseValidSearch).stream()
                .collect(Collectors.groupingBy(HospDataStatBaseVO::getVhospitalCode));
        return hospDataStatBaseVOS.stream().map(item -> {
            HospDataStatBaseVO hospDataStatBaseVO = HospDataStatBaseVO.aggregatData(Optional.ofNullable(vhospitalCodeMap.get(item.getVhospitalCode())).orElse(Lists.newArrayList()));
            hospDataStatBaseVO.calcRatio();
            item.setOfflineCaseCountRate(hospDataStatBaseVO.getOfflineCaseCountRate());
            return item;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CampaignMapTrendView> findByHospitalGroupByDate(BdCrmDataStatParam crmDataStatParam) {
        if (crmDataStatParam == null || StringUtils.isBlank(crmDataStatParam.getVvhospitalCode()) || CollectionUtils.isEmpty(crmDataStatParam.getOrgIdList())) {
            log.debug("findByHospitalGroupByDate传入的参数不满足查询");
            return Lists.newArrayList();
        }

        List<Long> orgIdList = crmDataStatParam.getOrgIdList();
        List<OrgValidCaseConfig> orgValidCaseConfigList = validCaseConfigService.partitionOrgIds(orgIdList);
        List<CampaignMapTrendView> listDataFromDb = Lists.newArrayList();
        for (OrgValidCaseConfig orgValidCaseConfig : orgValidCaseConfigList) {
            CaseValidCommonSearchParam caseValidSearch = orgValidCaseConfig.getSearchParam();
            List<List<Long>> queryOrgIds = Lists.partition(orgValidCaseConfig.getOrgIds(), 100);
            List<CampaignMapTrendView> campaignMapTrendViewList = queryOrgIds.parallelStream().map(list -> cfHospitalInterviewCaseRepository.findByHospitalGroupByDate(crmDataStatParam, list, caseValidSearch))
                    .reduce((total, value) -> {
                        total.addAll(value);
                        return total;
                    }).orElse(Lists.newArrayList());
            listDataFromDb.addAll(campaignMapTrendViewList);
        }

        // 单独计算  总发起量 和线下发起量 医院维度统计时 不和组织挂钩
        List<CampaignMapTrendView> caseCountByHospitalGroupByDate = cfHospitalInterviewCaseRepository.findCaseCountByHospitalGroupByDate(crmDataStatParam);
        return CampaignMapTrendView.reOrgDataByDateTime(listDataFromDb,crmDataStatParam,caseCountByHospitalGroupByDate);
    }

    @Override
    public List<HospDataStatBaseVO> findByHospitalGroupByVolunteer(BdCrmDataStatParam crmDataStatParam) {
        if (crmDataStatParam == null || StringUtils.isBlank(crmDataStatParam.getVvhospitalCode()) || CollectionUtils.isEmpty(crmDataStatParam.getOrgIdList())) {
            log.debug("findByHospitalGroupByVolunteer传入的参数不满足查询");
            return Lists.newArrayList();
        }
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        List<List<Long>> queryOrgIds = Lists.partition(crmDataStatParam.getOrgIdList(),100);
        List<HospDataStatBaseVO> listDataFromDb = queryOrgIds.parallelStream().map(list -> cfHospitalInterviewCaseRepository.findByHospitalGroupByVolunteer(crmDataStatParam,list,caseValidSearch))
                .reduce((total,value)->{
                    total.addAll(value);
                    return total;
                }).orElse(Lists.newArrayList());
        return HospDataStatBaseVO.reOrgDataGroupByString(listDataFromDb,null,HospDataStatBaseVO::getUniqueCode).stream()
                .filter(item -> item.getCoverDayCount()>0).collect(Collectors.toList());
    }

    @Override
    public List<HospDataStatBaseVO> findByUniqueCodeGroupByDate(BdCrmDataStatParam crmDataStatParam) {
        if (crmDataStatParam == null || StringUtils.isBlank(crmDataStatParam.getVvhospitalCode()) || StringUtils.isBlank(crmDataStatParam.getUniqueCode())
                || CollectionUtils.isEmpty(crmDataStatParam.getOrgIdList())) {
            log.debug("findByUniqueCodeGroupByDate传入的参数不满足查询");
            return Lists.newArrayList();
        }
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        List<List<Long>> queryOrgIds = Lists.partition(crmDataStatParam.getOrgIdList(),100);
        List<HospDataStatBaseVO> listDataFromDb = queryOrgIds.parallelStream().map(list -> cfHospitalInterviewCaseRepository.findByUniqueCodeGroupDate(crmDataStatParam,list,caseValidSearch))
                .reduce((total,value)->{
                    total.addAll(value);
                    return total;
                }).orElse(Lists.newArrayList());
        return HospDataStatBaseVO.reOrgDataGroupByDate(listDataFromDb,HospDataStatBaseVO::getInterviewTime).stream()
                .filter(item -> item.getCoverDayCount()>0 || item.getOfflineCaseCount()>0)
                .sorted(Comparator.comparing(HospDataStatBaseVO::getInterviewTime).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<OrgDataStatVO> getCityOverviewData4Hosp(BdCrmDataStatParam crmDataStatParam, List<Integer> cityIds) {
        List<OrgDataStatVO> resulut = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cityIds)){
            resulut = cfHospitalInterviewCaseRepository.getCityOverviewData4Hosp(crmDataStatParam,cityIds);
        }
        return resulut;
    }

    @Override
    public List<OrgDataStatVO> listHospDataGroupByCity(BdCrmDataStatParam crmDataStatParam, List<Integer> cityIds) {
        List<OrgDataStatVO> resulut = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cityIds)){
            resulut = cfHospitalInterviewCaseRepository.listHospDataGroupByCity(crmDataStatParam,cityIds);
        }
        return resulut;
    }

    @Override
    public List<HospDataStatBaseVO> listHospDataGroupByHosp(BdCrmDataStatPageParam crmDataStatParam, List<Integer> cityIds,List<String> vvhospCodeList) {
        List<HospDataStatBaseVO> resulut = Lists.newArrayList();
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        if (CollectionUtils.isNotEmpty(cityIds) && CollectionUtils.isNotEmpty(vvhospCodeList)){
            resulut = cfHospitalInterviewCaseRepository.listHospDataGroupByHosp(crmDataStatParam,cityIds,vvhospCodeList,caseValidSearch);
        }
        return resulut;
    }

    @Override
    public List<OrgDataStatVO> listHospDataGroupByOrg(BdCrmDataStatParam crmDataStatParam) {
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        return cfHospitalInterviewCaseRepository.listHospDataGroupByOrg(crmDataStatParam,caseValidSearch);
    }

    @Override
    public long hospitalCountByCity(BdCrmDataStatPageParam crmDataStatParam, List<Integer> cityIds) {
        return cfHospitalInterviewCaseRepository.distinctHospital(crmDataStatParam,cityIds);
    }

    @Override
    public long hospitalAreaCountByCityId(BdCrmDataStatPageParam crmDataStatParam, List<Integer> cityIds) {
        return cfHospitalInterviewCaseRepository.hospitalAreaCountByCityId(crmDataStatParam,cityIds);
    }

    @Override
    public List<HospDataStatBaseVO> listHospDataGroupByVvhospcode(BdCrmDataStatPageParam crmDataStatParam, List<Integer> cityIds) {
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        return cfHospitalInterviewCaseRepository.listHospDataGroupByVvhospcode(crmDataStatParam,cityIds,caseValidSearch);
    }

    @Override
    public List<HospDataStatBaseVO> listVvHospCodeListGroupByHosp(BdCrmDataStatPageParam crmDataStatParam, List<Integer> cityIds) {
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        return cfHospitalInterviewCaseRepository.listVvHospCodeListGroupByHosp(crmDataStatParam,cityIds,caseValidSearch);
    }
}
