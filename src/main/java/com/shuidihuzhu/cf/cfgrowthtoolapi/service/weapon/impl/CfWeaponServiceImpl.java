package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WeaponSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.ICfWeaponService;
import com.shuidihuzhu.cf.dao.weapon.CfWeaponDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-10-16 11:44 上午
 **/
@Slf4j
@Service
public class CfWeaponServiceImpl implements ICfWeaponService {

    @Autowired
    private CfWeaponDao cfWeaponDao;

    @Override
    public int insert(CfWeaponDO cfWeaponDO) {
        return cfWeaponDao.insert(cfWeaponDO);
    }

    @Override
    public int editCfWeapon(CfWeaponDO cfWeaponDO) {
        return cfWeaponDao.update(cfWeaponDO);
    }

    @Override
    public List<CfWeaponDO> listAllWeapon() {
        return cfWeaponDao.listAllCfWeapon();
    }

    @Override
    public List<CfWeaponDO> listAllWeapon(Integer tab) {
        List<CfWeaponDO> cfWeaponDOS = cfWeaponDao.listAllCfWeapon().stream()
                .filter(item -> WeaponEnums.WeaponUseStatusEnum.shangjia.getCode() == item.getUseStatus()).collect(Collectors.toList());
        if (Objects.isNull(tab)){
            return cfWeaponDOS;
        }
        return cfWeaponDOS.stream().filter(item -> {
            if (StringUtils.isNotBlank(item.getTabs())) {
                return Splitter.on(",").splitToList(item.getTabs())
                        .stream()
                        .anyMatch(weaponTab -> ObjectUtils.nullSafeEquals(weaponTab, String.valueOf(tab)));
            }
            return false;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CfWeaponDO> listWeaponByTabAndIds(Integer tab, Set<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        List<CfWeaponDO> cfWeaponDOS = cfWeaponDao.listByIds(Lists.newArrayList(ids))
                .stream()
                .filter(item -> WeaponEnums.WeaponUseStatusEnum.shangjia.getCode() == item.getUseStatus())
                .collect(Collectors.toList());
        if (Objects.isNull(tab)){
            return cfWeaponDOS;
        }
        return cfWeaponDOS.stream().filter(item -> {
            if (StringUtils.isNotBlank(item.getTabs())) {
                return Splitter.on(",").splitToList(item.getTabs())
                        .stream()
                        .anyMatch(weaponTab -> ObjectUtils.nullSafeEquals(weaponTab, String.valueOf(tab)));
            }
            return false;
        }).collect(Collectors.toList());
    }

    @Override
    public CfWeaponDO getById(int weaponId) {
        return cfWeaponDao.getById(weaponId);
    }

    @Override
    public CfWeaponDO getByActivityId(int activityId) {
        return cfWeaponDao.getByActivityId(activityId);
    }

    @Override
    public int handleWeapon(int weaponId, Integer useStatus, int isDelete, Date useTime) {
        return cfWeaponDao.handleWeapon(weaponId, useStatus, isDelete, useTime);
    }

    @Override
    public int countByParam(WeaponSearchParam queryParam) {
        return cfWeaponDao.countByParam(queryParam);
    }

    @Override
    public List<CfWeaponDO> queryCfWeaponDoListByParam(WeaponSearchParam queryParam) {
        if (queryParam == null) {
            return new ArrayList<>();
        }
        return cfWeaponDao.listByParam(queryParam);
    }

    @Override
    public int getWeaponIdByActivityId(int activityId) {
        return Optional.ofNullable(cfWeaponDao.getWeaponIdByActivityId(activityId)).orElse(0);
    }

    @Override
    public CfWeaponDO getByActivityTypeAndBudgetManagementId(int activityType, int budgetManagementId) {
        return cfWeaponDao.getByActivityTypeAndBudgetManagementId(activityType, budgetManagementId);
    }

    @Override
    public List<CfWeaponDO> listByActivityTypeAndBudgetManagementId(int activityType, List<Integer> budgetManagementIds) {
        return cfWeaponDao.listByActivityTypeAndBudgetManagementId(activityType, budgetManagementIds);
    }

}
