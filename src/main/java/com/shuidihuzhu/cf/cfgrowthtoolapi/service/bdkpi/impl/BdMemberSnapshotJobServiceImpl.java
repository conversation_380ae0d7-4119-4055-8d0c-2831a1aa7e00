package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseTimeDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.CrowdfundingVolunteerUtil;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-08-03 4:36 下午
 **/
@Slf4j
@Service
public class BdMemberSnapshotJobServiceImpl implements IBdMemberSnapshotJobService {

    @Autowired
    private IBdMemberSnapshotService bdMemberSnapshotService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Resource(name = "selfBuiltOrgForSea")
    private ICrmSelfBuiltOrgReadService orgReadDbService;

    @Autowired
    private CrmOrganizationRelationServiceImpl crmOrganizationRelationService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ICfKpiCaseTimeService cfKpiCaseTimeService;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;

    @Autowired
    private MemberSnapshotHandlerService memberSnapshotHandlerService;

    @Override
    public void initData() {
        List<CfCrmMemberSnapshotDO> snapshotDOList = getAllMemberSnapshotInfo();
        bdMemberSnapshotService.addBatch(snapshotDOList);
    }


    @Override
    public List<CfCrmMemberSnapshotDO> getAllMemberSnapshotInfo() {
        //获取所有的城市
        List<BdCrmOrganizationDO> allOrg = crmSelfBuiltOrgReadService.getAllOrg();
        List<BdCrmOrganizationDO> leafOrgs = allOrg.stream()
                .filter(item -> item.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode())
                .collect(Collectors.toList());

        List<CfCrmMemberSnapshotDO> snapshotDOList = Lists.newArrayList();
        //获取当前所有的人员
        for (BdCrmOrganizationDO leafOrg : leafOrgs) {
            List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = crmOrganizationRelationService.listRelationByOrgId(leafOrg.getId());
            //构建顾问快照信息
            for (BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO : bdCrmOrgUserRelationDOS) {
                CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(bdCrmOrgUserRelationDO.getUniqueCode());
                CfCrmMemberSnapshotDO memberSnapshotWithInitParam = createMemberSnapshotWithInitParam(bdCrmOrgUserRelationDO, volunteer, true);
                if (memberSnapshotWithInitParam != null) {
                    snapshotDOList.add(memberSnapshotWithInitParam);
                }
            }
        }
        List<String> uniqueCodeList = snapshotDOList.stream().map(CfCrmMemberSnapshotDO::getUniqueCode).collect(Collectors.toList());
        //除运营和超管外的其他管理人员
        Map<String, List<BdCrmOrgUserRelationDO>> allValidRelationMap = crmOrganizationRelationService.getAllValidRelation().stream().collect(Collectors.groupingBy(BdCrmOrgUserRelationDO::getUniqueCode));
        for (Map.Entry<String, List<BdCrmOrgUserRelationDO>> entry : allValidRelationMap.entrySet()) {
            if (uniqueCodeList.contains(entry.getKey())) {
                continue;
            }

            CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(entry.getKey());
            BdCrmOrgUserRelationDO shortestRelation = null;
            //高级渠道经理需要同步
            if (volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getLevel()) {
                shortestRelation = crmMemberInfoService.findShortestPathForDelegateKpi(entry.getKey());
            } else {
                shortestRelation = crmMemberInfoService.findShortestPathForKpi(entry.getKey());
            }
            if (Objects.isNull(shortestRelation)) {
                continue;
            }
            CfCrmMemberSnapshotDO memberSnapshotWithInitParam = createMemberSnapshotWithInitParam(shortestRelation, volunteer,true);
            if (memberSnapshotWithInitParam != null) {
                snapshotDOList.add(memberSnapshotWithInitParam);
            }
        }
        return snapshotDOList;
    }


    @Override
    public CfCrmMemberSnapshotDO createMemberSnapshot(CrowdfundingVolunteer volunteer) {
        BdCrmOrgUserRelationDO shortestRelation = crmMemberInfoService.findShortestPathForKpi(volunteer.getUniqueCode());
        if (Objects.isNull(shortestRelation)){
            return null;
        }
        return createMemberSnapshotWithInitParam(shortestRelation, volunteer, false);
    }

    @Override
    public CfCrmMemberSnapshotDO createMemberSnapshotV2(CrowdfundingVolunteer volunteer) {
        BdCrmOrgUserRelationDO shortestRelation = crmMemberInfoService.findShortestPathForKpi(volunteer.getUniqueCode());
        if (Objects.isNull(shortestRelation)){
            return null;
        }
        return createMemberSnapshotWithInitParam(shortestRelation, volunteer, true);
    }


    @Override
    public CfCrmMemberSnapshotDO buildOnlyShowMemberSnapshot(BdCrmOrgUserRelationDO relationDO, CrowdfundingVolunteer volunteer) {
        //添加月份时间
        String dateKey = DateTime.now().toString("yyyy-MM");
        CfCrmMemberSnapshotDO snapshotDO = null;
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(volunteer.getLevel());
        if (Objects.isNull(roleEnum)){
            return null;
        }
        CrowdfundingVolunteerEnum.KpiMemberSnapshotStatusEnum kpiMemberSnapshotStatusEnum = roleEnum.getKpiMemberSnapshotStatusEnum();
        switch (kpiMemberSnapshotStatusEnum) {
            case NUM0:
            case NUM2:
                snapshotDO = new CfCrmMemberSnapshotDO();
                break;
            case NUM1:
            default:
                //默认值 + NUM1 无需入库
                break;
        }
        if (Objects.isNull(snapshotDO)){
            return null;
        }
        //设置兼职信息,为以后排除兼职人员留个口子
        snapshotDO.setPartnerTag(volunteer.getPartnerTag());
        //查找对应的组织
        BdCrmOrganizationDO currentOrg = crmSelfBuiltOrgReadService.getCurrentOrgById(relationDO.getOrgId());
        snapshotDO.setDateKey(dateKey);
        snapshotDO.setUniqueCode(relationDO.getUniqueCode());
        snapshotDO.setVolunteerName(relationDO.getMisName());
        snapshotDO.setLevel(roleEnum.getLevel());
        snapshotDO.setNewStaff(0);
        snapshotDO.setWorkStatus(volunteer.getWorkStatus());
        snapshotDO.setLeaveTime(volunteer.getLeaveTime());
        //顾问只有一个路径
        snapshotDO.setIsDelete(2);
        memberSnapshotHandlerService.setOrgRelation(relationDO, snapshotDO, currentOrg, roleEnum.getLevel());
        return snapshotDO;
    }



    private CfCrmMemberSnapshotDO createMemberSnapshotWithInitParam(BdCrmOrgUserRelationDO relationDO, CrowdfundingVolunteer volunteer, boolean initData) {
        if (volunteer == null) {
            return null;
        }
        //添加月份时间
        String dateKey = DateTime.now().toString("yyyy-MM");
        CfKpiCaseTimeDO caseTimeDO = cfKpiCaseTimeService.getCfKpiCaseTimeByMonthkey(dateKey);
        boolean newStaff = false;
        if (caseTimeDO == null) {
            log.info("无当月的cf_kpi_case_time数据,且入职时间为:{}", volunteer.getEntryTime());
            return null;
        }

        newStaff = CfCrmMemberSnapshotDO.newStaffOrNot(volunteer, caseTimeDO, initData);
        //查找对应的组织
        BdCrmOrganizationDO currentOrg = orgReadDbService.getCurrentOrgById(relationDO.getOrgId());
        CfCrmMemberSnapshotDO snapshotDO = null;
        int volunteerLevel = volunteer.getLevel();
        if (currentOrg == null) {
            log.info("找不到对应的组织信息relationDO:{}", relationDO);
        }
        //如果是新员工或者初始化,需要加入
        if (newStaff || initData) {
            snapshotDO = new CfCrmMemberSnapshotDO();
            snapshotDO.setDateKey(dateKey);
            snapshotDO.setUniqueCode(relationDO.getUniqueCode());
            snapshotDO.setVolunteerName(relationDO.getMisName());
            snapshotDO.setLevel(volunteerLevel);
            snapshotDO.setNewStaff(newStaff ? 1 : 0);
            snapshotDO.setWorkStatus(volunteer.getWorkStatus());
            snapshotDO.setLeaveTime(initData ? null : volunteer.getLeaveTime());
            //顾问只有一个路径
            memberSnapshotHandlerService.setOrgRelation(relationDO, snapshotDO, currentOrg, volunteerLevel);
        }

        if (Objects.isNull(snapshotDO)){
            return null;
        }
        //设置兼职信息,未以后排除兼职人员留个口子
        if (Objects.equals(volunteer.getPartnerTag(), CrowdfundingVolunteerEnum.PartnerTagEnum.partner.getCode())) {
            snapshotDO.setPartnerTag(volunteer.getPartnerTag());
        } else {
            snapshotDO.setPartnerTag(volunteer.getPartnerTag());
        }
        //如果新加入的是大区经理，is_delete设置为2
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(volunteerLevel);
        if (Objects.isNull(roleEnum)){
            snapshotDO.setIsDelete(2);
            return snapshotDO;
        }
        CrowdfundingVolunteerEnum.KpiMemberSnapshotStatusEnum kpiMemberSnapshotStatusEnum = roleEnum.getKpiMemberSnapshotStatusEnum();
        switch (kpiMemberSnapshotStatusEnum) {
            case NUM0:
                snapshotDO.setIsDelete(0);
                break;
            case NUM1:
                snapshotDO = null;
                break;
            case NUM2:
                snapshotDO.setIsDelete(2);
                break;
            default:
                //默认值不入库
                snapshotDO.setIsDelete(2);
                break;
        }
        return snapshotDO;
    }


}
