package com.shuidihuzhu.cf.cfgrowthtoolapi.service;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.QywxSdkDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfShuidichouWechatFriendDO;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSimpleTrueOrFalseEnum;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewQyWxCorpDO;
import com.shuidihuzhu.client.cf.growthtool.model.GroupTagResponseModel;
import com.shuidihuzhu.client.cf.growthtool.model.MarkTagModel;
import com.shuidihuzhu.client.cf.growthtool.model.TagGroupModel;
import com.shuidihuzhu.client.cf.growthtool.model.TagModel;
import com.shuidihuzhu.client.cf.qywechat.QywxSdkClient;
import com.shuidihuzhu.client.grpc.account.v1.feign.UserThirdServiceClient;
import com.shuidihuzhu.mdc.client.model.MdcQyWechatFriendModel;
import com.shuidihuzhu.wx.grpc.enums.EventType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PatientSheQunTagServiceImpl implements IPatientSheQunTagService {

    @Autowired
    private QywxSdkClient qywxSdkClient;

    @Autowired
    private QywxSdkDelegate qywxSdkDelegate;

    @Autowired
    private UserThirdServiceClient userThirdServiceClient;

    @Autowired
    private ApolloService apolloService;

    private static final String corpId = "ww7e123ba6d95a5ffc";
    private static final String appSecretKey = "BfZr_ZqpxhZDfy3MDHHPA-nA5ij-KhPsPFLLzo3sR7U";

    @Override
    public String addOrRemoveTag(MarkTagModel markTagModel) {
        //新增tag
        String markTagResponse = "";
        if (CollectionUtils.isNotEmpty(markTagModel.getAdd_tag()) || CollectionUtils.isNotEmpty(markTagModel.getRemove_tag())) {
            String accessToken = qywxSdkDelegate.getAccessToken(CfSimpleTrueOrFalseEnum.FALSE, corpId, appSecretKey);
            markTagResponse = qywxSdkClient.markTag(accessToken, JSON.toJSONString(markTagModel));
            log.info("markTag request:{},response:{}", JSON.toJSONString(markTagModel), markTagResponse);
        }
        return markTagResponse;
    }

    @Override
    public String getOpenId(long userId, int userThirdType) {
        UserThirdModel userThirdModel = userThirdServiceClient.getThirdModelWithUserId(userId, userThirdType);
        if (Objects.isNull(userThirdModel)) {
            return "";
        }
        return userThirdModel.getOpenId();
    }

    @Override
    public MarkTagModel buildMarkTagModel(MdcQyWechatFriendModel mdcQyWechatFriendModel) {
        MarkTagModel markTagModel = new MarkTagModel();
        markTagModel.setUserid(mdcQyWechatFriendModel.getQyWechatUserId());
        markTagModel.setExternal_userid(mdcQyWechatFriendModel.getExternalUserId());
        List<String> addTagIdList = Lists.newArrayList();
        markTagModel.setAdd_tag(addTagIdList);
        List<String> removeTagIdList = Lists.newArrayList();
        markTagModel.setRemove_tag(removeTagIdList);
        return markTagModel;
    }

    @Override
    public MarkTagModel handleMarkTagModel(MarkTagModel markTagModel, Boolean miaoYiSubscribeFlag) {
        if (miaoYiSubscribeFlag) {
            if (GeneralConstant.tagNameMapId.get("妙医关注中") != null) {
                //新增关注
                markTagModel.getAdd_tag().add(GeneralConstant.tagNameMapId.get("妙医关注中"));
            }
            if (GeneralConstant.tagNameMapId.get("妙医未关注") != null) {
                //删除未关注
                markTagModel.getRemove_tag().add(GeneralConstant.tagNameMapId.get("妙医未关注"));
            }
        }
        if (!miaoYiSubscribeFlag) {
            //取关
            if (GeneralConstant.tagNameMapId.get("妙医未关注") != null) {
                //新增未关注
                markTagModel.getAdd_tag().add(GeneralConstant.tagNameMapId.get("妙医未关注"));
            }
            if (GeneralConstant.tagNameMapId.get("妙医关注中") != null) {
                //删除关注
                markTagModel.getRemove_tag().add(GeneralConstant.tagNameMapId.get("妙医关注中"));
            }
        }
        return markTagModel;
    }


    public List<TagGroupModel> getAllGroupList(CfClewQyWxCorpDO cfClewWxCorpMsgDO) {
        String accessToken;
        if (cfClewWxCorpMsgDO == null) {
            accessToken = qywxSdkDelegate.getToken(CfSimpleTrueOrFalseEnum.FALSE);
        } else {
            accessToken = qywxSdkDelegate.getAccessToken(CfSimpleTrueOrFalseEnum.FALSE, cfClewWxCorpMsgDO.getCorpId(), cfClewWxCorpMsgDO.getAppSecret());
        }
        String responseCorpTag = qywxSdkClient.getCorpTagList(accessToken, "{}");
        log.info("getCorpTagList request:{},response:{}", accessToken, responseCorpTag);
        if (StringUtils.isBlank(responseCorpTag)) {
            return Lists.newArrayList();
        }
        GroupTagResponseModel groupTagResponseModel = JSON.parseObject(responseCorpTag, GroupTagResponseModel.class);
        if (groupTagResponseModel == null || groupTagResponseModel.getTag_group() == null) {
            return Lists.newArrayList();
        }
        return groupTagResponseModel.getTag_group();
    }

    @Override
    public String handleMarkTagModelByGroupName(String groupName, String tagName, CfClewQyWxCorpDO cfClewWxCorpMsgDO, String userID, String externalUserID) {
        //获取当前企业主体下，所有标签集合
        List<TagGroupModel> tagGroupModelList = getAllGroupList(cfClewWxCorpMsgDO);
        //以通过后门初始化过标签组,所以一定有该标签组 + 标签值
        List<TagGroupModel> groupNameList = tagGroupModelList.stream().filter(item -> item.getGroup_name().equals(groupName)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupNameList)) {
            log.warn("handleMarkTagModelByGroupName_tagList_is_null:{}", groupName);
            return null;
        }
        MarkTagModel markTagModel = new MarkTagModel();
        markTagModel.setUserid(userID);
        markTagModel.setExternal_userid(externalUserID);
        //删除其余标签,打上当前标签（仅适用于正反类型的标签场景）
        List<TagModel> tagModels = groupNameList.get(0).getTag();
        markTagModel.setAdd_tag(tagModels.stream().filter(item -> item.getName().equals(tagName)).map(TagModel::getId).collect(Collectors.toList()));
        markTagModel.setRemove_tag(tagModels.stream().filter(item -> !item.getName().equals(tagName)).map(TagModel::getId).collect(Collectors.toList()));
        //新增标签
        log.info("handleMarkTagModelByGroupName_param:{}", JSON.toJSONString(markTagModel));
        return addOrRemoveTagByCorp(markTagModel,cfClewWxCorpMsgDO);
    }


    @Override
    public String addOrRemoveTagByCorp(MarkTagModel markTagModel, CfClewQyWxCorpDO cfClewWxCorpMsgDO) {
        String accessToken;
        if (cfClewWxCorpMsgDO == null) {
            accessToken = qywxSdkDelegate.getToken(CfSimpleTrueOrFalseEnum.FALSE);
        } else {
            accessToken = qywxSdkDelegate.getAccessToken(CfSimpleTrueOrFalseEnum.FALSE, cfClewWxCorpMsgDO.getCorpId(), cfClewWxCorpMsgDO.getAppSecret());
        }
        String markTagResponse = qywxSdkClient.markTag(accessToken, JSON.toJSONString(markTagModel));
        log.info("markTag request:{},response:{}", JSON.toJSONString(markTagModel), markTagResponse);
        return markTagResponse;
    }

}
