package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseScoreDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiCaseScoreVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCaseScoreService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiCaseScoreDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
@Service
@Slf4j
public class CfKpiCaseScoreServiceImpl implements ICfKpiCaseScoreService {

    @Autowired
    private CfKpiCaseScoreDao cfKpiCaseScoreDao;

    @Override
    public OpResult<List<CfKpiCaseScoreVO>> queryCaseScoreDetail(String monthkey, String uniqueCode) {
        if (StringUtils.isEmpty(monthkey)){
            monthkey = DateUtil.getYYYYMMStr(DateUtil.getCurrentDate());
        }
        List<CfKpiCaseScoreVO> list = cfKpiCaseScoreDao.queryCaseScoreDetail(monthkey,uniqueCode);
        return OpResult.createSucResult(list);
    }

    @Override
    public int batchSaveOrUpdate(List<CfKpiCaseScoreDO> kpiCaseScoreList, String monthKey, String uniqueCode) {
        List<CfKpiCaseScoreDO> caseScoreListFromDb = cfKpiCaseScoreDao.queryCaseScoreList(monthKey,uniqueCode);
        Map<Long,CfKpiCaseScoreDO> caseScoreMap = caseScoreListFromDb.stream().collect(Collectors.toMap(CfKpiCaseScoreDO::getCaseId, Function.identity()));
        Map<Long,CfKpiCaseScoreDO> inputCaseSocreMap = kpiCaseScoreList.stream().collect(Collectors.toMap(CfKpiCaseScoreDO::getCaseId, Function.identity()));
        List<CfKpiCaseScoreDO> insertList = Lists.newArrayList();
        List<CfKpiCaseScoreDO> updateList = Lists.newArrayList();
        List<CfKpiCaseScoreDO> delList = Lists.newArrayList();
        for (CfKpiCaseScoreDO caseScoreDO : kpiCaseScoreList){
            CfKpiCaseScoreDO singleDataFromDb = caseScoreMap.get(caseScoreDO.getCaseId());
            if (Objects.isNull(singleDataFromDb)){
                insertList.add(caseScoreDO);
            }else{
                caseScoreDO.setId(singleDataFromDb.getId());
                updateList.add(caseScoreDO);
            }
        }

        for (CfKpiCaseScoreDO cfKpiCaseScoreDO: caseScoreListFromDb){
            if (Objects.isNull(inputCaseSocreMap.get(cfKpiCaseScoreDO.getCaseId()))){
                delList.add(cfKpiCaseScoreDO);
            }
        }

        if (CollectionUtils.isNotEmpty(insertList)){
            List<List<CfKpiCaseScoreDO>> listList = Lists.partition(insertList, GeneralConstant.MAX_PAGE_SIZE);
            listList.forEach(list -> cfKpiCaseScoreDao.batchInsert(list));
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            List<List<CfKpiCaseScoreDO>> listList = Lists.partition(updateList, GeneralConstant.MAX_PAGE_SIZE);
            listList.forEach(list -> cfKpiCaseScoreDao.batchUpdate(list));
        }
        if (CollectionUtils.isNotEmpty(delList)){
            List<List<CfKpiCaseScoreDO>> listList = Lists.partition(delList, GeneralConstant.MAX_PAGE_SIZE);
            listList.forEach(list -> cfKpiCaseScoreDao.batchUpdateDelete(list));
        }
        return 0;
    }

    @Override
    public OpResult<List<CfKpiCaseScoreVO>> queryCaseScoreWhaleDetail(String monthKey, String uniqueCode, long validAmount, int validDonateNum) {
        List<CfKpiCaseScoreVO> list = cfKpiCaseScoreDao.queryCaseScoreWhaleDetail(monthKey, uniqueCode, validAmount, validDonateNum);
        return OpResult.createSucResult(list);
    }

    @Override
    public int batchDeleteByUniqueCode(String uniqueCode, String monthKey) {
        if (StringUtils.isBlank(uniqueCode) || StringUtils.isBlank(monthKey)) {
            return 0;
        }
        return cfKpiCaseScoreDao.batchDeleteByUniqueCode(uniqueCode, monthKey);
    }
}
