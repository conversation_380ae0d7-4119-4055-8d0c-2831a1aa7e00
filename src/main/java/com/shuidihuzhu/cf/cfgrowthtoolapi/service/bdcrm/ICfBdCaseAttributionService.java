package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCaseAttributionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-04-02 17:55
 */
public interface ICfBdCaseAttributionService {

	/**
	 * @param cfInfoExt
	 * @param bindPreposeMaterialResult  left 为报备的手机号   right为ClewCrowdfundingReportRelation对象
	 */
	void saveCfBdCaseAttribution(CfInfoExt cfInfoExt, ImmutablePair<String, ClewCrowdfundingReportRelation> bindPreposeMaterialResult, long userId);

	@Async
	void saveCfBdCaseAttribution(ImmutablePair<String, ClewCrowdfundingReportRelation> bindPreposeMaterialResult);

	CfBdCaseAttributionDO getCfBdCaseAttributionDOByCaseId(long caseId);

	List<CfBdCaseAttributionDO> listByCaseIds(List<Long> caseIds);
}
