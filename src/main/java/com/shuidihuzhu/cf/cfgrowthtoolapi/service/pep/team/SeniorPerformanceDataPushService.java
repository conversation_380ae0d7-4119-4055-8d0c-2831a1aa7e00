package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.team;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.KpiManagerDataTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.team.CfKpiSeniorPerformanceModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiManagerDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepPersonalEfficiencyModel;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 人效-人数
 *
 * @author: fengxuan
 * @create 2023-03-20 19:25
 **/
@Slf4j
@Service("seniorPerformanceDataPushService")
public class SeniorPerformanceDataPushService extends AbstractPushDataService {

    @Autowired
    private CfKpiManagerDataService kpiManagerDataService;


    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.senior_performance;
    }

    @Override
    protected List<PepPersonalEfficiencyModel> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        KpiManagerDataTypeEnum dataTypeEnum = KpiManagerDataTypeEnum.getByPepPushEnum(getPushEnum());
        //找到对应的月份
        List<CfKpiSeniorPerformanceModel> seniorPerformanceDOList = kpiManagerDataService.listByDayKeyAndType(pushWhichDay.toString(GrowthtoolUtil.ymdfmt), dataTypeEnum.getCode())
                .stream()
                .map(item -> JSON.parseObject(item.getContent(), CfKpiSeniorPerformanceModel.class))
                .collect(Collectors.toList());
        //找到对应的月份
        List<PepPersonalEfficiencyModel> result = Lists.newArrayList();
        for (CfKpiSeniorPerformanceModel kpiSeniorPerformanceDO : seniorPerformanceDOList) {
            PepPersonalEfficiencyModel personalEfficiencyModel = new PepPersonalEfficiencyModel();
            personalEfficiencyModel.setUserId(kpiSeniorPerformanceDO.getUnique_code());
            personalEfficiencyModel.setDonate_num(kpiSeniorPerformanceDO.getDonate_num());
            personalEfficiencyModel.setTeam_member_num(Double.parseDouble(kpiSeniorPerformanceDO.getTeam_member_num()));
            personalEfficiencyModel.setMonth_key(kpiSeniorPerformanceDO.getMonth_key());
            personalEfficiencyModel.setLotId(lotInfo.getLotId());
            result.add(personalEfficiencyModel);
        }
        return result;
    }

}
