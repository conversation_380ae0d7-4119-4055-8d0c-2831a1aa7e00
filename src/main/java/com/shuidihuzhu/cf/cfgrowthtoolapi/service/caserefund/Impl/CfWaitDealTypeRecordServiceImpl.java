package com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund.Impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.caserefund.CfWaitDealTypeRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.caserefund.CfWaitDealResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.caserefund.CfWaitTaskDealModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.caserefund.CfCaseRefundDetailParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund.CfWaitDealTypeRecordService;
import com.shuidihuzhu.cf.dao.caserefund.CfWaitDealTypeRecordDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/8/22 11:27 AM
 */
@Service("cfWaitDealTypeRecordService")
public class CfWaitDealTypeRecordServiceImpl implements CfWaitDealTypeRecordService {

    @Resource
    private CfWaitDealTypeRecordDao cfWaitDealTypeRecordDao;

    @Override
    public int insert(CfWaitDealTypeRecordDO cfWaitDealTypeRecordDO) {
        return cfWaitDealTypeRecordDao.insert(cfWaitDealTypeRecordDO);
    }

    @Override
    public void updateDealStatus(Long id, Integer dealStatus, Integer dealResult) {
        cfWaitDealTypeRecordDao.updateDealStatus(id, dealStatus, dealResult);
    }

    @Override
    public CfWaitDealTypeRecordDO getWaitDealTypeById(Long id) {
        return cfWaitDealTypeRecordDao.getWaitDealTypeById(id);
    }

    @Override
    public List<CfWaitDealTypeRecordDO> getWaitDealTypeByParams(CfCaseRefundDetailParam cfCaseRefundDetailParam) {
        return cfWaitDealTypeRecordDao.getWaitDealTypeByParams(cfCaseRefundDetailParam);
    }

    @Override
    public Integer getWaitDealTypeByParamsCount(CfCaseRefundDetailParam cfCaseRefundDetailParam) {
        return cfWaitDealTypeRecordDao.getWaitDealTypeByParamsCount(cfCaseRefundDetailParam);
    }

    @Override
    public CfWaitDealResultModel getWaitDealTypeCountByParams(CfCaseRefundDetailParam param) {
        return cfWaitDealTypeRecordDao.getWaitDealTypeCountByParams(param);
    }

    @Override
    public int getWaitDealCount(String uniqueCode) {
        if (StringUtils.isEmpty(uniqueCode)) {
            return 0;
        }
        return cfWaitDealTypeRecordDao.getWaitDealCount(uniqueCode);
    }

    @Override
    public CfWaitTaskDealModel getWaitDealTaskByOrgIds(String startTime, String endTime, Integer dealResult, List<Long> orgIdList) {
        if (StringUtils.isAnyEmpty(startTime, endTime) || CollectionUtils.isEmpty(orgIdList)) {
            return new CfWaitTaskDealModel();
        }
        return Optional.ofNullable(cfWaitDealTypeRecordDao.getWaitDealTaskByOrgIds(startTime, endTime, dealResult, orgIdList)).orElse(new CfWaitTaskDealModel());
    }

    @Override
    public List<CfWaitTaskDealModel> groupByUniqueCode(long orgId, Integer dealResult, String startTime, String endTime) {
        if (StringUtils.isAnyEmpty(startTime, endTime) || orgId <= 0) {
            return Lists.newArrayList();
        }
        return cfWaitDealTypeRecordDao.groupByUniqueCode(orgId, dealResult, startTime, endTime);
    }

    @Override
    public CfWaitTaskDealModel getTaskModelByUniqueCode(String startTime, String endTime, String uniqueCode) {
        if (StringUtils.isAnyEmpty(startTime, endTime, uniqueCode)) {
            return new CfWaitTaskDealModel();
        }
        return cfWaitDealTypeRecordDao.getTaskModelByUniqueCode(startTime, endTime, uniqueCode);
    }

    @Override
    public CfWaitTaskDealModel getTaskModelByOrgIds(String startTime, String endTime, List<Long> allSubOrgIds) {
        if (StringUtils.isAnyEmpty(startTime, endTime) || CollectionUtils.isEmpty(allSubOrgIds)) {
            return new CfWaitTaskDealModel();
        }
        return cfWaitDealTypeRecordDao.getTaskModelByOrgIds(startTime, endTime, allSubOrgIds);
    }
}
