package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrManagerRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.GrManagerRelationService;
import com.shuidihuzhu.cf.dao.gr.GrManagerRelationDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2021-05-20 2:33 下午
 **/
@Slf4j
@Service
public class GrManagerRelationServiceImpl implements GrManagerRelationService {

    @Autowired
    private GrManagerRelationDao relationDao;

    @Override
    public int insert(GrManagerRelationDO relationDO) {
        if (relationDO == null) {
            return 0;
        }
        return relationDao.insert(relationDO);
    }

    @Override
    public void deleteById(int id) {
        relationDao.deleteById(id);
    }

    @Override
    public List<GrManagerRelationDO> listAll() {
        return relationDao.listAll();
    }

    @Override
    public GrManagerRelationDO getByUniqueCode(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return null;
        }
        return relationDao.getByUniqueCode(uniqueCode);
    }
}
