package com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.CfBdCaseDonateCityConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate.IBdCaseDonateCityConfigService;
import com.shuidihuzhu.cf.dao.donate.CfBdCaseDonateCityConfigDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2022-06-30 15:21
 **/
@Service
public class BdCaseDonateCityConfigServiceImpl implements IBdCaseDonateCityConfigService {

    @Resource
    private CfBdCaseDonateCityConfigDao cfBdCaseDonateCityConfigDao;


    @Override
    public List<CfBdCaseDonateCityConfigDO> listAllDonateCityConfig() {
        return cfBdCaseDonateCityConfigDao.listAllDonateCityConfig();
    }
}
