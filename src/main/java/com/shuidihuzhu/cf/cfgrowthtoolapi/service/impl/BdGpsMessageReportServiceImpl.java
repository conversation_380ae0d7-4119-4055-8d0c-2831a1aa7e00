package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.param.GpsReportSearchParam;
import com.shuidihuzhu.cf.dao.BdGpsMessageReportDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdGpsMessageReportDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.BdGpsMessageReportService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * bd上报gps分析(BdGpsMessageReport)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-08 15:05:40
 */
@Service("bdGpsMessageReportService")
public class BdGpsMessageReportServiceImpl implements BdGpsMessageReportService {

    @Resource
    private BdGpsMessageReportDao bdGpsMessageReportDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public BdGpsMessageReportDO queryById(long id) {
        return bdGpsMessageReportDao.queryById(id);
    }


    /**
     * 新增数据
     *
     * @param bdGpsMessageReportDO 实例对象
     * @return 实例对象
     */
    @Override
    public int insert(BdGpsMessageReportDO bdGpsMessageReportDO) {
        return bdGpsMessageReportDao.insert(bdGpsMessageReportDO);
    }


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return bdGpsMessageReportDao.deleteById(id) > 0;
    }

    @Override
    public List<BdGpsMessageReportDO> listByAdminGpsSearch(GpsReportSearchParam searchParam) {
        return bdGpsMessageReportDao.listByAdminGpsSearch(searchParam);
    }

    @Override
    public long countAdminGpsSearch(GpsReportSearchParam searchParam) {
        return bdGpsMessageReportDao.countAdminGpsSearch(searchParam);
    }
}