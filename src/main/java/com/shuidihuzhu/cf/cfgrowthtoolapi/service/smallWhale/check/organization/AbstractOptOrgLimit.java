package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.organization;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.OrgOptParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICrowdfundingCityService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-05-09 17:38
 **/
@Slf4j
public abstract class AbstractOptOrgLimit implements IOptOrgLimit {

    @Autowired
    ICrmOrganizationRelationService organizationRelationService;

    @Autowired
    private ICrmSelfBuiltOrgWriteService orgWriteService;

    @Resource(name = "selfBuiltOrgForSea")
    ICrmSelfBuiltOrgReadService crmOrganizationService;

    @Autowired
    ICfVolunteerService volunteerService;

    @Autowired
    ICrowdfundingCityService crowdfundingCityService;


    protected List<BdCrmOrganizationDO> allOrg;

    public static final int leafNodeCode = OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode();

    private List<BdCrmOrganizationDO> getAllOrgFromInterface() {
        return crmOrganizationService.getAllOrg();
    }

    private final static List<String> leafNodeNameExcludes = Lists.newArrayList("市辖市", "省直辖县级行政区划");


    Optional<BdCrmOrganizationDO> isExistOrgInfo(long orgId) {
        //affectOrgInfo check
        return allOrg.stream()
                .filter(item -> item.getId() == orgId)
                .findFirst();
    }


    boolean nodeForbidName(OrgOptParam orgOptParam) {
        BdCrmOrganizationDO currentOptOrg = orgOptParam.getCurrentOptOrg();
        if (ObjectUtils.nullSafeEquals(currentOptOrg.getOrgAttribute(), leafNodeCode)) {
            return leafNodeNameExcludes.contains(currentOptOrg.getOrgName());
        }
        return false;
    }



    //获取当前节点下的人员绑定信息
    List<BdCrmOrgUserRelationDO> listMemberUnderOrgByOrgId(long orgId) {
        return organizationRelationService.listRelationByOrgId(orgId);
    }



    //检查同层是否有相同的名称
    Response<Boolean> checkHasSameName(OrgOptParam orgOptParam) {
        BdCrmOrganizationDO currentOptOrgs = orgOptParam.getCurrentOptOrg();
        String orgName = currentOptOrgs.getOrgName();
        if (StringUtils.isBlank(orgName) || orgName.length() > 20) {
            return NewResponseUtil.makeFail("请输入20个字符内的组织名");
        }
        if (orgName.contains("-")) {
            return NewResponseUtil.makeFail("不能包含-的特殊字符");
        }
        //查看同层组织是否有相同名称
        long parentId = getParentId(orgOptParam);
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = allOrg.stream()
                .filter(item -> item.getParentId() == parentId && item.getId() != currentOptOrgs.getId())
                .filter(item -> item.getOrgName().equals(orgName))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bdCrmOrganizationDOList)) {
            log.info("已经存在同名的组织:{}", orgName);
            return NewResponseUtil.makeFail(String.format("当前组织下已存在同名的组织:%s", orgName));
        }
        return NewResponseUtil.makeSuccess(true);
    }


    /**
     * 获取上一级节点id
     */
    private long getParentId(OrgOptParam orgOptParam) {
        long parentId = 0L;
        if (orgOptParam.getOptEnum() == OrganizationMemberOptEnum.add_single_node) {
            parentId = orgOptParam.getAddToOrg().getId();
        } else if (orgOptParam.getOptEnum() == OrganizationMemberOptEnum.edit_node) {
            parentId = allOrg.stream().filter(item -> orgOptParam.getCurrentOptOrg().getId() == item.getId())
                    .map(BdCrmOrganizationDO::getParentId)
                    .findFirst()
                    .orElse(0L);
        }
        return parentId;
    }




    Response<Boolean> basicAdd(OrgOptParam orgOptParam) {
        if (orgOptParam.getAddToOrg() == null) {
            return NewResponseUtil.makeFail("参数错误");
        }

        //affectOrgInfo check
        Optional<BdCrmOrganizationDO> addToOrgOpt = isExistOrgInfo(orgOptParam.getAddToOrg().getId());
        if (addToOrgOpt.isEmpty()) {
            log.warn("addToOrgOpt Id:{}设置不对", orgOptParam.getAddToOrg().getId());
            return NewResponseUtil.makeFail("参数错误");
        }
        orgOptParam.setAddToOrg(addToOrgOpt.get());
        return isNotLeafOrCanChangeToNoLeaf(orgOptParam);
    }


    //检查是否能插入新节点
    private Response<Boolean> isNotLeafOrCanChangeToNoLeaf(OrgOptParam orgOptParam) {
        BdCrmOrganizationDO addToOrg = orgOptParam.getAddToOrg();
        if (leafNodeCode == addToOrg.getOrgAttribute()) {
            //检查节点是否有非管理岗
            List<BdCrmOrgUserRelationDO> relationDOList = listMemberUnderOrgByOrgId(addToOrg.getId())
                    .stream()
                    .filter(item -> item.getUserRole() == OrganizationUserEnums.UserRoleEnum.normal.getCode())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(relationDOList)) {
                log.warn("组织为最小层级，请先将非管理岗成员移除后操作");
                return NewResponseUtil.makeFail(String.format("%s组织为最小层级，请先将非管理岗成员移除后操作", addToOrg.getOrgName()));
            }
            //自动将叶子结点转化为非叶子结点
            log.info("自动将叶子结点转化为非叶子结点,参数:{}", JSON.toJSONString(orgOptParam));
            addToOrg.setOrgAttribute(OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode());
            orgWriteService.editCurrentNode(addToOrg, OrganizationMemberOptEnum.edit_node, orgOptParam.getAdminUserId());
            //重新设置allOrg
            allOrg.forEach(item -> {
                if (item.getId() == addToOrg.getId()) {
                    item.setOrgAttribute(OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode());
                }
            });

        }
        return NewResponseUtil.makeSuccess(true);
    }


    @Override
    public Response<Boolean> checkCanOptOrg(OrgOptParam orgOptParam) {
        //基本参数判断
        if (orgOptParam == null || orgOptParam.getOptEnum() == null || orgOptParam.getAdminUserId() <= 0) {
            return NewResponseUtil.makeFail("入参错误");
        }
        OrganizationMemberOptEnum optEnum = orgOptParam.getOptEnum();
        this.allOrg = getAllOrgFromInterface();
        if (!Optional.ofNullable(getNeedCheckOptEnums()).orElse(Lists.newArrayList()).contains(optEnum)) {
            return NewResponseUtil.makeSuccess(true);
        }
        return doCanOptCheck(orgOptParam);
    }


    public abstract Response<Boolean> doCanOptCheck(OrgOptParam orgOptParam);
}
