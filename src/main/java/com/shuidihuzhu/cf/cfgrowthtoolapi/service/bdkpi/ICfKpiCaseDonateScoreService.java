package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseDonateScoreDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiCaseScoreVO;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-04
 */
public interface ICfKpiCaseDonateScoreService {

    /**
     * 获取案例捐单得分总条数
     * @param monthKey
     * @param uniqueCode
     * @return
     */
    long queryCaseDonateScoreCount(String monthKey, String uniqueCode, long validAmount, int validDonateNum);

    /**
     * 获取案例捐单得分
     *
     * @param monthKey
     * @param uniqueCode
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<CfKpiCaseScoreVO> queryCaseDonateScoreDetail(String monthKey, String uniqueCode, long validAmount, int validDonateNum, int pageNo, int pageSize);

    /**
     * 获取案例捐单得分列表
     * @param monthKey
     * @param uniqueCode
     * @return
     */
    OpResult<List<CfKpiCaseScoreVO>> queryCaseScoreDetail(String monthKey, String uniqueCode);

    /**
     * 更新或插入捐单得分明细表
     * @param kpiCaseDonateScoreList
     * @param monthKey
     * @param uniqueCode
     * @return
     */
    int batchSaveOrUpdate(List<CfKpiCaseDonateScoreDo> kpiCaseDonateScoreList, String monthKey, String uniqueCode);

    /**
     * 删除捐单得分
     */
    int batchDeleteByUniqueCode(String uniqueCode, String monthKey);
}
