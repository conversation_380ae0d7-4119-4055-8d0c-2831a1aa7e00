package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfWaitHandleHospitalDO;
import com.shuidihuzhu.cf.dao.record.HospitalSubmitRecord;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-06-05 12:13
 **/
public interface IHospitalSubmitRecordService {

    void insert(HospitalSubmitRecord hospitalSubmitRecord);

    //待整理库相关
    void insertWaitHandleHospital(CfWaitHandleHospitalDO waitHandleHospitalDO);

    /**
     * 当待整理库同步到使用库时，发送mq通知对应的案例有医院信息
     */
    void notifyWhenSyncToUseHospital(CfWaitHandleHospitalDO waitHandleHospitalDo);

    /**
     * 更新待整理库中的医院
     */
    int updateReportHospitalName(int reportId, String inputName, int confirmStatus);

    /**
     * 查看报备是否有对应的待整理医院
     * @param reportId
     * @return
     */
    CfWaitHandleHospitalDO getByReportId(int reportId);

    int countCaseRaiseBetweenTime(String hospitalName, String cityName, String startTime);

    List<HospitalSubmitRecord> getHospitalSubmitByCaseId(int caseId);
}
