package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMasterForGrowthtoolDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.AccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.message.IMsgClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WxAddUserActionItemParm;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WxAddUserActionParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.dao.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.TimeBorder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.CfUrlConfig;
import com.shuidihuzhu.cf.client.feign.CfGrayTestFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfToufangChannelType;
import com.shuidihuzhu.cf.enums.crowdfunding.PrimaryChannelEnum;

import com.shuidihuzhu.cf.model.clewtrack.ClewReceiveModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister;
import com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign;
import com.shuidihuzhu.cf.model.crowdfunding.CfToutiaoRegisterRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.toufang.CfToufangSignUserInfo;
import com.shuidihuzhu.cf.model.mina.CfMinaSecretInfo;
import com.shuidihuzhu.cf.model.toufang.CfToufangRegister;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.chaifenbeta.growthtool.PrimaryChannelFeignClient;
import com.shuidihuzhu.client.cf.api.client.OutsidePutFeignClient;
import com.shuidihuzhu.client.cf.api.model.PrimaryChannelRequestModel;
import com.shuidihuzhu.client.cf.api.model.enums.PrimaryChannelObjectTypeEnum;
import com.shuidihuzhu.client.cf.growthtool.model.CfTouFangSignDto;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.common.web.util.http.HttpResponseModel;
import com.shuidihuzhu.common.web.util.http.HttpUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.msg.enums.RecordConstant;
import com.shuidihuzhu.msg.model.SmsRecord;
import com.shuidihuzhu.msg.vo.rpc.MsgRecord;
import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.averagingDouble;

/**
 * @Auther: zgq
 * @Date: 2019-05-09 15:10
 * @Description:投放相关service
 */
@Service
@Slf4j
@RefreshScope
public class OutsidePutSignServiceImpl implements OutsidePutSignInterface {

    @Autowired
    private CfGrayTestFeignClient cfGrayTestFeignClient;

    @Autowired
    private CfToufangRegisterDayDao cfTouFangRegisterDayDao;

    @Autowired
    private ICfToufangRegisterDayService cfToufangRegisterDayService;


    @Autowired
    private CfMinaSecretInfoService cfMinaSecretInfoService;

    @Autowired
    private IRegisterClewFacadeInterface registerClewFacade;


    @Autowired
    private PrimaryChannelFeignClient primaryChannelFeignClient;

    @Autowired
    private CfAdRegisterBizInterface cfAdRegisterBizInterface;

    @Resource
    private AccountServiceDelegate accountServiceDelegate;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ICfVolunteerService cfVolunteerServiceImpl;

    @Autowired
    TaskCfAdRegisterDao taskCfAdRegisterDao;

    @Autowired
    private IMsgClientDelegate msgClientDelegate;
    @Autowired
    CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    OutsidePutFeignClient outsidePutFeignClient;
    @Autowired
    private ICfMasterForGrowthtoolDelegate cfMasterForGrowthtoolDelegate;

    private static final String CONTENT_FOR_HELP = "【登陆成功】关注“水滴筹”微信公众号可以更方便的申请筹款，获得《筹款秘籍》，不收任何手续费。" +
            "关注步骤：1.打开微信；2.点右上角+号“添加朋友”；3.点击“公众号”；4.搜索“水滴筹”并关注。紧急筹款拨打：**********";


    @Value("${toufang.page.percent:70}")
    private int toufangPagePercent;

    @Value("${toufang.webcall.type.sem:1}")
    private int webCallTypeSem = 1;

    private static final String ABTEST_KEY = "toufang_register";

    @Value("${msg.page.wx:}")
    private String msgPageWX;

    @Autowired
    private IMqProducerService mqProducerServiceImpl;
    @Autowired
    private ShuidiCipher shuidiCipher;

    //activityId
    public static final int RULE_COLLECTION_TOUFANG_SIGN = 2;
    private static final int RULE_COLLECTION_AD_REGISTER = 3;

    //医护人员
    private static final int MEDICALSTAFF = 5;

    /**
     * 投放注册新增service
     *
     * @param cfTouFangSignDto
     * @return
     */
    @Override
    public Response registerAdd(CfTouFangSignDto cfTouFangSignDto) {
        //投放页面分流
        int page = 0;
        String selfTag = cfTouFangSignDto.getSelfTag();
        if (!StringUtils.isEmpty(selfTag)) {
            FeignResponse<Integer> redIn = cfGrayTestFeignClient.getGrayTestBySelfTag(ABTEST_KEY, 0, selfTag);
            page = redIn.getData() == null ? 0 : redIn.getData();
        } else {
            int num = new Random().nextInt(100);
            if (num > toufangPagePercent - 1) {
                page = 1;
            }
        }
        Map map = Maps.newHashMap();
        map.put("page", page);
        map.put("timestamp", System.currentTimeMillis());
        map.put("displayId",cfTouFangSignDto.getDisplayId());
        String referer = cfTouFangSignDto.getReferer();
        referer = StringUtils.isEmpty(referer) ? "" : referer;
        String channel = cfTouFangSignDto.getChannel();
        String formId = cfTouFangSignDto.getFormId();
        String code = cfTouFangSignDto.getCode();
        String mobile = cfTouFangSignDto.getMobile();
        Integer minaAppCode = cfTouFangSignDto.getMinaAppCode();
        String source = cfTouFangSignDto.getSource();
        String openId = sendAppMsg(channel, formId, minaAppCode, code, mobile);
        String relation = cfTouFangSignDto.getRelation();
        String disease = cfTouFangSignDto.getDisease();
        String help = cfTouFangSignDto.getHelp();

        //业务日志
        CfTouFangSign cfTouFangSign = this.buildCfTouFangSign(cfTouFangSignDto, referer, channel, mobile, source, relation, disease, help);
        //获取primaruchannel
        PrimaryChannelRequestModel primaryChannelRequestModel = new PrimaryChannelRequestModel(RULE_COLLECTION_TOUFANG_SIGN, JSONObject.toJSONString(cfTouFangSign), PrimaryChannelObjectTypeEnum.CfTouFangSign);
        String primaryChannel = this.queryPrimaryChannelByPrimaryChannelFegin(primaryChannelRequestModel);
        //线上合作渠道
        if (StringUtils.isNotBlank(channel) && StringUtils.startsWith(channel, "cf_clew_online_material")) {
            primaryChannel = "cf_clew_online_material";
        }
        cfTouFangSign.setPrimaryChannel(primaryChannel);

        //发线索登记消息
        ClewReceiveModel clewReceiveModel = this.buildClewReceiveModel(cfTouFangSignDto, selfTag, referer, channel, mobile, source, openId, relation, disease, primaryChannel);
        clewReceiveModel.setDisplayId(cfTouFangSignDto.getDisplayId());
        //上报广点通数据
        String sourceChannel = primaryChannel + source;
        if("feed".equals(primaryChannel) && "gdt".equals(source)){
            clewReceiveModel.setPrimaryKeyChannel(sourceChannel);
            mqProducerServiceImpl.sendReporGdtMsg(mobile);
        }
        if("sm_mobile_unbrand".equals(source) && StringUtils.isNotBlank(cfTouFangSignDto.getClickid())){
            if(StringUtils.isNotBlank(cfTouFangSignDto.getAccount())){
                if(GdtMediaServiceImpl.accountMap.containsKey(cfTouFangSignDto.getAccount())){
                    mqProducerServiceImpl.sendReportSM(cfTouFangSignDto);
                }
            }
        }
        if(StringUtils.isNotBlank(channel) && "feed_mobile_wxmp_dj".compareToIgnoreCase(channel)==0){
            this.sendReportWxMT(cfTouFangSignDto.getUrl(),cfTouFangSignDto.getClickid());
        }
        this.registerClewFacade.sendNewClewMsg(clewReceiveModel);
        registerClewFacade.sendRegisterSuccessDelayMessage(this.createSmsRecord(-1L, mobile));
        //发Message消息
        //mqProducerServiceImpl.sendCfToufangSignMsg(cfTouFangSign);
        //记录到统计库
        mqProducerServiceImpl.sendTouFangRegisterDayMsg(cfTouFangSign);
        mqProducerServiceImpl.sendCfTouFangSignGrowthMsg(cfTouFangSign);

        return NewResponseUtil.makeSuccess(map);
    }

    public static void sendReportWxMT(String registerUrl,String clickId) {
        String appId = "wx7654c44edbe67920";
        String secretKey="b652cdc5f6a5c24fd9c4663ff91fb5a7";
        String response = HttpUtil.httpGet("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="+appId+"&secret="+secretKey).getBodyString();
        log.info("wxMarketingDelegate.getAccessToken request:{},secretKey,response:{}",appId,secretKey,response);
        if(StringUtils.isBlank(response)){
            return;
        }
        JSONObject accessTokenResponse = JSON.parseObject(response);
        if(accessTokenResponse==null || StringUtils.isBlank(accessTokenResponse.getString("access_token"))){
            return ;
        }
        String accessToken = accessTokenResponse.getString("access_token");
        String url = "https://api.weixin.qq.com/marketing/user_actions/add?version=v1.0&access_token="+accessToken;

        WxAddUserActionParam wxAddUserActionParam = new WxAddUserActionParam();
        wxAddUserActionParam.setUser_action_set_id("1110619650");
        List<WxAddUserActionItemParm> itemParmList = Lists.newArrayList();
        WxAddUserActionItemParm itemParm = new WxAddUserActionItemParm();
        itemParm.setUrl("");
        itemParm.setAction_time((int)(new Date().getTime()/1000));
        itemParm.setAction_type("RESERVATION");
        Map<String,String> trace = Maps.newConcurrentMap();
        trace.put("click_id",clickId);
        itemParm.setTrace(trace);
        itemParmList.add(itemParm);
        wxAddUserActionParam.setActions(itemParmList);
        doPost(url,wxAddUserActionParam);
    }

    /**
     * post请求
     * @param url
     * @param wxAddUserActionParam
     * @return
     */
    public static String doPost(String url, WxAddUserActionParam wxAddUserActionParam){

        CloseableHttpClient httpclient ;
        try {
            httpclient = com.shuidihuzhu.cf.cfgrowthtoolapi.util.HttpClientUtils.getHttpClient();
        } catch (Exception e) {
            log.error("doPost Exception",e);
            return "";
        }

        HttpPost post = new HttpPost(url);
        try {
            StringEntity s = new StringEntity(JSON.toJSON(wxAddUserActionParam).toString());
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");//发送json数据需要设置contentType
            post.setEntity(s);
            HttpResponse res = httpclient.execute(post);
            if(res.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                String result = EntityUtils.toString(res.getEntity());// 返回json格式：
                log.info("user_actions/add request:{},response:{}",JSON.toJSONString(wxAddUserActionParam),result);
                return result;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return "";
    }

    /**
     * 构建ClewReceiveModel
     * @param cfTouFangSignDto
     * @param selfTag
     * @param referer
     * @param channel
     * @param mobile
     * @param source
     * @param openId
     * @param relation
     * @param disease
     * @param primaryChannel
     * @return
     */
    private ClewReceiveModel buildClewReceiveModel(CfTouFangSignDto cfTouFangSignDto, String selfTag, String referer, String channel, String mobile, String source, String openId, String relation, String disease, String primaryChannel) {
        ClewReceiveModel clewReceiveModel = new ClewReceiveModel();
        clewReceiveModel.setChannel(channel);
        clewReceiveModel.setPhone(mobile);
        clewReceiveModel.setRegisterTime(new Date());
        clewReceiveModel.setDisease(disease);
        clewReceiveModel.setRelationType(relation);
        clewReceiveModel.setPrimaryKeyChannel(primaryChannel);
        clewReceiveModel.setClientIp(cfTouFangSignDto.getIp());
        clewReceiveModel.setOuterCheckTestValue(cfTouFangSignDto.getOuterCheckTestValue());
        clewReceiveModel.setNote(null);
        clewReceiveModel.setSource(source);
        clewReceiveModel.setSelfTag(selfTag);
        clewReceiveModel.setOpenId(openId);
        clewReceiveModel.setRegisterUrl(cfTouFangSignDto.getUrl());
        clewReceiveModel.setKeyword(cfTouFangSignDto.getKeyword());
        clewReceiveModel.setMatch(cfTouFangSignDto.getMatch());
        clewReceiveModel.setAccount(cfTouFangSignDto.getAccount());
        clewReceiveModel.setSemwp(cfTouFangSignDto.getSemwp());
        clewReceiveModel.setCreative(cfTouFangSignDto.getCreative());
        clewReceiveModel.setExtendField(cfTouFangSignDto.getOuterCheckTestValue());
        //设置referer
        clewReceiveModel.setReferer(referer);
        //设置58同镇镇长
        clewReceiveModel.setTownLeaderId(cfTouFangSignDto.getTownLeaderId());
        clewReceiveModel.setUserAgent(cfTouFangSignDto.getUserAgent());
        clewReceiveModel.setAge(cfTouFangSignDto.getAge());
        clewReceiveModel.setMale(cfTouFangSignDto.getMale());
        clewReceiveModel.setMediaChannelId(StringUtils.isEmpty(cfTouFangSignDto.getMediaChannelId()) ? "" : cfTouFangSignDto.getMediaChannelId());
        clewReceiveModel.setMediaClickId(StringUtils.isEmpty(cfTouFangSignDto.getMediaClickId()) ? "" : cfTouFangSignDto.getMediaClickId());
        clewReceiveModel.setHospital(cfTouFangSignDto.getHospital());
        return clewReceiveModel;
    }

    /**
     * 构建CfTouFangSign
     * @param cfTouFangSignDto
     * @param referer
     * @param channel
     * @param mobile
     * @param source
     * @param relation
     * @param disease
     * @param help
     * @return
     */
    private CfTouFangSign buildCfTouFangSign(CfTouFangSignDto cfTouFangSignDto, String referer, String channel, String mobile, String source, String relation, String disease, String help) {
        CfTouFangSign cfTouFangSign = new CfTouFangSign();
        cfTouFangSign.setChannel(channel);
        cfTouFangSign.setMobile(mobile);
        cfTouFangSign.setCryptoMobile(ShuidiCipherUtils.encrypt(mobile));
        cfTouFangSign.setSource(source);
        cfTouFangSign.setMatch(cfTouFangSignDto.getMatch());
        cfTouFangSign.setKeyword(cfTouFangSignDto.getKeyword());
        cfTouFangSign.setSemwp(cfTouFangSignDto.getSemwp());
        cfTouFangSign.setAccount(cfTouFangSignDto.getAccount());
        cfTouFangSign.setKwid(cfTouFangSignDto.getKwid());
        cfTouFangSign.setCreative(cfTouFangSignDto.getCreative());
        cfTouFangSign.seteAdposition(cfTouFangSignDto.getEAdPosition());
        cfTouFangSign.setReferer(referer);
        cfTouFangSign.setChannelType(CfToufangChannelType.SEM.getType());
        cfTouFangSign.setRelation(StringUtils.isEmpty(relation) ? "" : relation);
        cfTouFangSign.setDisease(StringUtils.isEmpty(disease) ? "" : disease);
        cfTouFangSign.setHelp(StringUtils.isEmpty(help) ? "" : help);
        cfTouFangSign.setClientIp(cfTouFangSignDto.getIp());
        //新增mediaId
        cfTouFangSign.setMediaId(cfTouFangSignDto.getMediaId());
        return cfTouFangSign;
    }

    /**
     * 通过feign获取primaryChannel
     * @param primaryChannelRequestModel
     * @return
     */
    private String queryPrimaryChannelByPrimaryChannelFegin(PrimaryChannelRequestModel primaryChannelRequestModel) {
        String primaryChannel = PrimaryChannelEnum.OTHER.getValue();
        try{
            Response<String> resStr = primaryChannelFeignClient.getPrimaryChannelByActivity(primaryChannelRequestModel);
            if(resStr == null || StringUtils.isEmpty(resStr.getData())){
                primaryChannel = PrimaryChannelEnum.OTHER.getValue();
            }else{
                primaryChannel = resStr.getData();
            }
        }catch (Exception e){
           log.error("PrimaryChannelFeignClient getPrimaryChannelByActivity error, requestBody:{}",primaryChannelRequestModel);
        }
        return primaryChannel;
    }

    private SmsRecord createSmsRecord(long userId, String mobile) {
        String smsContent = msgPageWX;
        if (StringUtils.isEmpty(smsContent)) {
            smsContent = CONTENT_FOR_HELP;
        }
        SmsRecord smsRecord = new SmsRecord();
        smsRecord.setUserId((userId < 0) ? -1L : userId);
        smsRecord.setType(RecordConstant.MSG_TYPE_USER_NORMAL);
        smsRecord.setBusinessTime(new Timestamp(System.currentTimeMillis()));
        smsRecord.setBusinessType(RecordConstant.BIZ_TYPE_AIXINCHOU);
        smsRecord.setSubBusinessType(RecordConstant.SUB_BIZ_TYPE_U_CF_FOR_HELP);
        smsRecord.setCryptoMobile(ShuidiCipherUtils.encrypt(mobile));
        smsRecord.setContent(smsContent);
        return smsRecord;
    }

    public String sendAppMsg(String channel, String formId, Integer minaAppCode, String code, String mobile) {
        String openId = "";
        try {
            if ("ad_search_miniapp".equals(channel) && StringUtils.isNotBlank(formId) && minaAppCode != null && StringUtils.isNotBlank(code)) {
                CfMinaSecretInfo cfMinaSecretInfo = cfMinaSecretInfoService.getValue(minaAppCode);
                log.info("cfMinaSecretInfo:{}", cfMinaSecretInfo);
                if (cfMinaSecretInfo != null) {
                    String appId = cfMinaSecretInfo.getAppId();
                    String appSecret = cfMinaSecretInfo.getAppSecret();
                    //获取opendId
                    String url = "https://api.weixin.qq.com/sns/jscode2session?appid=##APPID##&secret=##SECRET##&js_code=##JSCODE##&grant_type=authorization_code";
                    if (StringUtils.isNotBlank(appId) && StringUtils.isNotBlank(appSecret) && StringUtils.isNotBlank(code)) {
                        HttpResponseModel httpResponseModel = HttpUtil.httpGet(url.replace("##APPID##", appId).replace("##SECRET##", appSecret).replace("##JSCODE##", code));
                        if (httpResponseModel.isOk()) {
                            JSONObject result = JSON.parseObject(httpResponseModel.getBodyString());
                            openId = result.getString("openid");
                        }
                    }
                    //发送消息
                    log.info("openId:{}", openId);
                }
            }
        } catch (Exception e) {
            log.error("小程序登记发送小程序消息失败!", e);
        }
        return openId;
    }





    /** 内部登记
     * @param cfTouFangSignDto
     * @return
     */
    @Override
    public Response registerMobile(CfTouFangSignDto cfTouFangSignDto) {
        String mobile = cfTouFangSignDto.getMobile();
        String wxNo = cfTouFangSignDto.getWxNo();
        String disease = cfTouFangSignDto.getDisease();
        String note = cfTouFangSignDto.getNote();
        String channel = cfTouFangSignDto.getChannel();
        String source = cfTouFangSignDto.getSource();
        String relation = cfTouFangSignDto.getRelation();
        String help = cfTouFangSignDto.getHelp();
        String keyword = cfTouFangSignDto.getKeyword();
        String semwp = cfTouFangSignDto.getSemwp();
        String selfTag = cfTouFangSignDto.getSelfTag();
        Boolean isInviter = cfTouFangSignDto.getIsInviter() ==null?false:cfTouFangSignDto.getIsInviter();
        String inviterMobile = cfTouFangSignDto.getInviterMobile() == null ? "": cfTouFangSignDto.getInviterMobile();
        String infoUuid = cfTouFangSignDto.getInfoUuid();
        String type = cfTouFangSignDto.getType();

        String uniqueCode = cfTouFangSignDto.getUniqueCode();
        String registerName = cfTouFangSignDto.getRegisterName();
        channel = StringUtils.isBlank(channel) ? "" : channel;
        relation = StringUtils.isBlank(relation) ? "" : relation;
        help = StringUtils.isBlank(help) ? "" : help;
        disease = StringUtils.isBlank(disease) ? "" : disease;
        selfTag = StringUtils.isBlank(selfTag) ? "" : selfTag;
        type = StringUtils.isBlank(type) ? "" : type;
        infoUuid = StringUtils.isBlank(infoUuid) ? "" : infoUuid;
        note = StringUtil.isBlank(note) ? "" : note;
        source = StringUtils.isBlank(source) ? "" : source;
        semwp = StringUtils.isBlank(semwp) ? "" : semwp;
        keyword = StringUtils.isBlank(keyword) ? "" : keyword;
        long userId = cfTouFangSignDto.getUserId();
        userId = userId <= 0 ? 0 : userId;

        CfAdRegister cfAdRegister = new CfAdRegister();
        cfAdRegister.setUserId(userId);
        cfAdRegister.setChannel(channel);
        cfAdRegister.setMobile(mobile);
        cfAdRegister.setHelp(help);
        cfAdRegister.setDisease(disease);
        cfAdRegister.setSelfTag(selfTag);
        cfAdRegister.setRelation(relation);
        cfAdRegister.setType(type);
        cfAdRegister.setDayKey(new Date());
        cfAdRegister.setInfoUuid(infoUuid);
        cfAdRegister.setNote(note);
        cfAdRegister.setInviter(isInviter);
        cfAdRegister.setInviterMobile(inviterMobile);
        cfAdRegister.setSource(source);
        cfAdRegister.setClientIp(cfTouFangSignDto.getIp());
        cfAdRegister.setSemwp(semwp);
        cfAdRegister.setKeyword(keyword);

        PrimaryChannelRequestModel primaryChannelRequestModel = new PrimaryChannelRequestModel(RULE_COLLECTION_AD_REGISTER, JSONObject.toJSONString(cfAdRegister), PrimaryChannelObjectTypeEnum.CfAdRegister);
        String primaryChannel = this.queryPrimaryChannelByPrimaryChannelFegin(primaryChannelRequestModel);
        cfAdRegister.setPrimaryChannel(primaryChannel);


        //发线索登记消息
        sendClewReceiveMsg(mobile, wxNo, registerName, channel, relation, disease, note, selfTag, source, uniqueCode, cfTouFangSignDto.getOuterCheckTestValue(), semwp, keyword, cfTouFangSignDto.getUrl(), userId, cfAdRegister, primaryChannel,
                cfTouFangSignDto.getAccountPlatformDesc(),cfTouFangSignDto.getUserAgent(),cfTouFangSignDto);
        //记录数据至cf_ad_register以便统计
        mqProducerServiceImpl.sendCfAdRegister(cfAdRegister);
        //发msg通知有新的登记
        mqProducerServiceImpl.sendCfToufangAdRegister(cfAdRegister);
        //当volunteerType为空时发送给医护登记信息
        if (StringUtils.isBlank(cfTouFangSignDto.getVolunteerType())) {
            log.info("sendClewRegistration2Medicalstaff_uniqueCode:{},registerName:{}", uniqueCode, registerName);
            sendClewRegistration2Medicalstaff(uniqueCode, registerName);
        }
        Map map = Maps.newHashMap();
        map.put("timestamp", System.currentTimeMillis());
        return NewResponseUtil.makeSuccess(map);

    }

    private void sendClewReceiveMsg(String mobile, String wxNo, String registerName, String channel, String relation, String disease, String note, String selfTag, String source, String uniqueCode, String outerCheckTestValue, String semwp, String keyword, String url, long userId, CfAdRegister cfAdRegister, String primaryChannel,
                                    String accountPlatformDesc, String userAgent, CfTouFangSignDto cfTouFangSignDto) {
        ClewReceiveModel clewReceiveModel = new ClewReceiveModel();
        clewReceiveModel.setChannel(channel);
        clewReceiveModel.setPhone(mobile);
        clewReceiveModel.setWxNo(wxNo);
        clewReceiveModel.setRegisterTime(new Date());
        clewReceiveModel.setDisease(disease);
        clewReceiveModel.setRelationType(relation);
        clewReceiveModel.setPrimaryKeyChannel(primaryChannel);
        clewReceiveModel.setClientIp(cfAdRegister.getClientIp());
        clewReceiveModel.setOuterCheckTestValue(outerCheckTestValue);
        clewReceiveModel.setNote(note);
        clewReceiveModel.setSource(source);
        clewReceiveModel.setUniqueCode(uniqueCode);
        clewReceiveModel.setUserId(userId);
        clewReceiveModel.setSelfTag(selfTag);
        clewReceiveModel.setRegisterUrl(url);
        clewReceiveModel.setKeyword(keyword);
        clewReceiveModel.setAccount(accountPlatformDesc);
        clewReceiveModel.setSemwp(semwp);
        clewReceiveModel.setExtendField(outerCheckTestValue);
        clewReceiveModel.setSource(source);
        clewReceiveModel.setRegisterName(registerName);
        clewReceiveModel.setUserAgent(userAgent);
        clewReceiveModel.setDeviceId(cfTouFangSignDto.getDeviceId());
        clewReceiveModel.setIdfa(cfTouFangSignDto.getIdfa());
        clewReceiveModel.setSourceInfoUuid(cfTouFangSignDto.getSourceInfoUuid());
        clewReceiveModel.setHospital(cfTouFangSignDto.getHospital());
        this.registerClewFacade.sendNewClewMsg(clewReceiveModel);
        registerClewFacade.sendRegisterSuccessDelayMessage(this.createSmsRecord(-1L, mobile));
    }

    private void sendClewRegistration2Medicalstaff(String uniqueCode, String registerName) {
        if (StringUtils.isNotBlank(uniqueCode)) {
            try {
                CrowdfundingVolunteer cfVolunteer = cfVolunteerServiceImpl.getOnWorkVolunteerByUniqueCode(uniqueCode);
                log.info("CfVolunteer:{}", cfVolunteer);
                com.shuidihuzhu.msg.vo.Response response = null;
                if (null != cfVolunteer && cfVolunteer.getVolunteerType() == MEDICALSTAFF) {
                    CfVolunteerMaterialDO cfVolunteerMaterialDO = cfVolunteerServiceImpl.getVolunteerMateri(uniqueCode);
                    log.info("CfToufangSignController_uniqueCode:{},mobile:{},realName:{},cfVolunteerMaterialDO:{}", uniqueCode,cfVolunteer.getMobile(),registerName,cfVolunteerMaterialDO);
                    //如果material有数据，且openID不为空
                    if (null != cfVolunteerMaterialDO && StringUtils.isNotBlank(cfVolunteerMaterialDO.getOpenId())) {
                        response = getResponse(registerName, cfVolunteerMaterialDO.getOpenId());
                    }else{
                        //兼容老用户
                        CfUserVolunteerRelationDO cfUserVolunteerRelationDO = cfVolunteerServiceImpl.getAccountUserAndVolunteerRelationByPhone(cfVolunteer.getMobile());
                        log.info("CfToufangSignController_cfUserVolunteerRelationDO:{}",cfUserVolunteerRelationDO);
                        if (null != cfUserVolunteerRelationDO){
                            response = getResponse(registerName, cfUserVolunteerRelationDO.getOpenId());
                        }
                    }
                    log.info("sendClewRegistration2Medicalstaff:{}", JSON.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("sendClewRegistration2Medicalstaff occur error:", e);
            }
        }
    }

    private com.shuidihuzhu.msg.vo.Response getResponse(String registerName, String openId) {
        com.shuidihuzhu.msg.vo.Response response = null;
        UserInfoModel userInfoModelVolunteer = accountServiceDelegate.getUserInfoByOpenId(openId);
        log.info("userInfoModelVolunteer:{}", userInfoModelVolunteer);
        if (null != userInfoModelVolunteer) {
            MsgRecord msg = new MsgRecord();
            HashMap<Integer, String> params = Maps.newHashMap();
            params.put(1, StringUtils.isNotBlank(registerName) ? registerName : "大病");
            msg.setParams(params);
            msg.setUserThirdType(AccountThirdTypeEnum.WX_CF_DREAM.getCode());
            msg.setUserId(userInfoModelVolunteer.getUserId());
            msgClientDelegate.saveBatch(MsgRecordBatch.build("1708model1", "", Lists.newArrayList(msg)));
        }
        return response;
    }

    @Override
    public Response fixAdRegister(String token, Long startId, Long endId, Integer limit) {
        this.fixAdRegisterPrimaryChannel(startId, endId, limit);
        return ResponseUtil.makeSuccess(null);
    }

    @Override
    public Response fixToufangRegister(String token, Long startId, Long endId, Integer limit) {
        this.fixToufangRegisterPrimaryChannel(startId, endId, limit);
        return ResponseUtil.makeSuccess(null);
    }

    @Override
    public Response handleToufangRegisterCount(List<String> monitorMobiles, Double alarmFactor, Integer lowestCount) {
        Timestamp end = DateUtil.nowTime();
        Timestamp start = DateUtil.addMinutes(end, -60);
        log.info("toufang param  startTime:{}, endTime:{}, alarmFactor:{}", start, end, alarmFactor);
        double count = (double) cfTouFangRegisterDayDao.getCountByTime(start, end);
        if (count == 0.0) {
            String message = "【监控报警】投放页注册用户统计任务异常,当前时间段注册量为0";
            this.sendMessage(message, monitorMobiles);
            return ResponseUtil.makeFail("当前时间段注册量为0");
        }
        List<TimeBorder> timeBorderList = Lists.newArrayList();
        for (int i = 1; i <= 7; i++) {
            //获取前一周 同时段的投放注册数量
            TimeBorder timeBorder = new TimeBorder();
            timeBorder.setStart(DateUtil.deleteDays(start, i));
            timeBorder.setEnd(DateUtil.deleteDays(end, i));
            timeBorderList.add(timeBorder);
        }
        List<Double> variance = Lists.newArrayList();
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(timeBorderList)){
            variance = cfTouFangRegisterDayDao.getListByTimeBorder(timeBorderList);
        } else {
            return ResponseUtil.makeFail("获取前一周时间段空");
        }
        if (variance.size() < 7){
            //variance需要添加的值
            int addCount = 7 - variance.size();
            for (int i=0; i< addCount; i++) {
                variance.add(0.0);
            }
        }
        //前一周的平均值
        Stat stat = calculateMeanAndVariance(variance);
        double alarmMin = stat.mean - (stat.mean * alarmFactor);
        double alarmMax = stat.mean + (stat.mean * alarmFactor);
        if (alarmMin >= count && count < lowestCount){
            String msg = "【投放注册报警】过去一周内同时期平均值：" + String.format("%.3f", stat.mean)
                    + ";门限最低值为：" + lowestCount
                    + ";预测最低值为：" + String.format("%.3f", alarmMin)
                    + ";预测最高值为：" + String.format("%.3f", alarmMax)
                    + ";标准差值为：" + String.format("%.3f", stat.variance)
                    + ";一小时内投放注册量：" + count
                    + ";偏移量：" + String.format("%.3f", Math.abs(stat.mean - count));
            this.sendMessage(msg, monitorMobiles);
        } else if ( count > alarmMax ){
            //高于最高值报警
            String msg = "【投放注册报警】过去一周内同时期平均值：" + String.format("%.3f", stat.mean)
                    + ";门限最低值为：" + lowestCount
                    + ";预测最低值为：" + String.format("%.3f", alarmMin)
                    + ";预测最高值为：" + String.format("%.3f", alarmMax)
                    + ";标准差值为：" + String.format("%.3f", stat.variance)
                    + ";一小时内投放注册量：" + count
                    + ";偏移量：" + String.format("%.3f", Math.abs(stat.mean - count));
            this.sendMessage(msg, monitorMobiles);
        }
        return ResponseUtil.makeSuccess(null);
    }



    /**
     * user_account 表
     *
     * @param infoUserIdSet
     * @return
     */
    private Set<String> getUserMobileSet(Set<Long> infoUserIdSet) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(infoUserIdSet)) {
            return Collections.emptySet();
        }
        List<List<Long>> userIdPartition = Lists.partition(new ArrayList<>(infoUserIdSet), 100);
        Set<String> infoMobileSet = Sets.newHashSet();
        List<UserInfoModel> tempUserList;
        for (List<Long> userIdList : userIdPartition) {
            tempUserList = accountServiceDelegate.getUserInfoByUserIdBatch(userIdList);
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(tempUserList)) {
                for (UserInfoModel userInfoModel : tempUserList) {
                    if (null != userInfoModel && !org.springframework.util.StringUtils.isEmpty(userInfoModel.getCryptoMobile())) {
                        infoMobileSet.add(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
                    }
                }
            }
        }

        return infoMobileSet;
    }

    private String compactOssUrl(String url) {
        return CfUrlConfig.compactOssUrl(url);
    }






    private static class Stat {
        double mean;
        double variance;

        public Stat(double mean, double variance) {
            this.mean = mean;
            this.variance = variance;
        }
    }

    private Stat calculateMeanAndVariance(List<Double> numbers) {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(numbers)) {
            return new Stat(0, 0);
        }
        double mean = numbers.stream().collect(averagingDouble(value -> value));
        double variance = Math.sqrt(numbers.stream().mapToDouble(value -> (value - mean) * (value - mean)).sum() / numbers.size());
        return new Stat(mean, variance);
    }

    private void sendMessage(String message, List<String> mobiles) {
        if(com.shuidi.weixin.common.util.StringUtils.isEmpty(message) || org.apache.commons.collections.CollectionUtils.isEmpty(mobiles)) {
            return;
        }
        msgClientDelegate.sendMessage("AENUKO1560425031",message, mobiles);
    }

    /**
     * 修shuidi_crowdfunding.cf_ad_register_record
     * @param startId
     * @param endId
     * @param limit
     */
    private void fixAdRegisterPrimaryChannel(Long startId, Long endId, Integer limit) {
        long anchorId = startId;
        while(anchorId < endId) {
            List<CfToufangRegister> toufangRegisters = cfAdRegisterBizInterface.getBetween(startId, endId, anchorId, limit);
            if(CollectionUtils.isEmpty(toufangRegisters)) {
                break;
            }

            anchorId = toufangRegisters.get(toufangRegisters.size() - 1).getId();
            //处理数据
            toufangRegisters.stream()
                    .forEach(toufangRegister -> {
                        PrimaryChannelRequestModel primaryChannelRequestModel = new PrimaryChannelRequestModel(RULE_COLLECTION_AD_REGISTER, JSONObject.toJSONString(toufangRegister), PrimaryChannelObjectTypeEnum.CfToufangRegister);
                        Response<String> resStr = primaryChannelFeignClient.getPrimaryChannelByActivity(primaryChannelRequestModel);
                        String primaryChannel = "";
                        if(resStr == null || resStr.getData() == null){
                            primaryChannel = PrimaryChannelEnum.OTHER.getValue();
                        }else{
                            primaryChannel = resStr.getData();
                        }
                        cfMasterForGrowthtoolDelegate.updatePrimaryChannel(toufangRegister.getId(), primaryChannel);
                        log.info("fix ad register id={};primaryChannel={}", toufangRegister.getId(), primaryChannel);
                        sleepQuietly(10L);
                    });
        }
    }

    /**
     * 修shuidi_stat.cf_toufang_register_day的primaryChannel
     * @param startId
     * @param endId
     * @param limit
     */
    private void fixToufangRegisterPrimaryChannel(Long startId, Long endId, Integer limit) {
        long anchorId = startId;
        while(anchorId < endId) {
            List<CfToufangRegister> toufangRegisters = cfToufangRegisterDayService.getBetween(startId, endId, anchorId, limit);
            if(CollectionUtils.isEmpty(toufangRegisters)) {
                break;
            }
            anchorId = toufangRegisters.get(toufangRegisters.size() - 1).getId();
            //处理数据
            toufangRegisters.stream()
                    .forEach(toufangRegister -> {
                        PrimaryChannelRequestModel primaryChannelRequestModel = new PrimaryChannelRequestModel(RULE_COLLECTION_TOUFANG_SIGN, JSONObject.toJSONString(toufangRegister), PrimaryChannelObjectTypeEnum.CfToufangRegister);
                        Response<String> resStr = primaryChannelFeignClient.getPrimaryChannelByActivity(primaryChannelRequestModel);
                        String primaryChannel = "";
                        if(resStr == null || resStr.getData() == null){
                            primaryChannel = PrimaryChannelEnum.OTHER.getValue();
                        }else{
                            primaryChannel = resStr.getData();
                        }
                        cfTouFangRegisterDayDao.updatePrimaryChannel(toufangRegister.getId(), primaryChannel);
                        log.info("fix toufang register id={};primaryChannel={}", toufangRegister.getId(), primaryChannel);
                        sleepQuietly(10L);
                    });
        }
    }

    private static void sleepQuietly(long mills) {
        try {
            Thread.sleep(mills);
        } catch (Exception e) {
            log.error("--sleepQuietly 异常信息{}",e);
        }
    }

}
