package com.shuidihuzhu.cf.cfgrowthtoolapi.service.prepose;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CardReportRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGrowthtoolApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CardReportRelationModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfGrowthtoolApproveService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IClewPreproseMaterialService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.dao.CardReportRelationDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @author: fengxuan
 * @create 2021-06-24 3:13 下午
 **/
@Slf4j
@Service
public class CardReportHandlerService {

    @Autowired
    private CardReportRelationDao cardReportRelationDao;

    @Autowired
    private ICfGrowthtoolApproveService cfGrowthtoolApproveService;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Autowired
    private IClewPreproseMaterialService clewPreproseMaterialService;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    //判断是否能提交代录入
    public OpResult<Boolean> checkCanSubmitCardReport(PreposeMaterialModel.MaterialInfoVo materialInfoVo, int type, CrowdfundingVolunteer cfVolunteer) {

        Long preposeId = Optional.ofNullable(materialInfoVo.getId()).orElse(0L);
        boolean commonLevel = Objects.equals(cfVolunteer.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel());
        //提交判断
        if (Objects.equals(materialInfoVo.getSpecialReportType(), 1) && Objects.equals(type, 2)) {
            CardReportRelationDO cardReportRelationDO = getCardReportByPreposeId(preposeId);
            //顾问 && (选择了名片报备,但是未审核通过)
            boolean notAllowCreateUrl = CrowdfundingVolunteerEnum.commonRolesV2.contains(cfVolunteer.getLevel()) &&
                    (cardReportRelationDO == null || !Objects.equals(cardReportRelationDO.getApproveStatus(), CfGreenChannelEnums.ApproveStatusEnum.pass.getCode()));

            //名片报备信息不存在
            if (notAllowCreateUrl) {
                return OpResult.createFailResult(commonLevel ? CfGrowthtoolErrorCode.CARD_REPORT_NOT_PASS : CfGrowthtoolErrorCode.PARTNER_CARD_REPORT_NOT_PASS);
            }
        }
        //提交名片报备判断
        if (Objects.equals(materialInfoVo.getSpecialReportType(), 1) && preposeId > 0) {
            //提交后不能由非名片报备改为-名片报备
            ClewCrowdfundingReportRelation reportRelation = clewPreproseMaterialService.getByPreposeMaterialId(preposeId);
            if (reportRelation == null) {
                return OpResult.createFailResult(commonLevel ? CfGrowthtoolErrorCode.CARD_REPORT_CAN_NOT_CHAHGE : CfGrowthtoolErrorCode.CARD_REPORT_CAN_NOT_CHAHGE_1);
            }
            //已经提交了代录入,不能改为名片报备
            boolean cannotChangeToCardReport = !Objects.equals(reportRelation.getApproveStatus(), ClewPreproseMaterialEnums.ApproveStatusEnum.NO_COMMIT.getStatus())
                    && Objects.equals(reportRelation.getSpecialReportType(), 0);
            if (cannotChangeToCardReport) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.CARD_REPORT_CAN_NOT_CHAHGE);
            }
        }
        return OpResult.createSucResult(true);
    }


    public OpResult<Void> saveOrUpdateCardReport(PreposeMaterialModel.MaterialInfoVo materialInfoVo, Integer cardReport,
                                                 int type, CrowdfundingVolunteer cfVolunteer, Long preposeMaterialId) {
        preposeMaterialId = Optional.ofNullable(preposeMaterialId).orElse(0L);
        //判断下状态是否能提交
        // 名牌报备 && 提交名牌报备 && 保存 && 顾问
        boolean needCreateCardReport = Objects.equals(materialInfoVo.getSpecialReportType(), 1)
                && Objects.equals(cardReport, 1)
                && Objects.equals(type, 1)
                && CrowdfundingVolunteerEnum.commonRolesV2.contains(cfVolunteer.getLevel());
        log.info("生成名片报备:{},needCreateCardReport:{}", preposeMaterialId, needCreateCardReport);
        if (needCreateCardReport) {
            if (StringUtils.isBlank(materialInfoVo.getRaiseMobile()) || StringUtils.isBlank(materialInfoVo.getPatientName())) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.CARD_REPORT_NOT_FILL);
            }
            if (preposeMaterialId <= 0) {
                log.info("传入的报备id为空");
                return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_ERROR);
            }
            //生成对应的待办
            CrowdfundingVolunteer leaderVolunteer;
            // 如果是筹款合作顾问优先分配给组长，组长不存在分配给高级渠道经理, 如果是筹款合作组长优先分配给高级渠道经理
            if (cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_COMMON.getLevel()) {
                leaderVolunteer = memberInfoService.listApplyLeaderWithDefaultExplicit(cfVolunteer.getUniqueCode(), Lists.newArrayList(CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_PROVINCE.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getLevel()));
            } else if (cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_PROVINCE.getLevel()) {
                leaderVolunteer = memberInfoService.listApplyLeaderWithDefaultExplicit(cfVolunteer.getUniqueCode(), Lists.newArrayList(CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getLevel()));
            } else {
                leaderVolunteer = memberInfoService.listApplyLeaderWithDefaultExplicit(cfVolunteer.getUniqueCode(), Lists.newArrayList());
            }
            if (leaderVolunteer == null) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.CARD_REPORT_CAN_NOT_SUBMIT);
            }
            //创建对应的relation
            CardReportRelationDO cardReportRelationDO = new CardReportRelationDO();
            cardReportRelationDO.setPreposeMaterialId(preposeMaterialId);
            cardReportRelationDO.setRaiseMobile(ShuidiCipherUtils.encrypt(materialInfoVo.getRaiseMobile()));
            cardReportRelationDO.setPatientName(materialInfoVo.getPatientName());
            cardReportRelationDO.setApproveStatus(0);
            String cardReportPic = "";
            if (!CrowdfundingVolunteerEnum.noMisRoles.contains(cfVolunteer.getLevel())) {
                //使用上传医疗证明上的图片
                cardReportPic = Optional.ofNullable(materialInfoVo.getPreAuditImageUrl()).orElse("");
            } else {
                cardReportPic = Optional.ofNullable(materialInfoVo.getCardReportPic()).orElse("");
                cardReportRelationDO.setPartnerType(CrowdfundingVolunteerEnum.PartnerTagEnum.partner.getCode());
            }
            cardReportRelationDO.setCardReportPic(cardReportPic);
            cardReportRelationDO.setRejectReason("");
            if (StringUtils.isNotBlank(materialInfoVo.getSpecialReportReason())) {
                cardReportRelationDO.setSpecialReportReason(materialInfoVo.getSpecialReportReason());
            }
            cardReportRelationDO.setUniqueCode(cfVolunteer.getUniqueCode());
            cardReportRelationDO.setMis(cfVolunteer.getMis());
            //查找对应的报备是否有cardPreposeRelation
            int cardReportId = 0;
            CardReportRelationDO cardReportRelationInDB = getCardReportByPreposeId(cardReportRelationDO.getPreposeMaterialId());
            if (cardReportRelationInDB == null) {
                cardReportRelationDao.insert(cardReportRelationDO);
                cardReportId = cardReportRelationDO.getId();
            } else {
                //判断下是否能进行提交
                if (!Objects.equals(cardReportRelationInDB.getApproveStatus(), CfGreenChannelEnums.ApproveStatusEnum.reject.getCode())) {
                    return OpResult.createFailResult(CfGrowthtoolErrorCode.CARD_REPORT_NOT_ALLOW_EDIT);
                }
                //每次提交,都将状态重新设置为0
                cardReportRelationDao.updateCardReportRelation(cardReportRelationDO);
                cardReportId = cardReportRelationInDB.getId();
            }
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(preposeMaterialId), OperateTypeEnum.SAVE_CARD_REPORT.getDesc(),
                    OperateTypeEnum.SAVE_CARD_REPORT, "名片报备提交上级审核", cfVolunteer.getId(), cfVolunteer.getVolunteerName()));
            //插入对应的申请记录
            cfGrowthtoolApproveService.saveApproveContent(String.valueOf(cardReportId),
                    CfGrowthtoolApproveDO.ApproveTypeEnum.CARD_REPORT.getType(), cfVolunteer, leaderVolunteer);
            //发送消息
            appPushCrmCaseMsgService.sendApproveCardReport(cfVolunteer.getVolunteerName(), leaderVolunteer);
        }
        return OpResult.createSucResult(null);
    }


    public CardReportRelationDO getCardReportByPreposeId(long preposeId) {
        if (preposeId == 0) {
            return null;
        }
        CardReportRelationDO relationDO = cardReportRelationDao.getByPreposeId(preposeId);
        if (relationDO != null && StringUtils.isNotBlank(relationDO.getRaiseMobile())) {
            relationDO.setRaiseMobile(shuidiCipher.decrypt(relationDO.getRaiseMobile()));
        }
        return relationDO;
    }

    public CardReportRelationModel getCardReportModeleByPreposeMaterialId(long preposeMaterialId) {
        CardReportRelationDO cardReportByPreposeId = this.getCardReportByPreposeId(preposeMaterialId);
        if (Objects.nonNull(cardReportByPreposeId)) {
            CardReportRelationModel cardReportRelationModel = new CardReportRelationModel();
            BeanUtils.copyProperties(cardReportByPreposeId, cardReportRelationModel);
            List<CfGrowthtoolApproveDO> cfGrowthtoolApproveDOS = cfGrowthtoolApproveService.listByContentAndType(CfGrowthtoolApproveDO.ApproveTypeEnum.CARD_REPORT.getType(), String.valueOf(cardReportRelationModel.getId()));
            if (CollectionUtils.isNotEmpty(cfGrowthtoolApproveDOS)) {
                Optional<CfGrowthtoolApproveDO> firstApprove = cfGrowthtoolApproveDOS.stream().sorted(Comparator.comparing(CfGrowthtoolApproveDO::getCreateTime).reversed()).findFirst();
                cardReportRelationModel.setVolunteerName(firstApprove.isPresent() ? firstApprove.get().getApproveName() : "");
            }
            return cardReportRelationModel;
        }
        return null;
    }


    public List<CardReportRelationDO> listByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        //需要解密
        List<CardReportRelationDO> cardReportRelationDOS = cardReportRelationDao.listByIds(ids);
        cardReportRelationDOS.forEach(item -> {
            if (StringUtils.isNotBlank(item.getRaiseMobile())) {
                item.setRaiseMobile(shuidiCipher.decrypt(item.getRaiseMobile()));
            }
        });
        return cardReportRelationDOS;
    }


    /**
     * 审核通过, id：报备id
     */
    public int approveByLeader(int id, int approveStatus, String rejectReason, String approveUniqueCode) {
        List<CardReportRelationDO> cardReportRelationDOS = listByIds(Lists.newArrayList(id));
        if (CollectionUtils.isEmpty(cardReportRelationDOS)) {
            log.info("没找到对应的名片:{}报备信息", id);
            return 0;
        }
        CardReportRelationDO cardReportRelationDO = cardReportRelationDOS.get(0);
        cardReportRelationDO.setRejectReason(Optional.ofNullable(rejectReason).orElse(""));
        cardReportRelationDO.setApproveStatus(approveStatus);
        //找到对应的人员
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(cardReportRelationDO.getUniqueCode());
        CrowdfundingVolunteer leaderVolunteer = cfVolunteerService.getCrowdfundingVolunteerByUniqueCode(approveUniqueCode);
        //volunteer是合作团队顾问 && leaderVolunteer不是高级渠道经理需要重新生成一条代办给高级渠道经理审批
        if (volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_COMMON.getLevel() && !Objects.equals(leaderVolunteer.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getLevel())
                && approveStatus != CfGreenChannelEnums.ApproveStatusEnum.reject.getCode()){
            CardReportRelationDO cardReportRelationByLeader = new CardReportRelationDO();
            BeanUtils.copyProperties(cardReportRelationDO, cardReportRelationByLeader);
            cardReportRelationByLeader.setRejectReason("");
            cardReportRelationByLeader.setApproveStatus(CfGreenChannelEnums.ApproveStatusEnum.wait.getCode());
            cardReportRelationByLeader.setRaiseMobile(oldShuidiCipher.aesEncrypt(cardReportRelationByLeader.getRaiseMobile()));
            //发送消息
            CrowdfundingVolunteer cfVolunteer = memberInfoService.listApplyLeaderWithDefaultExplicit(volunteer.getUniqueCode(), Lists.newArrayList(CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getLevel()));
            cardReportRelationDao.insert(cardReportRelationByLeader);
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(cardReportRelationByLeader.getPreposeMaterialId()), OperateTypeEnum.SAVE_CARD_REPORT.getDesc(),
                    OperateTypeEnum.SAVE_CARD_REPORT, "名片报备提交上级审核", leaderVolunteer.getId(), leaderVolunteer.getVolunteerName()));
            //插入对应的申请记录
            cfGrowthtoolApproveService.saveApproveContent(String.valueOf(cardReportRelationByLeader.getId()),
                    CfGrowthtoolApproveDO.ApproveTypeEnum.CARD_REPORT.getType(), volunteer, cfVolunteer);
            appPushCrmCaseMsgService.sendApproveCardReport(volunteer.getVolunteerName(), cfVolunteer);
        } else {
            appPushCrmCaseMsgService.approveCardReportByLeader(volunteer, cardReportRelationDO, approveStatus);
        }
        if (approveStatus == CfGreenChannelEnums.ApproveStatusEnum.reject.getCode()) {
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(cardReportRelationDO.getPreposeMaterialId()), OperateTypeEnum.SAVE_CARD_REPORT.getDesc(),
                    OperateTypeEnum.CARD_REPORT_REJECT, "上级审核驳回", leaderVolunteer.getId(), leaderVolunteer.getVolunteerName()));
        }
        if (approveStatus == CfGreenChannelEnums.ApproveStatusEnum.pass.getCode()) {
            rejectReason = "";
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(cardReportRelationDO.getPreposeMaterialId()), OperateTypeEnum.SAVE_CARD_REPORT.getDesc(),
                    OperateTypeEnum.CARD_REPORT_PASS, "上级审核通过", leaderVolunteer.getId(), leaderVolunteer.getVolunteerName()));
        }
        return cardReportRelationDao.approveByLeader(id, approveStatus, rejectReason);
    }


}
