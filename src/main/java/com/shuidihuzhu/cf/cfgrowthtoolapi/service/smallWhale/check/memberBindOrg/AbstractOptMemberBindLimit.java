package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.memberBindOrg;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.CfMemberOrgWrapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.MemberOrgRelationParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2020-05-12 16:28
 **/
@Slf4j
public abstract class AbstractOptMemberBindLimit implements IOptMemberBindLimit {

    ICrmSelfBuiltOrgReadService crmOrganizationService;

    ICfVolunteerService volunteerService;

    ICrmOrganizationRelationService crmOrganizationRelationService;

    protected CfMemberOrgWrapper memberOrgWrapper;

    public AbstractOptMemberBindLimit(ICrmSelfBuiltOrgReadService crmOrganizationService, ICfVolunteerService volunteerService, ICrmOrganizationRelationService crmOrganizationRelationService) {
        this.crmOrganizationService = crmOrganizationService;
        this.volunteerService = volunteerService;
        this.crmOrganizationRelationService = crmOrganizationRelationService;
    }


    @Override
    public Response<Boolean> checkCanOptMemberBind(MemberOrgRelationParam memberOrgRelationParam) {
        if (memberOrgRelationParam == null || memberOrgRelationParam.getOptEnum() == null) {
            return NewResponseUtil.makeFail("传入参数错误");
        }

        Response<Void> initResponse = initMemberWrapper(memberOrgRelationParam);
        if (initResponse.notOk()) {
            return NewResponseUtil.makeFail(initResponse.getMsg());
        }

        List<OrganizationMemberOptEnum> needCheckOptEnums = Optional.ofNullable(getNeedCheckOptEnums()).orElse(Lists.newArrayList());
        if (!needCheckOptEnums.contains(memberOrgRelationParam.getOptEnum())) {
            return NewResponseUtil.makeSuccess(true);
        }

        return doCheckOptMemberBind(memberOrgRelationParam);
    }



    Response<Void> initMemberWrapper(MemberOrgRelationParam memberOrgRelationParam) {
        long orgId = memberOrgRelationParam.getOrgId();
        String mis = memberOrgRelationParam.getCurrentOptMis();
        String uniqueCode = memberOrgRelationParam.getUniqueCode();
        if (StringUtils.isBlank(uniqueCode) || (orgId <= 0)) {
            return NewResponseUtil.makeFail("传入参数错误");
        }
        //检查人是否存在且有效
        CrowdfundingVolunteer crowdfundingVolunteer = volunteerService.getVolunteerByUniqueCode(uniqueCode);
        if (crowdfundingVolunteer == null) {
            return NewResponseUtil.makeFail(String.format("%s不存在", mis));
        }
        try {
            this.memberOrgWrapper = new CfMemberOrgWrapper(crowdfundingVolunteer);
        } catch (Exception e) {
            log.error("异常", e);
            return NewResponseUtil.makeFail(e.getMessage());
        }
        //如果传入组织,组织是否有效
        BdCrmOrganizationDO crmOrganizationDO = crmOrganizationService.getCurrentOrgById(orgId);
        if (crmOrganizationDO == null) {
            return NewResponseUtil.makeFail(String.format("入参中传入的组织%d不存在", orgId));
        }
        memberOrgWrapper.setBdCrmOrganizationDO(crmOrganizationDO);
        return NewResponseUtil.makeSuccess(null);
    }


    @Override
    public BdCrmOrgUserRelationDO getRelationDO(MemberOrgRelationParam memberOrgRelationParam) {
        Response<Void> response = initMemberWrapper(memberOrgRelationParam);
        if (response.notOk()) {
            return null;
        }
        return CfMemberOrgWrapper.buildRelationDO(memberOrgWrapper);
    }

    abstract Response<Boolean> doCheckOptMemberBind(MemberOrgRelationParam memberOrgRelationParam);

}
