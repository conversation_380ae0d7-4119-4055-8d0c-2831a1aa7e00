package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.memberBindOrg;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.MemberOrgRelationParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.handler.OptLimitHandler;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.CrowdfundingVolunteerUtil;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: fengxuan
 * @create 2020-05-12 17:10
 **/
@Slf4j
public class AddMemberLimitImpl extends AbstractOptMemberBindLimit {

    ICrmMemberInfoService crmMemberInfoService;

    public AddMemberLimitImpl(OptLimitHandler optLimitHandler) {
        super(optLimitHandler.getCrmOrganizationService(), optLimitHandler.getCfVolunteerService(), optLimitHandler.getRelationService());
        this.crmMemberInfoService = optLimitHandler.getCrmMemberInfoService();
    }

    @Override
    public Response<Boolean> doCheckOptMemberBind(MemberOrgRelationParam memberOrgRelationParam) {
        log.info("开始增加人员校验");
        //查看所添加的节点是否是没有人员的叶子结点如果是需要先修改为非叶子结点,然后重新获取当前组织信息

        //判断当前用户的角色 和 节点是否匹配
        Response<Boolean> judgeRoleAndNodeMatchResponse = judgeRoleAndNodeMatch();
        if (judgeRoleAndNodeMatchResponse.notOk()) {
            return judgeRoleAndNodeMatchResponse;
        }
        //顾问是否已添加到其他节点
        Response<Boolean> normalRoleHadAddTOtherOrg = memberHadAddTOtherOrg(memberOrgRelationParam);
        if (normalRoleHadAddTOtherOrg.notOk()) {
            return normalRoleHadAddTOtherOrg;
        }
        return NewResponseUtil.makeSuccess(true);
    }


    @Override
    public List<OrganizationMemberOptEnum> getNeedCheckOptEnums() {
        return Lists.newArrayList(OrganizationMemberOptEnum.add_member);
    }


    private Response<Boolean> judgeRoleAndNodeMatch() {
        int userRole = memberOrgWrapper.getUserRole();
        int nodeAttribute = memberOrgWrapper.getBdCrmOrganizationDO().getOrgAttribute();
        if (userRole < 0) {
            return NewResponseUtil.makeFail("人员未设置对应的级别");
        } else if (userRole == OrganizationUserEnums.UserRoleEnum.normal.getCode()
                && nodeAttribute != OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode()) {
            log.warn("当前账号为非管理岗，不允许在非最小层级,mis:{}", memberOrgWrapper.getCfVolunteer().getMis());
            return NewResponseUtil.makeFail("当前账号为非管理岗，不允许在非最小层级");
        } else if (userRole == OrganizationUserEnums.UserRoleEnum.manager.getCode()
                && nodeAttribute != OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode()) {
            log.warn("当前账号为管理岗，不允许在最小层级,mis:{}", memberOrgWrapper.getCfVolunteer().getMis());
            return NewResponseUtil.makeFail("当前账号为管理岗，不允许在最小层级");
        }
        //渠道经理判断
        Response<Boolean> response = checkPartnerManagerAdd();
        if (response.notOk()) {
            return response;
        }
        //直营架构组织判断
        Response<Boolean> selfOrgAdd = checkSelfOrgAdd();
        if (selfOrgAdd.notOk()) {
            return selfOrgAdd;
        }
        //蜂鸟计划组织判断
        Response<Boolean> partOrgAdd = checkPartnerOrgAdd();
        if (partOrgAdd.notOk()) {
            return partOrgAdd;
        }
        return NewResponseUtil.makeSuccess(true);
    }

    /**
     * 蜂鸟计划不能添加"业务经理"、"普通职员"
     *
     * @return
     */
    private Response<Boolean> checkPartnerOrgAdd() {
        int level = memberOrgWrapper.getCfVolunteer().getLevel();
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(level);
        //渠道直营顾问可以加入到蜂鸟计划组织中
        boolean directChannel = CrowdfundingVolunteerUtil.isDirectChannel(memberOrgWrapper.getCfVolunteer());
        //蜂鸟计划不能添加"业务经理"、"普通职员"
        BdCrmOrganizationDO bdCrmOrganizationDO = memberOrgWrapper.getBdCrmOrganizationDO();
        List<Long> partnerOrg = crmOrganizationService.listAllPartnerOrg();
        if (partnerOrg.contains(bdCrmOrganizationDO.getId()) && CrowdfundingVolunteerEnum.excludePartnerOrg.contains(roleEnum)) {
            //如果是渠道直营不限制
            if (!directChannel) {
                return NewResponseUtil.makeFail(String.format("%s不能加入蜂鸟计划组织", Optional.ofNullable(roleEnum).map(CrowdfundingVolunteerEnum.RoleEnum::getDesc).orElse("")));
            }
        }
        return NewResponseUtil.makeSuccess(true);
    }

    /**
     * 自有组织不能添加筹款伙伴
     *
     * @return
     */
    private Response<Boolean> checkSelfOrgAdd() {
        int level = memberOrgWrapper.getCfVolunteer().getLevel();
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(level);
        //筹款伙伴不能加到含有直营城市下的组织
        BdCrmOrganizationDO bdCrmOrganizationDO = memberOrgWrapper.getBdCrmOrganizationDO();
        List<Long> ownOrg = crmOrganizationService.listAllOwnOrg();
        //如果是渠道直营顾问不支持加入到直营城市和顾问生态
        boolean directChannel = CrowdfundingVolunteerUtil.isDirectChannel(memberOrgWrapper.getCfVolunteer());
        if (ownOrg.contains(bdCrmOrganizationDO.getId()) && directChannel) {
            return NewResponseUtil.makeFail("渠道直营人员不支持挂在该架构！");
        }
        List<Long> ecologicalOperationOrgIds = crmOrganizationService.listAllConsultantEcologicalOperationOrg();
        if (ecologicalOperationOrgIds.contains(bdCrmOrganizationDO.getId()) && directChannel) {
            return NewResponseUtil.makeFail("渠道直营人员不支持挂在该架构！");
        }
        if (ownOrg.contains(bdCrmOrganizationDO.getId()) && CrowdfundingVolunteerEnum.excludeOwnOrg.contains(roleEnum)) {
            return NewResponseUtil.makeFail(String.format("%s不能加入直营城市组织", Optional.ofNullable(roleEnum).map(CrowdfundingVolunteerEnum.RoleEnum::getDesc).orElse("")));
        }
        return NewResponseUtil.makeSuccess(true);
    }


    private Response<Boolean> memberHadAddTOtherOrg(MemberOrgRelationParam memberOrgRelationParam) {
        int userRole = memberOrgWrapper.getUserRole();
        CrowdfundingVolunteer cfVolunteer = memberOrgWrapper.getCfVolunteer();
        List<BdCrmOrgUserRelationDO> relationDOList = crmOrganizationRelationService.listMemberOrgRelationByUniqueCode(cfVolunteer.getUniqueCode());
        if (userRole < 0) {
            return NewResponseUtil.makeFail(String.format("人员%s未设置对应的级别", cfVolunteer.getMis()));
        }
        List<Long> orgIds = relationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList());
        //判断人员是否已经在当前组织中
        if (orgIds.contains(memberOrgRelationParam.getOrgId())) {
            return NewResponseUtil.makeFail(String.format("%s已经在当前组织下", cfVolunteer.getVolunteerName()));
        }
        if (userRole == OrganizationUserEnums.UserRoleEnum.normal.getCode()) {
            if (CollectionUtils.isNotEmpty(relationDOList)) {
                BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO = relationDOList.get(0);
                crmOrganizationService.getCurrentOrgById(bdCrmOrgUserRelationDO.getOrgId());
                Map<Long, String> orgIdTChainMsg = crmOrganizationService.listChainByOrgIdsWithDefaultSplitter(relationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList()));
                return NewResponseUtil.makeFail(String.format("%s已在%s组织下，不允许重复添加", cfVolunteer.getVolunteerName(), Joiner.on(",").join(orgIdTChainMsg.values())));
            }
        }
        if (userRole == OrganizationUserEnums.UserRoleEnum.normal.getCode()) {
            Set<Integer> userRoles = relationDOList.stream()
                    .map(BdCrmOrgUserRelationDO::getUserRole)
                    .collect(Collectors.toSet());
            if (userRoles.size() > 1) {
                log.warn("当前用户:{}既作为管理又作为顾问绑定组织,需要修改数据", memberOrgWrapper.getCfVolunteer().getMis());
                return NewResponseUtil.makeFail("当前用户既作为管理又作为顾问绑定组织,请联系研发后再操作");
            }
        }

        return NewResponseUtil.makeSuccess(true);
    }


    private Response<Boolean> checkPartnerManagerAdd() {
        CrowdfundingVolunteer cfVolunteer = memberOrgWrapper.getCfVolunteer();
        int level = memberOrgWrapper.getCfVolunteer().getLevel();
        boolean normalRole = Objects.equals(memberOrgWrapper.getCfVolunteer().getPartnerTag(), CrowdfundingVolunteerEnum.PartnerTagEnum.normal.getCode());
        boolean partnerManager = CrowdfundingVolunteerEnum.partnerManager.contains(level);
        if (normalRole && !Objects.equals(level, CrowdfundingVolunteerEnum.RoleEnum.PRACTICE_AID.getLevel())
                && StringUtils.isBlank(cfVolunteer.getMis())) {
            return NewResponseUtil.makeFail("正式员工没有mis账号不能加入组织");
        }
        //其他角色（非顾问正式角色）能正常加入组织
        if (!partnerManager && normalRole && !Objects.equals(level, CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel())) {
            return NewResponseUtil.makeSuccess(true);
        }
        //渠道经理只能加到含有蜂鸟计划下的组织
        BdCrmOrganizationDO bdCrmOrganizationDO = memberOrgWrapper.getBdCrmOrganizationDO();
        List<Long> partnerOrg = crmOrganizationService.listAllPartnerOrg();
        if (!partnerOrg.contains(bdCrmOrganizationDO.getId()) && partnerManager) {
            return NewResponseUtil.makeFail(CrowdfundingVolunteerEnum.RoleEnum.parse(level).getDesc() +
                    "只能加入到蜂鸟计划组织中");
        }
        //渠道直营顾问可以加入到蜂鸟计划组织中
        boolean directChannel = CrowdfundingVolunteerUtil.isDirectChannel(cfVolunteer);
        if (!directChannel && partnerOrg.contains(bdCrmOrganizationDO.getId())
                && Objects.equals(level, CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel())) {
            return NewResponseUtil.makeFail("正式顾问不能加入蜂鸟计划组织中");
        }
        return crmMemberInfoService.canAddPartnerManager(bdCrmOrganizationDO.getId(), level);
    }

}
