package com.shuidihuzhu.cf.cfgrowthtoolapi.service.delegate.impl;

import com.baidu.aip.ocr.AipOcr;
import com.mysql.cj.util.StringUtils;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.delegate.IOutFeignServiceDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospital;
import com.shuidihuzhu.cf.model.dedicated.VolunteerIdCardModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.json.JSONObject;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

import com.qcloud.cos.utils.IOUtils;


@Component
@Slf4j
@RefreshScope
public class OutFeignServiceDelegateImpl implements IOutFeignServiceDelegate {

    //设置APPID/AK/SK免费账号
    private static final String APP_ID_FACE = "11433303";
    private static final String API_KEY_FACE = "0NFVt52Gt52Uv8xt4662WbGF";
    private static final String SECRET_KEY_FACE = "PO2En1zR13o6tDzYlLGkqs6raogyQrIf";
    private static final String APP_ID_OCR = "10948171";
    private static final String API_KEY_OCR = "EQY2swgAqqQrk0kzV1QYPcre";
    private static final String SECRET_KEY_OCR = "GKXEUP3r8uxBCfVAhs4a7ZfIyu7IU4vS";
    private static AipOcr clientOcr = null;
    static{
        try {
            clientOcr = new AipOcr(APP_ID_OCR, API_KEY_OCR, SECRET_KEY_OCR);
            clientOcr.setSocketTimeoutInMillis(60000);
        }catch (Exception e){
            log.error("BaiduPhotoAiDelegateImpl AipOcr client Init Exception",e);
        }
    }

    @Override
    public CfHospital selectHospitalByName(String hosiptalName) {
        try{
            //todo 补充具体实现
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+" selectHospitalByName Exception request:{}",hosiptalName,e);
        }
        return null;
    }


    @Override
    public OpResult<VolunteerIdCardModel> getVolunteerIdCardByPhoto(String photoUrl){
        try{
            HashMap<String,String> options = new HashMap<>();
            options.put("detect_direction","true");
            options.put("detect_risk","false");
            JSONObject res = clientOcr.idcard(getStreamByte(photoUrl),"front",options);
            log.info("clientOcr.idcard request:{} response:{}",photoUrl,res);
            if(res==null){
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_BACK_IMAGE_STATUS_FAIL);
            }
            String imageStatus = res.getString("image_status");
            if(StringUtils.isNullOrEmpty(imageStatus)){
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_BACK_IMAGE_STATUS_FAIL);
            }
            if(imageStatus.compareTo("normal")!=0){
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_IMAGE_STATUS_FAIL);
            }
            JSONObject wordsResult = res.getJSONObject("words_result");
            if(wordsResult==null){
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_IMAGE_STATUS_FAIL);
            }
            JSONObject idCardNoJSON = wordsResult.getJSONObject("公民身份号码");
            if(idCardNoJSON==null){
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_IMAGE_STATUS_FAIL);
            }
            VolunteerIdCardModel volunteerIdCardModel = new VolunteerIdCardModel();
            String idCard = idCardNoJSON.getString("words");
            if(StringUtils.isNullOrEmpty(idCard)){
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_IMAGE_STATUS_FAIL);
            }
            volunteerIdCardModel.setIdCardNo(idCardNoJSON.getString("words"));
            JSONObject nameJson=wordsResult.getJSONObject("姓名");
            if(nameJson==null){
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_IMAGE_STATUS_FAIL);
            }
            volunteerIdCardModel.setName(nameJson.getString("words"));

            JSONObject birthJson = wordsResult.getJSONObject("出生");
            if(birthJson!=null){
                String birth = birthJson.getString("words");
                if(StringUtils.isNullOrEmpty(birth)){
                    try{
                        Date birthDate = DateUtils.parseDate(birth,"yyyyMMdd");
                        int age = DateUtils.truncatedCompareTo(DateUtil.getCurrentDate(),birthDate, Calendar.YEAR);
                        volunteerIdCardModel.setAge(age);
                    }catch (Exception e){
                        log.error("clientOcr.idcard {} birth Exception",photoUrl,e);
                    }
                }
            }
            return OpResult.createSucResult(volunteerIdCardModel);
        }catch (Exception e){
            log.error("clientOcr.idcard request:{} execute Exception",photoUrl,e);
        }
        return OpResult.createFailResult(CfErrorCode.VOLUNTEER_IMAGE_STATUS_FAIL);
    }

    @Override
    public OpResult checkVolunteerIdCardBackPhoto(String photoUrl) {
        try{
            HashMap<String,String> options = new HashMap<>();
            options.put("detect_direction","true");
            options.put("detect_risk","false");
            JSONObject res = clientOcr.idcard(getStreamByte(photoUrl),"back",options);
            if(res==null){
                return null;
            }
            String imageStatus = res.getString("image_status");
            if(StringUtils.isNullOrEmpty(imageStatus)){
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_BACK_IMAGE_STATUS_FAIL);
            }
            if(imageStatus.compareTo("normal")!=0){
                return OpResult.createFailResult(CfErrorCode.VOLUNTEER_BACK_IMAGE_STATUS_FAIL);
            }
            return OpResult.createSucResult();
        }catch (Exception e){
            log.error("clientOcr.idcard back request:{} execute Exception",photoUrl,e);
        }
        return OpResult.createFailResult(CfErrorCode.VOLUNTEER_BACK_IMAGE_STATUS_FAIL);
    }

    private static byte[] getStreamByte(String fileURL) throws Exception {
        InputStream inputStream = null;
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        if (fileURL.startsWith("http") || fileURL.startsWith("https")) {
            URL url = new URL(fileURL);
            inputStream = url.openStream();
        } else {
            inputStream = new FileInputStream(fileURL);
        }
        IOUtils.copy(inputStream, output);
        return output.toByteArray();
    }
}
