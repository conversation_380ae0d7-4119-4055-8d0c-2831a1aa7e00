package com.shuidihuzhu.cf.cfgrowthtoolapi.service.greenchannel;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.greenchannel.CfGreenChannelApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.greenchannel.*;

import java.util.List;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2020-10-23 5:05 下午
 **/
public interface IGreenChannelApproveService {

    int create(CfGreenChannelApproveDO greenChannelApproveDO);

    //更新审核信息,审核通过
    void pass(CfGreenChannelApproveDO approveDO, String operator, String operatorMis, String fillInfo);

    void reject(ApproveRejectParamModel rejectParamModel, String operator, String operatorMis);

    void resetStepHandleStatus(int applyId, int stepCode, String backLastComment, String operator);

    //获取当前操作是在哪个环节
    CfGreenChannelApproveDO getCurrentStepCode(int applyId);


    /**
     * 判断是否能进行返回上一步
     * @param approveBaseParamModel
     * @return false:不能返回上一级, true:能返回上一级
     */
    boolean canBackToLastStep(ApproveBaseParamModel approveBaseParamModel);

    //根据applyId + stepCode -> ApplyDo
    CfGreenChannelApproveDO getByApplyIdAndStepCode(int applyId, int stepCode);

    List<CfGreenChannelApproveDO> listByApplyId(int applyId);

    void handleCaseEnd(int caseId);

    CfGreenChannelApproveDO getById(int id);

    Map<Long, List<CfGreenChannelApproveDO>> listByApplyIds(List<Long> applyIds);

    void pushMsgToApproveHandler(CfGreenChannelApproveDO greenChannelApproveDO, GreenChannelApproveServiceImpl.ActionType actionType, String backLastComment, String operator);
}
