package com.shuidihuzhu.cf.cfgrowthtoolapi.service.kuaishou.Impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfKuaiShouApplyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfKuaiShouApplyEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.export.impl.CfBdCrmExcelExpoetUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfKuaiShouApplyExportVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.kuaishou.CfKuaiShouApplyRecordModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.kuaishou.CfKuaiShouApplyResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.CfKuaiShouApplySearchParams;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.kuaishou.CfKuaiShouApplyService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.kuaishou.IBdKuaiShouApplyRecordService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/8/25 7:46 PM
 */
@Service
@Slf4j
public class CfKuaiShouApplyServiceImpl implements CfKuaiShouApplyService {

    @Autowired
    private IBdKuaiShouApplyRecordService bdKuaiShouApplyRecordService;

    @Autowired
    private ICrmSelfBuiltOrgReadService organizationService;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private CfBdCrmExcelExpoetUtil cfBdCrmExcelExpoetUtil;

    @Override
    public OpResult<String> addKuaiShouApplyRecord(CfKuaiShouApplyRecordModel kuaiShouApplyRecordModel, CrowdfundingVolunteer volunteer) {
        CfKuaiShouApplyDO cfKuaiShouApplyDO = new CfKuaiShouApplyDO();
        String infoUuid = kuaiShouApplyRecordModel.getInfoUuid();
        CfBdCaseInfoDo bdCaseInfoByInfoUuid = cfBdCaseInfoService.getBdCaseInfoByInfoUuid(infoUuid);
        if (Objects.isNull(bdCaseInfoByInfoUuid)) {
            log.info("未查询到案例信息:{}", infoUuid);
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        CfKuaiShouApplyDO applyRecord = bdKuaiShouApplyRecordService.getKuaiShouApplyByInfoId(infoUuid);
        if (Objects.nonNull(applyRecord) && Objects.equals(applyRecord.getDealResult(), CfKuaiShouApplyEnums.dealResultEnum.wait_apply.getCode())) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.KUAISHOU_APPLY_HAVE);
        }
        cfKuaiShouApplyDO.setInfoUuid(infoUuid);
        cfKuaiShouApplyDO.setPatientName(bdCaseInfoByInfoUuid.getPatientName());
        cfKuaiShouApplyDO.setCaseStartTime(bdCaseInfoByInfoUuid.getCreateTime());
        cfKuaiShouApplyDO.setOrgId(bdCaseInfoByInfoUuid.getOrgId());
        cfKuaiShouApplyDO.setUniqueCode(volunteer.getUniqueCode());
        cfKuaiShouApplyDO.setVolunteerName(volunteer.getVolunteerName());
        cfKuaiShouApplyDO.setAccountType(kuaiShouApplyRecordModel.getAccountType());
        kuaiShouApplyRecordModel.setPhone(oldShuidiCipher.aesEncrypt(kuaiShouApplyRecordModel.getPhone()));
        cfKuaiShouApplyDO.buildCfKuaiShouApplyDo(kuaiShouApplyRecordModel);
        // 保存快手筹款申请
        bdKuaiShouApplyRecordService.insert(cfKuaiShouApplyDO);
        return OpResult.createSucResult(CfGrowthtoolErrorCode.SUCCESS.getMsg());
    }

    @Override
    public OpResult<CfKuaiShouApplyDO> getKuaiShouApplyDetail(String infoId) {
        CfKuaiShouApplyDO kuaiShouApplyDetail = bdKuaiShouApplyRecordService.getKuaiShouApplyByInfoId(infoId);
        if (Objects.isNull(kuaiShouApplyDetail)) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        kuaiShouApplyDetail.setPhone(shuidiCipher.decrypt(kuaiShouApplyDetail.getPhone()));
        return OpResult.createSucResult(kuaiShouApplyDetail);
    }

    @Override
    public CommonPageModel<CfKuaiShouApplyResultModel> getApplyRecordList(CfKuaiShouApplySearchParams params) {
        CommonPageModel<CfKuaiShouApplyResultModel> pageModel = new CommonPageModel<>();
        this.getAddDayTime(params);
        if (StringUtils.isNotBlank(params.getPhone())) {
            params.setPhone(oldShuidiCipher.aesEncrypt(params.getPhone()));
        }
        // 获取当前组织下级所有组织
        if (CollectionUtils.isNotEmpty(params.getOrgIds())) {
            List<Long> allSubOrgIds = Lists.newArrayList();
            for (Long orgId : params.getOrgIds()) {
                allSubOrgIds.addAll(crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(orgId)
                        .stream()
                        .map(BdCrmOrganizationDO::getId)
                        .collect(Collectors.toList()));

            }
            params.setOrgIds(allSubOrgIds);
        }

        if (StringUtils.isEmpty(params.getApplyStartTime()) && StringUtils.isEmpty(params.getApplyEndTime()) && StringUtils.isEmpty(params.getDealStartTime()) && StringUtils.isEmpty(params.getDealEndTime())) {
            Date endTime = new Date();
            Date startTime = DateUtil.addDays(endTime, -90);
            String applyStartTime = DateUtil.formatDateTime(startTime);
            String applyEndTime = DateUtil.formatDateTime(endTime);
            params.setApplyStartTime(applyStartTime);
            params.setApplyEndTime(applyEndTime);
        }

        Long count = bdKuaiShouApplyRecordService.getCfKuaiShouApplyCountByParams(params);
        if (count == 0) {
            return pageModel;
        }
        List<CfKuaiShouApplyDO> resultByParams = bdKuaiShouApplyRecordService.getCfKuaiShouApplyResultByParams(params);
        pageModel.setCount(count.intValue());
        pageModel.setList(this.buildResultModel(resultByParams));
        return pageModel;
    }

    @Override
    public void modifyStatus(Long id, Integer dealResult, List<Integer> dealReasons, String name, String otherReason) {
        String dealReson = StringUtils.join(dealReasons.toArray(), ",");
        bdKuaiShouApplyRecordService.modifyStatus(id, dealResult, dealReson, new Date(), name, otherReason);
        // 给顾问发消息
        CfKuaiShouApplyDO kuaiShouApplyById = bdKuaiShouApplyRecordService.getKuaiShouApplyById(id);
        CrowdfundingVolunteer volunteer = cfVolunteerService.getOnWorkVolunteerByUniqueCode(kuaiShouApplyById.getUniqueCode());
        if (dealResult.equals(CfKuaiShouApplyEnums.dealResultEnum.pass_apply.getCode())) {
            appPushCrmCaseMsgService.sendKuaiShouApplyAgreeMsg(volunteer, kuaiShouApplyById);
        } else {
            appPushCrmCaseMsgService.sendKuaiShouApplyRejectMsg(volunteer, kuaiShouApplyById);
        }
    }

    @Override
    public void exportApplyRecord(CfKuaiShouApplySearchParams params, HttpServletResponse response, long adminLongUserId) {
        List<CfKuaiShouApplyDO> resultByParams = bdKuaiShouApplyRecordService.getCfKuaiShouApplyResultByParams(this.getAddDayTime(params));
        List<CfKuaiShouApplyResultModel> resultModels = this.buildResultModel(resultByParams);
        ArrayList<CfKuaiShouApplyExportVo> exportApplyVoList = Lists.newArrayList();
        for (CfKuaiShouApplyResultModel resultModel : resultModels) {
            exportApplyVoList.add(CfKuaiShouApplyExportVo.buildModel(resultModel));
        }

        //构造表头
        List<String> headerList = Lists.newArrayList();
        headerList.add("id");
        headerList.add("infoUuid");
        headerList.add("patientName");
        headerList.add("caseStartTime");
        headerList.add("volunteerName");
        headerList.add("volunteerOrganization");
        headerList.add("accountId");
        headerList.add("accountName");
        headerList.add("dealReason");
        headerList.add("dealResult");
        headerList.add("accountType");
        headerList.add("dealTime");
        headerList.add("dealName");
        headerList.add("phone");
        headerList.add("accountFans");
        headerList.add("createTime");

        //调用导出
        cfBdCrmExcelExpoetUtil.exportV3(adminLongUserId, exportApplyVoList, headerList, CfKuaiShouApplyExportVo.class, "快手筹款申请");

    }

    @Override
    public boolean getIsApplyRecord(String infoId) {
        CfKuaiShouApplyDO kuaiShouApplyById = bdKuaiShouApplyRecordService.getKuaiShouApplyByInfoId(infoId);
        // 判断当前案例有申请记录
        if (!Objects.isNull(kuaiShouApplyById)) {
            return true;
        }
        return false;
    }

    private List<CfKuaiShouApplyResultModel> buildResultModel(List<CfKuaiShouApplyDO> cfKuaiShouApplyDOList) {
        List<CfKuaiShouApplyResultModel> cfKuaiShouApplyResultList = Lists.newArrayList();
        for (CfKuaiShouApplyDO resultByParam : cfKuaiShouApplyDOList) {
            CfKuaiShouApplyResultModel cfKuaiShouApplyResultModel = new CfKuaiShouApplyResultModel();
            BeanUtils.copyProperties(resultByParam, cfKuaiShouApplyResultModel);
            cfKuaiShouApplyResultModel.setPhone(shuidiCipher.decrypt(cfKuaiShouApplyResultModel.getPhone()));
            Map<Long, String> orgIdToPath = organizationService.listChainByOrgIdsWithDefaultSplitter(Lists.newArrayList(resultByParam.getOrgId()));
            if (MapUtils.isEmpty(orgIdToPath)) {
                continue;
            }
            cfKuaiShouApplyResultModel.setVolunteerOrganization(orgIdToPath.get(resultByParam.getOrgId()));
            cfKuaiShouApplyResultList.add(cfKuaiShouApplyResultModel);
        }
        return cfKuaiShouApplyResultList;
    }

    private CfKuaiShouApplySearchParams getAddDayTime(CfKuaiShouApplySearchParams params) {
        if (StringUtils.isBlank(params.getApplyEndTime()) || StringUtils.isBlank(params.getDealEndTime())) {
            return params;
        }
        params.setApplyEndTime(DateUtil.getYmdhmsFromTimestamp(DateUtil.addDays(DateUtil.parseDateTime(params.getDealEndTime()), 1).getTime()));
        params.setDealEndTime(DateUtil.getYmdhmsFromTimestamp(DateUtil.addDays(DateUtil.parseDateTime(params.getDealEndTime()), 1).getTime()));
        return params;
    }


}
