package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AccompanyPatientDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.QrCodeUtils;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.api.chaifenbeta.growthtool.PayInfoFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.shuidihuzhu.cf.dao.bdcrm.CfCrmAccompanyPatientMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmAccompanyPatientDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfCrmAccompanyPatientService;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2021/7/6 下午5:45
 */
@Service
@Slf4j
public class CfCrmAccompanyPatientServiceImpl implements CfCrmAccompanyPatientService {

    @Resource
    private CfCrmAccompanyPatientMapper cfCrmAccompanyPatientMapper;
    @Resource
    private ApolloService apolloService;

    @Resource
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private PayInfoFeignClient payInfoFeignClient;

    @Override
    public int insert(CfCrmAccompanyPatientDO record) {
        return cfCrmAccompanyPatientMapper.insert(record);
    }

    @Override
    public void savePatient(CfCrmAccompanyPatientDO cfCrmAccompanyPatientDO, String phone) {
        String openId = getOpenId(cfCrmAccompanyPatientDO.getUserId(), cfCrmAccompanyPatientDO.getThirdType());
        cfCrmAccompanyPatientDO.setOpenId(openId);
        cfCrmAccompanyPatientMapper.insert(cfCrmAccompanyPatientDO);
        // 发送消息 给提醒群
        sendMsg2AlarmGroup(cfCrmAccompanyPatientDO, phone);

    }

    private String getOpenId(long userId, int userThirdType) {
        Response<String> response = payInfoFeignClient.getOpenId(userId, userThirdType);
        log.info("payInfoFeignClient getOpenId response:{}, userId:{}, userThirdType:{}", response, userId, userThirdType);
        if (response.notOk() || response.getData() == null) {
            return "";
        }
        return response.getData();
    }

    private void sendMsg2AlarmGroup(CfCrmAccompanyPatientDO patientDO, String phone) {
        long count = cfCrmAccompanyPatientMapper.countByEncryptPhone(patientDO.getEncryptPhone());
        // 表中条数如果已大于 提醒次数  则不再发消息
        if (count > apolloService.getAccompanyPatientMsgAlarmNum()) return;
        if (count>1) {
            phone = String.format("%s (今日多次申请)", phone);
        }
        String alarmContent = "【陪诊服务申请】" +
                "\n" +
                String.format("患者姓名：%s", patientDO.getPatientName()) +
                "\n" +
                String.format("患者手机号：%s", phone) +
                "\n" +
                String.format("患者状态：%s", patientDO.getPatientStatus()) +
                "\n" +
                String.format("疾病名称：%s", patientDO.getDiseaseName())+
                "\n" +
                String.format("就诊城市：%s", patientDO.getCityName())+
                "\n" +
                String.format("就诊医院：%s", patientDO.getHospitalName())+
                "\n" +
                String.format("其他服务项：%s", patientDO.getServiceLabels().replaceAll(",", "、"));;

        List<Long> orgIds = crmSelfBuiltOrgReadService.queryOrgIdListByCities(Lists.newArrayList(patientDO.getCityName()));

        Map<Long, String> orgNameMap = crmSelfBuiltOrgReadService.listChainByOrgIdsWithDefaultSplitter(orgIds);

        if(CollectionUtils.isEmpty(orgIds) || MapUtils.isEmpty(orgNameMap)){
            AlarmBotService.sentText(GeneralConstant.ACCOMPANY_PATIENT_GROUP_ID, alarmContent, null, null);
            return;
        }

        boolean containLiangguang = orgNameMap.values().stream().anyMatch(r->{return  r.contains("广东")||r.contains("广西");});
        boolean containShanxi = orgNameMap.values().stream().anyMatch(r->{return  r.contains("陕西");});
        boolean containBeiJing = orgNameMap.values().stream().anyMatch(r -> { return r.contains("北京") || r.contains("北京市"); });

        if(containLiangguang){
            AlarmBotService.sentText(GeneralConstant.ACCOMPANY_PATIENT_GROUP_ID, alarmContent, new String[]{"chenpinlin"}, null);
        } else if (containShanxi) {
            AlarmBotService.sentText(GeneralConstant.ACCOMPANY_PATIENT_GROUP_ID, alarmContent, new String[]{"sunyiming"}, null);
        } else if (containBeiJing) {
            AlarmBotService.sentText(GeneralConstant.ACCOMPANY_PATIENT_GROUP_ID, alarmContent, new String[]{"songwenbo"}, null);
        } else {
            AlarmBotService.sentText(GeneralConstant.ACCOMPANY_PATIENT_GROUP_ID, alarmContent, null, null);
        }
    }


}
