package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfOperatingRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OperateLogSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OperatorLogVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.CfMsgModelRecordVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.MsgQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.OperatingRecordQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import com.shuidihuzhu.cf.dao.CfOperatingRecordDao;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-08-10
 */

@Service
@Slf4j
public class OperateLogServiceImpl implements IOperateLogService {

    @Autowired
    private CfOperatingRecordDao cfOperatingRecordDao;

    @Override
    public int saveOperateLog(CfOperatingRecordDO cfOperatingRecordDO) {
        return cfOperatingRecordDao.saveOperateLog(cfOperatingRecordDO);
    }
    @Override
    public OpResult<CommonResultModel<CfMsgModelRecordVO>> listMsgModelRecord(MsgQueryParam msgQueryParam) {
        if (Objects.isNull(msgQueryParam.getModelId())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        CommonResultModel<CfMsgModelRecordVO> commonResultModel = new CommonResultModel<CfMsgModelRecordVO>();
        OperatingRecordQueryParam queryParam = new OperatingRecordQueryParam();
        queryParam.setOperateKey(String.valueOf(msgQueryParam.getModelId()));
        queryParam.setOperateTypeList(OperateTypeEnum.getEnumListByAttriButeType(OperateTypeEnum.SAVE_MS_MODEL.getAttributeType()));
        queryParam.setOffset(msgQueryParam.getOffset());
        queryParam.setPageSize(msgQueryParam.getPageSize());
        int count = cfOperatingRecordDao.countOperateLogByParam(queryParam);
        commonResultModel.setTotal(count);
        if (count == 0) {
            return OpResult.createSucResult(commonResultModel);
        }
        List<CfOperatingRecordDO> operateRecordList = cfOperatingRecordDao.operatelogListByParam(queryParam);
        List<CfMsgModelRecordVO> recordList = Lists.newArrayList();
        operateRecordList.forEach(item -> {
            recordList.add(item.buildCfMsgModelRecordVO());
        });
        commonResultModel.setModelList(recordList);
        return OpResult.createSucResult(commonResultModel);
    }

    @Override
    public int countRecordByQueryParam(OperatingRecordQueryParam queryParam) {
        if (queryParam == null){
            return 0;
        }
        return cfOperatingRecordDao.countOperateLogByParam(queryParam);
    }

    @Override
    public OpResult<List<CfOperatingRecordDO>> listRecordByQueryParam(OperatingRecordQueryParam queryParam) {
        if (queryParam == null){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CfOperatingRecordDO> recordDOList = cfOperatingRecordDao.operatelogListByParam(queryParam);
        return OpResult.createSucResult(recordDOList);
    }

    @Override
    public List<CfOperatingRecordDO> listOptLogByOptKeyAndOptType(String optKey, OperateTypeEnum operateTypeEnum) {
        return cfOperatingRecordDao.listOperatelogByOptKeyAndOptType(optKey,operateTypeEnum.getType());
    }

    @Override
    public CommonResultModel<OperatorLogVO> getOperateLog(OperateLogSearchModel searchModel, List<Integer> operateTypeList) {
        CommonResultModel commonResultModel= new CommonResultModel();
        OperatingRecordQueryParam queryParam = new OperatingRecordQueryParam();
        queryParam.setOperateKey(searchModel.getOperateKey()==null?null:String.valueOf(searchModel.getOperateKey()));
        queryParam.setOperateTypeList(operateTypeList);
        queryParam.setOffset((searchModel.getPageNo() - 1) * searchModel.getPageSize());
        queryParam.setPageSize(searchModel.getPageSize());
        int count = this.countRecordByQueryParam(queryParam);
        commonResultModel.setTotal(count);
        if (count == 0) {
            return commonResultModel;
        }
        OpResult<List<CfOperatingRecordDO>> opResult = this.listRecordByQueryParam(queryParam);
        commonResultModel.setModelList(opResult.getData().stream().map(CfOperatingRecordDO::convert).collect(Collectors.toList()));
        return commonResultModel;
    }

    @Override
    public CfOperatingRecordDO findLastAddOpt(String optKey, List<OperateTypeEnum> operateTypeEnumList) {
        if (CollectionUtils.isEmpty(operateTypeEnumList) || optKey == null) {
            return null;
        }
        return cfOperatingRecordDao.findLastAddOpt(optKey, operateTypeEnumList.stream().map(OperateTypeEnum::getType).collect(Collectors.toList()));
    }

    @Override
    public List<CfOperatingRecordDO> listByOptTypes(String optKey, List<OperateTypeEnum> operateTypeEnumList) {
        if (StringUtils.isBlank(optKey) || operateTypeEnumList == null) {
            return Lists.newArrayList();
        }
        return cfOperatingRecordDao.listByOptTypes(optKey, operateTypeEnumList.stream().map(OperateTypeEnum::getType).collect(Collectors.toList()));
    }

    @Override
    public List<CfOperatingRecordDO> listOptLogBetweenTime(List<OperateTypeEnum> operateTypeEnumList, String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime) || CollectionUtils.isEmpty(operateTypeEnumList)) {
            return Lists.newArrayList();
        }
        return cfOperatingRecordDao.listOptLogBetweenTime(operateTypeEnumList.stream().map(OperateTypeEnum::getType).collect(Collectors.toList()), startTime, endTime);
    }

    @Override
    public List<CfOperatingRecordDO> listOptLogGreaterId(String optKey, List<OperateTypeEnum> operateTypeEnumList, long greaterId) {
        if (StringUtils.isBlank(optKey) || CollectionUtils.isEmpty(operateTypeEnumList)) {
            return Lists.newArrayList();
        }
        return cfOperatingRecordDao.listOptLogGreaterId(optKey, operateTypeEnumList.stream().map(OperateTypeEnum::getType).collect(Collectors.toList()), greaterId);
    }

    @Override
    public List<CfOperatingRecordDO> listOptLogBatchOptKeys(List<String> optKeys, int optType, String startTime, String endTime) {
        if (CollectionUtils.isEmpty(optKeys)) {
            return Lists.newArrayList();
        }
        return cfOperatingRecordDao.listOptLogBatchOptKeys(optKeys, optType, startTime, endTime);
    }

    @Override
    public List<CfOperatingRecordDO> listOptLogRecordByKey(String optKey, List<OperateTypeEnum> operateTypeEnumList) {
        if (StringUtils.isEmpty(optKey) || CollectionUtils.isEmpty(operateTypeEnumList)) {
            return Lists.newArrayList();
        }
        return cfOperatingRecordDao.listOptLogRecordByOptKey(optKey, operateTypeEnumList.stream().map(OperateTypeEnum::getType).collect(Collectors.toList()));
    }

    @Override
    public List<CfOperatingRecordDO> listOptLogByOptKeys(List<String> optKeys, List<OperateTypeEnum> operateTypeEnumList) {
        if (CollectionUtils.isEmpty(optKeys) || CollectionUtils.isEmpty(operateTypeEnumList)) {
            return Lists.newArrayList();
        }
        return cfOperatingRecordDao.listOptLogByOptKeys(optKeys, operateTypeEnumList.stream().map(OperateTypeEnum::getType).collect(Collectors.toList()));
    }
}
