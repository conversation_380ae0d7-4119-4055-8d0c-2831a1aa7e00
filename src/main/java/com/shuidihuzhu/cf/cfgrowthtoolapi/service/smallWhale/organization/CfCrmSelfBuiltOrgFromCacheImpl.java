package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.*;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;

/**
 * @author: fengxuan
 * @create 2020-05-12 12:03
 **/
@Slf4j
@Primary
@Service("selfBuiltOrgFromCache")
public class CfCrmSelfBuiltOrgFromCacheImpl extends AbstractCrmSelfBuiltOrgService {


    /**
     * 获取组织所有的入口,使用缓存
     */
    @Override
    public List<BdCrmOrganizationDO> getAllOrg() {
        List<BdCrmOrganizationDO> organizationDOS = Lists.newArrayList();
        try {
            organizationDOS = Optional.ofNullable(allOrgList.get(0)).orElse(Lists.newArrayList());
        } catch (ExecutionException e) {
            organizationDOS = getAllOrgByDB();
        }
        return Lists.newCopyOnWriteArrayList(organizationDOS);
    }



}
