package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CaseReachDonateDate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.pojo.CaseTimeDO;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020-08-03
 */
public interface ICfKpiCaseBaseDataService {


    List<CfKpiCaseBaseDataDO> getCaseBaseMapByUniqueCode(String startTime, String endTime, String dayKey);


    List<CfKpiCaseBaseDataDO> listPartnerCaseBase(String uniqueCode, CaseTimeDO cfKpiCaseTimeDO);

    int insertOrUpdate(CfKpiCaseBaseDataDO cfKpiCaseBaseDataDO);

    CaseReachDonateDate selectReachDonateDate(int caseId);

    List<CfKpiCaseBaseDataDO> listByDateTimeAndUniqueCode(String dateTime, List<String> uniqueCodeList);
}
