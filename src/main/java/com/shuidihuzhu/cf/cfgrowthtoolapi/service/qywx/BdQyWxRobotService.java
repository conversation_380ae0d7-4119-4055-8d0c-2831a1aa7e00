package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdQyWxRobotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.qywx.QywxGroupRobotQueryParam;

import java.util.List;

/**
 * 企业微信机器人信息(BdQyWxRobot)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-11 11:33:18
 */
public interface BdQyWxRobotService {

    BdQyWxRobotDO queryById(long id);

    BdQyWxRobotDO queryValidById(long id);

    List<BdQyWxRobotDO> listByCaseSourceType(int caseSourceType);

    int insert(BdQyWxRobotDO bdQyWxRobot);

    int update(BdQyWxRobotDO bdQyWxRobot);

    BdQyWxRobotDO queryByExternalUserId(String externalUserId);

    List<BdQyWxRobotDO> queryByCorpId(String corpId);

    /**
     * 分页查询企微机器人列表
     *
     * @param queryParam 查询参数
     * @return 机器人列表
     */
    List<BdQyWxRobotDO> listRobotByPage(QywxGroupRobotQueryParam queryParam);

    /**
     * 查询企微机器人总数
     *
     * @param queryParam 查询参数
     * @return 总数
     */
    int countRobot(QywxGroupRobotQueryParam queryParam);

    /**
     * 切换账号状态
     *
     * @param id 机器人ID
     * @param accountStatus 账号状态
     * @return 更新行数
     */
    int switchStatus(long id, int accountStatus);

    /**
     * 根据外部用户ID列表批量查询机器人
     *
     * @param externalUserIds 外部用户ID列表
     * @return 机器人列表
     */
    List<BdQyWxRobotDO> queryByExternalUserIds(List<String> externalUserIds);
} 