package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMasterForGrowthtoolDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrmHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrmHospitalDepartmentDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CrmHospitalDepartmentModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CrmHospitalModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CrmHDDepartmentStatisticModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CrmHospitalDepartmentSimpleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CrmHDDataForOrgVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospitalv2.VHospitalCountVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICrmHospitalService;
import com.shuidihuzhu.cf.dao.bdcrm.CrmHospitalDao;
import com.shuidihuzhu.cf.dao.bdcrm.CrmHospitalDepartmentDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospital;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrmHospitalServiceImpl implements ICrmHospitalService {
    @Autowired
    private CrmHospitalDao crmHospitalDao;
    @Autowired
    private CrmHospitalDepartmentDao crmHospitalDepartmentDao;
    @Autowired
    private ICfMasterForGrowthtoolDelegate cfMasterForGrowthtoolDelegate;

    @Override
    public List<CrmHospitalDO> getHospitalByFlow(String hospitalName,Integer provinceId,Integer cityId) {
        List<CrmHospitalDO> retList = crmHospitalDao.selectByParam(null,null,hospitalName,null,provinceId,cityId,0,10);
        return retList;
    }

    @Override
    public List<CrmHospitalDO> searchHospitalByDefault(Integer offset, Integer pageSize) {
        List<CrmHospitalDO> retList = crmHospitalDao.searchHospitalByDefault(offset,pageSize);
        return retList;
    }

    @Override
    public List<CrmHospitalDO> searchHospital(String hospitalName, String city, String province, int offset, int pageSize) {
        List<CrmHospitalDO> retList = crmHospitalDao.selectByParam(city,province,hospitalName,null,null,null,offset,pageSize);
        return retList;
    }

    @Override
    public CrmHospitalDO getHospitalById(int hospitalId) {
        CrmHospitalDO ret = crmHospitalDao.getHospitalById(hospitalId);
        return ret;
    }

    @Override
    public int saveHospital(CrmHospitalDO crmHospitalDO) {
        int ret = cfMasterForGrowthtoolDelegate.insertCrmHospitalDO(crmHospitalDO);
        return ret;
    }

    @Override
    public int updateHospital(CrmHospitalDO crmHospitalDO) {
        int ret = cfMasterForGrowthtoolDelegate.updateCrmHospitalDO(crmHospitalDO);
        return ret;
    }

    @Override
    public boolean checkDepartmentIsDuplicate(int hospitalId, String name){
        int ret = crmHospitalDepartmentDao.checkHasNameDuplicate(hospitalId, name);
        return ret>0;
    }

    @Override
    public Map<Integer, List<CrmHospitalDepartmentModel>> getHospitalDeparmentByHospitalIds(List<Integer> hospitalIds) {
        if(CollectionUtils.isEmpty(hospitalIds)){
            return Maps.newHashMap();
        }
        List<List<Integer>> listList = Lists.partition(hospitalIds, GeneralConstant.MAX_PAGE_SIZE);
        List<CrmHospitalDepartmentDO> crmHospitalDepartmentDOS = listList.parallelStream().map(list -> crmHospitalDepartmentDao.getHospitalDeparmentByHospitalIds(list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).get();
        Map<Integer,List<CrmHospitalDepartmentModel>> retMap = Maps.newHashMap();
        for(CrmHospitalDepartmentDO crmHospitalDepartmentDO: crmHospitalDepartmentDOS){
            if(retMap.containsKey(crmHospitalDepartmentDO.getHospitalId())){
                retMap.get(crmHospitalDepartmentDO.getHospitalId()).add(convert(crmHospitalDepartmentDO));
            }else{
                List<CrmHospitalDepartmentModel> list = Lists.newArrayList();
                list.add(convert(crmHospitalDepartmentDO));
                retMap.put(crmHospitalDepartmentDO.getHospitalId(),list);
            }
        }
        return retMap;
    }

    @Override
    public List<CrmHospitalDO> getHospitalByCodes(List<String> hospitalCodeList) {
        if (CollectionUtils.isEmpty(hospitalCodeList)) {
            return Lists.newArrayList();
        }
        return crmHospitalDao.getHospitalByCodes(hospitalCodeList);
    }

    @Override
    public CrmHospitalDO getHospitalByName(String name) {
        return crmHospitalDao.getHospitalByName(name);
    }

    @Override
    public CrmHospitalDepartmentDO getHospitalDeparmentByName(String name,int hospitalId) {
        if(StringUtils.isBlank(name)){
            return null;
        }
        return crmHospitalDepartmentDao.getHospitalDeparmentByName(name,hospitalId);
    }

    @Override
    public int deleteHospital(Integer hospitalId) {
        int ret = cfMasterForGrowthtoolDelegate.deleteHospital(hospitalId);
        return ret;
    }

    @Override
    public List<CrmHospitalModel> getHospitalByProvinceIdWithCityId(int provinceId, int cityId){
        if (provinceId==0 || cityId==0){
            return Lists.newArrayList();
        }
        return crmHospitalDao.getHospitalByProvinceIdWithCityId(provinceId, cityId);
    }

    @Override
    public List<CrmHospitalModel> getHospitalByProvinceId(List<Integer> provinceId){
        if(CollectionUtils.isEmpty(provinceId)){
            return Lists.newArrayList();
        }
        return crmHospitalDao.getHospitalByProvinceId(provinceId);
    }

    private CrmHospitalDepartmentModel convert(CrmHospitalDepartmentDO crmHospitalDepartmentDO) {
        CrmHospitalDepartmentModel crmHospitalDepartmentModel = new CrmHospitalDepartmentModel();
        crmHospitalDepartmentModel.setBedCount(crmHospitalDepartmentDO.getBedCount());
        crmHospitalDepartmentModel.setDepartmentId(crmHospitalDepartmentDO.getId());
        crmHospitalDepartmentModel.setHospitalId(crmHospitalDepartmentDO.getHospitalId());
        crmHospitalDepartmentModel.setName(crmHospitalDepartmentDO.getName());
        return crmHospitalDepartmentModel;
    }
    @Override
    public List<CrmHospitalDepartmentSimpleModel> getCrmHospitalDepartmentSimpleModelByHospitalIds(List<Integer> hospitalIds){
        return crmHospitalDepartmentDao.getCrmHospitalDepartmentSimpleModelByHospitalIds(hospitalIds);
    }

    @Override
    public CrmHospitalDepartmentModel getHospitalDeparmentByDepartmentId(int departmentId) {
        return crmHospitalDepartmentDao.getHospitalDeparmentById(departmentId);
    }

    @Override
    public List<CrmHDDepartmentStatisticModel> getHDCountStatistic(List<Integer> hospitalIds) {
        return crmHospitalDepartmentDao.getHDCountStatistic(hospitalIds);
    }

    @Override
    public int updateHospitalVHospitalCode(Long id, String vhospitalCode) {
        return cfMasterForGrowthtoolDelegate.updateHospitalVHospitalCode(id,vhospitalCode);
    }

    @Override
    public Map<String, Integer> getVhospitalCountVo(Set<String> vhospitalCodes,Integer recommentStatus) {
        if(CollectionUtils.isEmpty(vhospitalCodes)){
            return Maps.newHashMap();
        }
        List<VHospitalCountVo> vHospitalCountVos =  crmHospitalDao.getVhospitalCountVo(vhospitalCodes,recommentStatus);
        if(CollectionUtils.isEmpty(vhospitalCodes)){
            return Maps.newHashMap();
        }
        return vHospitalCountVos.stream().collect(Collectors.toMap(VHospitalCountVo::getVhospitalCode,VHospitalCountVo::getCount));
    }

    @Override
    public List<CrmHospitalDO> useDatabaseHospitalSearch(String provinceName, String cityName, String hospitalName, String vhospitalCode,
                                                         Integer useStatus, int offset, Integer pageSize) {
//        if(StringUtils.isBlank(provinceName) && StringUtils.isBlank(cityName)
//                && StringUtils.isBlank(hospitalName) && StringUtils.isBlank(vhospitalCode) && useStatus ==null){
//            return Lists.newArrayList();
//        }
        return crmHospitalDao.useDatabaseHospitalSearch(provinceName,cityName,hospitalName,vhospitalCode,useStatus,offset,pageSize);
    }

    @Override
    public int useDatabaseHospitalSearchCount(String provinceName, String cityName, String hospitalName, String vhospitalCode, Integer useStatus) {
        return crmHospitalDao.useDatabaseHospitalSearchCount(provinceName,cityName,hospitalName,vhospitalCode,useStatus);
    }

    @Override
    public CrmHospitalDO getHospitalByNameAndProvinceCity(String province, String city, String name) {
        if(province!=null && (province.endsWith("省")||province.endsWith("市"))){
            province=province.substring(0,province.length()-1);
        }
        if(city!=null && (city.endsWith("市"))){
            city = city.substring(0,city.length()-1);
        }
        return crmHospitalDao.getHospitalByNameAndProvinceCity(province,city,name);
    }

    @Override
    public int updateStatusAndRstatusById(Long id, Integer useStatus, Integer rStatus) {
        if(useStatus==null && rStatus==null){
            return 0;
        }
        return cfMasterForGrowthtoolDelegate.updateStatusAndRstatusByHospitalId(id,useStatus,rStatus);
    }

    @Override
    public void updateHospitalVhospitalRalation(Long id, String misName, String timestamp) {
        cfMasterForGrowthtoolDelegate.updateHospitalVhospitalRalation(id,misName,timestamp);
    }

    @Override
    public CrmHospitalDO preciseMatchHospital(String hospitalName, int provinceId, int cityId) {
        if (StringUtils.isBlank(hospitalName)) {
            return null;
        }
        return crmHospitalDao.preciseMatchHospital(hospitalName, provinceId, cityId);
    }

    @Override
    public List<CrmHospitalDO> selectByCityId(int cityId) {
        if (cityId <= 0) {
            return Lists.newArrayList();
        }
        return crmHospitalDao.selectByCityId(cityId);
    }

    @Override
    public List<CrmHospitalDO> listFuzzyMatchHospital(String hospitalName, int provinceId, int cityId) {
        Integer province = provinceId > 0 ? provinceId : null;
        Integer city = cityId > 0 ? cityId : null;
        return crmHospitalDao.selectByParam(null,null, hospitalName,null, province, city,0,100);
    }

    @Override
    public List<CrmHospitalDO> getHospitalByNameAndCity(String hospitalName, String cityName) {
        return crmHospitalDao.getHospitalByNameAndCity(hospitalName,cityName);
    }

    @Override
    public int insert(String hospitalName, String cityName, String provinceName, String vhospitalCode, Integer checkStatus) {
        return cfMasterForGrowthtoolDelegate.insertByHospitalItem(hospitalName,cityName,provinceName,vhospitalCode,checkStatus);
    }
}
