package com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate;

import brave.Tracing;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCaseDayDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.BdCaseDonateTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.CfBdCaseDonateLevelConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.CfBdCaseDonateTaskConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CaseDonateEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdCfCommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdCrmCfSimpleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.donate.BdCaseDonateTaskParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.BdCaseTagService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfCaseDayDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl.CfBdCaseInfoServiceImpl;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolAsyncPoolConstants;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2022-06-29 19:30
 **/
@Slf4j
@Service
public class DonateTaskQueryService {

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrganizationService;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private IBdCaseDonateTaskService bdCaseDonateTaskService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegateImpl;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private IBdCaseDonateLevelConfigService bdCaseDonateLevelConfigService;
    @Autowired
    private IBdCaseDonateTaskConfigService bdCaseDonateTaskConfigService;

    @Autowired
    private BdCaseTagService bdCaseTagService;

    @Autowired
    private CfBdCaseInfoServiceImpl bdCaseInfoService;

    @Resource
    private CfCaseDayDataService caseDayDataService;

    @Resource(name = GrowthtoolAsyncPoolConstants.DONATE_TASK_POOL)
    private ExecutorService executorService;

    public CrmDonateTaskCountModel getCrowdfundingManage(BdCaseDonateTaskParam bdCaseDonateTaskParam, List<Long> orgIds) {
        List<String> dateTimes = GrowthtoolUtil.getDateTimeByStartTimeWithEndTime(bdCaseDonateTaskParam.getStartTime(), bdCaseDonateTaskParam.getEndTime());
        CrmDonateTaskCountModel result = new CrmDonateTaskCountModel();
        //如果管理的组织多余一个，需要展示
        boolean needSpecialShow = orgIds.size() > 1;
        for (Long singleOrgId : orgIds) {
            CrmDonateTaskCountModel taskCountModel = getSingleOrgIdModel(dateTimes, singleOrgId, bdCaseDonateTaskParam);
            if (needSpecialShow) {
                BdCrmOrganizationDO currentOrg = crmSelfBuiltOrganizationService.getCurrentOrgById(singleOrgId);
                Optional.of(taskCountModel)
                        .map(CrmDonateTaskCountModel::getDonateTaskModels)
                        .orElse(Lists.newArrayList())
                        .forEach(item -> {
                            if (item.getOrgOrMis() == 0) {
                                item.setName(currentOrg.getOrgName() + "-" + item.getName());
                            }
                        });
            }
            log.debug("组织:{}返回的数据:{}", singleOrgId, JSON.toJSONString(taskCountModel));
            //对数据做聚合
            result.calCnt(taskCountModel);
        }
        sortCrowdfundingModel(result);
        return result;
    }


    private void sortCrowdfundingModel(CrmDonateTaskCountModel result) {
        List<CrmDonateTaskModel> donateTaskModelList = Optional.ofNullable(result.getDonateTaskModels())
                .orElse(Lists.newArrayList());
        //人员需要排除重复
        Map<String, List<CrmDonateTaskModel>> uniqueKeyMap = donateTaskModelList.stream()
                .filter(item -> item.getOrgOrMis() == 1)
                .collect(Collectors.groupingBy(CrmDonateTaskModel::getUniqueKey));
        List<CrmDonateTaskModel> models = Lists.newArrayList();
        for (Map.Entry<String, List<CrmDonateTaskModel>> entry : uniqueKeyMap.entrySet()) {
            List<CrmDonateTaskModel> value = entry.getValue();
            CrmDonateTaskModel volunteerModel = value.get(0);
            models.add(volunteerModel);
        }
        //sort
        List<CrmDonateTaskModel> sortedOrgModelList = donateTaskModelList.stream()
                .filter(item -> item.getOrgOrMis() != 1).collect(Collectors.toList())
                .stream()
                .sorted(Comparator.comparing(CrmDonateTaskModel::getOrgAttribute).reversed())
                .collect(Collectors.toList());
        models.addAll(sortedOrgModelList);
        result.setDonateTaskModels(models);
    }


    private CrmDonateTaskCountModel getSingleOrgIdModel(List<String> dateTimes, long singleOrgId, BdCaseDonateTaskParam bdCaseDonateTaskParam) {
        CrmDonateTaskCountModel result = new CrmDonateTaskCountModel();
        List<CrmDonateTaskModel> donateTaskModels = Lists.newArrayList();
        //orgId为叶子结点直接展示
        CompletableFuture<List<CrmDonateTaskModel>> orgCfModelFuture = CompletableFuture.supplyAsync(() -> getOrgDonateTaskModels(dateTimes, singleOrgId, bdCaseDonateTaskParam), executorService);
        CompletableFuture<List<CrmDonateTaskModel>> volunteerModelFuture = CompletableFuture.supplyAsync(() -> getMemberDonateTaskModels(dateTimes, singleOrgId, bdCaseDonateTaskParam), executorService);
        try {
            CompletableFuture.allOf(orgCfModelFuture, volunteerModelFuture).get();
            donateTaskModels.addAll(orgCfModelFuture.get());
            donateTaskModels.addAll(volunteerModelFuture.get());
        } catch (Exception e) {
            log.error("getSingleOrgIdModel查询异常", e);
            return result;
        }
        crmMemberInfoService.resetStaffStatus(donateTaskModels);
        result.setDonateTaskModels(donateTaskModels);
        result.calByCrmDonateTaskModel();
        return result;
    }


    //下级组织中任务情况
    public List<CrmDonateTaskModel> getOrgDonateTaskModels(List<String> dateTimes, long orgId, BdCaseDonateTaskParam bdCaseDonateTaskParam) {
        boolean noLeafNode = Optional.ofNullable(crmSelfBuiltOrganizationService.getCurrentOrgById(orgId))
                .map(item -> item.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode())
                .orElse(true);
        if (!noLeafNode) {
            return Lists.newArrayList();
        }
        List<CrmDonateTaskModel> result = Lists.newArrayList();
        //获取直接子级组织
        List<BdCrmOrganizationDO> directSubOrgs = crmSelfBuiltOrganizationService.findDirectSubOrgByOrgId(orgId);
        for (BdCrmOrganizationDO directSubOrg : directSubOrgs) {
            //排除测试组织
            if (Objects.equals(directSubOrg.getId(), apolloService.getTestOrgId())) {
                continue;
            }
            //获取所有的子级节点
            List<Long> allSubOrgIds = crmSelfBuiltOrganizationService.listAllSubOrgIncludeSelf(directSubOrg.getId())
                    .stream()
                    .map(BdCrmOrganizationDO::getId)
                    .collect(Collectors.toList());

            //获取节点下的人员,去掉重复的成员
            Set<String> uniqueCodeList = crmOrganizationRelationService.listByOrgIdsFromDB(allSubOrgIds)
                    .stream()
                    .map(BdCrmOrgUserRelationDO::getUniqueCode)
                    .collect(Collectors.toSet());

            CrmDonateTaskModel donateTaskModel = bdCaseDonateTaskService.getTaskModelByOrgIds(dateTimes, allSubOrgIds);
            donateTaskModel.setUniqueKey(String.valueOf(directSubOrg.getId()));
            donateTaskModel.setName(directSubOrg.getOrgName());
            donateTaskModel.setOrgAttribute(directSubOrg.getOrgAttribute());
            donateTaskModel.setTotalStaff(uniqueCodeList.size());
            result.add(donateTaskModel);
        }
        return result;
    }


    //组织下人员的情况
    private List<CrmDonateTaskModel> getMemberDonateTaskModels(List<String> dateTimes, long orgId, BdCaseDonateTaskParam bdCrmSearchParam) {
        //查看当前组所有的人员,做一个数据的merge
        Map<String, BdCrmOrgUserRelationDO> uniqueCodeTRelation = crmOrganizationRelationService.listByOrgIds(Lists.newArrayList((long) orgId))
                .stream()
                .collect(Collectors.toMap(BdCrmOrgUserRelationDO::getUniqueCode, Function.identity(), (before, after) -> before));
        //核心
        List<CrmDonateTaskModel> donateTaskModels = bdCaseDonateTaskService.groupByUniqueCode(dateTimes, orgId, bdCrmSearchParam);
        List<String> uniqueCodes = donateTaskModels.stream().map(CrmDonateTaskModel::getUniqueKey).collect(Collectors.toList());
        //添加下没有数据的人员
        for (Map.Entry<String, BdCrmOrgUserRelationDO> item : uniqueCodeTRelation.entrySet()) {
            if (!uniqueCodes.contains(item.getKey())) {
                BdCrmOrgUserRelationDO value = item.getValue();
                CrmDonateTaskModel countModel = new CrmDonateTaskModel();
                countModel.setUniqueKey(value.getUniqueCode());
                countModel.setName(value.getMisName());
                donateTaskModels.add(countModel);
            }
        }
        //标记下是否转岗,在caseCountModels且没有离职的,不在uniqueCodeTRelation中的
        donateTaskModels.stream()
                .filter(item -> !uniqueCodeTRelation.containsKey(item.getUniqueKey()))
                .forEach(item -> {
                    item.setStaffStatus("转岗");
                });
        donateTaskModels.forEach(item -> item.setOrgOrMis(1));

        log.info(this.getClass().getSimpleName() + "getMemberDonateTaskModels result:{}", JSON.toJSONString(donateTaskModels));
        return donateTaskModels;
    }


    //根据人员查询总数
    public DonateTaskBaseModel queryByUniqueCode(List<String> dateTimes, String uniqueCode) {
        return Optional.ofNullable(bdCaseDonateTaskService.getTaskModelByUniqueCode(dateTimes, uniqueCode)).orElse(new DonateTaskBaseModel());
    }

    public DonateTaskBaseModel queryByOrgId(List<String> dateTimes, List<Long> orgIds) {
        List<Long> allSubOrgIds = Lists.newArrayList();
        for (Long orgId : orgIds) {
            allSubOrgIds.addAll(crmSelfBuiltOrganizationService.listAllSubOrgIncludeSelf(orgId)
                    .stream()
                    .map(BdCrmOrganizationDO::getId)
                    .collect(Collectors.toList()));

        }
        CrmDonateTaskModel donateTaskModel = bdCaseDonateTaskService.getTaskModelByOrgIds(dateTimes, allSubOrgIds);
        DonateTaskBaseModel donateTaskBaseModel = new DonateTaskBaseModel();
        if (donateTaskModel != null) {
            donateTaskBaseModel.setInitDonateTaskCnt(donateTaskModel.getInitDonateTaskCnt());
            donateTaskBaseModel.setFinishDonataTaskCnt(donateTaskModel.getFinishDonataTaskCnt());
            donateTaskBaseModel.setExpireDonateTaskCnt(donateTaskModel.getExpireDonateTaskCnt());
            donateTaskBaseModel.setStopDonateTaskCnt(donateTaskModel.getStopDonateTaskCnt());
        }
        return donateTaskBaseModel;
    }


    public BdCfCommonPageModel<BdCrmCfSimpleModel> listDonateTask(BdCaseDonateTaskParam bdCaseDonateTaskParam) {
        BdCfCommonPageModel<BdCrmCfSimpleModel> result = new BdCfCommonPageModel<BdCrmCfSimpleModel>();
        List<String> dateTimes = GrowthtoolUtil.getDateTimeByStartTimeWithEndTime(bdCaseDonateTaskParam.getStartTime(), bdCaseDonateTaskParam.getEndTime());
        int countTask = bdCaseDonateTaskService.pageCountTask(dateTimes, bdCaseDonateTaskParam);
        if (countTask == 0) {
            return result;
        }
        List<BdCaseDonateTaskDO> taskModelList = bdCaseDonateTaskService.pageListTask(dateTimes, bdCaseDonateTaskParam);
        //查询案例信息
        List<Integer> caseIds = taskModelList.stream().map(BdCaseDonateTaskDO::getCaseId).collect(Collectors.toList());

        //查询配置id
        List<Long> cityConfigList = taskModelList.stream().map(BdCaseDonateTaskDO::getCityConfigId).collect(Collectors.toList());

        //查询bdCaseInfo
        List<CfBdCaseInfoDo> cfBdCaseInfoDos = cfBdCaseInfoService.listCaseInfoByCaseIds(caseIds);
        cfBdCaseInfoDos.forEach(item -> item.setRaiserPhone(shuidiCipher.decrypt(item.getRaiserPhone())));
        Map<Integer, CfBdCaseInfoDo> bdCaseInfoDoMap = cfBdCaseInfoDos
                .stream()
                .collect(Collectors.toMap(CfBdCaseInfoDo::getCaseId, Function.identity(), (before, after) -> before));
        List<CrowdfundingInfo> crowdfundingInfos = crowdFundingFeignDelegateImpl.getCrowdfundingListById(caseIds);
        if (CollectionUtils.isEmpty(crowdfundingInfos)) {
            return result;
        }
        result.setCount(countTask);
        Map<Long, CfBdCaseDonateLevelConfigDO> levelConfigDOMap = bdCaseDonateLevelConfigService.listByCityConfigIds(cityConfigList)
                .stream()
                .collect(Collectors.toMap(CfBdCaseDonateLevelConfigDO::getCityConfigId, Function.identity(), (before, after) -> before));

        Map<Long, CfBdCaseDonateTaskConfigDO> taskConfigDOMap = bdCaseDonateTaskConfigService.listByCityConfigIds(cityConfigList)
                .stream()
                .collect(Collectors.toMap(CfBdCaseDonateTaskConfigDO::getCityConfigId, Function.identity(), (before, after) -> before));
        Map<Integer, CrowdfundingInfo> caseIdTCfInfo = crowdfundingInfos.stream()
                .collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (before, after) -> before));
        List<BdCrmCfSimpleModel> simpleModelList = Lists.newArrayList();
        for (BdCaseDonateTaskDO taskDO : taskModelList) {
            CfBdCaseInfoDo cfBdCaseInfoDo = bdCaseInfoDoMap.get(taskDO.getCaseId());
            if (cfBdCaseInfoDo == null) {
                continue;
            }
            BdCrmCfSimpleModel bdCrmCfSimpleModel = new BdCrmCfSimpleModel();
            bdCrmCfSimpleModel.buildByBdCaseInfo(cfBdCaseInfoDo);
            CrowdfundingInfo crowdfundingInfo = caseIdTCfInfo.get(cfBdCaseInfoDo.getCaseId());
            if (crowdfundingInfo != null) {
                bdCrmCfSimpleModel.buildByCfModel(crowdfundingInfo);
            }
            bdCrmCfSimpleModel.setHospitalName(cfBdCaseInfoDo.getHospitalName());
            bdCrmCfSimpleModel.setDepartmentName(cfBdCaseInfoDo.getDepartmentName());
            bdCrmCfSimpleModel.setDuplicateNum(cfBdCaseInfoDo.getCaseDuplicateFlag());
            bdCrmCfSimpleModel.setShareTarget(taskDO.getShareTarget());
            bdCrmCfSimpleModel.setShareCount(taskDO.getShareCount());
            bdCrmCfSimpleModel.setTaskId(taskDO.getId());
            if (!Objects.equals(taskDO.getTaskStatus(), CaseDonateEnums.DonateTaskStatusEnum.init.getCode())) {
                SimpleDonateInfo simpleDonateInfo = new SimpleDonateInfo();
                simpleDonateInfo.setName(taskDO.getVolunteerName());
                simpleDonateInfo.setTaskStatus(taskDO.getTaskStatus());
                simpleDonateInfo.setHandleStatus(CaseDonateEnums.DonateHandleStatusEnum.parseDesc(taskDO.getHandleStatus()));
                simpleDonateInfo.setTaskFailReason(taskDO.getTaskFailReason());
                simpleDonateInfo.setTaskUpdateTime(taskDO.getTaskUpdateTime());
                bdCrmCfSimpleModel.setSimpleDonateInfo(simpleDonateInfo);
            }
            bdCrmCfSimpleModel.setTaskStatus(taskDO.getTaskStatus());
            buildbdCrmSimpleModel(bdCrmCfSimpleModel, levelConfigDOMap.get(taskDO.getCityConfigId()), taskConfigDOMap.get(taskDO.getCityConfigId()));
            // 当前时间距离发起时间时间超过75h展示案例等级
            Timestamp dateCreated = cfBdCaseInfoDo.getDateCreated();
            long between = ChronoUnit.HOURS.between(dateCreated.toLocalDateTime(), LocalDateTime.now());
            if (between > 75) {
                bdCrmCfSimpleModel.setCaseLevel(levelConfigDOMap.get(taskDO.getCityConfigId()).getCaseLevel());
            }
            simpleModelList.add(bdCrmCfSimpleModel);
        }
        result.setList(simpleModelList);
        return result;
    }

    private void buildbdCrmSimpleModel(BdCrmCfSimpleModel bdCrmCfSimpleModel, CfBdCaseDonateLevelConfigDO levelConfigDO, CfBdCaseDonateTaskConfigDO taskConfigDO) {
        //计算剩余运营天数（与案例运营分类匹配“转发运营天数-已运营天数）
        if (levelConfigDO == null || taskConfigDO == null) {
            return;
        }
        int taskDays = levelConfigDO.getTaskDays();
        CfBdCaseInfoDo bdCaseInfoDo = bdCaseInfoService.getBdCaseInfoByInfoId(bdCrmCfSimpleModel.getCaseId());

        // https://wdh.feishu.cn/wiki/wikcnhar7BoQSf8qkIGDTAjasaf
        // 转发运营天数-（初审日期至当前的天数统计），如果为负数则展示0
        LocalDateTime now = LocalDateTime.now();
        int finishTask = 0;
        if (bdCaseInfoDo != null && Objects.nonNull(bdCaseInfoDo.getFirstApproveTime())) {
            LocalDateTime firstApproveTime = bdCaseInfoDo.getFirstApproveTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            long between = ChronoUnit.DAYS.between(firstApproveTime, now);
            if (between == 0) {
                between += 1;
            }
            int leaveDonateDays = (int) (taskDays - between);
            if (leaveDonateDays < 0) {
                leaveDonateDays = 0;
            }
            bdCrmCfSimpleModel.setLeaveDonateDays(leaveDonateDays);
            List<String> dateTimes = GrowthtoolUtil.getDateTimeByStartTimeWithEndTime(DateUtil.formatDate(bdCaseInfoDo.getFirstApproveTime()), DateUtil.formatDate(new Date()));
            Map<Integer, DonateDailyConfig> dailyConfigMap = levelConfigDO.parseDayConfig().stream().collect(Collectors.toMap(DonateDailyConfig::getDay, Function.identity(), (before, after) -> before));

            // 获取案例从初审通过到现在的捐单数据
            List<CfCaseDayDataDO> byCaseIdAndDayKeys = caseDayDataService.getByCaseIdAndDayKeys(bdCrmCfSimpleModel.getCaseId(), dateTimes);
            for (CfCaseDayDataDO byCaseIdAndDayKey : byCaseIdAndDayKeys) {
                LocalDateTime dayKeyTime = DateUtil.parseDate(byCaseIdAndDayKey.getDayKey()).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                DonateDailyConfig donateDailyConfig = dailyConfigMap.get((int) ChronoUnit.DAYS.between(dayKeyTime, now));
                if (Objects.nonNull(donateDailyConfig) && byCaseIdAndDayKey.getTodayShareCount() >= donateDailyConfig.getShareTarget()) {
                    finishTask++;
                }
            }
        }

        // 达成转发目标天数(运营目标达成进度):以案例为维度，统计案例从初审通过时间开始天转发次数大于等于案例运营配置对应转发次数的天数
        bdCrmCfSimpleModel.setArriveForwardDays(finishTask);
        // 目标转发天数(运营目标达成进度):根据案例分层类别、城市匹配案例运营配置的“要求目标完成天数”进行展示
        bdCrmCfSimpleModel.setTargetForwardDays(levelConfigDO.getRequireDays());
    }

    public CfDonateDataBoardModel getDonateManagerData(List<String> dateTimes, List<Long> orgIds, String uniqueCode) {
        List<Long> allSubOrgIds = Lists.newArrayList();
        CfDonateDataBoardModel cfDonateDataBoardModel = new CfDonateDataBoardModel();
        if (CollectionUtils.isNotEmpty(orgIds)) {
            for (Long orgId : orgIds) {
                allSubOrgIds.addAll(crmSelfBuiltOrganizationService.listAllSubOrgIncludeSelf(orgId)
                        .stream()
                        .map(BdCrmOrganizationDO::getId)
                        .collect(Collectors.toList()));

            }
            CfDonateUnEndCaseModel donateTaskUnEndCaseData = bdCaseDonateTaskService.getDonateUnEndCaseByOrgIds(dateTimes, allSubOrgIds);
            CfDonateEndCaseModel donateTaskEndCaseData = bdCaseDonateTaskService.getDonateEndCaseByOrgIds(dateTimes, allSubOrgIds);
            donateTaskEndCaseData.setCompleteRatio(donateTaskEndCaseData.calCompleteRatio());
            cfDonateDataBoardModel.setDonateUnEndCaseModel(donateTaskUnEndCaseData);
            cfDonateDataBoardModel.setDonateEndCaseModel(donateTaskEndCaseData);
        } else {
            CfDonateUnEndCaseModel donateUnEndCaseByUniqueCodes = bdCaseDonateTaskService.getDonateUnEndCaseByUniqueCodes(dateTimes, uniqueCode);
            CfDonateEndCaseModel donateEndCaseByUniqueCodes = bdCaseDonateTaskService.getDonateEndCaseByUniqueCodes(dateTimes, uniqueCode);
            donateEndCaseByUniqueCodes.setCompleteRatio(donateEndCaseByUniqueCodes.calCompleteRatio());
            cfDonateDataBoardModel.setDonateUnEndCaseModel(donateUnEndCaseByUniqueCodes);
            cfDonateDataBoardModel.setDonateEndCaseModel(donateEndCaseByUniqueCodes);
        }
        return cfDonateDataBoardModel;
    }

    public List<CfDonateDataBoardModel> getDonateSelfData(List<String> dateTimes, List<Long> orgIds) {
        List<CfDonateDataBoardModel> donateDataBoardModels = Lists.newArrayList();
        List<BdCrmOrgUserRelationDO> allBdCrmOrgUserRelationDOS = Lists.newArrayList();
        for (Long orgId : orgIds) {
            List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = crmOrganizationRelationService.listByOrgIds(Lists.newArrayList(orgId));
            allBdCrmOrgUserRelationDOS.addAll(bdCrmOrgUserRelationDOS);
        }
        Map<String, BdCrmOrgUserRelationDO> uniqueCodeTRelation = allBdCrmOrgUserRelationDOS.stream()
                .collect(Collectors.toMap(BdCrmOrgUserRelationDO::getUniqueCode, Function.identity(), (before, after) -> before));
        for (String uniqueCode : uniqueCodeTRelation.keySet()) {
            CfDonateDataBoardModel cfDonateDataBoardModel = new CfDonateDataBoardModel();
            CfDonateUnEndCaseModel donateUnEndCaseByUniqueCodes = bdCaseDonateTaskService.getDonateUnEndCaseByUniqueCodes(dateTimes, uniqueCode);
            CfDonateEndCaseModel donateEndCaseByUniqueCodes = bdCaseDonateTaskService.getDonateEndCaseByUniqueCodes(dateTimes, uniqueCode);
            cfDonateDataBoardModel.setVolunteerName(uniqueCodeTRelation.get(uniqueCode).getMisName());
            donateEndCaseByUniqueCodes.setCompleteRatio(donateEndCaseByUniqueCodes.calCompleteRatio());
            cfDonateDataBoardModel.setDonateEndCaseModel(donateEndCaseByUniqueCodes);
            cfDonateDataBoardModel.setDonateUnEndCaseModel(donateUnEndCaseByUniqueCodes);
            donateDataBoardModels.add(cfDonateDataBoardModel);
        }
        return donateDataBoardModels;
    }

}
