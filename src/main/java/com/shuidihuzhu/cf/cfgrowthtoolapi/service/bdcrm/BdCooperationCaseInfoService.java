package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.CfGrowthToolDS;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdCooperationCaseInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.AppealLabelSearchParam;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface BdCooperationCaseInfoService {

    int insert(BdCooperationCaseInfoDO bdCooperationCaseInfoDO);

    BdCooperationCaseInfoDO selectByCaseId(long caseId);

    void dealCooperationCaseInfo(CfBdCaseInfoDo bdCaseInfoDo);

    void updateLocalCityTag(int caseId, int localCity);

    void updateAdminApproveStatus(int caseId, int approveStatus);

    void updateApproveTime(int caseId, Date approveTime);

    List<BdCooperationCaseInfoDO> getCaseLabelBySearchParam(AppealLabelSearchParam params);

    long getCaseLabelCount(AppealLabelSearchParam params);
}
