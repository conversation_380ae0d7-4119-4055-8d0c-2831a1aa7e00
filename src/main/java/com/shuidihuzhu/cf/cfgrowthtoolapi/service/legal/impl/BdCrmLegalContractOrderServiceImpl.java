package com.shuidihuzhu.cf.cfgrowthtoolapi.service.legal.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.legal.BdCrmLegalContractOrderDO;
import com.shuidihuzhu.cf.dao.legal.BdCrmLegalContractOrderDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.legal.BdCrmLegalContractOrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 法律援助合同订单(BdCrmLegalContractOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-19 16:48:37
 */
@Service("bdCrmLegalContractOrderService")
public class BdCrmLegalContractOrderServiceImpl implements BdCrmLegalContractOrderService {
   
    @Resource
    private BdCrmLegalContractOrderDao bdCrmLegalContractOrderDao;

    @Override
    public BdCrmLegalContractOrderDO queryById(long id) {
        return bdCrmLegalContractOrderDao.queryById(id);
    }
    

    @Override
    public int insert(BdCrmLegalContractOrderDO bdCrmLegalContractOrder) {
        return bdCrmLegalContractOrderDao.insert(bdCrmLegalContractOrder);
    }

    @Override
    public int updatePayInfo(BdCrmLegalContractOrderDO bdCrmLegalContractOrder) {
        return bdCrmLegalContractOrderDao.updatePayInfo(bdCrmLegalContractOrder);
    }

    @Override
    public boolean deleteById(long id) {
        return bdCrmLegalContractOrderDao.deleteById(id) > 0;
    }

    @Override
    public BdCrmLegalContractOrderDO getByOrderId(String orderId) {
        return bdCrmLegalContractOrderDao.getByOrderId(orderId);
    }
}
