package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdSubTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdSubTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.IBdTaskCheckService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.subtask.BdSubTaskFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2023/12/28  11:19
 */
@Service
public class OneDayHelpFundraisersWithdrawDepositPreparationImpl implements IBdTaskCheckService {

    @Autowired
    private BdSubTaskFactory bdSubTaskFactory;

    @Override
    public CrmBdTaskDO.TaskTypeEnum getTaskType() {
        return CrmBdTaskDO.TaskTypeEnum.one_day_help_fundraisers_withdraw_deposit_preparation;
    }

    @Override
    public boolean checkNeedCreateTask(BdTaskContext bdTaskContext) {
        return true;
    }

    @Override
    public boolean checkTaskComplete(BdTaskContext bdTaskContext) {
        return true;
    }

    @Override
    public void createSubTask(BdSubTaskContext bdSubTaskContext) {
        //生成子任务
        bdSubTaskFactory.checkAndCreateTask(BdSubTaskContext.builder()
                .caseId(bdSubTaskContext.getCaseId())
                .parentTaskId(bdSubTaskContext.getParentTaskId())
                .cfBdCaseInfoDo(bdSubTaskContext.getCfBdCaseInfoDo())
                .taskTypeEnumList(Lists.newArrayList(CrmBdSubTaskDO.TaskTypeEnum.case_verify_three_people, CrmBdSubTaskDO.TaskTypeEnum.case_material_review_pass))
                .build());
    }

}
