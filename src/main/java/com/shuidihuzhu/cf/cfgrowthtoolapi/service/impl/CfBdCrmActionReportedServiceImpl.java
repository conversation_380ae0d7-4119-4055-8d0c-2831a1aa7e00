package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmActionReportedMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmActionReportedDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfBdCrmActionReportedService;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/5/27 上午10:44
 */
@Service
public class CfBdCrmActionReportedServiceImpl implements CfBdCrmActionReportedService{

    @Resource
    private CfBdCrmActionReportedMapper cfBdCrmActionReportedMapper;

    @Override
    public int insert(CfBdCrmActionReportedDO record) {
        return cfBdCrmActionReportedMapper.insert(record);
    }

    @Override
    public long countCurrentDayByUniqueCodeWithIconType(String startTime, String uniqueCode, int iconType){
        return cfBdCrmActionReportedMapper.countCurrentDayByUniqueCodeWithIconType(startTime, uniqueCode, iconType);
    }

    @Override
    public List<CfBdCrmActionReportedDO> listCurrentDayByUniqueCodeWithIconType(String startTime, String uniqueCode, int iconType, long num) {
        return cfBdCrmActionReportedMapper.listCurrentDayByUniqueCodeWithIconType(startTime, uniqueCode, iconType, num);
    }
}
