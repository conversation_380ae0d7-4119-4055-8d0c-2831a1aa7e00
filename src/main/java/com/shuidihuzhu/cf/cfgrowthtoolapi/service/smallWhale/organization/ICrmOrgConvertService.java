package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdcrmVolunteerOrgnizationCopyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.OrgMembersResult;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl.CfCrmVolunteerOrgnizationImpl;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.client.cf.growthtool.model.OrgInfoModel;

import java.util.Collection;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-06-15 11:02
 * 兼容之前的copy中的逻辑
 **/
public interface ICrmOrgConvertService {

    /**
     * {@link CfCrmVolunteerOrgnizationImpl#getAllUserByOrgId(int)}
     * 找到组织以及子组织下所有的人员,包含当前节点本身
     * @param orgId
     * @return
     */
    List<BdCrmVolunteerOrgnizationSimpleModel> listByOrgId(long orgId);


    List<BdCrmVolunteerOrgnizationSimpleModel> listByOrgIds(List<Long> orgIds);


    /**
     * todo: 暂时只支持一个人在一个组织下
     * {@link CfCrmVolunteerOrgnizationImpl#getUserOrganization(java.lang.String)}
     * @param mis
     * @return
     */
    AdminOrganization getUserOrganization(String mis);


    AdminOrganization getUserOrganizationByUniqueCode(String uniqueCode);

    /**
     * 需要设置下级组织的名称,直接下级组织
     * {@link CfCrmVolunteerOrgnizationImpl#getOrgMembers(int)}
     * @param parentOrgId
     * @return
     */
    OrgMembersResult getOrgMembers(int parentOrgId);

    /**
     * 获取所有的下级组织，查看下级组织中的人员
     * {@link CfCrmVolunteerOrgnizationImpl#getAllUserByOrgId(int)}
     * @param orgId
     * @return
     */
    List<BdCrmVolunteerOrgnizationSimpleModel> getAllUserByOrgId(int orgId);


    List<BdCrmVolunteerOrgnizationSimpleModel> getAllUserByOrgIds(List<Long> orgIds);

    /**
     * 只获取直接下级组织
     * {@link CfCrmVolunteerOrgnizationImpl#getSubOrgByOrgId(int)}
     * @param orgId
     * @return
     */
    List<OrgInfoModel> getSubOrgByOrgId(int orgId);


    /**
     * {@link CfCrmVolunteerOrgnizationImpl#getOrgListByOrgId(java.util.List)}
     * @param orgIdList
     * @return
     */
    List<OrgInfoModel> getOrgListByOrgId(List<Integer> orgIdList);

    /**
     * 获取上级组织信息
     * {@link CfCrmVolunteerOrgnizationImpl#getParentOrgByOrgId(int)}
     * @param orgId
     * @return
     */
    OrgInfoModel getParentOrgByOrgId(int orgId);


    /**
     * {@link CfCrmVolunteerOrgnizationImpl#getOfflineAllOrg()}
     * @return
     */
    List<OrgInfoModel> getAllOrg();


    List<CfBdcrmVolunteerOrgnizationCopyDO> getOrgByUniqueCodes(List<String> uniqueCodes);

    /**
     * {@link CfCrmVolunteerOrgnizationImpl#getBdCrmVolunteerOrgnizationSimpleModelByMisList(java.util.List)}
     * @param misList
     * @return
     */
    List<BdCrmVolunteerOrgnizationSimpleModel> getBdCrmVolunteerOrgnizationSimpleModelByMisList(Collection<String> misList);

    List<BdCrmVolunteerOrgnizationSimpleModel> getByUniqueCodeList(Collection<String> uniqueCodeList);

    /**
     * @param misNameList
     * @return
     */
    List<BdCrmVolunteerOrgnizationSimpleModel> getSimpleModelByMisNameList(List<String> misNameList);


}
