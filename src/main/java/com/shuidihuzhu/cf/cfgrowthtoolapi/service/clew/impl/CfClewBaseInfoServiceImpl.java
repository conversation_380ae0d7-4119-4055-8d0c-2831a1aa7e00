package com.shuidihuzhu.cf.cfgrowthtoolapi.service.clew.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.ClewModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.clew.ICfClewBaseInfoService;
import com.shuidihuzhu.cf.dao.clew.CfClewBaseInfoDao;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-09-03
 */
@Service
@Slf4j
public class CfClewBaseInfoServiceImpl implements ICfClewBaseInfoService {

    @Autowired
    private CfClewBaseInfoDao cfClewBaseInfoDao;

    @Override
    public List<ClewModel> listSourceTypeUniqueCodeAndEncryptPhone(String volunteerCode, String encryptPhone) {
        return cfClewBaseInfoDao.listSourceTypeUniqueCodeAndEncryptPhone(volunteerCode,encryptPhone);
    }

    @Override
    public Long getLatelyCaseIdByClewIds(List<Long> clewIds) {
        return cfClewBaseInfoDao.getLatelyCaseIdByClewIds(clewIds);
    }

    @Override
    public List<CfClewBaseInfoDO> getClewBaseInfoByStatusAndDiseaseName(Date startTime, Date endTime, Integer status, String diseaseNameRegex) {
        return cfClewBaseInfoDao.getClewBaseInfoByStatusAndDiseaseName(startTime, endTime, status, diseaseNameRegex);
    }
}
