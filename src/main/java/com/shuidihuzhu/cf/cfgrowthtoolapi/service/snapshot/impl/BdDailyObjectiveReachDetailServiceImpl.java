package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdDailyObjectiveReachDetail;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.BdDailyObjectiveReachDetailService;
import com.shuidihuzhu.cf.dao.snapshot.BdDailyObjectiveReachDetailMapper;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class BdDailyObjectiveReachDetailServiceImpl implements BdDailyObjectiveReachDetailService {

    @Resource
    private BdDailyObjectiveReachDetailMapper bdDailyObjectiveReachDetailMapper;


    @Override
    public BdDailyObjectiveReachDetail selectByTargetTypeAndCaseId(Integer targetType, Long caseId) {
        if (caseId == null || targetType == null) {
            return null;
        }
        return bdDailyObjectiveReachDetailMapper.selectByTargetTypeAndCaseId(targetType, caseId);
    }

    @Override
    public int inset(BdDailyObjectiveReachDetail bdDailyObjectiveReach) {
        if (bdDailyObjectiveReach == null) {
            return 0;
        }
        return bdDailyObjectiveReachDetailMapper.insertSelective(bdDailyObjectiveReach);
    }
}
