package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgOptLogDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OrgOrMemberChangeEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.OrgOptParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-07-10 12:58 下午
 **/
@Slf4j
@Service("selfBuiltOrgForSea")
public class CfCrmSelfBuiltOrgForSeaImpl extends AbstractCrmSelfBuiltOrgService implements ICrmSelfBuiltOrgWriteService {

    //同步给第三方组织上有修改
    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Autowired
    private ICrmSelfBuiltOrgReadService selfBuiltOrgReadService;

    @Autowired
    private ICrmOrganizationRelationService organizationRelationService;

    /**
     * 获取组织所有的入口,使用缓存
     */
    @Override
    public List<BdCrmOrganizationDO> getAllOrg() {
        return getAllOrgByDB();
    }


    @Override
    public Response<Boolean> addOrg(OrgOptParam orgOptParam) {
        BdCrmOrganizationDO currentOptOrg = orgOptParam.getCurrentOptOrg();
        currentOptOrg.setParentId(orgOptParam.getAddToOrg().getId());
        String addComment = listChainByOrgIdsWithDefaultSplitter(Lists.newArrayList(orgOptParam.getAddToOrg().getId()))
                .get(orgOptParam.getAddToOrg().getId()) + "-" + currentOptOrg.getOrgName();
        addOrgNode(currentOptOrg);
        if(orgOptParam.getCurrentOptOrg().getOrgAttribute()==0){
            bdCrmOrganizationDao.updateCityIdAndCityName(currentOptOrg.getId(),orgOptParam.getCityId(),orgOptParam.getCityName());
        }
        commitAddLog(orgOptParam, addComment);
        return NewResponseUtil.makeSuccess(true);
    }


    @Override
    public Response<Boolean> moveOrgNode(OrgOptParam orgOptParam) {
        BdCrmOrganizationDO currentOptOrg = orgOptParam.getCurrentOptOrg();
        BdCrmOrganizationDO addToOrg = orgOptParam.getAddToOrg();
        bdCrmOrganizationDao.moveToNewParent(currentOptOrg.getId(), addToOrg.getId());
        commitMoveLog(orgOptParam);
        customEventPublisher.publish(new OrgOrMemberChangeEvent(this, null, currentOptOrg));
        if (apolloService.getTestOrgId()<=0){
            return NewResponseUtil.makeSuccess(true);
        }
        List<BdCrmOrganizationDO> organizationList = selfBuiltOrgReadService.listAllSubOrgIncludeSelf(apolloService.getTestOrgId());
        Set<Long> allTestOrgSet = organizationList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toSet());
        //检测移动组织是否是测试区域
        if (!allTestOrgSet.contains(addToOrg.getId())){
            //非测试区域
            return NewResponseUtil.makeSuccess(true);
        }
        //需要删除
        if (BooleanUtils.toBoolean(orgOptParam.getDelete())){
            //找出被移动组织下的所有orgRelation,执行删除逻辑
            List<Long> orgIds = selfBuiltOrgReadService.listAllSubOrgIncludeSelf(currentOptOrg.getId()).stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
            List<BdCrmOrgUserRelationDO> orgUserRelationList = organizationRelationService.listByOrgIdsFromDB(orgIds);
            orgUserRelationList.forEach(item -> {
                organizationRelationService.deleteMemberById(item.getId(),orgOptParam.getAdminUserId());
            });
        }
        return NewResponseUtil.makeSuccess(true);
    }

    @Override
    public Response<Boolean> editCurrentNode(BdCrmOrganizationDO currentOrg, OrganizationMemberOptEnum optEnum, long adminUser) {
        BdCrmOrganizationDO beforeChangeOrgInfo = getCurrentOrgById(currentOrg.getId());
        //传啥修改啥
        bdCrmOrganizationDao.editOrg(currentOrg);
        commitEditLog(currentOrg, beforeChangeOrgInfo, optEnum, adminUser);
        customEventPublisher.publish(new OrgOrMemberChangeEvent(this, null, currentOrg));
        return NewResponseUtil.makeSuccess(true);
    }

    @Override
    public int updateCityAndCityNameById(long currentOrgId, int cityId, String cityName) {
        return bdCrmOrganizationDao.updateCityIdAndCityName(currentOrgId,cityId,cityName);
    }


    private void addOrgNode(BdCrmOrganizationDO organizationDO) {
        Assert.notNull(organizationDO, "组织信息不能为空");
        bdCrmOrganizationDao.insert(organizationDO);
        customEventPublisher.publish(new OrgOrMemberChangeEvent(this, null, organizationDO));
    }


    private void commitAddLog(OrgOptParam orgOptParam, String addComment) {
        allOrgList.invalidateAll();
        log.info("清除组织缓存中的数据");
        BdCrmOrganizationDO currentOptOrg = orgOptParam.getCurrentOptOrg();
        BdCrmOrgOptLogDO bdCrmOrgOptLogDO = new BdCrmOrgOptLogDO();
        bdCrmOrgOptLogDO.setAdminUserId(orgOptParam.getAdminUserId());
        bdCrmOrgOptLogDO.setOptType(orgOptParam.getOptEnum().getCode());
        bdCrmOrgOptLogDO.setOrgId(currentOptOrg.getId());
        bdCrmOrgOptLogDO.setComment("新增组织：" + String.format("新增组织%s", addComment));
        crmOptLogService.addOptLog(bdCrmOrgOptLogDO);
    }


    private void commitMoveLog(OrgOptParam orgOptParam) {
        allOrgList.invalidateAll();
        log.info("清除组织缓存中的数据");
        //currentOptOrg：移动前已经填充完整的组织信息, 参考MoveOrgLimitImpl.doCanOptCheck中的逻辑
        BdCrmOrganizationDO currentOptOrg = orgOptParam.getCurrentOptOrg();
        BdCrmOrganizationDO addToOrg = orgOptParam.getAddToOrg();
        BdCrmOrgOptLogDO bdCrmOrgOptLogDO = new BdCrmOrgOptLogDO();
        bdCrmOrgOptLogDO.setAdminUserId(orgOptParam.getAdminUserId());
        bdCrmOrgOptLogDO.setOptType(orgOptParam.getOptEnum().getCode());
        bdCrmOrgOptLogDO.setOrgId(currentOptOrg.getId());
        Map<Long, String> orgIdTChain = listChainByOrgIdsWithDefaultSplitter(Lists.newArrayList(currentOptOrg.getParentId(), addToOrg.getId()));
        String moveFromComment = orgIdTChain.getOrDefault(currentOptOrg.getParentId(), "") + default_splitter + currentOptOrg.getOrgName();
        String moveToComment = orgIdTChain.getOrDefault(addToOrg.getId(), "") + default_splitter + currentOptOrg.getOrgName();
        bdCrmOrgOptLogDO.setComment("移动组织：" + String.format("从%s 移动到 %s", moveFromComment, moveToComment));
        crmOptLogService.addOptLog(bdCrmOrgOptLogDO);
    }


    private void commitEditLog(BdCrmOrganizationDO currentOrg, BdCrmOrganizationDO beforeChangeOrgInfo, OrganizationMemberOptEnum optEnum, long adminUser) {
        allOrgList.invalidateAll();
        log.info("清除缓存中的数据");
        BdCrmOrgOptLogDO bdCrmOrgOptLogDO = new BdCrmOrgOptLogDO();
        bdCrmOrgOptLogDO.setAdminUserId(adminUser);
        bdCrmOrgOptLogDO.setOptType(optEnum.getCode());
        bdCrmOrgOptLogDO.setOrgId(currentOrg.getId());
        String orgChain = listChainByOrgIdsWithDefaultSplitter(Lists.newArrayList(beforeChangeOrgInfo.getId()))
                .get(beforeChangeOrgInfo.getId());
        OrganizationUserEnums.OrgNodeAttributeEnum beforeChangeAttributeEnum = OrganizationUserEnums.paresNodeAttribute(beforeChangeOrgInfo.getOrgAttribute());
        OrganizationUserEnums.OrgNodeAttributeEnum orgNodeAttributeEnum = OrganizationUserEnums.paresNodeAttribute(currentOrg.getOrgAttribute());
        String beforeChange = StringUtils.isBlank(orgChain) ? beforeChangeOrgInfo.getOrgName() : orgChain + default_splitter + beforeChangeOrgInfo.getOrgName();
        String afterChange = StringUtils.isBlank(orgChain) ? currentOrg.getOrgName() : orgChain + default_splitter + currentOrg.getOrgName();
        bdCrmOrgOptLogDO.setComment("编辑组织信息：" + String.format("%s 修改为 %s", beforeChange + ":" + beforeChangeAttributeEnum.getDesc(), afterChange + ":" + orgNodeAttributeEnum.getDesc()));
        crmOptLogService.addOptLog(bdCrmOrgOptLogDO);
    }

}
