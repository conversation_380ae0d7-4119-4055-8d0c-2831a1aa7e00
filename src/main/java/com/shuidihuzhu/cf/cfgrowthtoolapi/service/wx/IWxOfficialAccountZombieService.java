package com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.wx.CfWxOfficialAccountZombieFanDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-24
 */
public interface IWxOfficialAccountZombieService {

    /**
     * 根据公众号获取僵尸粉账号
     * @param thirdType
     * @return
     */
    List<CfWxOfficialAccountZombieFanDO> listFanByThirdType(Integer thirdType);

    /**
     * 更新使用数量
     * @param zombieFanList
     * @return
     */
    int updateUseCount(List<CfWxOfficialAccountZombieFanDO> zombieFanList);

    /**
     * 获取最大主键id
     * @return
     */
    Long getMaxPrimaryKey();

    /**
     * 重置UseCount
     * @param maxId
     * @return
     */
    int resetUseCount(long maxId);
}
