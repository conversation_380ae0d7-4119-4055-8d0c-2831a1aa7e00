package com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.ai.alps.cupid.model.CupidItem;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.AIDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICaseLevelDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCaseDayDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CaseDonateEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.DonateConfig;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.DonateDailyConfig;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2022-06-27 16:42
 **/
@Slf4j
@Service
public class DonateTaskCreateService {

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private BdCaseTagService bdCaseTagService;

    @Autowired
    private IBdCaseDonateTaskService caseDonateTaskService;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Resource
    private CfCaseDayDataService caseDayDataService;

    @Autowired
    private IBdCaseDonateCityConfigService cityConfigService;

    @Autowired
    private IBdCaseDonateLevelConfigService levelConfigService;

    @Autowired
    private IBdCaseDonateTaskConfigService donateTaskConfigService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ICaseLevelDelegate caseLevelDelegate;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ICrmMemberInfoService memberService;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    @Autowired
    private AIDelegate aiDelegate;

    @Autowired
    private ICfBdCaseInfoService bdCaseInfoService;


    public void scoreCase() {
        //查询近三个小时发起的案例-未打分的
//        String startTime = DateTime.now().minusHours(3).toString(GrowthtoolUtil.ymdhmsfmt);
//        List<BdCaseTagDO> bdCaseTagDOList = bdCaseTagService.listByTimeRange(startTime)
//                .stream()
//                .filter(item -> StringUtils.isBlank(item.getCaseScore()))
//                .collect(Collectors.toList());
//        log.info("caseDonateLevelModels:{}", caseDonateLevelModels);
//        for (BdCaseTagDO bdCaseTagDO : bdCaseTagDOList) {
//            CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegate.getCaseInfoById(bdCaseTagDO.getCaseId());
//            if (crowdfundingInfo == null) {
//                continue;
//            }
//            String caseScore = caseLevelDelegate.caseLevel(crowdfundingInfo.getInfoId());
//            if (StringUtils.isBlank(caseScore)) {
//                continue;
//            }
//            CaseDonateLevelModel caseDonateLevelModel = caseDonateLevelModels.stream()
//                    .filter(item -> caseScore.compareTo(item.getMinScore()) >= 0 && caseScore.compareTo(item.getMaxScore()) <= 0)
//                    .findFirst()
//                    .orElse(null);
//            if (caseDonateLevelModel == null) {
//                continue;
//            }
//            bdCaseTagService.updateCaseLevel(bdCaseTagDO.getId(), caseScore, caseDonateLevelModel.getCaseLevel());
//        }
        String caseLevelModelString = apolloService.getCaseLevelModel();
        if (StringUtils.isBlank(caseLevelModelString)) {
            return;
        }
        List<CaseDonateLevelModel> caseDonateLevelModels = JSON.parseArray(caseLevelModelString, CaseDonateLevelModel.class);
        //查询符合75h的案例进行更新打分
        String startTime = DateTime.now().minusHours(75).toString(GrowthtoolUtil.ymdhmsfmt);
        List<BdCaseTagDO> bdCaseTagDOS = bdCaseTagService.listByTimeRange(startTime);
        log.info("符合75h的案例:{}", bdCaseTagDOS);
        List<Integer> caseIds = bdCaseTagDOS.stream().map(BdCaseTagDO::getCaseId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }
        List<CupidItem> caseScoreList = aiDelegate.getCaseScore(caseIds);
        if (CollectionUtils.isEmpty(caseScoreList)) {
            return;
        }

        Map<String, CupidItem> caseScoreMap = caseScoreList.stream().collect(Collectors.toMap(CupidItem::getItemId, Function.identity()));
        for (BdCaseTagDO bdCaseTagDO : bdCaseTagDOS) {
            CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegate.getCaseInfoById(bdCaseTagDO.getCaseId());
            if (crowdfundingInfo == null) {
                continue;
            }
            CupidItem cupidItem = caseScoreMap.get(String.valueOf(bdCaseTagDO.getCaseId()));
            if (Objects.isNull(cupidItem)) {
                continue;
            }
            String caseScore = StringUtils.substring(String.valueOf(cupidItem.getWeight()), 0, 20);
            // 根据ai案例打分时间过滤相应级别
            String itemDesc = cupidItem.getItemDesc();
            Map<String, Integer> caseScoreTime = JSON.parseObject(itemDesc, new TypeReference<>(){});
            if (caseScoreTime == null || caseScoreTime.get("time") == null) {
                continue;
            }
            Integer time = caseScoreTime.get("time");

            CaseDonateLevelModel caseDonateLevelModel = caseDonateLevelModels.stream()
                    .filter(item -> caseScore.compareTo(item.getMinScore()) >= 0 && caseScore.compareTo(item.getMaxScore()) <= 0)
                    .min((o1, o2) -> {
                        int result = Math.abs(o1.getInitTime() - time) - Math.abs(o2.getInitTime() - time);
                        if (result == 0) {
                            return o1.getInitTime() - o2.getInitTime();
                        }
                        return result;
                    })
                    .orElse(null);
            if (caseDonateLevelModel == null) {
                continue;
            }
            log.info("案例打分更新结果,案例id:{},案例打分时间:{},案例打分:{},案例分层:{}", bdCaseTagDO.getCaseId(), time, caseScore, caseDonateLevelModel.getCaseLevel());
            bdCaseTagService.updateCaseLevel(bdCaseTagDO.getId(), caseScore, caseDonateLevelModel.getCaseLevel(), time);
        }

    }


    public void alarmMonitor(List<CfBdCaseDonateCityConfigDO> donateCityConfigDOList) {
        DateTime now = DateTime.now();
        DateTime thirtyMinBefore = now.minusMinutes(30);
        DateTime thirtyMinAfter = DateTime.now();
        String todayString = now.toString(GrowthtoolUtil.ymdfmt);
        String yesterdayString = now.minusDays(1).toString(GrowthtoolUtil.ymdfmt);
        List<CfBdCaseDonateCityConfigDO> filterCityConfigList = donateCityConfigDOList.stream()
                .filter(item -> Objects.equals(item.getUseStatus(), CaseDonateEnums.CityUseStatusEnum.open.getCode()))
                .filter(item -> Objects.equals(item.getMonitorStatus(), CaseDonateEnums.MonitorStatusEnum.OPEN_MONITOR.getCode()))
                .filter(item -> {
                    List<Integer> parseMonitorTime = item.parseMonitorTime();
                    if (parseMonitorTime.size() < 2) {
                        return false;
                    }
                    DateTime dateTime = now.withHourOfDay(parseMonitorTime.get(0)).withMinuteOfHour(parseMonitorTime.get(1));
                    if (dateTime.isAfter(thirtyMinBefore) && dateTime.isBefore(thirtyMinAfter)) {
                        return true;
                    }
                    return false;
                })
                .collect(Collectors.toList());
        for (CfBdCaseDonateCityConfigDO donateCityConfigDO : filterCityConfigList) {
            List<BdCaseDonateTaskDO> donateTaskDOS = caseDonateTaskService.listByCityConfigId(donateCityConfigDO.getId(), CaseDonateEnums.DonateTaskStatusEnum.init.getCode());
            int monitorConditionType = donateCityConfigDO.getMonitorConditionType();
            int monitorCondition = donateCityConfigDO.getMonitorCondition();
            switch (monitorConditionType) {
                case 0:
                    donateTaskDOS = donateTaskDOS.stream()
                            .filter(item -> Objects.equals(item.getDayKey(), todayString))
                            .collect(Collectors.toList());
                    break;
                case 1:
                    donateTaskDOS = donateTaskDOS.stream()
                            .filter(item -> Objects.equals(item.getDayKey(), yesterdayString))
                            .collect(Collectors.toList());
                    break;
                default:
                    break;
            }
            for (BdCaseDonateTaskDO donateTaskDO : donateTaskDOS) {
                int shareTarget = donateTaskDO.getShareTarget() * monitorCondition / 100;
                if (shareTarget > donateTaskDO.getShareCount()) {
                    //发送消息
                    CrowdfundingVolunteer volunteer = cfVolunteerService.getVolunteerByUniqueCode(donateTaskDO.getUniqueCode());
                    CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegate.getCaseInfoById(donateTaskDO.getCaseId());
                    CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoId(donateTaskDO.getCaseId());
                    DonateConfig donateConfig = new DonateConfig();
                    donateConfig.setCaseInfo(crowdfundingInfo);
                    donateConfig.setCfBdCaseInfoDo(cfBdCaseInfoDo);
                    appPushCrmCaseMsgService.sendDonateMonitor(donateConfig, donateTaskDO, volunteer);
                }
            }
        }


    }


    public void createByCronTab() {
        DateTime now = DateTime.now();
        DateTime thirtyMinBefore = now.minusMinutes(30);
        DateTime thirtyMinAfter = DateTime.now();

        List<CfBdCaseDonateCityConfigDO> donateCityConfigDOList = cityConfigService.listAllDonateCityConfig();
        alarmMonitor(donateCityConfigDOList);
        //获取所有的配置
        Map<Long, CfBdCaseDonateCityConfigDO> cityConfigDOMap = donateCityConfigDOList
                .stream()
                .filter(item -> Objects.equals(item.getUseStatus(), CaseDonateEnums.CityUseStatusEnum.open.getCode()))
                .collect(Collectors.toMap(CfBdCaseDonateCityConfigDO::getId, Function.identity()));
        if (CollectionUtils.isEmpty(cityConfigDOMap.keySet())) {
            return;
        }
        List<Long> cityConfigIds = Lists.newArrayList(cityConfigDOMap.keySet());
        int maxTaskDays = 0;
        List<CfBdCaseDonateLevelConfigDO> levelConfigDOList = levelConfigService.listByCityConfigIds(cityConfigIds);
        if (CollectionUtils.isEmpty(levelConfigDOList)) {
            return;
        }
        for (CfBdCaseDonateLevelConfigDO levelConfigDO : levelConfigDOList) {
            maxTaskDays = Math.max(levelConfigDO.getTaskDays() - 1, maxTaskDays);
        }
        String startTime = now.minusDays(maxTaskDays).withTimeAtStartOfDay().toString(GrowthtoolUtil.ymdfmt);
        List<BdCaseTagDO> caseTagDOS = bdCaseTagService.listByTimeRange(startTime);

        List<Integer> caseIds = caseTagDOS.stream()
                .map(BdCaseTagDO::getCaseId)
                .collect(Collectors.toList());

        //批量获取案例+捐单数据
        Map<Integer, CfBdCaseInfoDo> cfBdCaseInfoDOs = cfBdCaseInfoService.listCaseInfoByCaseIds(caseIds)
                .stream()
                .collect(Collectors.toMap(CfBdCaseInfoDo::getCaseId, Function.identity(), (before, after) -> before));

        Map<Integer, CfCaseDayDataDO> caseDayDataMap = caseDayDataService.getByCaseIdAndDayKey(caseIds, now.toString(GrowthtoolUtil.ymdfmt))
                .stream()
                .collect(Collectors.toMap(CfCaseDayDataDO::getCaseId, Function.identity(), (before, after) -> before));

        Map<Integer, CfCaseDayDataDO> yesterdayDataMap = caseDayDataService.getByCaseIdAndDayKey(caseIds, now.minusDays(1).toString(GrowthtoolUtil.ymdfmt))
                .stream()
                .collect(Collectors.toMap(CfCaseDayDataDO::getCaseId, Function.identity(), (before, after) -> before));

        List<CfBdCaseDonateTaskConfigDO> taskConfigDOList = donateTaskConfigService.listByCityConfigIds(cityConfigIds)
                .stream()
                .filter(item -> {
                    List<Integer> hourMinutes = item.parseCornDes();
                    if (hourMinutes.size() < 2) {
                        return false;
                    }
                    DateTime dateTime = now.withHourOfDay(hourMinutes.get(0)).withMinuteOfHour(hourMinutes.get(1));
                    if (dateTime.isAfter(thirtyMinBefore) && dateTime.isBefore(thirtyMinAfter)) {
                        return true;
                    }
                    return false;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskConfigDOList)) {
            log.info("没有符合条件可执行的定时任务");
            return;
        }

        //获取当前最接近的任务
        for (CfBdCaseDonateTaskConfigDO taskConfigDO : taskConfigDOList) {
            CfBdCaseDonateCityConfigDO cityConfigDO = cityConfigDOMap.get(taskConfigDO.getCityConfigId());
            levelConfigDOList.forEach(item -> {
                if (Objects.equals(item.getCityConfigId(), taskConfigDO.getCityConfigId())) {
                    DonateConfig donateConfig = new DonateConfig(cityConfigDO, item, taskConfigDO);
                    createDonateByConfig(donateConfig, caseTagDOS, cfBdCaseInfoDOs, caseDayDataMap, yesterdayDataMap);
                }
            });
        }
    }


    public void createDonateByConfig(DonateConfig donateConfig, List<BdCaseTagDO> caseTagDOS,
                                     Map<Integer, CfBdCaseInfoDo> caseInfoDoMap,
                                     Map<Integer, CfCaseDayDataDO> caseDayDataMap,
                                     Map<Integer, CfCaseDayDataDO> yesterdayDataMap) {
        CfBdCaseDonateCityConfigDO cityConfigDO = donateConfig.getCityConfigDO();
        List<Long> cityIds = cityConfigDO.parseOrgId();
        if (CollectionUtils.isEmpty(cityIds)) {
            return;
        }
        caseTagDOS.stream()
                .filter(item -> cityIds.contains(item.getCityId()))
                .filter(item -> item.getCaseLevel() == donateConfig.getLevelConfigDO().getCaseLevel())
                .forEach(item -> {
                    CfBdCaseInfoDo cfBdCaseInfoDo = caseInfoDoMap.get(item.getCaseId());
                    if (cfBdCaseInfoDo == null) {
                        log.info("cfBdCaseInfoDo为空");
                        return;
                    }
                    CfCaseDayDataDO caseDayDataDO = caseDayDataMap.get(cfBdCaseInfoDo.getCaseId());
                    CfCaseDayDataDO yesterdayDataDO = yesterdayDataMap.get(cfBdCaseInfoDo.getCaseId());
                    donateConfig.setCfBdCaseInfoDo(cfBdCaseInfoDo);
                    donateConfig.setBdCaseTagDO(item);
                    donateConfig.setTodayCaseDayDataDO(caseDayDataDO);
                    donateConfig.setYesterdayDateDO(yesterdayDataDO);
                    createDonateTask(donateConfig);
                });
    }


    public void createDonateTask(DonateConfig donateConfig) {
        //当前案例是否满足生成任务
        boolean checkCanCreateTask = checkCanCreateTask(donateConfig);
        if (!checkCanCreateTask) {
            return;
        }
        String uniqueCode = donateConfig.getCfBdCaseInfoDo().getUniqueCode();
        CrowdfundingVolunteer volunteer = memberService.getCanHandlerVolunteer(uniqueCode);
        if (volunteer == null) {
            log.info("找不到任务分配人员");
            return;
        }
        String volunteerName = volunteer.getVolunteerName();
        //生成对应的任务
        BdCaseDonateTaskDO donateTaskDO = BdCaseDonateTaskDO.initBdCaseDonateTask(donateConfig, volunteerName);
        log.info("caseId:{}生成任务donateTaskDO:{}", donateConfig.getCaseId(), donateTaskDO);
        caseDonateTaskService.insert(donateTaskDO);
        //推送消息
        appPushCrmCaseMsgService.sendDonateTaskCreate(donateConfig, donateTaskDO, volunteer);
    }





    //true能创建任务
    public boolean checkCanCreateTask(DonateConfig donateConfig){
        BdCaseTagDO bdCaseTagDO = donateConfig.getBdCaseTagDO();
        log.info("checkCanCreateTask caseId:{}", bdCaseTagDO.getCaseId());
        CrowdfundingInfo caseInfo = crowdFundingFeignDelegate.getCaseInfoById(bdCaseTagDO.getCaseId());
        if (caseInfo == null) {
            return false;
        }
        donateConfig.setCaseInfo(caseInfo);
        if (!caseInfo.getEndTime().after(new Date())) {
            return false;
        }
        if (bdCaseTagDO.getStopOperate() != 0) {
            log.info("caseId:{}案例已停止运营", caseInfo.getId());
            return false;
        }
        //当天是否有任务,如果生成任务不再生成
        DateTime now = DateTime.now();
        BdCaseDonateTaskDO donateTaskDO = caseDonateTaskService.getByCaseIdAndDayKey(bdCaseTagDO.getCaseId(), now.toString(GrowthtoolUtil.ymdfmt));
        if (donateTaskDO != null) {
            log.info("caseId:{}当天已经生成任务", caseInfo.getId());
            return false;
        }
        /**
         * 0831线下案例运营规则完善新增规则，1在新增配置时候已经校验
         * 1、有与案例归属城市、分层类别一致且状态为‘已启用’的运营配置
         * 2、案例运营配置的“开始运营时间”<=当前时间距离发起时间<="开始运营时间+转发运营天数"
         * 3、案例任务运营目标完成数<=案例运营配置的"要求转发达成天数"
         */
        CfBdCaseDonateLevelConfigDO levelConfigDO = donateConfig.getLevelConfigDO();
        int startDonateDays = levelConfigDO.getStartDonateDays();
        int addTaskDays = levelConfigDO.getTaskDays() + startDonateDays;
        Date dateCreated = bdCaseTagDO.getDateCreated();
        CfBdCaseDonateTaskConfigDO donateTaskConfigDO = donateConfig.getDonateTaskConfigDO();
        int calculateTimeDiff = calculateTimeDiff(dateCreated);
        //案例运营配置的“开始运营时间”<=当前时间距离发起时间<="开始运营时间+转发运营天数"
        if (startDonateDays > calculateTimeDiff || calculateTimeDiff > addTaskDays) {
            log.info("caseId:{}案例运营配置的“开始运营时间”不符合案例当前时间距离发起时间范围,startDonateDays:{},calculateTimeDiff:{}", caseInfo.getId(), startDonateDays, calculateTimeDiff);
            return false;
        }
        int finishTaskCount = 0;
        // 案例任务运营实际完成数<=案例运营配置的"要求转发达成天数"
        // 获取案例从初审通过到现在的捐单数据
        LocalDateTime currentTime = LocalDateTime.now();
        CfBdCaseInfoDo bdCaseInfoDo = bdCaseInfoService.getBdCaseInfoByInfoId(bdCaseTagDO.getCaseId());
        if (Objects.nonNull(bdCaseInfoDo) && Objects.nonNull(bdCaseInfoDo.getFirstApproveTime())) {
            List<String> dateTimes = GrowthtoolUtil.getDateTimeByStartTimeWithEndTime(DateUtil.formatDate(bdCaseInfoDo.getFirstApproveTime()), DateUtil.formatDate(now.toDate()));
            Map<Integer, DonateDailyConfig> dailyConfigMap = levelConfigDO.parseDayConfig().stream().collect(Collectors.toMap(DonateDailyConfig::getDay, Function.identity(), (before, after) -> before));
            List<CfCaseDayDataDO> byCaseIdAndDayKeys = caseDayDataService.getByCaseIdAndDayKeys(bdCaseTagDO.getCaseId(), dateTimes);
            for (CfCaseDayDataDO byCaseIdAndDayKey : byCaseIdAndDayKeys) {
                LocalDateTime dayKeyTime = DateUtil.parseDate(byCaseIdAndDayKey.getDayKey()).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                DonateDailyConfig donateDailyConfig = dailyConfigMap.get((int) ChronoUnit.DAYS.between(dayKeyTime, currentTime));
                if (Objects.nonNull(donateDailyConfig) && byCaseIdAndDayKey.getTodayShareCount() >= donateDailyConfig.getShareTarget()) {
                    finishTaskCount++;
                }
            }
        }
        if (finishTaskCount > levelConfigDO.getRequireDays()) {
            log.info("caseId:{}案例运营任务完成数已经达到转发目标达成天数,finishTaskCount:{},requireDays:{}",caseInfo.getId(), finishTaskCount, levelConfigDO.getRequireDays());
            return false;
        }
        int days = donateConfig.getDays();
        log.info("days:{}", days);

        int conditionType = donateTaskConfigDO.getConditionType();
        CaseDonateEnums.ConditionTypeEnum conditionTypeEnum = CaseDonateEnums.parseConditionType(conditionType);
        if (conditionTypeEnum == null) {
            return false;
        }
        int todayShareCount = getTodayShareCount(donateConfig);
        switch (conditionTypeEnum) {
            case today_share_target:
                int todayShareTarget = donateConfig.getTodayShareTarget();
                donateConfig.setShareTarget(todayShareTarget);
                if (todayShareCount >= todayShareTarget) {
                    log.info("caseId:{}今日超过设置未达到目标,无需生成任务,todayShareCount:{},target:{}", caseInfo.getId(), todayShareCount, todayShareTarget);
                    return false;
                }
                break;
            case yesterday_share_target:
                int yesterdayShareCount = getYesterdayShareCount(donateConfig);
                int shareTarget = donateConfig.getShareTarget(days - 1);
                donateConfig.setShareTarget(shareTarget);
                if (yesterdayShareCount >= shareTarget) {
                    log.info("caseId:{}昨日超过设置未达到目标,无需生成任务,yesterdayShareCount:{},target:{}", caseInfo.getId(), yesterdayShareCount, shareTarget);
                    return false;
                }
                break;
            default:
                break;
        }
        return true;
    }


    private int getTodayShareCount(DonateConfig donateConfig) {
        if (donateConfig.getTodayCaseDayDataDO() != null) {
            return donateConfig.getTodayCaseDayDataDO().getTodayShareCount();
        }
        return 0;
    }


    private int getYesterdayShareCount(DonateConfig donateConfig) {
        if (donateConfig.getYesterdayDateDO() != null) {
            return donateConfig.getYesterdayDateDO().getTodayShareCount();
        }
        return 0;
    }

    private int calculateTimeDiff(Date caseCreated) {
        long now = new Date().getTime();
        long from = caseCreated.getTime();
        return (int) ((now - from) / (1000 * 60 * 60 * 24));
    }


}
