package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalAreaBuildingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalAreaBuildingService;
import com.shuidihuzhu.cf.dao.hospital.HospitalAreaBuildingDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 院区-楼宇信息(HospitalAreaBuilding)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-01-04 17:25:39
 */
@Service
public class HospitalAreaBuildingServiceImpl implements HospitalAreaBuildingService {

    @Resource
    private HospitalAreaBuildingDao hospitalAreaBuildingDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public HospitalAreaBuildingDO queryById(long id) {
        return hospitalAreaBuildingDao.queryById(id);
    }

    @Override
    public List<HospitalAreaBuildingDO> listByHospitalCode(List<String> vhospitalCodes) {
        if (CollectionUtils.isEmpty(vhospitalCodes)) {
            return Lists.newArrayList();
        }
        return hospitalAreaBuildingDao.listByHospitalCode(vhospitalCodes);
    }

    @Override
    public List<HospitalAreaBuildingDO> listByHospitalCodeAndBuildingNameLike(String vhospitalCode, String buildingName) {
        if (StringUtils.isBlank(vhospitalCode)) {
            return Lists.newArrayList();
        }
        return hospitalAreaBuildingDao.listByHospitalCodeAndBuildingNameLike(vhospitalCode, buildingName);
    }

    @Override
    public List<HospitalAreaBuildingDO> listByHospitalCodeAndBuildingName(String vhospitalCode, String buildingName) {
        if (StringUtils.isBlank(vhospitalCode)) {
            return Lists.newArrayList();
        }
        return hospitalAreaBuildingDao.listByHospitalCodeAndBuildingName(vhospitalCode, buildingName);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    @Override
    public List<HospitalAreaBuildingDO> queryAllByLimit(int offset, int limit) {
        return hospitalAreaBuildingDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param hospitalAreaBuilding 实例对象
     * @return 实例对象
     */
    @Override
    public int insert(HospitalAreaBuildingDO hospitalAreaBuilding) {
        if (hospitalAreaBuilding == null) {
            return 0;
        }
        return hospitalAreaBuildingDao.insert(hospitalAreaBuilding);
    }

    /**
     * 修改数据
     *
     * @param hospitalAreaBuilding 实例对象
     * @return 实例对象
     */
    @Override
    public int update(HospitalAreaBuildingDO hospitalAreaBuilding) {
        if (hospitalAreaBuilding == null) {
            return 0;
        }
        return hospitalAreaBuildingDao.update(hospitalAreaBuilding);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return hospitalAreaBuildingDao.deleteById(id) > 0;
    }

    @Override
    public List<HospitalAreaBuildingDO> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return hospitalAreaBuildingDao.listByIds(ids);
    }

    @Override
    public void updateMaxChangeId(int buildingId, long maxChangeId) {
        HospitalAreaBuildingDO hospitalAreaBuildingDO = queryById(buildingId);
        if (hospitalAreaBuildingDO == null || hospitalAreaBuildingDO.getMaxChangeId() > maxChangeId) {
            return;
        }
        hospitalAreaBuildingDao.updateMaxChangeId(buildingId, maxChangeId);
    }

    @Override
    public int updateBuildingFloorArea(int id, String buildingFloorArea) {
        if (id <= 0) {
            return 0;
        }
        return hospitalAreaBuildingDao.updateBuildingFloorArea(id, buildingFloorArea);
    }

    @Override
    public HospitalAreaBuildingDO getById(long id) {
        return hospitalAreaBuildingDao.getById(id);
    }
}