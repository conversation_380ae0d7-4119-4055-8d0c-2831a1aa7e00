package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.bdcrm.BdCrmOrganizationPathDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationPathDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * crm自建组织信息(BdCrmOrganizationPath)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-25 15:09:18
 */
@Service
public class BdCrmOrganizationPathServiceImpl implements IBdCrmOrganizationPathService {

    @Resource
    private BdCrmOrganizationPathDao bdCrmOrganizationPathDao;

    @Override
    public BdCrmOrganizationPathDO queryById(long id) {
        return bdCrmOrganizationPathDao.queryById(id);
    }

    @Override
    public List<BdCrmOrganizationPathDO> listByIds(List<Long> listByIds) {
        if (CollectionUtils.isEmpty(listByIds)) {
            return Lists.newArrayList();
        }
        return bdCrmOrganizationPathDao.listByIds(listByIds);
    }

    @Override
    public int updateOrInsert(BdCrmOrganizationPathDO bdCrmOrganizationPath) {
        return bdCrmOrganizationPathDao.updateOrInsert(bdCrmOrganizationPath);
    }

    @Override
    public BdCrmOrganizationPathDO queryByOrgPath(String orgPath) {
        return bdCrmOrganizationPathDao.queryByOrgPath(orgPath);
    }

}