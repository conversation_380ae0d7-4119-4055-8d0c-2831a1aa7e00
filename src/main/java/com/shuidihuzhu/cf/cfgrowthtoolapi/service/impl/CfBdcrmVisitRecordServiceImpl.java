package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfBdcrmVisitRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfBdcrmVisitRecordService;
import com.shuidihuzhu.cf.dao.CfBdcrmVisitRecordDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CfBdcrmVisitRecordServiceImpl implements CfBdcrmVisitRecordService {

    @Autowired
    private CfBdcrmVisitRecordDao bdcrmVisitRecordDao;


    @Override
    public List<CfBdcrmVisitRecordDO> queryRecordByBeVisitUniqueCode(List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)){
            return null;
        }

        return bdcrmVisitRecordDao.selectRecordByUniquecode(uniqueCodes,null,null,null);
    }

    @Override
    public List<CfBdcrmVisitRecordDO> queryRecordByVisitUniqueCode(String uniqueCode,String dateTime,String endTime) {
        if (StringUtils.isEmpty(uniqueCode)){
            return null;
        }
        return bdcrmVisitRecordDao.selectRecordByUniquecode(null,uniqueCode,dateTime,endTime);
    }

    @Override
    public int saveRecord(CfBdcrmVisitRecordDO recordDO) {
        if (recordDO == null){
            return 0;
        }
        return bdcrmVisitRecordDao.insertSelective(recordDO);
    }

    @Override
    public List<CfBdcrmVisitRecordDO> listRecordByLeaders(List<String> visitUniqueCodes, String dateTime, String endTime) {
        if (CollectionUtils.isEmpty(visitUniqueCodes)) {
            return Lists.newArrayList();
        }
        return bdcrmVisitRecordDao.listRecordByLeaders(visitUniqueCodes, dateTime, endTime);
    }
}
