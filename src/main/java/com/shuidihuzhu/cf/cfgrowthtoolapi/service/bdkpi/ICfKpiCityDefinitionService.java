package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCityDefinitionDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
public interface ICfKpiCityDefinitionService {
    /**
     * 查询所有城市
     * @return
     */
    List<CfKpiCityDefinitionDO> listAll();

    List<String> getCityname(Integer cityType);

    int insertCfKpiCityDefinitionDO(CfKpiCityDefinitionDO cfKpiCityDefinitionDO);

    int updateCfKpiCityDefinitionDO(CfKpiCityDefinitionDO cfKpiCityDefinitionDO);
}
