package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrDepartmentDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.GrDepartmentService;
import com.shuidihuzhu.cf.dao.gr.GrDepartmentDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/25 14:32
 */
@Service
@Slf4j
public class GrDepartmentServiceImpl implements GrDepartmentService {

    @Resource
    private GrDepartmentDao grDepartmentDao;

    @Override
    public int batchInsert(List<GrDepartmentDO> grDepartmentDOS) {
        if (CollectionUtils.isEmpty(grDepartmentDOS)) {
            return 0;
        }
        return grDepartmentDao.batchInsert(grDepartmentDOS);
    }

    @Override
    public int update(GrDepartmentDO grDepartmentDO) {
        if (grDepartmentDO == null) {
            return 0;
        }
        return grDepartmentDao.update(grDepartmentDO);
    }

    @Override
    public List<GrDepartmentDO> getGrDepartmentList(List<Integer> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Lists.newArrayList();
        }
        return grDepartmentDao.getGrDepartmentList(customerIds);
    }

    @Override
    public int deleteDepartment(long id) {
        return grDepartmentDao.deleteDepartment(id);
    }


}
