package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmObjectiveCompleteDetailModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmObjectiveIndicatorValueModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;

import java.util.*;

/**
 * @author: wanghui
 * @create: 2021/2/23 上午10:39
 */
public interface CfBdCrmObjectiveStatService{

    List<CfBdCrmObjectiveIndicatorValueModel> listCountGroupByObjectiveIndicatorId(Long objectiveCycleId, List<String> uniqueCodeList);

    Map<Integer, List<CfBdCrmObjectiveCompleteDetailModel>> getCountGroupByObjectiveIndicatorIdWithUniqueCode(Long objectiveCycleId, List<String> uniqueCodeList, List<Integer> objectiveIndicatorIdList);

    Map<Integer, List<CfBdCrmObjectiveCompleteDetailModel>> getCountGroupByObjectiveIndicatorIdWithOrgId(Long objectiveCycleId, List<Long> orgIdList, String uniqueCode, List<Integer> objectiveIndicatorIdList);

    /**
     * 筹款500案例 统计
     * @param orgMemberSnapshotList
     * @param crowdfundingInfo
     */
    void incrCurrentWeekCaseAmountExceed500Num(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo);

    void incrCurrentMonthCaseAmountExceed5000Num(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo);

    void incrCurrentMonthCaseNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo);

    void incrCurrentMonthValidCaseNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo);

    void incrCurrentWeekCaseNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo);

    void incrCurrentWeekValidCaseNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo);

    void incrCurrentMonthDonatedNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList);

    void incrCurrentWeekDonatedNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList);

    void incrIndicator(String uniqueCode, Set<CfBdCrmObjectiveIndicatorEnum> indicatorEnums);

    void decrIndicator(String uniqueCode, Set<CfBdCrmObjectiveIndicatorEnum> indicatorEnums);

    void setCurrentWeekSubmitThroughSuccess(String uniqueCode, long coverValue);

    void setCurrentMonthSubmitThroughSuccess(String uniqueCode, long coverValue);
}
