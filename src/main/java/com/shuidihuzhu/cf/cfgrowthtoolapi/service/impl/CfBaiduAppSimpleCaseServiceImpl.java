package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfBaiduAppSimpleCaseDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfBaiduAppSimpleCaseService;
import com.shuidihuzhu.cf.dao.CfBaiduAppSimpleCaseDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-04-23 21:37
 **/
@Service
@Slf4j
public class CfBaiduAppSimpleCaseServiceImpl implements CfBaiduAppSimpleCaseService {

    @Autowired
    private CfBaiduAppSimpleCaseDao baiduAppSimpleCaseDao;

    private final static int limit = 8;

    @Override
    public void insert(CfBaiduAppSimpleCaseDO cfBaiduAppSimpleCaseDO) {
        if (cfBaiduAppSimpleCaseDO == null || cfBaiduAppSimpleCaseDO.getCaseId() <= 0) {
            return;
        }
        //插入前还是查下
        CfBaiduAppSimpleCaseDO simpleCaseDO = baiduAppSimpleCaseDao.getByCaseId(cfBaiduAppSimpleCaseDO.getCaseId());
        if (simpleCaseDO != null) {
            return;
        }
        int insert = baiduAppSimpleCaseDao.insert(cfBaiduAppSimpleCaseDO);
        if (insert < 1) {
            log.info("插入失败,cfBaiduAppSimpleCaseDO:{}", JSON.toJSONString(cfBaiduAppSimpleCaseDO));
        }
    }

    @Override
    public void update(CfBaiduAppSimpleCaseDO cfBaiduAppSimpleCaseDO) {
        baiduAppSimpleCaseDao.update(cfBaiduAppSimpleCaseDO);
    }

    @Override
    public List<CfBaiduAppSimpleCaseDO> listByCursor(long lastCaseId, int limit) {
        return baiduAppSimpleCaseDao.listByCursor(lastCaseId, limit);
    }

    @Override
    public void updateContent(CfBaiduAppSimpleCaseDO cfBaiduAppSimpleCaseDO) {
        baiduAppSimpleCaseDao.updateContent(cfBaiduAppSimpleCaseDO);
    }

    @Override
    public List<CfBaiduAppSimpleCaseDO> listByDisease(String diseaseName) {
        if (StringUtils.isBlank(diseaseName)) {
            return Lists.newArrayList();
        }
        return baiduAppSimpleCaseDao.listByDisease(diseaseName, limit);
    }

    @Override
    public CfBaiduAppSimpleCaseDO getByInfoId(String infoId) {
        if (StringUtils.isBlank(infoId)) {
            return null;
        }
        return baiduAppSimpleCaseDao.getByInfoId(infoId);
    }

    @Override
    public List<String> listUnSubmitInfoIds(long id) {
        return baiduAppSimpleCaseDao.listUnSubmitInfoIds(id);
    }

    @Override
    public void updateSubmitStatus(List<String> infoIds) {
        if (CollectionUtils.isEmpty(infoIds)) {
            return;
        }
        List<List<String>> partition = Lists.partition(infoIds, 100);
        partition.forEach(item -> {
            if (CollectionUtils.isNotEmpty(item)) {
                baiduAppSimpleCaseDao.updateSubmitStatus(item);
            }
        });
    }
}
