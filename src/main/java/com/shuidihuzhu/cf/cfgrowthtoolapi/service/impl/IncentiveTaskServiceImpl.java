package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qcloud.cos.utils.DateUtils;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.incentive.IncentivePersonTaskDetailDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.incentive.IncentivePersonTaskRelDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.incentive.IncentiveTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.IncentiveTaskEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.incentive.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.incentive.awardRule.IncentiveAwardConfigModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.incentive.awardRule.IncentiveTaskBaseRule;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.incentive.awardRule.support.IncentiveSingleCaseAwardRuleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.incentive.awardRule.support.IncentiveTotalDonateAwardRuleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IncentiveTaskService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.dao.IncentivePersonTaskDetailDao;
import com.shuidihuzhu.cf.dao.IncentivePersonTaskRelDao;
import com.shuidihuzhu.cf.dao.IncentiveTaskDao;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import java.text.NumberFormat;



import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2022/1/6 下午2:03
 */
@Service
@Slf4j
public class IncentiveTaskServiceImpl implements IncentiveTaskService {
    @Autowired
    private IncentiveTaskDao incentiveTaskDao;
    @Autowired
    private IncentivePersonTaskRelDao incentivePersonTaskRelDao;
    @Autowired
    private IncentivePersonTaskDetailDao incentivePersonTaskDetailDao;
    @Resource(name = "cfOlapCaseStat")
    private RedissonHandler redissonHandler;


    @Autowired
    private CustomEventPublisher customEventPublisher;


    @Override
    public List<IncentivePersonTaskRelDO> selectListByTaskId(long taskId) {
        return incentivePersonTaskRelDao.selectListByTaskId(taskId);
    }


    @Override
    public IncentiveTaskDO getIncentiveTaskById(Long id) {
        return incentiveTaskDao.selectByPrimaryKey(id);
    }

    @Override
    public List<IncentivePersonTaskDTO> listTaskOfVolunteer(Set<String> uniqueCodeList, IncentiveTaskEnums.TaskStatusEnum taskStatusEnum, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(uniqueCodeList)) return Lists.newArrayList();
        return incentivePersonTaskRelDao.listTaskByTaskStatus(uniqueCodeList, taskStatusEnum.getCode(), startDate, endDate);
    }

    @Override
    public long countListIncentiveTaskForSea(IncentiveTaskSearchParam incentiveTaskSearchParam) {
        return incentiveTaskDao.countListIncentiveTaskForSea(incentiveTaskSearchParam);
    }

    @Override
    public List<IncentiveTaskDO> listIncentiveTaskForSea(IncentiveTaskSearchParam incentiveTaskSearchParam) {
        incentiveTaskSearchParam.setOffset((incentiveTaskSearchParam.getPageNo()-1)*incentiveTaskSearchParam.getPageSize());
        return incentiveTaskDao.listIncentiveTaskForSea(incentiveTaskSearchParam);
    }

    @Override
    public int updateIncentiveTaskUseStatus(Long incentiveTaskId, int useStatus,String userName) {
        return incentiveTaskDao.updateIncentiveTaskUseStatus(incentiveTaskId, useStatus,userName);
    }

    @Override
    public List<IncentivePersonTaskRelDO> listPersonTaskByTaskIdForSea(Long incentiveTaskId, Integer pageNo, Integer pageSize) {
        return incentivePersonTaskRelDao.listPersonTaskByTaskIdForSea(incentiveTaskId, (pageNo-1)*pageSize,pageSize);
    }
    @Override
    public long countListPersonTaskByTaskIdForSea(Long incentiveTaskId) {
        return incentivePersonTaskRelDao.countListPersonTaskByTaskIdForSea(incentiveTaskId);
    }
    @Override
    public List<IncentivePersonDonateStatistics> listPersonTaskDonateStatistics(List<Long> personTaskRelIdList) {
        if (CollectionUtils.isEmpty(personTaskRelIdList)) return Lists.newArrayList();
        return incentivePersonTaskDetailDao.listPersonTaskDonateStatistics(personTaskRelIdList);
    }
    @Override
    public List<IncentivePersonCaseStatistics> listPersonTaskCaseStatistics(List<Long> personTaskRelIdList, Integer singleCaseDonateLimit) {
        if (CollectionUtils.isEmpty(personTaskRelIdList)) return Lists.newArrayList();
        return incentivePersonTaskDetailDao.listPersonTaskCaseStatistics(personTaskRelIdList, singleCaseDonateLimit);
    }

    @Override
    public List<IncentiveTaskDO> listTaskByCalcAwardEndDate(Date startDate, Date endDate) {
        return incentiveTaskDao.listTaskByCalcAwardEndDate(startDate, endDate);
    }

    @Override
    public void saveIncentiveTask(IncentiveTaskDTO incentiveTaskDTO) {
        incentiveTaskDao.insert(incentiveTaskDTO);
    }

    @Override
    public void updateIncentiveTask(IncentiveTaskDTO incentiveTaskDTO) {
        incentiveTaskDao.updateByPrimaryKeySelective(incentiveTaskDTO);
    }

    @Override
    public void savePersonForIncentiveTask(IncentiveTaskDTO incentiveTaskDTO) {
        List<String> uniqueCodeInDbOfTask = incentivePersonTaskRelDao.listUniqueCodeByIncentiveTaskId(incentiveTaskDTO.getId());
        if (CollectionUtils.isEmpty(incentiveTaskDTO.getPersonList())) return;
        List<IncentivePersonModel> needSavePerson = incentiveTaskDTO.getPersonList().stream().filter(item -> !uniqueCodeInDbOfTask.contains(item.getVolunteerUniqueCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needSavePerson)) return;
        incentivePersonTaskRelDao.batchSave(needSavePerson, incentiveTaskDTO.getId());
    }
    @Override
    public List<IncentivePersonTaskDTO> listTaskByTaskStatusAndLimit(String volunteerUniqueCode, int taskStatus, Date date, Long taskIdMin, int pageSize) {
        return incentivePersonTaskRelDao.listTaskByTaskStatusAndLimit(volunteerUniqueCode,taskStatus, date, date, taskIdMin, pageSize);
    }

    @Override
    public IncentivePersonTaskRelDO selectByTaskIdAndUniqueCode(long taskId, String volunteerUniqueCode) {
        return incentivePersonTaskRelDao.selectByTaskIdAndUniqueCode(taskId, volunteerUniqueCode);
    }

    @Override
    public int updateTaskStatusAndReceiveTime(Long id, int taskStatus, Date receiveTime) {
        return incentivePersonTaskRelDao.updateTaskStatusAndReceiveTime(id, taskStatus, receiveTime);
    }

    @Override
    public List<IncentivePersonTaskDetailDO> selectByPersonTaskRelIdList(List<Long> personTaskRelIdList) {
        if (CollectionUtils.isEmpty(personTaskRelIdList)) return Lists.newArrayList();
        return incentivePersonTaskDetailDao.selectByPersonTaskRelIdList(personTaskRelIdList);
    }

    @Override
    public List<IncentivePersonTaskDTO> listTaskByUniqueCode(String volunteerUniqueCode) {
        return incentivePersonTaskRelDao.listTaskByUniqueCode(volunteerUniqueCode);
    }


    @Override
    public void saveOrUpdatePersonTaskDetailOfDonateNum(Date caseCreteTime, Date donatedDate, int donateNum, CfBdCaseInfoDo cfBdCaseInfoDo){
        if (caseCreteTime==null || donatedDate==null || cfBdCaseInfoDo==null) return;
        String lockName = "growthtool_incentive_task_" + cfBdCaseInfoDo.getCaseId();
        RLock lock = null;
        try {
            lock = redissonHandler.getLock(lockName);
            if (!lock.tryLock()) {
                log.info("saveOrUpdatePersonTaskDetailOfDonateNum handle get lock fail,key:{}", lockName);
                return;
            }
            log.info("saveOrUpdatePersonTaskDetailOfDonateNum handle key:{},donateNum:{} cfBdCaseInfoDo:{}", lockName, donateNum, JSON.toJSONString(cfBdCaseInfoDo));
            doSaveOrUpdatePersonTaskDetailOfDonatedNum(caseCreteTime, donatedDate, donateNum, cfBdCaseInfoDo);
        } finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    private void doSaveOrUpdatePersonTaskDetailOfDonatedNum(Date caseCreteTime, Date donatedDate, int donateNum, CfBdCaseInfoDo cfBdCaseInfoDo) {
        log.info("doSaveOrUpdatePersonTaskDetailOfDonatedNum caseId:{} donateNum:{}", cfBdCaseInfoDo.getCaseId(), donateNum);
        //限制案例时间
        //进行中任务(时间上是进行中 + useStatus=1) 领取后： 领取时间<=案例开始时间<=奖励结束时间
        List<IncentiveTaskDO> incentiveTasks = incentiveTaskDao.listTaskByStartDateWithCalcAwardEndDate(caseCreteTime).stream()
                .filter(item -> BooleanUtils.toBoolean(item.getUseStatus())).collect(Collectors.toList());
        log.info("doSaveOrUpdatePersonTaskDetailOfDonatedNum listTaskByStartDateWithCalcAwardEndDate:{}", JSON.toJSONString(incentiveTasks));
        if (CollectionUtils.isEmpty(incentiveTasks)) return;
        // 捐单时间也要在 开始时间-截止时间之内
        // 进行中任务，捐单时间要在 任务领取时间--截止时间之内
        List<Long> incentiveTaskIds = incentiveTasks.stream()
                .filter(item -> donatedDate.getTime()>=item.getStartDate().getTime() && donatedDate.getTime()<=item.getCalcAwardEndDate().getTime())
                .map(IncentiveTaskDO::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(incentiveTaskIds)) return;
        List<IncentivePersonTaskRelDO> personTaskRelList = incentivePersonTaskRelDao.selectByTaskIdsAndUniqueCode(incentiveTaskIds, cfBdCaseInfoDo.getUniqueCode());
        //限制案例在领取时间之后,限制捐单在领取时间之后
        List<IncentivePersonTaskRelDO> notNullPersonTaskRelList = personTaskRelList.stream().filter(item -> item.getReceiveTime() != null).collect(Collectors.toList());
        List<IncentivePersonTaskRelDO> filterTaskRelDOList = notNullPersonTaskRelList.stream()
                .filter(item -> caseCreteTime.getTime()>=item.getReceiveTime().getTime() && donatedDate.getTime()>=item.getReceiveTime().getTime())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterTaskRelDOList)) return;
        //进行当前金额是否大于奖励max判断并进行群机器人报警
        //判断当前已有的明细的金额
        rewardMaxCheck(filterTaskRelDOList,donateNum,cfBdCaseInfoDo,false);
        //当前明细进入明细表后，再次计算最新的明细金额
        rewardMaxCheck(filterTaskRelDOList,donateNum,cfBdCaseInfoDo,true);

        //更新任务完成状态
        updateCompletionStatus(filterTaskRelDOList);
    }


    /**
     * 奖励上限校验
     */
    private void rewardMaxCheck(List<IncentivePersonTaskRelDO> taskRelDOS,int donateNum,CfBdCaseInfoDo cfBdCaseInfoDo,boolean isDetailFlag){
        List<Long> taskIds = taskRelDOS.stream().map(IncentivePersonTaskRelDO::getIncentiveTaskId).collect(Collectors.toList());
        List<IncentiveTaskDO> incentiveTaskDOS = incentiveTaskDao.selectByTaskIdList(taskIds);
        List<IncentivePersonTaskRelDO> personTaskRelDOS =  incentivePersonTaskRelDao.selectByTaskIds(taskIds);
        Map<Long, List<IncentivePersonTaskRelDO>> relIdDOMap = personTaskRelDOS.stream().collect(Collectors.groupingBy(IncentivePersonTaskRelDO::getIncentiveTaskId));
        for (IncentiveTaskDO taskDO : incentiveTaskDOS){
                List<IncentivePersonTaskRelDO> personTaskRelDOList =  relIdDOMap.get(taskDO.getId());
                List<IncentivePersonTaskVO> taskAwardVOS = buildIncentivePersonTaskVOList(taskDO, personTaskRelDOList);
                if (CollectionUtils.isEmpty(taskAwardVOS)){
                    return;
                }
                long taskAwardSum = taskAwardVOS.stream().mapToLong(IncentivePersonTaskVO::getAwardAmount).sum();
                log.info("taskAwardSum:{},taskId:{}",taskAwardSum,taskDO.getId());
                //添加明细时，只计算当前人的明细
                personTaskRelDOList = personTaskRelDOList.stream().filter(item -> item.getVolunteerUniqueCode().equals(cfBdCaseInfoDo.getUniqueCode())).collect(Collectors.toList());
                //比例值判断，并报警
                percentCalculate(taskAwardSum,taskDO,donateNum,personTaskRelDOList,cfBdCaseInfoDo,isDetailFlag);
        }
    }


    /**
     * 当前奖励总金额与奖励上限金额占比情况
     * @param awardSum
     * @param taskDO
     */
    private void percentCalculate(long awardSum, IncentiveTaskDO taskDO,int donateNum,List<IncentivePersonTaskRelDO> taskRelDOS,CfBdCaseInfoDo cfBdCaseInfoDo,boolean isDetailFlag) {
        long awardMax = Long.parseLong(taskDO.getTaskAwardMax());
        NumberFormat numberFormat = NumberFormat.getInstance();
        //setMaximumFractionDigits 函数设置显示的数字位数为格式化对象设定小数点后的显示的最多位,但注意的是显示的最后位是舍入的
        numberFormat.setMaximumFractionDigits(2);
        String percentnum = numberFormat.format((float)awardSum/(float) awardMax*100);
       if (awardMax > awardSum){
           lessThanMax(awardSum,taskDO,percentnum,donateNum,taskRelDOS,cfBdCaseInfoDo,isDetailFlag);
       }else {
           //大于等于上限
           moreThanMax(awardSum,taskDO,percentnum);
       }
    }

    /**
     * 大于或等于上限
     * @param awardSum
     * @param taskDO
     * @param percentum
     */
    private void moreThanMax(long awardSum, IncentiveTaskDO taskDO, String percentum) {
        //报警处理
        if (taskDO.getUseStatus() == 1){
            //当任务已经关闭时，无需再次发消息
            percentum = percentum.substring(0,1)+"00";
            AlarmBotService.sentText(GeneralConstant.INCENTIVE_TASK_AWARD_MAX_GROUP_ID,
                    hintContent(awardSum, taskDO, percentum, false), null, null);
            //关闭任务
            incentiveTaskDao.updateIncentiveTaskUseStatus(taskDO.getId(), 0, "系统");
            //添加操作记录
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(taskDO.getId()), OperateTypeEnum.INCENTIVE_USE_STATUS_EDIT.getDesc(),
                    OperateTypeEnum.INCENTIVE_USE_STATUS_EDIT,
                    "任务id：" + taskDO.getId() + ",任务名称：" + taskDO.getTaskName() + "修改状态：" + "系统自动关闭", 0L, "系统"));
        }
    }

    /**
     * 未达到上限
     * @param awardSum
     * @param taskDO
     * @param percentnum
     */
    private void lessThanMax(long awardSum, IncentiveTaskDO taskDO,String percentnum,int donateNum,List<IncentivePersonTaskRelDO> taskRelDOS,CfBdCaseInfoDo cfBdCaseInfoDo,boolean isDetailFlag){
        //报警
        robotAlarmPercentNum(awardSum,taskDO,percentnum);
        //插入明细表,需要区分，插入明细之后的校验，无论结果如何，无需再次插入明细表
        if (!isDetailFlag){
            List<Long> personTaskRelIds = taskRelDOS.stream().map(IncentivePersonTaskRelDO::getId).collect(Collectors.toList());
            //激励任务捐款明细
            List<IncentivePersonTaskDetailDO> taskDetails = incentivePersonTaskDetailDao.selectByPersonTaskRelIdsWithCaseId(personTaskRelIds, Long.valueOf(cfBdCaseInfoDo.getCaseId()));
            List<Long> needSavePersonTaskRelIds = personTaskRelIds;
            if (CollectionUtils.isNotEmpty(taskDetails)) {
                //需要更新 （也可能需要插入）
                personTaskRelIds.removeAll(taskDetails.stream().map(IncentivePersonTaskDetailDO::getPersonTaskRelId).collect(Collectors.toList()));
                needSavePersonTaskRelIds = personTaskRelIds;
                incentivePersonTaskDetailDao.updateDonateNumByIds(taskDetails.stream().map(IncentivePersonTaskDetailDO::getId).collect(Collectors.toList()), donateNum);
            }
            if (CollectionUtils.isNotEmpty(needSavePersonTaskRelIds)) {
                List<Long> finalNeedSavePersonTaskRelIds = needSavePersonTaskRelIds;
                incentivePersonTaskDetailDao.batchSave(taskRelDOS.stream().filter(item -> finalNeedSavePersonTaskRelIds.contains(item.getId())).collect(Collectors.toList()), donateNum, cfBdCaseInfoDo);
            }
        }
    }


    /**
     * 根据比率进行机器人报警
     */
    private void robotAlarmPercentNum(long awardSum, IncentiveTaskDO taskDO,String percentnum){

        if(Double.valueOf(percentnum)<70){
            return;
        }
        //进行数据处理
        percentnum = percentnum.substring(0,1)+"0";
        String taskPercentKey = "growthtool_incentive_task_percentKey" + taskDO.getId() + percentnum;
        long diffDate = taskDO.getEndDate().getTime()-new Date().getTime();
        if(redissonHandler.setNX(taskPercentKey,1,diffDate)){
            AlarmBotService.sentText(GeneralConstant.INCENTIVE_TASK_AWARD_MAX_GROUP_ID,
                    hintContent(awardSum,taskDO,percentnum,true) , null, null);
        }
    }


    /**
     * 报警消息整理
     * @param awardSum
     * @param taskDO
     * @param percentum
     * @return
     */
    private String hintContent(long awardSum, IncentiveTaskDO taskDO,String percentum,boolean isMoreMax){
        return "任务ID：" + taskDO.getId() + " 任务名称：" +taskDO.getTaskName() +" 活动奖金上限："+taskDO.getTaskAwardMax() +
                "元，当前该任务消耗奖金："+ awardSum + "元，已经达到该任务奖金上限的" + percentum +"%" + (isMoreMax? "，请及时关注！" : "，系统已经自动关闭该任务，请及时关注！");
    }


    /**
     * 获取当前任务奖励明细
     * @param incentiveTaskDO
     * @param personTaskRelDOList
     * @return
     */
    private List<IncentivePersonTaskVO> buildIncentivePersonTaskVOList(IncentiveTaskDO incentiveTaskDO, List<IncentivePersonTaskRelDO> personTaskRelDOList) {
        List<Long> personTaskIdList = personTaskRelDOList.stream().map(IncentivePersonTaskRelDO::getId).collect(Collectors.toList());
        Map<String, IncentivePersonDonateStatistics> uniqueCodeMapDonate = listPersonTaskDonateStatistics(personTaskIdList).stream().collect(Collectors.toMap(IncentivePersonDonateStatistics::getUniqueCode, Function.identity(), (o1, o2)->o2));
        Map<String, IncentivePersonCaseStatistics> uniqueCodeMapCaseNum = Maps.newHashMap();
        if (incentiveTaskDO.getRule()!=null && incentiveTaskDO.getRule().getSingleCaseAwardRuleModel()!=null) {
            int singleCaseDonateLimit = Optional.ofNullable(incentiveTaskDO.getRule().getSingleCaseAwardRuleModel().getStandardValue()).orElse(0);
            uniqueCodeMapCaseNum.putAll(listPersonTaskCaseStatistics(personTaskIdList,singleCaseDonateLimit).stream().collect(Collectors.toMap(IncentivePersonCaseStatistics::getUniqueCode, Function.identity(), (o1,o2)->o2)));
        }
        return personTaskRelDOList.stream().map(item -> {
            IncentivePersonDonateStatistics donateStatistics = uniqueCodeMapDonate.get(item.getVolunteerUniqueCode());
            IncentivePersonCaseStatistics caseStatistics = uniqueCodeMapCaseNum.get(item.getVolunteerUniqueCode());
            long awardAmount = 0;
            if (incentiveTaskDO.getRule()!=null) {
                awardAmount += calcSingleCaseAwardRuleModel(caseStatistics, incentiveTaskDO.getRule().getSingleCaseAwardRuleModel());
                awardAmount += calcTotalDonateAwardRuleModel(donateStatistics, incentiveTaskDO.getRule().getTotalDonateAwardRuleModel());
            }
            return new IncentivePersonTaskVO()
                    .setVolunteerUniqueCode(item.getVolunteerUniqueCode())
                    .setCaseNum(Optional.ofNullable(caseStatistics).map(IncentivePersonCaseStatistics::getCaseNum).orElse(0))
                    .setCaseIdsStr(Optional.ofNullable(caseStatistics).map(IncentivePersonCaseStatistics::getCaseIdsStr).orElse(""))
                    .setTotalDonateNum(Optional.ofNullable(donateStatistics).map(IncentivePersonDonateStatistics::getTotalDonateNum).orElse(0L))
                    .setAwardAmount(awardAmount);
        }).collect(Collectors.toList());
    }






    private long calcTotalDonateAwardRuleModel(IncentivePersonDonateStatistics donateStatistics, IncentiveTotalDonateAwardRuleModel totalDonateAwardRuleModel) {
        long totalAmount = 0;
        if (totalDonateAwardRuleModel==null || CollectionUtils.isEmpty(totalDonateAwardRuleModel.getConfigModelList()) || donateStatistics == null) return totalAmount;
        for (IncentiveAwardConfigModel configModel : totalDonateAwardRuleModel.getConfigModelList()) {
            if (donateStatistics.getTotalDonateNum() >= configModel.getStandardValue()) totalAmount += configModel.getAwardAmount();
        }
        return totalAmount;
    }

    private long calcSingleCaseAwardRuleModel(IncentivePersonCaseStatistics caseStatistics, IncentiveSingleCaseAwardRuleModel singleCaseAwardRuleModel) {
        long totalAmount = 0;
        if (singleCaseAwardRuleModel==null || CollectionUtils.isEmpty(singleCaseAwardRuleModel.getConfigModelList()) || caseStatistics == null) return totalAmount;
        for (IncentiveAwardConfigModel configModel : singleCaseAwardRuleModel.getConfigModelList()) {
            if (caseStatistics.getCaseNum() >= configModel.getStandardValue()) totalAmount += configModel.getAwardAmount();
        }
        return totalAmount;
    }




    /**
     * 更新任务完成状态
     */
    private void updateCompletionStatus(List<IncentivePersonTaskRelDO> taskRelDOList) {
        List<Long> personTaskRelIds = taskRelDOList.stream().map(IncentivePersonTaskRelDO::getId).collect(Collectors.toList());
        List<Long> taskIds = taskRelDOList.stream().map(IncentivePersonTaskRelDO::getIncentiveTaskId).collect(Collectors.toList());
        //根据taskId分组
        Map<Long,List<IncentivePersonTaskRelDO>> taskRelMap =   taskRelDOList.stream().collect(Collectors.groupingBy(IncentivePersonTaskRelDO::getIncentiveTaskId));
        List<IncentivePersonTaskDetailDO> personTaskDetailDOS = incentivePersonTaskDetailDao.selectByPersonTaskRelIdList(personTaskRelIds);
        if (CollectionUtils.isEmpty(personTaskDetailDOS)) return;

        //明细根据relId分组
        Map<Long,List<IncentivePersonTaskDetailDO>> detailMap =   personTaskDetailDOS.stream().collect(Collectors.groupingBy(IncentivePersonTaskDetailDO::getPersonTaskRelId));
        List<IncentiveTaskDO> taskDOS = incentiveTaskDao.selectByTaskIdList(taskIds);
        if (CollectionUtils.isEmpty(taskDOS)) return;
        //匹配规则计算
        matchTaskRule(detailMap,taskDOS,taskRelMap);
    }

    /**
     * 匹配规则计算
     * @param detailMap
     */
    private void matchTaskRule(Map<Long, List<IncentivePersonTaskDetailDO>> detailMap, List<IncentiveTaskDO> taskDOS,Map<Long,List<IncentivePersonTaskRelDO>> taskRelMap) {
        for (IncentiveTaskDO taskDO : taskDOS){
            List<IncentivePersonTaskRelDO> taskRelList =  taskRelMap.get(taskDO.getId());

            List<IncentivePersonTaskRelDO> needUpdateRelList = new ArrayList<>();
            for (IncentivePersonTaskRelDO taskRelDO : taskRelList){
                List<IncentivePersonTaskDetailDO> detailDOS = detailMap.get(taskRelDO.getId());
                //匹配任务的规则
                //任务奖励设置
                IncentiveTaskRuleModel incentiveTaskRuleModel = taskDO.getRule();
                IncentiveSingleCaseAwardRuleModel singleCaseAwardRuleModel = incentiveTaskRuleModel.getSingleCaseAwardRuleModel();
                IncentiveTotalDonateAwardRuleModel totalDonateAwardRuleModel = incentiveTaskRuleModel.getTotalDonateAwardRuleModel();
                int caseAmount = 0;
                int donateNum = 0;
                //案例
                if (singleCaseAwardRuleModel != null) {
                    if (CollectionUtils.isNotEmpty(detailDOS)) {
                        for (IncentivePersonTaskDetailDO personTaskDO : detailDOS) {
                            //案例数，需满足rule中案例限制
                            if (personTaskDO.getDonateNum() >= singleCaseAwardRuleModel.getStandardValue()) {
                                caseAmount++;
                            }
                        }
                    }
                    //案例任务应达标值
                   int needProgress = trimNeedProgress(singleCaseAwardRuleModel, null);
                   int taskStatus =  trimTaskDrawStatus(needProgress,caseAmount);
                   if (taskStatus == IncentiveTaskEnums.TaskDrawStatusEnum.COMPLETED.getCode()){
                       taskRelDO.setFinishTime(new Date());
                   }
                   taskRelDO.setTaskStatus(taskStatus);
                   needUpdateRelList.add(taskRelDO);

                }
                //捐单
                if (totalDonateAwardRuleModel != null) {
                    if (CollectionUtils.isNotEmpty(detailDOS)) {
                        for (IncentivePersonTaskDetailDO personTaskDO : detailDOS) {
                            //总捐单量
                            donateNum += personTaskDO.getDonateNum();
                        }
                    }
                    //捐单任务应达标值
                    int needProgress = trimNeedProgress(null, totalDonateAwardRuleModel);
                    int taskStatus =  trimTaskDrawStatus(needProgress,donateNum);
                    if (taskStatus == IncentiveTaskEnums.TaskDrawStatusEnum.COMPLETED.getCode()){
                        taskRelDO.setFinishTime(new Date());
                    }
                    taskRelDO.setTaskStatus(taskStatus);
                    needUpdateRelList.add(taskRelDO);
                }
            }
            incentivePersonTaskRelDao.updateTaskStatus(needUpdateRelList);
        }

        }


    /**
     * 完成进度计算
     * @param needProgress
     * @param amount
     * @return
     */
    private int trimTaskDrawStatus(int needProgress, int amount) {
        if (0 < amount && amount < needProgress) {
         return  IncentiveTaskEnums.TaskDrawStatusEnum.PARTIYCOMPLETED.getCode();
        }
        if (needProgress <= amount) {
         return  IncentiveTaskEnums.TaskDrawStatusEnum.COMPLETED.getCode();
        }
        if (amount == 0){
            return  IncentiveTaskEnums.TaskDrawStatusEnum.UNFINISHED.getCode();
        }
        return -1;
    }


    /**
     * 应达标值计算
     *
     * @param singleCaseAwardRuleModel
     * @param totalDonateAwardRuleModel
     * @return
     */
    private int trimNeedProgress(IncentiveSingleCaseAwardRuleModel singleCaseAwardRuleModel, IncentiveTotalDonateAwardRuleModel totalDonateAwardRuleModel) {
        IncentiveTaskBaseRule incentiveTaskBaseRule = new IncentiveTaskBaseRule();
        if (singleCaseAwardRuleModel != null) {
            incentiveTaskBaseRule = singleCaseAwardRuleModel;
        }
        if (totalDonateAwardRuleModel != null) {
            incentiveTaskBaseRule = totalDonateAwardRuleModel;
        }
        //正序排序
        List<Integer> sortValueList = incentiveTaskBaseRule.getConfigModelList().stream().map(IncentiveAwardConfigModel::getStandardValue).sorted().collect(Collectors.toList());
        return sortValueList.get(sortValueList.size() - 1);
    }


    public static void main(String[] args) {
        long awardMax = 20000;
        long awardSum = 14270;
        NumberFormat numberFormat = NumberFormat.getInstance();
        //setMaximumFractionDigits 函数设置显示的数字位数为格式化对象设定小数点后的显示的最多位,但注意的是显示的最后位是舍入的
        numberFormat.setMaximumFractionDigits(2);
        String percentum = numberFormat.format((float)awardSum/(float) awardMax*100);
        System.out.println(percentum);
    }
}
