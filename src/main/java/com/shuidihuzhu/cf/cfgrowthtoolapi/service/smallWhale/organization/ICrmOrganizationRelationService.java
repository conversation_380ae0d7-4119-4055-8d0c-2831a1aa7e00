package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2020-05-12 14:12
 **/
public interface ICrmOrganizationRelationService {

    //-----opt 组织和人的关系----

    /**
     * 新增成员
     * @param bdCrmOrgUserRelationDO
     * @param adminUserId
     */
    void addMember(BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO, long adminUserId);

    /**
     * 删除成员
     * @param relationId
     * @param adminUserId
     */
    void deleteMemberById(long relationId, long adminUserId);

    /**
     * 删除成员,自动解绑,离职
     */
    void autoDeleteMemberId(String uniqueCode, String comment);

    /**
     * 返回当前有效的关系
     * @param uniqueCode
     * @return
     */
    List<BdCrmOrgUserRelationDO> listMemberOrgRelationByUniqueCode(String uniqueCode);

    /**
     * 返回人员信息即使已经删除
     * @param uniqueCode
     * @return
     */
    List<BdCrmOrgUserRelationDO> listMemberOrgRelationNoMatterDelete(String uniqueCode);

    /**
     * 根据uniqueCode查找
     * @param uniqueCodes
     * @return
     */
    List<BdCrmOrgUserRelationDO> listByUniqueCodes(List<String> uniqueCodes);

    /**
     * 根据uniqueCode查找
     * @param uniqueCode
     * @return
     */
    BdCrmOrgUserRelationDO getByUniqueCode(String uniqueCode);

    /**
     * 根据mis返回用户相关的组织
     * @param mis
     * @return
     */
    List<BdCrmOrgUserRelationDO> listMemberOrgRelationByMis(String mis);


    /**
     * 根据misName返回用户相关的组织
     * @param misName
     * @return
     */
    List<BdCrmOrgUserRelationDO> listMemberOrgRelationByMisName(String misName);

    /**
     * 返回人员信息即使已经删除
     * @param uniqueCode
     * @return
     */
    List<BdCrmOrgUserRelationDO> listMemberOrgRelationNoMatterDeleteByUniqueCode(String uniqueCode);

    /**
     * 返回当前组织下的所有有效的绑定关系
     * @param orgId
     * @return
     */
    List<BdCrmOrgUserRelationDO> listRelationByOrgId(long orgId);

    /**
     * @param orgIds
     * @return
     */
    Map<Long, List<BdCrmOrgUserRelationDO>> batchListRelation(List<Long> orgIds);


    /**
     * 批量查询组织人员关系,后面会替换掉 listByOrgIds(java.util.List)
     * @param orgIds
     * @return
     */
    List<BdCrmOrgUserRelationDO> listByOrgIdsFromDB(List<Long> orgIds);

    /**
     * 这种直接给sea后台的就直接走db查询
     */
    List<BdCrmOrgUserRelationDO> pageRelationByOrgId(long orgId, int offset, int limit);

    /**
     * 根据组织id统计组织上绑定的人员
     */
    int countRelationByOrgId(long orgId);


    List<BdCrmOrgUserRelationDO> listByOrgIds(List<Long> orgIds);

    /**
     * 根据 misList 查询对应的关系
     */
    List<BdCrmOrgUserRelationDO> listByMisList(Collection<String> misList);

    /**
     * 根据 uniqueCodeList 查询对应的关系
     * @param uniqueCodeList
     * @return
     */
    List<BdCrmOrgUserRelationDO> listByUniqueCodes(Collection<String> uniqueCodeList);

    /**
     * 根据misName查询对应的关系
     */
    List<BdCrmOrgUserRelationDO> listByMisNameList(List<String> misNameList);

    /**
     * 获取所有的绑定关系，慎用，慎用，慎用
     * @return
     */
    List<BdCrmOrgUserRelationDO> getAllValidRelation();

    List<BdCrmOrgUserRelationDO> listAllValidRelation(long id, int limit);

    /**
     * 模糊匹配姓名
     * @param likeValue
     * @return
     */
    List<BdCrmOrgUserRelationDO> getUserByLikeUserName(String likeValue);

    /**
     * 查询离职顾问
     *
     * @param orgIds
     * @param startTime
     * @param endTime
     * @return
     */
    List<BdCrmOrgUserRelationDO> listAutoDeleteMember(List<Long> orgIds, String startTime, String endTime);

    BdCrmOrgUserRelationDO getByRelationId(long id);
}
