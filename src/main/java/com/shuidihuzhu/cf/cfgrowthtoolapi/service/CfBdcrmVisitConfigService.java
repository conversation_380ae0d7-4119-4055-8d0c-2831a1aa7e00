package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfBdcrmVisitConfigModel;

import java.util.List;
import java.util.Set;

public interface CfBdcrmVisitConfigService {

    /**
     * 获取所有区域的模版信息
     * @return
     */
    List<CfBdcrmVisitConfigModel> queryAllLatestTemplateConfigList();

    List<CfBdcrmVisitConfigModel> queryVisitConfigByIdList(Set<Long> idList);



}
