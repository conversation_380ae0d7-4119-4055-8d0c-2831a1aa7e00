package com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.campaignv2.HospitalDateSummaryDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DateQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.CampHospitalSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.FollowHospitalSearchParam;

import java.util.List;

/**
 * 医院维度汇总表(HospitalDateSummary)表服务接口
 *
 * <AUTHOR>
 * @since 2021-08-27 15:09:07
 */
public interface HospitalDateSummaryService {

    HospitalDateSummaryDO queryById(long id);

    List<HospitalDateSummaryDO> listByOrgIds(List<Integer> orgIds, List<String> hospitalCodes, DateQueryParam dateQueryParam);

    int countSearchHospital(FollowHospitalSearchParam searchParam, List<Integer> orgIds);

    List<HospitalDateSummaryDO> searchHospital(FollowHospitalSearchParam searchParam, List<Integer> orgIds);

    List<HospitalDateSummaryDO> hospitalHomeView(CampHospitalSearchParam hospitalSearchParam);

    List<HospitalDateSummaryDO> campaignHospitalView(CampHospitalSearchParam hospitalSearchParam);

    List<HospitalDateSummaryDO> highDataCampaignHospitalView(CampHospitalSearchParam hospitalSearchParam);

    List<HospitalDateSummaryDO> avgDataCampaignHospitalView(CampHospitalSearchParam hospitalSearchParam);

}