package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DateQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PartnerEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerCaseRelationService;
import com.shuidihuzhu.cf.dao.lovepartner.CfPartnerCaseRelationDao;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerCaseRelationDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */

@Service
@Slf4j
public class CfPartnerCaseRelationServiceImpl implements CfPartnerCaseRelationService {

    @Autowired
    private CfPartnerCaseRelationDao caseRelationDao;

    @Override
    public int insert(CfPartnerCaseRelationDo cfPartnerCaseRelationDo) {
        return caseRelationDao.insert(cfPartnerCaseRelationDo);
    }

    @Override
    public CfPartnerCaseRelationDo getCaseRelationByCaseId(Integer caseId) {
        return caseRelationDao.getCaseRelationByCaseId(caseId);
    }

    @Override
    public List<CfPartnerCaseRelationDo> listByPartnerUniqueCode(String partnerUniqueCode, DateQueryParam dateQueryParam) {
        return caseRelationDao.listByPartnerUniqueCode(partnerUniqueCode, dateQueryParam);
    }

    @Override
    public List<Integer> listCaseRelationWithCaseTimeAndUniqueCode(Date caseStartTime, Date caseEndTime, List<String> uniqueCodeList, PartnerEnums.CaseCalcEnums caseCalcEnum) {
        return caseRelationDao.listCaseRelationWithCaseTimeAndUniqueCode(caseStartTime,caseEndTime,uniqueCodeList,caseCalcEnum.getCode());
    }

    @Override
    public int updateCaseCalStatus(List<Integer> caseIds, PartnerEnums.CaseCalcEnums caseCalcEnum) {
        if (CollectionUtils.isEmpty(caseIds) || caseCalcEnum == null) {
            return 0;
        }
        return caseRelationDao.updateCaseCalStatus(caseIds, caseCalcEnum.getCode());
    }
}
