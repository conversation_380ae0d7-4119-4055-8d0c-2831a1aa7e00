package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfClewSpecialInfoRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfClewSpecialInfoRecordService;
import com.shuidihuzhu.cf.dao.bdcrm.CfClewSpecialInfoRecordDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/8/17 5:43 PM
 * 特殊报备信息修改记录
 */
@Service("cfClewSpecialInfoRecordService")
public class CfClewSpecialInfoRecordServiceImpl implements CfClewSpecialInfoRecordService {

    @Resource
    private CfClewSpecialInfoRecordDao cfClewSpecialInfoRecordDao;

    @Override
    public int insert(CfClewSpecialInfoRecordDO cfClewSpecialInfoRecordDO) {
        return cfClewSpecialInfoRecordDao.insert(cfClewSpecialInfoRecordDO);
    }

    @Override
    public Integer recordCount(long preposeMaterialId) {
        return cfClewSpecialInfoRecordDao.recordCount(preposeMaterialId);
    }
}
