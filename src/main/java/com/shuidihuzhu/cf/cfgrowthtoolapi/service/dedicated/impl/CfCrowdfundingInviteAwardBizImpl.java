package com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMasterForGrowthtoolDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfPatientMaterialClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.AccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.message.IMsgClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfDotService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfCrowdfundingInviteAwardBiz;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.clinet.event.center.util.MsgUtil;
import com.shuidihuzhu.cf.dao.CfToufangInviteCaseRelationDao;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInviteCaseRelationDO;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.msg.vo.Response;
import com.shuidihuzhu.msg.vo.rpc.MsgRecord;
import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-02-07
 */
@Service
@Slf4j
@RefreshScope
public class CfCrowdfundingInviteAwardBizImpl implements ICfCrowdfundingInviteAwardBiz {
    @Autowired
    private CfToufangInviteCaseRelationDao cfTaskTouFangInviteCaseRelationDao;
    @Autowired
    private ICrowdFundingFeignDelegate crowdfundingFeignClientDelegate;
    @Autowired
    private IMsgClientDelegate msgClientV2;
    @Resource
    private AccountServiceDelegate accountServiceDelegate;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ICfDotService cfDotServiceImpl;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private CfFirstApproveFeignClient cfFirstApproveFeignClient;

    @Autowired
    private ICfMasterForGrowthtoolDelegate cfMasterForGrowthtoolDelegate;

    @Autowired
    private MsgUtil msgUtil;

    @Value("${raiser.recommend.test.user:}")
    private String qaUserId;


    private static final Integer messageHasSend = 1;
    private static final Integer messageUnSend = 0;
    /**
     * 筹款激励任务在有效期内已达标
     */
    private static final Integer cfTaskReachStandard = 1;
    /**
     * 筹款激励超过有效期仍未达标
     */
    private static final Integer cfTaskReachStandardExpire = 2;

    @Override
    public void doExcuteIniviteAwardTask() {
        Date startTime = DateUtils.addDays(DateUtil.getCurrentDate(),-30);
        Date endTime = new Date();
        List<CfToufangInviteCaseRelationDO> needHandleList = cfTaskTouFangInviteCaseRelationDao.getNeedHandleInviteCase(null , startTime, endTime);
        if(CollectionUtils.isEmpty(needHandleList)){
            return ;
        }
        changeRelationStatus(startTime, needHandleList);
    }

    @Override
    public void realTimeChangeRelationStatus(long invitorId) {
        Date startTime = DateUtils.addDays(DateUtil.getCurrentDate(),-30);
        Date endTime = new Date();

        List<CfToufangInviteCaseRelationDO> needHandleList = cfTaskTouFangInviteCaseRelationDao.getNeedHandleInviteCase(String.valueOf(invitorId), startTime, endTime);

        if(CollectionUtils.isEmpty(needHandleList)){
            log.info("有效期内不存在推荐案例invitorId:{}", invitorId);
            return ;
        }

        Optional<CfToufangInviteCaseRelationDO> anyHasRecommend = cfTaskTouFangInviteCaseRelationDao
                .getInviteCaseRelation(String.valueOf(invitorId))
                .stream()
                .filter(relation -> relation.getInvitorType() == CfToufangInviteCaseRelationDO.InvitorTypeEnum.raiser.getCode())
                .filter(relation -> relation.getCompleteFlag().equals(1))
                .findAny();

        //判断是否领取过
        if (anyHasRecommend.isPresent()) {
            log.info("作为筹款人推荐已经申请过了invitorId:{}", invitorId);
            return;
        }
        log.info("发起人作为推荐人，更新是否满足条件的案例invitorId:{}", invitorId);
        changeRelationStatus(startTime, needHandleList);
    }


    private void changeRelationStatus(Date startTime, List<CfToufangInviteCaseRelationDO> needHandleList) {
        List<String> infoUuids = needHandleList.stream().map(CfToufangInviteCaseRelationDO::getInfoUuid).collect(Collectors.toList());
        Map<String, CrowdfundingInfo> infoMap = crowdfundingFeignClientDelegate.getMapByInfoUuIds(infoUuids);
        List<Integer> caseIdList = infoMap.values().stream().map(CrowdfundingInfo::getId).collect(Collectors.toList());
        Map<String, CfCaseCommonInfo> caseCommonInfoMap = crowdfundingFeignClientDelegate.getCaseCommonInfoByCaseIds(caseIdList);
        List<CfToufangInviteCaseRelationDO> reachStandardCases = Lists.newArrayList();
        List<CfToufangInviteCaseRelationDO> unreachStandardCases = Lists.newArrayList();
        List<Integer> infoIdList = Lists.newArrayList();

        List<CfToufangInviteCaseRelationDO> normalNeedHandList = needHandleList.stream()
                .filter(relation -> relation.getInvitorType() == CfToufangInviteCaseRelationDO.InvitorTypeEnum.normal.getCode())
                .collect(Collectors.toList());

        Map<String, List<CfToufangInviteCaseRelationDO>> raiserNeedHandList = needHandleList.stream()
                .filter(relation -> relation.getInvitorType() == CfToufangInviteCaseRelationDO.InvitorTypeEnum.raiser.getCode())
                .sorted(Ordering.natural().onResultOf(CfToufangInviteCaseRelationDO::getCreateTime))
                .collect(Collectors.groupingBy(CfToufangInviteCaseRelationDO::getInvitorUniqueCode));
        //筹款人推荐成功的历史信息
        List<String> hasRecommendRaisers = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(raiserNeedHandList.keySet())) {
            hasRecommendRaisers = cfTaskTouFangInviteCaseRelationDao.listHasRecommendRaiserInvitor(raiserNeedHandList.keySet());
        }
        for (String raiserId : raiserNeedHandList.keySet()) {
            List<CfToufangInviteCaseRelationDO> relationDOListByRaiser = raiserNeedHandList.get(raiserId);
            if (hasRecommendRaisers.contains(raiserId)) {
                log.info("已经存在推荐,推荐人raiserId:{}", raiserId);
                unreachStandardCases.addAll(relationDOListByRaiser);
                continue;
            }

            Optional<CfToufangInviteCaseRelationDO> firstMatch = relationDOListByRaiser.stream().filter(item -> {
                CrowdfundingInfo crowdfundingInfo = infoMap.get(item.getInfoUuid());
                CfCaseCommonInfo cfCaseCommonInfo = caseCommonInfoMap.get(item.getInfoUuid());
                Integer donationCount = crowdfundingFeignClientDelegate.getdonationCountByInfoUuId(crowdfundingInfo.getId());

                boolean isQaInvitor = getQaUserIds().contains(Long.parseLong(item.getInvitorUniqueCode()));
                boolean reachRecommendCondition = false;
                if (isQaInvitor) {
                    log.info("测试用户:{}", item.getInvitorUniqueCode());
                    reachRecommendCondition = (crowdfundingInfo.getAmount() / 100) >= 5 && donationCount >= 3 && crowdfundingInfo.getStatus().value() == 2 && CollectionUtils.isEmpty(cfCaseCommonInfo.getCrowdfundingReports());
                } else {
                    reachRecommendCondition = (crowdfundingInfo.getAmount() / 100) >= 1000 && donationCount >= 30 && crowdfundingInfo.getStatus().value() == 2 && CollectionUtils.isEmpty(cfCaseCommonInfo.getCrowdfundingReports());
                }
                if (reachRecommendCondition) {
                    log.debug("案例:{}基本条件已达标,接着验证是否是首次发起", crowdfundingInfo.getId());
                    CfFirsApproveMaterial cfFirsApproveMaterial = cfCaseCommonInfo.getCfFirsApproveMaterial();
                    if (StringUtils.isBlank(cfFirsApproveMaterial.getPatientCryptoIdcard()) && StringUtils.isBlank(cfFirsApproveMaterial.getPatientBornCard())) {
                        log.info("出生证和身份证均为空,cfFirsApproveMaterial:{}", JSON.toJSONString(cfFirsApproveMaterial));
                        return false;
                    }
                    CfFirsApproveMaterial queryParam = new CfFirsApproveMaterial();
                    if (StringUtils.isNotBlank(cfFirsApproveMaterial.getPatientCryptoIdcard())) {
                        queryParam.setPatientCryptoIdcard(cfFirsApproveMaterial.getPatientCryptoIdcard());
                    }
                    if (StringUtils.isNotBlank(cfFirsApproveMaterial.getPatientBornCard())) {
                        queryParam.setPatientBornCard(cfFirsApproveMaterial.getPatientBornCard());
                    }
                    Optional<CfFirsApproveMaterial> firstApproveMaterial = Optional.ofNullable(cfFirstApproveFeignClient.getCfFirstApproveMaterialsByParam(queryParam))
                            .map(FeignResponse::getData)
                            .orElse(Lists.newArrayList())
                            .stream()
                            .filter(fam -> fam.getInfoId() > 0)
                            .min(Ordering.natural().onResultOf(CfFirsApproveMaterial::getCreateTime));
                    firstApproveMaterial.ifPresent(fam -> log.debug("首次发起案例:{}", JSON.toJSONString(fam)));
                    return firstApproveMaterial.filter(firsApproveMaterial -> firsApproveMaterial.getInfoId() == crowdfundingInfo.getId()).isPresent();
                }
                log.debug("推荐案例:{}不达标", crowdfundingInfo.getId());
                return false;
            }).findFirst();

            if (firstMatch.isPresent()) {
                log.info("符合条件得第一个发起人作为推荐人的案例信息:{}", JSON.toJSONString(firstMatch.get()));
                reachStandardCases.add(firstMatch.get());
                unreachStandardCases.addAll(relationDOListByRaiser
                        .stream()
                        .filter(rd -> !rd.getId().equals(firstMatch.get().getId()))
                        .collect(Collectors.toList()));
            } else {
                for (CfToufangInviteCaseRelationDO caseRelationDO : relationDOListByRaiser) {
                    if(DateUtil.getFirstTimeOfDay(caseRelationDO.getCreateTime().getTime()).compareTo(startTime)==0) {
                        unreachStandardCases.add(caseRelationDO);
                    }
                }
            }
        }

        //非筹款人推荐 不补贴（老活动下线）
        unreachStandardCases.addAll(normalNeedHandList);
        //for(CfToufangInviteCaseRelationDO caseRelationDO : normalNeedHandList){
        //    CrowdfundingInfo crowdfundingInfo = infoMap.get(caseRelationDO.getInfoUuid());
        //    CfCaseCommonInfo cfCaseCommonInfo = caseCommonInfoMap.get(caseRelationDO.getInfoUuid());
        //    if(crowdfundingInfo == null || cfCaseCommonInfo == null){
        //        continue;
        //    }
        //    infoIdList.add(crowdfundingInfo.getId());
        //    Integer donationCount = crowdfundingFeignClientDelegate.getdonationCountByInfoUuId(crowdfundingInfo.getId());
        //    if((crowdfundingInfo.getAmount()/100)>=1000 && donationCount>=30 && crowdfundingInfo.getStatus().value()==2 && CollectionUtils.isEmpty(cfCaseCommonInfo.getCrowdfundingReports())){
        //        CfFirsApproveMaterial cfFirsApproveMaterial = cfCaseCommonInfo.getCfFirsApproveMaterial();
        //        OpResult<Boolean> opResult = cfPatientMaterialClientDelegateImpl.checkIsFirstCaseByPatientCryptCard(cfFirsApproveMaterial.getPatientCryptoIdcard());
        //        if (opResult.isFail()){
        //            continue;
        //        }
        //        if (opResult.getData()){
        //            reachStandardCases.add(caseRelationDO);
        //        }else{
        //            unreachStandardCases.add(caseRelationDO);
        //        }
        //    }else{
        //        if(DateUtil.getFirstTimeOfDay(caseRelationDO.getCreateTime().getTime()).compareTo(startTime)==0) {
        //            unreachStandardCases.add(caseRelationDO);
        //        }
        //    }
        //}
        reachStandardCases.forEach(item -> infoIdList.add(infoMap.get(item.getInfoUuid()).getId()));

        for(CfToufangInviteCaseRelationDO caseRelationDO : unreachStandardCases){
            cfMasterForGrowthtoolDelegate.updateCaseHandleStatus(caseRelationDO.getId(),cfTaskReachStandardExpire,messageUnSend,new Date());
        }
        for(CfToufangInviteCaseRelationDO caseRelationDO : reachStandardCases){
            cfMasterForGrowthtoolDelegate.updateCaseHandleStatus(caseRelationDO.getId(),cfTaskReachStandard,messageHasSend,null);
            if(StringUtils.isBlank(caseRelationDO.getInvitorUniqueCode())){
                continue;
            }

            CrowdfundingInfo crowdfundingInfo = infoMap.get(caseRelationDO.getInfoUuid());
            UserInfoModel userInfoModel = accountServiceDelegate.getUserInfoModelByUserId(crowdfundingInfo.getUserId());
            if(userInfoModel == null){
                continue;
            }
            cfDotServiceImpl.reportCfToufangInviteCaseRelationDO(caseRelationDO,userInfoModel);
        }
    }


    public List<Long> getQaUserIds() {
        List<Long> userIds = Lists.newArrayList();
        try {
            userIds = Splitter.on(",").splitToList(qaUserId).stream().map(Long::parseLong).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("raiser.recommend.test.user配置:{}错误", qaUserId, e);
        }
        return userIds;
    }

}
