package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponApplyRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.export.impl.CfBdCrmExcelExpoetUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.CustomWeaponSpecialModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.export.BudgetDetailExportModelForBase;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.AbstractWeaponExcelService;
import com.shuidihuzhu.client.cf.growthtool.enums.WeaponActivityTypeEnum;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * @author: fengxuan
 * @create 2020-12-10 11:35 上午
 **/
@Slf4j
@Service
public class WeaponExcelServiceForCustom extends AbstractWeaponExcelService {

    @Autowired
    private CfBdCrmExcelExpoetUtil cfBdCrmExcelExpoetUtil;

    @Override
    protected void doExportExcel(HttpServletResponse response, List<BudgetDetailExportModelForBase> baseList, long adminLongUserId) throws IOException {
        List<List<String>> head = head(baseList);
        log.info("head:{}", JSON.toJSONString(head));

        String fileName = "预算使用明细";
        List<List<Object>> data = data(baseList);

        cfBdCrmExcelExpoetUtil.exportV2(adminLongUserId, data, head, fileName);

//        EasyExcel.write(response.getOutputStream())
//                // 这里放入动态头
//                .head(head(baseList))
//                .sheet("模板")
//                // 当然这里数据也可以用 List<List<String>> 去传入
//                .doWrite(data(baseList));
    }


    private List<List<Object>> data(List<BudgetDetailExportModelForBase> baseList) {
        List<List<Object>> dataList = Lists.newArrayList();
        for (BudgetDetailExportModelForBase modelForBase : baseList) {
            CfWeaponApplyRecordDO cfWeaponApplyRecordDO = modelForBase.getCfWeaponApplyRecordDO();
            if (cfWeaponApplyRecordDO == null) {
                continue;
            }
            List<List<CustomWeaponSpecialModel>> lists = CustomWeaponSpecialModel.parseByExtInfo(cfWeaponApplyRecordDO.getExtInfo());
            log.debug("lists:{}", lists);
            for (List<CustomWeaponSpecialModel> specialModels : lists) {
                //一次遍历对应的一条记录
                List<Object> data = Lists.newArrayList();
                data.add(cfWeaponApplyRecordDO.getId() + "");
                data.add(modelForBase.getActivityName());
                data.add(modelForBase.getApplyName());
                data.add(modelForBase.getApplyTime());
                data.add(modelForBase.getApplyStatus());
                data.add(Optional.ofNullable(modelForBase.getRejectReason()).orElse(""));
                for (CustomWeaponSpecialModel specialModel : specialModels) {
                    data.add(specialModel.getValue());
                }
                data.add(modelForBase.getOrgName());
                dataList.add(data);
            }
        }
        log.debug("dataList:{}", dataList);
        return dataList;
    }


    private List<List<String>> head(List<BudgetDetailExportModelForBase> baseList) {
        List<List<String>> list = Lists.newArrayList(Lists.newArrayList());
        List<String> head0 = new ArrayList<String>();
        head0.add("申请id");
        List<String> head1 = new ArrayList<String>();
        head1.add("活动名称");
        List<String> head2 = new ArrayList<String>();
        head2.add("申请人");
        List<String> head3 = new ArrayList<String>();
        head3.add("申请时间");
        List<String> head5 = new ArrayList<String>();
        head5.add("当前审批状态");
        List<String> head6 = new ArrayList<String>();
        head6.add("驳回原因备注");
        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head5);
        list.add(head6);
        if (CollectionUtils.isNotEmpty(baseList)) {
            CfWeaponApplyRecordDO cfWeaponApplyRecordDO = baseList.get(0).getCfWeaponApplyRecordDO();
            if (cfWeaponApplyRecordDO != null) {
                List<List<CustomWeaponSpecialModel>> specialModelList = CustomWeaponSpecialModel.parseByExtInfo(cfWeaponApplyRecordDO.getExtInfo());
                if (CollectionUtils.isNotEmpty(specialModelList)) {
                    for (CustomWeaponSpecialModel customWeaponSpecialModel : specialModelList.get(0)) {
                        List<String> dataHeader = new ArrayList<String>();
                        dataHeader.add(customWeaponSpecialModel.getAttributeDesc());
                        list.add(dataHeader);
                    }
                }
            }
        }
        List<String> head4 = new ArrayList<String>();
        head4.add("申请人部门");
        list.add(head4);
        return list;
    }

    @Override
    public boolean supportActivityType(int activityType) {
        return ObjectUtils.nullSafeEquals(activityType, WeaponActivityTypeEnum.zidingyi.getCode());
    }

}
