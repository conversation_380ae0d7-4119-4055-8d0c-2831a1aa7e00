package com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.BdCaseDonateTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.CfDonateEndCaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.CfDonateUnEndCaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.CrmDonateTaskModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.DonateTaskBaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.donate.BdCaseDonateTaskParam;

import java.util.List;
import java.util.Set;

/**
 * 捐转案例分层-任务生成配置(BdCaseDonateTask)表服务接口
 *
 * <AUTHOR>
 * @since 2022-06-27 17:59:21
 */
public interface IBdCaseDonateTaskService {

    int insert(BdCaseDonateTaskDO bdCaseDonateTask);

    boolean deleteById(long id);

    void addShareCount(long id);

    void updateHandleStatus(List<Long> ids, int taskStatus, int handleStatus);

    void updateHandleStatusWithReason(List<Long> ids, int taskStatus, int handleStatus, String failReason);

    BdCaseDonateTaskDO queryById(long id);

    List<BdCaseDonateTaskDO> listByIds(List<Long> ids);

    BdCaseDonateTaskDO getByCaseIdAndDayKey(int caseId, String dayKey);

    List<BdCaseDonateTaskDO> listByDayKey(String dayKey);

    List<BdCaseDonateTaskDO> listByCaseId(int caseId);

    CrmDonateTaskModel getTaskModelByOrgIds(List<String> dateTimes, List<Long> orgIdList);

    DonateTaskBaseModel getTaskModelByUniqueCode(List<String> dateTimes, String uniqueCode);

    List<CrmDonateTaskModel> groupByUniqueCode(List<String> dateTimes, long orgId, BdCaseDonateTaskParam bdCaseDonateTaskParam);

    int pageCountTask(List<String> dateTimes, BdCaseDonateTaskParam bdCaseDonateTaskParam);

    List<BdCaseDonateTaskDO> pageListTask(List<String> dateTimes, BdCaseDonateTaskParam bdCaseDonateTaskParam);

    List<BdCaseDonateTaskDO> listByCityConfigId(long cityConfigId, int taskStatus);

    List<BdCaseDonateTaskDO> getFinishTaskByCaseId(List<Integer> caseIds);

    CfDonateUnEndCaseModel getDonateUnEndCaseByOrgIds(List<String> dateTimes, List<Long> orgIdList);

    CfDonateEndCaseModel getDonateEndCaseByOrgIds(List<String> dateTimes, List<Long> orgIdList);

    CfDonateUnEndCaseModel getDonateUnEndCaseByUniqueCodes(List<String> dateTimes, String uniqueCode);

    CfDonateEndCaseModel getDonateEndCaseByUniqueCodes(List<String> dateTimes, String uniqueCode);

}
