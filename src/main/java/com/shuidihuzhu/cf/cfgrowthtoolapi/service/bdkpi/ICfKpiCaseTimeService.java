package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseTimeDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKpiCaseApplyTimeModel;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
public interface ICfKpiCaseTimeService {

    List<String> listAllOuterShowMonthKey();

    OpResult<Integer> setOuterShow(int outerShow, String monthKey);

    CfKpiCaseTimeDO getCfKpiCaseTimeByMonthkey(String monthKey);

    OpResult<Integer> queryOuterShow();

    OpResult saveOrUpdateCaseApplyTime(CfKpiCaseApplyTimeModel cfKpiCaseApplyTime);

    OpResult checkCanMod(int status, String monthKey,String oldCityNameStr,String newCityNameStr);

    List<CfKpiCaseTimeDO> listAllMonth(List<Integer> outShowList);

    CfKpiCaseTimeDO listOuterShow();

    int countAll(Date startTime);

    List<CfKpiCaseTimeDO> pageAll(Date startTime, int offset, int limit);


}
