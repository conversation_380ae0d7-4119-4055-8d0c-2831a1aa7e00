package com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund.Impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.caserefund.CfCaseRefundTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.caserefund.CfCaseRefundDetailParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund.CfCaseRefundTaskService;
import com.shuidihuzhu.cf.dao.caserefund.CfCaseRefundTaskDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/8/22 11:26 AM
 */
@Service("cfCaseRefundTaskService")
public class CfCaseRefundTaskServiceImpl implements CfCaseRefundTaskService {

    @Resource
    private CfCaseRefundTaskDao cfCaseRefundTaskDao;

    @Override
    public int insert(CfCaseRefundTaskDO cfCaseRefundTaskDO) {
        return cfCaseRefundTaskDao.insert(cfCaseRefundTaskDO);
    }

    @Override
    public int updateCacleTime(Date cacleTime, String traceNo) {
        if (StringUtils.isEmpty(traceNo) || Objects.isNull(cacleTime)) {
            return 0;
        }
        return cfCaseRefundTaskDao.updateCancleTime(cacleTime, traceNo);
    }

    @Override
    public CfCaseRefundTaskDO getCaseRefundTaskInfo(String traceNo) {
        if (StringUtils.isEmpty(traceNo)) {
            return null;
        }
        return cfCaseRefundTaskDao.getCaseRefundTaskInfo(traceNo);
    }

    @Override
    public CfCaseRefundTaskDO getCaseRefundTaskInfoById(Integer id) {
        return cfCaseRefundTaskDao.getCaseRefundTaskInfoById(id);
    }

    @Override
    public int modifyCaseRefundTaskStatus(Integer id, Integer dealStatus) {
        Date dealTime = new Date();
        return cfCaseRefundTaskDao.modifyCaseRefundTaskStatus(id, dealStatus, dealTime);
    }

    @Override
    public List<CfCaseRefundTaskDO> getCaseRefundTaskByIds(CfCaseRefundDetailParam cfCaseRefundDetailParam) {
        if (CollectionUtils.isEmpty(cfCaseRefundDetailParam.getCaseRefundIds())) {
            return Lists.newArrayList();
        }
        return cfCaseRefundTaskDao.getCaseRefundTaskByWaitDealIds(cfCaseRefundDetailParam);
    }

    @Override
    public void updateUrgeStatus(int id) {
        cfCaseRefundTaskDao.updateUrgeStatus(id);
    }

    @Override
    public Integer getCaseRefundCountByParams(CfCaseRefundDetailParam cfCaseRefundDetailParam) {
        return cfCaseRefundTaskDao.getCaseRefundCountByParams(cfCaseRefundDetailParam);
    }

    @Override
    public List<CfCaseRefundTaskDO> getCaseRefundTaskByTime(CfCaseRefundDetailParam params) {
        if (StringUtils.isBlank(params.getStartTime()) || StringUtils.isBlank(params.getEndTime())) {
            return Lists.newArrayList();
        }
        return cfCaseRefundTaskDao.getCaseRefundTaskByTime(params);
    }
}
