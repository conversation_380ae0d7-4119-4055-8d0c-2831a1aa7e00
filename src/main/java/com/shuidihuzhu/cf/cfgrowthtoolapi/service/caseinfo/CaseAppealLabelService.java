package com.shuidihuzhu.cf.cfgrowthtoolapi.service.caseinfo;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdDelegateCaseTagRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfCaseAppealLabelModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfCaseRemoteTagLabelModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.AppealLabelSearchParam;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/12 3:13 PM
 */
public interface CaseAppealLabelService {

    OpResult<CommonPageModel<CfCaseAppealLabelModel>> getCaseLabelInfo(AppealLabelSearchParam appealLabelSearchParam);

    List<BdDelegateCaseTagRecordDO> getCaseTagRecord(Integer caseId);

    CfCaseRemoteTagLabelModel getCaseRemoteTagLabel(String infoUuid);

}
