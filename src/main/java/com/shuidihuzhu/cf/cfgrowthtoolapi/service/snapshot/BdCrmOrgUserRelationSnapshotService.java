package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdCrmOrgUserRelationSnapshot;

import java.util.List;

/**
 * 日目标人员关系信息快照(BdCrmOrgUserRelationSnapshot)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-02 14:58:48
 */
public interface BdCrmOrgUserRelationSnapshotService {

    BdCrmOrgUserRelationSnapshot queryById(long id);

    void batchInsert(List<BdCrmOrgUserRelationSnapshot> userRelationSnapshotList);

    int update(BdCrmOrgUserRelationSnapshot bdCrmOrgUserRelationSnapshot);

    boolean deleteById(long id);

    List<BdCrmOrgUserRelationSnapshot> listByUniqueCode(String dateKey, String uniqueCode);

    /**
     * 返回当前组织下的所有有效的绑定关系
     * @param orgId
     * @return
     */
    List<BdCrmOrgUserRelationSnapshot> listRelationByOrgId(String dateKey, long orgId);

}
