package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfGrowthtoolKpiLotDO;
import com.shuidihuzhu.cf.client.performance.PepClient;
import com.shuidihuzhu.cf.client.performance.calResult.ProcedureForCModel;
import com.shuidihuzhu.cf.dao.bdkpi.CfGrowthtoolKpiLotDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfGrowthtoolKpiLotService;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 批次信息维护(CfGrowthtoolKpiLot)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-04 15:29:37
 */
@Slf4j
@Service("cfGrowthtoolKpiLotService")
public class CfGrowthtoolKpiLotServiceImpl implements ICfGrowthtoolKpiLotService {

    @Resource
    private CfGrowthtoolKpiLotDao cfGrowthtoolKpiLotDao;

    @Autowired
    private PepClient pepClient;

    @Override
    public List<CfGrowthtoolKpiLotDO> listByTemplateIdAndBizType(long templateId, int bizType, int subBizType) {
        return cfGrowthtoolKpiLotDao.listByTemplateIdAndBizType(templateId, bizType, subBizType);
    }


    @Override
    public int insert(CfGrowthtoolKpiLotDO cfGrowthtoolKpiLot) {
        return cfGrowthtoolKpiLotDao.insert(cfGrowthtoolKpiLot);
    }

    @Override
    public int update(CfGrowthtoolKpiLotDO cfGrowthtoolKpiLot) {
        return cfGrowthtoolKpiLotDao.update(cfGrowthtoolKpiLot);
    }

    @Override
    public int updatePushStatus(long id, int pushStatus) {
        return cfGrowthtoolKpiLotDao.updatePushStatus(id, pushStatus);
    }

    @Override
    public void insertOrUpdate(CfGrowthtoolKpiLotDO cfGrowthtoolKpiLot) {
        //根据模板id和业务类型查询是否存在
        //批次 + 模板 + 业务类型 + 子业务类型 唯一
        List<CfGrowthtoolKpiLotDO> modelListFromDB = listByTemplateIdAndBizType(cfGrowthtoolKpiLot.getTemplateId(), cfGrowthtoolKpiLot.getBizType(), cfGrowthtoolKpiLot.getSubBizType());
        CfGrowthtoolKpiLotDO kpiLotInDB = modelListFromDB.stream()
                .filter(Objects::nonNull)
                .filter(item -> Objects.equals(item.getProcedureId(), cfGrowthtoolKpiLot.getProcedureId()))
                .findFirst()
                .orElse(null);
        if (kpiLotInDB != null) {
            cfGrowthtoolKpiLot.setId(kpiLotInDB.getId());
            update(cfGrowthtoolKpiLot);
        } else {
            insert(cfGrowthtoolKpiLot);
        }
        Map<Long, ProcedureForCModel> procedureForCModelMap = new HashMap<>();
        for (CfGrowthtoolKpiLotDO kpiLotDO : modelListFromDB) {
            long procedureId = kpiLotDO.getProcedureId();
            ProcedureForCModel procedureForCModel = procedureForCModelMap.computeIfAbsent(procedureId, k -> Optional.ofNullable(pepClient.getByProduceId(procedureId)).map(Response::getData).orElse(null));
            if (procedureForCModel == null) {
                log.info("获取程序信息失败:{}", procedureId);
                continue;
            }
            Date endTime = procedureForCModel.getEndTime();
            if (endTime != null && endTime.before(new Date())) {
                log.info("当前程序:{}周期已经失效不需要推送数据了:{}", procedureId, endTime);
                deleteById(kpiLotDO.getId());
            }
        }
    }

    @Override
    public boolean deleteById(long id) {
        return cfGrowthtoolKpiLotDao.deleteById(id) > 0;
    }
}
