package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.CrmTaskStatModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.TaskStatDetail;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.bdtask.CrmBdTaskParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.bdtask.CrmManagerBaseParam;

import java.util.List;

/**
 * 顾问任务信息(CrmBdTask)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-20 11:05:27
 */
public interface ICrmBdTaskService {

    CrmBdTaskDO queryById(long id);

    int insert(CrmBdTaskDO crmBdTask);

    int update(CrmBdTaskDO crmBdTask);

    CrmBdTaskDO getByTaskTypeAndCaseId(int taskType, long caseId);

    void updateWhenOverTime(long id);

    void updateWhenComplete(long id);

    List<CrmBdTaskDO> listByCaseIds(List<Integer> caseIds);

    List<TaskStatDetail> getMemberOrOrgModel(CrmManagerBaseParam crmManagerBaseParam);

    List<TaskStatDetail> groupByUniqueCode(CrmManagerBaseParam crmManagerBaseParam);

    List<CrmBdTaskDO> pageTaskList(CrmBdTaskParam crmBdTaskParam);

    int countTaskList(CrmBdTaskParam crmBdTaskParam);

    boolean isExistTask(String uniqueCode);

    int countUnFinishTaskByCaseId(String uniqueCode);

}
