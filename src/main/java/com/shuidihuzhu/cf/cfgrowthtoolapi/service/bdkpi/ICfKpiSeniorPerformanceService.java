package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiSeniorPerformanceDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;

import java.util.List;

/**
 * kpi-老员工人效数据(CfKpiSeniorPerformance)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-18 11:39:10
 */
public interface ICfKpiSeniorPerformanceService {

    CfKpiSeniorPerformanceDO queryById(long id);

    int insert(CfKpiSeniorPerformanceDO cfKpiSeniorPerformanceDO);

    int update(CfKpiSeniorPerformanceDO cfKpiSeniorPerformanceDO);

    List<CfKpiSeniorPerformanceDO> listByDayKey(String dayKey);

}
