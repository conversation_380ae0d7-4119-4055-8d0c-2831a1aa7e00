package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.MemberOrgRelationParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.OrgOptParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.memberBindOrg.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.organization.IOptOrgLimit;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-05-09 17:23
 **/
@Data
@Service
@Slf4j
public class OptLimitHandler {

    @Autowired
    List<IOptOrgLimit> optLimitList;

    @Autowired
    ICfVolunteerService cfVolunteerService;

    @Autowired
    ICrmOrganizationRelationService relationService;

    @Autowired
    ICrmSelfBuiltOrgReadService crmOrganizationService;

    @Resource(name = "selfBuiltOrgForSea")
    ICrmSelfBuiltOrgReadService orgReadDbService;

    @Autowired
    ICrmMemberInfoService crmMemberInfoService;
    /**
     * 判断是否能对组织进行操作
     *
     * @param orgOptParam
     * @return
     */
    public Response<Boolean> checkCanOptOrg(OrgOptParam orgOptParam) {
        if (CollectionUtils.isEmpty(optLimitList)) {
            return NewResponseUtil.makeSuccess(true);
        }
        Response<Boolean> canOpt = NewResponseUtil.makeSuccess(true);
        for (IOptOrgLimit optLimit : optLimitList) {
            canOpt = optLimit.checkCanOptOrg(orgOptParam);
            log.info("optLimit class:{},result:{}", optLimit.getClass().getSimpleName(), JSON.toJSONString(canOpt));
            if (canOpt == null || canOpt.notOk()) {
                return canOpt;
            }
        }
        return canOpt;
    }

    /**
     * 判断能否对组织成员操作
     *
     * @return
     */
    public Response<Boolean> checkCanOptMemberBind(MemberOrgRelationParam memberOrgRelationParam) {
        List<IOptMemberBindLimit> optMemberBindLimits = Lists.newArrayList(
                new AddMemberLimitImpl(this),
                new DeleteMemberLimitImpl(this)
        );
        Response<Boolean> canOpt = NewResponseUtil.makeSuccess(true);
        for (IOptMemberBindLimit optMemberBindLimit : optMemberBindLimits) {
            canOpt = optMemberBindLimit.checkCanOptMemberBind(memberOrgRelationParam);
            log.info("optLimit class:{},result:{}", optMemberBindLimit.getClass().getSimpleName(), JSON.toJSONString(canOpt));
            if (canOpt == null || canOpt.notOk()) {
                return canOpt;
            }
        }
        return canOpt;
    }


    public BdCrmOrgUserRelationDO buildRelationForAdd(MemberOrgRelationParam memberOrgRelationParam) {
        IOptMemberBindLimit memberRelationConvert = new AddMemberLimitImpl(this);
        return memberRelationConvert.getRelationDO(memberOrgRelationParam);
    }
}
