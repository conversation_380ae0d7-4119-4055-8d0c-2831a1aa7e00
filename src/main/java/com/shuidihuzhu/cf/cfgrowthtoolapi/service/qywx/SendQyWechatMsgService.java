package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.QyWechatHandlerModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.SendMsgWechatParam;
import com.shuidihuzhu.client.account.wecom.feign.AccessTokenClient;
import com.shuidihuzhu.client.account.wecom.model.Response;
import com.shuidihuzhu.client.account.wecom.model.WxAccessToken;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.qywechat.QywxSdkClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2023-03-03 15:52
 * <p>
 * 调用企业微信原生api发送付密消息
 **/
@Slf4j
@Service
public class SendQyWechatMsgService {

    @Autowired
    private QywxSdkClient qywxSdkClient;

    @Autowired
    private AccessTokenClient accessTokenClient;

    @Autowired
    private QyWechatHandler qyWechatHandler;


    public void sendMsgToQyWechat(List<CrowdfundingVolunteer> volunteerList, String content) {
        qyWechatHandler.handle(volunteerList, content, this::sendMsgToQyWechatUserIds);
    }


    public void sendMsgToQyWechatUserIds(QyWechatHandlerModel<String> qyWechatHandlerModel) {
        doSendMsgToQyWechat(qyWechatHandlerModel.getVolunteerCropTypeEnum(), Joiner.on("|").join(qyWechatHandlerModel.getUserIds()), qyWechatHandlerModel.getContent());
    }


    public void doSendMsgToQyWechat(CrowdfundingVolunteerEnum.VolunteerCropTypeEnum volunteerCropTypeEnum, String toUser, String content) {
        try {
            Response<WxAccessToken> wxAccessTokenResponse = accessTokenClient.getTokenSelfBuildApp(volunteerCropTypeEnum.getCorpId(), volunteerCropTypeEnum.getAppSecret());
            log.info("getTokenSelfBuildApp :{}", wxAccessTokenResponse);
            String accessToken = "";
            if (wxAccessTokenResponse.getData() != null) {
                accessToken = wxAccessTokenResponse.getData().getAccessToken();
            }
            if (StringUtils.isBlank(accessToken)) {
                return;
            }
            SendMsgWechatParam sendMsgWechatParam = new SendMsgWechatParam(toUser, "text", volunteerCropTypeEnum.getAgentId(), content);
            String sendMsgResult = qywxSdkClient.sendMsgShuidiChou(accessToken, JSON.toJSONString(sendMsgWechatParam));
            log.info(this.getClass().getSimpleName() + " sendMsgCommon param:{},result:{}", JSON.toJSONString(sendMsgWechatParam), sendMsgResult);
        } catch (Exception e) {
            log.error("发送企业微信消息失败", e);
        }
    }

}
