package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfWorkRecordVoiceModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.PreposeMaterialVoiceCountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WorkRecordVoiceParam;

import java.util.List;

/**
 * @author: yangliming
 * @create: 2019/12/25
 */
public interface ICfBdCrmWorkRecordVoiceService {

    int addWorkRecordVoice(WorkRecordVoiceParam param);

    List<CfWorkRecordVoiceModel> getWorkRecordVoiceByUserIdAndCaseId(String volunteerUniqueCode, Long preposeMaterialId);

    Long getVoiceDurationByUserIdAndCaseId(String volunteerUniqueCode, Long preposeMaterialId);

    List<Long> getVoicePreposeMaterialIdByPreposeMaterialIds(List<Long> preposeMaterialIds);

    List<PreposeMaterialVoiceCountModel> getPreposeMaterialVoiceCountModel(List<Long> preposeMaterialIds);

    List<CfWorkRecordVoiceModel> getWorkRecordVoiceByPreposeMaterial(long preposeMaterial);

}
