package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.google.common.collect.Ordering;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IQywxSdkDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum.VolunteerCropTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.channel.IDedicatedServiceFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfClewQyWxCorpExtModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.qywechat.WXBizMsgCrypt;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.qywechat.XMLParse;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewQyWxCorpDO;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @author: fengxuan
 * @create 2023-07-05 17:46
 **/
@Slf4j
@Service
public class QywxCallbackService {

    @Autowired
    private IQywxSdkDelegate qywxSdkDelegate;


    @Autowired
    private ApolloService apolloService;

    //只需要关注通讯录和客户联系应用
    public static Map<String, Integer> appTypeMap = Map.of("change_contact", 1,
            "change_external_chat", 3,
            "change_external_contact", 3);

    //水滴筹病友之家需要额外处理下
    private static final String SPECIAL_CORP_ID = "ww7e123ba6d95a5ffc";

    /**
     * 水滴筹病友之家,水滴筹,水滴筹爱心平台,水滴筹大健康,水滴筹服务,水滴筹大病救助,水滴康复
     * 水滴筹需要特殊关注下
     */
    private static final Set<String> SQ_CORP_IDS = Sets.newHashSet("ww7e123ba6d95a5ffc",
            "wwb8cc2f5c0fc58917",
            "wwf67888370c3563f8",
            "wwb72bdb1577c65797",
            "ww54aed0dc0e0d80ad",
            "ww70c287e38e3cd1b8",
            "ww2860595d3bb712e9");

    @Autowired
    private IMqProducerService mqProducerService;


    @Autowired
    private IDedicatedServiceFacade dedicatedServiceFacade;

    @Autowired
    private AutoCreateContactCodeService autoCreateContactCodeService;

    @Autowired
    private ApplicationService applicationService;


    public String handleEventByInterface(WXBizMsgCrypt wxcpt, String msgSig, String timestamp, String nonce, String reqData, CfClewQyWxCorpDO clewQyWxCorpDO) {
        if (apolloService.isQywxCallbackSwitch()) {
            return "";
        }
        try {
            String sMsg = wxcpt.DecryptMsg(msgSig, timestamp, nonce, reqData);
            log.info("after decrypt msg: " + sMsg);
            QyWechatEventCommonModel qyWechatEventCommonModel = XMLParse.xml2Obj(sMsg);
            log.info("interface handle qyWechatEventCommonModel:{}, cfClewQyWxCorpDO:{}", qyWechatEventCommonModel, clewQyWxCorpDO);
            handleEvent(qyWechatEventCommonModel, clewQyWxCorpDO);
            return XMLParse.generate(reqData, msgSig, timestamp, nonce);
        } catch (Exception e) {
            // 解密失败，失败原因请查看异常
            log.warn("handleEventByInterface err:", e);
        }
        return "";
    }


    /**
     * 通过消息通知处理回调事件
     */
    public void handleEventByConsumer(MessageParser messageParser) {

        //找到对应企业微信配置
        QyWechatEventCommonModel qyWechatEventCommonModel = messageParser.resolveMsgModel(messageParser.getQyWechatEventEnum().getAClass());
        if (qyWechatEventCommonModel == null) {
            log.warn("handleEventByConsumer qyWechatEventCommonModel is null");
            return;
        }

        //cropId
        String cropId = qyWechatEventCommonModel.getToUserName();
        QyWechatEventEnum qyWechatEventEnum = messageParser.getQyWechatEventEnum();
        if (qyWechatEventEnum == null) {
            log.info("handleEventByConsumer qyWechatEventEnum is null");
            return;
        }
        log.debug("handleEventByConsumer cropId:{}, qyWechatEventEnum:{}", cropId, qyWechatEventEnum);
        //如果是筹款服务的需要特殊处理,且暂时只处理change_external_contact事件
        if (qyWechatEventEnum == QyWechatEventEnum.ADD_EXTERNAL_CONTACT_MODEL && Objects.equals(cropId, VolunteerCropTypeEnum.shuidi_chou_service.getCorpId())) {
            //拉群处理
            autoCreateContactCodeService.addContactCallback(qyWechatEventCommonModel);
            return;
        }
        Integer appType = appTypeMap.get(qyWechatEventEnum.getEvent());
        if (appType == null) {
            log.info("event事件忽略");
            return;
        }
        //水滴筹病友之家只有通讯录
        if (Objects.equals(SPECIAL_CORP_ID, cropId)) {
            appType = 1;
        }

        Integer finalAppType = appType;
        CfClewQyWxCorpDO cfClewQyWxCorpDO = qywxSdkDelegate.listByCorpId(cropId)
                .stream()
                .filter(item -> Objects.equals(finalAppType, item.getAppType()))
                .min(Ordering.natural().onResultOf(CfClewQyWxCorpDO::getAppType))
                .orElse(null);
        if (cfClewQyWxCorpDO == null) {
            log.info("handleEventByConsumer找不到对应的主体应用,appType:{},cropId:{}", appType, cropId);
            return;
        }
        log.info("consumer handle qyWechatEventCommonModel:{}, cfClewQyWxCorpDO:{}", qyWechatEventCommonModel, cfClewQyWxCorpDO);
        if (apolloService.isQywxCallbackSwitch()) {
            handleEvent(qyWechatEventCommonModel, cfClewQyWxCorpDO);
        }
    }

    private void handleEvent(QyWechatEventCommonModel qyWechatEventCommonModel, CfClewQyWxCorpDO cfClewWxCorpMsgDO) {
        try {
            Class aClass = QyWechatEventEnum.parseByType(qyWechatEventCommonModel.getEvent(), qyWechatEventCommonModel.getChangeType());
            if (aClass == null) {
                log.warn(this.getClass().getSimpleName() + " event:{}   changeType:{} 还未支持", qyWechatEventCommonModel.getEvent(), qyWechatEventCommonModel.getChangeType());
                return;
            }
            if (CreateExternalChatModel.class.equals(aClass)) {
                // 客户群创建事件
                if (SQ_CORP_IDS.contains(qyWechatEventCommonModel.getToUserName()) && dedicatedServiceFacade.createExternalChat((CreateExternalChatModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO).isFail()) {
                    mqProducerService.sendGrowthtoolGetDataFromQyWechat(CfClewQyWxCorpExtModel.buildCreateExternalChat((CreateExternalChatModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO), DelayLevel.M20);
                }
            } else if (UpdateExternalChatModel.class.equals(aClass)) {
                // 客户群变更事件
                if (SQ_CORP_IDS.contains(qyWechatEventCommonModel.getToUserName()) && dedicatedServiceFacade.updateExternalChat((UpdateExternalChatModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO).isFail()) {
                    mqProducerService.sendGrowthtoolGetDataFromQyWechat(CfClewQyWxCorpExtModel.buildUpdateExternalChat((UpdateExternalChatModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO), DelayLevel.M20);
                }
            } else if (DismissExternalChatModel.class.equals(aClass)) {
                // 客户群解散事件
                if (SQ_CORP_IDS.contains(qyWechatEventCommonModel.getToUserName()) && dedicatedServiceFacade.dismissExternalChat((DismissExternalChatModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO).isFail()) {
                    mqProducerService.sendGrowthtoolGetDataFromQyWechat(CfClewQyWxCorpExtModel.buildDismissExternalChat((DismissExternalChatModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO), DelayLevel.M20);
                }
            } else if (CreateUserModel.class.equals(aClass)) {
                // 新增成员事件
                if (dedicatedServiceFacade.handleContact((CreateUserModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO).isFail()) {
                    mqProducerService.sendGrowthtoolGetDataFromQyWechat(CfClewQyWxCorpExtModel.buildCreateUser((CreateUserModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO), DelayLevel.M20);
                }
            } else if (UpdateUserModel.class.equals(aClass)) {
                // TODO 更新成员事件
                if (dedicatedServiceFacade.handleContact((UpdateUserModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO).isFail()) {
                    mqProducerService.sendGrowthtoolGetDataFromQyWechat(CfClewQyWxCorpExtModel.buildUpdateUser((UpdateUserModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO), DelayLevel.M20);
                }
            } else if (DeleteUserModel.class.equals(aClass)) {
                // TODO 删除成员事件
                if (dedicatedServiceFacade.handleContact((DeleteUserModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO).isFail()) {
                    mqProducerService.sendGrowthtoolGetDataFromQyWechat(CfClewQyWxCorpExtModel.buildDeleteUser((DeleteUserModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO), DelayLevel.M20);
                }
            } else if (AddExternalContactModel.class.equals(aClass)) {
                // 添加外部联系人事件
                if (dedicatedServiceFacade.handleExternalContact((AddExternalContactModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO).isFail()) {
                    mqProducerService.sendGrowthtoolGetDataFromQyWechat(CfClewQyWxCorpExtModel.buildAddExternal((AddExternalContactModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO), DelayLevel.M20);
                }
            } else if (EditExternalContactModel.class.equals(aClass)) {
                // 编辑外部联系人事件
                if (dedicatedServiceFacade.handleExternalContact((EditExternalContactModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO).isFail()) {
                    mqProducerService.sendGrowthtoolGetDataFromQyWechat(CfClewQyWxCorpExtModel.buildEditExternal((EditExternalContactModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO), DelayLevel.M20);
                }
            } else if (DelExternalContactModel.class.equals(aClass)) {
                // 删除外部联系人事件
                DelExternalContactModel delExternalContactModel = (DelExternalContactModel) qyWechatEventCommonModel;
                if (dedicatedServiceFacade.delExternalContact(delExternalContactModel.getUserID(), delExternalContactModel.getExternalUserID(), cfClewWxCorpMsgDO, 2).isFail()) {
                    mqProducerService.sendGrowthtoolGetDataFromQyWechat(CfClewQyWxCorpExtModel.buildDelExternalContact((DelExternalContactModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO), DelayLevel.M20);
                }
            } else if (DelFollowUserModel.class.equals(aClass)) {
                // 删除跟进成员事件
                DelFollowUserModel delFollowUserModel = (DelFollowUserModel) qyWechatEventCommonModel;
                if (dedicatedServiceFacade.delExternalContact(delFollowUserModel.getUserID(), delFollowUserModel.getExternalUserID(), cfClewWxCorpMsgDO, 1).isFail()) {
                    mqProducerService.sendGrowthtoolGetDataFromQyWechat(CfClewQyWxCorpExtModel.buildDelFollowUserModel((DelFollowUserModel) qyWechatEventCommonModel, cfClewWxCorpMsgDO), DelayLevel.M20);
                }
            }
        } catch (Exception e) {
            // 解密失败，失败原因请查看异常
            log.warn(this.getClass().getSimpleName() + " decryptMsg err:", e);
        }
    }

}
