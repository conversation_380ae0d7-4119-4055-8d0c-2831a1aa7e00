package com.shuidihuzhu.cf.cfgrowthtoolapi.service;


import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfOfflineBreakWaveTaskDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

/**
 * 离线断波任务服务接口
 */
public interface ICfOfflineBreakWaveTaskService {

    /**
     * 插入离线断波任务
     *
     * @param record 任务记录
     * @return 影响行数
     */
    int insertTask(CfOfflineBreakWaveTaskDO record, String infoUuid);

    /**
     * 根据案例ID和日期键查询任务
     *
     * @param caseId 案例ID
     * @param dayKey 日期键
     * @return 任务列表
     */
    List<CfOfflineBreakWaveTaskDO> queryTaskByCaseIdAndDayKey(Integer caseId, String dayKey);

    /**
     * 批量更新任务状态
     *
     * @param idList     任务ID列表
     * @param taskStatus 任务状态
     * @param reason     更新状态原因
     * @return 更新成功数量
     */
    int batchUpdateTaskStatus(List<Long> idList, Integer taskStatus, String infoUuid, String reason);

    int updateTaskStatus(Integer caseId, Integer taskStatus, String infoUuid, Integer taskType);


    Integer getTaskCaseCount(String uniqueCode, Integer taskStatus);

    List<CfOfflineBreakWaveTaskDO> selectByStatusAndCaseIds(Integer taskStatus, List<Integer> caseIds);


    Boolean hasNoNeedCompleteTask(Integer caseId);

    int endTask(Integer caseId, Integer taskStatus);
    
    /**
     * 获取指定状态、类型和日期的任务最大ID
     *
     * @param taskStatus 任务状态
     * @param taskTypes 任务类型列表
     * @param dayKey 日期键
     * @return 最大ID
     */
    Long getMaxIdByStatusAndTypesAndDayKey(Integer taskStatus, List<Integer> taskTypes, String dayKey);
    
    /**
     * 获取指定状态、类型和日期的任务最小ID
     *
     * @param taskStatus 任务状态
     * @param taskTypes 任务类型列表
     * @param dayKey 日期键
     * @return 最小ID
     */
    Long getMinIdByStatusAndTypesAndDayKey(Integer taskStatus, List<Integer> taskTypes, String dayKey);
    
    /**
     * 根据ID范围查询指定状态、类型和日期的任务
     *
     * @param taskStatus 任务状态
     * @param taskTypes 任务类型列表
     * @param dayKey 日期键
     * @param minId 最小ID
     * @param maxId 最大ID
     * @param limit 限制条数
     * @return 任务列表
     */
    List<CfOfflineBreakWaveTaskDO> selectByStatusAndTypesAndDayKeyAndIdRange(
            Integer taskStatus, 
            List<Integer> taskTypes,
            String dayKey,
            Long minId,
            Long maxId,
            Integer limit);
}