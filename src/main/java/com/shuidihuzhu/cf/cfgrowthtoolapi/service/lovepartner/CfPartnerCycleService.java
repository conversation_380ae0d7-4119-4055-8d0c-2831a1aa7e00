package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerCycleDo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */
public interface CfPartnerCycleService {

    /**
     * 插入周期
     * @param partnerCycleDo
     * @return
     */
    int insertPartnerCycle(CfPartnerCycleDo partnerCycleDo);

    CfPartnerCycleDo getPartnerCycleByQueryTime(Date queryTime);

    CfPartnerCycleDo getByCycleId(int cycleId);

    List<CfPartnerCycleDo> getCycleList();

}
