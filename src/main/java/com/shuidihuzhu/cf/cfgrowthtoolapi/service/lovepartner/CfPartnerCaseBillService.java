package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerCaseBillDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerCycleDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerSnapshotDo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */
public interface CfPartnerCaseBillService {


    /**
     * 批量插入数据
     * @param bdCaseInfoList
     * @param partnerSnapshotDo
     * @param cfPartnerCycleDo
     * @return
     */
    int batchInsert(List<CfBdCaseInfoDo> bdCaseInfoList, CfPartnerSnapshotDo partnerSnapshotDo, CfPartnerCycleDo cfPartnerCycleDo);

    /**
     * 根据cycleId查询数据
     * @param cycleId
     * @return
     */
    List<CfPartnerCaseBillDo> listByCycle(int cycleId);

    int deleteByCycleId(long cycleId);


}
