package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrDepartmentDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/25 14:32
 */
public interface GrDepartmentService {

    int batchInsert(List<GrDepartmentDO> grDepartmentDOS);

    int update(GrDepartmentDO grDepartmentDO);

    List<GrDepartmentDO> getGrDepartmentList(List<Integer> customerId);

    int deleteDepartment(long id);
}
