package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPartnerDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiPartnerDataService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiPartnerDataDao;
import com.shuidihuzhu.cf.enhancer.subject.query.EhCursorQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合作团队kpi基础数据(CfKpiPartnerData)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-22 20:18:57
 */
@Service("cfKpiPartnerDataService")
public class CfKpiPartnerDataServiceImpl implements CfKpiPartnerDataService {
   
    @Resource
    private CfKpiPartnerDataDao cfKpiPartnerDataDao;

    @Override
    public CfKpiPartnerDataDO queryById(long id) {
        return cfKpiPartnerDataDao.queryById(id);
    }
    

    @Override
    public int insert(CfKpiPartnerDataDO cfKpiPartnerData) {
        //查看数据是否存在
        CfKpiPartnerDataDO partnerDataDOInDB = cfKpiPartnerDataDao.getByDateKeyAndUniqueKey(cfKpiPartnerData.getDayKey(),
                cfKpiPartnerData.getUniqueKey(), cfKpiPartnerData.getPartnerDataType());
        if (partnerDataDOInDB != null) {
            //存在直接覆盖
            cfKpiPartnerData.setId(partnerDataDOInDB.getId());
            return cfKpiPartnerDataDao.update(cfKpiPartnerData);
        }
        return cfKpiPartnerDataDao.insert(cfKpiPartnerData);
    }

    @Override
    public int update(CfKpiPartnerDataDO cfKpiPartnerData) {
        return cfKpiPartnerDataDao.update(cfKpiPartnerData);
    }

    @Override
    public boolean deleteById(long id) {
        return cfKpiPartnerDataDao.deleteById(id) > 0;
    }

    @Override
    public List<CfKpiPartnerDataDO> listByDateKeyAndPartnerType(String dateKey, int partnerType) {
        return EhCursorQuery.queryByCursor((id, limit) -> cfKpiPartnerDataDao.listByDateKeyAndPartnerType(dateKey, partnerType, (Long) id, limit),
                CfKpiPartnerDataDO::getId);
    }


}
