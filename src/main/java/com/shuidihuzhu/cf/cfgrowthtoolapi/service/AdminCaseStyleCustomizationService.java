package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OperateLogSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OperatorLogVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.CommonPermissionConfigParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.CommonPermissionConfigVo;
import com.shuidihuzhu.common.web.model.Response;

/**
 * <AUTHOR>
 * @date 2024/9/3  15:56
 */
public interface AdminCaseStyleCustomizationService {

    Response<Void> saveOrUpdate(CommonPermissionConfigParam commonPermissionConfigParam, long authSaasUserId);

    Response<CommonPermissionConfigVo> get(int configType);

    Response<CommonResultModel<OperatorLogVO>> getOperateLog(OperateLogSearchModel searchModel);

}
