package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.campaignv2.CampaignProCityModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * @author: wanghui
 * @create: 2021/8/6 下午4:46
 */
public interface ICrowdfundingCityService {

    Map<String, Integer> getCityNameMapCityId();

    List<CrowdfundingCity> getCityByParentIds(Collection<Integer> parentIds);

    List<CampaignProCityModel> getCampaignCity(List<Integer> cityIds);

    List<CrowdfundingCity> getCityByCityIds(List<Integer> cityIds);

    List<CrowdfundingCity> getByParentId(Integer parentId);

    CrowdfundingCity getById(Integer id);

    List<CrowdfundingCity> getProvinceByName(String province);

    List<CrowdfundingCity> getByProvinceAndCityName(String city, Integer parentId);

    List<CrowdfundingCity> getByProvinceAndCityNameByLike(String city, Integer parentId);

    List<CrowdfundingCity> getByCityNames(List<String> queryCities);

    /**
     * 根据城市名称和城市等级查询城市列表
     *
     * @param cityName 城市/省份名称
     * @param levels   城市等级
     * @param limit    查询数量
     * @return 城市列表
     */
    List<CrowdfundingCity> listByCityNameAndLevel(String cityName, List<Integer> levels, Integer limit);

}
