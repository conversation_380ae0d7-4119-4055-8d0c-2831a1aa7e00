package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponBudgetGroupDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.ICfWeaponBudgetGroupService;
import com.shuidihuzhu.cf.dao.weapon.CfWeaponBudgetGroupDao;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-10-16 10:49 上午
 **/

@Slf4j
@Service
public class CfWeaponBudgetGroupServiceImpl implements ICfWeaponBudgetGroupService {

    @Autowired
    private CfWeaponBudgetGroupDao budgetGroupDao;

    @Override
    public List<CfWeaponBudgetGroupDO> listByWeaponId(int weaponId) {
        return budgetGroupDao.listByWeaponId(weaponId);
    }

    /**
     * 查询未作废的预算
     */
    @Override
    public List<CfWeaponBudgetGroupDO> listInUseBudgetGroup(int weaponId) {
        return budgetGroupDao.listByWeaponIdAndStatus(weaponId, WeaponEnums.BudgetValidStatusEnum.valid.getCode());
    }

    @Override
    public List<CfWeaponBudgetGroupDO> listInUseBudgetGroup(List<Integer> weaponIds) {
        return budgetGroupDao.listByWeaponIdsAndStatus(weaponIds, WeaponEnums.BudgetValidStatusEnum.valid.getCode());
    }

    @Override
    public CfWeaponBudgetGroupDO getById(int budgetGroupId) {
        return budgetGroupDao.getById(budgetGroupId);
    }

    @Override
    public int insert(CfWeaponBudgetGroupDO budgetGroupDO) {
        if (budgetGroupDO == null) {
            return 0;
        }
        return budgetGroupDao.insert(budgetGroupDO);
    }

    @Override
    public int invalidBudgetGroup(int budgetGroupId) {
        return budgetGroupDao.invalidBudgetGroup(budgetGroupId);
    }

    @Override
    public int editBudgetGroup(CfWeaponBudgetGroupDO budgetGroupDO) {
        return budgetGroupDao.editBudgetGroup(budgetGroupDO);
    }

    @Override
    public List<CfWeaponBudgetGroupDO> listGroupByWeaponSortByEndTime(List<Integer> allWeaponIdList) {
        if (CollectionUtils.isEmpty(allWeaponIdList)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(budgetGroupDao.listByWeaponIdsOrderByEndTime(allWeaponIdList, DateUtil.nowDate())
                .stream()
                .collect(Collectors.toMap(CfWeaponBudgetGroupDO::getWeaponId, Function.identity(), (before, after) -> before))
                .values());
    }

    @Override
    public List<CfWeaponBudgetGroupDO> listByIds(Set<Integer> idSet) {
        if (CollectionUtils.isEmpty(idSet)) {
            return Lists.newArrayList();
        }
        return budgetGroupDao.listByIds(Lists.newArrayList(idSet));
    }

    @Override
    public List<CfWeaponBudgetGroupDO> listInUsingBudgetGroup(int weaponId) {
        return budgetGroupDao.listInUsingBudgetGroup(weaponId, new Date());
    }

    @Override
    public int modifyGroupResource(int id, int addUseResource, int addApplyingResource, int addUseMoney, int addApplyingMoney) {
        return budgetGroupDao.modifyGroupResource(id, addUseResource, addApplyingResource, addUseMoney, addApplyingMoney);
    }

    @Override
    public int addApplyingResource(int id, int addApplyingResource) {
        if (id <= 0) {
            return 0;
        }
        return budgetGroupDao.addApplyingResource(id, addApplyingResource);
    }

    @Override
    public int addApplyingMoney(int id, int addApplyingMoney) {
        return budgetGroupDao.addApplyingMoney(id, addApplyingMoney);
    }

    @Override
    public int minusUsedMoney(int id, int unUsedMoney) {
        return budgetGroupDao.minusUsedMoney(id, unUsedMoney);
    }


    @Override
    public int repairResource(int id, int usedResource, int applyingResource) {
        return budgetGroupDao.repairResource(id, usedResource, applyingResource);
    }

    @Override
    public int getApplyingResource(int weaponId, Date time) {
        return Optional.ofNullable(budgetGroupDao.getApplyingResource(weaponId, time)).orElse(0);
    }

    @Override
    public int updateAmountByWeaponId(int weaponId, long amount) {
        return budgetGroupDao.updateAmountByWeaponId(weaponId, amount);
    }
}
