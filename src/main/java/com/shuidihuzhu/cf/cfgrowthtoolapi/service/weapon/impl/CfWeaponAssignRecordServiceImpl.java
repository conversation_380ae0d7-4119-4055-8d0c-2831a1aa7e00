package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponAssignRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.ICfWeaponAssignRecordService;
import com.shuidihuzhu.cf.dao.weapon.CfWeaponAssignRecordDao;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-10-16 12:05 下午
 **/
@Slf4j
@Service
public class CfWeaponAssignRecordServiceImpl implements ICfWeaponAssignRecordService {

    @Autowired
    private CfWeaponAssignRecordDao assignRecordDao;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public void batchAdd(List<CfWeaponAssignRecordDO> weaponAssignRecordDOs) {
        //通过uniqueCode + budgetId能找到就不插入
        Map<Integer, List<CfWeaponAssignRecordDO>> budgetIdTRecordMap = weaponAssignRecordDOs.stream().collect(Collectors.groupingBy(CfWeaponAssignRecordDO::getBudgetId));
        for (Integer budgetId : budgetIdTRecordMap.keySet()) {
            String lockName = "weapon_assign_lock" + budgetId;
            RLock lock = null;
            try {
                lock = redissonHandler.getLock(lockName);
                if (!lock.tryLock()) {
                    log.info("doPassByActivity get lock fail,key:{}", lockName);
                    return;
                }
                log.info("添加分配表,对应的子预算id:{}", budgetId);
                //过滤掉既没有分配权限又不能没查看权限的
                List<CfWeaponAssignRecordDO> hasInsertAssignRecordList = assignRecordDao
                        .listByBudgetIdAndUniqueCodes(budgetId, budgetIdTRecordMap.get(budgetId).stream().map(CfWeaponAssignRecordDO::getUniqueCode).collect(Collectors.toList()))
                        .stream()
                        .filter(item -> WeaponEnums.hasApplyAuthority().contains(item.getAssignStatus()))
                        .collect(Collectors.toList());
                //去除调weaponAssignRecordDOs中重复uniqueCode
                Collection<CfWeaponAssignRecordDO> removeRepeatUniqueCode = budgetIdTRecordMap.get(budgetId)
                        .stream()
                        .filter(item -> hasInsertAssignRecordList.stream().noneMatch(hadAssignRecord -> Objects.equals(hadAssignRecord.getOrgId(), item.getOrgId()) && Objects.equals(hadAssignRecord.getUniqueCode(), item.getUniqueCode())))
                        .collect(Collectors.toMap(item -> {
                            //兼容下身兼多职的情况
                            return item.getUniqueCode() + item.getBudgetId() + "-" + item.getOrgId();
                        }, Function.identity(), (before, after) -> before)).values();
                List<CfWeaponAssignRecordDO> needInsertData = Lists.newArrayList(removeRepeatUniqueCode);
                List<List<CfWeaponAssignRecordDO>> partition = Lists.partition(needInsertData, 100);
                partition.forEach(item -> {
                    if (CollectionUtils.isNotEmpty(item)) {
                        assignRecordDao.batchAdd(item);
                    }
                });
            } finally {
                //isLocked():查询lock 是否被任意线程所持有。
                //isHeldByCurrentThread():查询当前线程是否保持此锁定
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    @Override
    public List<CfWeaponAssignRecordDO> listCanAssignByBudgetId(int budgetId) {
        return assignRecordDao.listCanAssignByBudgetId(budgetId);
    }

    @Override
    public List<CfWeaponAssignRecordDO> listCanAssignByBudgetIds(List<Integer> budgetIds) {
        return assignRecordDao.listCanAssignByBudgetIds(budgetIds);
    }

    @Override
    public int updateAssignStatus(List<Integer> budgetIds, WeaponEnums.BudgetAssignStatusEnum assignStatusEnum) {
        if (CollectionUtils.isEmpty(budgetIds)) {
            log.info("updateAssignStatus budgetIds为空");
            return 0;
        }
        return assignRecordDao.updateAssignStatus(budgetIds, assignStatusEnum.getCode());
    }

    @Override
    public List<CfWeaponAssignRecordDO> listByUniqueCode(String uniqueCode) {
        return assignRecordDao.listByUniqueCode(uniqueCode);
    }

    @Override
    public CfWeaponAssignRecordDO getByUniqueCodeAndBudgetId(String uniqueCode, int budgetId) {
        return assignRecordDao.getByUniqueCodeAndBudgetId(uniqueCode, budgetId);
    }

    @Override
    public int insert4ViewWeapon(CfWeaponAssignRecordDO assignRecord) {
        if (assignRecord == null) {
            return 0;
        }
        return assignRecordDao.insert4ViewWeapon(assignRecord);
    }

    @Override
    public List<CfWeaponAssignRecordDO> listCanAssignByBudgetIdsAndUniqueCode(List<Integer> budgetIds, String uniqueCode) {
        if (CollectionUtils.isEmpty(budgetIds)) {
            return Lists.newArrayList();
        }
        return assignRecordDao.listCanAssignByBudgetIdsAndUniqueCode(budgetIds, uniqueCode);
    }

    @Override
    public Map<Integer, Long> mapByWeaponIdAndBudgetsAndAssignSource(int weaponId, List<Integer> budgetIds, WeaponEnums.BudgetAssignStatusEnum assignStatusEnum, int assignSource) {
        Map<Integer, Long> map = Maps.newHashMap();
        List<Map<String, Object>> list = assignRecordDao.getCountMapByWeaponIdAndBudgetsAndAssignSource(weaponId, budgetIds, assignSource, assignStatusEnum.getCode());
        if (CollectionUtils.isNotEmpty(list)) {
            for (Map<String, Object> map1 : list) {
                Integer key = null;
                Long value = null;
                for (Map.Entry<String, Object> entry : map1.entrySet()) {
                    if ("key".equals(entry.getKey())) {
                        key = (Integer) entry.getValue();
                    } else if ("value".equals(entry.getKey())) {
                        value = (Long) entry.getValue();
                    }
                }
                map.put(key, value);
            }
        }
        return map;
    }

    @Override
    public List<CfWeaponAssignRecordDO> listByBudgetIdAndAssignSource(int budgetId, int assignSource) {
        return assignRecordDao.listByBudgetIdAndAssignSource(budgetId, assignSource);
    }

    @Override
    public List<CfWeaponAssignRecordDO> listByBudgetIdsAndAssignSource(List<Integer> budgetIds, int assignSource) {
        return assignRecordDao.listByBudgetIdsAndAssignSource(budgetIds, assignSource);
    }

    @Override
    public void batchCancelAssign(List<Integer> delIdList, WeaponEnums.BudgetAssignStatusEnum assignStatusEnum, String assignMis) {
        List<List<Integer>> partition = Lists.partition(delIdList, 100);
        partition.forEach(item -> assignRecordDao.batchCancelAssign(item, assignStatusEnum.getCode(), assignMis));
    }

    @Override
    public List<CfWeaponAssignRecordDO> listByUniqueCodeAndWeaponId(String uniqueCode, int weaponId) {
        return assignRecordDao.listByUniqueCodeAndWeaponId(uniqueCode, weaponId);
    }

    @Override
    public int batchUpdateViewWeaponStatus(List<Integer> ids, boolean viewWeaponStatus) {
        return assignRecordDao.bactchUpdateViewWeaponStatus(ids, viewWeaponStatus ? 1 : 0);
    }

    @Override
    public int batchAddOrUpdate(List<CfWeaponAssignRecordDO> weaponAssignList, List<CfWeaponAssignRecordDO> weaponListFromDb, String assignMis) {
        List<CfWeaponAssignRecordDO> needAddList = Lists.newArrayList();
        List<CfWeaponAssignRecordDO> updateList = Lists.newArrayList();
        //通过uniqueCode能找到就更新，找不到就插入
        Map<String, CfWeaponAssignRecordDO> uniqueCodeRecordMap = weaponListFromDb.stream().collect(Collectors.toMap(CfWeaponAssignRecordDO::getUniqueCode, Function.identity()));
        for (CfWeaponAssignRecordDO cfWeaponAssignRecordDO : weaponAssignList) {
            if (Objects.isNull(uniqueCodeRecordMap.get(cfWeaponAssignRecordDO.getUniqueCode()))) {
                //不存在
                needAddList.add(cfWeaponAssignRecordDO);
            } else {
                updateList.add(uniqueCodeRecordMap.get(cfWeaponAssignRecordDO.getUniqueCode()));
            }
        }
        if (CollectionUtils.isNotEmpty(needAddList)) {
            this.batchAdd(needAddList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<CfWeaponAssignRecordDO>> partition = Lists.partition(updateList, 100);
            partition.forEach(item -> {
                List<Integer> updateIds = item.stream().map(CfWeaponAssignRecordDO::getId).collect(Collectors.toList());
                assignRecordDao.batchAssignByUniqueCodeAndBudgetId(updateIds, assignMis, WeaponEnums.BudgetAssignStatusEnum.can_assign.getCode(), 1);
            });
        }
        return 0;
    }

    @Override
    public int updateOrgId(int id, int orgId) {
        return assignRecordDao.updateOrgId(id, orgId);
    }

    @Override
    public CfWeaponAssignRecordDO findById(int id) {
        return assignRecordDao.findById(id);
    }

    @Override
    public List<CfWeaponAssignRecordDO> listAssignByBudgetGroupId(int budgetGroupId) {
        return assignRecordDao.listAssignByBudgetGroupId(budgetGroupId);
    }

    @Override
    public int updateAssignStatusByUniqueCode(String uniqueCode, WeaponEnums.BudgetAssignStatusEnum assignStatusEnum) {
        return assignRecordDao.updateAssignStatusByUniqueCode(uniqueCode, assignStatusEnum.getCode());
    }

    @Override
    public int updateAvailableAmount(int id, double availableAmount) {
        return assignRecordDao.updateAvailableAmount(id, availableAmount);
    }

    @Override
    public List<CfWeaponAssignRecordDO> listByBudgetIdAndAssignStatus(int budgetId, int assignStatus) {
        return assignRecordDao.listByBudgetIdAndAssignStatus(budgetId, assignStatus);
    }

}
