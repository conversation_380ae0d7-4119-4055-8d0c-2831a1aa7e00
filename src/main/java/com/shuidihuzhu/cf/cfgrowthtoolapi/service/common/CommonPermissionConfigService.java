package com.shuidihuzhu.cf.cfgrowthtoolapi.service.common;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.CommonPermissionConfigDo;

import java.util.List;
import java.util.Map;

public interface CommonPermissionConfigService {

    int insert(CommonPermissionConfigDo commonPermissionConfigDo);

    int update(CommonPermissionConfigDo commonPermissionConfigDo);

    CommonPermissionConfigDo getByConfigType(int configType);

    List<CommonPermissionConfigDo> listByConfigTypes(List<Integer> configTypes);

    Map<Integer, CommonPermissionConfigDo> mapByConfigTypes(List<Integer> configTypes);

}
