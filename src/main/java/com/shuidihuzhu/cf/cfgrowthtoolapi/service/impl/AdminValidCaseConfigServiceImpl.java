package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.CfGrowthtoolMQ;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationPathDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ValidCaseConfigStatusEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.ValidCaseConfigEffectiveModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdCfCommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.CfPartnerInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.CommonMessageHelperService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.MessageBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.AdminValidCaseConfigParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.AdminValidCaseConfigRecordParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.AdminValidCaseConfigRecordService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.AdminValidCaseConfigService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CacheAdminValidCaseConfigService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.IBdCrmOrganizationPathService;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.feign.cipher.constants.DesensitizeEnum;
import com.shuidihuzhu.cf.dao.AdminValidCaseConfigDao;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/8  14:44
 */
@Service
public class AdminValidCaseConfigServiceImpl implements AdminValidCaseConfigService {


    @Autowired
    private AdminValidCaseConfigDao adminValidCaseConfigDao;

    @Autowired
    private CommonMessageHelperService commonMessageHelperService;

    @Autowired
    private IBdCrmOrganizationPathService bdCrmOrganizationPathService;

    @Autowired
    private AdminValidCaseConfigRecordService adminValidCaseConfigRecordService;

    @Autowired
    private CacheAdminValidCaseConfigService cacheAdminValidCaseConfigService;
    @Override
    public Response<Integer> saveOrUpdate(AdminValidCaseConfigParam param) {
        //新增
        if (param.getId() == 0L) {
            return save(param);
        }
        //修改
        return update(param);
    }

    @Override
    public Response<Integer> expiration(AdminValidCaseConfigParam param) {
        if (param.getId() <= 0) {
            return NewResponseUtil.makeFail("配置不存在");
        }
        int res = adminValidCaseConfigDao.updateStatusById(param.getId(), ValidCaseConfigStatusEnum.EXPIRATION.getCode());
        if (res > 0) {
            //清除缓存中的数据
            cacheAdminValidCaseConfigService.delRedis();
            //操作记录
            AdminValidCaseConfigDO configDO = adminValidCaseConfigDao.getById(param.getId());
            param.setOrgId(configDO.getOrgId());
            saveRecord(param, 2, null);
        }
        return NewResponseUtil.makeSuccess(res);
    }

    @Override
    public Response<AdminValidCaseConfigPageModel<AdminValidCaseConfigVO>> getList(Integer orgId, Integer status, int pageNo, int pageSize) {
        AdminValidCaseConfigPageModel<AdminValidCaseConfigVO> pageResult = new AdminValidCaseConfigPageModel<AdminValidCaseConfigVO>();
        int count = adminValidCaseConfigDao.countList(orgId, status);
        List<AdminValidCaseConfigVO> list = Lists.newArrayList();

        if (count <= 0) {
            list = Lists.newArrayList();
        } else {
            int offset = (pageNo - 1) * pageSize;
            List<AdminValidCaseConfigDO> adminValidCaseConfigDOList = adminValidCaseConfigDao.getList(orgId, status, offset, pageSize);
            if (CollectionUtils.isEmpty(adminValidCaseConfigDOList)) {
                count = 0;
            } else {
                List<Long> orgIdList = adminValidCaseConfigDOList.stream().map(AdminValidCaseConfigDO::getOrgId).map(Integer::longValue).collect(Collectors.toList());
                List<BdCrmOrganizationPathDO> bdCrmOrganizationPathDOList = bdCrmOrganizationPathService.listByIds(orgIdList);
                Map<Long, String> bdCrmOrganizationPathDOMap = bdCrmOrganizationPathDOList.stream().collect(Collectors.toMap(BdCrmOrganizationPathDO::getId, BdCrmOrganizationPathDO::getOrgPath, (before, after) -> before));

                list = adminValidCaseConfigDOList.stream().map(v -> {
                    AdminValidCaseConfigVO adminValidCaseConfigVO = new AdminValidCaseConfigVO();
                    adminValidCaseConfigVO.setId(v.getId());
                    adminValidCaseConfigVO.setOrgId(v.getOrgId());
                    adminValidCaseConfigVO.setOrgPath(bdCrmOrganizationPathDOMap.get((long) v.getOrgId()));
                    adminValidCaseConfigVO.setStatusDesc(Optional.ofNullable(ValidCaseConfigStatusEnum.parse(v.getStatus())).map(ValidCaseConfigStatusEnum::getDesc).orElse(""));
                    adminValidCaseConfigVO.setEffectiveTime(DateUtil.formatDateTime(v.getEffectiveTime()));
                    adminValidCaseConfigVO.setValidAmount((v.getValidAmount() / 100) + "元");
                    adminValidCaseConfigVO.setValidDonateNum(v.getValidDonateNum() + "单");
                    return adminValidCaseConfigVO;
                }).collect(Collectors.toList());
            }
        }

        pageResult.setCount(count);
        pageResult.setList(list);
        return NewResponseUtil.makeSuccess(pageResult);
    }

    @Override
    public AdminValidCaseConfigDO getById(long id) {
        if (id <= 0) {
            return null;
        }
        return adminValidCaseConfigDao.getById(id);
    }

    @Override
    public List<AdminValidCaseConfigDO> getListByOrgIdAndStatus(int orgId, int status) {
        if (orgId <= 0) {
            return Lists.newArrayList();
        }
        return adminValidCaseConfigDao.getListByOrgIdAndStatus(orgId, status);
    }

    @Override
    public int updateStatusById(long id, int status) {
        if (id <= 0) {
            return 0;
        }
        return adminValidCaseConfigDao.updateStatusById(id, status);
    }


    private Response<Integer> save(AdminValidCaseConfigParam param) {
        //检查
        Response<Void> check = saveCheck(param);
        if (check.notOk()) {
            return NewResponseUtil.makeFail(check.getMsg());
        }
        //新增
        int res = adminValidCaseConfigDao.save(param);
        //通过mq发送生效消息
        if (res > 0) {
            //清除缓存中的数据
            cacheAdminValidCaseConfigService.delRedis();
            //发送生效消息
            sendEffectiveMsg(param.getId(), param.getEffectiveTime());
            //操作记录
            saveRecord(param, 0, null);
        }
        return NewResponseUtil.makeSuccess(res);
    }

    private Response<Integer> update(AdminValidCaseConfigParam param) {
        AdminValidCaseConfigDO configDO = adminValidCaseConfigDao.getById(param.getId());
        //检查
        Response<Void> check = updateCheck(configDO, param);
        if (check.notOk()) {
            return NewResponseUtil.makeFail(check.getMsg());
        }
        //修改
        int res = adminValidCaseConfigDao.update(param);
        if (res > 0) {
            //清除缓存中的数据
            cacheAdminValidCaseConfigService.delRedis();
            //发送生效消息
            if (configDO.getStatus() == ValidCaseConfigStatusEnum.VACATIO_LEGIS.getCode()
                    && param.getEffectiveTime().getTime() != configDO.getEffectiveTime().getTime()) {
                sendEffectiveMsg(param.getId(), param.getEffectiveTime());
            }
            //操作记录
            saveRecord(param, 1, configDO);
        }
        return NewResponseUtil.makeSuccess(res);
    }

    /**
     * 发送生效消息
     *
     * @param id
     * @param effectiveTime
     */
    private void sendEffectiveMsg(long id, Date effectiveTime) {
        ValidCaseConfigEffectiveModel validCaseConfigEffectiveModel = ValidCaseConfigEffectiveModel
                .builder()
                .id(id)
                .effectiveTime(effectiveTime)
                .build();
        Message<ValidCaseConfigEffectiveModel> message = MessageBuilder
                .createWithPayload(validCaseConfigEffectiveModel)
                .addKey()
                .setTags(CfGrowthtoolMQ.VALID_CASE_CONFIG_EFFECTIVE)
                .build();
        commonMessageHelperService.sendDelayByTargetTime(message, effectiveTime.getTime());
    }

    private Response<Void> saveCheck(AdminValidCaseConfigParam param) {
        Date now = new Date();
        long upperTime = DateUtil.addDay(now, 90).getTime();
        if (param.getEffectiveTime().getTime() > upperTime) {
            return NewResponseUtil.makeFail("生效时间不能超过当前时间90天");
        }
        if (param.getEffectiveTime().getTime() < now.getTime()) {
            return NewResponseUtil.makeFail("生效时间小于当前时间");
        }
        int res = adminValidCaseConfigDao.countByOrgId(param.getOrgId(), ValidCaseConfigStatusEnum.VACATIO_LEGIS.getCode());
        if (res > 0) {
            BdCrmOrganizationPathDO bdCrmOrganizationPathDO = bdCrmOrganizationPathService.queryById(param.getOrgId());
            return NewResponseUtil.makeFail(Optional.ofNullable(bdCrmOrganizationPathDO).map(BdCrmOrganizationPathDO::getOrgPath).orElse("") + "组织不能重复设置待生效规则");
        }
        return NewResponseUtil.makeSuccess();
    }

    private Response<Void> updateCheck(AdminValidCaseConfigDO configDO, AdminValidCaseConfigParam param) {
        if (configDO.getStatus() == ValidCaseConfigStatusEnum.VACATIO_LEGIS.getCode()
                && configDO.getEffectiveTime().getTime() != param.getEffectiveTime().getTime()) {
            Date now = new Date();
            long upperTime = DateUtil.addDay(now, 90).getTime();
            if (param.getEffectiveTime().getTime() > upperTime) {
                return NewResponseUtil.makeFail("生效时间不能超过当前时间90天");
            }
            if (param.getEffectiveTime().getTime() < now.getTime()) {
                return NewResponseUtil.makeFail("生效时间小于当前时间");
            }
        }
        return NewResponseUtil.makeSuccess();
    }

    /**
     * @param param
     * @param num   0-新增 1-修改 2-失效
     */
    private void saveRecord(AdminValidCaseConfigParam param, int num, AdminValidCaseConfigDO configDO) {
        BdCrmOrganizationPathDO bdCrmOrganizationPathDO = bdCrmOrganizationPathService.queryById(param.getOrgId());
        String orgPath = Optional.ofNullable(bdCrmOrganizationPathDO).map(BdCrmOrganizationPathDO::getOrgPath).orElse("");
        String operatorContent = StringUtils.EMPTY;
        if (num == 0) {
            //新增
            operatorContent = "新增组织：" + orgPath
                    + "，生效时间为"
                    + DateUtil.formatDateTime(param.getEffectiveTime())
                    + "，捐单为" + param.getValidDonateNum()
                    + "单，筹款金额为" + param.getValidAmount() / 100 + "元";
        } else if (num == 1) {
            //修改
            String effectiveTimeDesc = "";
            if (param.getEffectiveTime().getTime() != configDO.getEffectiveTime().getTime()) {
                effectiveTimeDesc = "生效时间从" + DateUtil.formatDateTime(configDO.getEffectiveTime()) + "修改为" + DateUtil.formatDateTime(param.getEffectiveTime());
            }
            String validAmountDesc = "";
            if (param.getValidAmount() != configDO.getValidAmount()) {
                validAmountDesc = " 筹款金额从" + configDO.getValidAmount() / 100 + "元修改为" + param.getValidAmount() / 100 + "元";
            }
            String validDonateNumDesc = "";
            if (param.getValidDonateNum() != configDO.getValidDonateNum()) {
                validDonateNumDesc = " 捐单从" + configDO.getValidDonateNum() + "单修改为" + param.getValidDonateNum() + "单";
            }
            operatorContent = orgPath + "的" + effectiveTimeDesc + validAmountDesc + validDonateNumDesc;
        } else if (num == 2) {
            //失效
            operatorContent = orgPath + "的有效案例规则操作弃用";
        }

        AdminValidCaseConfigRecordParam adminValidCaseConfigRecordParam = AdminValidCaseConfigRecordParam.builder()
                .configId(param.getId())
                .operatorUserId(param.getAuthSaasUserId())
                .operatorContent(operatorContent)
                .build();
        adminValidCaseConfigRecordService.save(adminValidCaseConfigRecordParam);
    }
}
