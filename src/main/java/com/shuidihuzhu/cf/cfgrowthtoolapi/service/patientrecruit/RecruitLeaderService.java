package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientrecruit;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiRecruitClewBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.patientrecruit.CfPatientRecruitClueInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiRecruitClewBaseDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2022-08-31 14:42
 **/
@Service
public class RecruitLeaderService {

    @Autowired
    private ICfPatientRecruitClueService recruitClueService;

    @Autowired
    private ICfKpiRecruitClewBaseDataService cfKpiRecruitClewBaseDataService;

    public void updateLeaderInfoWhenMonthEnd() {
        DateTime now = DateTime.now();
        String dayKey = now.minusDays(1).toString(GrowthtoolUtil.ymdfmt);
        String startTime = now.withTimeAtStartOfDay().withDayOfMonth(1).toString(GrowthtoolUtil.ymdfmt);

        List<CfKpiRecruitClewBaseDataDO> cfKpiRecruitClewBaseDataDOS = cfKpiRecruitClewBaseDataService.listRandomizedByDateKey(dayKey, startTime, now.toString(GrowthtoolUtil.ymdfmt));
        //找到本月随机成功的患者,更新对应的组织链路信息
        for (CfKpiRecruitClewBaseDataDO clewBaseDataDO : cfKpiRecruitClewBaseDataDOS) {
            //找到对应的招募信息
            CfPatientRecruitClueInfoDo clueInfoDo = recruitClueService.getByPatientId(clewBaseDataDO.getPatientId());
            if (clueInfoDo != null) {
                //随机成功
                String leaderInfo = recruitClueService.leaderInfo(clueInfoDo, 6, 2);
                if (StringUtils.isNotBlank(leaderInfo)) {
                    recruitClueService.updateLeaderInfo(clueInfoDo.getId(), leaderInfo);
                }
            }
        }
    }
}
