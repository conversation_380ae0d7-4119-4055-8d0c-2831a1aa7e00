package com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfOperatingRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.CfBdCaseDonateModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.CfBdCaseDonateTaskResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.donate.BdCaseDonateTaskSearchParam;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/27 5:45 PM
 */
public interface CfBdCaseDonateService {

    /*
    * 更新捐转使用状态
    */
    OpResult<String> updateCityDonateUseStatus(Integer useStatus, Long id, AdminUserAccountModel adminUserAccountModel);

    /*
    * 增加或更新捐转案例运营配置
    */
    OpResult<String> addOrUpdateCaseDonate(CfBdCaseDonateModel cfBdCaseDonateModel, AdminUserAccountModel adminUserAccountModel);

    /*
    * 条件查询案例运营配置查询
    */
    CommonPageModel<CfBdCaseDonateModel> getBdCaseDonateConfigInfo(String cityName, Integer useStatus, Integer pageNo, Integer pageSize);

    /*
    * 获取捐转运营任务详情
    */
    CfBdCaseDonateModel getDetailDonate(Long id);

    /*
    * 获取操作记录
    */
    List<CfOperatingRecordDO> getOperatingRecord(Long recordKey);

    /*
    * 条件查询捐转运营任务
    */
    CommonPageModel<CfBdCaseDonateTaskResultModel> getBdCaseDonateTask(BdCaseDonateTaskSearchParam bdCaseDonateTaskSearchParam);

    /*
    * 为app提供需运营状态的任务数量
    */
    Long getBdCaseDonateTaskCount(String uniqueCode);
}
