package com.shuidihuzhu.cf.cfgrowthtoolapi.service.starrock;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitBoardModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitThoughSuccessModel;
import com.shuidihuzhu.cf.dao.starrocks.RecruitBoardDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @author: fengxuan
 * @create 2023-03-01 16:50
 **/
@Slf4j
@Service
public class RecruitBoardServiceImpl implements IRecruitBoardService{


    @Autowired
    private RecruitBoardDao recruitBoardDao;


    @Override
    public RecruitBoardModel aggregateByOrgIds(List<Long> orgIds, String startTime, String endTime) {
        RecruitBoardModel recruitBoardModel = new RecruitBoardModel();
        if (CollectionUtils.isEmpty(orgIds)) {
            return recruitBoardModel;
        }
        return Optional.ofNullable(recruitBoardDao.aggregateByOrgIds(orgIds, startTime, endTime)).orElse(recruitBoardModel);
    }

    @Override
    public List<RecruitThoughSuccessModel> groupByUniqueCode(String startTime) {
        return recruitBoardDao.groupByUniqueCode(startTime);
    }

}
