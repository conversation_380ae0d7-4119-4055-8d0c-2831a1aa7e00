package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.PepRealTimeLeaderInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.MemberOrOrgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.BdCrmMemberModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.LeaderPermissionInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientpt.PatientHandlerModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmUserOrgInfo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * @author: fengxuan
 * @create 2020-05-19 12:06
 *
 * 组织人员信息
 **/
public interface ICrmMemberInfoService {

    /**
     * key: uniqueCode
     */
    Map<String, BdCrmMemberModel> listByUniqueCodes(List<String> uniqueCodes);



    /**
     * 找到路径最长或者创建时间最早的,见需求
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=493716998
     * @return
     */
    BdCrmOrganizationDOWithChain getRightBdCaseOrg(String uniqueCode);


    /**
     * 给小鲸鱼展示使用的接口
     * 如果有多个团队且多个团队有上下层级关系,只展示最上层
     */
    List<BdCrmOrganizationDO> listOrgForCByMis(String mis);

    /**
     * 给小鲸鱼展示使用的接口
     * 如果有多个团队且多个团队有上下层级关系,只展示最上层
     * @param uniqueCode
     * @return
     */
    List<BdCrmOrganizationDO> listForCByUniqueCode(String uniqueCode);

    /**
     * 获取用户能查看到的所有组织（包含他所在的组织）,使用缓存优化接口性能
     * @param mis
     * @return
     */
    Set<Long> listAllOrgByMis(String mis);

    /**
     * 获取用户能查看到的所有组织（包含他所在的组织）,使用缓存优化接口性能
     * @param uniqueCode
     * @return
     */
    Set<Long> listAllOrgByUniqueCode(String uniqueCode);


    /**
     * 获取用户能查看到的所有组织（包含他所在的组织）,使用缓存优化接口性能
     * @param uniqueCode
     * @return
     */
    List<BdCrmOrganizationDO> listAllOrgModelByUniqueCode(String uniqueCode);

    /**
     * 权限校验，防止爬取数据
     * @param mis
     * @param inputOrgId
     * @return
     */
    List<Integer> inspectOrgIdByMis(String mis, int inputOrgId);

    List<Integer> inspectOrgIdByUniqueCode(String uniqueCode, int inputOrgId);

    /**
     * 找到顾问对应的区域id,如果是直接在线下组织返回57,在多个区域返回随机一个所在的大区
     * @return
     */
    long getUniqueCodeAreaOrgId(String uniqueCode);

    /**
     * 找到顾问指定的区域下的组织id链路,例如2对应->蜂鸟东北区域，1对应->蜂鸟计划
     *
     * @return
     */
    List<String> getUniqueCodeSubAreaOrgIds(String uniqueCode);

    /**
     * 找到顾问对应第几层的区域id,如果在之上的组织（比如57）或者找不到对应的组织,则返回0
     * level: 从0开始,0对应的组织为57
     */
    long getOrgAreaWithLevel(String uniqueCode, int level);


    /**
     * 根据uniqueCode获取组织相关的信息,包含所管理的所有城市信息
     * @param uniqueCode
     * @return
     */
    BdCrmUserOrgInfo getCrmUserOrgInfo(String uniqueCode);

    /**
     * 根据uniqueCode查找
     * 待办查找上级
     */
    CrowdfundingVolunteer findApplyLeaderForApprove(String uniqueCode);

    List<CrowdfundingVolunteer> listLeaderWithDefaultExplicit(String uniqueCode, List<Integer> expectRoles, boolean allowTestOrg);

    List<CrowdfundingVolunteer> listLeaderByCity(String cityName, List<Integer> expectRoles);

    /**
     * 根据uniqueCode获取直接leader的信息(uniqueCode不限制为顾问)
     * @param uniqueCode
     * @param expectRoles
     * @return
     */
    CrowdfundingVolunteer listApplyLeaderWithDefaultExplicit(String uniqueCode, List<Integer> expectRoles);


    /**
     * 根据uniqueCode获取直接leader的信息(uniqueCode不限制为顾问)
     */
    CrowdfundingVolunteer listApplyLeaderWithExplicit(String uniqueCode, List<Integer> filterRoles,
                                                      List<Integer> expectRoles,
                                                      List<Integer> explicitOrder);

    CrowdfundingVolunteer listApplyLeaderWithExplicitByCaseId(int caseId, String uniqueCode, List<Integer> filterRole, List<Integer> expectRoles, List<Integer> explicitOrder);

    /**
     * @param uniqueCode
     * @param orgId 用于离职人员打分兜底
     * @param needRecursion
     * @return
     */
    OpResult<CrowdfundingVolunteer> listRandomLeaderForKpi(String uniqueCode, Long orgId, boolean needRecursion);


    //包含查看同组织下人员（顾问除外）
    LeaderPermissionInfo getLeaderPermissionByVolunteer(CrowdfundingVolunteer cfVolunteer);

    //包含查看同组织下人员（顾问除外）
    LeaderPermissionInfo getLeaderPermission(String uniqueCode);

    //包含查看同组织下角色（顾问除外）
    List<CrowdfundingVolunteerEnum.RoleEnum> listCanViewRole(List<Long> orgIds);

    //不能看同组织下的人员,同时也不能查看自己组织的orgId
    LeaderPermissionInfo getLeaderPermissionNoShowSameLevel(String uniqueCode);

    //查询组织id下的所有绑定关系,包含当前节点本身
    List<BdCrmOrgUserRelationDO> listAllSubMemberIncludeSelf(int orgId);


    /**
     * 获取排除测试大区的组织
     */
    List<BdCrmOrgUserRelationDO> listNotInTestRelation(String uniqueCode);

    /**
     * 根据城市找到对应的绑定关系
     */
    @NotNull
    CrowdfundingVolunteer getRandomVolunteerByCityIds(int cityId, String uniqueCode);

    /**
     * 给绩效人员设置组织提供方法
     */
    BdCrmOrgUserRelationDO findShortestPathForKpi(String uniqueCode);


    /**
     * 给代理三设置组织提供方法
     * @param uniqueCode
     * @return
     */
    BdCrmOrgUserRelationDO findShortestPathForDelegateKpi(String uniqueCode);


    /**
     * 渠道下面不能挂正式员工以及渠道经理不能在非蜂鸟计划下
     * true: 能添加  false:不能添加
     */
    Response<Boolean> canAddPartnerManager(CrowdfundingVolunteer crowdfundingVolunteer);

    Response<Boolean> canAddPartnerManager(long orgId, int level);

    void syncOrgPath();

    void resetStaffStatus(List<? extends MemberOrOrgModel> crmCrowdfundingModels);

    List<CrowdfundingVolunteer> listProvinceRolesVolunteer(String uniqueCode);

    /**
     * 查找能处理的人员 本人 -> 离职前的上级
     */
    CrowdfundingVolunteer getCanHandlerVolunteer(String uniqueCode);

    List<PepRealTimeLeaderInfo> getRealTimeLeader(String uniqueCode, List<Integer> expectRoles);

    /**
     * 为患者平台查找对应线下人员
     * 规则，如下
     * 1.如果是在岗、二维码未被冻结、非物料的员工记录直接返回人员
     * 2.物料走物料逻辑：通过城市或者省份找对应的员工
     * 3.离职或者冻结：判断是否是二次入职，按照城市和省份找对应上级
     * 4.二次入职：身份证是否相同
     * 5.兜底逻辑：随机找一个符合的
     */
    Pair<CrowdfundingVolunteer, String> getVolunteerForPatientPt(PatientHandlerModel patientHandlerModel);


    List<BdCrmOrganizationDO> listShortestPath(List<BdCrmOrganizationDO> organizationDoList);

    CrowdfundingVolunteer getVolunteerByPartitionName(String partitionName);

    //灰度城市,用于判断人员是否符合灰度城市
    boolean isGreyCity(List<String> cityList, String uniqueCode);

    boolean isGreyPartition(List<Long> partitionList, String uniqueCode);

    @Data
    public class BdCrmOrganizationDOWithChain extends BdCrmOrganizationDO {
        @ApiModelProperty("组织id链路,如57-130-134")
        private String chain;
        @ApiModelProperty("组织名称链路")
        private String chainName;


        public static BdCrmOrganizationDOWithChain createByDO(BdCrmOrganizationDO bdCrmOrganizationDO) {
            if (bdCrmOrganizationDO == null) {
                return null;
            }
            BdCrmOrganizationDOWithChain chain = new BdCrmOrganizationDOWithChain();
            chain.setId(bdCrmOrganizationDO.getId());
            chain.setOrgName(bdCrmOrganizationDO.getOrgName());
            chain.setParentId(bdCrmOrganizationDO.getParentId());
            chain.setOrgAttribute(bdCrmOrganizationDO.getOrgAttribute());
            return chain;
        }
    }




}
