package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgOptLogDO;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-05-18 10:33
 * <p>
 * 用户对节点的操作日志
 **/
public interface ICrmOptLogService {

    void addOptLog(BdCrmOrgOptLogDO bdCrmOrgOptLogDO);

    int countByOrgId(long orgId);

    List<BdCrmOrgOptLogDO> pageByOrgId(long orgId, int offset, int limit);

    int countByKeyWord(int optType, String keyWord);

    List<BdCrmOrgOptLogDO> pageByKeyWord(int optType, String keyWord, int offset, int limit);

    List<BdCrmOrgOptLogDO> listOptLogByOrgId(long orgId);

    List<BdCrmOrgOptLogDO> listByTimeRange(Date startTime, Date endTime);

}
