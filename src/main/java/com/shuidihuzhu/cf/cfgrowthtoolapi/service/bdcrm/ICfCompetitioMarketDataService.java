package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseBaseDataModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OrgDataStatVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDataStatParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-07
 */
public interface ICfCompetitioMarketDataService {

    /**
     * 按照城市市场数据-按照城市获取友商数据
     * @param crmDataStatParam
     * @param cityIds
     * @param allowCityEmpty
     * @return
     */
    List<OrgDataStatVO> getCityOverviewData(BdCrmDataStatParam crmDataStatParam, List<Integer> cityIds, boolean allowCityEmpty);

    /**
     * 按照城市分组查看明细数据
     * @param crmDataStatParam
     * @param cityIds
     * @return
     */
    List<OrgDataStatVO> listDataGroupByCity(BdCrmDataStatParam crmDataStatParam, List<Integer> cityIds);

    /**
     * 根据城市和日期获取诊断基础数据
     * @param dayKeys
     * @param cityIds
     * @return
     */
    List<CfBdCrmDiagnoseBaseDataModel> listCrmDiagnoseBaseDataByCityAndTheDate(List<String> dayKeys, List<Long> cityIds);
}
