package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrHospitalDepartmentManageDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.gr.GrHospitalDepartmentManageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.GrHospitalDepartmentManageParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.GrHospitalDepartmentManageService;
import com.shuidihuzhu.cf.dao.gr.GrHospitalDepartmentManageDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class GrHospitalDepartmentManageServiceImpl implements GrHospitalDepartmentManageService {

    @Resource
    private GrHospitalDepartmentManageDao hospitalDepartmentManageDao;


    @Override
    public int insert(GrHospitalDepartmentManageDO hospitalDepartmentManage) {
        return hospitalDepartmentManageDao.insert(hospitalDepartmentManage);
    }

    @Override
    public int update(GrHospitalDepartmentManageDO hospitalDepartmentManage) {
        return hospitalDepartmentManageDao.update(hospitalDepartmentManage);
    }

    @Override
    public List<GrHospitalDepartmentManageDO> getDepartmentManageListByParam(GrHospitalDepartmentManageParam param) {
        if (param == null) {
            return Lists.newArrayList();
        }
        return hospitalDepartmentManageDao.getDepartmentManageListByParam(param);
    }

    @Override
    public List<GrHospitalDepartmentManageDO> getDepartmentManageListByParamExcel(GrHospitalDepartmentManageParam param) {
        if (Objects.isNull(param)) {
            return Lists.newArrayList();
        }
        return hospitalDepartmentManageDao.getDepartmentManageListByParamExcel(param);
    }

    @Override
    public int getDepartmentManageCount(GrHospitalDepartmentManageParam param) {
        if (param == null) {
            return 0;
        }
        return hospitalDepartmentManageDao.getDepartmentManageCount(param);
    }

    @Override
    public GrHospitalDepartmentManageDO getDepartmentManageById(Long id) {
        if (id == null || id <= 0) {
            return new GrHospitalDepartmentManageDO();
        }
        return hospitalDepartmentManageDao.getDepartmentManageById(id);
    }

    @Override
    public void updateManageModel(GrHospitalDepartmentManageModel model) {
        if (Objects.isNull(model)) {
            return;
        }
        hospitalDepartmentManageDao.updateManageModel(model);
    }

    @Override
    public List<GrHospitalDepartmentManageDO> listByCustomerId(Integer customerId) {
        if (Objects.isNull(customerId) || customerId <= 0) {
            return Lists.newArrayList();
        }
        return hospitalDepartmentManageDao.listByCustomerId(customerId);
    }

    @Override
    public List<GrHospitalDepartmentManageDO> listByDepartmentIds(List<Integer> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Lists.newArrayList();
        }
        return hospitalDepartmentManageDao.listByDepartmentIds(departmentIds);
    }

    @Override
    public List<GrHospitalDepartmentManageDO> getDepartmentManageListByDepartmentName(String departmentName, Integer grCustomerId) {
        if (Objects.isNull(grCustomerId) || grCustomerId <= 0) {
            return Lists.newArrayList();
        }
        return hospitalDepartmentManageDao.getDepartmentManageListByDepartmentName(departmentName, grCustomerId);
    }
}
