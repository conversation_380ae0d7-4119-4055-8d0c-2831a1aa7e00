package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.DepartmentHospitalSummaryDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.department.DepartmentModifyModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.department.DepartmentPriorityExcelModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.department.DepartmentPriorityModifyModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.department.AdminDepartmentManagerParam;

import java.util.List;

/**
 * 科室-医院信息(DepartmentHospitalSummary)表服务接口
 *
 * <AUTHOR>
 * @since 2022-12-07 14:47:42
 */
public interface DepartmentHospitalSummaryService {

    DepartmentHospitalSummaryDO queryById(long id);

    DepartmentHospitalSummaryDO queryByHospitalCode(String hospitalCode);

    List<DepartmentHospitalSummaryDO> listByHospitalCode(List<String> vhospitalCodes);

    boolean deleteById(long id);


    int countByAdminDepartmentManagerParam(AdminDepartmentManagerParam managerParam);

    List<DepartmentHospitalSummaryDO> pageByAdminDepartmentManagerParam(AdminDepartmentManagerParam managerParam);


    void confirmDepartment(long id, int confirmStatus, String mark, AdminUserAccountModel validUserAccountById);

    /**
     * 维护医院信息
     */
    void maintainDepartmentHospital(DepartmentModifyModel departmentModifyModel);

    void modifyPriority(List<DepartmentPriorityModifyModel> priorityModifyModels, AdminUserAccountModel validUserAccountById);

    String modifyPriorityByExcel(List<DepartmentPriorityExcelModel> priorityList, AdminUserAccountModel validUserAccountById);

    List<DepartmentHospitalSummaryDO> getByHospitalProvinceCity(String hospitalProvince, String hospitalCity);

}
