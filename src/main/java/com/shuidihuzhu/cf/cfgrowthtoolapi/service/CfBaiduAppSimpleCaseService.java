package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfBaiduAppSimpleCaseDO;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-04-23 21:33
 **/
public interface CfBaiduAppSimpleCaseService {

    void insert(CfBaiduAppSimpleCaseDO cfBaiduAppSimpleCaseDO);

    void update(CfBaiduAppSimpleCaseDO cfBaiduAppSimpleCaseDO);

    List<CfBaiduAppSimpleCaseDO> listByCursor(long lastCaseId, int limit);

    void updateContent(CfBaiduAppSimpleCaseDO cfBaiduAppSimpleCaseDO);

    List<CfBaiduAppSimpleCaseDO> listByDisease(String diseaseName);

    CfBaiduAppSimpleCaseDO getByInfoId(String infoId);

    List<String> listUnSubmitInfoIds(long id);

    void updateSubmitStatus(List<String> infoIds);
}
