package com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.campaignv2.CrmOrgPreDayCampaignDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.CampCommonSearchParam;

import java.util.List;

/**
 * 每日-每个组织当天累计数据(CrmOrgPreDayCampaign)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-10 17:24:46
 */
public interface CrmOrgPreDayCampaignService {

    void batchInsertOrUpdate(List<CrmOrgPreDayCampaignDO> crmOrgPreDayCampaignList, String dateKey);

    int update(CrmOrgPreDayCampaignDO crmOrgPreDayCampaign);

    boolean deleteById(long id);

    CrmOrgPreDayCampaignDO sumByCampCommonSearchParam(CampCommonSearchParam searchParam);

    CrmOrgPreDayCampaignDO highDateByCampCommonSearchParam(CampCommonSearchParam searchParam);

    CrmOrgPreDayCampaignDO avgDateByCampCommonSearchParam(CampCommonSearchParam searchParam);

    List<CrmOrgPreDayCampaignDO> listByDaysAndOrgId(long orgId, List<String> days);

}