package com.shuidihuzhu.cf.cfgrowthtoolapi.service.clew;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.ClewModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-09-03
 */
public interface ICfClewBaseInfoService {

    List<ClewModel> listSourceTypeUniqueCodeAndEncryptPhone(String volunteerCode, String encryptPhone);

    Long getLatelyCaseIdByClewIds(List<Long> clewIds);

    List<CfClewBaseInfoDO> getClewBaseInfoByStatusAndDiseaseName(Date startTime, Date endTime, Integer status, String diseaseNameRegex);
}
