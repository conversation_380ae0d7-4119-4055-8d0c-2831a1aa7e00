package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMasterForGrowthtoolDelegate;
import com.shuidihuzhu.cf.dao.CfToufangInviteCaseRelationDao;
import com.shuidihuzhu.cf.dao.CfToufangInvitorVisitDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfToufangInviteService;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInviteCaseRelationDO;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInvitorVisitDO;
import com.shuidihuzhu.client.cf.growthtool.model.PageReturnModel;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RefreshScope
@Slf4j
public class CfToufangInviteServiceImpl implements ICfToufangInviteService {
    @Autowired
    private CfToufangInvitorVisitDao cfToufangInvitorVisitDao;

    @Autowired
    private CfToufangInviteCaseRelationDao cfToufangInviteCaseRelationDao;

    @Autowired
    private ICfMasterForGrowthtoolDelegate cfMasterForGrowthtoolDelegate;


    private int INVITE_MAX_USER = 10;

    @Override
    public int add(CfToufangInvitorVisitDO cfToufangInvitorVisitDO) {
        return cfMasterForGrowthtoolDelegate.addCfToufangInvitorVisitDO(cfToufangInvitorVisitDO);
    }

    @Override
    public CfToufangInvitorVisitDO getCfToufangInvitorVisitDOByUserId(Long userId, String channel) {
        return cfToufangInvitorVisitDao.getCfToufangInvitorVisitDOByUserId(userId,channel);
    }

    @Override
    public int addInviteRelation(CfToufangInviteCaseRelationDO caseRelationDO) {
        return cfMasterForGrowthtoolDelegate.addInviteRelation(caseRelationDO);
    }

    @Override
    public List<CfToufangInviteCaseRelationDO> getInviteCaseRelation(long userId) {
        return cfToufangInviteCaseRelationDao.getInviteCaseRelation(String.valueOf(userId));
    }

    @Override
    public boolean checkIfReachInvitorLimtForSourceUserId(long sourceUserId) {
        Date startTime = DateUtil.getCurrentDate();
        Date endTime = new Date();
        int count = cfToufangInvitorVisitDao.getCfToufangInvitorVisitDOCountByUserId(sourceUserId,INVITE_CHANNE,startTime,endTime);
        return count>=INVITE_MAX_USER;
    }

    @Override
    public CfToufangInviteCaseRelationDO getInviteCaseRelationByUserIdAndInfoId(long userId, String infoUuid) {
        return cfToufangInviteCaseRelationDao.getInviteCaseRelationByUserIdAndInfoId(String.valueOf(userId),infoUuid);
    }

    @Override
    public PageReturnModel<CfToufangInviteCaseRelationDO> listRelationPaging(long userId, int current, int pageSize) {
        PageReturnModel<CfToufangInviteCaseRelationDO> pageReturnModel = new PageReturnModel<>();
        if (userId <= 0 || pageSize <= 0 || current < 1) {
            pageReturnModel.setList(Lists.newArrayList());
            return pageReturnModel;
        }
        int countRaiserRecommend = cfToufangInviteCaseRelationDao.countRaiserRecommend(String.valueOf(userId));
        if (countRaiserRecommend == 0) {
            pageReturnModel.setList(Lists.newArrayList());
            return pageReturnModel;
        }
        List<CfToufangInviteCaseRelationDO> relationDOList = cfToufangInviteCaseRelationDao.listRaiserRecommendPaging(String.valueOf(userId), (current - 1) * pageSize, pageSize);
        pageReturnModel.setList(relationDOList);
        pageReturnModel.setTotal(countRaiserRecommend);
        return pageReturnModel;
    }

}
