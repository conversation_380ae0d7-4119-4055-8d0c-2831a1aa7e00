package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCaseBaseDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.*;
import com.shuidihuzhu.cf.dao.CrowdfundingVolunteerDao;
import com.shuidihuzhu.cf.performance.data.meta.PepGrowthtoolCaseIdTemplate;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2022-07-28 16:15
 **/
@Slf4j
@Service
public class PartnerPushData {

    @Autowired
    private PepClient pepClient;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private ICrmSelfBuiltOrgReadService selfBuiltOrgReadService;

    @Autowired
    private ICrmOrganizationRelationService orgRelationService;

    @Autowired
    private CrowdfundingVolunteerDao crowdfundingVolunteerDao;

    @Autowired
    private ICfKpiCaseBaseDataService cfKpiCaseBaseDataService;

    @Autowired
    private PepLotObtainService pepLotObtainService;

    public static final String testOrgName = "测试";


    private void pushBusinessData(JSONObject jsonObject) {
        mqProducerService.pushBusinessData(jsonObject);
    }

    public void syncCaseData() {
        List<Long> lotIds = pepLotObtainService.getLotIds(PepPushEnum.little_aid_case);
        for (Long caseBusinessLot : lotIds) {
            Response<LotInfo> lotResponse = pepClient.getLotInfoById(caseBusinessLot);
            log.info("获取案例业务批次,id:{},response:{}", caseBusinessLot, lotResponse);
            if (lotResponse.notOk() || lotResponse.getData() == null) {
                return;
            }
            LotInfo lotInfo = lotResponse.getData();
            //找到对应的业务数据
            String startTime = new DateTime(lotInfo.getLotStartTime()).toString(GrowthtoolUtil.ymdhmsfmt);
            String endTime = new DateTime(lotInfo.getLotEndTime()).toString(GrowthtoolUtil.ymdhmsfmt);

            //统一按照批次结束时间来
            DateTime dateTime = new DateTime(lotInfo.getUseDataTime());
            if (dateTime.isAfter(DateTime.now().minusDays(1))) {
                dateTime = DateTime.now().minusDays(1);
            }

            List<CrowdfundingVolunteer> partnerList = crowdfundingVolunteerDao.listAllPartner();

            List<CrowdfundingVolunteer> volunteerList = partnerList.stream()
                    .filter(item -> Objects.equals(item.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AID.getLevel()))
                    .collect(Collectors.toList());

            List<CfKpiCaseBaseDataDO> dataList = cfKpiCaseBaseDataService.getCaseBaseMapByUniqueCode(startTime, endTime, dateTime.toString(GrowthtoolUtil.ymdfmt));

            for (CfKpiCaseBaseDataDO cfKpiCaseBaseDataDO : dataList) {
                Optional<CrowdfundingVolunteer> first = volunteerList.stream()
                        .filter(item -> Objects.equals(item.getUniqueCode(), cfKpiCaseBaseDataDO.getUniqueCode()))
                        .findFirst();
                if (first.isEmpty()) {
                    //非小助理数据不要推送
                    continue;
                }
                //离职后的数据就不要推送了
                if (first.get().getLeaveTime() != null
                        && first.get().getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue()
                        && first.get().getLeaveTime().before(cfKpiCaseBaseDataDO.getFirstApproveTime())) {
                    continue;
                }
                PepGrowthtoolCaseIdTemplate casePepTemplate = new PepGrowthtoolCaseIdTemplate();
                casePepTemplate.setUserId(cfKpiCaseBaseDataDO.getUniqueCode());
                casePepTemplate.setCase_id(cfKpiCaseBaseDataDO.getCaseId());
                casePepTemplate.setInfo_uuid(cfKpiCaseBaseDataDO.getInfoUuid());
                casePepTemplate.setPatient_name(cfKpiCaseBaseDataDO.getPatientName());
                casePepTemplate.setTitle(cfKpiCaseBaseDataDO.getTitle());
                casePepTemplate.setCase_create_time(cfKpiCaseBaseDataDO.getCaseCreateTime());
                casePepTemplate.setCase_end_time(cfKpiCaseBaseDataDO.getCaseEndTime());
                casePepTemplate.setFirst_approve_time(cfKpiCaseBaseDataDO.getFirstApproveTime());
                casePepTemplate.setFirst_share_time(cfKpiCaseBaseDataDO.getFirstShareTime());
                casePepTemplate.setIs_offline_channel(GrowthtoolUtil.convertToLong(cfKpiCaseBaseDataDO.getIsOfflineChannel()));
                if (cfKpiCaseBaseDataDO.getValidAmount() <= 0) {
                    casePepTemplate.setCase_amount(cfKpiCaseBaseDataDO.getCaseAmount() / 100);
                    casePepTemplate.setDonate_num(GrowthtoolUtil.convertToLong(cfKpiCaseBaseDataDO.getDonateNum()));
                } else {
                    casePepTemplate.setCase_amount(cfKpiCaseBaseDataDO.getValidAmount() / 100);
                    casePepTemplate.setDonate_num(cfKpiCaseBaseDataDO.getValidDonateNum());
                }
                casePepTemplate.setDonate_user_num(GrowthtoolUtil.convertToLong(cfKpiCaseBaseDataDO.getDonateUserNum()));
                casePepTemplate.setLaunch_pic_num(GrowthtoolUtil.convertToLong(cfKpiCaseBaseDataDO.getLaunchPicNum()));
                casePepTemplate.setClew_source_type(GrowthtoolUtil.convertToLong(cfKpiCaseBaseDataDO.getClewSourceType()));
                casePepTemplate.setFundraiser_share_days(GrowthtoolUtil.convertToLong(cfKpiCaseBaseDataDO.getFundraiserShareDays()));
                casePepTemplate.setLotId(caseBusinessLot);
                JSONObject jsonObject = (JSONObject) JSONObject.toJSON(casePepTemplate);
                pushBusinessData(jsonObject);
            }
        }
    }

    public void syncPartner() {
        List<Long> lotIds = pepLotObtainService.getLotIds(PepPushEnum.little_aid_member);
        for (Long memberLot : lotIds) {

            Response<LotInfo> lotResponse = pepClient.getLotInfoById(memberLot);
            log.info("获取人员批次,id:{},response:{}", memberLot, lotResponse);
            if (lotResponse.notOk() || lotResponse.getData() == null) {
                return;
            }
            LotInfo lotInfo = lotResponse.getData();
            //判断取哪一部分数据
            Date lotStartTime = lotInfo.getLotStartTime();
            if (lotStartTime == null) {
                return;
            }
            List<CrowdfundingVolunteer> partnerList = crowdfundingVolunteerDao.listAllPartner();
            List<CrowdfundingVolunteer> volunteerList = partnerList.stream()
                    .filter(item -> Objects.equals(item.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AID.getLevel()))
                    .collect(Collectors.toList());
            DateTime startTime = new DateTime(lotInfo.getLotStartTime());
            boolean noNeedPush = new DateTime(lotInfo.getLotEndTime()).plusDays(1).isBefore(DateTime.now())
                    && new DateTime(lotInfo.getLotStartTime()).plusDays(1).isAfter(DateTime.now());
            if (noNeedPush) {
                log.info("超过当前周期,无需推动");
                return;
            }
            //查找快照中的人员数据
            List<PepUserModel> pepUserModels = Lists.newArrayList();
            for (CrowdfundingVolunteer volunteer : volunteerList) {
                //周期前离职的人员不算
                if (volunteer.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue()) {
                    if (volunteer.getLeaveTime() != null && startTime.isAfter(new DateTime(volunteer.getLeaveTime()))) {
                        continue;
                    }
                }
                PepUserModel pepUserModel = new PepUserModel();
                List<BdCrmOrgUserRelationDO> relationDOList = orgRelationService.listMemberOrgRelationByUniqueCode(volunteer.getUniqueCode());
                if (CollectionUtils.isNotEmpty(relationDOList)) {
                    BdCrmOrgUserRelationDO relationDO = relationDOList.get(0);
                    BdCrmOrganizationDO organizationDO = selfBuiltOrgReadService.getCurrentOrgById(relationDO.getOrgId());
                    Map<Long, String> orgMap = selfBuiltOrgReadService.listChainByOrgIdsWithDefaultSplitter(Lists.newArrayList(relationDO.getOrgId()));
                    if (organizationDO != null) {
                        pepUserModel.setCity(organizationDO.getCityName());
                    }
                    if (MapUtils.isNotEmpty(orgMap)) {
                        String orgPath = orgMap.getOrDefault(relationDO.getOrgId(), "");
                        pepUserModel.setOrgPath(orgPath);
                    }
                } else {
                    List<BdCrmOrgUserRelationDO> orgUserRelationList = orgRelationService.listMemberOrgRelationNoMatterDelete(volunteer.getUniqueCode())
                            .stream()
                            .sorted(Comparator.comparing(BdCrmOrgUserRelationDO::getId).reversed())
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(orgUserRelationList)) {
                        BdCrmOrgUserRelationDO relationDO = orgUserRelationList.get(0);
                        BdCrmOrganizationDO currentOrg = selfBuiltOrgReadService.getCurrentOrgById(relationDO.getOrgId());
                        if (currentOrg != null) {
                            List<BdCrmOrganizationDO> organizationDOS = selfBuiltOrgReadService.listParentOrgAsChain(currentOrg.getId());
                            String chainName = organizationDOS.stream().map(item -> String.valueOf(item.getOrgName())).collect(Collectors.joining("-"));
                            pepUserModel.setCity(currentOrg.getCityName());
                            pepUserModel.setOrgPath(chainName);
                        }
                    }
                }
                pepUserModel.setLotId(memberLot);
                pepUserModel.setUserId(volunteer.getUniqueCode());
                pepUserModel.setUserName(volunteer.getVolunteerName());
                pepUserModel.setMemberLevel(volunteer.getLevel());
                pepUserModel.setWorkStatus(volunteer.getWorkStatus());
                pepUserModel.setLeaveTime(volunteer.getLeaveTime());
                pepUserModel.setMis(volunteer.getMis());
                pepUserModel.setEntryTime(volunteer.getEntryTime());
                pepUserModel.setJobNum(volunteer.getJobNum());
                pepUserModel.setEncryptIdCard(volunteer.getIdCardNumber());
                pepUserModel.setEncryptMobile(volunteer.getMobile());
                pepUserModels.add(pepUserModel);
            }

            int testOrgId = apolloService.getTestOrgId();
            for (PepUserModel pepUserModel : pepUserModels) {
                //过滤下，当前人员如果只再测试区域不需要推送
                if (StringUtils.isNotBlank(pepUserModel.getOrgPath()) && pepUserModel.getOrgPath().contains(testOrgName)) {
                    List<BdCrmOrgUserRelationDO> relationDOList = orgRelationService.listMemberOrgRelationByUniqueCode(pepUserModel.getUserId());
                    if (CollectionUtils.isNotEmpty(relationDOList)) {
                        boolean allInTest = relationDOList
                                .stream()
                                .allMatch(item -> selfBuiltOrgReadService.listParentOrgAsChain(item.getOrgId())
                                        .stream()
                                        .map(BdCrmOrganizationDO::getId)
                                        .collect(Collectors.toList())
                                        .contains((long) testOrgId));
                        if (allInTest) {
                            continue;
                        }
                    }
                }
                //发送人员给绩效平台
                pushMemberData(pepUserModel);
            }
        }
    }


    private void pushMemberData(PepUserModel pepUserModel) {
        mqProducerService.pushMemberData(pepUserModel);
    }


}
