package com.shuidihuzhu.cf.cfgrowthtoolapi.service.clew;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGwClewTaskDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-12
 */
public interface ICfGwClewTaskService {

    /**
     * 获取clewIdList
     * @param uniqueCode
     * @param phone
     * @return
     */
    List<Long> listClewIdByPhoneAndUniqueCode(String uniqueCode, String phone);

    List<CfGwClewTaskDO> getGwClewTaskByPhone(String encryptPhone, Date startDate);
}
