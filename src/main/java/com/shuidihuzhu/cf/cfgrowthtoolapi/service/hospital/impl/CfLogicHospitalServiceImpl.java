package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfLogicHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.StandardHospitalSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.CfLogicHospitalService;
import com.shuidihuzhu.cf.dao.CfLogicHospitalDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2021-03-07 3:55 下午
 **/
@Service
public class CfLogicHospitalServiceImpl implements CfLogicHospitalService {

    @Resource
    private CfLogicHospitalDao cfLogicHospitalDao;

    @Override
    public List<CfLogicHospitalDO> pageByCityName(StandardHospitalSearchParam param) {
        if (param == null) {
            return Lists.newArrayList();
        }
        return cfLogicHospitalDao.pageByCityName(param);
    }

    @Override
    public int countByCityName(StandardHospitalSearchParam param) {
        if (param == null) {
            return 0;
        }
        return cfLogicHospitalDao.countByCityName(param);
    }

    @Override
    public List<CfLogicHospitalDO> pageByCityNameAndPublicFlag(StandardHospitalSearchParam param) {
        if (param == null) {
            return Lists.newArrayList();
        }
        return cfLogicHospitalDao.pageByCityNameAndPublicFlag(param);
    }

    @Override
    public int countByCityNameAndPublicFlag(StandardHospitalSearchParam param) {
        if (param == null) {
            return 0;
        }
        return cfLogicHospitalDao.countByCityNameAndPublicFlag(param);
    }

    @Override
    public List<CfLogicHospitalDO> listByVvhospitalCode(List<String> vvhospitalCodes) {
        if (CollectionUtils.isEmpty(vvhospitalCodes)) {
            return Lists.newArrayList();
        }
        return cfLogicHospitalDao.listByVvhospitalCode(vvhospitalCodes);
    }
}
