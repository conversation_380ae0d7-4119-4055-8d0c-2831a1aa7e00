package com.shuidihuzhu.cf.cfgrowthtoolapi.service.call;


import com.shuidihuzhu.client.baseservice.msg.callrobot.request.CallRobotRecordSendRequest;
import com.shuidihuzhu.client.baseservice.msg.callrobot.response.CallRobotRecordSendResponse;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.msg.vo.MsgResponse;


/**
 * <AUTHOR>
 * @Date 2022/10/24 15:10
 */
public interface ICallRobotRecord {

    MsgResponse<CallRobotRecordSendResponse> callRobotRecordSend(CallRobotRecordSendRequest callRobotRecordSendRequest, CfBdCaseInfoDo cfBdCaseInfoDo, CrowdfundingVolunteer volunteer);
}
