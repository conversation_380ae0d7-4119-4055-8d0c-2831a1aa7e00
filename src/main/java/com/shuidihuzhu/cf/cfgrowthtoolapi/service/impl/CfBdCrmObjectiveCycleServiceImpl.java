package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdCrmObjectiveConfigScheduleVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdCrmObjectiveValueConfigVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.SeaCfBdCrmObjectiveVO;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveCycle;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmObjectiveCycleMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfBdCrmObjectiveCycleService;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2021/2/23 上午10:39
 */
@Service
public class CfBdCrmObjectiveCycleServiceImpl implements CfBdCrmObjectiveCycleService {

    @Resource
    private CfBdCrmObjectiveCycleMapper cfBdCrmObjectiveCycleMapper;

    @Override
    public OpResult<Long> insertSelective(CfBdCrmObjectiveCycle record) {
        cfBdCrmObjectiveCycleMapper.insertSelective(record);
        return OpResult.createSucResult(record.getId());
    }
    @Override
    public OpResult checkCanSaveOrUpdate(CfBdCrmObjectiveCycle record){
        CfBdCrmObjectiveCycle cfBdCrmObjectiveCycle = cfBdCrmObjectiveCycleMapper.getByObjectiveName(Optional.ofNullable(record.getObjectiveName()).orElse(""));
        if (cfBdCrmObjectiveCycle != null) {
            //添加时，目标名称不能重复
            if (record.getId() == null || record.getId() == 0) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.OBJECTIVE_NAME_REPETITION);
            }
            //修改时，如果修改了目标名称，目标名称不能重复
            if (!record.getId().equals(cfBdCrmObjectiveCycle.getId())) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.OBJECTIVE_NAME_REPETITION);
            }
        }
        cfBdCrmObjectiveCycle = cfBdCrmObjectiveCycleMapper.getByStartTimeOrEndTime(record.getObjectiveType(), record.getStartTime(), record.getEndTime());
        if (cfBdCrmObjectiveCycle != null) {
            //添加时，起止日期不能有重叠
            if (record.getId() == null || record.getId() == 0) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.OBJECTIVE_TIME_RANGE_CONFLICT);
            }
            //修改时，如果修改了起止日期，起止日期不能有重叠
            if (!record.getId().equals(cfBdCrmObjectiveCycle.getId())) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.OBJECTIVE_TIME_RANGE_CONFLICT);
            }
        }
        List<CfBdCrmObjectiveCycle> objectiveCycleList = cfBdCrmObjectiveCycleMapper.listUnStartByCurrentDate(DateUtil.getCurrentDateStr()).stream().filter(objectiveCycle -> objectiveCycle.getObjectiveType()==record.getObjectiveType()).collect(Collectors.toList());
        // 周目标只能提前设置13个周   月目标只能提前设置3个月
        if (record.getObjectiveType() == CfBdCrmObjectiveCycle.ObjectiveTypeEnum.WEEK_OBJECTIVE.getType()
                && objectiveCycleList.size()>=13) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OBJECTIVE_CYCLE_NOT_ALLOW_1);
        }else if (record.getObjectiveType() == CfBdCrmObjectiveCycle.ObjectiveTypeEnum.MONTH_OBJECTIVE.getType()
                && objectiveCycleList.size()>=3){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OBJECTIVE_CYCLE_NOT_ALLOW_2);
        }
        return OpResult.createSucResult();
    }

    @Override
    public CfBdCrmObjectiveCycle selectByPrimaryKey(Long id) {
        return cfBdCrmObjectiveCycleMapper.selectByPrimaryKey(id);
    }

    @Override
    public OpResult<Long> updateByPrimaryKeySelective(CfBdCrmObjectiveCycle record) {
        cfBdCrmObjectiveCycleMapper.updateByPrimaryKeySelective(record);
        return OpResult.createSucResult(record.getId());
    }

    @Override
    public CommonResultModel<SeaCfBdCrmObjectiveVO> listObjectiveCyclePage(String currentDate, Integer objectiveType, Integer pageNo, Integer pageSize) {
        CommonResultModel<SeaCfBdCrmObjectiveVO> commonResultModel = new CommonResultModel<>();
        long total = cfBdCrmObjectiveCycleMapper.countByObjectiveTypeWithTime(currentDate, objectiveType);
        if (total > 0) {
            int offset = (pageNo - 1) * pageSize;
            commonResultModel.setModelList(cfBdCrmObjectiveCycleMapper.listByObjectiveTypeWithTime(currentDate, objectiveType, offset, pageSize));
        }
        commonResultModel.setTotal(total);
        return commonResultModel;
    }

    @Override
    public CfBdCrmObjectiveCycle getCurrentObjectiveCycleByObjectiveType(String currentDate, Integer objectiveType) {
        return cfBdCrmObjectiveCycleMapper.getCurrentObjectiveCycleByObjectiveType(currentDate, objectiveType);
    }
    @Override
    public List<CfBdCrmObjectiveCycle> getCurrentObjectiveCycle(String currentDate) {
        return cfBdCrmObjectiveCycleMapper.getCurrentObjectiveCycle(currentDate);
    }

    @Override
    public List<CfBdCrmObjectiveCycle> listObjectiveCycleWithIds(List<Long> objectiveCycleIdList) {
        return cfBdCrmObjectiveCycleMapper.listByIds(objectiveCycleIdList);
    }

    @Override
    public List<CfBdCrmObjectiveCycle> listUnStartByCurrentDate(String currentDateStr){
        return cfBdCrmObjectiveCycleMapper.listUnStartByCurrentDate(currentDateStr);
    }

    @Override
    public List<CfBdCrmObjectiveCycle> listCurrentDateStartCycle(String currentDateStr) {
        return cfBdCrmObjectiveCycleMapper.listCurrentDateStartCycle(currentDateStr);
    }

    @Override
    public List<CfBdCrmObjectiveCycle> listCurrentDateWithBeforeCycle(String currentDateStr) {
        return cfBdCrmObjectiveCycleMapper.listCurrentDateWithBeforeCycle(currentDateStr);
    }

    @Override
    public List<CfBdCrmObjectiveCycle> listCurrentDateWithAfterCycle(String currentDateStr) {
        return cfBdCrmObjectiveCycleMapper.listCurrentDateWithAfterCycle(currentDateStr);
    }

    @Override
    public List<CfBdCrmObjectiveConfigScheduleVO> getCurrentConfigSchedule(Long id) {
        return cfBdCrmObjectiveCycleMapper.getCurrentConfigSchedule(id);
    }

    @Override
    public List<CfBdCrmObjectiveValueConfigVO> listCurrentConfigIndicatorConfig(Long cycleId) {
        return cfBdCrmObjectiveCycleMapper.listCurrentConfigIndicatorConfig(cycleId).stream().map(item -> item.handleDataForShow()).collect(Collectors.toList());
    }

    @Override
    public CfBdCrmObjectiveCycle getByTypeStartTimeOrEndTime(Integer objectiveType, String startTime, String endTime) {
        return cfBdCrmObjectiveCycleMapper.getByTypeStartTimeOrEndTime(objectiveType, startTime, endTime);
    }
}
