package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.*;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

/**
 * @description:
 * @author: zhen<PERSON><PERSON><PERSON>
 * @date: 2021-09-01 21:06
 **/
public interface PartnerApproveService {

    PartnerApproveVo getApproveInfo(long approveId, long attendId);

    int updateApproveStatus(long approveId, String remark, int approveState);

    List<AttendDateInfoVO> getDateInfo(String uniquecode);

    /**
     * @param approveStatus    审核状态
     * @param leaderUniqueCode 负责人唯一code
     * @return
     */
    List<PartnerAttendInfoVO> getAttendInfoOfMy(int approveStatus, String leaderUniqueCode);

    List<ApproveMisVO> getAttendInfoOfApprove(int approveStatus, String approveUniqueCode);

    List<ApprovePartnerModel> getPartnerInfoOfApprove(int approveStatus, String approveUniqueCode);

    PartnerInfoModel searchApproveInfo(long approveId);

    List<PartnerApproveDTO> getInfoByBizId(Long bizId, int type);

    int adjustApproveInfo(long id, CrowdfundingVolunteer approveVolunteer);
}
