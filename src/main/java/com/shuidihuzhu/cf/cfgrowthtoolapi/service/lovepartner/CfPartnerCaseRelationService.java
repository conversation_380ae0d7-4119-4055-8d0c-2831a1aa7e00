package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DateQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PartnerEnums;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerCaseRelationDo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */
public interface CfPartnerCaseRelationService {

    /**
     * 插入案例关联数据
     * @param cfPartnerCaseRelationDo
     * @return
     */
    int insert(CfPartnerCaseRelationDo cfPartnerCaseRelationDo);

    /**
     * 根据caseId获取关联信息
     * 场景:先扫码,后发起
     * @param caseId
     * @return
     */
    CfPartnerCaseRelationDo getCaseRelationByCaseId(Integer caseId);

    List<CfPartnerCaseRelationDo> listByPartnerUniqueCode(String partnerUniqueCode, DateQueryParam dateQueryParam);

    /**
     * 获取所有未计算的案例
     * @param caseStartTime
     * @param caseEndTime
     * @param uniqueCodeList
     * @param caseCalcEnum
     * @return
     */
    List<Integer> listCaseRelationWithCaseTimeAndUniqueCode(Date caseStartTime, Date caseEndTime, List<String> uniqueCodeList, PartnerEnums.CaseCalcEnums caseCalcEnum);

    int updateCaseCalStatus(List<Integer> caseIds, PartnerEnums.CaseCalcEnums caseCalcEnum);
}
