package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patient;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.patient.BdPatientServePtDO;
import com.shuidihuzhu.cf.dao.patient.BdPatientServePtDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 患者服务平台(BdPatientServePt)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-24 16:49:22
 */
@Service("bdPatientServePtService")
public class BdPatientServePtServiceImpl implements BdPatientServePtService {
   
    @Resource
    private BdPatientServePtDao bdPatientServePtDao;

    @Override
    public BdPatientServePtDO queryById(long id) {
        return bdPatientServePtDao.queryById(id);
    }

    @Override
    public List<BdPatientServePtDO> listByPhoneAndScene(String phone, int scene, Date startTime) {
        return bdPatientServePtDao.listByPhoneAndScene(phone, scene, startTime);
    }


    @Override
    public int insert(BdPatientServePtDO bdPatientServePt) {
        return bdPatientServePtDao.insert(bdPatientServePt);
    }

    @Override
    public boolean deleteById(long id) {
        return bdPatientServePtDao.deleteById(id) > 0;
    }
}
