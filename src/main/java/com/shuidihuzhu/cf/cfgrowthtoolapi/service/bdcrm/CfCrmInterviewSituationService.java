package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmBdInterviewSituationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmHospitalInterviewSituationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmInterviewSituationRankModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmBdInterviewDurationModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmHospitalInterviewDurationModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmOrgHospitalInterviewDurationModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfCrmBdInterviewSituationInfoVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfCrmHospitalInterviewSituationInfoVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.HospitalSimpleVO;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/6/28 下午4:40
 */
public interface CfCrmInterviewSituationService{
    CfCrmBdInterviewSituationInfoVO getInterviewSituation(List<String> searchUniqueCodeList, int role,int showType, int maxRank);

    CfCrmHospitalInterviewSituationInfoVO getHospitalInterviewSituation(List<Integer> searchCityIdList, int maxRank);

    CfCrmHospitalInterviewSituationInfoVO getHospitalInterviewSituationByVhospitalCodeList(List<String> vhospitalCodeList, int maxRank);

    CfCrmHospitalInterviewSituationInfoVO getHospitalInterviewSituationByOrgIdList(List<Long> orgIdList, int maxRank);

    long countFromBdInterviewByDateTime(String currentDateStr);

    void batchInsertBdInterview(List<CfCrmBdInterviewSituationDO> cfCrmBdInterviewSituationDOS);

    List<CfCrmInterviewSituationRankModel> listBdInterviewSituationByDateTime(String dateTime);

    List<Long> listBdInterviewIdByDateTime(String dateTime);

    List<CfCrmBdInterviewDurationModel> listBdInterviewByIdRange(Long minId, Long maxId);

    void batchUpdateBdDuration(List<CfCrmBdInterviewDurationModel> list);

    void batchUpdateBdRank(List<CfCrmInterviewSituationRankModel> bdInterviewSituationList);

    List<CfCrmHospitalInterviewDurationModel> listHospitalInterviewByIdRange(long minId, long maxId);

    List<CfCrmOrgHospitalInterviewDurationModel> listOrgHospitalInterviewByIdRange(long minId, long maxId);

    List<CfCrmInterviewSituationRankModel> listHospitalRankModelByDateTime(String dateTime);

    List<CfCrmInterviewSituationRankModel> listOrgHospitalRankModelByDateTime(String dateTime);

    List<Long> listHospitalInterviewIdByDateTime(String dateTime);

    List<Long> listOrgHospitalInterviewIdByDateTime(String dateTime);

    void batchUpdateHospitalDuration(List<CfCrmHospitalInterviewDurationModel> interviewSituationList);

    void batchUpdateOrgHospitalDuration(List<CfCrmOrgHospitalInterviewDurationModel> interviewSituationList);

    void batchUpdateHospitalRank(List<CfCrmInterviewSituationRankModel> interviewSituationList);

    void batchUpdateOrgHospitalRank(List<CfCrmInterviewSituationRankModel> interviewSituationList);

    long countFromHospitalInterviewByDateTime(String currentDateStr);

    void batchInsertHospitalInterview(List<CfCrmHospitalInterviewSituationDO> situationDOList);

    void saveCfCrmOrgHospitalInterviewSituation(HospitalSimpleVO hospitalSimpleVO, BdCrmOrganizationDO organizationDO);
}
