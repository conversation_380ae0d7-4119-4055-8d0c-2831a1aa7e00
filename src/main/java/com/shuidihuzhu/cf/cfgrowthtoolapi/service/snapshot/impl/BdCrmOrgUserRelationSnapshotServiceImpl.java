package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdCrmOrgUserRelationSnapshot;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.BdCrmOrgUserRelationSnapshotService;
import com.shuidihuzhu.cf.dao.snapshot.BdCrmOrgUserRelationSnapshotDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 日目标人员关系信息快照(BdCrmOrgUserRelationSnapshot)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-02 14:58:48
 */
@Service("bdCrmOrgUserRelationSnapshotService")
public class BdCrmOrgUserRelationSnapshotServiceImpl implements BdCrmOrgUserRelationSnapshotService {
   
    @Resource
    private BdCrmOrgUserRelationSnapshotDao bdCrmOrgUserRelationSnapshotDao;

    @Override
    public BdCrmOrgUserRelationSnapshot queryById(long id) {
        return bdCrmOrgUserRelationSnapshotDao.queryById(id);
    }
    

    @Override
    public void batchInsert(List<BdCrmOrgUserRelationSnapshot> userRelationSnapshotList) {
        bdCrmOrgUserRelationSnapshotDao.batchInsert(userRelationSnapshotList);
    }

    @Override
    public int update(BdCrmOrgUserRelationSnapshot bdCrmOrgUserRelationSnapshot) {
        return bdCrmOrgUserRelationSnapshotDao.update(bdCrmOrgUserRelationSnapshot);
    }

    @Override
    public boolean deleteById(long id) {
        return bdCrmOrgUserRelationSnapshotDao.deleteById(id) > 0;
    }

    @Override
    public List<BdCrmOrgUserRelationSnapshot> listByUniqueCode(String dateKey, String uniqueCode) {
        return bdCrmOrgUserRelationSnapshotDao.listByUniqueCode(dateKey, uniqueCode);
    }

    @Override
    public List<BdCrmOrgUserRelationSnapshot> listRelationByOrgId(String dateKey, long orgId) {
        return bdCrmOrgUserRelationSnapshotDao.listRelationByOrgId(dateKey, orgId);
    }
}
