package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdCropOrgMappingDO;
import com.shuidihuzhu.cf.dao.qywx.BdCropOrgMappingDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.BdCropOrgMappingService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 主体departmentId映射(BdCropOrgMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-28 11:33:18
 */
@Service
public class BdCropOrgMappingServiceImpl implements BdCropOrgMappingService {
   
    @Resource
    private BdCropOrgMappingDao bdCropOrgMappingDao;

    @Override
    public BdCropOrgMappingDO queryById(long id) {
        return bdCropOrgMappingDao.queryById(id);
    }
    

    @Override
    public int insert(BdCropOrgMappingDO bdCropOrgMapping) {
        return bdCropOrgMappingDao.insert(bdCropOrgMapping);
    }


    @Override
    public BdCropOrgMappingDO queryByOrgId(String orgId, int cropId) {
        return bdCropOrgMappingDao.queryByOrgId(orgId, cropId);
    }

    @Override
    public List<BdCropOrgMappingDO> listCropId(int cropId) {
        return bdCropOrgMappingDao.listCropId(cropId);
    }

    @Override
    public void deleteRelation(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        bdCropOrgMappingDao.deleteByIds(ids);
    }

    @Override
    public String maxDepartmentId(int cropId) {
        return bdCropOrgMappingDao.maxDepartmentId(cropId);
    }
}
