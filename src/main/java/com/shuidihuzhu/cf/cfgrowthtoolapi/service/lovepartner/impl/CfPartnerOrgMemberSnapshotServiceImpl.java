package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveOrgMemberSnapshot;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerOrgMemberSnapshotDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfBdCrmOrgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerOrgMemberSnapshotService;
import com.shuidihuzhu.cf.dao.lovepartner.CfPartnerOrgMemberSnapshotDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */

@Slf4j
@Service
public class CfPartnerOrgMemberSnapshotServiceImpl implements CfPartnerOrgMemberSnapshotService {

    @Autowired
    private CfPartnerOrgMemberSnapshotDao orgMemberSnapshotDao;

    @Override
    public void batchInsertOrgSnapshot(List<CfBdCrmOrgModel> crmOrgModelList, Long cycleId) {

        List<List<CfBdCrmOrgModel>> listList = Lists.partition(crmOrgModelList, 100);
        for (List<CfBdCrmOrgModel> list : listList) {
            List<Long> orgIdInDb = orgMemberSnapshotDao.listCfBdCrmOrgModelByCycleIdWithOrgIdList(cycleId,
                    list.stream().map(CfBdCrmOrgModel::getOrgId).collect(Collectors.toList()))
                    .stream()
                    .map(CfBdCrmOrgModel::getOrgId)
                    .collect(Collectors.toList());
            List<CfBdCrmOrgModel> insertList = list.stream().filter(item -> !orgIdInDb.contains(item.getOrgId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(insertList)) {
                continue;
            }
            orgMemberSnapshotDao.batchInsertOrgSnapshot(insertList, cycleId);
        }
    }

    @Override
    public void batchInsertMemberSnapshot(List<CfPartnerOrgMemberSnapshotDo> orgMemberSnapshotList, Long cycleId) {
        List<List<CfPartnerOrgMemberSnapshotDo>> listList = Lists.partition(orgMemberSnapshotList, 100);

        for (List<CfPartnerOrgMemberSnapshotDo> list : listList){
            List<String> memberUniqueKeyList = orgMemberSnapshotDao.listByCycleIdWithUniqueCodeList(cycleId,
                    list.stream().map(CfPartnerOrgMemberSnapshotDo::getUniqueCode).collect(Collectors.toList()))
                    .stream()
                    .map(CfPartnerOrgMemberSnapshotDo::showUniqueKey)
                    .collect(Collectors.toList());
            List<CfPartnerOrgMemberSnapshotDo> insertList = list.stream().filter(item -> !memberUniqueKeyList.contains(item.showUniqueKey())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(insertList)) {
                continue;
            }
            orgMemberSnapshotDao.batchInsertMemberSnapshot(insertList, cycleId);
        }
    }

    @Override
    public List<CfPartnerOrgMemberSnapshotDo> listByCycleId(int cycleId) {
        return orgMemberSnapshotDao.listByCycleId(cycleId);
    }

    @Override
    public int deleteByCycleId(long cycleId) {
        return orgMemberSnapshotDao.deleteByCycleId(cycleId);
    }
}
