package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.dao.CfMinaSecretInfoDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfMinaSecretInfoBizInterface;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ListUtil;
import com.shuidihuzhu.cf.model.mina.CfMinaSecretInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by Ahrievil on 2017/9/21
 */
@Slf4j
@Service
public class CfMinaSecretInfoBizImpl implements CfMinaSecretInfoBizInterface {

    @Autowired
    private CfMinaSecretInfoDao cfMinaSecretInfoDao;


    @Override
    public List<CfMinaSecretInfo> selectAll() {
        return ListUtil.getList(3000, cfMinaSecretInfoDao::selectAll);
    }
}
