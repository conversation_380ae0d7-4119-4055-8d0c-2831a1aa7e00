package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveCycle;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveManageConfig;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveOrgMemberSnapshot;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmObjectiveIndicatorValueModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmObjectiveStatusBaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmTeamObjectiveDTO;
import com.shuidihuzhu.cf.response.OpResult;
import org.springframework.scheduling.annotation.Async;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * @author: wanghui
 * @create: 2021/2/23 上午10:39
 */
public interface CfBdCrmObjectiveManageConfigService{

    CfBdCrmObjectiveManageConfig getObjectiveManageConfigByCycleIdWithOrgId(Long objectiveCycleId, Long orgId);

    List<CfBdCrmObjectiveManageConfig> listObjectiveManageConfigByCycleIdListWithOrgIdList(List<Long> objectiveCycleIdList, List<Long> orgIdList);

    CfBdCrmObjectiveManageConfig getObjectiveManageConfigByCycleIdWithUniqueCode(Long objectiveCycleId, String uniqueCode);

    List<CfBdCrmObjectiveManageConfig> listObjectiveManageConfigByCycleIdListWithUniqueCode(List<Long> objectiveCycleIdList, String uniqueCode);

    CfBdCrmObjectiveStatusBaseModel getMyObjectiveIndicatorValueStatus(List<Long> orgIdList, String uniqueCode, List<CfBdCrmObjectiveCycle> objectiveCycleList, int roleCode);

    CfBdCrmObjectiveStatusBaseModel getTeamObjectiveIndicatorStatus(List<Long> orgIdList, List<CfBdCrmObjectiveCycle> objectiveCycleList, int roleCode);

    CfBdCrmObjectiveStatusBaseModel getTeamObjectiveIndicatorValueStatus(List<Long> orgIdList, List<CfBdCrmObjectiveCycle> objectiveCycleList, int roleCode);

    void updateConfigJsonWithCommitStatus(List<Long> orgIdList, List<Long> objectiveCycleIdList, String configJson, int commitStatus);

    OpResult insertOrUpdateObjectiveManageConfig(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, Long objectiveCycleId, List<CfBdCrmObjectiveIndicatorValueModel> objectiveIndicatorValueModelList);

    void batchInsertObjectiveManageConfig(List<CfBdCrmObjectiveManageConfig> cfBdCrmObjectiveManageConfigList);

    void insertOrUpdateObjectiveManageConfig(List<CfBdCrmObjectiveManageConfig> manageConfigList, CfBdCrmObjectiveCycle objectiveCycle);

    List<Long> listObjectiveCycleIdByOrgIdListWithObjectiveType(List<Long> subOrgIdList, Integer objectiveType);

    List<CfBdCrmObjectiveManageConfig> listObjectiveManageConfigByOrgIdList(List<Long> subOrgIdList, Integer objectiveType, List<Long> objectiveCycleIdList);

    long countObjectiveManageConfigByUniqueCode(String uniqueCode, Integer objectiveType);

    List<CfBdCrmObjectiveManageConfig> listObjectiveManageConfigByUniqueCode(String uniqueCode, Integer objectiveType, Integer pageSize);

    int updateConfigJsonWithObjectiveIndicatorValueStatus(Long objectiveCycleId, String uniqueCode, List<CfBdCrmObjectiveIndicatorValueModel> objectiveIndicatorValueModelList);

    int updateConfigJsonWithObjectiveIndicatorValueStatus(Long objectiveCycleId, Long orgId, List<CfBdCrmObjectiveIndicatorValueModel> objectiveIndicatorValueModelList);

    List<CfBdCrmObjectiveManageConfig> listObjectiveManageConfigByCycleIdWithUniqueCodeList(Long objectiveCycleId, Collection<String> uniqueCodeList);

    int batchUpdateObjectiveIndicatorValueStatus(List<CfBdCrmObjectiveManageConfig> objectiveManageConfigList);

    List<CfBdCrmObjectiveManageConfig> listByCycleIdWithOrgIdList(Long objectiveCycleId, List<Long> orgIdList);

    List<CfBdCrmObjectiveManageConfig> listOrgObjectiveManageConfigByOrgIdListWithObjectiveType(List<Long> orgIdList, Integer objectiveType);

    void updateObjectiveIndicatorValueStatus(Long objectiveCycleId, List<Long> orgIdList);

    List<Long> listUnCommitOrgIdByCycleId(Long objectiveCycleId);

    void updateIsDelete(List<Long> objectiveCycleIdList, String uniqueCode,Long orgId, int isDelete);

    void updateIsDelete(List<Long> orgIdList,  int isDelete);

    List<CfBdCrmObjectiveManageConfig> listByCycleIdWithUniqueCodeList(Long objectiveCycleId, List<String> uniqueCodeList);

    void updateOrgNameWithCycleIdList(List<Long> objectiveCycleIdList, String newOrgName, long orgId);

    void coverConfigJsonForTopOrg(long cycleId, long orgId);
}
