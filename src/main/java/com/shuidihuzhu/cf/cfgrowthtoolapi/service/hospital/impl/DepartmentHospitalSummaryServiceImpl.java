package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfOperatingRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.GdMapHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.DepartmentHospitalSummaryDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalAreaBuildingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.department.DepartmentModifyModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.department.DepartmentPriorityExcelModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.department.DepartmentPriorityModifyModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.department.AdminDepartmentManagerParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICrmNewHospitalService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.DepartmentHospitalSummaryService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalAreaBuildingService;
import com.shuidihuzhu.cf.dao.bdcrm.CrmNewHospitalDao;
import com.shuidihuzhu.cf.dao.hospital.*;
import com.shuidihuzhu.cf.dao.record.HospitalSubmitRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 科室-医院信息(DepartmentHospitalSummary)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-07 14:47:43
 */
@Slf4j
@Service("departmentHospitalSummaryService")
public class DepartmentHospitalSummaryServiceImpl implements DepartmentHospitalSummaryService {
   
    @Resource
    private DepartmentHospitalSummaryDao departmentHospitalSummaryDao;

    @Resource
    private HospitalAreaBuildingDao hospitalAreaBuildingDao;

    @Resource
    private HospitalBuildingDepartmentDao hospitalBuildingDepartmentDao;

    @Resource
    private CrmNewHospitalDao newHospitalDao;

    @Autowired
    private ICrmNewHospitalService crmNewHospitalService;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Autowired
    private HospitalAreaBuildingService buildingService;

    @Autowired
    private IOperateLogService operateLogService;

    @Override
    public DepartmentHospitalSummaryDO queryById(long id) {
        return departmentHospitalSummaryDao.queryById(id);
    }

    @Override
    public DepartmentHospitalSummaryDO queryByHospitalCode(String hospitalCode) {
        if (StringUtils.isBlank(hospitalCode)) {
            return new DepartmentHospitalSummaryDO();
        }
        return departmentHospitalSummaryDao.queryByHospitalCode(hospitalCode);
    }

    @Override
    public List<DepartmentHospitalSummaryDO> listByHospitalCode(List<String> vhospitalCodes) {
        if (CollectionUtils.isEmpty(vhospitalCodes)) {
            return Lists.newArrayList();
        }
        return departmentHospitalSummaryDao.listByHospitalCode(vhospitalCodes);
    }


    @Override
    public boolean deleteById(long id) {
        return departmentHospitalSummaryDao.deleteById(id) > 0;
    }

    @Override
    public int countByAdminDepartmentManagerParam(AdminDepartmentManagerParam managerParam) {
        return Optional.ofNullable(departmentHospitalSummaryDao.countByAdminDepartmentManagerParam(managerParam)).orElse(0);
    }

    @Override
    public List<DepartmentHospitalSummaryDO> pageByAdminDepartmentManagerParam(AdminDepartmentManagerParam managerParam) {
        return departmentHospitalSummaryDao.pageByAdminDepartmentManagerParam(managerParam);
    }

    @Override
    public void confirmDepartment(long id, int confirmStatus, String mark, AdminUserAccountModel validUserAccountById) {
        if (Objects.equals(confirmStatus, DepartmentHospitalSummaryDO.DepartmentConfirmStatusEnum.handled.getCode())) {
            mark = "";
        }
        departmentHospitalSummaryDao.updateByConfirm(id, new Date(), confirmStatus, mark);
        DepartmentHospitalSummaryDO departmentHospitalSummaryDO = departmentHospitalSummaryDao.queryById(id);

        if (confirmStatus == DepartmentHospitalSummaryDO.DepartmentConfirmStatusEnum.handled.getCode()) {
            List<HospitalAreaBuildingDO> buildingDOList = buildingService.listByHospitalCode(Lists.newArrayList(departmentHospitalSummaryDO.getVhospitalCode()));
            for (HospitalAreaBuildingDO buildingDO : buildingDOList) {
                long maxLogId = buildingDO.getMaxChangeId();
                long maxIdOpt = operateLogService.listOptLogGreaterId(String.valueOf(buildingDO.getId()), Lists.newArrayList(OperateTypeEnum.SEA_MANAGER_EDIT_DEPARTMENT, OperateTypeEnum.SEA_MANAGER_ADD_DEPARTMENT, OperateTypeEnum.EDIT_DEPARTMENT, OperateTypeEnum.ADD_DEPARTMENT), maxLogId)
                        .stream()
                        .filter(item -> StringUtils.isNumeric(item.getOperateKey()))
                        .map(CfOperatingRecordDO::getId)
                        .max(Long::compareTo)
                        .orElse(0L);
                //更新MaxChangeId
                buildingService.updateMaxChangeId(buildingDO.getId(), maxIdOpt);
            }
            customEventPublisher.publish(new OperateLogEvent(this, departmentHospitalSummaryDO.getVhospitalCode(), OperateTypeEnum.SEA_MANAGER_MODIFY_STATUS.getDesc(),
                    OperateTypeEnum.SEA_MANAGER_MODIFY_STATUS, "操作科室校正状态为完成校正", validUserAccountById.getId(), validUserAccountById.getName()));
        }
        if (confirmStatus == DepartmentHospitalSummaryDO.DepartmentConfirmStatusEnum.later_handle.getCode()) {
            String operateContent = "操作科室校正状态为稍后处理";
            if (StringUtils.isNotBlank(mark)) {
                operateContent = operateContent + ",原因" + mark;
            }
            customEventPublisher.publish(new OperateLogEvent(this, departmentHospitalSummaryDO.getVhospitalCode(), OperateTypeEnum.SEA_MANAGER_MODIFY_STATUS.getDesc(),
                    OperateTypeEnum.SEA_MANAGER_MODIFY_STATUS, operateContent, validUserAccountById.getId(), validUserAccountById.getName()));
        }
    }

    //维护信息信息
    @Override
    public void maintainDepartmentHospital(DepartmentModifyModel departmentModifyModel) {
        String vhospitalCode = departmentModifyModel.getVhospitalCode();
        if (StringUtils.isBlank(vhospitalCode)) {
            return;
        }
        DepartmentHospitalSummaryDO hospitalSummaryDO = departmentHospitalSummaryDao.getByVhosptialCode(vhospitalCode);
        //查找有多少个科室
        int departmentNum = Optional.ofNullable(hospitalBuildingDepartmentDao.countByHospitalCode(vhospitalCode))
                .orElse(0);
        //查找有多少个楼宇
        List<HospitalAreaBuildingDO> hospitalAreaBuildingDOS = hospitalAreaBuildingDao.listByHospitalCode(Lists.newArrayList(vhospitalCode));
        int buildNum = hospitalAreaBuildingDOS.size();
        if (hospitalSummaryDO == null && CollectionUtils.isNotEmpty(hospitalAreaBuildingDOS)) {
            HospitalAreaBuildingDO hospitalAreaBuildingDO = hospitalAreaBuildingDOS.get(0);
            hospitalSummaryDO = new DepartmentHospitalSummaryDO()
                    .setVhospitalCode(vhospitalCode)
                    .setHospitalName(hospitalAreaBuildingDO.getHospitalName())
                    .setHospitalCity(hospitalAreaBuildingDO.getHospitalCity());
            List<GdMapHospitalDO> hospitalDOList = crmNewHospitalService.getGdMapHospitalDOByVHospitalcodes(Sets.newHashSet(vhospitalCode));
            //如果找不到未删除的标准医院
            if (CollectionUtils.isEmpty(hospitalDOList)) {
                hospitalDOList = newHospitalDao.getAllGdMapHospitalDOByVHospitalcode(Sets.newHashSet(vhospitalCode));
            }
            if (CollectionUtils.isNotEmpty(hospitalDOList)) {
                GdMapHospitalDO gdMapHospitalDO = hospitalDOList.get(0);
                if (gdMapHospitalDO != null) {
                    hospitalSummaryDO.setHospitalProvince(gdMapHospitalDO.getGdPname());
                }

            }
        }
        if (hospitalSummaryDO == null) {
            return;
        }
        hospitalSummaryDO.setConfirmStatus(DepartmentHospitalSummaryDO.DepartmentConfirmStatusEnum.un_handle.getCode());
        hospitalSummaryDO.setDepartmentNum(departmentNum);
        hospitalSummaryDO.setBuildingNum(buildNum);
        HospitalSubmitRecord.OperatorEnum operatorEnum = departmentModifyModel.getOperatorEnum();
        if (Objects.equals(operatorEnum, HospitalSubmitRecord.OperatorEnum.xianxia_1v1)) {
            hospitalSummaryDO.setCommitTime(new Date());
        }
        //更新还是新增
        if (hospitalSummaryDO.getId() > 0) {
            departmentHospitalSummaryDao.updateBaseInfo(hospitalSummaryDO);
        } else {
            departmentHospitalSummaryDao.insert(hospitalSummaryDO);
        }

    }

    @Override
    public void modifyPriority(List<DepartmentPriorityModifyModel> priorityModifyModels, AdminUserAccountModel validUserAccountById) {
        for (DepartmentPriorityModifyModel priorityModifyModel : priorityModifyModels) {
            DepartmentHospitalSummaryDO.DepartmentPriorityEnum priorityEnum = DepartmentHospitalSummaryDO.DepartmentPriorityEnum.parse(priorityModifyModel.getPriority());
            if (priorityEnum == null) {
                continue;
            }
            departmentHospitalSummaryDao.updateByPriority(priorityModifyModel.getDepartmentId(), priorityModifyModel.getPriority());
            DepartmentHospitalSummaryDO departmentHospitalSummaryDO = departmentHospitalSummaryDao.queryById(priorityModifyModel.getDepartmentId());
            customEventPublisher.publish(new OperateLogEvent(this, departmentHospitalSummaryDO.getVhospitalCode(), OperateTypeEnum.SEA_MANAGER_MODIFY_PRIORITY.getDesc(),
                    OperateTypeEnum.SEA_MANAGER_MODIFY_PRIORITY, "修改科室优先级为" + priorityEnum.getDesc(), validUserAccountById.getId(), validUserAccountById.getName()));
        }
    }

    @Override
    public String modifyPriorityByExcel(List<DepartmentPriorityExcelModel> priorityList, AdminUserAccountModel validUserAccountById) {
        StringBuilder result = new StringBuilder();
        for (DepartmentPriorityExcelModel departmentPriorityExcelModel : priorityList) {
            List<DepartmentHospitalSummaryDO> byHospitalName = departmentHospitalSummaryDao.getByHospitalName(departmentPriorityExcelModel.getHospitalName());
            if (CollectionUtils.isEmpty(byHospitalName)) {
                result.append(departmentPriorityExcelModel.getHospitalName()).append("不存在;");
                continue;
            }
            if (byHospitalName.size() > 1) {
                continue;
            }
            DepartmentHospitalSummaryDO departmentHospitalSummaryDO = byHospitalName.get(0);
            DepartmentHospitalSummaryDO.DepartmentPriorityEnum priorityEnum = DepartmentHospitalSummaryDO.DepartmentPriorityEnum.parseDesc(departmentPriorityExcelModel.getPriority());
            if (priorityEnum == null) {
                result.append(departmentPriorityExcelModel.getHospitalName()).append("下的").append(departmentPriorityExcelModel.getPriority()).append("优先级设置错误;");
                continue;
            }
            departmentHospitalSummaryDao.updateByPriority(departmentHospitalSummaryDO.getId(), priorityEnum.getCode());
            customEventPublisher.publish(new OperateLogEvent(this, departmentHospitalSummaryDO.getVhospitalCode(), OperateTypeEnum.SEA_MANAGER_MODIFY_PRIORITY.getDesc(),
                    OperateTypeEnum.SEA_MANAGER_MODIFY_PRIORITY, "修改科室优先级为" + priorityEnum.getDesc(), validUserAccountById.getId(), validUserAccountById.getName()));
        }
        return result.toString();
    }

    @Override
    public List<DepartmentHospitalSummaryDO> getByHospitalProvinceCity(String hospitalProvince, String hospitalCity) {
        return departmentHospitalSummaryDao.getByHospitalProvinceCity(hospitalProvince, hospitalCity);
    }
}
