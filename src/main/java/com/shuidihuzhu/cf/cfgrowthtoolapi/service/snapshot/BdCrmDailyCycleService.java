package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdCrmDailyCycleDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.PageSearchModel;

import java.util.List;

/**
 * 日目标周期信息(BdCrmDailyCycle)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-02 14:59:41
 */
public interface BdCrmDailyCycleService {

    BdCrmDailyCycleDO queryById(long id);

    int batchInsert(List<BdCrmDailyCycleDO> bdCrmDailyCycleDOS);

    boolean deleteById(long id);

    void updateCreateSnapshot(String dateKey);

    List<BdCrmDailyCycleDO> listByDateKey(List<String> dateKeys);

    int countAllDailyCycle();

    List<BdCrmDailyCycleDO> listAllDailyCycle(PageSearchModel pageSearchModel);

}
