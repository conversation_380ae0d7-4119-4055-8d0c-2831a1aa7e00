package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.PayClientRpcDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiDeviceAdConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiOperateRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiOrderDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfJiekongDeviceDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfFangbiandaiOfficialAccountDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfFangbiandaiUserFollowRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.FangbiandaiDeviceParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.FangbiandaiOrderModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.FangbiandaiOrderParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfFangbiandaiService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolPayUtil;
import com.shuidihuzhu.cf.dao.CfFangbiandaiDeviceAdConfigDao;
import com.shuidihuzhu.cf.dao.CfFangbiandaiOperateRecordDao;
import com.shuidihuzhu.cf.dao.CfFangbiandaiOrderDao;
import com.shuidihuzhu.cf.dao.CfJiekongDeviceDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfFangbiandaiOfficialAccountDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfFangbiandaiUserFollowRecordDao;
import com.shuidihuzhu.client.baseservice.pay.model.PayResultV2;
import com.shuidihuzhu.client.baseservice.pay.model.PayRpcResponse;
import com.shuidihuzhu.client.baseservice.pay.model.v3.PayInfoParamV3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2020-05-30 16:45
 */
@Service
@Slf4j
public class CfFangbiandaiService implements ICfFangbiandaiService {

    @Autowired
    private CfJiekongDeviceDao cfJiekongDeviceDao;
    @Autowired
    private CfFangbiandaiOrderDao cfFangbiandaiOrderDao;
    @Autowired
    private PayClientRpcDelegate payClientRpcDelegate;
    @Autowired
    private GrowthtoolPayUtil growthtoolPayUtil;
    @Autowired
    private CfFangbiandaiOperateRecordDao cfFangbiandaiOperateRecordDao;
    @Autowired
    private CfFangbiandaiDeviceAdConfigDao cfFangbiandaiDeviceAdConfigDao;
    @Autowired
    private CfFangbiandaiOfficialAccountDao cfFangbiandaiOfficialAccountDao;
    @Autowired
    private CfFangbiandaiUserFollowRecordDao cfFangbiandaiUserFollowRecordDao;

    @Override
    public CfJiekongDeviceDO getCfJiekongDevice(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return null;
        }
        return cfJiekongDeviceDao.selectByUniqueCode(uniqueCode);
    }

    @Override
    public CfJiekongDeviceDO getCfJiekongDevice(long id) {
        return cfJiekongDeviceDao.selectById(id);
    }

    @Override
    public int saveDevice(CfJiekongDeviceDO cfJiekongDeviceDO) {
        return cfJiekongDeviceDao.insert(cfJiekongDeviceDO);
    }

    @Override
    public int updateFangbiandaiDevice(long id, String qrCode) {
        return cfJiekongDeviceDao.updateQrCode(id, qrCode);
    }

    @Override
    public List<CfFangbiandaiOrderDO> getCfFangbiandaiOrderByUnionId(String unionId, Date startTime) {
        if (StringUtils.isBlank(unionId)) {
            return Lists.newArrayList();
        }
        return cfFangbiandaiOrderDao.selectByUnionId(unionId, startTime);
    }

    @Override
    public int saveOrder(CfFangbiandaiOrderDO cfFangbiandaiOrderDO) {
        return cfFangbiandaiOrderDao.insertOrder(cfFangbiandaiOrderDO);
    }

    @Override
    public CfFangbiandaiOrderDO getCfFangbiandaiOrderByOrderId(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return null;
        }
        return cfFangbiandaiOrderDao.selectByOrderId(orderId);
    }

    @Override
    public void updateOrder(CfFangbiandaiOrderDO cfFangbiandaiOrderDO) {
        cfFangbiandaiOrderDao.updateOrder(cfFangbiandaiOrderDO);
    }

    @Override
    public int decrStockNum(String deviceId, String deviceSupplier) {
        return cfJiekongDeviceDao.decrStockNum(deviceId,deviceSupplier);
    }

    @Override
    public int addStockNum(long id, Integer stockNum) {
        return cfJiekongDeviceDao.addStockNum(id, stockNum);
    }

    @Override
    public CfJiekongDeviceDO getByDeviceId(String deviceId,String deviceSupplier) {
        return cfJiekongDeviceDao.getByDeviceId(deviceId,deviceSupplier);
    }
    @Override
    public CfJiekongDeviceDO getByDeviceIdWithMachineId(String deviceId,String machineId) {
        return cfJiekongDeviceDao.getByDeviceIdWithMachineId(deviceId,machineId);
    }


    @Override
    public void updateRealPayStatus(String orderId, int payStatus, String transactionId, int jiekongStatus, int status,String payOrderId) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }
        cfFangbiandaiOrderDao.updateRealPayStatus(orderId, payStatus, transactionId, jiekongStatus, status,payOrderId);
    }

    @Override
    public void updateRefundStatus(String orderId, int refundStatus, int payStatus,String refundOrderId) {
        if (StringUtils.isBlank(orderId)) {
            return;
        }
        cfFangbiandaiOrderDao.updateRedfundStatus(orderId, refundStatus, payStatus,refundOrderId);
    }

    @Override
    public List<CfJiekongDeviceDO> getFangbiandaiDevice(String machineId,  int pageNo, int pageSize) {
        int offset = (pageNo-1)*pageSize;
        return cfJiekongDeviceDao.getFangbiandaiDevice(machineId,offset, pageSize);
    }
    @Override
    public List<CfJiekongDeviceDO> getFangbiandaiDeviceByMachineId(String machineId) {
        return cfJiekongDeviceDao.getFangbiandaiDeviceByMachineId(machineId);
    }

    @Override
    public void offlineDevice(long id) {
        cfJiekongDeviceDao.offlineDevice(id);
    }

    @Override
    public int modDevice(FangbiandaiDeviceParam fangbiandaiDeviceParam) {
        return cfJiekongDeviceDao.modDevice(fangbiandaiDeviceParam);
    }

    @Override
    public CommonResultModel<CfFangbiandaiOrderDO> getOrderList(FangbiandaiOrderParam fangbiandaiOrderParam) {
        CommonResultModel<CfFangbiandaiOrderDO> commonResultModel = new CommonResultModel();
        Boolean drawSuccess = null;
        Integer obtainWays = null;
        Boolean refundStatus = null;
        String deviceId = null;
        int offset = (fangbiandaiOrderParam.getPageNo()-1)*fangbiandaiOrderParam.getPageSize();
        if (fangbiandaiOrderParam.getStatus()!=null && fangbiandaiOrderParam.getStatus()!=-1){
            FangbiandaiOrderModel.StatusEnum statusEnum = FangbiandaiOrderModel.StatusEnum.parse(fangbiandaiOrderParam.getStatus());
            drawSuccess = statusEnum==null?null:statusEnum.getDrawSuccess();
        }
        if (fangbiandaiOrderParam.getWay()!=null && fangbiandaiOrderParam.getWay()!=-1){
            FangbiandaiOrderModel.WayEnum wayEnum = FangbiandaiOrderModel.WayEnum.parse(fangbiandaiOrderParam.getWay());
            obtainWays = wayEnum==null?null:wayEnum.getObtainWays();
            refundStatus = wayEnum==null?null:wayEnum.getRefundStatus();
        }
        if (StringUtils.isNotBlank(fangbiandaiOrderParam.getMachineId())){
            List<CfJiekongDeviceDO> fangbiandaiDevice = cfJiekongDeviceDao.getFangbiandaiDeviceByMachineId(fangbiandaiOrderParam.getMachineId());
            deviceId = CollectionUtils.isEmpty(fangbiandaiDevice)?null:fangbiandaiDevice.get(0).getDeviceId();
        }
        // 说明 way 是没有选择选项
        if (obtainWays==null){
            commonResultModel.setModelList(cfFangbiandaiOrderDao.getOrderList(fangbiandaiOrderParam.getUnionId(),
                    deviceId,drawSuccess,offset,fangbiandaiOrderParam.getPageSize()));
            commonResultModel.setTotal(cfFangbiandaiOrderDao.countGetOrderList(fangbiandaiOrderParam.getUnionId(),
                    deviceId,drawSuccess));
            return commonResultModel;
        }
        // 说明way=1 此时 obtain_ways = 0
        if (obtainWays==0){
            commonResultModel.setModelList(cfFangbiandaiOrderDao.getOrderListByObtainWays(fangbiandaiOrderParam.getUnionId(),
                    deviceId,drawSuccess,obtainWays,offset,fangbiandaiOrderParam.getPageSize()));
            commonResultModel.setTotal(cfFangbiandaiOrderDao.countGetOrderListByObtainWays(fangbiandaiOrderParam.getUnionId(),
                    deviceId,drawSuccess,obtainWays));
            return commonResultModel;
        }
        // 说明Way=2 此时 obtain_ways = 1  and jiekong_status=100
        if (refundStatus==null){
            commonResultModel.setModelList(cfFangbiandaiOrderDao.getOrderListByDrawMethod(fangbiandaiOrderParam.getUnionId(),
                    deviceId,true,obtainWays,null,offset,fangbiandaiOrderParam.getPageSize()));
            commonResultModel.setTotal(cfFangbiandaiOrderDao.countGetOrderListByDrawMethod(fangbiandaiOrderParam.getUnionId(),
                    deviceId,true,obtainWays,null));
            return commonResultModel;
        }
        // 说明Way=3 4  此时再通过refundStatus 判断是否退款成功
        commonResultModel.setModelList(cfFangbiandaiOrderDao.getOrderListByDrawMethod(fangbiandaiOrderParam.getUnionId(),
                deviceId,drawSuccess,obtainWays,refundStatus,offset,fangbiandaiOrderParam.getPageSize()));
        commonResultModel.setTotal(cfFangbiandaiOrderDao.countGetOrderListByDrawMethod(fangbiandaiOrderParam.getUnionId(),
                deviceId,drawSuccess,obtainWays,refundStatus));
        return commonResultModel;
    }


    @Override
    public int saveCfFangbiandaiOperateRecord(CfFangbiandaiOperateRecordDO cfFangbiandaiOperateRecordDO){
        return cfFangbiandaiOperateRecordDao.saveCfFangbiandaiOperateRecord(cfFangbiandaiOperateRecordDO);
    }

    @Override
    public List<CfFangbiandaiOperateRecordDO> getCfFangbiandaiOperateRecordList(int operateType, String machineId) {
        return cfFangbiandaiOperateRecordDao.getCfFangbiandaiOperateRecordList(operateType,machineId);
    }

    @Override
    public List<String> getAllDeviceMachineIds() {
        return cfJiekongDeviceDao.getAllDeviceMachineIds();
    }

    @Override
    public long getFangbiandaiDeviceCount(String machineId) {
        return cfJiekongDeviceDao.getFangbiandaiDeviceCount(machineId);
    }

    @Override
    public List<CfFangbiandaiOrderDO> getCurrentDayNoStatusCfFangbiandaiOrder(String currentDay) {
        return cfFangbiandaiOrderDao.getCurrentDayNoStatusCfFangbiandaiOrder(currentDay);
    }

    @Override
    public int updateDeviceSupplier(List<Integer> ids, String deviceSupplier){
        return cfJiekongDeviceDao.updateDeviceSupplier(ids, deviceSupplier);
    }

    @Override
    public int updateDeviceId(String deviceId, long id){
        return cfJiekongDeviceDao.updateDeviceId(deviceId, id);
    }

    @Override
    public List<CfFangbiandaiDeviceAdConfigDO> getCfFangbiandaiDeviceAdConfigDO(String deviceId, String deviceSupplier){
        return cfFangbiandaiDeviceAdConfigDao.getCfFangbiandaiDeviceAdConfigDO(deviceId, deviceSupplier);
    }
    @Override
    public int insertCfFangbiandaiDeviceAdConfigDO(CfFangbiandaiDeviceAdConfigDO cfFangbiandaiDeviceAdConfigDO){
        return cfFangbiandaiDeviceAdConfigDao.insert(cfFangbiandaiDeviceAdConfigDO);
    }
    @Override
    public List<CfFangbiandaiOfficialAccountDO> listAllOfficialAccount(){
        return cfFangbiandaiOfficialAccountDao.listAll().stream().sorted(Comparator.comparing(CfFangbiandaiOfficialAccountDO::getSortNum)).collect(Collectors.toList());
    }

    @Override
    public CfFangbiandaiOfficialAccountDO selectByThirdType(String thirdType){
        return cfFangbiandaiOfficialAccountDao.selectByThirdType(thirdType);
    }

    @Override
    public void saveUserFollowRecord(CfFangbiandaiUserFollowRecordDO record){
        CfFangbiandaiUserFollowRecordDO inDb = cfFangbiandaiUserFollowRecordDao.getFollowRecordByOpenIdWithThirdType(record.getOpenId(), record.getThirdType());
        if (inDb != null) {
            record.setId(inDb.getId());
            return;
        }
        cfFangbiandaiUserFollowRecordDao.insert(record);
    }
    @Override
    public List<String> listFollowThirdTypeByUnionId(@Param("unionId") String unionId){
        return cfFangbiandaiUserFollowRecordDao.listFollowThirdTypeByUnionId(unionId);
    }
    @Override
    public void updateFollowRecordOrderIdById(@Param("id") Long id, @Param("orderId") String orderId){
        cfFangbiandaiUserFollowRecordDao.updateOrderIdById(id, orderId);
    }

    @Override
    public CfFangbiandaiUserFollowRecordDO getFollowRecordByOpenIdWithThirdType(String openId, String thirdType) {
        return cfFangbiandaiUserFollowRecordDao.getFollowRecordByOpenIdWithThirdType(openId, thirdType);
    }
    @Override
    public Integer countSuccessOrder(String unionId,
                                  String startTime){
        if (StringUtils.isBlank(unionId) || StringUtils.isBlank(startTime)) return 0;
        return cfFangbiandaiOrderDao.countSuccessOrder(unionId, startTime);
    }
}
