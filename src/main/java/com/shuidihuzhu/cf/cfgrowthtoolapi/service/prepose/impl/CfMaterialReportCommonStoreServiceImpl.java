package com.shuidihuzhu.cf.cfgrowthtoolapi.service.prepose.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.prepose.CfMaterialReportCommonStoreDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.prepose.CfMaterialReportCommonStoreService;
import com.shuidihuzhu.cf.dao.prepose.CfMaterialReportCommonStoreDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 材审代录入信息(CfMaterialReportCommonStore)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-07 11:02:37
 */
@Service("cfMaterialReportCommonStoreService")
public class CfMaterialReportCommonStoreServiceImpl implements CfMaterialReportCommonStoreService {
   
    @Resource
    private CfMaterialReportCommonStoreDao cfMaterialReportCommonStoreDao;

    @Override
    public List<CfMaterialReportCommonStoreDO> queryByInfoId(String infoId) {
        if (StringUtils.isBlank(infoId)) {
            return Lists.newArrayList();
        }
        return cfMaterialReportCommonStoreDao.queryByInfoId(infoId);
    }
    

    @Override
    public int insert(CfMaterialReportCommonStoreDO cfMaterialReportCommonStore) {
        return cfMaterialReportCommonStoreDao.insert(cfMaterialReportCommonStore);
    }


    @Override
    public boolean deleteById(long id) {
        return cfMaterialReportCommonStoreDao.deleteById(id) > 0;
    }
}
