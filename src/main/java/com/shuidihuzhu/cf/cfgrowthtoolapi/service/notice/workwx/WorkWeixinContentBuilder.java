package com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice.workwx;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.INameValuePair;
import com.shuidihuzhu.common.util.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-15
 */
public class WorkWeixinContentBuilder {

    private String subject;
    private List<INameValuePair> payloadList = Lists.newArrayList();

    private static final String SEPARATOR_PAYLOAD = "：";

    private static final String LINE_BREAK = "\n";

    public static WorkWeixinContentBuilder create(){
        return new WorkWeixinContentBuilder();
    }

    private WorkWeixinContentBuilder() {
    }

    public WorkWeixinContentBuilder subject(String subject) {
        this.subject = subject;
        return this;
    }

    public WorkWeixinContentBuilder payloadJson(String key, Object value) {
        String jsonString = JsonUtil.getJsonString(value);
        payloadList.add(new NameValuePair(key, jsonString));
        return this;
    }

    public WorkWeixinContentBuilder payload(String key, Object value) {
        if (value == null) {
            return this;
        }
        String strValue = String.valueOf(value);
        if (StringUtils.isBlank(strValue)) {
            return this;
        }
        payloadList.add(new NameValuePair(key, strValue));
        return this;
    }


    /**
     *
     * eg: "[申请取消订单] | [orderId]: [" + orderId + "]";
     * @return msg content
     */
    public String build() {
        // check 合法
        if (StringUtils.isEmpty(subject)) {
            throw new IllegalArgumentException("必须要有主题 subject");
        }
        // 组装content
        StringBuilder sb = new StringBuilder();

        sb.append(wrapItem(subject));
        sb.append(LINE_BREAK);

        for (INameValuePair pair : payloadList) {
            buildPayload(sb, pair);
        }
        return sb.toString();
    }
    /**
     *
     * eg: "[申请取消订单] | [orderId]: [" + orderId + "]";
     * @return msg content
     */
    public String buildWithNoSubject() {
        // 组装content
        StringBuilder sb = new StringBuilder();
        for (INameValuePair pair : payloadList) {
            buildPayload(sb, pair);
        }
        return sb.toString();
    }

    private void buildPayload(StringBuilder sb, INameValuePair pair) {
        sb.append(wrapItem(pair.getName()));
        sb.append(SEPARATOR_PAYLOAD);
        sb.append(wrapItem(pair.getValue()));
        sb.append(LINE_BREAK);
    }

    private static String wrapItem(String item) {
        return item;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    static class NameValuePair implements INameValuePair{

        private String name;

        private String value;
    }
}
