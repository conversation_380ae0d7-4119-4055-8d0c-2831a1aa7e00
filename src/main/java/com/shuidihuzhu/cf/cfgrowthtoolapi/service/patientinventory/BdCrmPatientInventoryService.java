package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.*;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/6  15:22
 */
public interface BdCrmPatientInventoryService {

    Response<Boolean> entrance(CrowdfundingVolunteer cfVolunteer);

    Response<List<PatientInventoryHospitalVo>> bedInventory(CrowdfundingVolunteer cfVolunteer);

    Response<List<PatientInventoryBedDetailVo>> bedInventoryDetail(String departmentIds, String startDateline, String endDateline, String patientName,
                                                                   String firstIntentions, String secondIntentions, String labels, String startNextFollowUpTime,
                                                                   String endNextFollowUpTime, Boolean needToFollowUpTodayLabel, Boolean payCloseAttentionToLabel,
                                                                   Boolean bedNullToLabel, Boolean bedStatusToLabel, int departmentType, CrowdfundingVolunteer cfVolunteer,String initialFirstIntentions,String initialSecondIntentions );

    Response<Void> bedAdd(PatientInventoryBedParam param, CrowdfundingVolunteer cfVolunteer, List<Long> orgIdList);

    Response<List<DepartmentsLabelVo>> getDepartmentsLabel(int departmentsId);

    Response<List<DepartmentsOfTheSameNameVo>> ofTheSameNameList(int departmentsId, String patientName);

    Response<Void> leaveHospital(long patientInfoId, int departmentType, CrowdfundingVolunteer cfVolunteer);

    Response<PatientInventoryBedVo> getBedInfo(long bedId, long patientInfoId, long bedPatientRecordId,int departmentType);

    Response<List<PatientInventoryPatientAllRecordVo>> allRecord(long patientInfoId);

    Response<Void> delRecord(long bedPatientRecordId);

    Response<Map<String, Object>> historyRecord(String vhospitalCode, String departmentsIds, String startDateline, String endDateline, String patientName, String firstIntentions,
                                                String secondIntentions, String labels, String startNextFollowUpTime, String endNextFollowUpTime, Integer departmentType, int current, int pageSize);

    void getNeedToFollowUpTodayLabel(PatientInventoryBedDetailVo.PatientInventoryPatientModel patientInventoryPatientModel, PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo,
                                     Map<Long, List<PatientInventoryBedPatientRecordDo>> patientInventoryBedPatientRecordCorrelationIdMap, int volunteerLevel);

    void getPayCloseAttentionToLabel(PatientInventoryBedDetailVo.PatientInventoryPatientModel patientInventoryPatientModel,
                                     PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo,
                                     PatientInventoryPatientInfoDo patientInventoryPatientInfoDo, int volunteerLevel);

    Response<Integer> verifyEmptyBed(long bedId, CrowdfundingVolunteer cfVolunteer);

    Response<List<PatientInventorySimpleBedVo>> listBedByDepartmentsId(int departmentsId);

    void leaveHospitalRecord(PatientInventoryPatientInfoDo patientInventoryPatientInfoDo, int departmentType, CrowdfundingVolunteer cfVolunteer);

    Response<Integer> getDepartmentTypeTab(CrowdfundingVolunteer crowdfundingVolunteer);

    Response<List<Integer>> getJurisdiction(CrowdfundingVolunteer crowdfundingVolunteer);
}
