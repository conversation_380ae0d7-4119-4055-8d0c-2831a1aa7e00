package com.shuidihuzhu.cf.cfgrowthtoolapi.service.greenchannel;

import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.greenchannel.CfGreenChannelApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGreenChannelEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.greenchannel.StepHandlerModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.comparators.FixedOrderComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-10-27 1:59 下午
 **/
@Service
public class GreenChannelRouterImpl implements IGreenChannelRouter {

    @Autowired
    private ApolloService apolloService;


    @Override
    public List<StepHandlerModel> listAllHandler(int type) {
        List<StepHandlerModel> stepHandlerModels = Lists.newArrayList();
        List<CfGreenChannelEnums.StepCodeEnum> stepCodeEnumList = Lists.newArrayList();
        if (type == CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode()) {
            stepCodeEnumList = CfGreenChannelEnums.StepCodeEnum.privateToPub;
        } else if (type == CfGreenChannelEnums.ApplyResourceTypeEnum.DOCTOR_RECOMMEND.getCode()) {
            stepCodeEnumList = CfGreenChannelEnums.StepCodeEnum.medicalCare;
        }
        for (CfGreenChannelEnums.StepCodeEnum stepCodeEnum : stepCodeEnumList) {
            List<String> operatorList = listOperator(type, stepCodeEnum.getCode());
            stepHandlerModels.add(new StepHandlerModel(stepCodeEnum.getCode(), operatorList));
        }
        return stepHandlerModels;
    }

    @Override
    public CfGreenChannelApproveDO getCurrentStepCode(List<CfGreenChannelApproveDO> approveDOList) {
        return getCfGreenChannelApproveDO(approveDOList, item -> CfGreenChannelEnums.current_approve_status.contains(item.getApproveStatus()));
    }



    @Override
    public boolean canBackToLastStep(List<CfGreenChannelApproveDO> approveDOList, int currentStepCode) {
        CfGreenChannelApproveDO cfGreenChannelApproveDO = getCfGreenChannelApproveDO(approveDOList, item -> item.getApproveStatus() == CfGreenChannelEnums.ApproveStatusEnum.wait.getCode());
        return cfGreenChannelApproveDO != null && cfGreenChannelApproveDO.getStepCode() == currentStepCode;
    }



    private CfGreenChannelApproveDO getCfGreenChannelApproveDO(List<CfGreenChannelApproveDO> approveDOList, Predicate<CfGreenChannelApproveDO> predicate) {
        if (CollectionUtils.isEmpty(approveDOList)) {
            return null;
        }
        Ordering<CfGreenChannelApproveDO> ordering = Ordering.natural().onResultOf(CfGreenChannelApproveDO::getStepCode);
        if (ObjectUtils.nullSafeEquals(approveDOList.get(0).getApplyType(), CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode())) {
            FixedOrderComparator<Integer> comparator = new FixedOrderComparator<Integer>(CfGreenChannelEnums.StepCodeEnum.privateToPub.stream().map(CfGreenChannelEnums.StepCodeEnum::getCode).collect(Collectors.toList()));
            comparator.setUnknownObjectBehavior(FixedOrderComparator.UnknownObjectBehavior.AFTER);
            ordering = Ordering
                    .from(comparator)
                    .onResultOf(CfGreenChannelApproveDO::getStepCode);
        }
        return approveDOList
                .stream()
                //改为由expect排序
                .sorted(ordering)
                //找到第一个待审核的
                .filter(predicate)
                .findFirst()
                .orElse(null);
    }

    @Override
    public Integer getNextStepCode(int activityType, int stepCode) {
        CfGreenChannelEnums.StepCodeEnum stepCodeEnum = CfGreenChannelEnums.StepCodeEnum.parseByCode(stepCode);
        if (stepCodeEnum == null) {
            return null;
        }
        List<CfGreenChannelEnums.StepCodeEnum> stepCodeEnumList = Lists.newArrayList();
        if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode()) {
            stepCodeEnumList = CfGreenChannelEnums.StepCodeEnum.privateToPub;
        } else if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.DOCTOR_RECOMMEND.getCode()) {
            stepCodeEnumList = CfGreenChannelEnums.StepCodeEnum.medicalCare;
        }
        if (CollectionUtils.isEmpty(stepCodeEnumList)) {
            return null;
        }
        for (int i = 0; i < stepCodeEnumList.size() - 1; i++) {
            if (stepCodeEnum == stepCodeEnumList.get(i)) {
                return stepCodeEnumList.get(i + 1).getCode();
            }
        }
        return null;
    }


    @Override
    public Integer getLastStepCode(int activityType, int stepCode) {
        CfGreenChannelEnums.StepCodeEnum stepCodeEnum = CfGreenChannelEnums.StepCodeEnum.parseByCode(stepCode);
        if (stepCodeEnum == null) {
            return null;
        }
        List<CfGreenChannelEnums.StepCodeEnum> stepCodeEnumList = Lists.newArrayList();
        if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode()) {
            stepCodeEnumList = CfGreenChannelEnums.StepCodeEnum.privateToPub;
        } else if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.DOCTOR_RECOMMEND.getCode()) {
            stepCodeEnumList = CfGreenChannelEnums.StepCodeEnum.medicalCare;
        }
        if (CollectionUtils.isEmpty(stepCodeEnumList)) {
            return null;
        }
        for (int i = 1; i < stepCodeEnumList.size(); i++) {
            if (stepCodeEnum == stepCodeEnumList.get(i)) {
                return stepCodeEnumList.get(i - 1).getCode();
            }
        }
        return null;
    }

    @Override
    public CfGreenChannelEnums.ApplyStatusEnum getApplyStatusWhenCreate(int activityType, int stepCode) {
        CfGreenChannelEnums.StepCodeEnum stepCodeEnum = CfGreenChannelEnums.StepCodeEnum.parseByCode(stepCode);
        if (stepCodeEnum == null) {
            return null;
        }
        if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode()) {
            switch (stepCodeEnum) {
                case one:
                    return CfGreenChannelEnums.ApplyStatusEnum.FIRST_PROJECT;
                case two:
                    return CfGreenChannelEnums.ApplyStatusEnum.SECOND_RISK;
                case three:
                    return CfGreenChannelEnums.ApplyStatusEnum.THIRD_CONTENT;
                case four:
                    return CfGreenChannelEnums.ApplyStatusEnum.FOURTH_FOUNDATION;
                case five:
                    return CfGreenChannelEnums.ApplyStatusEnum.FIVE_COMSUMER;
                case six:
                    return CfGreenChannelEnums.ApplyStatusEnum.SIX_FOUNDATION;
                case seven:
                    return CfGreenChannelEnums.ApplyStatusEnum.SEVEN_FOUNDATION;
                case end:
                    return CfGreenChannelEnums.ApplyStatusEnum.END_CONTENT;
                default:
                    return null;
            }
        } else if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.DOCTOR_RECOMMEND.getCode()) {
            switch (stepCodeEnum) {
                case one:
                    return CfGreenChannelEnums.ApplyStatusEnum.FIRST_PROJECT_DOCTOR;
                case two:
                    return CfGreenChannelEnums.ApplyStatusEnum.SECOND_RISK_DOCTOR;
                case three:
                    return CfGreenChannelEnums.ApplyStatusEnum.THIRD_CONTENT_DOCTOR;
                case four:
                    return CfGreenChannelEnums.ApplyStatusEnum.FOURTH_PROJECT_DOCTOR;
                case end:
                    return CfGreenChannelEnums.ApplyStatusEnum.END_CONTENT_DOCTOR;
                default:
                    return null;
            }
        }
        return null;
    }


    @Override
    public CfGreenChannelEnums.ApplyStatusEnum getApplyStatusWhenReject(int activityType, int stepCode) {
        CfGreenChannelEnums.StepCodeEnum stepCodeEnum = CfGreenChannelEnums.StepCodeEnum.parseByCode(stepCode);
        if (stepCodeEnum == null) {
            return null;
        }
        if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode()) {
            switch (stepCodeEnum) {
                case one:
                    return CfGreenChannelEnums.ApplyStatusEnum.FIRST_REJECT;
                case two:
                    return CfGreenChannelEnums.ApplyStatusEnum.SECOND_REJECT;
                case three:
                    return CfGreenChannelEnums.ApplyStatusEnum.THIRD_REJECT;
                case four:
                    return CfGreenChannelEnums.ApplyStatusEnum.FOURTH_REJECT;
                case five:
                    return CfGreenChannelEnums.ApplyStatusEnum.FIVE_REJECT;
                case six:
                    return CfGreenChannelEnums.ApplyStatusEnum.SIX_REJECT;
                case seven:
                    return CfGreenChannelEnums.ApplyStatusEnum.SEVEN_REJECT;
                default:
                    return null;
            }
        } else if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.DOCTOR_RECOMMEND.getCode()) {
            switch (stepCodeEnum) {
                case one:
                    return CfGreenChannelEnums.ApplyStatusEnum.FIRST_REJECT_DOCTOR;
                case two:
                    return CfGreenChannelEnums.ApplyStatusEnum.SECOND_REJECT_DOCTOR;
                case three:
                    return CfGreenChannelEnums.ApplyStatusEnum.THIRD_REJECT_DOCTOR;
                case four:
                    return CfGreenChannelEnums.ApplyStatusEnum.FOURTH_REJECT_DOCTOR;
                default:
                    return null;
            }
        }
        return null;
    }

    /**
     *
     * @param activityType
     * @param stepCode: 上一步的code
     * @return
     */
    @Override
    public CfGreenChannelEnums.ApplyStatusEnum getApplyStatusWhenBackToLast(int activityType, int stepCode) {
        return getApplyStatusWhenCreate(activityType, stepCode);
    }

    @Override
    public List<String> listOperator(int activityType, int stepCode) {
        Map<Integer, String> channelPermission = Maps.newHashMap();
        if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode()) {
            channelPermission = apolloService.getPublicGreenChannelPermission();
        }
        if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.DOCTOR_RECOMMEND.getCode()) {
            channelPermission = apolloService.getDoctorRecommendGreenChannelPermission();
        }
        return Splitter.on(',').splitToList(channelPermission.getOrDefault(stepCode, ""));
    }

    @Override
    public String getRobot(int activityType) {
        if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode()) {
            return apolloService.getPublicGreenChannelRobort();
        }
        if (activityType == CfGreenChannelEnums.ApplyResourceTypeEnum.DOCTOR_RECOMMEND.getCode()) {
            return apolloService.getDoctorGreenChannelRobot();
        }
        return null;
    }

}
