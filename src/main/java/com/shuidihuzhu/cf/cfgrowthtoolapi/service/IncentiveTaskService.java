package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.incentive.IncentivePersonTaskDetailDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.incentive.IncentivePersonTaskRelDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.incentive.IncentiveTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.IncentiveTaskEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.incentive.*;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @author: wanghui
 * @create: 2022/1/6 下午2:03
 */
public interface IncentiveTaskService {



    /**
     * 根据任务id获取人员任务关系列表
     */
    List<IncentivePersonTaskRelDO> selectListByTaskId(long taskId);

    IncentiveTaskDO getIncentiveTaskById(Long id);

    List<IncentivePersonTaskDTO>  listTaskOfVolunteer(Set<String> uniqueCodeList, IncentiveTaskEnums.TaskStatusEnum taskStatusEnum, Date startDate, Date endDate);

    long countListIncentiveTaskForSea(IncentiveTaskSearchParam incentiveTaskSearchParam);

    List<IncentiveTaskDO> listIncentiveTaskForSea(IncentiveTaskSearchParam incentiveTaskSearchParam);

    int updateIncentiveTaskUseStatus(Long incentiveTaskId, int useStatus,String userName);

    List<IncentivePersonTaskRelDO> listPersonTaskByTaskIdForSea(Long incentiveTaskId, Integer pageNo, Integer pageSize);

    long countListPersonTaskByTaskIdForSea(Long incentiveTaskId);

    List<IncentivePersonDonateStatistics> listPersonTaskDonateStatistics(List<Long> personTaskRelIdList);

    List<IncentivePersonCaseStatistics> listPersonTaskCaseStatistics(List<Long> personTaskRelIdList, Integer singleCaseDonateLimit);

    List<IncentiveTaskDO> listTaskByCalcAwardEndDate(Date startDate, Date endDate);

    void saveIncentiveTask(IncentiveTaskDTO incentiveTaskDTO);

    void updateIncentiveTask(IncentiveTaskDTO incentiveTaskDTO);

    void savePersonForIncentiveTask(IncentiveTaskDTO incentiveTaskDTO);

    List<IncentivePersonTaskDTO> listTaskByTaskStatusAndLimit(String volunteerUniqueCode, int taskStatus, Date date, Long taskIdMin, int pageSize);

    IncentivePersonTaskRelDO selectByTaskIdAndUniqueCode(long taskId, String volunteerUniqueCode);

    int updateTaskStatusAndReceiveTime(Long id, int taskStatus, Date receiveTime);

    List<IncentivePersonTaskDetailDO> selectByPersonTaskRelIdList(List<Long> personTaskRelIdList);

    List<IncentivePersonTaskDTO> listTaskByUniqueCode(String volunteerUniqueCode);

    void saveOrUpdatePersonTaskDetailOfDonateNum(Date caseCreteTime, Date donatedDate, int donateNum, CfBdCaseInfoDo cfBdCaseInfoDo);
}
