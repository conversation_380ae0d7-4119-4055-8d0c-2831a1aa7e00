package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiBdCommissionOperateDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfBdKpiEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiBdCommissionOperateService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiBdCommissionOperateDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
@Service
@Slf4j
public class CfKpiBdCommissionOperateServiceImpl implements ICfKpiBdCommissionOperateService {

    @Autowired
    private CfKpiBdCommissionOperateDao cfKpiBdCommissionOperateDao;

    @Override
    public void addBatch(List<CfKpiBdCommissionOperateDO> kpiBdCommissionOperateDOList) {
        if (CollectionUtils.isEmpty(kpiBdCommissionOperateDOList)) {
            return;
        }
        kpiBdCommissionOperateDOList = kpiBdCommissionOperateDOList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        Lists.partition(kpiBdCommissionOperateDOList, 100)
                .forEach(item -> cfKpiBdCommissionOperateDao.addBatch(item));
    }

    @Override
    public OpResult<List<CfKpiBdCommissionOperateDO>> queryBdCommissionDetail(String monthkey, String uniqueCode, CfBdKpiEnums.SpecialTypeEnum specialTypeEnum) {
        if (StringUtils.isEmpty(monthkey)){
            monthkey = DateUtil.getYYYYMMStr(DateUtil.getCurrentDate());
        }
        List<CfKpiBdCommissionOperateDO> list = cfKpiBdCommissionOperateDao.queryBdCommissionDetail(monthkey,uniqueCode,specialTypeEnum.getType());
        return OpResult.createSucResult(list);
    }

    @Override
    public List<CfKpiBdCommissionOperateDO> listAllSpecialCommission(String monthKey) {
        return cfKpiBdCommissionOperateDao.listAllSpecailCommission(monthKey);
    }

    @Override
    public int countOperateDetail(Date startTime, int bdSpecialType) {
        if (startTime == null) {
            return 0;
        }
        return cfKpiBdCommissionOperateDao.countOperateDetail(startTime, bdSpecialType);
    }

    @Override
    public List<CfKpiBdCommissionOperateDO> pageOperateDetail(Date startTime, int bdSpecialType, int offset, int limit) {
        if (startTime == null) {
            return Lists.newArrayList();
        }
        return cfKpiBdCommissionOperateDao.pageOperateDetail(startTime, bdSpecialType, offset, limit);
    }

}
