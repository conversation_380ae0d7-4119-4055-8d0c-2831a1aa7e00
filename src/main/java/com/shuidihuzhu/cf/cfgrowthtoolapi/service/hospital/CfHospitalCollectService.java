package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfHospitalCollectDo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/6/5 02:48
 */
public interface CfHospitalCollectService {

    void insert(CfHospitalCollectDo cfHospitalCollectDo);

    void batchInsert(List<CfHospitalCollectDo> cfHospitalCollectDoList);

    void update(CfHospitalCollectDo cfHospitalCollectDo);

    void batchUpdate(List<CfHospitalCollectDo> cfHospitalCollectDo);

    int cancelCollect(int collectType, String vhospitalCode, int departmentId);

    int cancelCommonlyUsed(int id);

    int batchCancelCommonlyUsed(List<Integer> idList);

    int selectHospitalCountByUniqueCode(String uniqueCode, Integer collectType);

    int selectDepartmentCountByUniqueCode(String uniqueCode, String vHospitalCode, Integer collectType);

    List<CfHospitalCollectDo> getCollectedDepartmentList(String vhospitalCode, Integer collectType, String uniqueCode);

    List<CfHospitalCollectDo> getCollectedDepartmentListV2(String vhospitalCode, List<Integer> collectTypeList, String uniqueCode);


    List<CfHospitalCollectDo> getCollectedHospitalList(Integer collectType, String uniqueCode);

    List<CfHospitalCollectDo> getCollectedHospitalListV2(List<Integer> collectTypeList, String uniqueCode);
}
