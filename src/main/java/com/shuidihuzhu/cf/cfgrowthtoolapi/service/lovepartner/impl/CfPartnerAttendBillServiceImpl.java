package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerAttendBillDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerCycleDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerSnapshotDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerAttendInfoDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerAttendBillService;
import com.shuidihuzhu.cf.dao.lovepartner.CfPartnerAttendBillDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */

@Service
@Slf4j
public class CfPartnerAttendBillServiceImpl implements CfPartnerAttendBillService {

    @Autowired
    private CfPartnerAttendBillDao partnerAttendBillDao;

    @Override
    public int batchInsert(List<PartnerAttendInfoDTO> list, CfPartnerSnapshotDo cfPartnerSnapshotDo, CfPartnerCycleDo cfPartnerCycleDo) {
        if (CollectionUtils.isEmpty(list)){
            return 0;
        }
        return partnerAttendBillDao.batchInsert(list,cfPartnerSnapshotDo,cfPartnerCycleDo);
    }

    @Override
    public List<CfPartnerAttendBillDo> listByCycle(int cycleId) {
        return partnerAttendBillDao.listByCycle(cycleId);
    }

    @Override
    public int deleteByCycleId(long cycleId) {
        return partnerAttendBillDao.deleteByCycleId(cycleId);
    }

}
