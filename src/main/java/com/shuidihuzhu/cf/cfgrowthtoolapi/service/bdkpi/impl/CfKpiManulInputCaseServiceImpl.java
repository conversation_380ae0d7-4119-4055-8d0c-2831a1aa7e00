package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiManulInputCaseDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiManulInputCaseService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiManulInputCaseDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-08-03
 */

@Service
@Slf4j
public class CfKpiManulInputCaseServiceImpl implements ICfKpiManulInputCaseService {

    @Autowired
    private CfKpiManulInputCaseDao cfKpiManulInputCaseDao;

    @Override
    public void addBatch(List<CfKpiManulInputCaseDO> kpiManulInputCaseDoList) {
        if (CollectionUtils.isEmpty(kpiManulInputCaseDoList)) {
            return;
        }
        kpiManulInputCaseDoList = kpiManulInputCaseDoList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        Lists.partition(kpiManulInputCaseDoList, 100)
                .forEach(item -> cfKpiManulInputCaseDao.addBatch(item));
    }

    @Override
    public List<CfKpiManulInputCaseDO> listManulInputCase(String monthKey) {
        return cfKpiManulInputCaseDao.listManulInputCase(monthKey);
    }

    @Override
    public List<CfKpiManulInputCaseDO> listByCaseId(List<Long> caseIds) {
        List<CfKpiManulInputCaseDO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(caseIds)) {
            return result;
        }
        Lists.partition(caseIds, 50)
                .forEach(item -> result.addAll(cfKpiManulInputCaseDao.listByCaseIds(item)));
        return result;
    }

    @Override
    public int countOperateDetail(Date startTime) {
        if (startTime == null) {
            return 0;
        }
        return cfKpiManulInputCaseDao.countOperateDetail(startTime);
    }

    @Override
    public List<CfKpiManulInputCaseDO> pageOperateDetail(Date startTime, int offset, int limit) {
        if (startTime == null) {
            return Lists.newArrayList();
        }
        return cfKpiManulInputCaseDao.pageOperateDetail(startTime, offset, limit);
    }
}
