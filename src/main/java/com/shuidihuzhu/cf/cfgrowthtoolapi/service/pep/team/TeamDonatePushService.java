package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.team;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.KpiManagerDataTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.team.TeamCasePerformanceModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiManagerDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepManagerDonateModel;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队 hc 数据推送
 *
 * @author: fengxuan
 * @create 2023-03-20 19:25
 **/
@Slf4j
@Service("teamDonatePushService")
public class TeamDonatePushService extends AbstractPushDataService {

    @Autowired
    private CfKpiManagerDataService kpiManagerDataService;


    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.team_donate_detail;
    }

    @Override
    protected List<PepManagerDonateModel> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        KpiManagerDataTypeEnum dataTypeEnum = KpiManagerDataTypeEnum.getByPepPushEnum(getPushEnum());
        //找到对应的月份
        List<TeamCasePerformanceModel> dataList = kpiManagerDataService.listByDayKeyAndType(pushWhichDay.toString(GrowthtoolUtil.ymdfmt), dataTypeEnum.getCode())
                .stream()
                .map(item -> JSON.parseObject(item.getContent(), TeamCasePerformanceModel.class))
                .collect(Collectors.toList());
        List<PepManagerDonateModel> result = Lists.newArrayList();
        for (TeamCasePerformanceModel data : dataList) {
            PepManagerDonateModel pepManagerDonateModel = new PepManagerDonateModel();
            pepManagerDonateModel.setUserId(data.getUnique_code());
            pepManagerDonateModel.setBelong_unique_code(data.getBelong_unique_code());
            pepManagerDonateModel.setCase_id(data.getCase_id());
            pepManagerDonateModel.setMonth_key(data.getMonth_key());
            pepManagerDonateModel.setDonate_num(data.getDonate_num());
            pepManagerDonateModel.setCase_amount(data.getAmount());
            pepManagerDonateModel.setLotId(lotInfo.getLotId());
            result.add(pepManagerDonateModel);
        }
        return result;
    }

}
