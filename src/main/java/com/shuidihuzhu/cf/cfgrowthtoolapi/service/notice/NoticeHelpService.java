package com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice;

import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.AccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.message.IMsgClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-08-29  17:39
 * 通知聚合
 */
@Service
@Slf4j
public class NoticeHelpService {

    @Resource
    private AccountServiceDelegate accountServiceDelegate;

    @Autowired
    private ApolloService apolloService;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Resource
    private ICfVolunteerService cfVolunteerServiceImpl;

    @Autowired
    private IMsgClientDelegate msgClientV2;

    public void sendVolunteerRaise(long userId, String volunteerUnique) {
        try {
            log.info("{}, {}", userId, volunteerUnique);
            CrowdfundingVolunteer volunteer = cfVolunteerServiceImpl.getByUniqueCode(volunteerUnique);
            if (volunteer==null){
                return;
            }
            //在职状态不为在职时,不执行发送短信逻辑，直接返回
            if (volunteer != null && volunteer.getWorkStatus() != 2){
                return;
            }
            String cryptoMobile = volunteer.getMobile();
            UserInfoModel userInfoModel = accountServiceDelegate.getUserInfoModelByUserId(userId);
            String userMobile = shuidiCipher.decrypt(userInfoModel.getCryptoMobile());

            LocalDateTime now = LocalDateTime.now();

            Map<Integer, String> params = Maps.newHashMap();
            params.put(1, String.valueOf(now.getYear()));
            params.put(2, String.valueOf(now.getMonthValue()));
            params.put(3, String.valueOf(now.getDayOfMonth()));
            params.put(4, String.valueOf(now.getHour()));
            params.put(5, String.valueOf(now.getMinute()));
            params.put(6, String.valueOf(now.getSecond()));
            params.put(7, userMobile);
            msgClientV2.sendSms("sms1021",cryptoMobile,params);
        }catch (Exception e){
            log.error("sendVolunteerRaise exception",e);
        }
    }

}
