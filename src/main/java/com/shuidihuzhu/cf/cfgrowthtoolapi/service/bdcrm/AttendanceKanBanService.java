package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IKongmingDataApiClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.AttendanceKanBanOrgInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.AttendanceKanBanOrgInfoModelVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.dataservice.kongmingdata.v1.dto.ExternalLoginRequest;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.MD5Util;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/7  20:45
 */
@Service
@Slf4j
public class AttendanceKanBanService {

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Resource
    private IKongmingDataApiClientDelegate kongmingDataApiClientDelegate;

    private final String DIRECTLY_OPERATED_CITIES = "直营城市";

    private final String CONSULTANT_ECOLOGICAL_OPERATION = "顾问生态运营";

    private final String WILDCARD = "&separator=,";

    private final String HOSTNAVONLY = "&pref.HostNavOnly=true";

    public Response<String> get(CrowdfundingVolunteer cfVolunteer, List<BdCrmOrganizationDO> bdOrgList, String startTime, String endTime, String bigAreaOrgName, String areaOrgName, String subareaOrgName, String groupOrgName) {

        boolean passCheck = check(bdOrgList, bigAreaOrgName, areaOrgName, subareaOrgName, groupOrgName);
        if (!passCheck) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.UNAUTHORIZED);
        }

        String pageUrl = buildPageUrl(cfVolunteer, bdOrgList, startTime, endTime, bigAreaOrgName, areaOrgName, subareaOrgName, groupOrgName);
        if (StringUtils.isBlank(pageUrl)) {
            return NewResponseUtil.makeFail("未获取到孔明url");
        }

        Optional<String> optional = getKongMingUrl(pageUrl, cfVolunteer);
        return optional.map(NewResponseUtil::makeSuccess).orElseGet(() -> NewResponseUtil.makeFail("未获取到孔明看板数据"));
    }

    public Response<AttendanceKanBanOrgInfoModelVo> getOrgInfo(CrowdfundingVolunteer cfVolunteer, List<BdCrmOrganizationDO> bdOrgList) {

        //获取直营组织
        List<Long> ownOrgList = crmSelfBuiltOrgReadService.listAllOwnOrg();
        List<BdCrmOrganizationDO> ownBdCrmOrganizationDOList = bdOrgList.stream().filter(v -> ownOrgList.contains(v.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ownBdCrmOrganizationDOList)) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.ORG_CAN_NOT_EXSITS);
        }

        AttendanceKanBanOrgInfoModelVo result = new AttendanceKanBanOrgInfoModelVo();
        result.setLevel(cfVolunteer.getLevel());

        List<AttendanceKanBanOrgInfoModel> attendanceKanBanOrgInfoModelList = Lists.newArrayList();
        for (BdCrmOrganizationDO bdCrmOrganizationDO : ownBdCrmOrganizationDOList) {
            AttendanceKanBanOrgInfoModel attendanceKanBanOrgInfoModel = getAttendanceKanBanOrgInfoModel(bdCrmOrganizationDO);
            attendanceKanBanOrgInfoModelList.add(attendanceKanBanOrgInfoModel);
        }

        result.setAttendanceKanBanOrgInfoModelList(attendanceKanBanOrgInfoModelList);
        return NewResponseUtil.makeSuccess(result);
    }

    public Response<String> getV1(CrowdfundingVolunteer cfVolunteer, List<BdCrmOrganizationDO> bdOrgList) {

//        //获取直营组织
//        List<Long> ownOrgList = crmSelfBuiltOrgReadService.listAllOwnOrg();
//        List<BdCrmOrganizationDO> ownBdCrmOrganizationDOList = bdOrgList.stream().filter(v -> ownOrgList.contains(v.getId())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(ownBdCrmOrganizationDOList)) {
//            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.ORG_CAN_NOT_EXSITS);
//        }

        String pageUrl = buildPageUrlV1(cfVolunteer, bdOrgList);
        if (StringUtils.isBlank(pageUrl)) {
            return NewResponseUtil.makeFail("未获取到孔明url");
        }

        Optional<String> optional = getKongMingUrl(pageUrl, cfVolunteer);
        return optional.map(NewResponseUtil::makeSuccess).orElseGet(() -> NewResponseUtil.makeFail("未获取到孔明看板数据"));
    }

    private Optional<String> getKongMingUrl(String pageUrl, CrowdfundingVolunteer cfVolunteer) {
        ExternalLoginRequest request = new ExternalLoginRequest();
        // biz由孔明BI系统开发人员分配提供
        request.setBiz("cf");
        // appKey由孔明BI系统开发人员分配提供
        String appKey = "747c6f5f93450208";
        // 接入方系统的用户账号（员工账号或者手机号），例如sea后台嵌入时，就传登录手机号
        request.setUsername(cfVolunteer.getMis());
        request.setTimestamp(System.currentTimeMillis());
        // 在孔明BI中已配置好的数据看板页面地址(PC端嵌入时加上?ps=iframe2)
        // 在孔明BI中已配置好的数据看板页面地址(移动端嵌入时加上?pref.HostNavOnly=true)
        request.setPageUrl(pageUrl);
        // 加密签名不区分大小写（32位加密）
        String sign = MD5Util.getMD5HashValue(appKey + request.getBiz() + request.getUsername() + request.getTimestamp() + request.getPageUrl());
        request.setAuthSign(sign);

        return kongmingDataApiClientDelegate.externalLogin(request);
    }

    private void listSubOrg(List<AttendanceKanBanOrgInfoModel> attendanceKanBanOrgInfoModelList, List<BdCrmOrganizationDO> crmOrganizationDOList) {
        if (CollectionUtils.isEmpty(attendanceKanBanOrgInfoModelList)) {
            return;
        }

        for (AttendanceKanBanOrgInfoModel attendanceKanBanOrgInfoModel : attendanceKanBanOrgInfoModelList) {
            List<AttendanceKanBanOrgInfoModel> subOrgInfos = crmOrganizationDOList
                    .stream()
                    .filter(v -> v.getParentId() == attendanceKanBanOrgInfoModel.getOrgId())
                    .map(AttendanceKanBanOrgInfoModel::convert)
                    .collect(Collectors.toList());

            attendanceKanBanOrgInfoModel.setSubOrgs(CollectionUtils.isEmpty(subOrgInfos) ? null : subOrgInfos);
            listSubOrg(subOrgInfos, crmOrganizationDOList);
        }
    }

    private AttendanceKanBanOrgInfoModel getAttendanceKanBanOrgInfoModel(BdCrmOrganizationDO bdCrmOrganizationDO) {
        AttendanceKanBanOrgInfoModel result = AttendanceKanBanOrgInfoModel.convert(bdCrmOrganizationDO);

        List<BdCrmOrganizationDO> allBdCrmOrganizationDOList = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(bdCrmOrganizationDO.getId())
                .stream()
                .filter(v -> v.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode())
                .collect(Collectors.toList());

        List<AttendanceKanBanOrgInfoModel> subOrgInfos = Lists.newArrayList();
        if (DIRECTLY_OPERATED_CITIES.equals(bdCrmOrganizationDO.getOrgName())) {
            List<BdCrmOrganizationDO> bigAreaOrgInfoList = allBdCrmOrganizationDOList.stream().filter(v -> v.getParentId() == bdCrmOrganizationDO.getId()).collect(Collectors.toList());
            for (BdCrmOrganizationDO crmOrganizationDO : bigAreaOrgInfoList) {
                List<BdCrmOrganizationDO> areaOrgInfoList = allBdCrmOrganizationDOList.stream().filter(v -> v.getParentId() == crmOrganizationDO.getId()).collect(Collectors.toList());
                subOrgInfos.addAll(areaOrgInfoList
                        .stream()
                        .map(AttendanceKanBanOrgInfoModel::convert)
                        .collect(Collectors.toList()));
            }
        } else {
            subOrgInfos = allBdCrmOrganizationDOList
                    .stream()
                    .filter(v -> v.getParentId() == bdCrmOrganizationDO.getId())
                    .map(AttendanceKanBanOrgInfoModel::convert)
                    .collect(Collectors.toList());
        }
        result.setSubOrgs(subOrgInfos);
        listSubOrg(subOrgInfos, allBdCrmOrganizationDOList);

        return result;
    }


    /**
     * 权限校验
     *
     * @param bdOrgList
     * @param bigAreaOrgName
     * @param areaOrgName
     * @param subareaOrgName
     * @param groupOrgName
     * @return
     */
    private boolean check(List<BdCrmOrganizationDO> bdOrgList, String bigAreaOrgName, String areaOrgName, String subareaOrgName, String groupOrgName) {
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = Lists.newArrayList();
        for (BdCrmOrganizationDO bdCrmOrganizationDO : bdOrgList) {
            List<BdCrmOrganizationDO> allBdCrmOrganizationDOList = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(bdCrmOrganizationDO.getId());
            if (CollectionUtils.isNotEmpty(allBdCrmOrganizationDOList)) {
                bdCrmOrganizationDOList.addAll(allBdCrmOrganizationDOList);
            }
        }

        return bdCrmOrganizationDOList.stream().anyMatch(v ->
                {
                    if (StringUtils.isNotBlank(bigAreaOrgName)) {
                        return v.getOrgName().equals(bigAreaOrgName);
                    }

                    if (StringUtils.isNotBlank(areaOrgName)) {
                        return v.getOrgName().equals(areaOrgName);
                    }

                    if (StringUtils.isNotBlank(subareaOrgName)) {
                        return v.getOrgName().equals(subareaOrgName);
                    }

                    if (StringUtils.isNotBlank(groupOrgName)) {
                        return v.getOrgName().equals(groupOrgName);
                    }
                    return false;
                }
        );
    }

    private String buildPageUrl(CrowdfundingVolunteer volunteer, List<BdCrmOrganizationDO> bdOrgList, String startTime, String endTime, String bigAreaOrgName, String areaOrgName, String subareaOrgName, String groupOrgName) {
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(volunteer.getLevel());
        if (Objects.isNull(roleEnum)) {
            return null;
        }

        switch (roleEnum) {
            case OPERATOR:
                boolean isBigAreaOperator = bdOrgList.stream().anyMatch(v -> v.getOrgName().equals(DIRECTLY_OPERATED_CITIES));
                if (isBigAreaOperator) {
                    return getBigAreaLeaderAndOperatorUrl(startTime, endTime, bigAreaOrgName, areaOrgName, subareaOrgName, groupOrgName);
                } else {
                    return getAreaLeaderAndOperatorUrl(startTime, endTime, areaOrgName, subareaOrgName, groupOrgName);
                }
            case BIG_AREA_LEADER:
                return getBigAreaLeaderAndOperatorUrl(startTime, endTime, bigAreaOrgName, areaOrgName, subareaOrgName, groupOrgName);
            case AREA_LEADER:
                return getAreaLeaderAndOperatorUrl(startTime, endTime, areaOrgName, subareaOrgName, groupOrgName);
            case PARTITION_LEADER:
                return getPartitionLeaderUrl(startTime, endTime, subareaOrgName, groupOrgName);
            case PROVINCE_LEADER:
                return getProvinceLeaderUrl(startTime, endTime, groupOrgName);
            default:
                return null;
        }

    }


    /**
     * 大区经理跟大区经理级别的总部运营
     *
     * @return
     */
    private String getBigAreaLeaderAndOperatorUrl(String startTime, String endTime, String bigAreaOrgName, String areaOrgName, String subareaOrgName, String groupOrgName) {
        String result = null;

        //是否是今日数据
        boolean isToday = StringUtils.isBlank(startTime) && StringUtils.isBlank(endTime);

        if (StringUtils.isNotBlank(bigAreaOrgName)) {
            result = getBigAreaUrl(isToday, startTime, endTime);
        }

        if (StringUtils.isNotBlank(areaOrgName)) {
            result = getAreaUrl(areaOrgName, isToday, startTime, endTime);
        }

        if (StringUtils.isNotBlank(subareaOrgName)) {
            result = getReasonUrl(subareaOrgName, isToday, startTime, endTime);
        }

        if (StringUtils.isNotBlank(groupOrgName)) {
            result = getGroupUrl(groupOrgName, isToday, startTime, endTime);
        }
        return result;
    }

    /**
     * 区域经理跟区域经理级别的总部运营
     *
     * @return
     */
    private String getAreaLeaderAndOperatorUrl(String startTime, String endTime, String areaOrgName, String subareaOrgName, String groupOrgName) {
        String result = null;

        //是否是今日数据
        boolean isToday = StringUtils.isBlank(startTime) && StringUtils.isBlank(endTime);

        if (StringUtils.isNotBlank(areaOrgName)) {
            result = getAreaUrl(areaOrgName, isToday, startTime, endTime);
        }

        if (StringUtils.isNotBlank(subareaOrgName)) {
            result = getReasonUrl(subareaOrgName, isToday, startTime, endTime);
        }

        if (StringUtils.isNotBlank(groupOrgName)) {
            result = getGroupUrl(groupOrgName, isToday, startTime, endTime);
        }
        return result;
    }


    /**
     * 分区经理
     *
     * @return
     */
    private String getPartitionLeaderUrl(String startTime, String endTime, String subareaOrgName, String groupOrgName) {
        String result = null;

        //是否是今日数据
        boolean isToday = StringUtils.isBlank(startTime) && StringUtils.isBlank(endTime);

        if (StringUtils.isNotBlank(subareaOrgName)) {
            result = getReasonUrl(subareaOrgName, isToday, startTime, endTime);
        }

        if (StringUtils.isNotBlank(groupOrgName)) {
            result = getGroupUrl(groupOrgName, isToday, startTime, endTime);
        }
        return result;
    }

    /**
     * 业务经理
     *
     * @return
     */
    private String getProvinceLeaderUrl(String startTime, String endTime, String groupOrgName) {
        String result = null;

        //是否是今日数据
        boolean isToday = StringUtils.isBlank(startTime) && StringUtils.isBlank(endTime);

        if (StringUtils.isNotBlank(groupOrgName)) {
            result = getGroupUrl(groupOrgName, isToday, startTime, endTime);
        }
        return result;
    }

    /**
     * 展示区域数据
     *
     * @param isToday
     * @param startTime
     * @param endTime
     * @return
     */
    private String getBigAreaUrl(boolean isToday, String startTime, String endTime) {
        String url = null;
        if (isToday) {
            url = "https://kongming.shuiditech.com/page/n0ae66e27631444c8a458113";
        } else {
            url = "https://kongming.shuiditech.com/page/d2e9f09a5c52740c988f9f0b?q89616de998c04eb799cbd27=" + startTime + "&la5e99049529340a79cf5647=" + endTime;
        }
        return url;
    }

    /**
     * 展示分区数据
     *
     * @param areaOrgName
     * @param isToday
     * @param startTime
     * @param endTime
     * @return
     */
    private String getAreaUrl(String areaOrgName, boolean isToday, String startTime, String endTime) {
        String url = null;
        if (isToday) {
            url = "https://kongming.shuiditech.com/page/s2390e126c763435cabb6191?c753e0b5e4c544d34af844be=" + areaOrgName;
        } else {
            url = "https://kongming.shuiditech.com/page/b3c3efa50dd59482890ab4ca?j64a79352db0548a68762f5e=" + areaOrgName + "&i93c6787d96494fd489ee918=" + startTime + "&m0608609446374c2a8ee59a7=" + endTime;
        }
        return url;
    }

    /**
     * 展示组数据
     *
     * @param subareaOrgName
     * @param isToday
     * @param startTime
     * @param endTime
     * @return
     */
    private String getReasonUrl(String subareaOrgName, boolean isToday, String startTime, String endTime) {
        String url = null;
        if (isToday) {
            url = "https://kongming.shuiditech.com/page/wc98990ba237b4639907b398?hf8e3c45e212541799f6c383=" + subareaOrgName;
        } else {
            url = "https://kongming.shuiditech.com/page/g54edb6b9a5a64ffca9d8d70?fa95d3040006f4811b25a5e8=" + subareaOrgName + "&na4e5c205dc364015b55cf40=" + startTime + "&o2c038835344e49c3bf5f71c=" + endTime;
        }
        return url;
    }

    /**
     * 展示人员数据
     *
     * @param groupOrgName
     * @param isToday
     * @param startTime
     * @param endTime
     * @return
     */
    private String getGroupUrl(String groupOrgName, boolean isToday, String startTime, String endTime) {
        String url = null;
        if (isToday) {
            url = "https://kongming.shuiditech.com/page/bded2cc35218e4c179266b1a?w8d72635f56eb4ab7b4e21ab=" + groupOrgName;
        } else {
            url = "https://kongming.shuiditech.com/page/q08caabb690f14d48998f027?eb7d8b6fc39d1494ba988985=" + groupOrgName + "&c0a60e20ee46c46c0840bf78=" + startTime + "&s38666ee6a5504789b398716=" + endTime;
        }
        return url;
    }

    private String buildPageUrlV1(CrowdfundingVolunteer volunteer, List<BdCrmOrganizationDO> bdOrgList) {
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(volunteer.getLevel());
        if (Objects.isNull(roleEnum)) {
            return null;
        }

        switch (roleEnum) {
            case OPERATOR:
                boolean isBigAreaOperator = bdOrgList.stream().anyMatch(v -> v.getOrgName().equals(DIRECTLY_OPERATED_CITIES) || v.getOrgName().equals(CONSULTANT_ECOLOGICAL_OPERATION));
                if (isBigAreaOperator) {
                    return getBigAreaLeaderAndOperatorUrlV1(bdOrgList);
                } else {
                    return getAreaLeaderAndOperatorUrlV1(bdOrgList);
                }
            case BIG_AREA_LEADER:
                return getBigAreaLeaderAndOperatorUrlV1(bdOrgList);
            case AREA_LEADER:
                return getAreaLeaderAndOperatorUrlV1(bdOrgList);
            case PARTITION_LEADER:
                //顾问生态运营的分区经理当区域经理使用
                List<Long> listAllConsultantEcologicalOperationOrg = crmSelfBuiltOrgReadService.listAllConsultantEcologicalOperationOrg();
                boolean isConsultantEcologicalOperationOrg = bdOrgList.stream().anyMatch(v -> listAllConsultantEcologicalOperationOrg.contains(v.getId()));
                if (isConsultantEcologicalOperationOrg) {
                    return getAreaLeaderAndOperatorUrlV1(bdOrgList);
                }
                return getPartitionLeaderUrlV1(bdOrgList);
            case PROVINCE_LEADER:
                return getProvinceLeaderUrlV1(bdOrgList);
            default:
                return null;
        }

    }

    /**
     * 大区经理跟大区经理级别的总部运营
     *
     * @return
     */
    private String getBigAreaLeaderAndOperatorUrlV1(List<BdCrmOrganizationDO> bdOrgList) {
        Optional<BdCrmOrganizationDO> optional = bdOrgList.stream().filter(v -> v.getOrgName().equals(DIRECTLY_OPERATED_CITIES) || v.getOrgName().equals(CONSULTANT_ECOLOGICAL_OPERATION)).findFirst();
        if (optional.isEmpty()) {
            return null;
        }

        BdCrmOrganizationDO bdCrmOrganizationDO = optional.get();
        List<BdCrmOrganizationDO> allBdCrmOrganizationDOList = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(bdCrmOrganizationDO.getId())
                .stream()
                .filter(v -> v.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode())
                .collect(Collectors.toList());

        List<String> orgNameList = Lists.newArrayList();
        List<BdCrmOrganizationDO> bigAreaOrgInfoList = allBdCrmOrganizationDOList.stream().filter(v -> v.getParentId() == bdCrmOrganizationDO.getId()).collect(Collectors.toList());
        for (BdCrmOrganizationDO crmOrganizationDO : bigAreaOrgInfoList) {
            List<BdCrmOrganizationDO> areaOrgInfoList = allBdCrmOrganizationDOList.stream().filter(v -> v.getParentId() == crmOrganizationDO.getId()).collect(Collectors.toList());
            orgNameList.addAll(areaOrgInfoList.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.toList()));
        }

        String param = Joiner.on(",").join(orgNameList);
        return "https://kongming.shuiditech.com/page/ffafa5428b7a1476e9a81b33?e05581e275bfa4e5a9ff9558=" + param + WILDCARD + HOSTNAVONLY;
    }

    /**
     * 区域经理跟区域经理级别的总部运营
     *
     * @return
     */
    private String getAreaLeaderAndOperatorUrlV1(List<BdCrmOrganizationDO> bdOrgList) {
        String param = Joiner.on(",").join(bdOrgList.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.toList()));
        return "https://kongming.shuiditech.com/page/u16d55a95c9b74b5198ea578?n7d594d99f27f4860ad5fd22=" + param + WILDCARD + HOSTNAVONLY;
    }


    /**
     * 分区经理
     *
     * @return
     */
    private String getPartitionLeaderUrlV1(List<BdCrmOrganizationDO> bdOrgList) {
        //获取上级组织
        List<Long> parentIdList = bdOrgList.stream().map(BdCrmOrganizationDO::getParentId).collect(Collectors.toList());
        List<BdCrmOrganizationDO> parentOrgList = crmSelfBuiltOrgReadService.getOrgInfoList(parentIdList);

        String param1 = Joiner.on(",").join(parentOrgList.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.toList()));
        String param2 = Joiner.on(",").join(bdOrgList.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.toList()));
        return "https://kongming.shuiditech.com/page/ab63bdff8bf9b44ecb30f9e0?k5a864240022e4ea79ea327d=" + param1 + "&p020551edaffb4bcfbb57c57=" + param2 + WILDCARD + HOSTNAVONLY;
    }

    /**
     * 业务经理
     *
     * @return
     */
    private String getProvinceLeaderUrlV1(List<BdCrmOrganizationDO> bdOrgList) {
        //获取上级组织
        List<Long> parentIdList = bdOrgList.stream().map(BdCrmOrganizationDO::getParentId).collect(Collectors.toList());
        List<BdCrmOrganizationDO> parentOrgList = crmSelfBuiltOrgReadService.getOrgInfoList(parentIdList);
        //获取上级的上级组织
        List<Long> parentParentIdList = parentOrgList.stream().map(BdCrmOrganizationDO::getParentId).collect(Collectors.toList());
        List<BdCrmOrganizationDO> parentParentOrgList = crmSelfBuiltOrgReadService.getOrgInfoList(parentParentIdList);

        String param1 = Joiner.on(",").join(parentParentOrgList.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.toList()));
        String param2 = Joiner.on(",").join(parentOrgList.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.toList()));
        String param3 = Joiner.on(",").join(bdOrgList.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.toList()));
        return "https://kongming.shuiditech.com/page/s1ddf4c9c2493467abcca395?l90db2e982e674e8e9d363b2=" + param1 + "&j2aad98030e6e465588e0f6c=" + param2 + "&h22c31c1a3e5945a9bd56992=" + param3 + WILDCARD + HOSTNAVONLY;
    }

}
