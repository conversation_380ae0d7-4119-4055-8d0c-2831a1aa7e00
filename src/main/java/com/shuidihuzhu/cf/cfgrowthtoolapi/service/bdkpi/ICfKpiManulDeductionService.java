package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiManulDeductionCaseDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiManulDeductionVO;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
public interface ICfKpiManulDeductionService {

    /**
     * 批量插入
     * @param cfKpiManulDeductionCaseDOList
     */
    void addBatch(List<CfKpiManulDeductionCaseDO> cfKpiManulDeductionCaseDOList);

    OpResult<List<CfKpiManulDeductionVO>> queryCaseDeductionAmountDetail(String monthkey, Long caseId);

    List<CfKpiManulDeductionCaseDO> listDeductCaseId(String monthKey);

    int countOperateDetail(Date startTime);

    List<CfKpiManulDeductionCaseDO> pageOperateDetail(Date startTime, int offset, int limit);
}
