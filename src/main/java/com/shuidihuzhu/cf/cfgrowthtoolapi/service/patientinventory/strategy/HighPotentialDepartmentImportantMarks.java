package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory.strategy;

import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PatientInventoryDepartmentTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PatientInventoryFirstIntentionType;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.PatientInventoryPatientFollowUpRecordDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.PatientInventoryPatientInfoDo;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20  17:23
 */
@Service
public class HighPotentialDepartmentImportantMarks extends AbstractImportantMarksStrategy {

    @Override
    public boolean support(int departmentType) {
        return departmentType == PatientInventoryDepartmentTypeEnum.HIGH_POTENTIAL_DEPARTMENT.getCode();
    }

    @Override
    public boolean getNeedToFollowUpTodayLabel(PatientInventoryPatientFollowUpRecordDo patientFollowUpRecordDo, String dateline, int volunteerLevel) {
        boolean result = false;
        //不满足职级
        if (!isCommon(volunteerLevel) && !isManagement(volunteerLevel)) {
            return result;
        }
        String now = DateUtil.getCurrentDateStr();
        //-只要最新的一级意向为需拜访，且有下次跟进时间（不判断二级意向是啥），下次跟进事件的当天则进行推送；推送时间：每天早上10点
        if (patientFollowUpRecordDo.getFirstIntention() == PatientInventoryFirstIntentionType.NEED_FOLLOW_UP.getCode()) {
            if (patientFollowUpRecordDo.getNextFollowUpTime() == null) {
                return result;
            }
            String nowDayStr = chooseDay(patientFollowUpRecordDo.getNextFollowUpTime(), 0);
            if (now.equals(nowDayStr)) {
                result = true;
            }
        }
        return result;
    }

    @Override
    public boolean getPayCloseAttentionToLabel(List<PatientInventoryPatientFollowUpRecordDo> patientInventoryPatientFollowUpRecordDoList, PatientInventoryPatientInfoDo patientInventoryPatientInfoDo, String dateline, int volunteerLevel) {
        return false;
    }

}
