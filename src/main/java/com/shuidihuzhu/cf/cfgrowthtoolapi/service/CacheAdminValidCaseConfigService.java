package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OrgValidCaseConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/12  11:09
 */
public interface CacheAdminValidCaseConfigService {

    Map<Integer, AdminValidCaseConfigDO> getValidCaseConfigMap();

    void delRedis();

    AdminValidCaseConfigDO getAdminValidCaseConfigDO(long orgId);

    List<OrgValidCaseConfig> partitionOrgIds(List<Long> orgIdList);
}
