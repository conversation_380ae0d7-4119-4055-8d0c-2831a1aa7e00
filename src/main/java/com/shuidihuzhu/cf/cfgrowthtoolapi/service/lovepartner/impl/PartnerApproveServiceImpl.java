package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ApproveStatusEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.LovePartnerInfoParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.PartnerApproveService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice.workwx.WorkWeixinContentBuilder;
import com.shuidihuzhu.cf.dao.lovepartner.CfPartnerInfoDao;
import com.shuidihuzhu.cf.dao.lovepartner.PartnerApproveDao;
import com.shuidihuzhu.cf.dao.lovepartner.PartnerAttendInfoDao;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhengqiu
 * @date: 2021-09-02 18:05
 **/
@Service
@Slf4j
public class PartnerApproveServiceImpl implements PartnerApproveService {

    @Autowired
    private PartnerApproveDao partnerApproveDao;
    @Autowired
    private PartnerAttendInfoDao partnerAttendInfoDao;
    @Autowired
    private ICfVolunteerService cfVolunteerService;
    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgServiceImpl;

    @Autowired
    private CfPartnerInfoDao cfPartnerInfoDao;
    @Autowired
    private ShuidiCipher shuidiCipher;


    @Override
    public PartnerApproveVo getApproveInfo(long approveId, long attendId) {

        if (attendId > 0) {
            PartnerAttendInfoDTO attendInfoDTO = partnerAttendInfoDao.getInfoId(attendId);
            List<PartnerApproveDTO> approveDTOList = partnerApproveDao.getInfoByBizId(attendId, 2);
            PartnerApproveVo approveVo = buildApproveVO(attendInfoDTO, approveDTOList);
            return approveVo;
        }

        if (approveId > 0) {
            PartnerApproveDTO approveDTO = partnerApproveDao.getInfoById(approveId);
            PartnerAttendInfoDTO attendInfoDTO = partnerAttendInfoDao.getInfoId(approveDTO.getBizId());
            List<PartnerApproveDTO> approveDTOList = partnerApproveDao.getInfoByBizId(approveDTO.getBizId(), 2);
            PartnerApproveVo approveVo = buildApproveVO(attendInfoDTO, approveDTOList);
            return approveVo;
        }
        return new PartnerApproveVo();
    }

    private PartnerApproveVo buildApproveVO(PartnerAttendInfoDTO attendInfoDTO, List<PartnerApproveDTO> approveDTOList) {
        List<PartnerApproveSimple> approveSimples = getSimpleList(approveDTOList);

        PartnerApproveSimple simple = new PartnerApproveSimple();
        simple.setApproveName(attendInfoDTO.getLeaderName());
        simple.setApproveStatus(-1);
        simple.setRemark("");
        simple.setUpdateTime(attendInfoDTO.getUpdateTime());
        approveSimples.add(0, simple);

        PartnerApproveVo approveVO = PartnerApproveVo.builder()
                .uniqueCode(attendInfoDTO.getUniqueCode())
                .name(attendInfoDTO.getName())
                .attendDate(attendInfoDTO.getAttendDate())
                .attendType(attendInfoDTO.getAttendType())
                .attendSalary(attendInfoDTO.getAttendSalary())
                .approveStatus(attendInfoDTO.getApproveStatus())
                .applicant(attendInfoDTO.getLeaderName())
                .approveSimples(approveSimples)
                .attendProveImgs(attendInfoDTO.getAttendProveImgs())
                .build();
        return approveVO;
    }


    private List<PartnerApproveSimple> getSimpleList(List<PartnerApproveDTO> approveDTOList) {
        ArrayList<PartnerApproveSimple> arrayList = Lists.newArrayList();
        for (PartnerApproveDTO dto : approveDTOList) {
            PartnerApproveSimple approveSimple = PartnerApproveSimple.builder()
                    .approveName(dto.getApproveName())
                    .approveStatus(dto.getApproveStatus())
                    .remark(dto.getRemark())
                    .updateTime(dto.getUpdateTime()).build();
            arrayList.add(approveSimple);
        }
        return arrayList;
    }

    @Override
//    @Transactional(rollbackFor = Exception.class, transactionManager = CfGrowthToolDS.CLEWTRACK_DATA_SOURECE_TRANSACTION_MANAGER)
    public int updateApproveStatus(long approveId, String remark, int approveState) {
        // 更新 approve 同时更新 atttenInfo
        PartnerApproveDTO approveDTO = partnerApproveDao.getInfoById(approveId);
        PartnerAttendInfoDTO attendInfoDTO = partnerAttendInfoDao.getInfoId(approveDTO.getBizId());
        if (approveState == 2) {
            // 更新状态，给驳回人发信息
            partnerApproveDao.updateApproveStatus(approveId, remark, ApproveStatusEnum.REJECT.getCode());
            partnerAttendInfoDao.updateApproveStatus(approveDTO.getBizId(), ApproveStatusEnum.REJECT.getCode());
            PartnerApproveDTO approveInfo = partnerApproveDao.getInfoById(approveId);
            String title = "【爱心伙伴出勤驳回】";
            String url = "https://www.shuidichou.com/bd/love-partner/attendance/launch?status=2";
            WorkWeixinContentBuilder cb = WorkWeixinContentBuilder.create()
                    .subject(title)
                    .payload("姓名", attendInfoDTO.getName())
                    .payload("出勤日期", DateUtil.formatDateTime(attendInfoDTO.getAttendDate()))
                    .payload("状态", "驳回")
                    .payload("审批人", approveDTO.getApproveName())
                    .payload("审批时间", DateUtil.formatDateTime(approveInfo.getUpdateTime()))
                    .payload("操作", "<a href=\"" + url + "\">点击查看</a>");
            String content = cb.build();
            CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(attendInfoDTO.getLeaderUniqueCode());
            appPushCrmCaseMsgServiceImpl.sendMsg2Bd(volunteer, content, title, "");
        } else {
            partnerApproveDao.updateApproveStatus(approveId, remark, ApproveStatusEnum.APPROVE.getCode());
            partnerAttendInfoDao.updateApproveStatus(approveDTO.getBizId(), ApproveStatusEnum.APPROVE.getCode());
        }
        return 1;
    }

    @Override
    public List<AttendDateInfoVO> getDateInfo(String uniquecode) {
        List<PartnerAttendInfoDTO> partnerAttendInfos = partnerAttendInfoDao.getInfoByUniqueCode(uniquecode);
        ArrayList<AttendDateInfoVO> list = Lists.newArrayList();
        for (PartnerAttendInfoDTO info : partnerAttendInfos) {
            list.add(new AttendDateInfoVO(info.getAttendDate(), info.getApproveStatus()));
        }
        return list;
    }

    @Override
    public List<PartnerAttendInfoVO> getAttendInfoOfMy(int approveStatus, String leaderUniqueCode) {
        ArrayList<PartnerAttendInfoVO> arrayList = Lists.newArrayList();
        List<PartnerAttendInfoDTO> info = null;
        if (approveStatus == 0) {
            info = partnerAttendInfoDao.getInfoByLeaderUniqueCode(leaderUniqueCode);
        } else {
            info = partnerAttendInfoDao.getInfoByLeaderUniqueCodeAndStatus(leaderUniqueCode, approveStatus);
        }

        if (CollectionUtils.isEmpty(info)) {
            return arrayList;
        }

        for (PartnerAttendInfoDTO attendInfoDTO : info) {
            PartnerAttendInfoVO attendInfoVO = PartnerAttendInfoVO.builder()
                    .uniqueCode(attendInfoDTO.getUniqueCode())
                    .name(attendInfoDTO.getName())
                    .attendDate(attendInfoDTO.getAttendDate())
                    .attendType(attendInfoDTO.getAttendType())
                    .attendSalary(attendInfoDTO.getAttendSalary())
                    .approveStatus(attendInfoDTO.getApproveStatus())
                    .applicant(attendInfoDTO.getLeaderName())
                    .attendId(attendInfoDTO.getId())
                    .createTime(attendInfoDTO.getCreateTime())
                    .build();

            arrayList.add(attendInfoVO);
        }


        return arrayList;
    }

    @Override
    public List<ApproveMisVO> getAttendInfoOfApprove(int approveStatus, String approveUniqueCode) {
        ArrayList<ApproveMisVO> arrayList = Lists.newArrayList();

        List<PartnerApproveDTO> infos = null;
        if (approveStatus == 0) {
            infos = partnerApproveDao.getByApproveUniqueCode(approveUniqueCode, 2);
        } else {
            infos = partnerApproveDao.getByApproveUniqueCodeAndStatus(approveStatus, approveUniqueCode, 2);
        }


        if (CollectionUtils.isEmpty(infos)) {
            return arrayList;
        }

        for (PartnerApproveDTO info : infos) {
            PartnerAttendInfoDTO attendInfoDTO = partnerAttendInfoDao.getInfoId(info.getBizId());
            ApproveMisVO approveMisVO = ApproveMisVO.builder()
                    .uniqueCode(attendInfoDTO.getUniqueCode())
                    .name(attendInfoDTO.getName())
                    .attendDate(attendInfoDTO.getAttendDate())
                    .attendType(attendInfoDTO.getAttendType())
                    .attendSalary(attendInfoDTO.getAttendSalary())
                    .approveStatus(attendInfoDTO.getApproveStatus())
                    .applicant(attendInfoDTO.getLeaderName())
                    .approveId(info.getId())
                    .createTime(attendInfoDTO.getCreateTime())
                    .build();
            arrayList.add(approveMisVO);

        }

        return arrayList;
    }


    //获取审批详情
    @Override
    public PartnerInfoModel searchApproveInfo(long approveId) {
        PartnerInfoModel partnerInfoModel = new PartnerInfoModel();
        //根据审批id，获取审批id
        PartnerApproveDTO approveDTO = partnerApproveDao.getInfoById(approveId);
        //根据审批bizid，获取人员详情
        CfPartnerInfoDo cfPartnerInfoDo = cfPartnerInfoDao.getPartnerInfoByPartnerId(approveDTO.getBizId());
        //根据bizid，拿到对应的信息列表
        List<PartnerApproveDTO> approveDTOList = partnerApproveDao.getInfoByBizId(approveDTO.getBizId(), 1);
        //进行组合
        List<PartnerApproveModel> approveModelList = new ArrayList<>();
        //在审批流程中添加提交人信息
        PartnerApproveModel approve = new PartnerApproveModel();
        approve.setApproveName(cfPartnerInfoDo.getLeaderName());
        approve.setApproveStatus(-2);//提交状态
        approve.setRemark("");
        approve.setUpdateTime(cfPartnerInfoDo.getCreateTime());
        approveModelList.add(0, approve);
        for (PartnerApproveDTO model : approveDTOList) {
            PartnerApproveModel partnerApproveModel = new PartnerApproveModel();
            partnerApproveModel.setApproveName(model.getApproveName());
            partnerApproveModel.setApproveStatus(model.getApproveStatus());
            partnerApproveModel.setRemark(model.getRemark());
            partnerApproveModel.setUpdateTime(model.getUpdateTime());
            approveModelList.add(partnerApproveModel);
        }
        partnerInfoModel.setApproveModelList(approveModelList);
        LovePartnerInfoParam partnerInfoParam = JSONObject.parseObject(approveDTO.getApproveContent(),LovePartnerInfoParam.class);
        BeanUtils.copyProperties(cfPartnerInfoDo, partnerInfoModel);
        partnerInfoModel.setEncryptIdCard(shuidiCipher.decrypt(cfPartnerInfoDo.getEncryptIdCard()));
        partnerInfoModel.setEncryptPhone(shuidiCipher.decrypt(cfPartnerInfoDo.getEncryptPhone()));
        partnerInfoModel.fillInfoByLoveParam(partnerInfoParam);
        return partnerInfoModel;
    }

    @Override
    public List<PartnerApproveDTO> getInfoByBizId(Long bizId, int type) {
        return partnerApproveDao.getInfoByBizId(bizId, type);
    }

    @Override
    public int adjustApproveInfo(long id, CrowdfundingVolunteer approveVolunteer) {
        return partnerApproveDao.adjustApproveInfoById(id, approveVolunteer);
    }

    @Override
    public List<ApprovePartnerModel> getPartnerInfoOfApprove(int approveStatus, String approveUniqueCode) {
        List<ApprovePartnerModel> approvePartnerModels = new ArrayList<>();
        int tpye = 1;
        List<PartnerApproveDTO> infos;
        if (approveStatus == 0) {
            //当状态为0,全部
            infos = partnerApproveDao.getByApproveUniqueCode(approveUniqueCode, tpye);
        } else {
            infos = partnerApproveDao.getByApproveUniqueCodeAndStatus(approveStatus, approveUniqueCode, tpye);
        }
        if (CollectionUtils.isEmpty(infos)) {
            return approvePartnerModels;
        }
        for (PartnerApproveDTO info : infos) {
            ApprovePartnerModel partnerModel = new ApprovePartnerModel();
            CfPartnerInfoDo partnerInfo = cfPartnerInfoDao.getPartnerInfoByPartnerId(info.getBizId());
            if (Objects.isNull(partnerInfo)) {
                continue;
            }
            partnerModel.setUniqueCode(partnerInfo.getUniqueCode());
            partnerModel.setName(partnerInfo.getName());
            partnerModel.setApproveStatus(info.getApproveStatus());
            partnerModel.setApplicant(partnerInfo.getLeaderName());
            partnerModel.setAcountType(partnerInfo.getAccountType());
            partnerModel.setApproveId(info.getId());
//            partnerModel.setMobile(shuidiCipher.decrypt(partnerInfo.getEncryptPhone()));
            partnerModel.setCreateTime(partnerInfo.getCreateTime());
            partnerModel.setWorkStatus(partnerInfo.getWorkStatus());
            partnerModel.setId(partnerInfo.getId());
            approvePartnerModels.add(partnerModel);
        }
        //根据人员信息创建时间倒排序
        return approvePartnerModels.stream().sorted(Comparator.comparing(ApprovePartnerModel::getCreateTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }
}
