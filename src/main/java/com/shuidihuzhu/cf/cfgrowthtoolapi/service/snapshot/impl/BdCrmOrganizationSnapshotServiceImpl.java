package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.impl;

import com.google.common.cache.*;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdCrmDailyCycleDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdCrmOrganizationSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.BdCrmDailyCycleService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.BdCrmOrganizationSnapshotService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.snapshot.BdCrmOrganizationSnapshotDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 日目标组织快照(BdCrmOrganizationSnapshot)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-02 14:58:29
 */
@Slf4j
@Service("bdCrmOrganizationSnapshotService")
public class BdCrmOrganizationSnapshotServiceImpl implements BdCrmOrganizationSnapshotService {


    //缓存当天的快照组织数据,其他天的数据不缓存
    LoadingCache<String, List<BdCrmOrganizationDO>> allOrgList = CacheBuilder
            .newBuilder()
            .maximumSize(30)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<BdCrmOrganizationDO>>() {
                @Override
                public List<BdCrmOrganizationDO> load(String param) {
                    log.debug("设置当天的快照");
                    return getAllOrgByDB(param);
                }
            });

    @Resource
    private BdCrmOrganizationSnapshotDao bdCrmOrganizationSnapshotDao;

    @Autowired
    private ICrmSelfBuiltOrgReadService orgReadService;

    @Autowired
    private BdCrmDailyCycleService bdCrmDailyCycleService;

    @Override
    public BdCrmOrganizationSnapshotDO queryById(long id) {
        return bdCrmOrganizationSnapshotDao.queryById(id);
    }


    @Override
    public int insert(BdCrmOrganizationSnapshotDO bdCrmOrganizationSnapshot) {
        return bdCrmOrganizationSnapshotDao.insert(bdCrmOrganizationSnapshot);
    }

    @Override
    public void batchInsert(List<BdCrmOrganizationSnapshotDO> organizationSnapshotList) {
        bdCrmOrganizationSnapshotDao.batchInsert(organizationSnapshotList);
    }

    @Override
    public int update(BdCrmOrganizationSnapshotDO bdCrmOrganizationSnapshot) {
        return bdCrmOrganizationSnapshotDao.update(bdCrmOrganizationSnapshot);
    }

    @Override
    public boolean deleteById(long id) {
        return bdCrmOrganizationSnapshotDao.deleteById(id) > 0;
    }


    @Override
    public List<BdCrmOrganizationDO> findDirectSubOrgByOrgId(String dateKey, long orgId) {
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = getAllOrgByMixed(dateKey)
                .stream().filter(item -> item.getParentId() == orgId)
                .collect(Collectors.toList());
        List<Long> directOrgIds = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
        log.debug("组织id:{}获取直接下级组织:{}", orgId, directOrgIds);
        return bdCrmOrganizationDOList;
    }


    @Override
    public List<BdCrmOrganizationDO> findDirectSubOrgByOrgIdList(String dateKey, List<Long> orgIdList) {
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = getAllOrgByMixed(dateKey).stream().filter(item -> orgIdList.contains(item.getParentId()))
                .collect(Collectors.toList());
        List<Long> directOrgIds = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
        log.debug("组织idList:{}获取直接下级组织:{}", orgIdList, directOrgIds);
        return bdCrmOrganizationDOList;
    }


    @Override
    public List<BdCrmOrganizationDO> getAllOrgByMixed(String dateKey) {
        if (StringUtils.isEmpty(dateKey)) {
            log.info("dateKey为空");
            return Lists.newArrayList();
        }
        //如果当前时间超过了当天,使用实时组织
        DateTime dateTime = DateTime.parse(dateKey, GrowthtoolUtil.ymdfmt);
        if (dateTime.isAfter(DateTime.now().withTimeAtStartOfDay())) {
            //查看下是否有快照
            List<BdCrmDailyCycleDO> bdCrmDailyCycleDOS = bdCrmDailyCycleService.listByDateKey(Lists.newArrayList(dateKey));
            if (CollectionUtils.isEmpty(bdCrmDailyCycleDOS)) {
                return Lists.newArrayList();
            }
            return orgReadService.listNotTestOrg();
        }
        String dateKeyNow = DateTime.now().toString(GrowthtoolUtil.ymdfmt);
        if (Objects.equals(dateKeyNow, dateKey)) {
            try {
                return allOrgList.get(dateKey);
            } catch (Exception e) {
                log.error("获取当天的组织快照失败", e);
                return getAllOrgByDB(dateKey);
            }
        } else {
            return getAllOrgByDB(dateKey);
        }
    }


    private List<BdCrmOrganizationDO> getAllOrgByDB(String dateKey) {
        return bdCrmOrganizationSnapshotDao.listAllByDateKey(dateKey);
    }

    @Override
    public BdCrmOrganizationDO getCurrentOrgById(String dateKey, long orgId) {
        return getAllOrgByMixed(dateKey).stream().filter(item -> item.getId() == orgId).findFirst().orElse(null);
    }

    @Override
    public List<BdCrmOrganizationDO> listAllSubOrgIncludeSelf(String dateKey, long orgId) {
        BdCrmOrganizationDO currentOrgById = getCurrentOrgById(dateKey, orgId);
        if (currentOrgById == null) {
            return Lists.newArrayList();
        }
        List<BdCrmOrganizationDO> result = Lists.newArrayList(currentOrgById);
        listSubOrg(result, getAllOrgByMixed(dateKey), orgId);
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }




    private void listSubOrg(List<BdCrmOrganizationDO> result, List<BdCrmOrganizationDO> allOrgs, long orgId) {
        List<BdCrmOrganizationDO> subOrgs = allOrgs
                .stream()
                .filter(item -> item.getParentId() == orgId)
                .collect(Collectors.toList());
        result.addAll(subOrgs);
        for (BdCrmOrganizationDO subOrg : subOrgs) {
            listSubOrg(result, allOrgs, subOrg.getId());
        }
    }
}
