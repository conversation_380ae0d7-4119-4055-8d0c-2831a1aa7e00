package com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IPreposeMaterialDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IShuidiChouQyWxDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.shorturl.ShortUrlDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.payload.DelayMsgNoticePayload;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmReportMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.ISendAppPushCrmService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCrmMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IClewPreproseMaterialService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice.workwx.WorkWeiXinService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice.workwx.WorkWeixinContentBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.CrowdfundingUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @author: yangliming
 * @create: 2019/11/29
 */
@RefreshScope
@Slf4j
@Service
public class AppPushCrmReportMsgServiceImpl implements IAppPushCrmReportMsgService {

    @Autowired
    private ISendAppPushCrmService sendAppPushCrmServiceImpl;

    @Resource
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegateImpl;

    @Autowired
    private IClewPreproseMaterialService clewPreproseMaterialService;

    @Autowired
    private ICfVolunteerService cfVolunteerServiceImpl;

    @Autowired
    private IPreposeMaterialDelegate preposeMaterialDelegate;

    @Autowired
    private ShortUrlDelegate shortUrlDelegate;
    @Autowired
    private ICfBdCrmMsgService cfBdCrmMsgService;
    @Resource
    private WorkWeiXinService workWeiXinService;

    @Value("${apollo.bdcrm.provinceleader.approveurl:}")
    private String provinceLeaderApproveurl;

    @Value("${apollo.bdcrm.operator.approveurl:}")
    private String operatorApproveurl;

    @Value("${apollo.bdcrm.provinceleader.rejecturl:https://www.shuidichou.com/bd/report/#preposeMaterialId#/already-check?checkResultStatus=3&entrySource=1}")
    private String approveRejectUrl;
    @Autowired
    private IShuidiChouQyWxDelegate shuidiChouQyWxDelegate;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public DelayMsgNoticePayload sendMsg2BdForPreposeMaterial(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, String url, String title, String buttonText) {
        CfFirsApproveMaterial cfFirsApproveMaterial = crowdFundingFeignDelegateImpl.getAuthorInfoByInfoId(crowdfundingInfo.getId());
        UserInfoModel userInfoByUserId = crowdFundingFeignDelegateImpl.getUserInfoByUserId(crowdfundingInfo.getUserId());
        if (cfFirsApproveMaterial != null && cfVolunteer!=null && StringUtils.isNotEmpty(cfVolunteer.getMis())) {
            WorkWeixinContentBuilder cb = WorkWeixinContentBuilder.create()
                    .subject(title)
                    .payload("需报备案例", "<a href=\""+ shortUrlDelegate.process(GeneralConstant.caseBaseUrl + crowdfundingInfo.getInfoId())+"\">"+crowdfundingInfo.getTitle()+"</a>")
                    .payload("发起人手机号", CrowdfundingUtil.getTelephoneMask(userInfoByUserId==null?"": shuidiCipher.decrypt(userInfoByUserId.getCryptoMobile())))
                    .payload("患者姓名", CrowdfundingUtil.getNameMask(cfFirsApproveMaterial.getPatientRealName()))
                    .payload("案例发起时间", com.shuidihuzhu.common.web.util.DateUtil.getYmdhmsFromTimestamp(crowdfundingInfo.getCreateTime().getTime()));
            String commonMsg = cb.build();
            String operaterText = "<a href=\"" + url + "\">"+buttonText+"</a>";
            cb.payload("操作", operaterText);
            String content = cb.build();
            workWeiXinService.sendByVolunteers(Lists.newArrayList(cfVolunteer), content);
            String subTitle = "需报备案例: "+crowdfundingInfo.getTitle();
            sendAppPushCrmServiceImpl.pushCrmMsg(cfVolunteer.getMobile(),"您有一条案例未报备通知", subTitle, content);
            cfBdCrmMsgService.saveCfBdCrmMsg(title, subTitle, content, cfVolunteer.getMis(), cfVolunteer.getUniqueCode());
            // 发delay 24小时的消息
            Map<String,Object> map = Maps.newHashMap();
            map.put("title","您有一条案例未报备通知");
            map.put("subTitle",subTitle);
            map.put("operaterText",operaterText);
            map.put("volunteer",cfVolunteer);
            map.put("commonMsg",commonMsg);
            DelayMsgNoticePayload model = new DelayMsgNoticePayload(cfVolunteer.getUniqueCode(),
                    Long.valueOf(crowdfundingInfo.getId()),
                    crowdfundingInfo.getInfoId(),
                    DelayMsgNoticePayload.DelayMsgEnum.CASE_AFTER_PREPOSEMASTERIAL,
                    map);
            return model;
        }
        return null;
    }

}
