package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfGrowthtoolKpiLotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfGrowthtoolKpiLotService;
import com.shuidihuzhu.cf.client.performance.PepClient;
import com.shuidihuzhu.cf.client.performance.calResult.ProcedureForCModel;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @author: fengxuan
 * @create 2024-03-04 17:03
 **/
@Slf4j
@Service
public class PepLotObtainService {

    @Autowired
    private ICfGrowthtoolKpiLotService cfGrowthtoolKpiLotService;

    @Autowired
    private PepClient pepClient;

    @Autowired
    private ApolloService apolloService;


    public List<CfGrowthtoolKpiLotDO> listKpiLotDO(PepPushEnum pepPushEnum) {
        String templateMapString = apolloService.getTemplateLotMap();
        if (StringUtils.isBlank(templateMapString)) {
            log.info("没有配置模板相关信息");
            return Lists.newArrayList();
        }
        if (pepPushEnum == null) {
            log.info("请确认推送数据是否有配置相关的关键信息");
            return Lists.newArrayList();
        }
        Map<String, Long> pepMap = JSON.parseObject(templateMapString, new TypeReference<Map<String, Long>>() {});
        Long memberTemplate = pepMap.get(pepPushEnum.getCode());
        if (memberTemplate == null) {
            log.info("没有配置模板:{}", pepPushEnum.getDesc());
            return Lists.newArrayList();
        }
        List<CfGrowthtoolKpiLotDO> cfGrowthtoolKpiLotDOList = cfGrowthtoolKpiLotService.listByTemplateIdAndBizType(memberTemplate, pepPushEnum.getSubBizType().getBizType().getCode(), pepPushEnum.getSubBizType().getCode());
        if (CollectionUtils.isEmpty(cfGrowthtoolKpiLotDOList)) {
            log.info("没有批次信息:{}", pepPushEnum.getDesc());
            return Lists.newArrayList();
        }
        List<CfGrowthtoolKpiLotDO> result = Lists.newArrayList();
        for (CfGrowthtoolKpiLotDO cfGrowthtoolKpiLotDO : cfGrowthtoolKpiLotDOList) {
            //这里控制下,如果公示期已经超过了当前时间,就不再推送数据
            long procedureId = cfGrowthtoolKpiLotDO.getProcedureId();
            Response<ProcedureForCModel> procedureForCModelResponse = pepClient.getByProduceId(procedureId);
            if (procedureForCModelResponse.notOk() || procedureForCModelResponse.getData() == null) {
                log.info("获取程序信息失败:{}", procedureForCModelResponse);
                continue;
            }
            Date endTime = procedureForCModelResponse.getData().getEndTime();
            if (endTime != null && endTime.before(new Date())) {
                log.info("结算结束不需要推送数据了:{},模板:{}", endTime, pepPushEnum.getDesc());
                continue;
            }
            result.add(cfGrowthtoolKpiLotDO);
        }
        if (CollectionUtils.isEmpty(result)) {
            log.info("没有业务批次:{}", pepPushEnum.getDesc());
            return Lists.newArrayList();
        }
        return result;
    }


    public List<Long> getLotIds(PepPushEnum pepPushEnum) {
        List<Long> lotIds = Lists.newArrayList();
        listKpiLotDO(pepPushEnum).forEach(cfGrowthtoolKpiLotDO -> lotIds.addAll(cfGrowthtoolKpiLotDO.parseLotIds()));
        if (CollectionUtils.isEmpty(lotIds)) {
            log.info("没有业务批次:{}", pepPushEnum.getDesc());
            return Lists.newArrayList();
        }
        return lotIds;
    }



}
