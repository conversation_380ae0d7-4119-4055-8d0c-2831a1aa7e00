package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPerformanceCityRuleDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiStatVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKPIPerformanceVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.KpiSearchParam;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-08-03 14:24
 */
public interface ICfKpiPerformanceCityRuleService {
    /**
     * @author: wanghui
     * @time: 2020-08-06 16:22
     * @description: getPerformanceCityname 获得 某个月份 下该城市类型中的城市名
     * @param: [monthKey, cityType]
     * @return: com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPerformanceCityRuleDO
     */
    CfKpiPerformanceCityRuleDO getPerformanceCityname(String monthKey, Integer cityType);
    /**
     * @author: wanghui
     * @time: 2020-08-06 16:23
     * @description: getPerformance 查询指定 level+monthKey+cityType 的规则  cityNameStr是当cityType为单个城市时使用
     * @param: [cfKPIPerformance]
     * @return: com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPerformanceCityRuleDO
     * @param cfKPIPerformance
     */
    CfKpiPerformanceCityRuleDO getPerformance(CfKpiPerformanceCityRuleDO cfKPIPerformance);

    /**
     * @author: wanghui
     * @time: 2020-08-06 16:24
     * @description: getPerformanceByCityName 查询指定 level+monthKey+cityType 的规则 cityName 是作为模糊匹配使用
     * @param: [monthKey, level, cityType, cityName]
     * @return: com.shuidihuzhu.cf.response.OpResult<com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPerformanceCityRuleDO>
     */
    OpResult<CfKpiPerformanceCityRuleDO> getPerformanceByCityName(String monthKey, Integer level, Integer cityType, String cityName);

    OpResult<Long> saveOrUpdatePerformance(CfKpiPerformanceCityRuleDO data);

    CfKpiPerformanceCityRuleDO getPerformance(Long id);

    /**
     * @author: wanghui
     * @time: 2020-08-06 16:26
     * @description: getPerformanceList 规则列表页 查询方法
     * @param: [searchParam]
     * @return: com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel<com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKPIPerformanceVO>
     */
    CommonResultModel<CfKPIPerformanceVO> getPerformanceList(KpiSearchParam searchParam);

    List<CfKpiPerformanceCityRuleDO> listAllRule(String monthKey);

    /**
     * @author: wanghui
     * @time: 2020-08-06 16:26
     * @description: changeStatusOtherMonthKey 修改除 monthKey以后其他月份规则为失效状态
     * @param: [monthKey]
     * @return: void
     */
    void changeStatusOtherMonthKey(String monthKey);

    int repairePreformanceCustomType(long id);

    List<CfKpiPerformanceCityRuleDO> listPerformanceByCfKpiStatVO(CfKpiStatVO cfKpiStatVO);

    CfKpiPerformanceCityRuleDO getPerformanceNew(CfKpiPerformanceCityRuleDO cfKPIPerformance);

    List<CfKpiPerformanceCityRuleDO> getPerformanceRulesByType(String monthKey, Integer level, int performanceType);
}
