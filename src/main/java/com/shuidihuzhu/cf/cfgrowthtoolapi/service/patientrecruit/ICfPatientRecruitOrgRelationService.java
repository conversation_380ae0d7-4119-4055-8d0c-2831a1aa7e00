package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientrecruit;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.CfPatientRecruitOrgRelationVo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-10
 */
public interface ICfPatientRecruitOrgRelationService {

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(List<CfPatientRecruitOrgRelationVo> list);

    /**
     * 批量更新
     * @param list
     * @return
     */
    int batchUpdate(List<CfPatientRecruitOrgRelationVo> list);

    /**
     * 全部删除
     *
     * @return
     */
    int allUpdateDelete();

    /**
     * 置为逻辑删除
     * @param id
     * @return
     */
    int updateDelete(long id);

    /**
     * 计算总数
     * @param type
     * @param misName
     * @param mis
     * @param orgName
     * @return
     */
    int countCfPatientRecruit(int type, String misName, String mis, String orgName);

    /**
     * 招募经理列表数据
     * @param type
     * @param misName
     * @param mis
     * @param orgName
     * @return
     */
    List<CfPatientRecruitOrgRelationVo> listCfPatientRecruit(int type, String misName, String mis, String orgName);

    /**
     * 获取所有有效的数据
     * @return
     */
    List<CfPatientRecruitOrgRelationVo> listAllValidData();

    /**
     * 获取招募经理list
     * @param orgIds
     * @return
     */
    List<CfPatientRecruitOrgRelationVo> getPatientManagerByOrgIds(List<Long> orgIds);
}
