package com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund;

import brave.Tracing;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.caserefund.CfAllWaitDealBaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.caserefund.CfWaitDealCountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.caserefund.CfWaitTaskDealModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.caserefund.CfCaseRefundDetailParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolAsyncPoolConstants;
import com.shuidihuzhu.client.cf.growthtool.enums.VolunteerEnums;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/8/23 1:13 PM
 */
@Slf4j
@Service
public class CfWaitDealQueryService {

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrganizationService;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private CfWaitDealTypeRecordService cfWaitDealTypeRecordService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Resource(name = GrowthtoolAsyncPoolConstants.WAIT_DEAL_QUERY_POOL)
    private ExecutorService executorService;


    public CfWaitDealCountModel getWaitDealManage(CfCaseRefundDetailParam caseRefundDetailParam, List<Long> orgIds) {
        CfWaitDealCountModel result = new CfWaitDealCountModel();
        //如果管理的组织多余一个，需要展示
        boolean needSpecialShow = orgIds.size() > 1;
        for (Long singleOrgId : orgIds) {
            CfWaitDealCountModel taskCountModel = getWaitDealCountModel(singleOrgId, caseRefundDetailParam);
            if (needSpecialShow) {
                BdCrmOrganizationDO currentOrg = crmSelfBuiltOrganizationService.getCurrentOrgById(singleOrgId);
                Optional.of(taskCountModel)
                        .map(CfWaitDealCountModel::getWaitTaskDealModels)
                        .orElse(Lists.newArrayList())
                        .forEach(item -> {
                            if (item.getOrgOrMis() == 0) {
                                item.setName(currentOrg.getOrgName() + "-" + item.getName());
                            }
                        });
            }
            log.debug("组织:{}返回的数据:{}", singleOrgId, JSON.toJSONString(taskCountModel));
            //对数据做聚合
            result.calCnt(taskCountModel);
        }
        sortWaitTaskDealModel(result);
        return result;
    }


    private void sortWaitTaskDealModel(CfWaitDealCountModel result) {
        List<CfWaitTaskDealModel> donateTaskModelList = Optional.ofNullable(result.getWaitTaskDealModels())
                .orElse(Lists.newArrayList());
        //人员需要排除重复
        Map<String, List<CfWaitTaskDealModel>> uniqueKeyMap = donateTaskModelList.stream()
                .filter(item -> item.getOrgOrMis() == 1)
                .collect(Collectors.groupingBy(CfWaitTaskDealModel::getUniqueKey));
        List<CfWaitTaskDealModel> models = Lists.newArrayList();
        for (Map.Entry<String, List<CfWaitTaskDealModel>> entry : uniqueKeyMap.entrySet()) {
            List<CfWaitTaskDealModel> value = entry.getValue();
            CfWaitTaskDealModel volunteerModel = value.get(0);
            models.add(volunteerModel);
        }
        //sort
        List<CfWaitTaskDealModel> sortedOrgModelList = donateTaskModelList.stream()
                .filter(item -> item.getOrgOrMis() != 1).collect(Collectors.toList())
                .stream()
                .sorted(Comparator.comparing(CfWaitTaskDealModel::getOrgAttribute).reversed())
                .collect(Collectors.toList());
        models.addAll(sortedOrgModelList);
        result.setWaitTaskDealModels(models);
    }


    private CfWaitDealCountModel getWaitDealCountModel(long singleOrgId, CfCaseRefundDetailParam caseRefundDetailParam) {
        CfWaitDealCountModel result = new CfWaitDealCountModel();
        List<CfWaitTaskDealModel> cfWaitTaskDealModels = Lists.newArrayList();
        //orgId为叶子结点直接展示
        CompletableFuture<List<CfWaitTaskDealModel>> orgCfModelFuture = CompletableFuture.supplyAsync(() -> getOrgWaitDealTaskModels(singleOrgId, caseRefundDetailParam), executorService);
        CompletableFuture<List<CfWaitTaskDealModel>> volunteerModelFuture = CompletableFuture.supplyAsync(() -> getMemberWaitDealTaskModels(singleOrgId, caseRefundDetailParam), executorService);
        try {
            CompletableFuture.allOf(orgCfModelFuture, volunteerModelFuture).get();
            cfWaitTaskDealModels.addAll(orgCfModelFuture.get());
            cfWaitTaskDealModels.addAll(volunteerModelFuture.get());
        } catch (Exception e) {
            log.error("getWaitDealCountModel查询异常", e);
            return result;
        }
        crmMemberInfoService.resetStaffStatus(cfWaitTaskDealModels);
        result.setWaitTaskDealModels(cfWaitTaskDealModels);
        result.calByCfWaitDealModel();
        return result;
    }


    //下级组织中任务情况
    public List<CfWaitTaskDealModel> getOrgWaitDealTaskModels(long orgId, CfCaseRefundDetailParam caseRefundDetailParam) {
        boolean noLeafNode = Optional.ofNullable(crmSelfBuiltOrganizationService.getCurrentOrgById(orgId))
                .map(item -> item.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode())
                .orElse(true);
        if (!noLeafNode) {
            return Lists.newArrayList();
        }
        List<CfWaitTaskDealModel> result = Lists.newArrayList();
        //获取直接子级组织
        List<BdCrmOrganizationDO> directSubOrgs = crmSelfBuiltOrganizationService.findDirectSubOrgByOrgId(orgId);
        for (BdCrmOrganizationDO directSubOrg : directSubOrgs) {
            //排除测试组织
            if (Objects.equals(directSubOrg.getId(), apolloService.getTestOrgId())) {
                continue;
            }
            //获取所有的子级节点
            List<Long> allSubOrgIds = crmSelfBuiltOrganizationService.listAllSubOrgIncludeSelf(directSubOrg.getId())
                    .stream()
                    .map(BdCrmOrganizationDO::getId)
                    .collect(Collectors.toList());

            CfWaitTaskDealModel waitDealTaskByOrgIds = cfWaitDealTypeRecordService.getWaitDealTaskByOrgIds(caseRefundDetailParam.getStartTime(), caseRefundDetailParam.getEndTime(), caseRefundDetailParam.getDealResult(), allSubOrgIds);
            waitDealTaskByOrgIds.setUniqueKey(String.valueOf(directSubOrg.getId()));
            waitDealTaskByOrgIds.setName(directSubOrg.getOrgName());
            waitDealTaskByOrgIds.setOrgAttribute(directSubOrg.getOrgAttribute());
            // 对组织下所有待办数做聚合
            waitDealTaskByOrgIds.calTotalWaitDeal(waitDealTaskByOrgIds);
            result.add(waitDealTaskByOrgIds);
        }
        return result;
    }


    //组织下人员的情况
    private List<CfWaitTaskDealModel> getMemberWaitDealTaskModels(long orgId, CfCaseRefundDetailParam caseRefundDetailParam) {
        //查看当前组所有的人员,做一个数据的merge
        Map<String, BdCrmOrgUserRelationDO> uniqueCodeTRelation = crmOrganizationRelationService.listByOrgIds(Lists.newArrayList(orgId))
                .stream()
                .collect(Collectors.toMap(BdCrmOrgUserRelationDO::getUniqueCode, Function.identity(), (before, after) -> before));
        //核心
        List<CfWaitTaskDealModel> cfWaitTaskDealModels = cfWaitDealTypeRecordService.groupByUniqueCode(orgId, caseRefundDetailParam.getDealResult(), caseRefundDetailParam.getStartTime(), caseRefundDetailParam.getEndTime());
        List<String> uniqueCodes = cfWaitTaskDealModels.stream().map(CfWaitTaskDealModel::getUniqueKey).collect(Collectors.toList());

        //根据uniqueCodes查询顾问信息添加顾问角色
        Map<String, CrowdfundingVolunteer> volunteerMap = cfVolunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodes)
                .stream()
                .collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity(), (before, after) -> before));
        for (CfWaitTaskDealModel cfWaitTaskDealModel : cfWaitTaskDealModels) {
            cfWaitTaskDealModel.setVolunteerRole(CrowdfundingVolunteerEnum.RoleEnum.parse(volunteerMap.get(cfWaitTaskDealModel.getUniqueKey()).getLevel()).getDesc());
        }
        //添加下没有数据的人员
        for (Map.Entry<String, BdCrmOrgUserRelationDO> item : uniqueCodeTRelation.entrySet()) {
            if (!uniqueCodes.contains(item.getKey())) {
                BdCrmOrgUserRelationDO value = item.getValue();
                CfWaitTaskDealModel countModel = new CfWaitTaskDealModel();
                countModel.setUniqueKey(value.getUniqueCode());
                countModel.setName(value.getMisName());
                cfWaitTaskDealModels.add(countModel);
            }
        }

        //标记下是否转岗,在caseCountModels且没有离职的,不在uniqueCodeTRelation中的
        cfWaitTaskDealModels.stream()
                .filter(item -> !uniqueCodeTRelation.containsKey(item.getUniqueKey()))
                .forEach(item -> {
                    item.setStaffStatus("转岗");
                });
        cfWaitTaskDealModels.forEach(item -> item.setOrgOrMis(1));

        log.info(this.getClass().getSimpleName() + "getMemberWaitDealTaskModels result:{}", JSON.toJSONString(cfWaitTaskDealModels));
        return cfWaitTaskDealModels;
    }


    //根据人员查询总数
    public CfAllWaitDealBaseModel queryByUniqueCode(CfCaseRefundDetailParam cfCaseRefundDetailParam, List<Long> orgIds) {
        String startTime = cfCaseRefundDetailParam.getStartTime();
        String endTime = cfCaseRefundDetailParam.getEndTime();

        CfWaitTaskDealModel cfMeberWaitTaskDealCount = Optional.ofNullable(cfWaitDealTypeRecordService.getTaskModelByUniqueCode(startTime, endTime, cfCaseRefundDetailParam.getUniqueCode())).orElse(new CfWaitTaskDealModel());
        CfWaitTaskDealModel cfTeamWaitTaskDealCount = this.queryByOrgId(startTime, endTime, orgIds);

        CfAllWaitDealBaseModel cfAllWaitDealBaseModel = new CfAllWaitDealBaseModel();
        if (Objects.nonNull(cfTeamWaitTaskDealCount)) {
            cfAllWaitDealBaseModel.setInitMeberWaitTaskCount(cfMeberWaitTaskDealCount.getInitWaitTaskCount());
            cfAllWaitDealBaseModel.setFinishMeberWaitTaskCount(cfMeberWaitTaskDealCount.getFinishWaitTaskCount());
            cfAllWaitDealBaseModel.setOverMeberWaitTaskCount(cfMeberWaitTaskDealCount.getOverWaitTaskCount());
            cfAllWaitDealBaseModel.setInitTeamWaitTaskCount(cfTeamWaitTaskDealCount.getInitWaitTaskCount());
            cfAllWaitDealBaseModel.setFinishTeamWaitTaskCount(cfTeamWaitTaskDealCount.getFinishWaitTaskCount());
            cfAllWaitDealBaseModel.setOverTeamWaitTaskCount(cfTeamWaitTaskDealCount.getOverWaitTaskCount());
        }
        return cfAllWaitDealBaseModel;
    }

    //根据组织查询待办任务总数
    private CfWaitTaskDealModel queryByOrgId(String startTime, String endTime, List<Long> orgIds) {
        List<Long> allSubOrgIds = Lists.newArrayList();
        for (Long orgId : orgIds) {
            allSubOrgIds.addAll(crmSelfBuiltOrganizationService.listAllSubOrgIncludeSelf(orgId)
                    .stream()
                    .map(BdCrmOrganizationDO::getId)
                    .collect(Collectors.toList()));

        }
        return Optional.ofNullable(cfWaitDealTypeRecordService.getTaskModelByOrgIds(startTime, endTime, allSubOrgIds)).orElse(new CfWaitTaskDealModel());
    }

}
