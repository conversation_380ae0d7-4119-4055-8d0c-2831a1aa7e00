package com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage;


import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.context.CaseRaiseContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.caserefund.CfCaseRefundTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.BdCaseDonateTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.greenchannel.CfClewGreenChannelApplyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfCaseHospitalDepartmentApplyRecordDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.legal.BdCrmLegalContractDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponBudgetDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.approve.DepartmentApproveContent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.approve.GrApproveContent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.RemoteCaseTagModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.DonateConfig;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.greenchannel.fill.FilledLastConfirm;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.message.HospitalEditMessage;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.incentive.IncentiveTaskDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.message.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.payload.DelayMsgNoticePayload;
import com.shuidihuzhu.cf.client.adminpure.model.report.ReportOperationPayload;
import com.shuidihuzhu.cf.finance.model.CfFinanceBroadcastMsg;
import com.shuidihuzhu.cf.finance.model.CfSubsidySuccessMsgModel;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.admin.model.*;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestNoticeModel;

import java.util.*;

/**
 * @author: yangliming
 * @create: 2019/11/29
 */
public interface IAppPushCrmCaseMsgService {

    void sendMsg2BdForPaySuccess(UserInfoModel userInfoByUserId, CrowdfundingInfo crowdfundingInfo, CrowdfundingVolunteer volunteer,
                                 CfSubsidySuccessMsgModel cfSubsidySuccessMsgModel);

    void sendRecoverCaseMsgToBd(CrowdfundingInfo crowdfundingInfo, CrowdfundingVolunteer cfVolunteer, UserInfoModel userInfo);

    void sendMsg2ProvincialManager(CrowdfundingVolunteer cfVolunteer, String content);

    void sendMsgPaySuccessCaseContent(CrowdfundingInfo crowdfundingInfo, CrowdfundingVolunteer cfVolunteer, UserInfoModel userInfo);

    void sendMsgCaseChushenResult(CrowdfundingInfo crowdfundingInfo, CrowdfundingVolunteer cfVolunteer, UserInfoModel userInfoModel,
                                  String refuseComment, String result);

    void sendCaseEndMsgToBd(CrowdfundingInfo crowdfundingInfo, CrowdfundingVolunteer cfVolunteer, UserInfoModel userInfo, CaseEndModel caseEndModel);

    void sendRestoreFundraisingMsgToBd(CrowdfundingInfo crowdfundingInfo, CrowdfundingVolunteer cfVolunteer, UserInfoModel userInfo);


    void sendMsg2Bd(CrowdfundingVolunteer cfVolunteer, String content, String title, String subTitle);

    // 退款成功
    void refundSuccessMsg(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial, UserInfoModel userInfoByUserId);

    // 退款失败
    void refundFailedMsg(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial, UserInfoModel userInfoByUserId, String remark);

    // 付款失败

    void drawFailedOneMsg(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial, UserInfoModel userInfoByUserId, String remark);

    // 提现审核驳回

    void drawAuditRejectMsg(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial, UserInfoModel userInfoByUserId, CfFinanceBroadcastMsg cfFinanceBroadcastMsg);

    // 提现审核通过  （公示也是在此之后）

    void drawkAuditMsg(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial, UserInfoModel userInfoByUserId, CfFinanceBroadcastMsg cfFinanceBroadcastMsg);

    // 提现审核驳回 24小时提醒
    void tixianRejectMsg(DelayMsgNoticePayload delayMsgNoticePayload);

    // 初审审核驳回 24小时提醒
    void chushenRejectMsg(DelayMsgNoticePayload delayMsgNoticePayload);

    // 材料审核驳回 24小时提醒
    void chailiaoRejectMsg(DelayMsgNoticePayload delayMsgNoticePayload);

    // 发起案例未报备 24小时提醒
    void caseAfterPreposemasterialMsg(DelayMsgNoticePayload delayMsgNoticePayload);

	void sendTwiceCaseEndMsg(InitialAuditTwiceEndCaseInfo initialAuditTwiceEndCaseInfo, CrowdfundingVolunteer cfVolunteer);

	String getContent(CrowdfundingInfo crowdfundingInfo, CrowdfundingVolunteer cfVolunteer, UserInfoModel userInfo, String caseUrl, int type);

	void sendApproveContentMsg(String volunteerName, CrowdfundingVolunteer suppirVolunteer);

    /**
     * 发起退款申请消息推送
     * @param cfVolunteer
     * @param crowdfundingInfo
     * @param cfFirsApproveMaterial
     * @param userInfoByUserId
     * @param executionTime
     */
    void refundApplyMsg(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial, UserInfoModel userInfoByUserId, Date executionTime);

    /**
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=561348934
     * 发送顾问发起案例 免提现手续费消息通知
     * @param cfVolunteer
     * @param crowdfundingInfo
     * @param cfFirsApproveMaterial
     * @param userInfoByUserId
     * @param desc
     */
    void sendFreeHandleFee(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial, UserInfoModel userInfoByUserId, String desc);

    /**
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=561348934
     * 发送顾问发起案例 免提现手续费到账通知
     * @param cfVolunteer
     * @param crowdfundingInfo
     * @param cfFirsApproveMaterial
     * @param userInfoByUserId
     * @param desc
     * @param cfFinanceBroadcastMsg
     */
    void sendFreeHandleFeeArrive(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial, UserInfoModel userInfoByUserId, String desc,CfFinanceBroadcastMsg cfFinanceBroadcastMsg);

    void refundAuditMsg(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial, UserInfoModel userInfoByUserId, Date executionTime);

    void refundAuditRejectMsg(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial, UserInfoModel userInfoByUserId, CfFinanceBroadcastMsg cfFinanceBroadcastMsg);

    void sendQQGaoQianMsg(CaseLabel4QQ caseLabel4QQ, CrowdfundingVolunteer crowdfundingVolunteer);
    //待录入新增医院确认
    void addHospitalNotify(CfWaitHandleHospitalDO waitHandleHospitalDO, String submitMisName, CrowdfundingVolunteer leaderVolunteer);

    //子预算下发成功
    void budgetAssignSuc(WeaponPushMessageModel pushMessageModel, CrowdfundingVolunteer leaderVolunteer);

    //武器申请需要人工审批
    void applyWeaponTLeader(String weaponName, String applyReason, int hours, CrowdfundingVolunteer leaderVolunteer, String applyName);

    //武器审核驳回
    void notifyWeaponRejectByLeader(String weaponName, String leaderRejectReason, int caseId, CrowdfundingVolunteer applyVolunteer);

    //武器运营驳回
    void notifyWeaponRejectByActivity(WeaponCommonMessageModel commonMessageModel);

    //武器过时未审批
    void notifyWeaponApplyTimeOut(String weaponName, int hours, int caseId, CrowdfundingVolunteer applyVolunteer, CrowdfundingVolunteer leaderVolunteer);

    void notifyWeaponPassByActivity(String weaponName, int caseId, CrowdfundingVolunteer applyVolunteer, GreenChannelWeaponMessage greenChannelWeaponMessage);

    //医院纠正，申请人告知结果
    void hospitalRedressMessage(HospitalEditMessage hospitalEditMessage);

    void sendQualityTestFirstResultNotice(List<CrowdfundingVolunteer> volunteers, CfGwReplaceInputQualityTestNoticeModel noticeModel, Long id);

    void sendQualityTestResultNoticeDelay24Hour(Long id, String subTitle, List<CrowdfundingVolunteer> volunteers, String commonMsg, String closeMsg, String expireTime);

    void sendQualityTestResultDelayNotice(DelayMsgNoticePayload noticePayload);

    void sendQualityTestUnCommitForQualityDelayMsg(DelayMsgNoticePayload delayMsgNoticePayload);

    void sendQualityTestAutoEndForQualityDelayMsg(DelayMsgNoticePayload delayMsgNoticePayload);

    void sendQualityTestResultNotice(List<CrowdfundingVolunteer> leaderList, CfGwReplaceInputQualityTestNoticeModel noticeModel, Long id);

    void sendQualityTestAppealWaitApproveMsg(CfGwReplaceInputAppealDO appealDO, CrowdfundingVolunteer leaderVolunteer);

    void sendQualityTestLeaderApproveResult2Bd(CfGwReplaceInputAppealDO appealDO);

    void greenChannelOffline(CfClewGreenChannelApplyDO channelApplyDO, FilledLastConfirm filledLastConfirm, String predicateTime, String publishChannel, String linkToPublish, String titleToPublish);

    OpResult<Void> sendCaseInitMsg2Bd(CrowdfundingVolunteer volunteerUniqueCode, String content);

    //科室不存在通知
    void departmentNotExist(DepartmentApproveContent departmentRecord, CrowdfundingVolunteer leaderVolunteer, String volunteerName, CfGrowthtoolApproveDO approveDO);

    void sendOfficialAnnounceMsg(CfBdcrmOfficialAnnounceDO cfBdcrmOfficialAnnounceDO, List<CrowdfundingVolunteer> volunteers);

    void sendCaseReportOrderMsg(ReportOperationPayload reportOperationPayload, CrowdfundingVolunteer crowdfundingVolunteer, CrowdfundingInfo crowdfundingInfo, CfFirsApproveMaterial cfFirsApproveMaterial);
    //预算借调消息
    void sendLoanWeaponMessage(WeaponLoanMessage weaponLoanMessage);

    void sendObjectiveManageUnCommitIndicatorMsg(List<String> uniqueCodeList, Integer objectiveType);

    void sendObjectiveManageUnCommitIndicatorValueMsg(List<String> uniqueCodeList);

    void sendIndicatorValueConfigMsg(List<String> uniqueCodeList, CfBdCrmObjectiveCycle objectiveCycle, Long orgId);

    void sendApproveHeaderMsg(CfGrowthtoolApproveDO cfGrowthtoolApproveDO, Integer approveStatus);

    void sendApproveWorkCardMsg(CfGrowthtoolApproveDO cfGrowthtoolApproveDO, Integer approveStatus);

    void sendApproveHonourPicMsg(CfGrowthtoolApproveDO cfGrowthtoolApproveDO, Integer approveStatus, boolean needRemark);

    void sendDiagnoseResultMsg(CfBdCrmDiagnoseCronTaskDO cronTaskDO, CfBdCrmDiagnoseResultDO cfBdCrmDiagnoseResultDO);

    void changeApproveMis(CrowdfundingVolunteer volunteerUniqueCode, int count);

    void sendUploadExceptionLocation2Leader(CrowdfundingVolunteer gwInfo, CfCrmGpsDO cfCrmGpsDO);

    void sendApproveCardReport(String volunteerName, CrowdfundingVolunteer volunteer);

    void approveCardReportByLeader(CrowdfundingVolunteer volunteerUniqueCode, CardReportRelationDO cardReportRelationDO, int approveStatus);

    void notifyGrSign(CrowdfundingVolunteer volunteer, GrApproveContent grApproveContent);

    void notifyGrFeedback(CrowdfundingVolunteer volunteer, GrApproveContent grApproveContent, String feedbackOptor);

    void warnLoveHomeUnUse(CrowdfundingVolunteer volunteer, CfWeaponBudgetDO budgetDO, Set<String> notApplyName);

    //通知已经审核通过的上级,申请被驳回
    void notifyWeaponApproveReject(String weaponName, String leaderRejectReason, int caseId, CrowdfundingVolunteer approveVolunteer, String applyName);

    //账单提醒
    void alarmPartnerBillCreate(PartnerBillCreateMsg partnerBillCreateMsg);

    //待录入生成线索失败
    void notifyCreateClewFail(RecruitPushMsg recruitPushMsg);

    void noticeIncentiveTaskForGw(IncentiveTaskDTO incentiveTaskDTO, CrowdfundingVolunteer volunteer);
    //筹款金额调整提醒
    void notifyAlterAmount(CfBdCaseInfoDo cfBdCaseInfoDo, AdminCfModifyAmountMessage amountMessageModel,CrowdfundingVolunteer volunteer,CrowdfundingInfo crowdfundingInfo);

    //待审批-异地案例
    void remoteCaseApply(CrowdfundingVolunteer approveVolunteer, CrowdfundingVolunteer leader, RemoteCaseTagModel remoteCaseTagModel, boolean needShowOffsite);

    //待审批-2小时未审批
    void remoteCaseRemind(CrowdfundingVolunteer approveVolunteer, RemoteCaseTagModel remoteCaseTagModel, String time);

    //审批驳回
    void remoteCaseApprove(int approveStatus, String rejectReason,
                           CrowdfundingVolunteer volunteer, CrowdfundingVolunteer leader, RemoteCaseTagModel remoteCaseTagModel);

    void rejectBySea(String rejectReason, CrowdfundingVolunteer leader, CrowdfundingVolunteer volunteer, RemoteCaseTagModel remoteCaseTagModel);

    void sendDonateTaskCreate(DonateConfig donateConfig, BdCaseDonateTaskDO donateTaskDO, CrowdfundingVolunteer volunteer);

    void sendDonateMonitor(DonateConfig donateConfig, BdCaseDonateTaskDO donateTaskDO, CrowdfundingVolunteer volunteer);

    //案例退款顾问跟进任务
    void sendRefundTaskCreate(CrowdfundingVolunteer volunteer, CfBdCaseInfoDo cfBdCaseInfoDo, CfCaseRefundTaskDO cfCaseRefundTaskDO);

    //案例退款用户取消
    void sendRefundTaskCancle(CrowdfundingVolunteer volunteer, CfBdCaseInfoDo cfBdCaseInfoDo, CfCaseRefundTaskDO caseRefundTaskInfo);

    //异地案例标签消息
    void sendLocalCityTagMsg(BdDelegateCaseTagRecordDO caseTagRecordDO, CfBdCaseInfoDo cfBdCaseInfoDo);

    void sendKuaiShouApplyRejectMsg(CrowdfundingVolunteer volunteer, CfKuaiShouApplyDO cfKuaiShouApplyDO);

    void sendKuaiShouApplyAgreeMsg(CrowdfundingVolunteer volunteer, CfKuaiShouApplyDO cfKuaiShouApplyDO);

    void sendFirstReportDealProgresMsg(CrowdfundingVolunteer volunteer, ReportOperationPayload reportOperationPayload, CfBdCaseInfoDo cfBdCaseInfoDo);

    void sendLegalPaySuc(BdCrmLegalContractDO bdCrmLegalContractDO);

    void sendUrgeWaitDealMsg(CrowdfundingVolunteer volunteer, CrowdfundingVolunteer waitVolunteer, CfBdCaseInfoDo cfBdCaseInfoDo);

    void sendRecruitClewFromC(RecruitClewFromCMessage recruitClewFromCMessage);

    void sendBigAmountNotify(CaseRaiseContext caseRaiseContext, CrowdfundingVolunteer cfVolunteer);

    void sendCaseStatMsg(CrowdfundingVolunteer volunteer);

    void sendMaintainHospitalOrDepartment(CrowdfundingVolunteer volunteer, String content);

    void remindConfirmMsg(CrowdfundingVolunteer volunteer, UserConfirmRemindMessage modelCreateByDraft);

    void remindTaskMsg(CrowdfundingVolunteer volunteer, int unFinishTaskCount);

    //案例医院科室变更需要人工审批
    void applyCaseHospitalDepartmentLeader(CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo, CrowdfundingVolunteer leaderVolunteer);

    void applyCaseHospitalDepartmentLeaderPass(CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo);

    void applyCaseHospitalDepartmentLeaderReject(CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo);
}
