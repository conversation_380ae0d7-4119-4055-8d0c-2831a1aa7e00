package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.export.BudgetDetailExportModelForBase;
import lombok.Data;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-12-09 10:07 下午
 **/
public interface IWeaponExcelService {

    default boolean supportActivityType(int activityType) {
        return false;
    }

    /**
     *
     * @param beHandler: 被处理的对象
     * @param handlerParam：参数信息
     */
    default List<BudgetDetailExportModelForBase> postProcess(List<BudgetDetailExportModelForBase> beHandler, ExcelNeedModel handlerParam) {
        return beHandler;
    }

    void exportExcel(HttpServletResponse response, int budgetGroupId, String startTime, String endTime, long adminLongUserId);

    @Data
    class ExcelNeedModel {
        CfWeaponBudgetGroupDO budgetGroupDO;
        CfWeaponDO cfWeaponDO;
        List<CfWeaponApplyRecordDO> applyRecordDOList;
    }
}
