package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CaseReachDonateDate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.partner.model.pojo.CaseTimeDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCaseBaseDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolAsyncPoolConstants;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiCaseBaseDataDao;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;

import static com.shuidihuzhu.common.util.redisson.RedissonHandler.ONE_DAY;

/**
 * <AUTHOR>
 * @date 2020-08-03
 */

@Service
@Slf4j
public class CfKpiCaseBaseDataServiceImpl implements ICfKpiCaseBaseDataService {

    @Resource
    private CfKpiCaseBaseDataDao cfKpiCaseBaseDataDao;
    @Resource(name = GrowthtoolAsyncPoolConstants.KPI_CASE_DATA_POOL)
    private ExecutorService executorService;
    @Autowired
    private AlarmClient alarmClient;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;


    @Override
    public List<CfKpiCaseBaseDataDO> getCaseBaseMapByUniqueCode(String startTime, String endTime, String dayKey) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return Lists.newArrayList();
        }
        List<Long> idList = cfKpiCaseBaseDataDao.getIdList(startTime, endTime, dayKey);
        //添加报警
        if (CollectionUtils.isEmpty(idList)) {
            redissonHandler.setEX(String.format(GeneralConstant.cfBdKpiCaseListPrefix, dayKey), false, ONE_DAY);
            String content = "绩效计算-案例列表数据为空,请核查是否正常";
            alarmClient.sendByUser(Lists.newArrayList("fengxuan"), content);
        }
        List<List<Long>> partionList = Lists.partition(idList, 500);
        List<Future<List<CfKpiCaseBaseDataDO>>> futureList = Lists.newArrayList();
        for (List<Long> subIdList : partionList) {
            Future<List<CfKpiCaseBaseDataDO>> future = executorService.submit(new Callable<List<CfKpiCaseBaseDataDO>>() {
                @Override
                public List<CfKpiCaseBaseDataDO> call() throws Exception {
                    return cfKpiCaseBaseDataDao.getCaseBaseByIds(subIdList);
                }
            });
            futureList.add(future);
        }
        List<CfKpiCaseBaseDataDO> allCaseBaseData = Lists.newArrayList();
        for (Future<List<CfKpiCaseBaseDataDO>> futures : futureList) {
            try {
                allCaseBaseData.addAll(futures.get());
            } catch (Exception e) {
                log.error("parallelGetCaseBaseMapByUnqiueCode Exception", e);
            }
        }
        return allCaseBaseData;
    }


    @Override
    public List<CfKpiCaseBaseDataDO> listPartnerCaseBase(String uniqueCode, CaseTimeDO cfKpiCaseTimeDO) {
        if (Objects.isNull(cfKpiCaseTimeDO) || StringUtils.isEmpty(cfKpiCaseTimeDO.getStartTime()) || StringUtils.isEmpty(cfKpiCaseTimeDO.getEndTime()) || StringUtils.isEmpty(uniqueCode)) {
            return Lists.newArrayList();
        }
        return cfKpiCaseBaseDataDao.listByUniqueCode(cfKpiCaseTimeDO.getStartTime(), cfKpiCaseTimeDO.getEndTime(), uniqueCode, cfKpiCaseTimeDO.getDayKey());
    }

    @Override
    public int insertOrUpdate(CfKpiCaseBaseDataDO cfKpiCaseBaseDataDO) {
        //查找是否有对应的数据
        CfKpiCaseBaseDataDO dataInDB = cfKpiCaseBaseDataDao.getByDayKeyAndCaseId(cfKpiCaseBaseDataDO.getDayKey(), cfKpiCaseBaseDataDO.getCaseId());
        if (dataInDB != null) {
            //更新数据
            cfKpiCaseBaseDataDO.setId(dataInDB.getId());
            //如果数据没变化直接跳过,有变化需要更新,主要考虑下面几个字段已筹、捐单次数、初审时间
            boolean changed = checkInfoChange(dataInDB, cfKpiCaseBaseDataDO);
            if (changed) {
                return cfKpiCaseBaseDataDao.update(cfKpiCaseBaseDataDO);
            }
            return 0;
        } else {
            cfBdCaseInfoService.updateReachDonateDate(cfKpiCaseBaseDataDO.getCaseId().intValue());
            return cfKpiCaseBaseDataDao.insert(cfKpiCaseBaseDataDO);
        }
    }

    private boolean checkInfoChange(CfKpiCaseBaseDataDO dataInDB, CfKpiCaseBaseDataDO cfKpiCaseBaseDataDO) {
        boolean changed = false;
        if (!Objects.equals(dataInDB.getCaseAmount(), cfKpiCaseBaseDataDO.getCaseAmount())
                || !Objects.equals(dataInDB.getDonateNum(), cfKpiCaseBaseDataDO.getDonateNum())
                || !Objects.equals(dataInDB.getFirstApproveTime(), cfKpiCaseBaseDataDO.getFirstApproveTime())
                || !Objects.equals(dataInDB.getValidAmount(), cfKpiCaseBaseDataDO.getValidAmount())
                || !Objects.equals(dataInDB.getValidDonateNum(), cfKpiCaseBaseDataDO.getValidDonateNum())
                ) {
            changed = true;
        }
        return changed;
    }


    @Override
    public CaseReachDonateDate selectReachDonateDate(int caseId) {
        return cfKpiCaseBaseDataDao.selectReachDonateDate(caseId);
    }

    @Override
    public List<CfKpiCaseBaseDataDO> listByDateTimeAndUniqueCode(String dateTime, List<String> uniqueCodeList) {
        if (CollectionUtils.isEmpty(uniqueCodeList)) {
            return Lists.newArrayList();
        }
        return cfKpiCaseBaseDataDao.listByDateTimeAndUniqueCode(dateTime, uniqueCodeList);
    }
}
