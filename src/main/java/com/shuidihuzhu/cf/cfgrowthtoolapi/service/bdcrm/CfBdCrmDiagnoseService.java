package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseBaseDataModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseForCalcModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IBdCrmOrgAndCityRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolAsyncPoolConstants;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.repository.SdCityHierarchyRepository;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2021/5/7 下午4:59
 */
@Slf4j
@Service
public class CfBdCrmDiagnoseService {
    @Autowired
    protected ICfCompetitioMarketDataService cfCompetitioMarketDataService;
    @Autowired
    protected IBdCrmOrgAndCityRelationService bdCrmOrgAndCityRelationService;
    @Autowired
    protected ICrmSelfBuiltOrgReadService crmOrganizationService;
    @Autowired
    protected SdCityHierarchyRepository sdCityHierarchyRepository;
    @Autowired
    private ApolloService apolloService;

    @Autowired
    private AbstractBdCrmDiagnosePrepareDataService cfBdCrmDiagnosePrepareDataService;
    @Autowired
    private AbstractBdCrmDiagnoseHandleService cfBdCrmDiagnoseService;
    @Autowired
    private CfBdCrmDiagnoseCronTaskService cfBdCrmDiagnoseCronTaskService;
    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;
    @Autowired
    private CfBdCrmDiagnoseResultService cfBdCrmDiagnoseResultServiceImpl;

    public void doDiagnose(String curDateTime, Integer curDayRange,String preDateTime, Integer preDayRange, boolean needAverage) {
        CfBdCrmDiagnoseForCalcModel calcModel = fullCfBdCrmDiagnoseForCalcModel(null,curDateTime, curDayRange, preDateTime, preDayRange, needAverage);
        cfBdCrmDiagnosePrepareDataService.preprocessBefore1DayData(calcModel);
        cfBdCrmDiagnoseService.diagnoseData(calcModel);
    }
    @Async(GrowthtoolAsyncPoolConstants.GROWTHTOOL_DIAGNOSE_DATA)
    public void doDiagnose(long orgId, String curDateTime, Integer curDayRange,String preDateTime, Integer preDayRange) {
        CfBdCrmDiagnoseForCalcModel calcModel = fullCfBdCrmDiagnoseForCalcModel(orgId,curDateTime, curDayRange, preDateTime, preDayRange, false);
        OpResult<Integer> integerOpResult = cfBdCrmDiagnosePrepareDataService.preprocessBefore1DayData(calcModel);
        // 查询 可诊断且待诊断的任务
        List<CfBdCrmDiagnoseCronTaskDO> waitDiagnoseTasks = cfBdCrmDiagnoseCronTaskService.listWaitDiagnoseTask(curDateTime,curDayRange,preDateTime,preDayRange);
        cfBdCrmDiagnoseCronTaskService.batchUpdateStatus(waitDiagnoseTasks.stream().map(CfBdCrmDiagnoseCronTaskDO::getId).collect(Collectors.toList()), CfBdCrmDiagnoseCronTaskDO.StatusEnum.ING.getStatus());
        if (integerOpResult.isFail()){
            cfBdCrmDiagnoseCronTaskService.batchUpdateStatus(waitDiagnoseTasks.stream().map(CfBdCrmDiagnoseCronTaskDO::getId).collect(Collectors.toList()), CfBdCrmDiagnoseCronTaskDO.StatusEnum.FAIL.getStatus());
        }else if (integerOpResult.getData()==0){ // 如果诊断数据以及准备就绪  则查询符合诊断时间段的 任务 将其处理
            waitDiagnoseTasks.forEach(task -> {
                calcModel.setOrgId(task.getOrgId());
                // 查询是否有重复 诊断（组织id、时间段 都一致）
                List<CfBdCrmDiagnoseCronTaskDO> cronTaskDOList = cfBdCrmDiagnoseCronTaskService.listCronTask(task.getOrgId(), task.getCurDateTime(), task.getCurDayRange(), task.getPreDateTime(), task.getPreDayRange())
                        .stream()
                        .filter(item -> CfBdCrmDiagnoseCronTaskDO.StatusEnum.listFinalState().contains(item.getStatus())).collect(Collectors.toList());
                int status;
                if (CollectionUtils.isEmpty(cronTaskDOList)) {
                    cfBdCrmDiagnoseService.diagnoseData(calcModel);
                    status = CfBdCrmDiagnoseCronTaskDO.StatusEnum.END.getStatus();
                }else {
                    status = cronTaskDOList.get(0).getStatus();
                }
                CfBdCrmDiagnoseResultDO cfBdCrmDiagnoseResultDO = cfBdCrmDiagnoseResultServiceImpl.getCfBdCrmDiagnoseResultDO(task.getOrgId(), task.getCurDateTime(), task.getCurDayRange(), task.getPreDateTime(), task.getPreDayRange(), String.valueOf(orgId), CfBdCrmDiagnoseResultDO.DataTypeEnum.ORG.getType());
                cfBdCrmDiagnoseCronTaskService.batchUpdateStatus(Lists.newArrayList(task.getId()), status);
                task.setStatus(status);
                appPushCrmCaseMsgService.sendDiagnoseResultMsg(task, cfBdCrmDiagnoseResultDO);
            });
        }
    }


    // 封装 CfBdCrmDiagnoseForCalcModel
    private CfBdCrmDiagnoseForCalcModel fullCfBdCrmDiagnoseForCalcModel(Long orgId,String curDateTime, Integer curDayRange,String preDateTime, Integer preDayRange, boolean needAverage) {
        // 获取大区
        List<BdCrmOrganizationDO> directSubOrgs = crmOrganizationService.findDirectSubOrgByOrgId(GeneralConstant.CRM_ORG_ID).stream().filter(bdCrmOrganizationDO -> bdCrmOrganizationDO.getId()!=571).collect(Collectors.toList());
        // 获取大区下的组织
        List<BdCrmOrganizationDO> allAreaOrgList = directSubOrgs.stream().map(item ->crmOrganizationService.findDirectSubOrgByOrgId(item.getId())).reduce((result, list) -> {result.addAll(list); return result;}).get();
        List<Integer> orgLevelList = Lists.newArrayList(CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.AREA_PROVINCE.getLevel());
        List<BdCrmOrganizationDO> allProvinceOrPartionAreaOrgList = crmOrganizationService.getAllOrg().stream().filter(item -> orgLevelList.contains(item.getOrgLevel())).collect(Collectors.toList());
        //  后续如果扩展 到分区、省区。。  添加 要扩展的组织即可
        List<BdCrmOrganizationDO> allAreaOrgWithSubOrgList = Lists.newArrayList(allAreaOrgList);
        allAreaOrgWithSubOrgList.addAll(allProvinceOrPartionAreaOrgList);
        List<SdCityHierarchyDO> allCity = sdCityHierarchyRepository.listAllCity();
        Map<Long, List<SdCityHierarchyDO>> provinceIdMapSdCityList = allCity.stream().collect(Collectors.groupingBy(SdCityHierarchyDO::getProvinceId));
        log.info("fullCfBdCrmDiagnoseForCalcModel_provinceIdMapSdCityList:{}", JSON.toJSONString(provinceIdMapSdCityList));
        List<Long> allCityIdList = allCity.stream().map(SdCityHierarchyDO::getCityId).collect(Collectors.toList());
        List<CfBdCrmDiagnoseBaseDataModel> curAllCityDataModels = Lists.newArrayList();
        List<CfBdCrmDiagnoseBaseDataModel> preAllCityDataModels = Lists.newArrayList();
        List<List<Long>> allCityIdLists = Lists.partition(allCityIdList,100);
        allCityIdLists.parallelStream().forEach(list -> {
            curAllCityDataModels.addAll(cfCompetitioMarketDataService.listCrmDiagnoseBaseDataByCityAndTheDate(GrowthtoolUtil.listDateTimeByStartTimeWithDayRange(curDateTime,curDayRange,true),list));
            List<CfBdCrmDiagnoseBaseDataModel> preDateDataModelList = cfCompetitioMarketDataService.listCrmDiagnoseBaseDataByCityAndTheDate(GrowthtoolUtil.listDateTimeByStartTimeWithDayRange(preDateTime, preDayRange, true), list);
            if (needAverage){
                // 同期比较的是范围日期 且需要是平均数  比如 同期的数据是 近7天  则需要平均
                preDateDataModelList = preDateDataModelList.stream().map(item -> {
                    if (item.getDays()>1){
                        item.setSdTotalCaseAmount(item.getSdTotalCaseAmount()/item.getDays());
                        item.setQsTotalCaseAmount(item.getQsTotalCaseAmount()/item.getDays());
                        item.setSdOnlineCaseAmount(item.getSdOnlineCaseAmount()/item.getDays());
                        item.setSdOfflineCaseAmount(item.getSdOfflineCaseAmount()/item.getDays());
                    }
                    return item;
                }).collect(Collectors.toList());
            }
            preAllCityDataModels.addAll(preDateDataModelList);
        });
        log.info("fullCfBdCrmDiagnoseForCalcModel_curAllCityDataModels:{}", JSON.toJSONString(curAllCityDataModels));
        log.info("fullCfBdCrmDiagnoseForCalcModel_preAllCityDataModels:{}", JSON.toJSONString(preAllCityDataModels));
        if (CollectionUtils.isEmpty(curAllCityDataModels) || CollectionUtils.isEmpty(preAllCityDataModels)) { //  报警提醒 昨日数据 没有推 所以无需诊断
            log.info("{} 日数据为空，因此无需诊断", CollectionUtils.isEmpty(curAllCityDataModels)?curDateTime:preDateTime);
            AlarmBotService.sentText(GeneralConstant.BD_CRM_DIAGNOSE_GROUP_ID, curDateTime+" （诊断的数据日期）天眼诊断异常提醒\n" +
                    "异常原因：【无市占数据】", apolloService.getDiagnoseAlarmMisArr(), null);
            return null;
        }

        Map<Long,CfBdCrmDiagnoseBaseDataModel> curCityDiagnoseDataMap = curAllCityDataModels.stream().map(CfBdCrmDiagnoseBaseDataModel::calcRatio).collect(Collectors.toMap(CfBdCrmDiagnoseBaseDataModel::getCityId, Function.identity(),(oldVal, newVal)->newVal));
        Map<Long,CfBdCrmDiagnoseBaseDataModel> preCityDiagnoseDataMap = preAllCityDataModels.stream().map(CfBdCrmDiagnoseBaseDataModel::calcRatio).collect(Collectors.toMap(CfBdCrmDiagnoseBaseDataModel::getCityId, Function.identity(),(oldVal,newVal)->newVal));
        // Map<日期,Map<城市Id,CfBdCrmDiagnoseBaseDataModel>
        Map<String, Map<Long, CfBdCrmDiagnoseBaseDataModel>> cityDiagnoseData = Maps.newHashMap();
        cityDiagnoseData.put(curDateTime,curCityDiagnoseDataMap);
        cityDiagnoseData.put(preDateTime,preCityDiagnoseDataMap);
        log.info("fullCfBdCrmDiagnoseForCalcModel_cityDiagnoseData:{}", JSON.toJSONString(cityDiagnoseData));
        // Map<日期,Map<省份Id,CfBdCrmDiagnoseBaseDataModel>
        Map<String, Map<Long, CfBdCrmDiagnoseBaseDataModel>> provinceDiagnoseData = Maps.newHashMap();
        Map<Long,CfBdCrmDiagnoseBaseDataModel> curProvinceDiagnoseDataMap = Maps.newHashMap();
        Map<Long,CfBdCrmDiagnoseBaseDataModel> preProvinceDiagnoseDataMap = Maps.newHashMap();
        for (Long provinceId : provinceIdMapSdCityList.keySet()){
            List<SdCityHierarchyDO> value = provinceIdMapSdCityList.get(provinceId);
            List<CfBdCrmDiagnoseBaseDataModel> curCityDiagnoseDataList = value.stream().map(item -> curCityDiagnoseDataMap.get(item.getCityId())).filter(Objects::nonNull).collect(Collectors.toList());
            List<CfBdCrmDiagnoseBaseDataModel> preCityDiagnoseDataList = value.stream().map(item -> preCityDiagnoseDataMap.get(item.getCityId())).filter(Objects::nonNull).collect(Collectors.toList());
            CfBdCrmDiagnoseBaseDataModel curProvinceDiagnoseData = CfBdCrmDiagnoseBaseDataModel.aggregateData(curCityDiagnoseDataList);
            curProvinceDiagnoseData.setCityId(provinceId);
            CfBdCrmDiagnoseBaseDataModel preProvinceDiagnoseData = CfBdCrmDiagnoseBaseDataModel.aggregateData(preCityDiagnoseDataList);
            preProvinceDiagnoseData.setCityId(provinceId);
            curProvinceDiagnoseDataMap.put(provinceId,curProvinceDiagnoseData);
            preProvinceDiagnoseDataMap.put(provinceId,preProvinceDiagnoseData);
        }
        provinceDiagnoseData.put(curDateTime,curProvinceDiagnoseDataMap);
        provinceDiagnoseData.put(preDateTime,preProvinceDiagnoseDataMap);
        log.info("fullCfBdCrmDiagnoseForCalcModel_provinceDiagnoseData:{}", JSON.toJSONString(provinceDiagnoseData));
        List<Long> allOrgIdList = Lists.newArrayList(allAreaOrgWithSubOrgList, directSubOrgs).stream().flatMap(list -> list.stream()).map(BdCrmOrganizationDO::getId).distinct().collect(Collectors.toList());
        allOrgIdList.add((long) GeneralConstant.CRM_ORG_ID);
        List<String> orgNameList = crmOrganizationService.getOrgInfoList(allOrgIdList).stream().filter(item -> item.getOrgLevel() == -1).map(BdCrmOrganizationDO::getOrgName).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orgNameList)) {
            String orgNameListStr = orgNameList.stream().collect(Collectors.joining(","));
            log.info("{} 这些没有配置组织类型", orgNameListStr);
            AlarmBotService.sentText(GeneralConstant.BD_CRM_DIAGNOSE_GROUP_ID, curDateTime+" （诊断的数据日期）天眼诊断异常提醒\n" +
                    "异常原因：【没有配置组织类型】\n"+"组织如下："+orgNameListStr, apolloService.getDiagnoseAlarmMisArr(), null);
            return null;
        }
        Map<Long, List<BdCrmOrgAndCityRelationDO>> orgIdMapOrgAndCityRelationList = bdCrmOrgAndCityRelationService.getByOrgIds(allOrgIdList).stream().collect(Collectors.groupingBy(BdCrmOrgAndCityRelationDO::getOrgId));
        Map<Long, BdCrmOrganizationDO> orgIdMapOrg = allAreaOrgWithSubOrgList.stream().collect(Collectors.toMap(BdCrmOrganizationDO::getId, Function.identity(), (oldObj, newObj) -> newObj));
        Map<Long, List<BdCrmOrganizationDO>> parentOrgIdMapOrgList = allAreaOrgWithSubOrgList.stream().collect(Collectors.groupingBy(BdCrmOrganizationDO::getParentId));
        return new CfBdCrmDiagnoseForCalcModel(orgId,curDateTime,curDayRange,preDateTime,preDayRange, needAverage, directSubOrgs,
                allAreaOrgList,allAreaOrgWithSubOrgList, orgIdMapOrg, parentOrgIdMapOrgList, orgIdMapOrgAndCityRelationList,
                provinceIdMapSdCityList,provinceDiagnoseData,cityDiagnoseData);
    }
}
