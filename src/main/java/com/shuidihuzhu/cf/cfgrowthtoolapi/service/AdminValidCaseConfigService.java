package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.AdminValidCaseConfigParam;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdminValidCaseConfigService {

    Response<Integer> saveOrUpdate(AdminValidCaseConfigParam param);

    Response<Integer> expiration(AdminValidCaseConfigParam param);

    Response<AdminValidCaseConfigPageModel<AdminValidCaseConfigVO>> getList(Integer orgId, Integer status, int pageNo, int pageSize);

    AdminValidCaseConfigDO getById(long id);

    List<AdminValidCaseConfigDO> getListByOrgIdAndStatus(int orgId, int status);

    int updateStatusById(long id, int status);

}