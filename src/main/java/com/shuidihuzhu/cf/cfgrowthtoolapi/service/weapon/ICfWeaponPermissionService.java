package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponAssignRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponBudgetDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponBudgetModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2021-02-20 3:45 下午
 * 武器库权限
 **/
public interface ICfWeaponPermissionService {


    //拦截下触发下发
    List<CfWeaponAssignRecordDO> interceptListWeapon(CrowdfundingVolunteer volunteer, boolean isExecutive);


    //人员查看有哪些子预算分配权限
    List<WeaponBudgetModel> findCanAssignBudget(CrowdfundingVolunteer volunteer, int weaponId);


    boolean checkPermission(CfWeaponBudgetDO weaponBudgetDO, String uniqueCode);

}

