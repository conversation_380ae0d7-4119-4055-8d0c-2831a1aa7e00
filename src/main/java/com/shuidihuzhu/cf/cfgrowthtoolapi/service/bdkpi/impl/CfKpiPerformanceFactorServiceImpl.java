package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfBdPerformanceFactorDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiPerformanceFactorService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiPerformanceFactorDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CfKpiPerformanceFactorServiceImpl implements ICfKpiPerformanceFactorService {

    @Resource
    private CfKpiPerformanceFactorDao cfKpiPerformanceFactorDao;

    @Override
    public int batchInsert(List<CfBdPerformanceFactorDO> cfBdPerformanceFactorList) {
        if (CollectionUtils.isEmpty(cfBdPerformanceFactorList)) {
            return 0;
        }
        return cfKpiPerformanceFactorDao.batchInsert(cfBdPerformanceFactorList);
    }


    @Override
    public List<CfBdPerformanceFactorDO> listByMonthKey(List<String> monthKeyList) {
        if (CollectionUtils.isEmpty(monthKeyList)) {
            return Lists.newArrayList();
        }
        return cfKpiPerformanceFactorDao.listByMonthKey(monthKeyList);
    }

    @Override
    public int deleteByMonthKey(String monthKey) {
        if (StringUtils.isBlank(monthKey)) {
            return 0;
        }
        return cfKpiPerformanceFactorDao.deleteByMonthKey(monthKey);
    }
}
