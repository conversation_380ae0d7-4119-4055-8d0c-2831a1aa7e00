package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveIdcardVerifyStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.common.web.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * @Author: duchao
 * @Date: 2018/8/4 下午3:33
 */
public interface CfFirstApproveBizInterface {
    // 当前置审核被驳回时，用户可以编辑的资料
	@AllArgsConstructor
	@Getter
	enum EditMaterialType {
		DEFAULT(0, "默认"),
		PATIENT_NAME_IDCARD(1, "患者姓名和身份证号"),
		IMAGE_URL(2, "医疗材料图片"),
		TARGET_AMOUNT_DESC(3, " 目标金额原因"),
		SUGGEST_END_CASE(4, " 建议停止筹款"),
		POVERTY(5, "贫困证明材料"),

		// 只入库使用，不返回给前端
		ALL(99, "患者姓名和身份证号、医疗材料、目标金额原因"),
		;

		private int code;
		private String desc;

		public static EditMaterialType valueOfCode(int code) {

			for (EditMaterialType type : EditMaterialType.values()) {
				if (type.getCode() == code) {
					return type;
				}
			}
			return DEFAULT;
		}
	}

	static final int MAX_LENGTH_USER_INPUT_TARGET_DESC = 400;
	static final int MIN_LENGTH_USER_INPUT_TARGET_DESC = 10;
	static final int TARGET_AMOUT_NEED_DESC = 500000;

	@Data
	class CaseRejectObject {
		String title;
		String infoId;
		String rejectMessage;
	}
}
