package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfLogicHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.StandardHospitalSearchParam;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2021-03-07 3:50 下午
 **/
public interface CfLogicHospitalService {

    /**
     * 根据省份+城市查询医院信息,分页查询
     */
    List<CfLogicHospitalDO> pageByCityName(StandardHospitalSearchParam param);

    int countByCityName(StandardHospitalSearchParam param);

    /**
     * 根据省份+城市+是否有对公打款,分页查询
     */
    List<CfLogicHospitalDO> pageByCityNameAndPublicFlag(StandardHospitalSearchParam param);

    int countByCityNameAndPublicFlag(StandardHospitalSearchParam param);

    List<CfLogicHospitalDO> listByVvhospitalCode(List<String> vvhospitalCodes);
}
