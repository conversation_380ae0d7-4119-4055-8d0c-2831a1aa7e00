package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerRecruitInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfStatusEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerBaseInfoVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.RecruitPartnerInfoVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.LovePartnerInfoParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-09
 */
public interface CfPartnerRecruitInfoService {

    /**
     * 收集信息
     * @param cfPartnerRecruitInfoDo
     * @return
     */
    OpResult<Void> collectInfo(CfPartnerRecruitInfoDo cfPartnerRecruitInfoDo);

    /**
     * 获取基本信息
     * @param userId
     * @return
     */
    OpResult<PartnerBaseInfoVo> getBaseInfo(long userId);

    /**
     * 推荐线索
     * @param userId
     * @param phone
     * @param patientName
     * @return
     */
    OpResult<Void> recommendClue(long userId, String phone, String patientName);

    /**
     * 获取待审批人数
     * @param leaderUniqueCode
     * @param recruitApproveStatusEnum
     * @return
     */
    int getRecruitPartnerCount(String leaderUniqueCode, CfStatusEnums.PartnerRecruitApproveStatusEnum recruitApproveStatusEnum);

    /**
     * 伙伴招募列表展示
     * @param volunteer
     * @param approveStatus
     * @param pageNo
     * @param pageSize
     * @return
     */
    CommonResultModel<RecruitPartnerInfoVo> listRecruitPartner(CrowdfundingVolunteer volunteer, int approveStatus, int pageNo, int pageSize);

    /**
     * 伙伴招募详情信息
     * @param volunteer
     * @param id
     * @return
     */
    RecruitPartnerInfoVo recruitPartnerDetail(CrowdfundingVolunteer volunteer, long id);

    /**
     * 取消合作
     * @param volunteer
     * @param id
     * @return
     */
    OpResult<Void> recruitCancel(CrowdfundingVolunteer volunteer, long id);

    /**
     * 提交时更新招募表
     * @param lovePartnerInfoParam
     * @param cfVolunteer
     * @return
     */
    int updateFromSubmit(LovePartnerInfoParam lovePartnerInfoParam, CrowdfundingVolunteer cfVolunteer);

    /**
     * 更新二维码信息
     * @param qrCode
     * @param uniqueCode
     * @return
     */
    int updateQrCodeByUniqueCode(String qrCode, String uniqueCode);

    /**
     * 兼职审批通过后发送公众号消息
     * @param cfPartnerInfoDo
     */
    void sendPartnerApproveMsg(CfPartnerInfoDo cfPartnerInfoDo);

    /**
     * 批量获取公众号兼职信息
     * @param uniqueCodes
     * @return
     */
    List<CfPartnerRecruitInfoDo> listPartnerByUniqueCodes(List<String> uniqueCodes);

    /***
     * 调整负责人
     * @param partnerUniqueCode
     * @param leaderUniqueCode
     * @param leaderName
     * @param leaderMobile
     * @return
     */
    int adjustLovePartnerByPartnerUniqueCode(String partnerUniqueCode, String leaderUniqueCode, String leaderName, String leaderMobile);

    /**
     * 取消合作
     * @param leaderUniqueCode
     * @param uniqueCode
     */
    void cancelCooperation(String leaderUniqueCode, String uniqueCode);
}
