package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCaseDayDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.DepartmentDayCaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CampaignMapTrendView;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OrgDataStatVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDataStatParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DepartmentCmpParam;

import java.util.List;
import java.util.Map;

/**
 * 案例每日数据(CfCaseDayData)表服务接口
 *
 * <AUTHOR>
 * @since 2020-11-13 14:45:34
 */
public interface CfCaseDayDataService {

    /**
     * 查找某段时间内,某个组织下的捐单量
     */
    List<OrgDataStatVO> countDonateCount(String startTime, String endTime, List<Long> orgIds);

    OrgDataStatVO countRealTimeDonateCount(String time, List<Long> orgIds);

    /**
     * 查找某段时间内,按照orgId分组捐单量
     * @param crmDataStatParam
     * @param querOrgIdList
     * @return
     */
    List<OrgDataStatVO> countDonateCountByOrgId(BdCrmDataStatParam crmDataStatParam, List<Long> querOrgIdList);

    List<OrgDataStatVO> countDonateCountGroupByUniqueCode(BdCrmDataStatParam crmDataStatParam, List<Long> querOrgIdList);

    /**
     * 查询指定时间内 指定组织 的卷单量
     * @param dateTimes
     * @param allSubOrgIds
     * @return
     */
    Long getTotalCfDonatedCountByDateTimeWithOrderIdList(List<String> dateTimes, List<Integer> allSubOrgIds);

    /**
     * 获取科室的捐单量
     * @param departmentCmpParam
     * @return
     */
    List<DepartmentDayCaseModel> departmentGbDateKey(DepartmentCmpParam departmentCmpParam);


    Map<String, Integer> viewAreaByHospital(String vvhospitalCode, List<String> vhospitalCodes, String startTime, String endTime);


    List<CampaignMapTrendView> hospitalGbDateKey(String vvhospitalCode, String vhospitalCode,
                                                 String startTime, String endTime);

    List<OrgDataStatVO> listHospDataGroupByOrg(BdCrmDataStatParam crmDataStatParam);


    List<CfCaseDayDataDO> getByCaseIdAndDayKey(List<Integer> caseIds, String dayKey);

    /**
     * 获取案例指定时间范围内的捐单量
     * @param caseId
     * @param dayKeys
     * @return
     */
    List<CfCaseDayDataDO> getByCaseIdAndDayKeys(Integer caseId, List<String> dayKeys);

}
