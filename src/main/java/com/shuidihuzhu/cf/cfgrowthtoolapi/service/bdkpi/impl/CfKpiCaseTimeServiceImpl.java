package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.SeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseTimeDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKpiCaseApplyTimeModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCaseTimeService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiCaseTimeDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
@Service
@Slf4j
public class CfKpiCaseTimeServiceImpl implements ICfKpiCaseTimeService {

    @Autowired
    private CfKpiCaseTimeDao cfKpiCaseTimeDao;

    @Autowired
    private SeaAccountServiceDelegate seaAccountServiceDelegate;

    @Override
    public List<String> listAllOuterShowMonthKey() {
        return cfKpiCaseTimeDao.listByOuterShow(1)
                .stream()
                .map(CfKpiCaseTimeDO::getMonthKey)
                .collect(Collectors.toList());
    }

    @Override
    public OpResult<Integer> setOuterShow(int outerShow, String monthKey) {
        if (outerShow > 1 || outerShow < 0){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (StringUtils.isBlank(monthKey)) {
            monthKey = DateUtil.getYYYYMMStr(DateUtil.getCurrentDate());
        }
        //查询下其他未关闭展示的月份并关闭
        int ret = 0;
        List<CfKpiCaseTimeDO> cfKpiCaseTimeDOS = cfKpiCaseTimeDao.listByOuterShow(1);
        if (outerShow == 0) {
            //关闭当前月
            String currentOptMonth = monthKey;
            Optional<CfKpiCaseTimeDO> currentOptInfo = cfKpiCaseTimeDOS.stream().filter(item -> item.getMonthKey().equals(currentOptMonth))
                    .findFirst();
            if (currentOptInfo.isPresent()) {
                ret = cfKpiCaseTimeDao.closeOuterShowByIds(Lists.newArrayList(currentOptInfo.get().getId()));
            }
            return OpResult.createSucResult(ret);
        }
        if (CollectionUtils.isNotEmpty(cfKpiCaseTimeDOS)) {
            //关闭开启展示的
            cfKpiCaseTimeDao.closeOuterShowByIds(cfKpiCaseTimeDOS.stream().map(CfKpiCaseTimeDO::getId).collect(Collectors.toList()));
        }
        ret = cfKpiCaseTimeDao.setOuterShow(monthKey,outerShow);
        return OpResult.createSucResult(ret);
    }

    @Override
    public CfKpiCaseTimeDO getCfKpiCaseTimeByMonthkey(String monthKey) {
        return cfKpiCaseTimeDao.getCfKpiCaseTimeDoByMonthkey(monthKey);
    }

    @Override
    public OpResult<Integer> queryOuterShow() {
        String monthKey = DateUtil.getYYYYMMStr(DateUtil.getCurrentDate());
        CfKpiCaseTimeDO cfKpiCaseTimeDO = this.getCfKpiCaseTimeByMonthkey(monthKey);
        if (Objects.isNull(cfKpiCaseTimeDO)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }else{
            return OpResult.createSucResult(cfKpiCaseTimeDO.getOpenShow());
        }
    }

    @Override
    public OpResult saveOrUpdateCaseApplyTime(CfKpiCaseApplyTimeModel cfKpiCaseApplyTime) {
        CfKpiCaseTimeDO cfKpiCaseTimeDO = cfKpiCaseTimeDao.getCfKpiCaseTimeDoByMonthkey(cfKpiCaseApplyTime.getMonthKey());
        log.info(this.getClass().getName()+" getCfKpiCaseTimeDoByMonthkey param:{} result:{}",cfKpiCaseApplyTime.getMonthKey(),cfKpiCaseTimeDO);
        String name = "";
        long adminUserId = AuthSaasContext.getAuthSaasUserId();
        if (adminUserId > 0) {
            name = Optional.ofNullable(seaAccountServiceDelegate.getValidUserAccountById(adminUserId))
                    .map(AdminUserAccountModel::getName)
                    .orElse("");
        }
        cfKpiCaseApplyTime.setOperator(name);
        if (cfKpiCaseTimeDO==null){
            cfKpiCaseTimeDao.insert(cfKpiCaseApplyTime);
        }else {
            cfKpiCaseTimeDO.setStartTime(cfKpiCaseApplyTime.getStartTime());
            cfKpiCaseTimeDO.setEndTime(cfKpiCaseApplyTime.getEndTime());
            cfKpiCaseTimeDao.updateCfKpiCaseTimeDO(cfKpiCaseTimeDO);
        }
        return OpResult.createSucResult(null);
    }

    @Override
    public OpResult checkCanMod(int status, String monthKey,String oldCityNameStr,String newCityNameStr) {
        CfKpiCaseTimeDO cfKpiCaseTimeDO = this.getCfKpiCaseTimeByMonthkey(monthKey);
        log.info(this.getClass().getName() + " checkCanMod param:{} cfKpiCaseTimeDO:{}", String.join(GeneralConstant.splitChar, status + "", monthKey), cfKpiCaseTimeDO);
        // 已有规则  且 已开启小鲸鱼展示 就不允许修改
        if (cfKpiCaseTimeDO != null && BooleanUtils.toBoolean(cfKpiCaseTimeDO.getOpenShow())) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.UN_ALLOW_MOD);
        }
        // 如果单个城市  oldCityNameList 不是 newCityNameList的子集 则不允许添加，因为会导致 某些城市不在规则内
        if (newCityNameStr != null
                && oldCityNameStr != null
                && !Lists.newArrayList(newCityNameStr.split(GeneralConstant.splitChar)).containsAll(Lists.newArrayList(oldCityNameStr.split(GeneralConstant.splitChar)))) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CITY_NAME_UN_ALLOW_REMOVE);
        }
        return OpResult.createSucResult();
    }

    @Override
    public List<CfKpiCaseTimeDO> listAllMonth(List<Integer> outShowList) {
        if (CollectionUtils.isEmpty(outShowList)){
            return Lists.newArrayList();
        }
        return cfKpiCaseTimeDao.listlately12Month(outShowList);
    }

    @Override
    public CfKpiCaseTimeDO listOuterShow() {
        List<CfKpiCaseTimeDO> cfKpiCaseTimeList = cfKpiCaseTimeDao.listByOuterShow(1);
        if (CollectionUtils.isEmpty(cfKpiCaseTimeList)){
            return null;
        }else{
            return cfKpiCaseTimeList.get(0);
        }
    }

    @Override
    public int countAll(Date startTime) {
        if (startTime == null) {
            return 0;
        }
        return cfKpiCaseTimeDao.countAll(startTime);
    }

    @Override
    public List<CfKpiCaseTimeDO> pageAll(Date startTime, int offset, int limit) {
        if (startTime == null) {
            return Lists.newArrayList();
        }
        return cfKpiCaseTimeDao.pageAll(startTime, offset, limit);
    }
}
