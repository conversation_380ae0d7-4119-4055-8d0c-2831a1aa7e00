package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.calresult.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.calresult.EncourageActivityUserResultDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.calresult.EncourageActivityUserResultService;
import com.shuidihuzhu.cf.dao.starrocks.EncourageActivityUserResultDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 个人计算结果(EncourageActivityUserResult)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-23 15:32:57
 */
@Service("encourageActivityUserResultService")
public class EncourageActivityUserResultServiceImpl implements EncourageActivityUserResultService {
   
    @Resource
    private EncourageActivityUserResultDao encourageActivityUserResultDao;

    @Override
    public List<EncourageActivityUserResultDO> listByActivityIdAndUniqueCodeAndRuleIds(long activityId, String uniqueCode, List<Long> ruleIds) {
        return encourageActivityUserResultDao.listByActivityIdAndUniqueCode(activityId, uniqueCode)
                .stream()
                .filter(item -> ruleIds.contains(item.getRuleId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<EncourageActivityUserResultDO> listByUniqueCodeAndTime(String uniqueCode, String startTime, String startFinishTime, String endTime, String finishTime) {
        return encourageActivityUserResultDao.listByUniqueCodeAndTime(uniqueCode, startTime, startFinishTime, endTime, finishTime);
    }

    @Override
    public List<EncourageActivityUserResultDO> listByActivityIdAndRuleId(long activityId, long ruleId) {
        return encourageActivityUserResultDao.listByActivityIdAndRuleId(activityId, ruleId);
    }

    @Override
    public List<EncourageActivityUserResultDO> listByActivityIdAndRuleIds(long activityId, List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return List.of();
        }
        return encourageActivityUserResultDao.listByActivityIdAndRuleIds(activityId, ruleIds);
    }

    @Override
    public List<Long> listAllActivityId(List<String> uniqueCodes, String startTime, String startFinishTime, String endTime, String finishTime) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return List.of();
        }
        return encourageActivityUserResultDao.listAllActivityId(uniqueCodes, startTime, startFinishTime, endTime, finishTime);
    }


}
