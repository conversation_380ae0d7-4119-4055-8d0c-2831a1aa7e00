package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.dao.BdGpsErrorReportDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdGpsErrorReportDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.BdGpsErrorReportService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * bd gps error report(BdGpsErrorReportDO)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-07-07 16:51:36
 */
@Service("BdGpsErrorReportDOService")
public class BdGpsErrorReportServiceImpl implements BdGpsErrorReportService {

    @Resource
    private BdGpsErrorReportDao BdGpsErrorReportDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public BdGpsErrorReportDO queryById(long id) {
        return BdGpsErrorReportDao.queryById(id);
    }


    /**
     * 新增数据
     *
     * @param BdGpsErrorReportDO 实例对象
     * @return 实例对象
     */
    @Override
    public int insert(BdGpsErrorReportDO BdGpsErrorReportDO) {
        return BdGpsErrorReportDao.insert(BdGpsErrorReportDO);
    }

    /**
     * 修改数据
     *
     * @param BdGpsErrorReportDO 实例对象
     * @return 实例对象
     */
    @Override
    public int update(BdGpsErrorReportDO BdGpsErrorReportDO) {
        return BdGpsErrorReportDao.update(BdGpsErrorReportDO);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return BdGpsErrorReportDao.deleteById(id) > 0;
    }
}