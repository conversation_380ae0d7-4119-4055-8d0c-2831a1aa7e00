package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientrecruit.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.CfPatientRecruitOrgRelationVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientrecruit.ICfPatientRecruitOrgRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.dao.patientrecruit.CfPatientRecruitOrgUserRelationDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-05-10
 */

@Service
@Slf4j
public class CfPatientRecruitOrgRelationServiceImpl implements ICfPatientRecruitOrgRelationService {

    @Autowired
    private CfPatientRecruitOrgUserRelationDao recruitOrgUserRelationDao;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Override
    public int batchInsert(List<CfPatientRecruitOrgRelationVo> list) {
        return recruitOrgUserRelationDao.batchInsert(list);
    }

    @Override
    public int batchUpdate(List<CfPatientRecruitOrgRelationVo> list) {
        return recruitOrgUserRelationDao.batchUpdate(list);
    }

    @Override
    public int allUpdateDelete() {
        return recruitOrgUserRelationDao.allUpdateDelete();
    }

    @Override
    public int updateDelete(long id) {
        return recruitOrgUserRelationDao.updateDelete(id);
    }

    @Override
    public int countCfPatientRecruit(int type, String misName, String mis, String orgName) {
        return recruitOrgUserRelationDao.countCfPatientRecruit(type,misName,mis,orgName);
    }

    @Override
    public List<CfPatientRecruitOrgRelationVo> listCfPatientRecruit(int type, String misName, String mis, String orgName) {
        return recruitOrgUserRelationDao.listCfPatientRecruit(type,misName,mis,orgName);
    }

    @Override
    public List<CfPatientRecruitOrgRelationVo> listAllValidData() {
        return recruitOrgUserRelationDao.listAllValidData();
    }

    @Override
    public List<CfPatientRecruitOrgRelationVo> getPatientManagerByOrgIds(List<Long> orgIds){
        if (CollectionUtils.isEmpty(orgIds)){
            return Lists.newArrayList();
        }
        return recruitOrgUserRelationDao.listRecruitOrgRelationByOrgIds(orgIds);

    }
}
