package com.shuidihuzhu.cf.cfgrowthtoolapi.service.mdc.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.biz.CfWorkImGroupUserBiz;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IQywxSdkDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfShuidichouWechatFriendDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupUserDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.mdc.IWeChatFriendService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.LocalCacheHelper;
import com.shuidihuzhu.cf.dao.dedicated.CfShuidichouWechatFriendDao;
import com.shuidihuzhu.cf.dao.dedicated.CfWorkImGroupInfoDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSimpleTrueOrFalseEnum;
import com.shuidihuzhu.cf.repository.ChatGroupKratosTagRelRepository;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewQyWxCorpDO;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import com.shuidihuzhu.client.cf.qywechat.QywxSdkClient;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: guohaidong
 * @create: 2021/09/22 17:05
 */
@Service
@Slf4j
public class WeChatFriendServiceImpl implements IWeChatFriendService {

    @Resource
    private CfShuidichouWechatFriendDao cfShuidichouWechatFriendDao;

    @Autowired
    private CfWorkImGroupUserBiz cfWorkImGroupUserBiz;

    @Autowired
    private CfWorkImGroupInfoDao cfWorkImGroupInfoDao;

    @Autowired
    private IQywxSdkDelegate qywxSdkDelegate;

    @Autowired
    private QywxSdkClient qywxSdkClient;

    @Autowired
    private ChatGroupKratosTagRelRepository chatGroupKratosTagRelRepository;


    private LocalCacheHelper<Integer, List<String>> listYiLiaoChat =
            LocalCacheHelper.create("listYiLiaoChat", this::listYiLiaoChat, 30, TimeUnit.MINUTES);

    public List<String> listYiLiaoChat(Integer chatId) {
        //获取医疗运营群组的在群信息
        return chatGroupKratosTagRelRepository.selectAllChatId();
    }

    public List<String> listYiLiaoChatFromCache() throws ExecutionException {
        return listYiLiaoChat.getCache().get(1);
    }


    @Override
    public List<CfShuidichouWechatFriendDO> getFriendInfoByEncryptPhone(List<String> phoneList) {
        List<String> phoneList_new = Optional.ofNullable(phoneList).orElse(Lists.newArrayList()).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phoneList_new)) {
            return Lists.newArrayList();
        }
        return cfShuidichouWechatFriendDao.getCfShuidichouWechatFriendByPhoneList(phoneList_new);
    }

    @Override
    public CfShuidichouWechatFriendDO getFriendInfoById(Long id) {
        if(id<=0){
            return null;
        }
        return cfShuidichouWechatFriendDao.getFriendInfoById(id);
    }

    @Override
    public List<CfShuidichouWechatFriendDO> getFriendInfoByExternalUserId(String externalUserId) {
        if(StringUtils.isBlank(externalUserId)){
            return Lists.newArrayList();
        }
        return cfShuidichouWechatFriendDao.getFriendInfoByExternalUserId(externalUserId);
    }

    @Override
    public List<String> getFriendInfoUnionIds(List<String> unionIds) {
        if(CollectionUtils.isEmpty(unionIds)){
            return Lists.newArrayList();
        }
        Set<String> retSet = Sets.newHashSet();
        Lists.partition(unionIds,200).parallelStream().forEach(list->{
            List<String> resultList = cfShuidichouWechatFriendDao.getFriendInfoByUnionIds(list);
            retSet.addAll(resultList);
        });
        return Lists.newArrayList(retSet);
    }

    @Override
    public List<CfShuidichouWechatFriendDO> getBingYouFriendInfoByUnionId(String unionId) {
        if(StringUtils.isBlank(unionId)){
            return Lists.newArrayList();
        }
        return cfShuidichouWechatFriendDao.getBingYouFriendInfoByUnionId(unionId);
    }

    @Override
    public List<CfShuidichouWechatFriendDO> listFriendInfo(Long minId, Long maxId) {
        if (minId==null || maxId==null) return Lists.newArrayList();
        if (minId>maxId) return Lists.newArrayList();
        return cfShuidichouWechatFriendDao.listFriendInfo(minId, maxId);
    }

    @Override
    public IdRangeModel getIdRange4ListFriendInfo(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) return new IdRangeModel(0L,0L);
        return cfShuidichouWechatFriendDao.getIdRange4ListFriendInfo(startTime, endTime);
    }

    @Override
    public List<CfShuidichouWechatFriendDO> listFriendInfoByUnionId(String unionId) {
        if (StringUtils.isBlank(unionId)) {
            return Lists.newArrayList();
        }
        return cfShuidichouWechatFriendDao.getCfShuidichouWechatFriendByUnionId(unionId);
    }

    @Override
    public List<CfShuidichouWechatFriendDO> getFriendByExternalUserIdAndQyWechatUserId(String externalUserId, String qyWechatUserId) {
        if (StringUtils.isBlank(externalUserId) || StringUtils.isBlank(qyWechatUserId)){
            return Lists.newArrayList();
        }
       return cfShuidichouWechatFriendDao.getCfShuidichouWechatFriendByExternalUseridWithQyWechatUserId(externalUserId, qyWechatUserId);
    }

    @Override
    public List<CfShuidichouWechatFriendDO> getFriendInfoByTimeAndCallbackId(Date startDay, Date endDay, Integer callBackId) {
        if (callBackId == null) return Lists.newArrayList();
        if (startDay == null || endDay == null) {
            startDay =  DateUtil.getFirstMsOfThisDay(new Date());
            endDay = DateUtil.getFirstMsOfThisDay(DateUtil.addDay(startDay, 1));
        }
        return cfShuidichouWechatFriendDao.getFriendInfoByTimeAndCallbackId(startDay, endDay, callBackId);
    }

    @Override
    public List<CfWeChatFriendGroupInfo> trimFriendGroupInfo(List<CfWeChatFriendInfo> weChatFriendInfos) {
        if (CollectionUtils.isEmpty(weChatFriendInfos)){
            return Lists.newArrayList();
        }
        List<String> unionIdList = weChatFriendInfos.stream().map(CfWeChatFriendInfo :: getUnionId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unionIdList)){
            return Lists.newArrayList();
        }
        List<CfWorkImGroupUserDO> groupUserDOS = cfWorkImGroupUserBiz.selectInfoByUnionIdList(unionIdList);
        log.info("getFriendGroupInfoByTimeAndCallbackId_trimFriendGroupInfo_groupUserDOS:{}",JSON.toJSONString(groupUserDOS));
        if (CollectionUtils.isEmpty(groupUserDOS)){
            return Lists.newArrayList();
        }
        List<CfWorkImGroupInfoDO> groupInfoDOList = listKratosRelChat(groupUserDOS);
        log.info("getFriendGroupInfoByTimeAndCallbackId_trimFriendGroupInfo_groupInfoDOList:{}",JSON.toJSONString(groupInfoDOList));
        if (CollectionUtils.isEmpty(groupInfoDOList)) {
            return Lists.newArrayList();
        }
        List<CfWeChatFriendGroupInfo> resultList = new ArrayList<>();
        Map<String, CfWorkImGroupInfoDO> chatIdMapGroupInfo = groupInfoDOList.stream().collect(Collectors.toMap(CfWorkImGroupInfoDO::getChatId, Function.identity(), (key1, key2) -> key2));
        Map<String, List<CfWorkImGroupUserDO>> unionIdMapGroupInfo = groupUserDOS.stream().collect(Collectors.groupingBy(CfWorkImGroupUserDO::getUnionId));
        for (CfWeChatFriendInfo friendInfo : weChatFriendInfos){
            CfWeChatFriendGroupInfo friendGroupInfo = new CfWeChatFriendGroupInfo();
            friendGroupInfo.setQyWechatUserId(friendInfo.getQyWechatUserId());
            friendGroupInfo.setUnionId(friendInfo.getUnionId());
            friendGroupInfo.setExternalUserid(friendInfo.getExternalUserid());
            friendGroupInfo.setExternalUserName(friendInfo.getExternalUserName());
            friendGroupInfo.setExternalUserTags(friendInfo.getExternalUserTags());
            friendGroupInfo.setCorpName(friendInfo.getCorpName());
            friendGroupInfo.setPassTime(friendInfo.getPassTime());
            friendGroupInfo.setEncryptPhone(friendInfo.getEncryptPhone());
            friendGroupInfo.setCorpId(friendInfo.getCorpId());
            if (CollectionUtils.isEmpty(unionIdMapGroupInfo.get(friendInfo.getUnionId()))) {
                resultList.add(friendGroupInfo);
                continue;
            }
            List<CfWorkImGroupUserDO> invitorUserGroupList = unionIdMapGroupInfo.get(friendInfo.getUnionId()).stream().filter(item -> friendGroupInfo.getQyWechatUserId().equals(item.getInvitorUserid())).collect(Collectors.toList());
            List<FriendGroupInfoModel> friendGroupInfoList = new ArrayList<>();
            if (CollectionUtils.isEmpty(invitorUserGroupList)){
                try {
                    //如果没有匹配要邀请人,则比较一下union_id相同的用户加好友的时间是否与入群时间是否在同一天,且仅在invitorUserid为空的情况下，则判定为是该客户是由该客服人员添加成功的
                    invitorUserGroupList = unionIdMapGroupInfo.get(friendInfo.getUnionId()).stream().filter(item -> StringUtils.isBlank(item.getInvitorUserid())
                            &&DateUtil.formatDate(friendGroupInfo.getPassTime()).compareTo(DateUtil.formatDate(item.getJoinTime())) == 0).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(invitorUserGroupList)) {
                        resultList.add(friendGroupInfo);
                    } else {
                        //如果有多个，则取第一个做关联
                        CfWorkImGroupUserDO groupUserDO = invitorUserGroupList.get(0);
                        FriendGroupInfoModel groupInfoModel = new FriendGroupInfoModel();
                        groupInfoModel.setChatId(groupUserDO.getChatId());
                        groupInfoModel.setJoinTime(groupUserDO.getJoinTime());
                        groupInfoModel.setValid(groupUserDO.getValid());
                        groupInfoModel.setInvitorUserid(friendInfo.getQyWechatUserId());
                        groupInfoModel.setUnionId(groupUserDO.getUnionId());
                        if (chatIdMapGroupInfo.get(groupUserDO.getChatId()) != null) {
                            groupInfoModel.setChatName(chatIdMapGroupInfo.get(groupUserDO.getChatId()).getName());
                        }
                        updateChannelByChatIdAndUnionId(groupInfoModel);
                        friendGroupInfoList.add(groupInfoModel);
                        friendGroupInfo.setFriendGroupInfoList(friendGroupInfoList);
                        resultList.add(friendGroupInfo);
                    }
                }catch (Exception e){
                    log.warn("trimFriendGroupInfo 补偿失败 Exception: ",e);
                }
                continue;
            }
            for (CfWorkImGroupUserDO groupUserDO : invitorUserGroupList){
                FriendGroupInfoModel groupInfoModel = new FriendGroupInfoModel();
                groupInfoModel.setChatId(groupUserDO.getChatId());
                groupInfoModel.setJoinTime(groupUserDO.getJoinTime());
                groupInfoModel.setValid(groupUserDO.getValid());
                groupInfoModel.setInvitorUserid(groupUserDO.getInvitorUserid());
                groupInfoModel.setUnionId(groupUserDO.getUnionId());
                if (chatIdMapGroupInfo.get(groupUserDO.getChatId()) != null) {
                    groupInfoModel.setChatName(chatIdMapGroupInfo.get(groupUserDO.getChatId()).getName());
                }
                updateChannelByChatIdAndUnionId(groupInfoModel);
                friendGroupInfoList.add(groupInfoModel);
            }
            friendGroupInfo.setFriendGroupInfoList(friendGroupInfoList);
            resultList.add(friendGroupInfo);
        }
        log.info("getFriendGroupInfoByTimeAndCallbackId_trimFriendGroupInfo_resultList:{}",JSON.toJSONString(resultList));
        return resultList;
    }

    private void updateChannelByChatIdAndUnionId(FriendGroupInfoModel friendGroupInfoModel){
        log.info("updateChannelByChatIdAndUnionId_param:{}",JSON.toJSONString(friendGroupInfoModel));
        if (friendGroupInfoModel == null){
            return;
        }
       int count = cfWorkImGroupUserBiz.updateChannelByChatIdAndUnionId(friendGroupInfoModel.getChatId(),friendGroupInfoModel.getUnionId(),"mdc_cc","mdc_cc");
        log.info("updateChannelByChatIdAndUnionId_count:{}",count);
    }

    private List<CfWorkImGroupInfoDO> listKratosRelChat(List<CfWorkImGroupUserDO> groupUserDOS) {
          if (CollectionUtils.isEmpty(groupUserDOS)){
              return Lists.newArrayList();
          }
        List<String> kratosTagRelChatIdList = null;
        try {
            kratosTagRelChatIdList = listYiLiaoChatFromCache();
        } catch (Exception e) {
            log.warn("filterKratosRelChat listYiLiaoChatFromCache e:", e);
        }
        // 过滤掉不在运营群组的info
        log.info("filterKratosRelChat kratosTagRelChatIdList:{}",JSON.toJSONString(kratosTagRelChatIdList));
        if (CollectionUtils.isEmpty(kratosTagRelChatIdList)){
            return Lists.newArrayList();
        }
        List<CfWorkImGroupUserDO> kratosGroupUserDOList = new ArrayList<>();
        for (CfWorkImGroupUserDO groupUserDO : groupUserDOS){
            if (kratosTagRelChatIdList.contains(groupUserDO.getChatId())){
                kratosGroupUserDOList.add(groupUserDO);
            }
        }
        if (CollectionUtils.isEmpty(kratosGroupUserDOList)){
            log.info("getAddGroupInfoByEncryptPhones kratosTagRelChatIdList is null");
            return Lists.newArrayList();
        }
        List<String> chatIdList = kratosGroupUserDOList.stream().map(CfWorkImGroupUserDO::getChatId).distinct().collect(Collectors.toList());
        List<CfWorkImGroupInfoDO> groupInfoDOList = cfWorkImGroupInfoDao.selectInfoByChatIdList(chatIdList);
        log.info("getAddGroupInfoByEncryptPhones groupInfoDOList:{}",JSON.toJSONString(groupInfoDOList));
        if (CollectionUtils.isEmpty(groupInfoDOList)) {
            return Lists.newArrayList();
        }
        return groupInfoDOList;
    }

    @Override
    public List<PatientAddGroupModel> getAddGroupInfoByEncryptPhones(List<String> phoneList) {
        log.info("getAddGroupInfoByEncryptPhones param:{}",JSON.toJSONString(phoneList));
        List<PatientAddGroupModel> patientAddGroupModels = new ArrayList<>();
        List<String> filterPhones = phoneList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> unionIdList = cfShuidichouWechatFriendDao.getWechatFriendByPhoneList(filterPhones).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        log.info("getAddGroupInfoByEncryptPhones unionIdList:{}",JSON.toJSONString(unionIdList));
        if (CollectionUtils.isEmpty(unionIdList)) {
            return patientAddGroupModels;
        }
        List<CfWorkImGroupUserDO> groupUserList = cfWorkImGroupUserBiz.selectInfoByUnionIdList(unionIdList);
        log.info("getAddGroupInfoByEncryptPhones groupUserList:{}",JSON.toJSONString(groupUserList));
        if (CollectionUtils.isEmpty(groupUserList)) {
            return patientAddGroupModels;
        }
        // 查询正在运营群群明细
        List<CfWorkImGroupInfoDO> groupInfoDOList = listKratosRelChat(groupUserList);
        if (CollectionUtils.isEmpty(groupInfoDOList)) {
            return Lists.newArrayList();
        }
        Map<String, List<CfWorkImGroupInfoDO>> chatIdMapGroupInfo = groupInfoDOList.stream().collect(Collectors.groupingBy(CfWorkImGroupInfoDO::getChatId));
        for (CfWorkImGroupUserDO groupUserDO : groupUserList) {
            if (CollectionUtils.isEmpty(chatIdMapGroupInfo.get(groupUserDO.getChatId()))) {
                continue;
            }
            CfWorkImGroupInfoDO cfWorkImGroupInfoDO = chatIdMapGroupInfo.get(groupUserDO.getChatId()).get(0);
            PatientAddGroupModel patientAddGroupMode = buildAddGroupModelList(cfWorkImGroupInfoDO, groupUserDO);
            patientAddGroupModels.add(patientAddGroupMode);
        }
        return patientAddGroupModels;
    }


    private PatientAddGroupModel buildAddGroupModelList(CfWorkImGroupInfoDO cfWorkImGroupInfoDOS, CfWorkImGroupUserDO groupUserDO) {
        PatientAddGroupModel patientAddGroupModel = new PatientAddGroupModel();
        patientAddGroupModel.setGroupName(cfWorkImGroupInfoDOS.getName());
        patientAddGroupModel.setGroupOwnerName(gainGroupOwnerName(cfWorkImGroupInfoDOS.getOwnerId()));
        patientAddGroupModel.setJoinTime(groupUserDO.getJoinTime());
        patientAddGroupModel.setIsValid(groupUserDO.getValid());
        patientAddGroupModel.setJoinScene(groupUserDO.getJoinScene());
        return patientAddGroupModel;
    }

    private String gainGroupOwnerName(String ownerId) {
        String accessToken = "";
        String name = "";
        //水滴筹病友之家主体
        CfClewQyWxCorpDO cfClewQyWxCorpDO = qywxSdkDelegate.getWxCorpMsgByCallbackId(123);
        if (cfClewQyWxCorpDO != null) {
            accessToken = qywxSdkDelegate.getAccessToken(CfSimpleTrueOrFalseEnum.FALSE, cfClewQyWxCorpDO.getCorpId(), cfClewQyWxCorpDO.getAppSecret());
        }
        log.info("gainGroupOwnerName getAccessToken response:{}", accessToken);
        if (StringUtils.isBlank(accessToken)) {
            return name;
        }
        String infoJson = qywxSdkClient.getQyWeChatUserInfo(accessToken, ownerId);
        log.info("gainGroupOwnerName  request:{},{} response:{}", accessToken, ownerId, infoJson);
        if (StringUtils.isBlank(infoJson)) {
            return name;
        }
        JSONObject jsonObject = JSON.parseObject(infoJson);
        if (jsonObject == null || jsonObject.getInteger("errcode") != 0) {
            return name;
        }
        if (StringUtils.isNotBlank(jsonObject.getString("name"))) {
            name = jsonObject.getString("name");
        }
        return name;
    }


}
