package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmDiagnoseCronTaskMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmDiagnoseCronTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfBdCrmDiagnoseCronTaskService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2021/6/10 下午2:56
 */
@Service
public class CfBdCrmDiagnoseCronTaskServiceImpl implements CfBdCrmDiagnoseCronTaskService {

    @Resource
    private CfBdCrmDiagnoseCronTaskMapper cfBdCrmDiagnoseCronTaskMapper;

    @Override
    public int saveCronTask(CfBdCrmDiagnoseCronTaskDO record) {
        List<CfBdCrmDiagnoseCronTaskDO> cfBdCrmDiagnoseCronTaskDOS = cfBdCrmDiagnoseCronTaskMapper.listCronTask(record.getOrgId(), record.getCurDateTime(), record.getCurDayRange(), record.getPreDateTime(), record.getPreDayRange());
        List<CfBdCrmDiagnoseCronTaskDO> existList = cfBdCrmDiagnoseCronTaskDOS.stream().filter(item -> item.getUniqueCode().equals(record.getUniqueCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existList)) {
            record.setId(existList.get(0).getId());
            return 1;
        }
        return cfBdCrmDiagnoseCronTaskMapper.insert(record);
    }

    @Override
    public CfBdCrmDiagnoseCronTaskDO selectByPrimaryKey(Long id) {
        return cfBdCrmDiagnoseCronTaskMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CfBdCrmDiagnoseCronTaskDO record) {
        return cfBdCrmDiagnoseCronTaskMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<CfBdCrmDiagnoseCronTaskDO> getHistoryForDiagnose(Long orgId, String uniqueCode) {
        return cfBdCrmDiagnoseCronTaskMapper.getHistoryForDiagnose(orgId, uniqueCode);
    }

    @Override
    public List<CfBdCrmDiagnoseCronTaskDO> listCronTask(Long orgId, String curDateTime, Integer curDayRange, String preDateTime, Integer preDayRange) {
        return cfBdCrmDiagnoseCronTaskMapper.listCronTask(orgId,curDateTime,curDayRange,preDateTime,preDayRange);
    }
    @Override
    public List<CfBdCrmDiagnoseCronTaskDO> listWaitDiagnoseTask(String curDateTime, Integer curDayRange, String preDateTime, Integer preDayRange) {
        return cfBdCrmDiagnoseCronTaskMapper.listWaitDiagnoseTask(curDateTime,curDayRange,preDateTime,preDayRange);
    }

    @Override
    public void batchUpdateStatus(List<Long> idList, int status) {
        if (CollectionUtils.isEmpty(idList)) return;
        cfBdCrmDiagnoseCronTaskMapper.batchUpdateStatus(idList, status);
    }

    @Override
    public CfBdCrmDiagnoseCronTaskDO getCronTaskForDiagnose(Long id, String uniqueCode) {
        return cfBdCrmDiagnoseCronTaskMapper.getCronTaskForDiagnose(id, uniqueCode);
    }

}
