package com.shuidihuzhu.cf.cfgrowthtoolapi.service.msg.impl;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.SeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthMsgEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.CfMsgBodyVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.CfMsgModelVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.MsgQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.msg.ICfMsgBodyService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.msg.ICfMsgModelService;
import com.shuidihuzhu.cf.dao.msg.CfMsgBodyDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2020-08-10
 */

@Service
@Slf4j
public class CfMsgBodyServiceImpl implements ICfMsgBodyService {

    @Autowired
    private CfMsgBodyDao cfMsgBodyDao;
    @Autowired
    private SeaAccountServiceDelegate seaAccountServiceDelegate;
    @Autowired
    private CustomEventPublisher customEventPublisher;
    @Autowired
    private ICfMsgModelService cfMsgModelServiceImpl;

    @Override
    public OpResult<List<CfMsgBodyVO>> listMsgBody(MsgQueryParam msgQueryParam) {
        if (Objects.isNull(msgQueryParam.getModelId())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CfMsgBodyVO> listMsgBody = cfMsgBodyDao.listMsgBody(msgQueryParam.getModelId());
        return OpResult.createSucResult(listMsgBody);
    }

    @Override
    public OpResult<Long> saveOrUpdateMsg(CfMsgBodyVO cfMsgBodyVO) {
        OpResult<Void> opResult = this.setOperatNameAndUserId(cfMsgBodyVO);
        if (opResult.isFail()){
            return OpResult.createFailResult(opResult.getErrorCode());
        }
        CfGrowthMsgEnums.MsgTypeEnum msgTypeEnum = CfGrowthMsgEnums.MsgTypeEnum.parse(cfMsgBodyVO.getMsgType());
        if (Objects.isNull(cfMsgBodyVO.getModelId()) || Objects.isNull(msgTypeEnum)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<CfMsgBodyVO> listMsgBody = cfMsgBodyDao.listMsgBody(cfMsgBodyVO.getModelId());
        OpResult<Long> checkResult = this.checkIsHaveSameMsgType(listMsgBody,cfMsgBodyVO);
        if (checkResult.isFail()){
            return checkResult;
        }
        JSONObject jsonObject = new JSONObject();
        if (Objects.nonNull(cfMsgBodyVO.getId())){
            CfMsgBodyVO msgBodyFromDb = listMsgBody.stream().filter(item -> item.getId().equals(cfMsgBodyVO.getId())).findFirst().orElse(null);
            if (Objects.isNull(msgBodyFromDb)){
                return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
            }
            cfMsgBodyDao.updateMsg(cfMsgBodyVO);
            jsonObject.put("preContent",msgBodyFromDb.queryOperateLog());
            jsonObject.put("curContent",cfMsgBodyVO.queryOperateLog());
            //添加操作日志
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(cfMsgBodyVO.getModelId()),
                    OperateTypeEnum.EDIT_MS_BODY.getDesc(),
                    OperateTypeEnum.EDIT_MS_BODY,
                    jsonObject.toJSONString(),
                    cfMsgBodyVO.getOperateUserId(),
                    cfMsgBodyVO.getMisName()));
        }else{
            cfMsgBodyDao.insertMsg(cfMsgBodyVO);
            //添加操作日志
            jsonObject.put("preContent","");
            jsonObject.put("curContent",cfMsgBodyVO.queryOperateLog());
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(cfMsgBodyVO.getModelId()),
                    OperateTypeEnum.SAVE_MS_BODY.getDesc(),
                    OperateTypeEnum.SAVE_MS_BODY,
                    jsonObject.toJSONString(),
                    cfMsgBodyVO.getOperateUserId(),
                    cfMsgBodyVO.getMisName()));
        }
        return OpResult.createSucResult(cfMsgBodyVO.getId());
    }

    @Override
    public OpResult<Void> deleteMsg(CfMsgBodyVO cfMsgBodyVO) {
        OpResult<Void> opResult = this.setOperatNameAndUserId(cfMsgBodyVO);
        if (opResult.isFail()){
            return OpResult.createFailResult(opResult.getErrorCode());
        }
        if (Objects.isNull(cfMsgBodyVO.getId()) || Objects.isNull(cfMsgBodyVO.getModelId())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfMsgModelVO cfMsgModelFromDb = cfMsgModelServiceImpl.getMsgModelById(cfMsgBodyVO.getModelId());
        if (cfMsgModelFromDb.getModelOnlineStatus().equals(CfGrowthMsgEnums.OnlineStatusEnum.ONLINE.getCode())){
            List<CfMsgBodyVO> msgBodyList = cfMsgBodyDao.listMsgBody(cfMsgBodyVO.getModelId());
            if (msgBodyList.size() <= 1){
                return OpResult.createFailResult(CfGrowthtoolErrorCode.MSG_BODY_DELETE);
            }
        }
        cfMsgBodyVO = cfMsgBodyDao.getMsgById(cfMsgBodyVO.getId());
        if (Objects.isNull(cfMsgBodyVO)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        cfMsgBodyDao.deleteMsg(cfMsgBodyVO);
        //添加操作日志
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("preContent",cfMsgBodyVO.queryOperateLog());
        jsonObject.put("curContent","");
        customEventPublisher.publish(new OperateLogEvent(this,
                String.valueOf(cfMsgBodyVO.getModelId()),
                OperateTypeEnum.DEL_MS_BODY.getDesc(),
                OperateTypeEnum.DEL_MS_BODY,
                jsonObject.toJSONString(),
                cfMsgBodyVO.getOperateUserId(),
                cfMsgBodyVO.getMisName()));
        return opResult;
    }

    @Override
    public List<CfMsgBodyVO> listMsgBodyByModelId(Long modelId) {
        return cfMsgBodyDao.listMsgBody(modelId);
    }

    @Override
    public OpResult<CfMsgBodyVO> getMsgBody(CfMsgBodyVO cfMsgBodyVO) {
        CfMsgBodyVO cfMsgBodyFromDb = cfMsgBodyDao.getMsgById(cfMsgBodyVO.getId());
        if (Objects.isNull(cfMsgBodyFromDb)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        return OpResult.createSucResult(cfMsgBodyFromDb);
    }

    /**
     * 检测是否可以添加或修改消息
     * @param listMsgBody
     * @param cfMsgBodyVO
     * @return
     */
    private OpResult<Long> checkIsHaveSameMsgType(List<CfMsgBodyVO> listMsgBody, CfMsgBodyVO cfMsgBodyVO) {

        for (CfMsgBodyVO msgBodyFromDb : listMsgBody){
            CfGrowthMsgEnums.MsgTypeEnum msgTypeEnumFromDb = CfGrowthMsgEnums.MsgTypeEnum.parse(msgBodyFromDb.getMsgType());
            CfGrowthMsgEnums.MsgTypeEnum msgTypeEnum = CfGrowthMsgEnums.MsgTypeEnum.parse(cfMsgBodyVO.getMsgType());
            if (Objects.isNull(msgTypeEnum) || Objects.isNull(msgTypeEnumFromDb)){
                return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
            }
            //存在相同类型
            if (msgTypeEnumFromDb.equals(msgTypeEnum) && !msgBodyFromDb.getId().equals(cfMsgBodyVO.getId())){
                return OpResult.createFailResult(CfGrowthtoolErrorCode.SAME_MSG_TYPE);
            }
            //判断是否存在互斥类型
            if (msgTypeEnumFromDb.getAttributeCode().equals(msgTypeEnum.getAttributeCode()) && !msgBodyFromDb.getId().equals(cfMsgBodyVO.getId())){
                return OpResult.createFailResult(CfGrowthtoolErrorCode.SAME_ATTRIBUTE_MSG_TYPE,"已存在"+msgTypeEnumFromDb.getDesc()+",不允许重复添加");
            }
        }
        return OpResult.createSucResult(null);
    }

    private OpResult<Void> setOperatNameAndUserId(CfMsgBodyVO msgBodyVO){
        long adminUserId = AuthSaasContext.getAuthSaasUserId();
        AdminUserAccountModel validUserAccountById = seaAccountServiceDelegate.getValidUserAccountById(adminUserId);
        if (Objects.isNull(validUserAccountById)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        }
        msgBodyVO.setOperateUserId(adminUserId);
        msgBodyVO.setMisName(validUserAccountById.getName());
        return OpResult.createSucResult();
    }
}
