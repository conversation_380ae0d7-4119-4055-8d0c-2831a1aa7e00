package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfKpiCaseMonthDonateData;

import java.util.List;

/**
 * kpi-月捐单数据统计(CfKpiCaseMonthDonateData)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-04 16:54:43
 */
public interface CfKpiCaseMonthDonateDataService {

    CfKpiCaseMonthDonateData queryById(long id);

    int insert(CfKpiCaseMonthDonateData cfKpiCaseMonthDonateData);

    int update(CfKpiCaseMonthDonateData cfKpiCaseMonthDonateData);

    List<CfKpiCaseMonthDonateData> listDonateDataByDayKey(String dayKey, String monthKey);

}
