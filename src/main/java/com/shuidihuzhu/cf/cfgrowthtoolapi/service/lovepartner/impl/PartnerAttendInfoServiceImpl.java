package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ApproveStatusEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.AttendPartnerInfoParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerApproveDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerAttendInfoDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdLovePartnerParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DateQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.PartnerAttendInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice.workwx.WorkWeixinContentBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.BdCrmContextUtil;
import com.shuidihuzhu.cf.dao.lovepartner.PartnerApproveDao;
import com.shuidihuzhu.cf.dao.lovepartner.PartnerAttendInfoDao;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 考勤信息
 * @author: zhengqiu
 * @date: 2021-09-02 10:32
 **/
@Service
@Slf4j
public class PartnerAttendInfoServiceImpl implements PartnerAttendInfoService {

    @Autowired
    private PartnerAttendInfoDao partnerAttendInfoDao;

    @Autowired
    private PartnerApproveDao partnerApproveDao;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgServiceImpl;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Override
    public String insert(AttendPartnerInfoParam param) {
        String attendDate = DateUtil.formatDate(param.getAttendDate());
        // 校验是否通过
        if (Objects.nonNull(partnerAttendInfoDao.getByAttendInfo(param.getUniqueCode(), attendDate))) {
            return "该日期已录入过考勤";
        }

        CrowdfundingVolunteer volunteer = BdCrmContextUtil.getCfVolunteer();
        CrowdfundingVolunteer leader = memberInfoService.listApplyLeaderWithDefaultExplicit(volunteer.getUniqueCode(), null);
        PartnerAttendInfoDTO attendInfoDTO = PartnerAttendInfoDTO.builder()
                .name(param.getName())
                .uniqueCode(param.getUniqueCode())
                .attendDate(param.getAttendDate())
                .attendType(param.getAttendType())
                .attendProveImgs(param.getAttendProveImgs())
                .attendSalary(param.getAttendSalary())
                .leaderUniqueCode(volunteer.getUniqueCode())
                .leaderName(volunteer.getVolunteerName())
                .approveStatus(ApproveStatusEnum.UN_DOING.getCode())
                .build();
        setApproveInfo(attendInfoDTO,leader);
        partnerAttendInfoDao.insert(attendInfoDTO);
        PartnerApproveDTO approveDTO = buildPartnerApproveDTO(attendInfoDTO,leader);
        if (Objects.nonNull(approveDTO)){
            partnerApproveDao.insert(approveDTO);
            sendApproveAttendMsg(attendInfoDTO, leader);
        }
        return "success";
    }

    @Override
    public String update(AttendPartnerInfoParam param, String uniqueCode) {
        CrowdfundingVolunteer volunteer = BdCrmContextUtil.getCfVolunteer();
        CrowdfundingVolunteer leader = memberInfoService.listApplyLeaderWithDefaultExplicit(volunteer.getUniqueCode(), null);
        String approveName = Optional.ofNullable(leader).map(CrowdfundingVolunteer::getVolunteerName).orElse("");
        partnerAttendInfoDao.updateInfo(uniqueCode, DateUtil.formatDate(param.getAttendDate()), param.getAttendSalary(), param.getAttendType(), param.getAttendProveImgs(), ApproveStatusEnum.UN_DOING.getCode(),approveName,getApproveOrgName(leader));
        PartnerAttendInfoDTO attendInfoDTO = partnerAttendInfoDao.getByAttendInfo(uniqueCode, DateUtil.formatDate(param.getAttendDate()));
        PartnerApproveDTO approveDTO = buildPartnerApproveDTO(attendInfoDTO,leader);
        if (Objects.nonNull(approveDTO)){
            partnerApproveDao.insert(approveDTO);
            sendApproveAttendMsg(attendInfoDTO, leader);
        }
        return "success";
    }

    @Override
    public PartnerAttendInfoDTO getInfo(String uniqueCode, String attendDate) {
        return partnerAttendInfoDao.getByAttendInfo(uniqueCode, attendDate);
    }

    @Override
    public List<PartnerAttendInfoDTO> listByPartnerUniqueCode(String partnerUniqueCode, DateQueryParam dateQueryParam) {
        return partnerAttendInfoDao.listByPartnerUniqueCode(partnerUniqueCode, dateQueryParam);
    }

    @Override
    public void sendApproveAttendMsg(PartnerAttendInfoDTO attendInfoDTO, CrowdfundingVolunteer leader) {
        if (Objects.isNull(leader)){
            return;
        }
        String title = "【爱心伙伴出勤审批】";
        String url = "https://www.shuidichou.com/bd/love-partner/attendance/record?status=1";
        WorkWeixinContentBuilder cb = WorkWeixinContentBuilder.create()
                .subject(title)
                .payload("姓名", attendInfoDTO.getName())
                .payload("出勤日期", DateUtil.formatDateTime(attendInfoDTO.getAttendDate()))
                .payload("负责人", attendInfoDTO.getLeaderName())
                .payload("操作", "<a href=\"" + url + "\">点击查看</a>");
        String content = cb.build();
        appPushCrmCaseMsgServiceImpl.sendMsg2Bd(leader, content, title, "");
    }

    @Override
    public PartnerAttendInfoDTO getAttendInfoById(Long id) {
        return partnerAttendInfoDao.getInfoId(id);
    }

    @Override
    public int adjustAttendApproveName(long id, CrowdfundingVolunteer approveVolunteer) {
        return partnerAttendInfoDao.adjustApproveNameById(id,approveVolunteer,getApproveOrgName(approveVolunteer));
    }

    @Override
    public int countLovePartnerAttend(BdLovePartnerParam bdLovePartnerParam, String uniqueCode) {
        return partnerAttendInfoDao.countLovePartnerAttend(bdLovePartnerParam,uniqueCode);
    }

    @Override
    public List<PartnerAttendInfoDTO> listLovePartnerAttend(BdLovePartnerParam bdLovePartnerParam, String uniqueCode) {
        return partnerAttendInfoDao.listLovePartnerAttend(bdLovePartnerParam,uniqueCode);
    }

    @Override
    public List<PartnerAttendInfoDTO> listAttendInfoWithCycleTimeAndUniqueCode(Date startTime, Date endTime, List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)){
            return Lists.newArrayList();
        }
        List<String> uniqueCodeList = uniqueCodes.stream().distinct().collect(Collectors.toList());
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> partnerAttendInfoDao.listAttendInfoWithCycleTimeAndUniqueCode(startTime,endTime,list))
        .reduce((total, item) -> {
            total.addAll(item);
            return total;
        }).orElse(Lists.newArrayList());
    }

    private PartnerApproveDTO buildPartnerApproveDTO(PartnerAttendInfoDTO attendInfoDTO,CrowdfundingVolunteer leader) {
        if (Objects.isNull(leader)){
            String content = String.format("顾问:%s,提交爱心伙伴:%s的考勤审批信息,找不到对应的审批人,请联系运营处理",
                    attendInfoDTO.getLeaderName(), attendInfoDTO.getName());
            if (applicationService.isDevelopment()) {
                content = "测试环境:" + System.lineSeparator() +
                        String.format("顾问:%s,提交爱心伙伴:%s的考勤审批信息,找不到对应的审批人,请联系测试处理", attendInfoDTO.getLeaderName(), attendInfoDTO.getName());
            }
            AlarmBotService.sentText("a7021cd6-ea5b-4ba2-8d74-3ecc09bbb2d3", content, null, null);
            return null;
        }
        return PartnerApproveDTO.builder()
                .uniqueCode(attendInfoDTO.getLeaderUniqueCode())
                .applyName(attendInfoDTO.getLeaderName())
                .type(2)
                .approveContent(JSON.toJSONString(attendInfoDTO))
                .bizId(attendInfoDTO.getId())
                .approveMis(leader.getMis())
                .approveName(leader.getVolunteerName())
                .approveUniqueCode(leader.getUniqueCode())
                .approveStatus(ApproveStatusEnum.UN_DOING.getCode())
                .build();
    }

    /**
     * 甚至审批人相关信息
     * @param attendInfoDTO
     * @param approveVolunteer
     */
    private void setApproveInfo(PartnerAttendInfoDTO attendInfoDTO, CrowdfundingVolunteer approveVolunteer) {
        if (Objects.nonNull(approveVolunteer)){
            attendInfoDTO.setApproveName(approveVolunteer.getVolunteerName());
            attendInfoDTO.setApproveOrgName(getApproveOrgName(approveVolunteer));
        }else{
            attendInfoDTO.setApproveName("");
            attendInfoDTO.setApproveOrgName("");
        }
    }

    /**
     * 获取组织名称
     * @param approveVolunteer
     * @return
     */
    private String getApproveOrgName(CrowdfundingVolunteer approveVolunteer){
        String approveOrgName = "";
        if (Objects.isNull(approveVolunteer)){
            return approveOrgName;
        }
        List<BdCrmOrganizationDO> bdCrmOrganizationList = memberInfoService.listForCByUniqueCode(approveVolunteer.getUniqueCode()).stream().sorted(Comparator.comparing(BdCrmOrganizationDO::depth)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bdCrmOrganizationList)){
            approveOrgName = crmSelfBuiltOrgReadService.listParentOrgAsChain(bdCrmOrganizationList.get(0).getId()).stream().map(item -> String.valueOf(item.getOrgName())).collect(Collectors.joining("-"));
        }
        return approveOrgName;
    }
}
