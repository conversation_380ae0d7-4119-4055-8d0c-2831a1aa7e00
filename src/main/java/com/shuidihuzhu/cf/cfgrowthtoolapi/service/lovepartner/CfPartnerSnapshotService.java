package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerSnapshotDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */
public interface CfPartnerSnapshotService {

    /**
     * 批量插入数据
     * @param partnerSnapshotDoList
     */
    int batchInsert(List<CfPartnerSnapshotDo> partnerSnapshotDoList);

    int deleteByCycleId(long cycleId);

    //根据人员查询对应的绩效
    List<CfPartnerSnapshotDo> listByCycleId(Long cycleId);

}
