package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdCrmWhaleClewInfoModel;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerClewRecordDo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */
public interface CfPartnerClewRecordService extends ICfLovePartnerService{

    /**
     * 新增数据
     * @param cfPartnerClewRecordDo
     * @return
     */
    int insert(CfPartnerClewRecordDo cfPartnerClewRecordDo);

    /**
     * 根据顾问uniqueCode和手机号，获取兼职录入线索列表
     *
     * @param leaderUniqueCode
     * @param encryptPhone
     * @param startTime
     * @param endTime
     * @return
     */
    List<CfPartnerClewRecordDo> listPartnerClewByUniqueCodeAndEncryptPhoneWithTime(String leaderUniqueCode, String encryptPhone, Date startTime, Date endTime);


    List<CfPartnerClewRecordDo> listPartnerClewByEncryptPhoneWithTime(String encryptPhone, Date startTime, Date endTime);

    /**
     * 根据线索id批量获取数据
     * @param clewIds
     * @return
     */
    List<CfPartnerClewRecordDo> listPartnerClewByClewIds(List<Long> clewIds);

    /**
     * 获取补录线索
     * @param startTime
     * @param endTime
     * @param leaderUniqueCode
     * @param partnerUniqueCode
     * @return
     */
    List<BdCrmWhaleClewInfoModel> clewManageWithMaskPhone(Date startTime, Date endTime, String leaderUniqueCode, String partnerUniqueCode);
}
