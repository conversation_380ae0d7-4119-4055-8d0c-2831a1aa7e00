package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.PreVolunteerOrgInfoRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.OrgMembersResult;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.client.cf.growthtool.model.*;

import java.util.List;

public interface ICfCrmVolunteerOrgnization {



    PreposeMaterialCommitNoticeModel buildPreposeMaterialCommitNoticeModel(PreposeMaterialModel.MaterialInfoVo materialInfoVo,
                                                                           ClewCrowdfundingReportRelation clewCrowdfundingReportRelation,
                                                                           String mis,
                                                                           String uniqueCode,
                                                                           ClewCrowdfundingReportRelation.TypeEnum typeEnum);


    List<BdCrmVolunteerOrgnizationSimpleModel> getAllUserByOrgId(int orgId);

    List<BdCrmVolunteerOrgnizationSimpleModel> getAllUserByOrgIds(List<Long> orgIds);

    List<OrgInfoModel> getSubOrgByOrgIdFromLoadingCache(int orgId);

    List<OrgInfoModel> getSubOrgByOrgId(int orgId);

    OrgInfoModel getOrgByOrgId(int orgId);

    List<BdCrmVolunteerOrgnizationSimpleModel> getBdCrmVolunteerOrgnizationSimpleModelByMisList(List<String> misList);

    List<BdCrmVolunteerOrgnizationSimpleModel> getBdCrmVolunteerOrgnizationSimpleModelByMisNameList(List<String> misNameList);

    List<OrgInfoModel> getOfflineAllOrg();

    /**
     * 使用的是自建组织
     */
    AdminOrganization getUserOrganization(String mis);

    OrgMembersResult getOrgMembers(int parentOrgId);

    /**
     * @param orgIdList
     * @return
     */
    List<OrgInfoModel> getOrgListByOrgId(List<Integer> orgIdList);

	List<String> getUniqueCodeListByOrgId(int orgId);

    OrgInfoModel getParentOrgByOrgId(int orgId);

    PreVolunteerOrgInfoRelationDO getPreVolunteerOrgInfoRelationDO(String uniqueCode);
}
