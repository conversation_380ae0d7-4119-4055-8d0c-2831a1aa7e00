package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.MQTagCons;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.SeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmActivityHomepagePopupDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfBdCrmActivityHomepagePopupStatus;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OperateLogSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.SeaCfBdCrmActivityHomepagePopupVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdCrmActivityHomepagePopupVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OperatorLogVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.CfBdCrmActivityHomepagePopupParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCrmActivityHomepagePopupService;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmActivityHomepagePopupDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.admin.PageUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2021/1/8 下午5:56
 */
@Service
public class CfBdCrmActivityHomepagePopupServiceImpl implements ICfBdCrmActivityHomepagePopupService {

    @Resource
    private CfBdCrmActivityHomepagePopupDao cfBdCrmActivityHomepagePopupDao;
    @Autowired
    private SeaAccountServiceDelegate seaAccountServiceDelegate;
    @Autowired
    private CustomEventPublisher customEventPublisher;
    @Autowired
    private IOperateLogService operateLogService;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;
    @Autowired(required = false)
    private Producer producer;

    private static final String REDIS_KEY = "cf:bd:crm:activity:homepage:popup:volunteer:";

    @Override
    public Response<String> saveOrUpdate(CfBdCrmActivityHomepagePopupParam cfBdCrmActivityHomepagePopupParam, long authSaasUserId) {
        List<CfBdCrmActivityHomepagePopupDO> cfBdCrmActivityHomepagePopupDOList = cfBdCrmActivityHomepagePopupDao.listByPublishStatus(CfBdCrmActivityHomepagePopupDO.PublishStatus.ONLINE.getCode(), cfBdCrmActivityHomepagePopupParam.getPopupType());
        //时间格式转换
        cfBdCrmActivityHomepagePopupParam.getDateFromLongString();
        AdminUserAccountModel adminUserAccountModel = seaAccountServiceDelegate.getValidUserAccountById(authSaasUserId);
        int res = 0;
        if (cfBdCrmActivityHomepagePopupParam.getId() == null || cfBdCrmActivityHomepagePopupParam.getId() == 0) {
            if (CollectionUtils.isNotEmpty(cfBdCrmActivityHomepagePopupDOList)) {
                Optional<CfBdCrmActivityHomepagePopupDO> optional = cfBdCrmActivityHomepagePopupDOList.stream().filter(v -> Objects.equals(v.getSort(), cfBdCrmActivityHomepagePopupParam.getSort())).findFirst();
                if (optional.isPresent()) {
                    return NewResponseUtil.makeFail("填写的优先级数字不可与在线活动优先级一致");
                }
            }
            long operatorUserId = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getId).orElse(authSaasUserId);
            String operatorUserName = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse("");
            cfBdCrmActivityHomepagePopupParam.setOperatorUserId(operatorUserId);
            cfBdCrmActivityHomepagePopupParam.setOperatorUserName(operatorUserName);
            res = cfBdCrmActivityHomepagePopupDao.insertSelective(cfBdCrmActivityHomepagePopupParam);
            if (res > 0) {
                sendMq(cfBdCrmActivityHomepagePopupParam);
                saveRecord(cfBdCrmActivityHomepagePopupParam.getId(), OperateTypeEnum.ACTIVITY_HOMEPAGE_POPUP_SAVE, cfBdCrmActivityHomepagePopupParam.getInsertRecordLog(), authSaasUserId, operatorUserName);
            }

        } else {
            if (CollectionUtils.isNotEmpty(cfBdCrmActivityHomepagePopupDOList)) {
                Optional<CfBdCrmActivityHomepagePopupDO> optional = cfBdCrmActivityHomepagePopupDOList.stream().filter(v -> !v.getId().equals(cfBdCrmActivityHomepagePopupParam.getId())
                        && Objects.equals(v.getSort(), cfBdCrmActivityHomepagePopupParam.getSort())).findFirst();
                if (optional.isPresent()) {
                    return NewResponseUtil.makeFail("填写的优先级数字不可与在线活动优先级一致");
                }
            }
            res = cfBdCrmActivityHomepagePopupDao.updateByIdSelective(cfBdCrmActivityHomepagePopupParam);
            if (res > 0) {
                sendMq(cfBdCrmActivityHomepagePopupParam);
                String operatorUserName = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse("");
                saveRecord(cfBdCrmActivityHomepagePopupParam.getId(), OperateTypeEnum.ACTIVITY_HOMEPAGE_POPUP_UPDATE, cfBdCrmActivityHomepagePopupParam.getUpdateRecordLog(), authSaasUserId, operatorUserName);
            }
        }
        return res > 0 ? NewResponseUtil.makeSuccess("操作成功") : NewResponseUtil.makeFail("当前没修改，保存失败");
    }

    @Override
    public Response<String> upOrDown(long id, int publishStatus, long authSaasUserId) {
        CfBdCrmActivityHomepagePopupDO cfBdCrmActivityHomepagePopupDO = cfBdCrmActivityHomepagePopupDao.getById(id);
        if (cfBdCrmActivityHomepagePopupDO == null) {
            return NewResponseUtil.makeFail("活动不存在");
        }

        if (publishStatus == CfBdCrmActivityHomepagePopupDO.PublishStatus.ONLINE.getCode()) {
            long now = System.currentTimeMillis();
            if (cfBdCrmActivityHomepagePopupDO.getEndTime().getTime() < now) {
                return NewResponseUtil.makeFail("活动已结束，不可上线");
            }

            List<CfBdCrmActivityHomepagePopupDO> cfBdCrmActivityHomepagePopupDOList = cfBdCrmActivityHomepagePopupDao.listByPublishStatus(CfBdCrmActivityHomepagePopupDO.PublishStatus.ONLINE.getCode(), cfBdCrmActivityHomepagePopupDO.getPopupType());
            if (CollectionUtils.isNotEmpty(cfBdCrmActivityHomepagePopupDOList)) {
                Optional<CfBdCrmActivityHomepagePopupDO> optional = cfBdCrmActivityHomepagePopupDOList.stream().filter(v -> Objects.equals(v.getSort(), cfBdCrmActivityHomepagePopupDO.getSort())).findFirst();
                if (optional.isPresent()) {
                    return NewResponseUtil.makeFail("填写的优先级数字不可与在线活动优先级一致");
                }
            }
        }

        int res = cfBdCrmActivityHomepagePopupDao.upOrDown(id, publishStatus);
        if (res > 0) {
            OperateTypeEnum operateTypeEnum = OperateTypeEnum.ACTIVITY_HOMEPAGE_POPUP_ONLINE;
            if (publishStatus == CfBdCrmActivityHomepagePopupDO.PublishStatus.OFFLINE.getCode()) {
                operateTypeEnum = OperateTypeEnum.ACTIVITY_HOMEPAGE_POPUP_OFFLINE;
            }
            AdminUserAccountModel adminUserAccountModel = seaAccountServiceDelegate.getValidUserAccountById(authSaasUserId);
            String recordLog = "";
            if (publishStatus == CfBdCrmActivityHomepagePopupDO.PublishStatus.ONLINE.getCode()) {
                //上线时间：2024-7-10 13:13:59 ，生效人群：全部可见/部分可见，可见区域：
                CfBdCrmActivityHomepagePopupDO crmActivityHomepagePopupDO = cfBdCrmActivityHomepagePopupDao.getById(id);
                if (crmActivityHomepagePopupDO != null) {
                    String permissionValue = crmActivityHomepagePopupDO.getPermissionValue();
                    CfBdCrmActivityHomepagePopupDO.PermissionType permissionType = CfBdCrmActivityHomepagePopupDO.PermissionType
                            .getByCode(crmActivityHomepagePopupDO.getPermissionType());
                    if (CfBdCrmActivityHomepagePopupDO.PermissionType.PUBLIC.getCode() == permissionType.getCode()) {
                        permissionValue = "全部";
                    }else if(CfBdCrmActivityHomepagePopupDO.PermissionType.PART_ROLE.getCode() == permissionType.getCode()){
                        List<String> levelList = Splitter.on(",").splitToList(crmActivityHomepagePopupDO.getPermissionValue());
                        StringBuilder sb = new StringBuilder();
                        for (String level : levelList) {
                            CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(Integer.parseInt(level));
                            if(roleEnum != null){
                                if(StringUtils.isNotEmpty(sb.toString())){
                                    sb.append(",");
                                }
                                sb.append(roleEnum.getDesc());
                            }
                        }
                        permissionValue = sb.toString();
                    }
                    recordLog = "上线时间：" + DateUtil.getCurrentDateTimeStr() + "，生效人群：" +
                            permissionType.getDesc() + "，可见区域：" + permissionValue;
                }
            } else {
                recordLog = "下线时间：" + DateUtil.getCurrentDateTimeStr();
            }
            saveRecord(id, operateTypeEnum, recordLog, authSaasUserId, Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse(""));
        }
        return res > 0 ? NewResponseUtil.makeSuccess("操作成功") : NewResponseUtil.makeFail("当前没修改，保存失败");
    }

    @Override
    public Response<String> del(long id, long authSaasUserId) {

        CfBdCrmActivityHomepagePopupDO cfBdCrmActivityHomepagePopupDO = cfBdCrmActivityHomepagePopupDao.getById(id);
        if (cfBdCrmActivityHomepagePopupDO == null) {
            return NewResponseUtil.makeFail("活动不存在");
        }
        if (cfBdCrmActivityHomepagePopupDO.getPublishStatus() != CfBdCrmActivityHomepagePopupDO.PublishStatus.NOTONLINE.getCode()) {
            return NewResponseUtil.makeFail("仅可删除从未上线活动，上线中活动不可删除，上线再下线活动不可删除");
        }

        int res = cfBdCrmActivityHomepagePopupDao.del(id);
        if (res > 0) {
            AdminUserAccountModel adminUserAccountModel = seaAccountServiceDelegate.getValidUserAccountById(authSaasUserId);
            saveRecord(id, OperateTypeEnum.ACTIVITY_HOMEPAGE_POPUP_DEL, "", authSaasUserId, Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse(""));
        }
        return res > 0 ? NewResponseUtil.makeSuccess("操作成功") : NewResponseUtil.makeFail("当前没修改，保存失败");
    }

    @Override
    public Response<CfBdCrmActivityHomepagePopupDO> get(long id) {
        CfBdCrmActivityHomepagePopupDO cfBdCrmActivityHomepagePopupDO = cfBdCrmActivityHomepagePopupDao.getById(id);
        return NewResponseUtil.makeSuccess(cfBdCrmActivityHomepagePopupDO);
    }

    @Override
    public Response<CommonResultModel<OperatorLogVO>> getOperateLog(OperateLogSearchModel searchModel) {
        CommonResultModel<OperatorLogVO> commonResultModel = operateLogService.getOperateLog(searchModel, OperateTypeEnum.getEnumListByAttriButeType(OperateTypeEnum.ACTIVITY_HOMEPAGE_POPUP_SAVE.getAttributeType()));
        return NewResponseUtil.makeSuccess(commonResultModel);
    }

    @Override
    public Response<Map<String, Object>> getList(String activityName, Integer publishStatus, String startTime, String endTime, int pageNum, int pageSize, Integer popupType) {
        PageHelper.startPage(pageNum, pageSize);
        List<SeaCfBdCrmActivityHomepagePopupVo> cfBdCrmActivityHomepagePopupVoList = cfBdCrmActivityHomepagePopupDao.getList(activityName, publishStatus, startTime, endTime, popupType);

        //填充数据
        buildCfBdCrmActivityHomepagePopupVoList(cfBdCrmActivityHomepagePopupVoList);

        Map<String, Object> result = Maps.newHashMap();
        result.put("pagination", PageUtil.transform2PageMap(cfBdCrmActivityHomepagePopupVoList));
        result.put("data", cfBdCrmActivityHomepagePopupVoList);
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<CfBdCrmActivityHomepagePopupVo> get(CrowdfundingVolunteer volunteer, Integer popupType) {
        //获取所有上线活动
        List<CfBdCrmActivityHomepagePopupDO> cfBdCrmActivityHomepagePopupDOList = cfBdCrmActivityHomepagePopupDao.listByPublishStatus(CfBdCrmActivityHomepagePopupDO.PublishStatus.ONLINE.getCode(), popupType);
        if (CollectionUtils.isEmpty(cfBdCrmActivityHomepagePopupDOList)) {
            return NewResponseUtil.makeSuccess();
        }
        //活动开始时间在当前时间之前的，过滤掉
        cfBdCrmActivityHomepagePopupDOList = cfBdCrmActivityHomepagePopupDOList.stream().filter(v -> v.getStartTime().before(new Date())).collect(Collectors.toList());

        List<CfBdCrmActivityHomepagePopupDO> activityHomepagePopupDOList = Lists.newArrayList();
        for (CfBdCrmActivityHomepagePopupDO cfBdCrmActivityHomepagePopupDO : cfBdCrmActivityHomepagePopupDOList) {
            if (cfBdCrmActivityHomepagePopupDO.getPermissionType() == CfBdCrmActivityHomepagePopupDO.PermissionType.PUBLIC.getCode()) {
                activityHomepagePopupDOList.add(cfBdCrmActivityHomepagePopupDO);
            } else if (cfBdCrmActivityHomepagePopupDO.getPermissionType() == CfBdCrmActivityHomepagePopupDO.PermissionType.PART_ROLE.getCode()) {
                List<String> levelList = Splitter.on(",").splitToList(cfBdCrmActivityHomepagePopupDO.getPermissionValue());
                for (String level : levelList) {
                    if (Integer.parseInt(level) == volunteer.getLevel()) {
                        activityHomepagePopupDOList.add(cfBdCrmActivityHomepagePopupDO);
                        break;
                    }
                }
            } else if (cfBdCrmActivityHomepagePopupDO.getPermissionType() == CfBdCrmActivityHomepagePopupDO.PermissionType.PART_PERSON.getCode()) {
                List<String> uniqueCodeList = Splitter.on(",").splitToList(cfBdCrmActivityHomepagePopupDO.getPermissionValue());
                for (String uniqueCode : uniqueCodeList) {
                    if (uniqueCode.equals(volunteer.getUniqueCode())) {
                        activityHomepagePopupDOList.add(cfBdCrmActivityHomepagePopupDO);
                        break;
                    }
                }
            }
        }

        //获取当前时间
        long now = System.currentTimeMillis();
        //获取顾问对活动的冷却期信息
        List<CoolingTime> coolingTimes = redissonHandler.getList(getRedisKey(volunteer.getUniqueCode()), CoolingTime.class);
        if (CollectionUtils.isNotEmpty(coolingTimes)) {
            Set<Long> ids = coolingTimes.stream().map(CoolingTime::getId).collect(Collectors.toSet());
            //获取没有展示过的活动
            List<CfBdCrmActivityHomepagePopupDO> cfBdCrmActivityHomepagePopupDOS = activityHomepagePopupDOList.stream().filter(v -> !ids.contains(v.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(cfBdCrmActivityHomepagePopupDOS)) {
                //过滤掉还在冷却期的活动
                Set<Long> idSet = coolingTimes.stream().filter(v -> v.getTime() > now).map(CoolingTime::getId).collect(Collectors.toSet());
                activityHomepagePopupDOList = activityHomepagePopupDOList.stream().filter(v -> !idSet.contains(v.getId())).collect(Collectors.toList());
            } else {
                activityHomepagePopupDOList = cfBdCrmActivityHomepagePopupDOS;
            }
        }

        //获取优先级最高的活动
        Optional<CfBdCrmActivityHomepagePopupDO> optional = activityHomepagePopupDOList.stream().min(Comparator.comparing(CfBdCrmActivityHomepagePopupDO::getSort));
        CfBdCrmActivityHomepagePopupDO result = optional.orElse(null);
        if (result != null) {
            //更新冷却期信息
            coolingTimes = CollectionUtils.isNotEmpty(coolingTimes) ? coolingTimes : Lists.newArrayList();
            Optional<CoolingTime> optionalCoolingTime = coolingTimes.stream().filter(v -> result.getId().equals(v.getId())).findFirst();
            if (optionalCoolingTime.isEmpty()) {
                CoolingTime coolingTime = new CoolingTime();
                coolingTime.setId(result.getId());
                coolingTime.setTime(result.getCoolingTime() + now);
                coolingTimes.add(coolingTime);
            } else {
                for (CoolingTime coolingTime : coolingTimes) {
                    if (result.getId().equals(coolingTime.getId())) {
                        coolingTime.setTime(result.getCoolingTime() + now);
                    }
                }
            }
            redissonHandler.addListEX(getRedisKey(volunteer.getUniqueCode()), coolingTimes, TimeUnit.DAYS.toSeconds(5));
        }
        return NewResponseUtil.makeSuccess(CfBdCrmActivityHomepagePopupVo.buildCfBdCrmActivityHomepagePopupVo(result));
    }

    /**
     * 添加操作记录
     */
    private void saveRecord(long id, OperateTypeEnum operateTypeEnum, String recordLog, long authSaasUserId, String operatorUserName) {
        customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(id), operateTypeEnum.getDesc(),
                operateTypeEnum, recordLog, authSaasUserId, operatorUserName));
    }

    private void buildCfBdCrmActivityHomepagePopupVoList(List<SeaCfBdCrmActivityHomepagePopupVo> cfBdCrmActivityHomepagePopupVoList) {
        for (SeaCfBdCrmActivityHomepagePopupVo cfBdCrmActivityHomepagePopupVo : cfBdCrmActivityHomepagePopupVoList) {
            cfBdCrmActivityHomepagePopupVo.setPublishStatusDesc(CfBdCrmActivityHomepagePopupDO.PublishStatus.getDescByCode(cfBdCrmActivityHomepagePopupVo.getPublishStatus()));
            cfBdCrmActivityHomepagePopupVo.setValidTime(DateUtil.formatDateTime(cfBdCrmActivityHomepagePopupVo.getStartTime()) + " - " + DateUtil.formatDateTime(cfBdCrmActivityHomepagePopupVo.getEndTime()));
            cfBdCrmActivityHomepagePopupVo.setCreateTimeStr(DateUtil.formatDateTime(cfBdCrmActivityHomepagePopupVo.getCreateTime()));
        }
    }

    private String getRedisKey(String uniqueCode) {
        return REDIS_KEY + uniqueCode;
    }

    @Data
    static class CoolingTime {
        @ApiModelProperty("活动id")
        private long id;
        @ApiModelProperty("冷却时间结束时间")
        private long time;
    }

    private void sendMq(CfBdCrmActivityHomepagePopupParam cfBdCrmActivityHomepagePopupParam) {
        CfBdCrmActivityHomepagePopupStatus cfBdCrmActivityHomepagePopupStatus = new CfBdCrmActivityHomepagePopupStatus();
        cfBdCrmActivityHomepagePopupStatus.setId(cfBdCrmActivityHomepagePopupParam.getId());
        cfBdCrmActivityHomepagePopupStatus.setEndTime(cfBdCrmActivityHomepagePopupParam.getEndTime().getTime());

        Message message = Message.ofSchedule(MQTopicCons.CF, MQTagCons.XJY_POPUP_ACTIVITY_OFFLINE_MSG,
                MQTagCons.XJY_POPUP_ACTIVITY_OFFLINE_MSG + "_" + cfBdCrmActivityHomepagePopupParam.getId() + "_" + DateUtil.getYMDStringByDate(new Date()), cfBdCrmActivityHomepagePopupStatus, cfBdCrmActivityHomepagePopupParam.getEndTime().getTime() / 1000);
        producer.send(message);
    }

}
