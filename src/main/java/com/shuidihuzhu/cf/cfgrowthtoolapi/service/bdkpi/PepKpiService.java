package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.PepClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiCaseScoreVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.kpiv2.PerformanceResultExtendParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.dataconvert.UserCalResultConvert;
import com.shuidihuzhu.cf.client.performance.PepUserLotData;
import com.shuidihuzhu.cf.client.performance.calResult.*;
import com.shuidihuzhu.cf.client.performance.model.GrowthtoolPepCaseScoreModel;
import com.shuidihuzhu.cf.client.performance.model.GrowthtoolUserCaseScoreModel;
import com.shuidihuzhu.cf.client.performance.param.PepUserLotParam;
import com.shuidihuzhu.cf.client.performance.query.PepCaseProgressParam;
import com.shuidihuzhu.cf.client.performance.query.PepTeamParam;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepGrowthtoolCaseIdTemplate;
import com.shuidihuzhu.cf.performance.data.meta.PepPartnerHcModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2022-08-17 16:11
 **/
@Service
@Slf4j
public class PepKpiService {

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private PepClientDelegate pepClientDelegate;

    @Autowired
    private ICrmOrganizationRelationService relationService;

    @Autowired
    private ICrmSelfBuiltOrgReadService orgReadService;

    @Autowired
    private ICrmMemberInfoService memberService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;


    public Response<List<FactCaseDetailModel>> listFactCalDetail(PerformanceResultExtendParam param) {
        if (param.getFactId() <= 0) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        param.setFactSourceType(Optional.ofNullable(param.getFactSourceType()).orElse(0));
        log.debug("listFactCalDetail:{}", param);
        List<FactDetailModel> listResponse = pepClientDelegate.listFactCalDetail(param);
        if (CollectionUtils.isEmpty(listResponse)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        ProcedureForCModel procedure = pepClientDelegate.getByProduceId(param.getProcedureId());
        //获取数据
        FactDetailDataMap factDetailDataMap = createFactDetailDateMap(listResponse, procedure, param);
        //组装数据
        List<FactCaseDetailModel> result = getFactCaseDetailModels(listResponse, factDetailDataMap);
        return NewResponseUtil.makeSuccess(result);
    }


    /**
     * 批量获取数据详情
     *
     * @param procedure
     * @return
     */
    @NotNull
    private FactDetailDataMap createFactDetailDateMap(List<FactDetailModel> factDetailModels, ProcedureForCModel procedure, PerformanceResultExtendParam param) {
        FactDetailDataMap factDetailDataMap = new FactDetailDataMap();
        if (CollectionUtils.isEmpty(factDetailModels)) {
            return factDetailDataMap;
        }
        Map<Long, PepGrowthtoolCaseIdTemplate> caseMap = Maps.newHashMap();
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(param.getUserId());
        factDetailDataMap.setVolunteer(volunteer);
        if (procedure == null) {
            return factDetailDataMap;
        }
        FactDetailModel factDetailModel = factDetailModels.get(0);
        FactCaseDetailModel.FiledTypeEnum filedTypeEnum = FactCaseDetailModel.parseByUniqueKey(factDetailModel.getUniqueKey());
        //案例模块
        if (Objects.equals(filedTypeEnum.getCode(), FactCaseDetailModel.FiledTypeEnum.caseId.getCode())) {
            FetchCaseIdsMiddleModel middleModel = listAllCaseIds(factDetailModels);
            Set<String> uniqueValues = middleModel.getUniqueValues();
            //如果没有案例信息，直接返回
            if (CollectionUtils.isEmpty(uniqueValues)) {
                return factDetailDataMap;
            }
            long lotId = middleModel.getLotId();
            //根据 userId + calcUnique + factId 确定本次使用的数据明细
            PepUserLotParam pepUserLotParam = new PepUserLotParam();
            pepUserLotParam.setLotId(lotId);
            pepUserLotParam.setLotUniqueValues(Lists.newArrayList(uniqueValues));
            PepUserLotData pepUserLotData = pepClientDelegate.listByLotIdAndUniqueValue(pepUserLotParam);
            if (pepUserLotData == null) {
                return factDetailDataMap;
            }
            List<JSONObject> jsonObjects = pepUserLotData.getJsonObjects();
            if (CollectionUtils.isEmpty(jsonObjects)) {
                return factDetailDataMap;
            }
            String simpleName = pepUserLotData.getDataType();
            PepCaseTemplateModel pepCaseTemplateModel = PepCaseTemplateModel.MAP.getOrDefault(simpleName, PepCaseTemplateModel.DEFAULT);
            List<CaseShowBaseInfo> showBaseInfoList = jsonObjects.stream()
                    .map(item -> {
                        Object javaObject = JSONObject.toJavaObject(item, pepCaseTemplateModel.getClazz());
                        return (CaseShowBaseInfo) pepCaseTemplateModel.getConvertor().apply(javaObject);
                    }).collect(Collectors.toList());
            boolean needFillInfo = pepCaseTemplateModel.isNeedFillInfo();
            if (needFillInfo) {
                List<Integer> caseIds = showBaseInfoList.stream()
                        .filter(item -> item.getCaseId() != null)
                        .map(item -> item.getCaseId().intValue())
                        .distinct()
                        .collect(Collectors.toList());
                Map<Long, CrowdfundingInfo> crowdfundingInfoMap = crowdFundingFeignDelegate.getCrowdfundingListById(caseIds)
                        .stream()
                        .collect(Collectors.toMap(item -> Long.valueOf(item.getId()), Function.identity(), (before, after) -> before));
                Map<Long, CfBdCaseInfoDo> baseCaseInfoMap = cfBdCaseInfoService.listCaseInfoByCaseIds(caseIds)
                        .stream()
                        .collect(Collectors.toMap(item -> Long.valueOf(item.getCaseId()), Function.identity(), (before, after) -> before));
                showBaseInfoList.forEach(item -> item.fillByCaseInfo(baseCaseInfoMap.get(item.getCaseId()), crowdfundingInfoMap.get(item.getCaseId())));
            }
            factDetailDataMap.setCaseMap(showBaseInfoList.stream()
                    .collect(Collectors.toMap(CaseShowBaseInfo::getCaseId, Function.identity(), (before, after) -> before)));

            if (Objects.equals(filedTypeEnum.getCode(), FactCaseDetailModel.FiledTypeEnum.caseId.getCode()) && StringUtils.isNumeric(factDetailModel.getUniqueValue())) {
                List<Long> caseIds = factDetailModels.stream().map(FactDetailModel::getUniqueValue).map(Long::valueOf).collect(Collectors.toList());
                Map<Long, GrowthtoolPepCaseScoreModel> caseScoreModelMap = pepClientDelegate.caseProgress(new PepCaseProgressParam()
                                .setCaseIds(caseIds)
                                .setUserId(param.getUserId())
                                .setProcedureId(param.getProcedureId())
                                .setFactId(param.getFactId())
                                .setCalcUnique(param.getCalcUnique()))
                        .stream()
                        .collect(Collectors.toMap(GrowthtoolPepCaseScoreModel::getCaseId, Function.identity(), (before, after) -> before));
                factDetailDataMap.setCaseScoreModelMap(caseScoreModelMap);
            }
        }
        //hc 模块
        if (Objects.equals(filedTypeEnum.getCode(), FactCaseDetailModel.FiledTypeEnum.hc_detail.getCode())) {
            //根据 userId + calcUnique + factId 确定本次使用的数据明细
            PepUserLotParam pepUserLotParam = new PepUserLotParam();
            long lotId = factDetailModel.getLotId();
            pepUserLotParam.setLotId(lotId);
            List<String> uniqueValues = factDetailModels.stream()
                    .map(FactDetailModel::getUniqueValue)
                    .collect(Collectors.toList());
            pepUserLotParam.setLotUniqueValues(uniqueValues);
            PepUserLotData pepUserLotData = pepClientDelegate.listByLotIdAndUniqueValue(pepUserLotParam);
            List<JSONObject> jsonObjects = pepUserLotData.getJsonObjects();
            if (CollectionUtils.isEmpty(jsonObjects)) {
                return factDetailDataMap;
            }
            Map<String, KpiHcModel> kpiHcModelMap = jsonObjects.stream()
                    .map(item -> JSONObject.toJavaObject(item, PepPartnerHcModel.class))
                    .map(item -> {
                        KpiHcModel kpiHcModel = new KpiHcModel();
                        kpiHcModel.setBelongUniqueCode(item.getBelong_unique_code());
                        kpiHcModel.setBelongUniqueName(item.getBelong_unique_name());
                        kpiHcModel.setHcTarget(item.getHc_target());
                        kpiHcModel.setServiceCaseNum(item.getService_case_num());
                        kpiHcModel.setValidHcFlag(item.getValid_hc_flag());
                        return kpiHcModel;
                    }).collect(Collectors.toMap(KpiHcModel::getBelongUniqueCode, Function.identity(), (before, after) -> before));
            factDetailDataMap.setKpiHcModelMap(kpiHcModelMap);
        }
        return factDetailDataMap;
    }


    @Accessors(chain = true)
    @Data
    static class FetchCaseIdsMiddleModel {
        Set<String> uniqueValues = Sets.newHashSet();
        boolean hasIllegalCase = false;
        long lotId = 0L;
    }


    public static FetchCaseIdsMiddleModel listAllCaseIds(List<FactDetailModel> factDetailModels) {
        FetchCaseIdsMiddleModel middleModel = new FetchCaseIdsMiddleModel();
        Set<String> uniqueValues = Sets.newHashSet();
        for (FactDetailModel factDetailModel : factDetailModels) {
            if (Objects.equals(factDetailModel.getUniqueKey(), FactCaseDetailModel.FiledTypeEnum.caseId.getUniqueKey())) {
                uniqueValues.add(factDetailModel.getUniqueValue());
                middleModel.setHasIllegalCase(true);
                middleModel.setLotId(factDetailModel.getLotId());
            }
        }
        middleModel.setUniqueValues(uniqueValues);
        return middleModel;
    }


    @Data
    static class FactDetailDataMap {
        //人员信息
        CrowdfundingVolunteer volunteer;
        //案例信息
        Map<Long, CaseShowBaseInfo> caseMap;
        //案例梯度相关信息
        Map<Long, GrowthtoolPepCaseScoreModel> caseScoreModelMap;
        //hc 相关信息
        Map<String, KpiHcModel> kpiHcModelMap;
    }


    //案例展示的基础信息
    @Data
    public static class CaseShowBaseInfo {
        @ApiModelProperty("案例表主键id")
        private Long caseId;
        @ApiModelProperty("'案例uuid'")
        private String infoUuid;
        @ApiModelProperty("筹款标题")
        protected String title;
        @ApiModelProperty("患者姓名")
        private String patientName;
        @ApiModelProperty("案例已筹金额 单位:分")
        private Long caseAmount;
        @ApiModelProperty("案例捐单")
        private Long donateNum;
        @ApiModelProperty("是否是违规案例,1:是")
        private int illegalCase;

        public void fillByCaseInfo(CfBdCaseInfoDo cfBdCaseInfoDo, CrowdfundingInfo crowdfundingInfo) {
            if (crowdfundingInfo != null) {
                this.infoUuid = crowdfundingInfo.getInfoId();
                this.title = crowdfundingInfo.getTitle();
            }
            if (cfBdCaseInfoDo != null) {
                this.patientName = cfBdCaseInfoDo.getPatientName();
            }
        }
    }


    @NotNull
    private List<FactCaseDetailModel> getFactCaseDetailModels(List<FactDetailModel> listResponse, FactDetailDataMap factDetailDataMap) {
        List<FactCaseDetailModel> result = Lists.newArrayList();
        Map<Long, CaseShowBaseInfo> caseMap = Optional.ofNullable(factDetailDataMap.getCaseMap()).orElse(Maps.newHashMap());
        Map<Long, GrowthtoolPepCaseScoreModel> caseScoreModelMap = Optional.ofNullable(factDetailDataMap.getCaseScoreModelMap()).orElse(Maps.newHashMap());
        Map<String, KpiHcModel> kpiHcModelMap = Optional.ofNullable(factDetailDataMap.getKpiHcModelMap()).orElse(Maps.newHashMap());
        boolean hcModel = false;
        for (FactDetailModel item : listResponse) {
            FactCaseDetailModel detailModel = new FactCaseDetailModel();
            detailModel.setUniqueKey(item.getUniqueKey());
            detailModel.setUniqueValue(item.getUniqueValue());
            detailModel.setResult(item.getResult());
            detailModel.setHasMoreInfo(item.isHasMoreInfo());
            detailModel.setNextFactId(item.getNextFactId());
            CrowdfundingVolunteer volunteer = factDetailDataMap.getVolunteer();
            if (volunteer != null) {
                detailModel.setUserName(volunteer.getVolunteerName());
            }
            FactCaseDetailModel.FiledTypeEnum filedTypeEnum = FactCaseDetailModel.parseByUniqueKey(item.getUniqueKey());
            detailModel.setFiledType(filedTypeEnum.getCode());
            //填充案例信息
            if (Objects.equals(filedTypeEnum.getCode(), FactCaseDetailModel.FiledTypeEnum.caseId.getCode()) &&
                    StringUtils.isNumeric(item.getUniqueValue())) {
                Long caseId = Long.valueOf(item.getUniqueValue());
                CaseShowBaseInfo caseBaseDataDO = caseMap.get(caseId);
                if (caseBaseDataDO != null) {
                    CfKpiCaseScoreVO cfKpiCaseScoreVO = new CfKpiCaseScoreVO();
                    cfKpiCaseScoreVO.setCaseId(caseId);
                    cfKpiCaseScoreVO.setInfoUuid(caseBaseDataDO.getInfoUuid());
                    cfKpiCaseScoreVO.setTitle(caseBaseDataDO.getTitle());
                    cfKpiCaseScoreVO.setPatientName(caseBaseDataDO.getPatientName());
                    cfKpiCaseScoreVO.setCaseAmount(caseBaseDataDO.getCaseAmount());
                    cfKpiCaseScoreVO.setDonateNum(caseBaseDataDO.getDonateNum());
                    cfKpiCaseScoreVO.setCaseResult(item.getResult());
                    cfKpiCaseScoreVO.setIllegalCase((int) caseBaseDataDO.getIllegalCase());
                    GrowthtoolPepCaseScoreModel pepCaseScoreModel = caseScoreModelMap.get(caseId);
                    if (pepCaseScoreModel != null) {
                        cfKpiCaseScoreVO.setNextCaseAmountGap(pepCaseScoreModel.getNextCaseAmountGap());
                        cfKpiCaseScoreVO.setNextDonateNumGap(pepCaseScoreModel.getNextDonateNumGap());
                        cfKpiCaseScoreVO.setNeedHighlight(pepCaseScoreModel.isNeedHighlight());
                        cfKpiCaseScoreVO.setNextTarget(pepCaseScoreModel.getNextTarget());
                        cfKpiCaseScoreVO.setSymbol(pepCaseScoreModel.getSymbol());
                        cfKpiCaseScoreVO.setContextSymbol(pepCaseScoreModel.getContextSymbol());
                        cfKpiCaseScoreVO.setCalDate(pepCaseScoreModel.getCalDate());
                    }
                    detailModel.setCfKpiCaseScoreVO(cfKpiCaseScoreVO);
                }
            }
            //填充hc信息
            if (Objects.equals(filedTypeEnum.getCode(), FactCaseDetailModel.FiledTypeEnum.hc_detail.getCode())) {
                KpiHcModel kpiHcModel = kpiHcModelMap.get(item.getUniqueValue());
                hcModel = true;
                detailModel.setKpiHcModel(kpiHcModel);
            }
            //填充患者信息
            fillPatientDetail(item, detailModel, filedTypeEnum);
            result.add(detailModel);
        }
        if (hcModel) {
            result = result.stream()
                    .sorted((a, b) -> {
                        if (a.getKpiHcModel() == null || b.getKpiHcModel() == null) {
                            return 0;
                        }
                        return (int) (-a.getKpiHcModel().getValidHcFlag() + b.getKpiHcModel().getValidHcFlag());
                    })
                    .collect(Collectors.toList());
        }
        return result;
    }

    private static void fillPatientDetail(FactDetailModel item, FactCaseDetailModel detailModel, FactCaseDetailModel.FiledTypeEnum filedTypeEnum) {
        if (Objects.equals(filedTypeEnum.getCode(), FactCaseDetailModel.FiledTypeEnum.patientId.getCode()) &&
                StringUtils.isNumeric(item.getUniqueValue())) {
            long patientId = Long.parseLong(item.getUniqueValue());
            PatientScoreDetail patientScoreDetail = new PatientScoreDetail();
            patientScoreDetail.setPatientId(patientId);
            patientScoreDetail.setPatientName("");
            patientScoreDetail.setPatientResult(item.getResult());
            detailModel.setPatientScoreDetail(patientScoreDetail);
        }
    }


    //不展示同级人员的绩效
    public TeamKpiModel queryTeamKpi(long procedureId, List<Long> orgIds, boolean noShowSameLevel, CrowdfundingVolunteer volunteer) {
        TeamKpiModel result = new TeamKpiModel();
        //排除蜂鸟计划
        //List<Long> partnerOrgList = orgReadService.listAllPartnerOrg();
        //orgIds = orgIds.stream().filter(item -> !partnerOrgList.contains(item)).collect(Collectors.toList());
        List<UserCalAttachCaseResult> userCalResults = Lists.newArrayList();
        //拼接组织名称
        String orgName = orgReadService.getOrgInfoList(orgIds).stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.joining(","));
        //遍历下级组织
        for (Long orgId : orgIds) {
            TeamKpiModel teamKpiModel = singleTeamKpi(procedureId, orgId, noShowSameLevel);
            result.getSubOrgList().addAll(teamKpiModel.getSubOrgList());
            userCalResults.addAll(teamKpiModel.getUserCalResultList().stream()
                    .filter(item -> !item.getUserId().equals(volunteer.getUniqueCode()))
                    .collect(Collectors.toList()));
        }
        userCalResults = sortMemberResult(userCalResults);
        result.setUserCalResultList(userCalResults);
        result.setOrgName(orgName);
        return result;
    }


    public boolean teamGrey(String uniqueCode) {
        List<String> kpiTeamGreyCityList = apolloService.getKpiTeamGreyCityList();
        return memberService.isGreyCity(kpiTeamGreyCityList, uniqueCode);
    }

    @NotNull
    protected static List<UserCalAttachCaseResult> sortMemberResult(List<UserCalAttachCaseResult> userCalResults) {
        return userCalResults.stream()
                .sorted((a, b) -> {
                            BigDecimal commssionValue = new BigDecimal(Optional.ofNullable(a.getCommissionResult()).map(SchemeCalResult::getAmount).orElse("0"));
                            BigDecimal performanceValue = new BigDecimal(Optional.ofNullable(a.getPerformanceResult()).map(SchemeCalResult::getAmount).orElse("0"));
                            BigDecimal bCommissionValue = new BigDecimal(Optional.ofNullable(b.getCommissionResult()).map(SchemeCalResult::getAmount).orElse("0"));
                            BigDecimal bCperformanceValue = new BigDecimal(Optional.ofNullable(b.getPerformanceResult()).map(SchemeCalResult::getAmount).orElse("0"));
                            if (commssionValue.compareTo(bCommissionValue) == 0) {
                                return -1 * performanceValue.compareTo(bCperformanceValue);
                            }
                            return -1 * commssionValue.compareTo(bCommissionValue);
                        }
                )
                .collect(Collectors.toList());
    }


    public TeamKpiModel singleTeamKpi(long procedureId, long orgId, boolean noShowSameLevel) {
        TeamKpiModel result = new TeamKpiModel();
        //查找人员
        List<UserCalAttachCaseResult> calAttachCaseResultList = Lists.newArrayList();
        if (!noShowSameLevel) {
            List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = relationService.listRelationByOrgId(orgId);
            List<String> userIds = bdCrmOrgUserRelationDOS.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).collect(Collectors.toList());
            List<UserCalResult> calResultList = pepClientDelegate.teamUserResult(userIds, procedureId);
            boolean containsNormal = bdCrmOrgUserRelationDOS.stream()
                    .anyMatch(item -> Objects.equals(item.getUserRole(), OrganizationUserEnums.UserRoleEnum.normal.getCode()));
            //查看团队中的人员的有效案例和捐单
            Map<String, GrowthtoolUserCaseScoreModel> userCaseScoreModelMap;
            Map<String, String> userLabelMap;
            if (containsNormal) {
                PepTeamParam pepTeamParam = new PepTeamParam();
                pepTeamParam.setProcedureId(procedureId);
                pepTeamParam.setUserIds(userIds);
                List<GrowthtoolUserCaseScoreModel> growthtoolUserCaseScoreModels = pepClientDelegate.teamUserScore(pepTeamParam);
                userCaseScoreModelMap = growthtoolUserCaseScoreModels.stream()
                        .collect(Collectors.toMap(GrowthtoolUserCaseScoreModel::getUserId, Function.identity(), (before, after) -> before));
                userLabelMap = cfVolunteerService.getCfVolunteerDOByUniqueCodes(userIds)
                        .stream()
                        .filter(item -> Objects.equals(item.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AID.getLevel()))
                        .collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, item -> CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AID.getDesc(), (before, after) -> before));
            } else {
                userCaseScoreModelMap = Maps.newHashMap();
                userLabelMap = Maps.newHashMap();
            }

            for (String userId : userIds) {
                UserCalResult userCalResult = calResultList.stream()
                        .filter(item -> item.getUserId().equals(userId))
                        .findFirst()
                        .orElse(null);
                //如果当前人员没有绩效需要初始化
                if (userCalResult == null) {
                    userCalResult = new UserCalResult();
                    userCalResult.setUserId(userId);
                    userCalResult.setUserName(bdCrmOrgUserRelationDOS.stream()
                            .filter(item -> item.getUniqueCode().equals(userId))
                            .map(BdCrmOrgUserRelationDO::getMisName)
                            .findFirst()
                            .orElse(""));
                    calResultList.add(userCalResult);
                }
            }
            calAttachCaseResultList = calResultList.stream()
                    .map(item -> {
                        UserCalAttachCaseResult userCalAttachCaseResult = UserCalResultConvert.INSTANCE.toModel(item);
                        GrowthtoolUserCaseScoreModel userCaseScoreModel = userCaseScoreModelMap.get(item.getUserId());
                        String label = userLabelMap.get(item.getUserId());
                        if (userCaseScoreModel != null) {
                            userCalAttachCaseResult.setCaseTotal(userCaseScoreModel.getCaseTotal());
                            userCalAttachCaseResult.setDonateNum(userCaseScoreModel.getDonateNum());
                            userCalAttachCaseResult.setRatio(userCaseScoreModel.getRatio());
                        }
                        userCalAttachCaseResult.setLabel(label);
                        return userCalAttachCaseResult;
                    }).collect(Collectors.toList());
        }
        //查找直接下级组织
        List<BdCrmOrganizationDO> directSubOrgs = orgReadService.findDirectSubOrgByOrgId(orgId);
        List<TeamKpiOrgModel> kpiOrgModels = directSubOrgs.stream()
                .map(item -> {
                    TeamKpiOrgModel teamKpiOrgModel = new TeamKpiOrgModel();
                    teamKpiOrgModel.setId(item.getId());
                    teamKpiOrgModel.setOrgName(item.getOrgName());
                    teamKpiOrgModel.setParentId(item.getParentId());
                    teamKpiOrgModel.setOrgAttribute(item.getOrgAttribute());
                    teamKpiOrgModel.setCityId(item.getCityId());
                    teamKpiOrgModel.setCityName(item.getCityName());
                    //下级组织上有多少人员
                    long count = memberService.listAllSubMemberIncludeSelf((int) item.getId())
                            .stream()
                            .map(BdCrmOrgUserRelationDO::getUniqueCode)
                            .distinct()
                            .count();
                    teamKpiOrgModel.setTotalStaff(count);
                    return teamKpiOrgModel;
                }).collect(Collectors.toList());
        result.setUserCalResultList(calAttachCaseResultList);
        result.setSubOrgList(kpiOrgModels);
        return result;
    }

}

