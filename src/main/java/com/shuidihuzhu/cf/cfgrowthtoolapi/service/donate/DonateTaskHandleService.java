package com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.BdCaseDonateTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.BdCaseTagService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2022-06-29 11:51
 **/
@Slf4j
@Service
public class DonateTaskHandleService {

    @Autowired
    private IBdCaseDonateTaskService bdCaseDonateTaskService;

    @Autowired
    private BdCaseTagService bdCaseTagService;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegateImpl;


    public void handleStopCase(int caseId) {
        BdCaseDonateTaskDO caseDonateTaskDO = bdCaseDonateTaskService.getByCaseIdAndDayKey(caseId, DateTime.now().toString(GrowthtoolUtil.ymdfmt));
        if (caseDonateTaskDO != null) {
            bdCaseDonateTaskService.updateHandleStatus(Lists.newArrayList(caseDonateTaskDO.getId()), CaseDonateEnums.DonateTaskStatusEnum.stop_operate.getCode(), CaseDonateEnums.DonateHandleStatusEnum.case_finish.getCode());
            bdCaseTagService.updateStopOperate(caseId, 1);
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(caseId),
                    OperateTypeEnum.DONATE_STOP_CASE.getDesc(),
                    OperateTypeEnum.DONATE_STOP_CASE,
                    String.valueOf(caseDonateTaskDO.getId()),
                    0L,
                    "系统"));
        }
    }

    //任务超时 过期未完成
    public void handleOverTime() {
        //找到前一天的任务
        String yesterday = DateTime.now().minusDays(1).toString(GrowthtoolUtil.ymdfmt);
        List<BdCaseDonateTaskDO> donateTaskList = bdCaseDonateTaskService.listByDayKey(yesterday)
                .stream()
                .filter(item -> Objects.equals(item.getHandleStatus(), CaseDonateEnums.DonateTaskStatusEnum.init.getCode()))
                .collect(Collectors.toList());
        List<Long> taskIds =donateTaskList
                .stream()
                .map(BdCaseDonateTaskDO::getId)
                .collect(Collectors.toList());
        log.info("前天未完成的任务个数:{}", taskIds.size());
        bdCaseDonateTaskService.updateHandleStatus(taskIds, CaseDonateEnums.DonateTaskStatusEnum.expired.getCode(), CaseDonateEnums.DonateHandleStatusEnum.un_reach.getCode());
        for (BdCaseDonateTaskDO caseDonateTaskDO : donateTaskList) {
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(caseDonateTaskDO.getCaseId()),
                    OperateTypeEnum.DONATE_UNCOMPLETE.getDesc(),
                    OperateTypeEnum.DONATE_UNCOMPLETE,
                    String.valueOf(caseDonateTaskDO.getId()),
                    0L,
                    "系统"));
        }
    }

    //任务完成
    public void handleWhenShare(int caseId) {
        BdCaseDonateTaskDO caseDonateTaskDO = bdCaseDonateTaskService.getByCaseIdAndDayKey(caseId, DateTime.now().toString(GrowthtoolUtil.ymdfmt));
        if (caseDonateTaskDO == null) {
            return;
        }
        bdCaseDonateTaskService.addShareCount(caseDonateTaskDO.getId());
        int taskStatus = caseDonateTaskDO.getTaskStatus();
        if (!Objects.equals(CaseDonateEnums.DonateTaskStatusEnum.init.getCode(), taskStatus)) {
            return;
        }
        if (caseDonateTaskDO.getShareCount() + 1 >= caseDonateTaskDO.getShareTarget()) {
            bdCaseDonateTaskService.updateHandleStatus(Lists.newArrayList(caseDonateTaskDO.getId()),
                    CaseDonateEnums.DonateTaskStatusEnum.task_finish.getCode(), CaseDonateEnums.DonateHandleStatusEnum.reach.getCode());

            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(caseDonateTaskDO.getCaseId()),
                    OperateTypeEnum.DONATE_COMPLETE.getDesc(),
                    OperateTypeEnum.DONATE_COMPLETE,
                    String.valueOf(caseDonateTaskDO.getId()),
                    0L,
                    caseDonateTaskDO.getVolunteerName()));
        }
    }


    //跟进任务失败
    public Response<Void> handleTaskFail(String infoUuid, String taskFailReason, CrowdfundingVolunteer cfVolunteer) {
        CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegateImpl.getCrowdfundingInfoByInfouuid(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeFail("案例不存在");
        }
        List<BdCaseDonateTaskDO> donateTaskDOS = bdCaseDonateTaskService.listByCaseId(crowdfundingInfo.getId());
        BdCaseDonateTaskDO caseDonateTaskDO = donateTaskDOS.stream()
                .filter(item -> item.getTaskStatus() == CaseDonateEnums.DonateTaskStatusEnum.init.getCode())
                .findFirst()
                .orElse(null);
        if (caseDonateTaskDO == null) {
            return NewResponseUtil.makeFail("没有需运营的任务");
        }
        if (!Objects.equals(caseDonateTaskDO.getUniqueCode(), cfVolunteer.getUniqueCode())) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.NO_ALLOW_OPERATE);
        }

        bdCaseDonateTaskService.updateHandleStatusWithReason(Lists.newArrayList(caseDonateTaskDO.getId()),
                CaseDonateEnums.DonateTaskStatusEnum.stop_operate.getCode(),
                CaseDonateEnums.DonateHandleStatusEnum.fail.getCode(), taskFailReason);

        bdCaseTagService.updateStopOperate(caseDonateTaskDO.getCaseId(), 1);
        customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(caseDonateTaskDO.getCaseId()),
                OperateTypeEnum.DONATE_TASK_FAIL.getDesc(),
                OperateTypeEnum.DONATE_TASK_FAIL,
                String.valueOf(caseDonateTaskDO.getId()),
                0L,
                cfVolunteer.getVolunteerName()));

        return NewResponseUtil.makeSuccess(null);
    }



}
