package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfToufangRegisterDayService;
import com.shuidihuzhu.cf.dao.CfToufangRegisterDayDao;
import com.shuidihuzhu.cf.model.toufang.CfToufangRegister;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/9/5 下午2:38
 * @desc
 */
@Service
public class CfToufangRegisterDayServiceImpl implements ICfToufangRegisterDayService {

    @Autowired
    private CfToufangRegisterDayDao cfTouFangRegisterDayDao;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public List<CfToufangRegister> getBetween(long startId, long endId, long anchorId, int limit) {

        List<CfToufangRegister> toufangRegisters = cfTouFangRegisterDayDao.getBetween(startId, endId, anchorId, limit);

        List<CfToufangRegister> registers = Lists.newArrayList();
        for (CfToufangRegister cfToufangRegister : toufangRegisters){
            cfToufangRegister.setMobile(shuidiCipher.decrypt(cfToufangRegister.getCryptoMobile()));
            registers.add(cfToufangRegister);
        }

        return registers;
    }

}
