package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICommonJudgment;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ILeftPageRedirect;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-09-01
 */
@Slf4j
@Service
@RefreshScope
public class LeftPageRedirectImpl implements ILeftPageRedirect {

    @Autowired
    private ICommonJudgment commonJudgmentImpl;

    @Override
    public OpResult<Integer> query(long userId) {
        //未关注水滴筹公众号 不进入实验
        if (!commonJudgmentImpl.checkSubscribeByUserIdAndThridType(userId, GeneralConstant.FUNDRAISER_THIRD_TYPE)){
            return OpResult.createSucResult(1);
        }
        // 线下扫码关注的用户 不进入实验
        if (commonJudgmentImpl.checkIsExceptSubscribe(userId)){
            return OpResult.createSucResult(1);
        }
        // 有在筹案例的用户 不进入实验
        if (commonJudgmentImpl.checkIsHaveNoEndCase(userId)){
            return OpResult.createSucResult(1);
        }
        // 有前置草稿的用户 不进入实验
        if (commonJudgmentImpl.checkIsHaveDraft(userId)){
            return OpResult.createSucResult(1);
        }
        //填写任何一项草稿信息 不进入实验
        /*if (commonJudgmentImpl.checkIsFilled(userId,
                DateUtil.formatDateTime(DateUtil.addMinute(DateUtil.nowDate(),-30)),
                DateUtil.formatDateTime(DateUtil.nowDate())))
        {
            return OpResult.createSucResult(1);
        }*/
        //用户24小时内在微信内登记过 不进入实验
        if (commonJudgmentImpl.checkIsWithin24HoursRepetition(userId)){
            return OpResult.createSucResult(1);
        }
        return OpResult.createSucResult(2);
    }
}
