package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PartnerTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DateQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.PartnerServiceSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerCaseRelationDo;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2021-09-09 10:49 上午
 **/
@Slf4j
@Service
public class CfLovePartnerService {

    @Autowired
    private ICfLovePartnerService clewRecordService;

    @Autowired
    private CfPartnerCaseRelationService caseRelationService;

    @Autowired
    private PartnerAttendInfoService partnerAttendInfoService;

    @Autowired
    private CfPartnerInfoService cfPartnerInfoService;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;


    public CommonPageModel<PartnerServiceInfo> serviceData(PartnerServiceSearchParam serviceSearchParam) {
        CommonPageModel<PartnerServiceInfo> commonPageModel = new CommonPageModel<PartnerServiceInfo>();
        PartnerTypeEnum.ServiceSearchEnum serviceSearchEnum = PartnerTypeEnum.ServiceSearchEnum.parseByCode(serviceSearchParam.getSearchTab());
        if (serviceSearchEnum == null) {
            return commonPageModel;
        }
        return clewRecordService.serviceData(serviceSearchParam);
    }


    public List<PartnerAwardInfo> awardList(String partnerUniqueCode) {
        List<PartnerAwardInfo> result = Lists.newArrayList();
        //找到当期的案例 + 宣传信息
        PartnerAwardInfo partnerAwardInfo = unPaySalary(partnerUniqueCode);
        result.add(partnerAwardInfo);
        return result;
    }


    private PartnerAwardInfo unPaySalary(String partnerUniqueCode) {
        DateTime now = DateTime.now();
        int dayOfWeek = now.getDayOfWeek();
        DateTime startDate = dayOfWeek <= 15 ? now.withTimeAtStartOfDay().withDayOfMonth(1) :
                now.withTimeAtStartOfDay().withDayOfMonth(16);
        DateQueryParam dateQueryParam = new DateQueryParam();
        dateQueryParam.setStartTime(startDate.toString(GrowthtoolUtil.ymdhmsfmt));
        dateQueryParam.setEndTime(now.toString(GrowthtoolUtil.ymdhmsfmt));
        //查询人员的配置
        CfPartnerInfoDo partnerInfoDo = cfPartnerInfoService.getPartnerInfoByUniqueCode(partnerUniqueCode);
        int baseCaseSalary = Optional.ofNullable(partnerInfoDo.getBaseCaseSalary()).orElse(0);
        int baseAttendSalary = Optional.ofNullable(partnerInfoDo.getBaseAttendSalary()).orElse(0);

        List<CfPartnerCaseRelationDo> cfPartnerCaseRelationDos = caseRelationService.listByPartnerUniqueCode(partnerUniqueCode, dateQueryParam);
        List<Integer> caseIds = cfPartnerCaseRelationDos.stream()
                .map(CfPartnerCaseRelationDo::getCaseId)
                .filter(item -> item > 0)
                .collect(Collectors.toList());
        int caseSalary = 0;
        int caseCount = 0;
        if (CollectionUtils.isNotEmpty(caseIds)) {
            caseCount = (int) cfBdCaseInfoService.listCaseInfoByCaseIds(caseIds)
                    .stream()
                    .filter(item -> item.getAmount() > GrowthtoolUtil.validCaseAmount)
                    .count();

            caseSalary = caseCount * baseCaseSalary;
        }

        List<PartnerAttendInfoDTO> partnerAttendInfoDTOS = partnerAttendInfoService.listByPartnerUniqueCode(partnerUniqueCode, dateQueryParam);
        int attendSalary = partnerAttendInfoDTOS.size() * baseAttendSalary;

        PartnerAwardInfo partnerAwardInfo = new PartnerAwardInfo();
        partnerAwardInfo.setName("未结算服务奖励");
        partnerAwardInfo.setAwardId(0);
        partnerAwardInfo.setValidCaseCount(caseCount);
        partnerAwardInfo.setCaseSalary(caseSalary);
        partnerAwardInfo.setAttendCount(partnerAttendInfoDTOS.size());
        partnerAwardInfo.setAttendSalary(attendSalary);
        partnerAwardInfo.setTotalSalary(caseSalary + attendSalary);
        partnerAwardInfo.setStatus(PartnerTypeEnum.BillPayStatusEnum.un_pay.getCode());
        return partnerAwardInfo;
    }
}
