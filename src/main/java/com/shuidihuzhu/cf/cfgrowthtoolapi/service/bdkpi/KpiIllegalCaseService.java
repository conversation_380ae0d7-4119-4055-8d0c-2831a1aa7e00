package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.KpiIllegalCaseDO;

import java.util.Date;
import java.util.List;

/**
 * 不合规案例(KpiIllegalCase)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-20 17:23:27
 */
public interface KpiIllegalCaseService {

    KpiIllegalCaseDO queryById(long id);

    int insert(KpiIllegalCaseDO kpiIllegalCase);

    void batchInsert(List<KpiIllegalCaseDO> kpiIllegalCaseList);

    List<KpiIllegalCaseDO> listByMonthKey(String monthKey);

    int countAll(Date beginTime);

    List<KpiIllegalCaseDO> pageList(Date beginTime, int offset, int limit);

}
