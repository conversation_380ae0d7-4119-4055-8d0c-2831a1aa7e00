package com.shuidihuzhu.cf.cfgrowthtoolapi.service.prepose;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IPreposeMaterialDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalBuildingDepartmentDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalBuildingDepartmentService;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.dao.bdcrm.ClewCrowdfundingReportRelationDao;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2023-02-09 11:14
 **/
@Slf4j
@Service
public class BdCaseInfoFillService {


    @Autowired
    private IPreposeMaterialDelegate preposeMaterialDelegate;

    @Autowired
    private HospitalBuildingDepartmentService departmentService;

    @Autowired
    private ClewCrowdfundingReportRelationDao clewCrowdfundingReportRelationDao;

    public void fillBdInfoByMaterial(CfBdCaseInfoDo bdCaseInfoDo) {
        ClewCrowdfundingReportRelation relation = clewCrowdfundingReportRelationDao.getLatelyReportedRelationByInfoId(bdCaseInfoDo.getCaseId());
        if (relation != null && relation.getPreposeMaterialId() != null) {
            this.fillBdInfoByMaterial(bdCaseInfoDo, relation.getPreposeMaterialId());
        } else {
            log.warn("案例:{}无ClewCrowdfundingReportRelation信息", bdCaseInfoDo.getCaseId());
        }
    }


    public void fillBdInfoByMaterial(CfBdCaseInfoDo bdCaseInfoDo, long preposeMaterialId) {
        RpcResult<PreposeMaterialModel.MaterialInfoVo> materialInfoVoRpcResult = preposeMaterialDelegate.selectMaterialsById(preposeMaterialId);
        if (materialInfoVoRpcResult.isSuccess() && materialInfoVoRpcResult.getData() != null) {
            String hospitalName = materialInfoVoRpcResult.getData().getHospital();
            String departmentName = materialInfoVoRpcResult.getData().getDepartment();
            String diseaseName = materialInfoVoRpcResult.getData().getDiseaseName();
            String cityName = materialInfoVoRpcResult.getData().getRaiseCityName();
            String vhospitalCode = materialInfoVoRpcResult.getData().getVhospitalCode();
            Integer departmentId = materialInfoVoRpcResult.getData().getDepartmentId();
            bdCaseInfoDo.setHospitalName(hospitalName);
            bdCaseInfoDo.setDepartmentName(departmentName);
            bdCaseInfoDo.setDiseaseName(diseaseName);
            bdCaseInfoDo.setCityName(cityName);
            bdCaseInfoDo.setVhospitalCode(vhospitalCode);
            if (departmentId != null && departmentId > 0) {
                bdCaseInfoDo.setDepartmentId(departmentId);
            } else {
                //根据vhospitalCode + departmentName找到对应的科室
                HospitalBuildingDepartmentDO departmentDO = departmentService.queryByHospitalCodeAndName(vhospitalCode, departmentName);
                if (departmentDO != null) {
                    bdCaseInfoDo.setDepartmentId(departmentDO.getId());
                }
            }
        }
    }


}
