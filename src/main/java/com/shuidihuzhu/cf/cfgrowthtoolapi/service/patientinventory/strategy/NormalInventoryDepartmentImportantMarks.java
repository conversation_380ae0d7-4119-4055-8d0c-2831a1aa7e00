package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory.strategy;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.*;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/20  17:23
 */
@Service
public class NormalInventoryDepartmentImportantMarks extends AbstractImportantMarksStrategy {
    @Override
    public boolean support(int departmentType) {
        return departmentType == PatientInventoryDepartmentTypeEnum.NORMAL_INVENTORY_DEPARTMENT.getCode();
    }

    /**
     * - 今日需拜访-未沟通上：录入日期的第二天、第三天
     * - 今日需拜访-非未沟通上（有发起可能/病情方案没确定/其他）：有下次跟进时间的当天和第二天
     * - 今日需拜访-有发起可能/病情方案没确定：无下次跟进时间，录入日期的第二天、第三天
     */
    @Override
    public boolean getNeedToFollowUpTodayLabel(PatientInventoryPatientFollowUpRecordDo patientFollowUpRecordDo,
                                               String dateline,
                                               int volunteerLevel) {
        boolean result = false;
        String now = DateUtil.getCurrentDateStr();
        //非首次跟进
        if (patientFollowUpRecordDo.getType() != PatientInventoryPatientFollowUpRecordDo.Type.FIRST_FOLLOW_UP.getCode()) {
            if (!isCommon(volunteerLevel) && !isManagement(volunteerLevel)) {
                return result;
            }
            if (patientFollowUpRecordDo.getFirstIntention() == PatientInventoryFirstIntentionType.NEED_FOLLOW_UP.getCode()) {
                //  3. 非首次意向（最新）：选择【需跟进】的，“下次跟进时间”的当日和第二日橙色提醒（重新住院的第一次跟进也是首次意向）
                String nowDayStr = chooseDay(patientFollowUpRecordDo.getNextFollowUpTime(), 0);
                String twoDayStr = chooseDay(patientFollowUpRecordDo.getNextFollowUpTime(), 1);
                if (now.equals(nowDayStr) || now.equals(twoDayStr)) {
                    result = true;
                }
            }
            return result;
        }
        //首次跟进（重新住院的第一次跟进也是首次跟进，换床不算首次跟进）
        //非需拜访，无需处理
        if (patientFollowUpRecordDo.getFirstIntention() != PatientInventoryFirstIntentionType.NEED_FOLLOW_UP.getCode()) {
            return result;
        }
        //没沟通上
        if (patientFollowUpRecordDo.getSecondIntention() == PatientInventoryFirstIntentionType.PatientInventorySecondIntentionType.NOT_CONTACTED.getCode()) {
            //    1. 选择需跟进--未沟通上，录入日期的第二天、第三天橙色提醒
            if (isCommon(volunteerLevel)) {
                //顾问录入日期的第二天、第三天橙色提醒
                String twoDayStr = chooseDay(dateline, 1);
                String threeDayStr = chooseDay(dateline, 2);
                if (now.equals(twoDayStr) || now.equals(threeDayStr)) {
                    result = true;
                }
            } else if (isManagement(volunteerLevel)) {
                //业务经理/分区经理/大区经理录入日期的第三天、第四天橙色提醒
                String threeDayStr = chooseDay(dateline, 2);
                String fourDayStr = chooseDay(dateline, 3);
                if (now.equals(threeDayStr) || now.equals(fourDayStr)) {
                    result = true;
                }
            }
        } else {
            if (isCommon(volunteerLevel)) {
                if (patientFollowUpRecordDo.getNextFollowUpTime() == null) {
                    //其他，无需处理
                    if (patientFollowUpRecordDo.getSecondIntention() == PatientInventoryFirstIntentionType.PatientInventorySecondIntentionType.NEED_FOLLOW_UP_OTHER.getCode()) {
                        return result;
                    }
                    //今日需拜访-有发起可能/病情方案没确定：无下次跟进时间，录入日期的第二天、第三天
                    String twoDayStr = chooseDay(dateline, 1);
                    String threeDayStr = chooseDay(dateline, 2);
                    if (now.equals(twoDayStr) || now.equals(threeDayStr)) {
                        result = true;
                    }
                } else {
                    //  2. 需跟进-非“未沟通上”的，“下次跟进时间”的当日和第二日橙色提醒
                    String nowDayStr = chooseDay(patientFollowUpRecordDo.getNextFollowUpTime(), 0);
                    String twoDayStr = chooseDay(patientFollowUpRecordDo.getNextFollowUpTime(), 1);
                    if (now.equals(nowDayStr) || now.equals(twoDayStr)) {
                        result = true;
                    }
                }
            }
            if (isManagement(volunteerLevel)) {
                //非有发起可能，无需处理
                if (patientFollowUpRecordDo.getSecondIntention() != PatientInventoryFirstIntentionType.PatientInventorySecondIntentionType.POSSIBLE_INITIATION.getCode()) {
                    return result;
                }
                if (patientFollowUpRecordDo.getNextFollowUpTime() == null) {
                    // - 今日需拜访-有发起可能：无下次跟进时间的数据，录入日期的第二天、第三天
                    String twoDayStr = chooseDay(dateline, 1);
                    String threeDayStr = chooseDay(dateline, 2);
                    if (now.equals(twoDayStr) || now.equals(threeDayStr)) {
                        result = true;
                    }
                } else {
                    // - 今日需拜访-有发起可能：有下次跟进时间的数据，下次跟进日期的当天和第二天
                    String nowDayStr = chooseDay(patientFollowUpRecordDo.getNextFollowUpTime(), 0);
                    String twoDayStr = chooseDay(patientFollowUpRecordDo.getNextFollowUpTime(), 1);
                    if (now.equals(nowDayStr) || now.equals(twoDayStr)) {
                        result = true;
                    }
                }
            }
        }
        return result;
    }

    /**
     * //只要符合首次录入时意向是【无意向】，且符合对应标签，则录入当天、第二天、第三天推送
     */
    @Override
    public boolean getPayCloseAttentionToLabel(List<PatientInventoryPatientFollowUpRecordDo> patientFollowUpRecordDoList,
                                               PatientInventoryPatientInfoDo patientInfoDo,
                                               String dateline,
                                               int volunteerLevel) {
        boolean result = false;
        //管理层已经盘点记录
        Optional<PatientInventoryPatientFollowUpRecordDo> optional = patientFollowUpRecordDoList.stream().filter(v -> StringUtils.isNotEmpty(v.getInventoryRecord())).findFirst();
        if (optional.isPresent()) {
            return result;
        }
        //没有权限
        if (!isManagement(volunteerLevel)) {
            return result;
        }
        //首次录入时意向是【无意向】
        boolean b = patientFollowUpRecordDoList.stream().anyMatch(v ->
                v.getType() == PatientInventoryPatientFollowUpRecordDo.Type.FIRST_FOLLOW_UP.getCode() && v.getFirstIntention() == PatientInventoryFirstIntentionType.NO_INTENTION.getCode());
        if (!b) {
            return result;
        }
        String twoDayStr = chooseDay(dateline, 1);
        String threeDayStr = chooseDay(dateline, 2);
        String now = DateUtil.getCurrentDateStr();
        //录入当天、第二天、第三天
        if (now.equals(dateline) || now.equals(twoDayStr) || now.equals(threeDayStr)) {
            List<DepartmentsLabelVo> labelList = getDepartmentsLabelVoList(patientInfoDo.getLabels());
            Set<Integer> codeSet = labelList.stream().map(DepartmentsLabelVo::getDepartmentsLabel).collect(Collectors.toSet());
            if (codeSet.contains(DepartmentsLabelEnum.special_identity.getCode())) {
                result = true;
            }
            if (codeSet.contains(DepartmentsLabelEnum.transplant_in_warehouse.getCode()) &&
                    codeSet.contains(DepartmentsLabelEnum.added_wechat.getCode())) {
                result = true;
            }
            if (codeSet.contains(DepartmentsLabelEnum.first_hospitalization.getCode()) &&
                    codeSet.contains(DepartmentsLabelEnum.poor_family_situation.getCode()) &&
                    codeSet.contains(DepartmentsLabelEnum.added_wechat.getCode())) {
                result = true;
            }
            if (codeSet.contains(DepartmentsLabelEnum.not_operated.getCode()) &&
                    codeSet.contains(DepartmentsLabelEnum.to_spend_more_than_5w.getCode()) &&
                    codeSet.contains(DepartmentsLabelEnum.poor_family_situation.getCode())) {
                result = true;
            }
            if (codeSet.contains(DepartmentsLabelEnum.postoperative_continued_treatment.getCode()) &&
                    codeSet.contains(DepartmentsLabelEnum.to_spend_more_than_5w.getCode()) &&
                    codeSet.contains(DepartmentsLabelEnum.added_wechat.getCode())) {
                result = true;
            }
        }
        return result;
    }

    private List<DepartmentsLabelVo> getDepartmentsLabelVoList(String labels) {
        if (StringUtils.isEmpty(labels)) {
            return Lists.newArrayList();
        }
        List<String> labelList = Splitter.on(",").splitToList(labels);
        List<DepartmentsLabelVo> result = Lists.newArrayList();
        for (String label : labelList) {
            DepartmentsLabelEnum departmentsLabelEnum = DepartmentsLabelEnum.getByCode(Integer.parseInt(label));
            if (departmentsLabelEnum == null) {
                continue;
            }
            DepartmentsLabelVo departmentsLabelVo = new DepartmentsLabelVo();
            departmentsLabelVo.setDepartmentsLabel(departmentsLabelEnum.getCode());
            departmentsLabelVo.setDepartmentsLabelDesc(departmentsLabelEnum.getDesc());
            result.add(departmentsLabelVo);
        }
        return result;
    }

}
