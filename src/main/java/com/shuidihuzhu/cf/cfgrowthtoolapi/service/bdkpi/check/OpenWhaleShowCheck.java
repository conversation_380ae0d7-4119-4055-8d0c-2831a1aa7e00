package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.check;

import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfBdKpiEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.bdkpi.ICfBdCrmWhaleKpiFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.bdkpi.mock.CfKpiBdScoreTempMockService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.bdkpi.mock.CfKpiScoreMockService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCrmPermissionService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.comparators.FixedOrderComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2021-09-26 4:36 下午
 **/
@Slf4j
@Service
public class OpenWhaleShowCheck {

    @Autowired
    private ICfBdCrmWhaleKpiFacade cfBdCrmWhaleKpiFacade;

    @Autowired
    private ICrmSelfBuiltOrgReadService selfBuiltOrgReadService;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoServiceImpl;

    @Autowired
    private ICrmOrganizationRelationService relationService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Resource(name = "cfKpiScoreMockService")
    private CfKpiScoreMockService cfKpiScoreMockService;

    @Resource
    private CfKpiBdScoreTempMockService cfKpiBdScoreTempMockService;

    @Autowired
    private ICfBdCrmPermissionService cfBdCrmPermissionService;

    public static final String alarmRobot = "0b1f5784-c442-4606-9d6b-2cdc2db36a24";



    //模拟打分流程
    //how
    //每个上级的流程都应该是 query-org -> update-score -> submit-score
    //可以看看哪些环节打不上去 - 打不上去,或者提交不了的直接报群里
    public void preMockScore(String monthKey) {
        List<BdCrmOrganizationDO> allOrg = selfBuiltOrgReadService.listNotTestOrg();
        //找到所有的人
        List<BdCrmOrgUserRelationDO> relationDOList = relationService
                .listByOrgIds(allOrg.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList()));
        List<String> uniqueCodes = relationDOList.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).collect(Collectors.toList());
        List<CrowdfundingVolunteer> volunteerList = cfVolunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodes);
        //业务经理
        FixedOrderComparator<Integer> ordering = new FixedOrderComparator<Integer>(CrowdfundingVolunteerEnum.needScoreRole);
        ordering.setUnknownObjectBehavior(FixedOrderComparator.UnknownObjectBehavior.AFTER);

        List<CrowdfundingVolunteer> provinceLeaders = volunteerList.stream()
                .filter(item -> CrowdfundingVolunteerEnum.needScoreRole.contains(item.getLevel()))
                .sorted(Ordering.from(ordering).onResultOf(CrowdfundingVolunteer::getLevel))
                .collect(Collectors.toList());
        for (CrowdfundingVolunteer leader : provinceLeaders) {
            //查找对应的组织
            List<BdCrmOrganizationDO> bdCrmOrganizationDOS = crmMemberInfoServiceImpl.listForCByUniqueCode(leader.getUniqueCode()).stream().sorted(Comparator.comparing(BdCrmOrganizationDO::depth)).collect(Collectors.toList());
            //查找权限
            List<String> dataPermissions = cfBdCrmPermissionService.getDataPermissionsByRole(leader.getLevel());
            OpResult<BdKpiAssessmentVO> assessmentOrgVO = cfBdCrmWhaleKpiFacade.queryAssessmentOrgMock(monthKey, bdCrmOrganizationDOS, leader, CfBdKpiEnums.WhaleOpenShowEnum.OPEN, false, cfKpiBdScoreTempMockService, cfKpiScoreMockService);
            log.info("人员:{},mis:{}queryAssessmentOrg:{}", leader.getVolunteerName(), leader.getMis(), assessmentOrgVO);
            BdKpiAssessmentVO data = assessmentOrgVO.getData();
            if (data == null || CollectionUtils.isEmpty(data.getOrganizationList())) {
                continue;
            }
            for (BdKpiOrganizationModel organizationModel : data.getOrganizationList()) {
                Boolean isHandle = organizationModel.getIsHandle();
                if (!isHandle) {
                    continue;
                }
                if (organizationModel.getHandleStatus() == 0) {
                    List<String> uniqueList = organizationModel.getWaitHandlerUniqueList();
                    for (String uniqueCode : uniqueList) {
                        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(uniqueCode);
                        //模拟打分
                        CfKPIBdScoreModel cfKPIBdScoreModel = new CfKPIBdScoreModel();
                        cfKPIBdScoreModel.setUniqueCode(uniqueCode);
                        cfKPIBdScoreModel.setMonthKey(monthKey);
                        CustomPerformanceScoreModel scoreModel = new CustomPerformanceScoreModel();
                        scoreModel.setCustomType(9);
                        scoreModel.setCustomName("上级评分");
                        scoreModel.setMaxValue(100);
                        scoreModel.setScoreBySea(0);
                        KPICustomRuleScoreModel kpiCustomRuleScoreModel = new KPICustomRuleScoreModel();
                        kpiCustomRuleScoreModel.setScore(120);
                        kpiCustomRuleScoreModel.setCustomType(9);
                        kpiCustomRuleScoreModel.setCustomContent("上级评分");
                        kpiCustomRuleScoreModel.setValue(100);
                        scoreModel.setKpiCustomRuleScoreModels(Lists.newArrayList(kpiCustomRuleScoreModel));
                        scoreModel.setCustomizeDesc("招募");

                        cfKPIBdScoreModel.setCustomPerformanceScoreModels(Lists.newArrayList(scoreModel));
                        cfKPIBdScoreModel.setAwardCollectModel(null);

                        OpResult<String> scoreMsg = cfBdCrmWhaleKpiFacade.updateScoreForMock(cfKPIBdScoreModel, leader, dataPermissions, cfKpiBdScoreTempMockService, cfKpiScoreMockService);
                        log.info("人员:{},mis:{}给下级:{}打分结果:{}", leader.getVolunteerName(), leader.getMis(), volunteer.getVolunteerName(), scoreMsg);
                        if (scoreMsg.isFail()) {
                            CrowdfundingVolunteer beScoreVolunteer = cfVolunteerService.getByUniqueCode(uniqueCode);
                            //警告
                            String content = "上级--" + leader.getVolunteerName() + "--给--" + beScoreVolunteer.getVolunteerName() + "--打不了分";
                            log.info("打分提示:{}", content);
                            AlarmBotService.sentText(alarmRobot, content, null, null);
                            return;
                        }
                    }
                }
            }
            OpResult<BdKpiAssessmentVO> hasScoreAssessment = cfBdCrmWhaleKpiFacade.queryAssessmentOrgMock(monthKey, bdCrmOrganizationDOS, leader, CfBdKpiEnums.WhaleOpenShowEnum.OPEN, false, cfKpiBdScoreTempMockService, cfKpiScoreMockService);
            if (hasScoreAssessment != null) {
                List<BdKpiOrganizationModel> organizationList = hasScoreAssessment.getData().getOrganizationList();
                for (BdKpiOrganizationModel organizationModel : organizationList) {
                    if (!organizationModel.getIsHandle() || organizationModel.getHandleStatus() == 1) {
                        //提交
                        log.info("人员:{}提交打分", leader.getVolunteerName());
                        OpResult<String> opResult = cfBdCrmWhaleKpiFacade.submitScoreForMock(monthKey, bdCrmOrganizationDOS, leader, cfKpiBdScoreTempMockService, cfKpiScoreMockService);
                        log.info("人员:{},mis:{}提交:{}", leader.getVolunteerName(), leader.getMis(), opResult);
                        if (opResult.isFail()) {
                            //警告
                            String content = "上级--" + leader.getVolunteerName() + "--提交不了";
                            log.info("提交提示:{}", content);
                            AlarmBotService.sentText(alarmRobot, content, null, null);
                            return;
                        }
                        break;
                    }
                }

            }
        }

    }


}
