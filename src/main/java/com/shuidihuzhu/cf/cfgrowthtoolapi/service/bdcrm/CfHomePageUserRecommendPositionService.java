package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfHomePageUserRecommendPositionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.PageSearchModel;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/6/2 下午6:00
 */
public interface CfHomePageUserRecommendPositionService{


    int insert(CfHomePageUserRecommendPositionDO record);

    CfHomePageUserRecommendPositionDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CfHomePageUserRecommendPositionDO record);

    long getListCountForSea(PageSearchModel pageSearchModel);

    List<CfHomePageUserRecommendPositionDO> getListForSea(PageSearchModel pageSearchModel);

    void upOrDown(Long id, int publishStatus);

    CfHomePageUserRecommendPositionDO getByUpStatus();
}
