package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.shuidihuzhu.cf.dao.CfCronTaskDOMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCronTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfCronTaskDOService;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/2/19 下午7:50
 */
@Service
public class CfCronTaskDOServiceImpl implements CfCronTaskDOService{

    @Resource
    private CfCronTaskDOMapper cfCronTaskDOMapper;

    @Override
    public int insertSelective(CfCronTaskDO record) {
        return cfCronTaskDOMapper.insertSelective(record);
    }

    @Override
    public CfCronTaskDO selectByPrimaryKey(Long id) {
        return cfCronTaskDOMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CfCronTaskDO record) {
        return cfCronTaskDOMapper.updateByPrimaryKeySelective(record);
    }
    @Override
    public int updateStatus(Long id, int status){
        return cfCronTaskDOMapper.updateStatus(id, status);
    }
    @Override
    public int updateRemarkContent(Long id, String remarkContent){
        return cfCronTaskDOMapper.updateRemarkContent(id, remarkContent);
    }

    @Override
    public long getSendTasksCount(Long sendKey, int bizType) {
        return cfCronTaskDOMapper.getSendTasksCount(sendKey, bizType);
    }

    @Override
    public List<CfCronTaskDO> getSendTasks(Long sendKey, int bizType, int pageNo, int pageSize) {
        int offset = (pageNo-1)*pageSize;
        return cfCronTaskDOMapper.getSendTasks(sendKey, bizType, offset, pageSize);
    }

}
