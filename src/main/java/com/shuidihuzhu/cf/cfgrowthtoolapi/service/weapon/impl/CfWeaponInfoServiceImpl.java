package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponApplyRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponAssignRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponBudgetDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponMoneyInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponMoneyParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-10-16 11:44 上午
 **/
@Slf4j
@Service
public class CfWeaponInfoServiceImpl implements ICfWeaponInfoService {

    @Autowired
    private ICfWeaponBudgetService budgetService;
    @Autowired
    private ICfWeaponAssignRecordService weaponAssignService;
    @Autowired
    private ICrmSelfBuiltOrgReadService orgReadService;
    @Autowired
    private ICfWeaponApplyRecordService applyRecordService;

    @Override
    public WeaponMoneyInfo getMoneyInfo(WeaponMoneyParam weaponMoneyParam) {
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = orgReadService.findDirectSubOrgByOrgId(weaponMoneyParam.getOrgId());
        if (CollectionUtils.isEmpty(bdCrmOrganizationDOList)) {
            return null;
        }

        CfWeaponBudgetDO cfWeaponBudgetDO = budgetService.getById(weaponMoneyParam.getBudgetId());

        boolean noLeaf = bdCrmOrganizationDOList.get(0).getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode();
        if (noLeaf) {
            List<Integer> orgIdList = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).map(Long::intValue).collect(Collectors.toList());
            List<CfWeaponBudgetDO> budgetUseResultVoList = budgetService.listByGroupIdAndOrgId(weaponMoneyParam.getBudgetGroupId(), orgIdList);
            budgetUseResultVoList = budgetUseResultVoList.stream().filter(CfWeaponBudgetDO::canUseByWhale).collect(Collectors.toList());
            double childTotalMoneySum = budgetUseResultVoList.stream().mapToDouble(CfWeaponBudgetDO::getChildTotalMoney).sum();
            double childUsedMoneySum = budgetUseResultVoList.stream().mapToDouble(CfWeaponBudgetDO::getChildUsedMoney).sum();
            double childApplyingMoneySum = budgetUseResultVoList.stream().mapToDouble(CfWeaponBudgetDO::getChildApplyingMoney).sum();

            double managementApplyingMoneySum = 0.0D;
            double managementUsedMoneySum = 0.0D;
            double managementUsedMoney = 0.0D;
            double managementApplyingMoney = 0.0D;
            if (cfWeaponBudgetDO != null) {
                managementApplyingMoneySum = cfWeaponBudgetDO.getManagementApplyingMoneySum();
                managementUsedMoneySum = cfWeaponBudgetDO.getManagementUsedMoneySum();
                managementUsedMoney = cfWeaponBudgetDO.getManagementUsedMoney();
                managementApplyingMoney = cfWeaponBudgetDO.getManagementApplyingMoney();
            }
            return WeaponMoneyInfo.builder()
                    .assignMoney(childTotalMoneySum + managementUsedMoney + managementApplyingMoney)
                    .unAssignMoney(weaponMoneyParam.getChildTotalMoney() - childTotalMoneySum - managementUsedMoney - managementApplyingMoney)
                    .useMoney(childUsedMoneySum + childApplyingMoneySum + managementApplyingMoneySum + managementUsedMoneySum)
                    .budgetId(weaponMoneyParam.getBudgetId())
                    .build();
        } else {
            List<CfWeaponAssignRecordDO> cfWeaponAssignRecordDOList = weaponAssignService.listCanAssignByBudgetId(weaponMoneyParam.getBudgetId());
            if (CollectionUtils.isEmpty(cfWeaponAssignRecordDOList)) {
                return null;
            }
            //查询已废弃预算中已使用的预算数据
            // 废弃预算中已使用的预算数据
            double invalidUsedMoney = 0.0D;
            List<CfWeaponAssignRecordDO> invalidAssignRecordDOList = weaponAssignService.listByBudgetIdAndAssignStatus(weaponMoneyParam.getBudgetId(),
                    WeaponEnums.BudgetAssignStatusEnum.cannot_assign_by_invalid.getCode());
            if (CollectionUtils.isNotEmpty(invalidAssignRecordDOList)) {
                //通过预算ID和uniqueCodes查询申请记录数据
                //管理层记录不计算
                Set<String> uniqueCodeSet = invalidAssignRecordDOList.stream().filter(item -> item.getAvailableAmount() > 0)
                        .map(CfWeaponAssignRecordDO::getUniqueCode).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(uniqueCodeSet)) {
                    List<CfWeaponApplyRecordDO> applyRecordDOList = applyRecordService.listByBudgetIdAndUniqueCodesAndApplyStatus(weaponMoneyParam.getBudgetId(),
                            uniqueCodeSet, WeaponEnums.middleAndSuccessApplyStatus);
                    invalidUsedMoney = applyRecordDOList.stream().mapToDouble(CfWeaponApplyRecordDO::getApplyMoney).sum();
                }
            }

            double sum = cfWeaponAssignRecordDOList.stream().mapToDouble(CfWeaponAssignRecordDO::getAvailableAmount).sum() + invalidUsedMoney;
            double managementUsedMoney = 0.0D;
            double managementApplyingMoney = 0.0D;
            if (cfWeaponBudgetDO != null) {
                managementUsedMoney = cfWeaponBudgetDO.getManagementUsedMoney();
                managementApplyingMoney = cfWeaponBudgetDO.getManagementApplyingMoney();
            }
            return WeaponMoneyInfo.builder()
                    .assignMoney(sum + managementUsedMoney + managementApplyingMoney)
                    .unAssignMoney(weaponMoneyParam.getChildTotalMoney() - sum - managementUsedMoney - managementApplyingMoney)
                    .budgetId(weaponMoneyParam.getBudgetId())
                    .build();
        }
    }

    @Override
    public List<WeaponMoneyInfo> listMoneyInfo(List<WeaponMoneyParam> weaponMoneyParamList) {
        List<Long> orgIdList = weaponMoneyParamList.stream().map(WeaponMoneyParam::getOrgId).map(Integer::longValue).collect(Collectors.toList());
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = orgReadService.findDirectSubOrgByOrgIdList(orgIdList);
        if (CollectionUtils.isEmpty(bdCrmOrganizationDOList)) {
            return null;
        }
        Map<Long, List<BdCrmOrganizationDO>> orgIdToOrgMap = bdCrmOrganizationDOList.stream().collect(Collectors.groupingBy(BdCrmOrganizationDO::getParentId));

        List<Integer> budgetGroupIds = weaponMoneyParamList.stream().map(WeaponMoneyParam::getBudgetGroupId).collect(Collectors.toList());
        List<Integer> budgetOrgList = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).distinct().map(Long::intValue).collect(Collectors.toList());
        List<CfWeaponBudgetDO> budgetUseResultVoList = budgetService.listByGroupIdsAndOrgId(budgetGroupIds, budgetOrgList);
        budgetUseResultVoList = budgetUseResultVoList.stream().filter(CfWeaponBudgetDO::canUseByWhale).collect(Collectors.toList());
        Map<Integer, List<CfWeaponBudgetDO>> budgetGroupIdToBudgetMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(budgetUseResultVoList)) {
            budgetGroupIdToBudgetMap = budgetUseResultVoList.stream().collect(Collectors.groupingBy(CfWeaponBudgetDO::getBudgetGroupId));
        }

        List<Integer> budgetIds = weaponMoneyParamList.stream().map(WeaponMoneyParam::getBudgetId).collect(Collectors.toList());
        Map<Integer, CfWeaponBudgetDO> budgetIdToBudgetMap = getBudgetDOMap(budgetIds);
        List<CfWeaponAssignRecordDO> cfWeaponAssignRecordDOList = weaponAssignService.listCanAssignByBudgetIds(budgetIds);
        Map<Integer, List<CfWeaponAssignRecordDO>> cfWeaponAssignRecordDOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(cfWeaponAssignRecordDOList)) {
            cfWeaponAssignRecordDOMap = cfWeaponAssignRecordDOList.stream().collect(Collectors.groupingBy(CfWeaponAssignRecordDO::getBudgetId));
        }

        List<WeaponMoneyInfo> result = Lists.newArrayList();
        for (WeaponMoneyParam weaponMoneyParam : weaponMoneyParamList) {
            List<BdCrmOrganizationDO> bdCrmOrganizationDOS = orgIdToOrgMap.get((long) weaponMoneyParam.getOrgId());
            if (CollectionUtils.isEmpty(bdCrmOrganizationDOS)) {
                continue;
            }
            boolean noLeaf = bdCrmOrganizationDOS.get(0).getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode();
            if (noLeaf) {
                List<CfWeaponBudgetDO> cfWeaponBudgetDOList = budgetGroupIdToBudgetMap.get(weaponMoneyParam.getBudgetGroupId());
                double childTotalMoneySum = 0.0D;
                double childUsedMoneySum = 0.0D;
                double childApplyingMoneySum = 0.0D;
                if (CollectionUtils.isNotEmpty(cfWeaponBudgetDOList)) {
                    Set<Integer> orgIdSet = bdCrmOrganizationDOList.stream().filter(v -> v.getParentId() == weaponMoneyParam.getOrgId()).map(BdCrmOrganizationDO::getId).map(Long::intValue).collect(Collectors.toSet());
                    cfWeaponBudgetDOList = cfWeaponBudgetDOList.stream().filter(v -> orgIdSet.contains(v.getOrgId())).collect(Collectors.toList());
                    childTotalMoneySum = cfWeaponBudgetDOList.stream().mapToDouble(CfWeaponBudgetDO::getChildTotalMoney).sum();
                    childUsedMoneySum = cfWeaponBudgetDOList.stream().mapToDouble(CfWeaponBudgetDO::getChildUsedMoney).sum();
                    childApplyingMoneySum = cfWeaponBudgetDOList.stream().mapToDouble(CfWeaponBudgetDO::getChildApplyingMoney).sum();
                }
                double managementApplyingMoneySum = 0.0D;
                double managementUsedMoneySum = 0.0D;
                double managementUsedMoney = 0.0D;
                double managementApplyingMoney = 0.0D;
                CfWeaponBudgetDO cfWeaponBudgetDO = budgetIdToBudgetMap.get(weaponMoneyParam.getBudgetId());
                if (cfWeaponBudgetDO != null) {
                    managementApplyingMoneySum = cfWeaponBudgetDO.getManagementApplyingMoneySum();
                    managementUsedMoneySum = cfWeaponBudgetDO.getManagementUsedMoneySum();
                    managementUsedMoney = cfWeaponBudgetDO.getManagementUsedMoney();
                    managementApplyingMoney = cfWeaponBudgetDO.getManagementApplyingMoney();
                }

                WeaponMoneyInfo weaponMoneyInfo = WeaponMoneyInfo.builder()
                        .assignMoney(childTotalMoneySum + managementUsedMoney + managementApplyingMoney)
                        .unAssignMoney(weaponMoneyParam.getChildTotalMoney() - childTotalMoneySum - managementUsedMoney - managementApplyingMoney)
                        .useMoney(childUsedMoneySum + childApplyingMoneySum + managementApplyingMoneySum + managementUsedMoneySum)
                        .budgetId(weaponMoneyParam.getBudgetId())
                        .build();
                result.add(weaponMoneyInfo);
            } else {
                List<CfWeaponAssignRecordDO> weaponAssignRecordDOList = cfWeaponAssignRecordDOMap.get(weaponMoneyParam.getBudgetId());
                double sum = 0.0D;
                if (CollectionUtils.isNotEmpty(weaponAssignRecordDOList)) {
                    sum = weaponAssignRecordDOList.stream().mapToDouble(CfWeaponAssignRecordDO::getAvailableAmount).sum();
                }
                double managementUsedMoney = 0.0D;
                double managementApplyingMoney = 0.0D;
                CfWeaponBudgetDO cfWeaponBudgetDO = budgetIdToBudgetMap.get(weaponMoneyParam.getBudgetId());
                if (cfWeaponBudgetDO != null) {
                    managementUsedMoney = cfWeaponBudgetDO.getManagementUsedMoney();
                    managementApplyingMoney = cfWeaponBudgetDO.getManagementApplyingMoney();
                }
                WeaponMoneyInfo weaponMoneyInfo = WeaponMoneyInfo.builder()
                        .assignMoney(sum + managementUsedMoney + managementApplyingMoney)
                        .unAssignMoney(weaponMoneyParam.getChildTotalMoney() - sum - managementUsedMoney - managementApplyingMoney)
                        .budgetId(weaponMoneyParam.getBudgetId())
                        .build();
                result.add(weaponMoneyInfo);
            }
        }
        return result;
    }

    @Override
    public Map<Integer, WeaponMoneyInfo> mapMoneyInfoByBudgetId(List<WeaponMoneyParam> weaponMoneyParamList) {
        List<WeaponMoneyInfo> listMoneyInfo = listMoneyInfo(weaponMoneyParamList);
        if (CollectionUtils.isEmpty(listMoneyInfo)) {
            return Maps.newHashMap();
        }
        return listMoneyInfo.stream().collect(Collectors.toMap(WeaponMoneyInfo::getBudgetId, weaponMoneyInfo -> weaponMoneyInfo));
    }

    @Override
    public WeaponMoneyInfo getNoLeafMoneyInfo(WeaponMoneyParam weaponMoneyParam) {
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = orgReadService.findDirectSubOrgByOrgId(weaponMoneyParam.getOrgId());
        if (CollectionUtils.isEmpty(bdCrmOrganizationDOList)) {
            return null;
        }

        CfWeaponBudgetDO cfWeaponBudgetDO = budgetService.getById(weaponMoneyParam.getBudgetId());

        List<Integer> orgIdList = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).map(Long::intValue).collect(Collectors.toList());
        List<CfWeaponBudgetDO> budgetUseResultVoList = budgetService.listByGroupIdAndOrgId(weaponMoneyParam.getBudgetGroupId(), orgIdList);
        budgetUseResultVoList = budgetUseResultVoList.stream().filter(CfWeaponBudgetDO::canUseByWhale).collect(Collectors.toList());
        double childTotalMoneySum = budgetUseResultVoList.stream().mapToDouble(CfWeaponBudgetDO::getChildTotalMoney).sum();
        double childUsedMoneySum = budgetUseResultVoList.stream().mapToDouble(CfWeaponBudgetDO::getChildUsedMoney).sum();
        double childApplyingMoneySum = budgetUseResultVoList.stream().mapToDouble(CfWeaponBudgetDO::getChildApplyingMoney).sum();

        double managementApplyingMoneySum = 0.0D;
        double managementUsedMoneySum = 0.0D;
        double managementApplyingMoney = 0.0D;
        double managementUsedMoney = 0.0D;
        if (cfWeaponBudgetDO != null) {
            managementApplyingMoneySum = cfWeaponBudgetDO.getManagementApplyingMoneySum();
            managementUsedMoneySum = cfWeaponBudgetDO.getManagementUsedMoneySum();
            managementApplyingMoney = cfWeaponBudgetDO.getManagementApplyingMoney();
            managementUsedMoney = cfWeaponBudgetDO.getManagementUsedMoney();
        }
        return WeaponMoneyInfo.builder()
                .assignMoney(childTotalMoneySum + managementApplyingMoney + managementUsedMoney)
                .useMoney(childUsedMoneySum + childApplyingMoneySum + managementApplyingMoneySum + managementUsedMoneySum)
                .budgetGroupId(weaponMoneyParam.getBudgetGroupId())
                .build();
    }

    @Override
    public List<WeaponMoneyInfo> listNoLeafMoneyInfo(List<WeaponMoneyParam> weaponMoneyParamList) {
        List<Long> orgIdList = weaponMoneyParamList.stream().map(WeaponMoneyParam::getOrgId).map(Integer::longValue).collect(Collectors.toList());
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = orgReadService.findDirectSubOrgByOrgIdList(orgIdList);
        if (CollectionUtils.isEmpty(bdCrmOrganizationDOList)) {
            return null;
        }

        List<Integer> budgetIdList = weaponMoneyParamList.stream().map(WeaponMoneyParam::getBudgetId).collect(Collectors.toList());
        Map<Integer, CfWeaponBudgetDO> budgetIdToBudgetMap = getBudgetDOMap(budgetIdList);
        List<Integer> budgetGroupIds = weaponMoneyParamList.stream().map(WeaponMoneyParam::getBudgetGroupId).collect(Collectors.toList());
        List<Integer> budgetOrgList = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).distinct().map(Long::intValue).collect(Collectors.toList());
        List<CfWeaponBudgetDO> budgetUseResultVoList = budgetService.listByGroupIdsAndOrgId(budgetGroupIds, budgetOrgList);
        budgetUseResultVoList = budgetUseResultVoList.stream().filter(CfWeaponBudgetDO::canUseByWhale).collect(Collectors.toList());
        Map<Integer, List<CfWeaponBudgetDO>> budgetGroupIdToBudgetMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(budgetUseResultVoList)) {
            budgetGroupIdToBudgetMap = budgetUseResultVoList.stream().collect(Collectors.groupingBy(CfWeaponBudgetDO::getBudgetGroupId));
        }

        List<WeaponMoneyInfo> result = Lists.newArrayList();
        for (WeaponMoneyParam weaponMoneyParam : weaponMoneyParamList) {
            List<CfWeaponBudgetDO> cfWeaponBudgetDOList = budgetGroupIdToBudgetMap.get(weaponMoneyParam.getBudgetGroupId());
            if (CollectionUtils.isEmpty(cfWeaponBudgetDOList)) {
                continue;
            }
            double childTotalMoneySum = cfWeaponBudgetDOList.stream().mapToDouble(CfWeaponBudgetDO::getChildTotalMoney).sum();
            double childUsedMoneySum = cfWeaponBudgetDOList.stream().mapToDouble(CfWeaponBudgetDO::getChildUsedMoney).sum();
            double childApplyingMoneySum = cfWeaponBudgetDOList.stream().mapToDouble(CfWeaponBudgetDO::getChildApplyingMoney).sum();

            double managementApplyingMoneySum = 0.0D;
            double managementUsedMoneySum = 0.0D;
            double managementUsedMoney = 0.0D;
            double managementApplyingMoney = 0.0D;
            CfWeaponBudgetDO cfWeaponBudgetDO = budgetIdToBudgetMap.get(weaponMoneyParam.getBudgetId());
            if (cfWeaponBudgetDO != null) {
                managementApplyingMoneySum = cfWeaponBudgetDO.getManagementApplyingMoneySum();
                managementUsedMoneySum = cfWeaponBudgetDO.getManagementUsedMoneySum();
                managementUsedMoney = cfWeaponBudgetDO.getManagementUsedMoney();
                managementApplyingMoney = cfWeaponBudgetDO.getManagementApplyingMoney();
            }
            WeaponMoneyInfo weaponMoneyInfo = WeaponMoneyInfo.builder()
                    .assignMoney(childTotalMoneySum + managementUsedMoney + managementApplyingMoney)
                    .unAssignMoney(weaponMoneyParam.getChildTotalMoney() - childTotalMoneySum - managementUsedMoney - managementApplyingMoney)
                    .useMoney(childUsedMoneySum + childApplyingMoneySum + managementApplyingMoneySum + managementUsedMoneySum)
                    .budgetGroupId(weaponMoneyParam.getBudgetGroupId())
                    .build();
            result.add(weaponMoneyInfo);
        }
        return result;
    }

    @Override
    public Map<Integer, WeaponMoneyInfo> mapNoLeafMoneyInfoByBudgetGroupId(List<WeaponMoneyParam> weaponMoneyParamList) {
        List<WeaponMoneyInfo> listMoneyInfo = listNoLeafMoneyInfo(weaponMoneyParamList);
        if (CollectionUtils.isEmpty(listMoneyInfo)) {
            return Maps.newHashMap();
        }
        return listMoneyInfo.stream().collect(Collectors.toMap(WeaponMoneyInfo::getBudgetGroupId, weaponMoneyInfo -> weaponMoneyInfo));
    }

    private Map<Integer, CfWeaponBudgetDO> getBudgetDOMap(List<Integer> budgetIdList) {
        List<CfWeaponBudgetDO> budgetDOList = budgetService.listIds(budgetIdList);
        if (CollectionUtils.isEmpty(budgetDOList)) {
            return Maps.newHashMap();
        }
        return budgetDOList.stream().collect(Collectors.toMap(CfWeaponBudgetDO::getId, v -> v));
    }
}
