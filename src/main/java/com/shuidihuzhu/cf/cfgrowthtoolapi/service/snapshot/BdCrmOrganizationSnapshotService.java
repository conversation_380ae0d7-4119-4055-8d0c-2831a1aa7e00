package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdCrmOrganizationSnapshotDO;

import java.util.List;

/**
 * 日目标组织快照(BdCrmOrganizationSnapshot)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-02 14:58:29
 */
public interface BdCrmOrganizationSnapshotService {

    BdCrmOrganizationSnapshotDO queryById(long id);

    int insert(BdCrmOrganizationSnapshotDO bdCrmOrganizationSnapshot);

    void batchInsert(List<BdCrmOrganizationSnapshotDO> organizationSnapshotList);

    int update(BdCrmOrganizationSnapshotDO bdCrmOrganizationSnapshot);

    boolean deleteById(long id);

    List<BdCrmOrganizationDO> getAllOrgByMixed(String dateKey);

    List<BdCrmOrganizationDO> findDirectSubOrgByOrgId(String dateKey, long orgId);

    List<BdCrmOrganizationDO> findDirectSubOrgByOrgIdList(String dateKey, List<Long> orgIdList);

    BdCrmOrganizationDO getCurrentOrgById(String dateKey, long orgId);

    List<BdCrmOrganizationDO> listAllSubOrgIncludeSelf(String dateKey, long orgId);

}
