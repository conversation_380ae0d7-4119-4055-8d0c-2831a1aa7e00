package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Ordering;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.SeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.DepartmentHospitalSummaryDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalAreaBuildingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalBuildingDepartmentDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.export.impl.CfBdCrmExcelExpoetUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OperateLogSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OperatorLogVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.PatientInventoryDepartmentsBedParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WashBaseParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.DepartmentHospitalSummaryService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalAreaBuildingService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalBuildingDepartmentService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.DownloadExcel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolCrusorQuery;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.dao.patientinventory.*;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.finance.model.CfReminderWord;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/6  15:23
 */
@Service
@Slf4j
public class AdminPatientInventoryServiceImpl implements AdminPatientInventoryService {

    @Autowired
    private PatientInventoryHospitalDao patientInventoryHospitalDao;

    @Autowired
    private PatientInventoryDepartmentsDao patientInventoryDepartmentsDao;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private DepartmentHospitalSummaryService departmentHospitalSummaryService;

    @Autowired
    private HospitalAreaBuildingService buildingService;

    @Autowired
    private PatientInventoryDepartmentsBedDao patientInventoryDepartmentsBedDao;

    @Autowired
    private SeaAccountServiceDelegate seaAccountServiceDelegate;

    @Autowired
    private IOperateLogService operateLogService;

    @Autowired
    private HospitalBuildingDepartmentService departmentService;

    @Autowired
    private PatientInventoryPatientInfoDao patientInventoryPatientInfoDao;

    @Autowired
    private PatientInventoryBedPatientRecordDao patientInventoryBedPatientRecordDao;

    @Autowired
    private PatientInventoryPatientFollowUpRecordDao patientInventoryPatientFollowUpRecordDao;

    @Autowired
    private AdminPatientInventorySearchService adminPatientInventorySearchService;

    @Autowired
    private PatientInventoryRecordDao patientInventoryRecordDao;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired
    private CfBdCrmExcelExpoetUtil cfBdCrmExcelExpoetUtil;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private MaskUtil maskUtil;

    @Autowired
    private BdCrmPatientInventoryService bdCrmPatientInventoryService;

    private static final String PATIENT_INVENTORY_UPLOAD_FILE_FAIL_KEY = "PATIENT_INVENTORY_UPLOAD_FILE_FAIL_KEY";


    @Override
    public Response<PatientInventoryHospitalDuty> getDutyUniqueInfoList(String vhospitalCode) {
        if (StringUtils.isEmpty(vhospitalCode)) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        PatientInventoryHospitalDo patientInventoryHospitalDo = patientInventoryHospitalDao.getByVhospitalCode(vhospitalCode);
        if (patientInventoryHospitalDo == null) {
            return NewResponseUtil.makeSuccess();
        }
        PatientInventoryHospitalDuty result = new PatientInventoryHospitalDuty();
        if (StringUtils.isEmpty(patientInventoryHospitalDo.getDutyUniqueCode())) {
            result.setId(patientInventoryHospitalDo.getId());
            return NewResponseUtil.makeSuccess(result);
        }

        List<String> dutyUniqueCodeList = Splitter.on(",").splitToList(patientInventoryHospitalDo.getDutyUniqueCode());
        List<BdCrmOrgUserRelationDO> crmOrgUserRelationDOList = crmOrganizationRelationService.listByUniqueCodes(dutyUniqueCodeList);
        if (CollectionUtils.isEmpty(crmOrgUserRelationDOList)) {
            result.setId(patientInventoryHospitalDo.getId());
            return NewResponseUtil.makeSuccess(result);
        }

        List<UniqueInfo> uniqueInfoList = Lists.newArrayList();
        for (BdCrmOrgUserRelationDO crmOrgUserRelationDO : crmOrgUserRelationDOList) {
            UniqueInfo uniqueInfo = new UniqueInfo();
            uniqueInfo.setMisName(crmOrgUserRelationDO.getMisName());
            uniqueInfo.setUniqueCode(crmOrgUserRelationDO.getUniqueCode());
            uniqueInfoList.add(uniqueInfo);
        }


        result.setId(patientInventoryHospitalDo.getId());
        result.setUniqueInfoList(uniqueInfoList);
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Void> saveOrUpdateDutyUniqueCodeList(long id, String vhospitalCode, String dutyUniqueCodes) {
        if (StringUtils.isEmpty(vhospitalCode)) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }

        int res = 0;
        if (id > 0) {
            dutyUniqueCodes = StringUtils.isEmpty(dutyUniqueCodes) ? "" : dutyUniqueCodes;
            res = patientInventoryHospitalDao.update(id, dutyUniqueCodes);
            if (res > 0) {
                //责任顾问删除，绑定顾问联动删除
                delBindingUniqueCodeById(vhospitalCode, dutyUniqueCodes);
            }
        } else {
            if (StringUtils.isEmpty(dutyUniqueCodes)) {
                return NewResponseUtil.makeFail("责任顾问不能为空");
            }
            PatientInventoryHospitalDo patientInventoryHospitalDo = patientInventoryHospitalDao.getByVhospitalCode(vhospitalCode);
            if (patientInventoryHospitalDo != null) {
                return NewResponseUtil.makeError(CfGrowthtoolErrorCode.EXIST);
            }
            res = patientInventoryHospitalDao.insert(vhospitalCode, dutyUniqueCodes);
        }

        return res > 0 ? NewResponseUtil.makeSuccess() : NewResponseUtil.makeFail("未做更新，不可提交");
    }

    private void delBindingUniqueCodeById(String vhospitalCode, String dutyUniqueCodes) {
        List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOList = departmentService.listByHospitalCode(vhospitalCode);
        if (CollectionUtils.isEmpty(hospitalBuildingDepartmentDOList)) {
            return;
        }
        List<Integer> idList = hospitalBuildingDepartmentDOList.stream().map(HospitalBuildingDepartmentDO::getId).collect(Collectors.toList());
        if (StringUtils.isEmpty(dutyUniqueCodes)) {
            patientInventoryDepartmentsDao.delBindingUniqueCodeByDepartmentsIds(idList, "", "");
        } else {
            List<PatientInventoryDepartmentsDo> patientInventoryDepartmentsDoList = patientInventoryDepartmentsDao.listByDepartmentsId(idList);
            List<String> dutyUniqueCodeList = Splitter.on(",").splitToList(dutyUniqueCodes);
            Set<String> dutyUniqueCodeSet = Sets.newHashSet(dutyUniqueCodeList);
            List<Long> ids = patientInventoryDepartmentsDoList.stream().filter(v -> !dutyUniqueCodeSet.contains(v.getBindingUniqueCode())).map(PatientInventoryDepartmentsDo::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ids)) {
                patientInventoryDepartmentsDao.delBindingUniqueCodeByIds(ids, "", "");
            }
        }
    }

    @Override
    public int saveOrUpdatePatientInventoryDepartments(int departmentId, String bindingUniqueCode, String bindingMisName, String labels, int departmentType) {
        PatientInventoryDepartmentsDo patientInventoryDepartmentsDo = patientInventoryDepartmentsDao.getByDepartmentsId(departmentId);
        if (patientInventoryDepartmentsDo == null) {
            return patientInventoryDepartmentsDao.insert(departmentId, labels, bindingUniqueCode, bindingMisName, departmentType);
        } else {
            int res = patientInventoryDepartmentsDao.update(departmentId, labels, bindingUniqueCode, bindingMisName, departmentType);
            if (res > 0) {
                updateLabels(departmentId, labels);
                changeDepartmentType(departmentId, patientInventoryDepartmentsDo.getDepartmentType(), departmentType);
            }
            return res;
        }
    }

    /**
     * 更换科室类型，该科室下的患者全部出院
     */
    private void changeDepartmentType(int departmentId, int nowDepartmentType, int newDepartmentType) {
        if (nowDepartmentType == newDepartmentType) {
            return;
        }
        List<PatientInventoryPatientInfoDo> patientInventoryPatientInfoDoList = patientInventoryPatientInfoDao.listByDepartmentsId(departmentId);
        if (CollectionUtils.isEmpty(patientInventoryPatientInfoDoList)) {
            return;
        }
        //获取当前住院患者
        patientInventoryPatientInfoDoList = patientInventoryPatientInfoDoList.stream().filter(v -> v.getBedId() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(patientInventoryPatientInfoDoList)) {
            return;
        }
        List<Long> ids = patientInventoryPatientInfoDoList.stream().map(PatientInventoryPatientInfoDo::getId).collect(Collectors.toList());
        //把顾问绑定的患者信息床位删掉
        patientInventoryPatientInfoDao.batchDelBedIdById(ids);
        //生成一个出院记录
        for (PatientInventoryPatientInfoDo patientInventoryPatientInfoDo : patientInventoryPatientInfoDoList) {
            bdCrmPatientInventoryService.leaveHospitalRecord(patientInventoryPatientInfoDo, nowDepartmentType, null);
        }
    }

    private void updateLabels(int departmentId, String labels) {
        List<PatientInventoryPatientInfoDo> patientInventoryPatientInfoDoList = patientInventoryPatientInfoDao.listByDepartmentsId(departmentId);
        if (CollectionUtils.isEmpty(patientInventoryPatientInfoDoList)) {
            return;
        }

        if (StringUtils.isEmpty(labels)) {
            patientInventoryPatientInfoDao.updateLabelsByDepartmentsId(departmentId, labels);
            patientInventoryRecordDao.updateLabelsByPatientInfoId(departmentId, labels);
            return;
        }

        List<String> labelList = Splitter.on(",").splitToList(labels);
        Set<String> labelSet = Sets.newHashSet(labelList);
        for (PatientInventoryPatientInfoDo patientInventoryPatientInfoDo : patientInventoryPatientInfoDoList) {
            if (StringUtils.isEmpty(patientInventoryPatientInfoDo.getLabels())) {
                continue;
            }
            List<String> patientLabelList = Splitter.on(",").splitToList(patientInventoryPatientInfoDo.getLabels());
            patientLabelList = patientLabelList.stream().filter(labelSet::contains).collect(Collectors.toList());
            String labelStr = CollectionUtils.isNotEmpty(patientLabelList) ? Joiner.on(",").join(patientLabelList) : "";
            patientInventoryPatientInfoDao.updateLabelsById(patientInventoryPatientInfoDo.getId(), labelStr);
            patientInventoryRecordDao.updateLabelsByPatientInfoId(patientInventoryPatientInfoDo.getId(), labelStr);
        }
    }

    @Override
    public List<PatientInventoryDepartmentsDo> listByDepartmentsId(List<Integer> departmentsIds) {
        if (CollectionUtils.isEmpty(departmentsIds)) {
            return Lists.newArrayList();
        }
        return patientInventoryDepartmentsDao.listByDepartmentsId(departmentsIds);
    }

    @Override
    public Response<List<PatientInventoryHospitalDetailModel>> getHospitalDepartmentsInfo(String province, String cityName) {
        List<DepartmentHospitalSummaryDO> departmentHospitalSummaryDOList = departmentHospitalSummaryService.getByHospitalProvinceCity(province, cityName);
        if (CollectionUtils.isEmpty(departmentHospitalSummaryDOList)) {
            return NewResponseUtil.makeSuccess();
        }

        List<String> vhospitalCodeList = departmentHospitalSummaryDOList.stream().map(DepartmentHospitalSummaryDO::getVhospitalCode).collect(Collectors.toList());
        // 根据医院获取楼宇信息
        List<HospitalAreaBuildingDO> hospitalAreaBuildingList = buildingService.listByHospitalCode(vhospitalCodeList);
        Map<String, List<HospitalAreaBuildingDO>> hospitalAreaBuildingMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(hospitalAreaBuildingList)) {
            hospitalAreaBuildingMap = hospitalAreaBuildingList.stream().collect(Collectors.groupingBy(HospitalAreaBuildingDO::getVhospitalCode));
        }

        // 根据医院获取科室信息
        List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentList = departmentService.listByVhospitalCodes(vhospitalCodeList);
        Map<String, List<HospitalBuildingDepartmentDO>> hospitalBuildingDepartmentDOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(hospitalBuildingDepartmentList)) {
            hospitalBuildingDepartmentDOMap = hospitalBuildingDepartmentList.stream().collect(Collectors.groupingBy(HospitalBuildingDepartmentDO::getVhospitalCode));
        }

        List<PatientInventoryHospitalDetailModel> result = Lists.newArrayList();
        for (DepartmentHospitalSummaryDO departmentHospitalSummaryDO : departmentHospitalSummaryDOList) {

            //医院
            PatientInventoryHospitalDetailModel patientInventoryHospitalDetailModel = new PatientInventoryHospitalDetailModel();
            patientInventoryHospitalDetailModel.setVhospitalCode(departmentHospitalSummaryDO.getVhospitalCode());
            patientInventoryHospitalDetailModel.setHospitalName(departmentHospitalSummaryDO.getHospitalName());

            List<PatientInventoryHospitalDetailModel.BuildingDetailModel> buildingDetailModelList = Lists.newArrayList();

            List<HospitalAreaBuildingDO> hospitalAreaBuildingDOList = hospitalAreaBuildingMap.get(departmentHospitalSummaryDO.getVhospitalCode());
            List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOList = hospitalBuildingDepartmentDOMap.get(departmentHospitalSummaryDO.getVhospitalCode());
            Map<Integer, List<HospitalBuildingDepartmentDO>> hospitalBuildingDepartmentDOListMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(hospitalBuildingDepartmentDOList)) {
                hospitalBuildingDepartmentDOListMap = hospitalBuildingDepartmentDOList.stream().collect(Collectors.groupingBy(HospitalBuildingDepartmentDO::getBuildingId));
            }

            if (CollectionUtils.isNotEmpty(hospitalAreaBuildingDOList)) {
                for (HospitalAreaBuildingDO hospitalAreaBuildingDO : hospitalAreaBuildingDOList) {
                    //楼层
                    PatientInventoryHospitalDetailModel.BuildingDetailModel buildingDetailModel = new PatientInventoryHospitalDetailModel.BuildingDetailModel();
                    buildingDetailModel.setBuildingFloorArea(hospitalAreaBuildingDO.getBuildingFloorArea());
                    buildingDetailModel.setBuildName(hospitalAreaBuildingDO.getBuildingName());

                    List<PatientInventoryHospitalDetailModel.DepartmentDetailModel> departmentDetailModelList = Lists.newArrayList();
                    List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOS = hospitalBuildingDepartmentDOListMap.get(hospitalAreaBuildingDO.getId());
                    if (CollectionUtils.isNotEmpty(hospitalBuildingDepartmentDOS)) {
                        for (HospitalBuildingDepartmentDO hospitalBuildingDepartmentDO : hospitalBuildingDepartmentDOS) {
                            //科室
                            PatientInventoryHospitalDetailModel.DepartmentDetailModel departmentDetailModel = new PatientInventoryHospitalDetailModel.DepartmentDetailModel();
                            departmentDetailModel.setId(hospitalBuildingDepartmentDO.getId());
                            departmentDetailModel.setBuildingId(hospitalBuildingDepartmentDO.getBuildingId());
                            departmentDetailModel.setDepartmentName(hospitalBuildingDepartmentDO.getBuildingDepartment());
                            departmentDetailModel.setClassifyBuildingDepartment(hospitalBuildingDepartmentDO.getClassifyBuildingDepartment());
                            departmentDetailModel.setBuildingFloor(hospitalBuildingDepartmentDO.getBuildingFloor());
                            departmentDetailModel.setBuildingFloorArea(hospitalBuildingDepartmentDO.getBuildingFloorArea());
                            departmentDetailModelList.add(departmentDetailModel);
                        }
                    }
                    buildingDetailModel.setDepartmentDetailModelList(departmentDetailModelList);
                    buildingDetailModelList.add(buildingDetailModel);
                }
            }
            patientInventoryHospitalDetailModel.setBuildingDetailModelList(buildingDetailModelList);
            result.add(patientInventoryHospitalDetailModel);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Void> saveOrUpdate(PatientInventoryDepartmentsBedParam param, long authSaasUserId) {
        if (param.getBedName().length() > 50) {
            return NewResponseUtil.makeFail("不得大于50字");
        }
        AdminUserAccountModel adminUserAccountModel = seaAccountServiceDelegate.getValidUserAccountById(authSaasUserId);
        int res = 0;
        if (param.getId() > 0) {
            PatientInventoryDepartmentsBedDo patientInventoryDepartmentsBedDo = patientInventoryDepartmentsBedDao.getByDepartmentsIdAndBedName(param.getDepartmentsId(), param.getBedName());
            if (patientInventoryDepartmentsBedDo != null && patientInventoryDepartmentsBedDo.getId() != param.getId()) {
                return NewResponseUtil.makeFail("床位名称已存在，不可提交");
            }
            res = patientInventoryDepartmentsBedDao.update(param.getId(), param.getBedName());
            if (res > 0) {
                String name = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse("");
                String comment = "操作类型：编辑"
                        + "；操作人：" + name
                        + "；操作时间：" + DateUtil.getCurrentDateTimeStr()
                        + "；操作内容：" + "床位名称改为：" + param.getBedName();
                customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(param.getId()), OperateTypeEnum.PATIENT_INVENTORY_UPDATE.getDesc(),
                        OperateTypeEnum.PATIENT_INVENTORY_UPDATE, comment, authSaasUserId, Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse("")));
            }
        } else {
            PatientInventoryDepartmentsBedDo patientInventoryDepartmentsBedDo = patientInventoryDepartmentsBedDao.getByDepartmentsIdAndBedName(param.getDepartmentsId(), param.getBedName());
            if (patientInventoryDepartmentsBedDo != null) {
                return NewResponseUtil.makeFail("床位名称已存在，不可提交");
            }

            String name = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse("");
            param.setOperatorUserName(name);
            param.setOperatorUserId(authSaasUserId);
            res = patientInventoryDepartmentsBedDao.insert(param);
            if (res > 0) {
                patientInventoryDepartmentsBedDao.updateOrderById(param.getId(), param.getId());
                String comment = "操作类型：新增"
                        + "；操作人：" + name
                        + "；操作时间：" + DateUtil.getCurrentDateTimeStr()
                        + "；操作内容：" + "床位名称为：" + param.getBedName();
                customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(param.getId()), OperateTypeEnum.PATIENT_INVENTORY_SAVE.getDesc(),
                        OperateTypeEnum.PATIENT_INVENTORY_SAVE, comment, authSaasUserId, Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse("")));
            }
        }
        return res > 0 ? NewResponseUtil.makeSuccess() : NewResponseUtil.makeFail("未做更新，不可提交");
    }

    @Override
    public Response<PatientInventoryDepartmentsBedVo> getBedInfo(long id) {
        PatientInventoryDepartmentsBedDo patientInventoryDepartmentsBedDo = patientInventoryDepartmentsBedDao.getById(id);
        if (patientInventoryDepartmentsBedDo == null) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }

        PatientInventoryDepartmentsBedVo result = new PatientInventoryDepartmentsBedVo();
        result.setId(patientInventoryDepartmentsBedDo.getId());
        result.setBedName(patientInventoryDepartmentsBedDo.getBedName());
        result.setDepartmentsId(patientInventoryDepartmentsBedDo.getDepartmentsId());
        result.setProvince(patientInventoryDepartmentsBedDo.getProvince());
        result.setCity(patientInventoryDepartmentsBedDo.getCity());
        result.setHospitalName(patientInventoryDepartmentsBedDo.getHospitalName());
        HospitalBuildingDepartmentDO hospitalBuildingDepartmentDO = departmentService.queryById(patientInventoryDepartmentsBedDo.getDepartmentsId());
        if (hospitalBuildingDepartmentDO != null) {
            result.setClassifyBuildingDepartment(hospitalBuildingDepartmentDO.getClassifyBuildingDepartment());
            result.setDepartmentsName(hospitalBuildingDepartmentDO.getBuildingDepartment() + "-" + hospitalBuildingDepartmentDO.getBuildingFloorArea() + "-" + hospitalBuildingDepartmentDO.getBuildingFloor());
            HospitalAreaBuildingDO hospitalAreaBuildingDO = buildingService.queryById(hospitalBuildingDepartmentDO.getBuildingId());
            if (hospitalAreaBuildingDO != null) {
                result.setBuildingName(hospitalAreaBuildingDO.getBuildingName());
            }
        }
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Map<String, Object>> departmentsBedList(String province, String city, String hospitalName, String departmentsName, Integer valid, int pageNum, int pageSize) {
        PatientInventoryDepartmentsBedSearch patientInventoryDepartmentsBedSearch = PatientInventoryDepartmentsBedSearch.builder()
                .province(province)
                .city(city)
                .hospitalName(hospitalName)
                .departmentsName(departmentsName)
                .valid(valid)
                .current(pageNum)
                .pageSize(pageSize)
                .order("create_time")
                .sortOrder(SortOrder.DESC)
                .build();
        Pair<Long, List<Long>> pair = adminPatientInventorySearchService.search(patientInventoryDepartmentsBedSearch);
        List<PatientInventoryDepartmentsBedVo> patientInventoryDepartmentsBedVoList = buildPatientInventoryDepartmentsBedVoList(pair.getRight(), false);
        Map<String, Object> result = Maps.newHashMap();

        Map<String, Object> pageMap = Maps.newHashMap();
        pageMap.put("total", pair.getLeft());
        pageMap.put("current", (patientInventoryDepartmentsBedSearch.getCurrent() == null || patientInventoryDepartmentsBedSearch.getCurrent() < 1) ? 1 : patientInventoryDepartmentsBedSearch.getCurrent());
        pageMap.put("pageSize", pageSize);
        result.put("pagination", pageMap);
        result.put("data", patientInventoryDepartmentsBedVoList);
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Map<String, Object>> departmentsBedListOrder(String hospitalName, String departmentsName, Integer valid, int pageNum, int pageSize) {
        PatientInventoryDepartmentsBedSearch patientInventoryDepartmentsBedSearch = PatientInventoryDepartmentsBedSearch.builder()
                .hospitalName(hospitalName)
                .departmentsName(departmentsName)
                .valid(valid)
                .current(pageNum)
                .pageSize(pageSize)
                .order("bed_order")
                .sortOrder(SortOrder.ASC)
                .build();
        Pair<Long, List<Long>> pair = adminPatientInventorySearchService.search(patientInventoryDepartmentsBedSearch);
        List<PatientInventoryDepartmentsBedVo> patientInventoryDepartmentsBedVoList = buildPatientInventoryDepartmentsBedVoList(pair.getRight(), true);
        //序号
        int orderNumber = (patientInventoryDepartmentsBedSearch.getCurrent() - 1) * patientInventoryDepartmentsBedSearch.getPageSize() + 1;
        for (PatientInventoryDepartmentsBedVo patientInventoryDepartmentsBedVo : patientInventoryDepartmentsBedVoList) {
            patientInventoryDepartmentsBedVo.setTheoreticalBedOrder(orderNumber++);
        }
        Map<String, Object> result = Maps.newHashMap();

        Map<String, Object> pageMap = Maps.newHashMap();
        pageMap.put("total", pair.getLeft());
        pageMap.put("current", (patientInventoryDepartmentsBedSearch.getCurrent() == null || patientInventoryDepartmentsBedSearch.getCurrent() < 1) ? 1 : patientInventoryDepartmentsBedSearch.getCurrent());
        pageMap.put("pageSize", pageSize);
        result.put("pagination", pageMap);
        result.put("data", patientInventoryDepartmentsBedVoList);
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<List<PatientInventoryDepartmentsBedPatientVo>> departmentsBedDetail(int bedId) {
        if (bedId <= 0) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<PatientInventoryBedPatientRecordDo> patientInventoryBedPatientRecordDoList = patientInventoryBedPatientRecordDao.listByBedId(bedId);
        if (CollectionUtils.isEmpty(patientInventoryBedPatientRecordDoList)) {
            return NewResponseUtil.makeSuccess();
        }

        List<Long> patientInfoIdList = patientInventoryBedPatientRecordDoList.stream().map(PatientInventoryBedPatientRecordDo::getPatientInfoId).distinct().collect(Collectors.toList());
        List<PatientInventoryPatientInfoDo> patientInventoryPatientInfoDoList = patientInventoryPatientInfoDao.listByIds(patientInfoIdList);
        if (CollectionUtils.isEmpty(patientInventoryPatientInfoDoList)) {
            return NewResponseUtil.makeSuccess();
        }
        Map<Long, PatientInventoryPatientInfoDo> patientInventoryPatientInfoDoMap = patientInventoryPatientInfoDoList.stream().collect(Collectors.toMap(PatientInventoryPatientInfoDo::getId, Function.identity()));

        //获取历史数据
        List<PatientInventoryRecordDo> patientInventoryRecordDoList = patientInventoryRecordDao.listByPatientInfoIds(patientInfoIdList);
        Map<Long, List<PatientInventoryRecordDo>> patientInventoryRecordDoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(patientInventoryRecordDoList)) {
            patientInventoryRecordDoMap = patientInventoryRecordDoList.stream().collect(Collectors.groupingBy(PatientInventoryRecordDo::getPatientInfoId));
        }
        //获取顾问姓名
        Map<String, String> uniqueCodeMap = listMisName(patientInventoryRecordDoList);

        List<PatientInventoryPatientFollowUpRecordDo> patientInventoryPatientFollowUpRecordDoList = patientInventoryPatientFollowUpRecordDao.listByPatientInfoIds(patientInfoIdList, null);
        Map<Long, List<PatientInventoryPatientFollowUpRecordDo>> patientInventoryPatientFollowUpRecordDoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(patientInventoryPatientFollowUpRecordDoList)) {
            patientInventoryPatientFollowUpRecordDoMap = patientInventoryPatientFollowUpRecordDoList.stream().collect(Collectors.groupingBy(PatientInventoryPatientFollowUpRecordDo::getPatientInfoId));
        }

        Set<Long> set = Sets.newHashSet();
        List<PatientInventoryDepartmentsBedPatientVo> result = Lists.newArrayList();
        for (PatientInventoryBedPatientRecordDo patientInventoryBedPatientRecordDo : patientInventoryBedPatientRecordDoList) {
            if (set.contains(patientInventoryBedPatientRecordDo.getPatientInfoId())) {
                continue;
            }
            PatientInventoryPatientInfoDo patientInventoryPatientInfoDo = patientInventoryPatientInfoDoMap.get(patientInventoryBedPatientRecordDo.getPatientInfoId());
            if (patientInventoryPatientInfoDo == null) {
                continue;
            }
            set.add(patientInventoryPatientInfoDo.getId());

            PatientInventoryDepartmentsBedPatientVo patientInventoryDepartmentsBedPatientVo = new PatientInventoryDepartmentsBedPatientVo();
            patientInventoryDepartmentsBedPatientVo.setFirstRegistrationTime(DateUtil.formatDateTime(patientInventoryPatientInfoDo.getCreateTime()));
            patientInventoryDepartmentsBedPatientVo.setPatientName(patientInventoryPatientInfoDo.getPatientName());
            NumberMaskVo numberMaskVo = maskUtil.buildByEncryptPhone(patientInventoryPatientInfoDo.getPatientMobile());
            patientInventoryDepartmentsBedPatientVo.setNumberMaskVo(numberMaskVo);
            patientInventoryDepartmentsBedPatientVo.setPatientDiseaseName(patientInventoryPatientInfoDo.getPatientDisease());
            patientInventoryDepartmentsBedPatientVo.setLabels(getDepartmentsLabelVoList(patientInventoryPatientInfoDo.getLabels()));

            List<PatientInventoryRecordDo> patientInventoryRecordDos = patientInventoryRecordDoMap.get(patientInventoryPatientInfoDo.getId());
            if (CollectionUtils.isNotEmpty(patientInventoryRecordDos)) {
                Optional<PatientInventoryRecordDo> patientInventoryRecordDo = patientInventoryRecordDos.stream().filter(v -> StringUtils.isEmpty(v.getBindingUniqueCodeLeader())).findFirst();
                patientInventoryRecordDo.ifPresent(inventoryRecordDo -> patientInventoryDepartmentsBedPatientVo.setBindingMisName(uniqueCodeMap.getOrDefault(inventoryRecordDo.getBindingUniqueCode(), "")));
                Optional<PatientInventoryRecordDo> optionalPatientInventoryRecordDo = patientInventoryRecordDos.stream().filter(v -> StringUtils.isNotEmpty(v.getBindingUniqueCodeLeader())).findFirst();
                optionalPatientInventoryRecordDo.ifPresent(inventoryRecordDo -> patientInventoryDepartmentsBedPatientVo.setLeaderName(uniqueCodeMap.getOrDefault(inventoryRecordDo.getBindingUniqueCodeLeader(), "")));
            }
            List<PatientInventoryPatientFollowUpRecordDo> patientInventoryPatientFollowUpRecordDos = patientInventoryPatientFollowUpRecordDoMap.get(patientInventoryPatientInfoDo.getId());
            if (CollectionUtils.isNotEmpty(patientInventoryPatientFollowUpRecordDos)) {
                List<PatientInventoryDepartmentsBedPatientVo.PatientFollowUpRecordView> intention = Lists.newArrayList();
                for (PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo : patientInventoryPatientFollowUpRecordDos) {
                    PatientInventoryDepartmentsBedPatientVo.PatientFollowUpRecordView patientInventoryRecordView = new PatientInventoryDepartmentsBedPatientVo.PatientFollowUpRecordView();
                    patientInventoryRecordView.setIntention(getIntention(patientInventoryPatientFollowUpRecordDo));
                    patientInventoryRecordView.setContent(patientInventoryPatientFollowUpRecordDo.getContent());
                    patientInventoryRecordView.setNextFollowUpTime(DateUtil.formatDateTime(patientInventoryPatientFollowUpRecordDo.getNextFollowUpTime()));
                    patientInventoryRecordView.setFollowUpTime(DateUtil.formatDateTime(patientInventoryPatientFollowUpRecordDo.getCreateTime()));
                    intention.add(patientInventoryRecordView);
                }
                patientInventoryDepartmentsBedPatientVo.setIntention(intention);

                List<PatientInventoryPatientFollowUpRecordDo> inventoryRecordList = patientInventoryPatientFollowUpRecordDos.stream().filter(v -> StringUtils.isNotEmpty(v.getInventoryRecord())).collect(Collectors.toList());
                List<PatientInventoryDepartmentsBedPatientVo.PatientInventoryRecordView> inventory = Lists.newArrayList();
                for (PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo : inventoryRecordList) {
                    PatientInventoryDepartmentsBedPatientVo.PatientInventoryRecordView patientInventoryRecordView = new PatientInventoryDepartmentsBedPatientVo.PatientInventoryRecordView();
                    patientInventoryRecordView.setInventoryTime(DateUtil.formatDateTime(patientInventoryPatientFollowUpRecordDo.getInventoryTime()));
                    patientInventoryRecordView.setInventoryRecord(patientInventoryPatientFollowUpRecordDo.getInventoryRecord());
                    inventory.add(patientInventoryRecordView);
                }
                patientInventoryDepartmentsBedPatientVo.setInventory(inventory);
            }
            if (patientInventoryPatientInfoDo.getBedId() > 0 && patientInventoryPatientInfoDo.getBedId() == bedId) {
                patientInventoryDepartmentsBedPatientVo.setStatus("在院");
            } else {
                patientInventoryDepartmentsBedPatientVo.setStatus("离院");
            }
            result.add(patientInventoryDepartmentsBedPatientVo);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<PatientInventoryHospitalInfo> departmentsBedFilter(String province, String city, String hospitalName, String departmentsName, int limit) {
        PatientInventoryHospitalInfo patientInventoryHospitalInfo = new PatientInventoryHospitalInfo();
        if (StringUtils.isNotEmpty(province)) {
            List<String> provinceList = patientInventoryDepartmentsBedDao.listByProvinceLimit(province, limit);
            patientInventoryHospitalInfo.setProvinceList(provinceList);
        }
        if (StringUtils.isNotEmpty(city)) {
            List<String> cityList = patientInventoryDepartmentsBedDao.listByCityLimit(city, limit);
            patientInventoryHospitalInfo.setCityList(cityList);
        }
        if (StringUtils.isNotEmpty(hospitalName)) {
            List<String> hospitalNameList = patientInventoryDepartmentsBedDao.listByHospitalNameLimit(hospitalName, limit);
            patientInventoryHospitalInfo.setHospitalNameList(hospitalNameList);
        }
        if (StringUtils.isNotEmpty(departmentsName)) {
            List<String> departmentsNameList = patientInventoryDepartmentsBedDao.listByDepartmentsNameLimit(departmentsName, limit);
            patientInventoryHospitalInfo.setDepartmentsNameList(departmentsNameList);
        }
        return NewResponseUtil.makeSuccess(patientInventoryHospitalInfo);
    }

    @Override
    public Response<Void> updateValid(long id, int valid) {
        int res = patientInventoryDepartmentsBedDao.updateValid(id, valid);
        return res > 0 ? NewResponseUtil.makeSuccess() : NewResponseUtil.makeFail("未做更新，不可提交");
    }

    @Override
    public Response<Integer> updateValidBatch(String ids, int valid) {
        if (StringUtils.isEmpty(ids)) {
            return NewResponseUtil.makeFail("未选择数据");
        }
        List<Long> idList = Splitter.on(",").splitToList(ids).stream().map(Long::parseLong).collect(Collectors.toList());
        int res = patientInventoryDepartmentsBedDao.updateValidBatch(idList, valid);
        return res > 0 ? NewResponseUtil.makeSuccess(res) : NewResponseUtil.makeFail("未做更新，不可提交");
    }

    @Override
    public Response<CommonResultModel<OperatorLogVO>> getOperateLog(OperateLogSearchModel searchModel) {
        CommonResultModel<OperatorLogVO> commonResultModel = operateLogService.getOperateLog(searchModel, OperateTypeEnum.getEnumListByAttriButeType(OperateTypeEnum.PATIENT_INVENTORY_SAVE.getAttributeType()));
        return NewResponseUtil.makeSuccess(commonResultModel);
    }

    @Override
    public void batchDelete(List<Integer> departmentsIds) {
        patientInventoryDepartmentsDao.batchDelete(departmentsIds);
        patientInventoryDepartmentsBedDao.batchDelete(departmentsIds);
    }

    @Override
    public Response<Void> importBedInfo(MultipartFile file, List<PatientInventoryDepartmentsBedTemplate> departmentsBedTemplateList, long authSaasUserId) {
        if (CollectionUtils.isEmpty(departmentsBedTemplateList)) {
            return NewResponseUtil.makeFail("未导入床位信息");
        }

        List<ExcelFailVo> failVoList = Lists.newArrayListWithCapacity(departmentsBedTemplateList.size());
        List<PatientInventoryDepartmentsBedTemplate> dataList = Lists.newArrayList();
        //检验 过滤掉参数缺失的
        for (PatientInventoryDepartmentsBedTemplate patientInventoryDepartmentsBedTemplate : departmentsBedTemplateList) {
            if (patientInventoryDepartmentsBedTemplate == null) {
                continue;
            }
            if (StringUtils.isEmpty(patientInventoryDepartmentsBedTemplate.getDepartmentsId()) || StringUtils.isEmpty(patientInventoryDepartmentsBedTemplate.getBedName())) {
                ExcelFailVo failVo = new ExcelFailVo(patientInventoryDepartmentsBedTemplate.getDepartmentsId(), patientInventoryDepartmentsBedTemplate.getBedName(), "参数缺失");
                failVoList.add(failVo);
                continue;
            }
            if (!StringUtils.isNumeric(patientInventoryDepartmentsBedTemplate.getDepartmentsId())) {
                ExcelFailVo failVo = new ExcelFailVo(patientInventoryDepartmentsBedTemplate.getDepartmentsId(), patientInventoryDepartmentsBedTemplate.getBedName(), "科室ID需要填写数字");
                failVoList.add(failVo);
                continue;
            }
            if (Long.parseLong(patientInventoryDepartmentsBedTemplate.getDepartmentsId()) > Integer.MAX_VALUE) {
                ExcelFailVo failVo = new ExcelFailVo(patientInventoryDepartmentsBedTemplate.getDepartmentsId(), patientInventoryDepartmentsBedTemplate.getBedName(), "科室ID超长");
                failVoList.add(failVo);
                continue;
            }
            dataList.add(patientInventoryDepartmentsBedTemplate);
        }

        //通过科室id进行分组
        Map<String, List<PatientInventoryDepartmentsBedTemplate>> departmentMap = dataList.stream().collect(Collectors.groupingBy(PatientInventoryDepartmentsBedTemplate::getDepartmentsId));
        Set<PatientInventoryDepartmentsBedTemplate> departmentsBedTemplateSet = Sets.newHashSet();
        for (Map.Entry<String, List<PatientInventoryDepartmentsBedTemplate>> data : departmentMap.entrySet()) {
            //同一科室，对床位名称进行分组
            Map<String, List<PatientInventoryDepartmentsBedTemplate>> bedNameMap = data.getValue().stream().collect(Collectors.groupingBy(PatientInventoryDepartmentsBedTemplate::getBedName));
            for (Map.Entry<String, List<PatientInventoryDepartmentsBedTemplate>> entry : bedNameMap.entrySet()) {
                //有重复的床位名称，过滤掉，加入失败集合
                if (entry.getValue().size() > 1) {
                    entry.getValue().forEach(v -> {
                        ExcelFailVo failVo = new ExcelFailVo(v.getDepartmentsId(), v.getBedName(), "床位名称重复");
                        failVoList.add(failVo);
                    });
                    PatientInventoryDepartmentsBedTemplate patientInventoryDepartmentsBedTemplate = new PatientInventoryDepartmentsBedTemplate();
                    patientInventoryDepartmentsBedTemplate.setDepartmentsId(data.getKey());
                    patientInventoryDepartmentsBedTemplate.setBedName(entry.getKey());
                    departmentsBedTemplateSet.add(patientInventoryDepartmentsBedTemplate);
                }
            }
        }
        dataList = dataList.stream().filter(v -> !departmentsBedTemplateSet.contains(v)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataList)) {
            return NewResponseUtil.makeFail("导入数据都未添加成功");
        }

        //通过科室id查询科室信息
        List<Integer> departmentsIdList = dataList.stream().map(PatientInventoryDepartmentsBedTemplate::getIntegerDepartmentsId).distinct().collect(Collectors.toList());
        List<List<Integer>> departmentsIdListPartition = Lists.partition(departmentsIdList, 500);
        List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOList = Lists.newArrayList();
        for (List<Integer> departmentDOList : departmentsIdListPartition) {
            List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOS = departmentService.listByIds(departmentDOList);
            hospitalBuildingDepartmentDOList.addAll(hospitalBuildingDepartmentDOS);
        }
        if (CollectionUtils.isEmpty(hospitalBuildingDepartmentDOList)) {
            return NewResponseUtil.makeFail("科室不存在");
        }

        //科室不存在的，添加到失败列表
        Set<Integer> departmentsIdSet = hospitalBuildingDepartmentDOList.stream().map(HospitalBuildingDepartmentDO::getId).collect(Collectors.toSet());
        List<PatientInventoryDepartmentsBedTemplate> failList = dataList.stream().filter(v -> !departmentsIdSet.contains(v.getIntegerDepartmentsId())).collect(Collectors.toList());
        for (PatientInventoryDepartmentsBedTemplate template : failList) {
            ExcelFailVo failVo = new ExcelFailVo(template.getDepartmentsId(), template.getBedName(), "科室不存在");
            failVoList.add(failVo);
        }

        List<PatientInventoryDepartmentsBedTemplate> patientInventoryDepartmentsBedTemplateList = dataList.stream().filter(v -> departmentsIdSet.contains(v.getIntegerDepartmentsId())).collect(Collectors.toList());
        //通过科室id查询床位信息
        List<Integer> departmentsIds = patientInventoryDepartmentsBedTemplateList.stream().map(PatientInventoryDepartmentsBedTemplate::getIntegerDepartmentsId).distinct().collect(Collectors.toList());
        List<List<Integer>> departmentsIdsPartition = Lists.partition(departmentsIds, 500);
        List<PatientInventoryDepartmentsBedDo> patientInventoryDepartmentsBedDoList = Lists.newArrayList();
        for (List<Integer> departmentIds : departmentsIdsPartition) {
            List<PatientInventoryDepartmentsBedDo> patientInventoryDepartmentsBedDos = patientInventoryDepartmentsBedDao.listByDepartmentsId(departmentIds);
            patientInventoryDepartmentsBedDoList.addAll(patientInventoryDepartmentsBedDos);
        }
        //获取床位信息
        Map<Integer, List<PatientInventoryDepartmentsBedDo>> departmentsBedMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(patientInventoryDepartmentsBedDoList)) {
            departmentsBedMap = patientInventoryDepartmentsBedDoList.stream().collect(Collectors.groupingBy(PatientInventoryDepartmentsBedDo::getDepartmentsId));
        }
        //获取科室信息
        Map<Integer, HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOMap = Maps.newHashMap();
        //获取医院信息
        Map<String, DepartmentHospitalSummaryDO> departmentHospitalSummaryDOMap = Maps.newHashMap();
        Set<Integer> departmentIdSet = patientInventoryDepartmentsBedDoList.stream().map(PatientInventoryDepartmentsBedDo::getDepartmentsId).collect(Collectors.toSet());
        //获取没有添加床位信息的科室
        List<HospitalBuildingDepartmentDO> newHospitalBuildingDepartmentDOList = hospitalBuildingDepartmentDOList.stream().filter(v -> !departmentIdSet.contains(v.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newHospitalBuildingDepartmentDOList)) {
            List<String> vhospitalCodeList = newHospitalBuildingDepartmentDOList.stream().map(HospitalBuildingDepartmentDO::getVhospitalCode).collect(Collectors.toList());
            List<List<String>> vhospitalCodesPartition = Lists.partition(vhospitalCodeList, 500);
            List<DepartmentHospitalSummaryDO> departmentHospitalSummaryDOList = Lists.newArrayList();
            for (List<String> vhospitalCodes : vhospitalCodesPartition) {
                List<DepartmentHospitalSummaryDO> departmentHospitalSummaryDOs = departmentHospitalSummaryService.listByHospitalCode(vhospitalCodes);
                departmentHospitalSummaryDOList.addAll(departmentHospitalSummaryDOs);
            }
            if (CollectionUtils.isNotEmpty(departmentHospitalSummaryDOList)) {
                departmentHospitalSummaryDOMap = departmentHospitalSummaryDOList.stream().collect(Collectors.toMap(DepartmentHospitalSummaryDO::getVhospitalCode, Function.identity()));
            }
            hospitalBuildingDepartmentDOMap = newHospitalBuildingDepartmentDOList.stream().collect(Collectors.toMap(HospitalBuildingDepartmentDO::getId, Function.identity()));
        }

        AdminUserAccountModel adminUserAccountModel = seaAccountServiceDelegate.getValidUserAccountById(authSaasUserId);
        String operatorUserName = Optional.ofNullable(adminUserAccountModel).map(AdminUserAccountModel::getName).orElse("");

        List<PatientInventoryDepartmentsBedParam> paramList = Lists.newArrayList();
        for (PatientInventoryDepartmentsBedTemplate patientInventoryDepartmentsBedTemplate : patientInventoryDepartmentsBedTemplateList) {
            if (patientInventoryDepartmentsBedTemplate.getBedName().length() > 50) {
                ExcelFailVo failVo = new ExcelFailVo(patientInventoryDepartmentsBedTemplate.getDepartmentsId(), patientInventoryDepartmentsBedTemplate.getBedName(), "床位名称不得大于50字");
                failVoList.add(failVo);
                continue;
            }
            List<PatientInventoryDepartmentsBedDo> bedDoList = departmentsBedMap.get(patientInventoryDepartmentsBedTemplate.getIntegerDepartmentsId());
            if (CollectionUtils.isNotEmpty(bedDoList)) {
                Set<String> bedNameSet = bedDoList.stream().map(PatientInventoryDepartmentsBedDo::getBedName).collect(Collectors.toSet());
                if (bedNameSet.contains(patientInventoryDepartmentsBedTemplate.getBedName())) {
                    ExcelFailVo failVo = new ExcelFailVo(patientInventoryDepartmentsBedTemplate.getDepartmentsId(), patientInventoryDepartmentsBedTemplate.getBedName(), "床位名称已存在");
                    failVoList.add(failVo);
                    continue;
                }
            }

            PatientInventoryDepartmentsBedParam param = new PatientInventoryDepartmentsBedParam();
            param.setBedName(patientInventoryDepartmentsBedTemplate.getBedName());
            param.setDepartmentsId(patientInventoryDepartmentsBedTemplate.getIntegerDepartmentsId());
            if (CollectionUtils.isNotEmpty(bedDoList)) {
                PatientInventoryDepartmentsBedDo patientInventoryDepartmentsBedDo = bedDoList.get(0);
                param.setDepartmentsName(patientInventoryDepartmentsBedDo.getDepartmentsName());
                param.setProvince(patientInventoryDepartmentsBedDo.getProvince());
                param.setCity(patientInventoryDepartmentsBedDo.getCity());
                param.setHospitalName(patientInventoryDepartmentsBedDo.getHospitalName());
                param.setVhospitalCode(patientInventoryDepartmentsBedDo.getVhospitalCode());
            } else {
                HospitalBuildingDepartmentDO hospitalBuildingDepartmentDO = hospitalBuildingDepartmentDOMap.get(patientInventoryDepartmentsBedTemplate.getIntegerDepartmentsId());
                if (hospitalBuildingDepartmentDO == null) {
                    ExcelFailVo failVo = new ExcelFailVo(patientInventoryDepartmentsBedTemplate.getDepartmentsId(), patientInventoryDepartmentsBedTemplate.getBedName(), "科室信息不存在");
                    failVoList.add(failVo);
                    continue;
                }
                DepartmentHospitalSummaryDO departmentHospitalSummaryDO = departmentHospitalSummaryDOMap.get(hospitalBuildingDepartmentDO.getVhospitalCode());
                if (departmentHospitalSummaryDO == null) {
                    ExcelFailVo failVo = new ExcelFailVo(patientInventoryDepartmentsBedTemplate.getDepartmentsId(), patientInventoryDepartmentsBedTemplate.getBedName(), "医院信息不存在");
                    failVoList.add(failVo);
                    continue;
                }
                param.setProvince(departmentHospitalSummaryDO.getHospitalProvince());
                param.setCity(departmentHospitalSummaryDO.getHospitalCity());
                param.setHospitalName(departmentHospitalSummaryDO.getHospitalName());
                param.setVhospitalCode(departmentHospitalSummaryDO.getVhospitalCode());
                param.setDepartmentsName(hospitalBuildingDepartmentDO.getBuildingDepartment());
            }
            param.setOperatorUserId(authSaasUserId);
            param.setOperatorUserName(operatorUserName);
            paramList.add(param);
        }

        if (CollectionUtils.isNotEmpty(paramList)) {
            List<List<PatientInventoryDepartmentsBedParam>> partition = Lists.partition(paramList, 500);
            for (List<PatientInventoryDepartmentsBedParam> params : partition) {
                int res = patientInventoryDepartmentsBedDao.batchInsert(params);
                if (res > 0) {
                    Map<Long, Long> map = params.stream().collect(Collectors.toMap(PatientInventoryDepartmentsBedParam::getId, PatientInventoryDepartmentsBedParam::getId));
                    patientInventoryDepartmentsBedDao.updateOrderByMap(map);
                    for (PatientInventoryDepartmentsBedParam param : params) {
                        String comment = "操作类型：导入"
                                + "；操作人：" + operatorUserName
                                + "；操作时间：" + DateUtil.getCurrentDateTimeStr()
                                + "；操作内容：导入成功";
                        customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(param.getId()), OperateTypeEnum.PATIENT_INVENTORY_IMPORT.getDesc(),
                                OperateTypeEnum.PATIENT_INVENTORY_IMPORT, comment, authSaasUserId, operatorUserName));
                    }
                }
            }
        }

        ExcelVo excelVo = new ExcelVo();
        excelVo.setOriginalFilename(file.getOriginalFilename());
        excelVo.setExcelFailVoList(failVoList);
        redissonHandler.setEX(PATIENT_INVENTORY_UPLOAD_FILE_FAIL_KEY, excelVo, RedissonHandler.ONE_HOUR);
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<Void> downFail(HttpServletResponse response, long authSaasUserId) {
        ExcelVo excelVo = redissonHandler.get(PATIENT_INVENTORY_UPLOAD_FILE_FAIL_KEY, ExcelVo.class);
        if (excelVo == null) {
            return null;
        }
        downFailList(response, excelVo, authSaasUserId);
        redissonHandler.del(PATIENT_INVENTORY_UPLOAD_FILE_FAIL_KEY);
        return null;
    }

    @Override
    public Response<Void> exportBedInfo(String province, String city, String hospitalName, String departmentsName, long authSaasUserId) {
        PatientInventoryDepartmentsBedSearch patientInventoryDepartmentsBedSearch = PatientInventoryDepartmentsBedSearch.builder()
                .province(province)
                .city(city)
                .hospitalName(hospitalName)
                .departmentsName(departmentsName)
                .current(1)
                .pageSize(2000)
                .order("create_time")
                .sortOrder(SortOrder.DESC)
                .build();
        Pair<Long, List<Long>> pair = adminPatientInventorySearchService.search(patientInventoryDepartmentsBedSearch);
        if (CollectionUtils.isEmpty(pair.getRight())) {
            return NewResponseUtil.makeSuccess();
        }

        List<PatientInventoryDepartmentsBedDo> patientInventoryDepartmentsBedDoList = patientInventoryDepartmentsBedDao.listByIds(pair.getRight());
        if (CollectionUtils.isEmpty(patientInventoryDepartmentsBedDoList)) {
            return NewResponseUtil.makeSuccess();
        }

        Map<Long, PatientInventoryDepartmentsBedDo> patientInventoryDepartmentsBedDoMap = patientInventoryDepartmentsBedDoList.stream().collect(Collectors.toMap(PatientInventoryDepartmentsBedDo::getId, Function.identity()));

        List<Long> bedIdList = patientInventoryDepartmentsBedDoList.stream().map(PatientInventoryDepartmentsBedDo::getId).collect(Collectors.toList());
        List<List<Long>> bedIdListPartition = Lists.partition(bedIdList, 500);
        List<PatientInventoryBedPatientRecordDo> patientInventoryBedPatientRecordDoList = Lists.newArrayList();
        for (List<Long> bedIds : bedIdListPartition) {
            List<PatientInventoryBedPatientRecordDo> bedPatientRecordDoList = patientInventoryBedPatientRecordDao.listByBedIds(bedIds);
            if (CollectionUtils.isNotEmpty(bedPatientRecordDoList)) {
                patientInventoryBedPatientRecordDoList.addAll(bedPatientRecordDoList);
            }
        }

        List<Integer> departmentsIdList = patientInventoryDepartmentsBedDoList.stream().map(PatientInventoryDepartmentsBedDo::getDepartmentsId).distinct().collect(Collectors.toList());
        List<List<Integer>> departmentsIdListPartition = Lists.partition(departmentsIdList, 500);
        List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOList = Lists.newArrayList();
        List<PatientInventoryPatientInfoDo> patientInventoryPatientInfoDoList = Lists.newArrayList();
        for (List<Integer> list : departmentsIdListPartition) {
            List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOs = departmentService.listByIds(list);
            if (CollectionUtils.isNotEmpty(hospitalBuildingDepartmentDOs)) {
                hospitalBuildingDepartmentDOList.addAll(hospitalBuildingDepartmentDOs);
            }
            List<PatientInventoryPatientInfoDo> patientInfoDoList = patientInventoryPatientInfoDao.listByDepartmentsIds(list);
            if (CollectionUtils.isNotEmpty(patientInfoDoList)) {
                patientInventoryPatientInfoDoList.addAll(patientInfoDoList);
            }
        }

        Map<Integer, HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOMap = hospitalBuildingDepartmentDOList.stream().collect(Collectors.toMap(HospitalBuildingDepartmentDO::getId, Function.identity()));
        Map<Long, PatientInventoryPatientInfoDo> patientInventoryPatientInfoDoMap = patientInventoryPatientInfoDoList.stream().collect(Collectors.toMap(PatientInventoryPatientInfoDo::getId, Function.identity()));
        Map<Long, String> maskPatientMobileMap = patientInventoryPatientInfoDoList.stream().collect(Collectors.toMap(PatientInventoryPatientInfoDo::getId, PatientInventoryPatientInfoDo::maskPatientMobile));

        Map<Integer, HospitalAreaBuildingDO> hospitalAreaBuildingDOMap = Maps.newHashMap();
        List<Long> buildingIdList = hospitalBuildingDepartmentDOList.stream().map(HospitalBuildingDepartmentDO::getBuildingId).distinct().map(Integer::longValue).collect(Collectors.toList());
        List<HospitalAreaBuildingDO> hospitalAreaBuildingDOList = buildingService.listByIds(buildingIdList);
        if (CollectionUtils.isNotEmpty(hospitalAreaBuildingDOList)) {
            hospitalAreaBuildingDOMap = hospitalAreaBuildingDOList.stream().collect(Collectors.toMap(HospitalAreaBuildingDO::getId, Function.identity()));
        }

        List<Long> patientInfoIdList = patientInventoryPatientInfoDoList.stream().map(PatientInventoryPatientInfoDo::getId).collect(Collectors.toList());
        List<List<Long>> patientInfoIdListPartition = Lists.partition(patientInfoIdList, 500);
        List<PatientInventoryPatientFollowUpRecordDo> patientInventoryPatientFollowUpRecordDoList = Lists.newArrayList();
        for (List<Long> list : patientInfoIdListPartition) {
            List<PatientInventoryPatientFollowUpRecordDo> patientFollowUpRecordDoList = patientInventoryPatientFollowUpRecordDao.listByPatientInfoIds(list, null);
            if (CollectionUtils.isNotEmpty(patientFollowUpRecordDoList)) {
                patientInventoryPatientFollowUpRecordDoList.addAll(patientFollowUpRecordDoList);
            }
        }
        Map<Long, List<PatientInventoryPatientFollowUpRecordDo>> patientInventoryPatientFollowUpRecordDoMap = patientInventoryPatientFollowUpRecordDoList.stream().collect(Collectors.groupingBy(PatientInventoryPatientFollowUpRecordDo::getPatientInfoId));

        List<Long> bedPatientRecordIdList = patientInventoryBedPatientRecordDoList.stream().map(PatientInventoryBedPatientRecordDo::getId).distinct().collect(Collectors.toList());
        List<List<Long>> bedPatientRecordIdListPartition = Lists.partition(bedPatientRecordIdList, 500);
        List<PatientInventoryRecordDo> patientInventoryRecordDoList = Lists.newArrayList();
        for (List<Long> list : bedPatientRecordIdListPartition) {
            List<PatientInventoryRecordDo> patientInventoryRecordDos = patientInventoryRecordDao.listByBedPatientRecordIds(list);
            if (CollectionUtils.isNotEmpty(patientInventoryRecordDos)) {
                patientInventoryRecordDoList.addAll(patientInventoryRecordDos);
            }
        }
        Map<Long, List<PatientInventoryRecordDo>> patientInventoryRecordDoMap = patientInventoryRecordDoList.stream().collect(Collectors.groupingBy(PatientInventoryRecordDo::getBedPatientRecordId));
        //获取顾问姓名
        Map<String, String> uniqueCodeMap = listMisName(patientInventoryRecordDoList);

        List<PatientInventoryBedPatientRecordExport> patientInventoryBedPatientRecordExportList = Lists.newArrayList();
        for (PatientInventoryBedPatientRecordDo patientInventoryBedPatientRecordDo : patientInventoryBedPatientRecordDoList) {
            PatientInventoryBedPatientRecordExport patientInventoryBedPatientRecordExport = new PatientInventoryBedPatientRecordExport();

            PatientInventoryDepartmentsBedDo patientInventoryDepartmentsBedDo = patientInventoryDepartmentsBedDoMap.get(patientInventoryBedPatientRecordDo.getBedId());
            if (patientInventoryDepartmentsBedDo == null) {
                continue;
            }

            patientInventoryBedPatientRecordExport.setProvince(patientInventoryDepartmentsBedDo.getProvince());
            patientInventoryBedPatientRecordExport.setCity(patientInventoryDepartmentsBedDo.getCity());
            patientInventoryBedPatientRecordExport.setHospitalName(patientInventoryDepartmentsBedDo.getHospitalName());
            patientInventoryBedPatientRecordExport.setBedName(patientInventoryDepartmentsBedDo.getBedName());

            HospitalBuildingDepartmentDO hospitalBuildingDepartmentDO = hospitalBuildingDepartmentDOMap.get(patientInventoryDepartmentsBedDo.getDepartmentsId());
            if (hospitalBuildingDepartmentDO != null) {
                patientInventoryBedPatientRecordExport.setDepartmentsName(hospitalBuildingDepartmentDO.getBuildingDepartment());
                patientInventoryBedPatientRecordExport.setBuildingFloor(hospitalBuildingDepartmentDO.getBuildingFloor());
                patientInventoryBedPatientRecordExport.setBuildingFloorArea(hospitalBuildingDepartmentDO.getBuildingFloorArea());


                HospitalAreaBuildingDO hospitalAreaBuildingDO = hospitalAreaBuildingDOMap.get(hospitalBuildingDepartmentDO.getBuildingId());
                if (hospitalAreaBuildingDO != null) {
                    patientInventoryBedPatientRecordExport.setBuildingName(hospitalAreaBuildingDO.getBuildingName());
                }
            }

            List<PatientInventoryRecordDo> patientInventoryRecordDos = patientInventoryRecordDoMap.get(patientInventoryBedPatientRecordDo.getId());
            if (CollectionUtils.isNotEmpty(patientInventoryRecordDos)) {
                PatientInventoryRecordDo patientInventoryRecordDo = patientInventoryRecordDos.get(0);
                patientInventoryBedPatientRecordExport.setBindingMisName(uniqueCodeMap.getOrDefault(patientInventoryRecordDo.getBindingUniqueCode(), ""));
                Optional<PatientInventoryRecordDo> optionalPatientInventoryRecordDo = patientInventoryRecordDos.stream().filter(v -> StringUtils.isNotEmpty(v.getBindingUniqueCodeLeader())).findFirst();
                optionalPatientInventoryRecordDo.ifPresent(inventoryRecordDo -> patientInventoryBedPatientRecordExport.setLeaderName(uniqueCodeMap.getOrDefault(inventoryRecordDo.getBindingUniqueCodeLeader(), "")));
            }

            PatientInventoryPatientInfoDo patientInventoryPatientInfoDo = patientInventoryPatientInfoDoMap.get(patientInventoryBedPatientRecordDo.getPatientInfoId());
            if (patientInventoryPatientInfoDo != null) {
                patientInventoryBedPatientRecordExport.setPatientName(patientInventoryPatientInfoDo.getPatientName());
                String patientMobile = maskPatientMobileMap.get(patientInventoryPatientInfoDo.getId());
                patientInventoryBedPatientRecordExport.setPatientMobile(patientMobile);
                patientInventoryBedPatientRecordExport.setPatientDiseaseName(patientInventoryPatientInfoDo.getPatientDisease());
                patientInventoryBedPatientRecordExport.setPatientCreateTime(DateUtil.formatDateTime(patientInventoryPatientInfoDo.getCreateTime()));
            }

            patientInventoryBedPatientRecordExport.setPatientStatus(PatientInventoryPatientType.getDescByCode(patientInventoryBedPatientRecordDo.getType()));

            if (patientInventoryBedPatientRecordDo.getType() == PatientInventoryPatientType.discharge_from_hospital.getCode()) {
                patientInventoryBedPatientRecordExport.setLeaveHospitalTime(DateUtil.formatDateTime(patientInventoryBedPatientRecordDo.getCreateTime()));
            }

            List<PatientInventoryPatientFollowUpRecordDo> patientInventoryPatientFollowUpRecordDos = patientInventoryPatientFollowUpRecordDoMap.get(patientInventoryBedPatientRecordDo.getPatientInfoId());
            if (CollectionUtils.isNotEmpty(patientInventoryPatientFollowUpRecordDos)) {

                StringBuilder intentionRecord = new StringBuilder();
                for (int i = 1; i <= patientInventoryPatientFollowUpRecordDos.size(); i++) {
                    PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo = patientInventoryPatientFollowUpRecordDos.get(i - 1);
                    intentionRecord.append(i).append(".（")
                            .append(getIntention(patientInventoryPatientFollowUpRecordDo))
                            .append("）").append(DateUtil.formatDateTime(patientInventoryPatientFollowUpRecordDo.getCreateTime()))
                            .append("-").append(patientInventoryPatientFollowUpRecordDo.getContent()).append("；下次跟进时间：")
                            .append(DateUtil.formatDateTime(patientInventoryPatientFollowUpRecordDo.getNextFollowUpTime()))
                            .append("\n");
                }
                patientInventoryBedPatientRecordExport.setIntentionRecord(intentionRecord.toString());

                List<PatientInventoryPatientFollowUpRecordDo> inventoryRecordList = patientInventoryPatientFollowUpRecordDos.stream().filter(v -> StringUtils.isNotEmpty(v.getInventoryRecord())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(inventoryRecordList)) {
                    StringBuilder inventoryRecord = new StringBuilder();
                    for (int i = 1; i <= inventoryRecordList.size(); i++) {
                        PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo = inventoryRecordList.get(i - 1);
                        inventoryRecord.append(i).append(".").append(DateUtil.formatDateTime(patientInventoryPatientFollowUpRecordDo.getInventoryTime()))
                                .append("-").append(patientInventoryPatientFollowUpRecordDo.getInventoryRecord())
                                .append("\n");
                    }
                    patientInventoryBedPatientRecordExport.setInventoryRecord(inventoryRecord.toString());
                }
            }
            patientInventoryBedPatientRecordExportList.add(patientInventoryBedPatientRecordExport);
        }

        List<PatientInventoryBedPatientRecordExportModel> patientInventoryBedPatientRecordExportModelList = patientInventoryBedPatientRecordExportList.stream().map(PatientInventoryBedPatientRecordExportModel::createByBPatientInventoryBedPatientRecordExportModel).collect(Collectors.toList());
        //构造表头
        List<String> headerList = Lists.newArrayList();
        headerList.add("province");
        headerList.add("city");
        headerList.add("hospitalName");
        headerList.add("buildingName");
        headerList.add("buildingFloor");
        headerList.add("buildingFloorArea");
        headerList.add("departmentsName");
        headerList.add("bindingMisName");
        headerList.add("bedName");
        headerList.add("patientName");
        headerList.add("patientMobile");
        headerList.add("patientDiseaseName");
        headerList.add("patientStatus");
        headerList.add("patientCreateTime");
        headerList.add("intentionRecord");
        headerList.add("leaveHospitalTime");
        headerList.add("leaderName");
        headerList.add("inventoryRecord");
        log.info("patientInventoryBedPatientRecordExportModelList size:{}", patientInventoryBedPatientRecordExportModelList.size());
        //调用导出
        CfReminderWord<Void> cfReminderWord = cfBdCrmExcelExpoetUtil.exportV3(authSaasUserId, patientInventoryBedPatientRecordExportModelList, headerList, PatientInventoryBedPatientRecordExportModel.class, "导出记录");
        return cfReminderWord.isSuccessFlag() ? NewResponseUtil.makeSuccess() : NewResponseUtil.makeFail(cfReminderWord.getMsgs());
    }

    @Override
    public Response<Integer> departmentBedOrder(int departmentId, long bedId, long order) {
        if (departmentId <= 0 || bedId <= 0 || order <= 0) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<PatientInventoryDepartmentsBedDo> patientInventoryDepartmentsBedDoList = patientInventoryDepartmentsBedDao.getByDepartmentsId(departmentId);
        if (CollectionUtils.isEmpty(patientInventoryDepartmentsBedDoList)) {
            return NewResponseUtil.makeFail("科室中不存在床位");
        }
        //从小到大排序
        Ordering<PatientInventoryDepartmentsBedDo> ordering = Ordering.natural().onResultOf(PatientInventoryDepartmentsBedDo::getBedOrder);
        patientInventoryDepartmentsBedDoList = patientInventoryDepartmentsBedDoList.stream().sorted(ordering).collect(Collectors.toList());
        Map<Long, Long> map = Maps.newHashMap();
        long currentOrder = 1L;
        // 先处理非移动的床位
        for (PatientInventoryDepartmentsBedDo bed : patientInventoryDepartmentsBedDoList) {
            // 跳过要移动的床位
            if (bed.getId() == bedId) {
                continue;
            }
            // 如果当前床位的排序等于要移动的床位的排序，那么当前床位的排序+1
            if (currentOrder == order) {
                currentOrder++;
            }
            map.put(bed.getId(), currentOrder++);
        }
        // 插入移动的床位
        map.put(bedId, order);
        int res = patientInventoryDepartmentsBedDao.updateOrderByMap(map);
        return NewResponseUtil.makeSuccess(res);
    }

    @Override
    public void washBedOrder() {
        List<PatientInventoryDepartmentsDo> patientInventoryDepartmentsDoList = GrowthtoolCrusorQuery.queryByCursor((id, limit) -> patientInventoryDepartmentsDao.listAll((long) id, limit), PatientInventoryDepartmentsDo::getId);
        if (CollectionUtils.isEmpty(patientInventoryDepartmentsDoList)) {
            return;
        }
        for (PatientInventoryDepartmentsDo patientInventoryDepartmentsDo : patientInventoryDepartmentsDoList) {
            List<PatientInventoryDepartmentsBedDo> patientInventoryDepartmentsBedDoList = patientInventoryDepartmentsBedDao.getByDepartmentsId(patientInventoryDepartmentsDo.getDepartmentsId());
            if (CollectionUtils.isEmpty(patientInventoryDepartmentsBedDoList)) {
                continue;
            }
            Map<Long, Long> map = Maps.newHashMap();
            for (PatientInventoryDepartmentsBedDo v : patientInventoryDepartmentsBedDoList) {
                map.put(v.getId(), v.getId());
            }
            patientInventoryDepartmentsBedDao.updateOrderByMap(map);
        }
    }

    /**
     * 清洗首次意向
     *
     * @param washBaseParam
     */
    @Override
    public void washInitialIntention(WashBaseParam washBaseParam) {
        // 通过id 分配查询跟进记录，再通过首次更新记录的意向和correlation_id，更新patient_inventory_record数据
        if (washBaseParam.getStartId() == null || washBaseParam.getEndId() == null || washBaseParam.getPageSize() == null) {
            log.error("washInitialIntention 参数错误: {}", washBaseParam);
            return;
        }

        long startId = washBaseParam.getStartId();
        long endId = washBaseParam.getEndId();
        int pageSize = washBaseParam.getPageSize();
        
        log.info("开始清洗首次意向数据，CorrelationId范围：[{} - {}]，每页数量：{}", startId, endId, pageSize);
        
        // 存储最后处理的ID，用于边界条件检查
        long lastProcessedId = startId - 1;
        
        while (startId <= endId) {
            // 计算当前批次的结束ID，确保不超过总范围
            long batchEndId = Math.min(startId + pageSize - 1, endId);
            
            // 查询特定范围内的首次跟进记录
            List<PatientInventoryPatientFollowUpRecordDo> recordDos = patientInventoryPatientFollowUpRecordDao.selectFirstByIdRangs(startId, batchEndId);
            
            // 更新开始ID为下一批次
            lastProcessedId = batchEndId;
            startId = batchEndId + 1;
            
            if (CollectionUtils.isEmpty(recordDos)) {
                log.debug("范围[{} - {}]内无首次跟进记录", lastProcessedId - pageSize + 1, lastProcessedId);
                continue;
            }
            
            log.debug("处理范围[{} - {}]内的{}条首次跟进记录", lastProcessedId - pageSize + 1, lastProcessedId, recordDos.size());
            
            for (PatientInventoryPatientFollowUpRecordDo recordDo : recordDos) {
                // 跳过无效的关联ID
                if (recordDo == null || recordDo.getCorrelationId() <= 0) {
                    continue;
                }
                
                // 更新数据
                int firstIntention = recordDo.getFirstIntention();
                int secondIntention = recordDo.getSecondIntention();

                // 根据关联ID更新patient_inventory_record表中的初始意向
                try {
                    int updated = patientInventoryRecordDao.updateInitialIntentionByCorrelationId(
                            recordDo.getCorrelationId(),
                            firstIntention,
                            secondIntention
                    );
                } catch (Exception e) {
                    log.error("更新patient_inventory_record初始意向失败, correlationId: {}, error: {}", recordDo.getCorrelationId(), e.getMessage(), e);
                }
            }
        }
        
        log.info("首次意向数据清洗完成，处理CorrelationId范围：[{} - {}]", washBaseParam.getStartId(), endId);
    }

    private Response<Void> downFailList(HttpServletResponse response, ExcelVo excelVo, long authSaasUserId) {
        DownloadExcel downloadExcel = new DownloadExcel();
        try (HSSFWorkbook hssfWorkbook = new HSSFWorkbook(); ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            HSSFCellStyle cellStyle = hssfWorkbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            int turn = 1;
            List<String> headList = Lists.newArrayList("科室ID", "床位名称", "失败原因");
            HSSFSheet sheet = hssfWorkbook.createSheet(excelVo.getOriginalFilename() + "批量添加床位失败原因" + (turn++));
            downloadExcel.makeRow(sheet, 0, headList, cellStyle);

            List<ExcelFailVo> failList = excelVo.getExcelFailVoList();
            for (int i = 0; i < failList.size(); i++) {
                List<String> list1 = Lists.newArrayList();
                ExcelFailVo excelFailVo = failList.get(i);
                list1.add(excelFailVo.getDepartmentsId());
                list1.add(excelFailVo.getBedName());
                list1.add(excelFailVo.getFailMsg());
                downloadExcel.makeRow(sheet, i + 1, list1, cellStyle);
            }
            String fileName = excelVo.getOriginalFilename() + "批量添加床位失败原因";
            hssfWorkbook.write(os);
            downloadExcel.downloadFile(os, response, fileName);
        } catch (Exception e) {
            log.error("downFailList download error!", e);
        }
        return null;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    static class ExcelVo {

        @ApiModelProperty("文件名称")
        private String originalFilename;

        private List<ExcelFailVo> excelFailVoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    static class ExcelFailVo {

        @ApiModelProperty("科室ID")
        private String departmentsId;

        @ApiModelProperty("床位名称")
        private String bedName;

        @ApiModelProperty("错误原因")
        private String failMsg;
    }

    private List<PatientInventoryDepartmentsBedVo> buildPatientInventoryDepartmentsBedVoList(List<Long> idList, boolean sort) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        List<PatientInventoryDepartmentsBedDo> patientInventoryDepartmentsBedDoList = patientInventoryDepartmentsBedDao.listByIds(idList);
        if (CollectionUtils.isEmpty(patientInventoryDepartmentsBedDoList)) {
            return Lists.newArrayList();
        }

        List<Integer> departmentsIdList = patientInventoryDepartmentsBedDoList.stream().map(PatientInventoryDepartmentsBedDo::getDepartmentsId).distinct().collect(Collectors.toList());
        List<PatientInventoryDepartmentsDo> patientInventoryDepartmentsDoList = patientInventoryDepartmentsDao.listByDepartmentsId(departmentsIdList);
        Map<Integer, PatientInventoryDepartmentsDo> departmentsIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(patientInventoryDepartmentsDoList)) {
            departmentsIdMap = patientInventoryDepartmentsDoList.stream().collect(Collectors.toMap(PatientInventoryDepartmentsDo::getDepartmentsId, Function.identity()));
        }

        List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOList = departmentService.listByIds(departmentsIdList);
        Map<Integer, HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOMap = Maps.newHashMap();
        Map<Integer, HospitalAreaBuildingDO> hospitalAreaBuildingDOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(hospitalBuildingDepartmentDOList)) {
            List<Long> buildingIdList = hospitalBuildingDepartmentDOList.stream().map(HospitalBuildingDepartmentDO::getBuildingId).distinct().map(Integer::longValue).collect(Collectors.toList());
            List<HospitalAreaBuildingDO> hospitalAreaBuildingDOList = buildingService.listByIds(buildingIdList);
            if (CollectionUtils.isNotEmpty(hospitalAreaBuildingDOList)) {
                hospitalAreaBuildingDOMap = hospitalAreaBuildingDOList.stream().collect(Collectors.toMap(HospitalAreaBuildingDO::getId, item -> item));
            }
            hospitalBuildingDepartmentDOMap = hospitalBuildingDepartmentDOList.stream().collect(Collectors.toMap(HospitalBuildingDepartmentDO::getId, Function.identity()));
        }


        List<Long> bedIds = patientInventoryDepartmentsBedDoList.stream().map(PatientInventoryDepartmentsBedDo::getId).collect(Collectors.toList());
        List<PatientInventoryPatientInfoDo> patientInventoryPatientInfoDoList = patientInventoryPatientInfoDao.listByBedIds(bedIds);
        Map<Long, PatientInventoryPatientInfoDo> patientInventoryPatientInfoDoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(patientInventoryPatientInfoDoList)) {
            patientInventoryPatientInfoDoMap = patientInventoryPatientInfoDoList.stream().collect(Collectors.toMap(PatientInventoryPatientInfoDo::getBedId, Function.identity()));
        }

        List<PatientInventoryDepartmentsBedVo> result = Lists.newArrayList();
        for (PatientInventoryDepartmentsBedDo patientInventoryDepartmentsBedDo : patientInventoryDepartmentsBedDoList) {
            PatientInventoryDepartmentsBedVo patientInventoryDepartmentsBedVo = new PatientInventoryDepartmentsBedVo();
            patientInventoryDepartmentsBedVo.setId(patientInventoryDepartmentsBedDo.getId());
            patientInventoryDepartmentsBedVo.setProvince(patientInventoryDepartmentsBedDo.getProvince());
            patientInventoryDepartmentsBedVo.setCity(patientInventoryDepartmentsBedDo.getCity());
            patientInventoryDepartmentsBedVo.setHospitalName(patientInventoryDepartmentsBedDo.getHospitalName());
            patientInventoryDepartmentsBedVo.setDepartmentsId(patientInventoryDepartmentsBedDo.getDepartmentsId());
            patientInventoryDepartmentsBedVo.setDepartmentsName(patientInventoryDepartmentsBedDo.getDepartmentsName());
            patientInventoryDepartmentsBedVo.setBedName(patientInventoryDepartmentsBedDo.getBedName());
            patientInventoryDepartmentsBedVo.setValid(patientInventoryDepartmentsBedDo.getValid());
            patientInventoryDepartmentsBedVo.setOperatorUserName(patientInventoryDepartmentsBedDo.getOperatorUserName());
            patientInventoryDepartmentsBedVo.setCreateTime(DateUtil.formatDateTime(patientInventoryDepartmentsBedDo.getCreateTime()));
            patientInventoryDepartmentsBedVo.setValidDesc(PatientInventoryDepartmentsBedValidEnum.getDescByCode(patientInventoryDepartmentsBedDo.getValid()));
            patientInventoryDepartmentsBedVo.setBedOrder(patientInventoryDepartmentsBedDo.getBedOrder());
            PatientInventoryDepartmentsDo patientInventoryDepartmentsDo = departmentsIdMap.get(patientInventoryDepartmentsBedDo.getDepartmentsId());
            if (patientInventoryDepartmentsDo != null) {
                patientInventoryDepartmentsBedVo.setBindingMisName(patientInventoryDepartmentsDo.getBindingMisName());
            }

            HospitalBuildingDepartmentDO hospitalBuildingDepartmentDO = hospitalBuildingDepartmentDOMap.get(patientInventoryDepartmentsBedDo.getDepartmentsId());
            if (hospitalBuildingDepartmentDO != null) {
                patientInventoryDepartmentsBedVo.setBuildingFloor(hospitalBuildingDepartmentDO.getBuildingFloor());
                patientInventoryDepartmentsBedVo.setBuildingFloorArea(hospitalBuildingDepartmentDO.getBuildingFloorArea());

                HospitalAreaBuildingDO hospitalAreaBuildingDO = hospitalAreaBuildingDOMap.get(hospitalBuildingDepartmentDO.getBuildingId());
                if (hospitalAreaBuildingDO != null) {
                    patientInventoryDepartmentsBedVo.setBuildingName(hospitalAreaBuildingDO.getBuildingName());
                    patientInventoryDepartmentsBedVo.setBuildingArea(hospitalAreaBuildingDO.getBuildingFloorArea());
                }
            }

            PatientInventoryPatientInfoDo patientInventoryPatientInfoDo = patientInventoryPatientInfoDoMap.get(patientInventoryDepartmentsBedDo.getId());
            if (patientInventoryPatientInfoDo != null) {
                patientInventoryDepartmentsBedVo.setPatientName(patientInventoryPatientInfoDo.getPatientName());
                patientInventoryDepartmentsBedVo.setCheckIn(patientInventoryPatientInfoDo.getBedId() > 0);
                NumberMaskVo numberMaskVo = maskUtil.buildByEncryptPhone(patientInventoryPatientInfoDo.getPatientMobile());
                patientInventoryDepartmentsBedVo.setNumberMaskVo(numberMaskVo);
                patientInventoryDepartmentsBedVo.setPatientDiseaseName(patientInventoryPatientInfoDo.getPatientDisease());
            }
            result.add(patientInventoryDepartmentsBedVo);
        }
        if (sort) {
            //按照床位排序
            Ordering<PatientInventoryDepartmentsBedVo> ordering = Ordering.natural().onResultOf(PatientInventoryDepartmentsBedVo::getBedOrder);
            result = result.stream().sorted(ordering).collect(Collectors.toList());
        }
        return result;
    }

    private List<DepartmentsLabelVo> getDepartmentsLabelVoList(String labels) {
        if (StringUtils.isEmpty(labels)) {
            return Lists.newArrayList();
        }
        List<String> labelList = Splitter.on(",").splitToList(labels);
        List<DepartmentsLabelVo> result = Lists.newArrayList();
        for (String label : labelList) {
            DepartmentsLabelEnum departmentsLabelEnum = DepartmentsLabelEnum.getByCode(Integer.parseInt(label));
            if (departmentsLabelEnum == null) {
                continue;
            }
            DepartmentsLabelVo departmentsLabelVo = new DepartmentsLabelVo();
            departmentsLabelVo.setDepartmentsLabel(departmentsLabelEnum.getCode());
            departmentsLabelVo.setDepartmentsLabelDesc(departmentsLabelEnum.getDesc());
            result.add(departmentsLabelVo);
        }
        return result;
    }

    private String getIntention(PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo) {
        PatientInventoryFirstIntentionType patientInventoryFirstIntentionType = PatientInventoryFirstIntentionType.getByCode(patientInventoryPatientFollowUpRecordDo.getFirstIntention());
        if (patientInventoryFirstIntentionType == null) {
            return "";
        }
        PatientInventoryFirstIntentionType.PatientInventorySecondIntentionType patientInventorySecondIntentionType =
                PatientInventoryFirstIntentionType.PatientInventorySecondIntentionType.getByCode(patientInventoryPatientFollowUpRecordDo.getSecondIntention());
        if (patientInventorySecondIntentionType == null) {
            return patientInventoryFirstIntentionType.getDesc();
        }
        return patientInventoryFirstIntentionType.getDesc() + "-" + patientInventorySecondIntentionType.getDesc();
    }

    private Map<String, String> listMisName(List<PatientInventoryRecordDo> patientInventoryRecordDoList) {
        if (CollectionUtils.isEmpty(patientInventoryRecordDoList)) {
            return Maps.newHashMap();
        }
        Set<String> bindingUniqueCodeSet = patientInventoryRecordDoList.stream().map(PatientInventoryRecordDo::getBindingUniqueCode).collect(Collectors.toSet());
        Set<String> bindingUniqueCodeLeaderSet = patientInventoryRecordDoList.stream().map(PatientInventoryRecordDo::getBindingUniqueCodeLeader).collect(Collectors.toSet());
        Set<String> uniqueCodeSet = Sets.newHashSet();
        uniqueCodeSet.addAll(bindingUniqueCodeSet);
        uniqueCodeSet.addAll(bindingUniqueCodeLeaderSet);

        List<CrowdfundingVolunteer> crowdfundingVolunteerList = cfVolunteerService.getCfVolunteerDOByUniqueCodes(Lists.newArrayList(uniqueCodeSet));
        if (CollectionUtils.isEmpty(crowdfundingVolunteerList)) {
            return Maps.newHashMap();
        }
        return crowdfundingVolunteerList.stream().collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, CrowdfundingVolunteer::getVolunteerName));
    }

}
