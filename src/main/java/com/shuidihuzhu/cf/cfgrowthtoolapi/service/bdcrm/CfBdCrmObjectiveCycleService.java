package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveCycle;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdCrmObjectiveConfigScheduleVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdCrmObjectiveValueConfigVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.SeaCfBdCrmObjectiveVO;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/2/23 上午10:39
 */
public interface CfBdCrmObjectiveCycleService{


    OpResult<Long> insertSelective(CfBdCrmObjectiveCycle record);

    OpResult checkCanSaveOrUpdate(CfBdCrmObjectiveCycle record);

    CfBdCrmObjectiveCycle selectByPrimaryKey(Long id);

    OpResult<Long> updateByPrimaryKeySelective(CfBdCrmObjectiveCycle record);

    CommonResultModel<SeaCfBdCrmObjectiveVO> listObjectiveCyclePage(String currentDate, Integer objectiveType, Integer pageNo, Integer pageSize);

    CfBdCrmObjectiveCycle getCurrentObjectiveCycleByObjectiveType(String currentDate, Integer objectiveType);

    List<CfBdCrmObjectiveCycle> getCurrentObjectiveCycle(String currentDate);

    List<CfBdCrmObjectiveCycle> listObjectiveCycleWithIds(List<Long> objectiveCycleIdList);

    List<CfBdCrmObjectiveCycle> listUnStartByCurrentDate(String currentDateStr);

    List<CfBdCrmObjectiveCycle> listCurrentDateStartCycle(String currentDateStr);

    List<CfBdCrmObjectiveCycle> listCurrentDateWithBeforeCycle(String currentDateStr);

    List<CfBdCrmObjectiveCycle> listCurrentDateWithAfterCycle(String currentDateStr);

    List<CfBdCrmObjectiveConfigScheduleVO> getCurrentConfigSchedule(Long id);

    List<CfBdCrmObjectiveValueConfigVO> listCurrentConfigIndicatorConfig(Long cycleId);

    CfBdCrmObjectiveCycle getByTypeStartTimeOrEndTime(Integer objectiveType, String startTime, String endTime);
}
