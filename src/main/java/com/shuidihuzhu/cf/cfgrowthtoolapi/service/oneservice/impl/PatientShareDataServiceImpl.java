package com.shuidihuzhu.cf.cfgrowthtoolapi.service.oneservice.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientpt.PatientShareDataAll;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.oneservice.PatientShareDataService;
import com.shuidihuzhu.cf.dao.oneservice.PatientShareDataDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/2/6 11:23
 */
@Service
public class PatientShareDataServiceImpl implements PatientShareDataService {

    @Resource
    private PatientShareDataDao patientShareDataDao;


    @Override
    public List<PatientShareDataAll> getPatientShareData(String dt, String uniqueCode) {
        if (StringUtils.isBlank(dt) || StringUtils.isBlank(uniqueCode)) {
            return Lists.newArrayList();
        }
        return patientShareDataDao.getPatientShareData(dt, uniqueCode);
    }
}
