package com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.wx.CfWxOfficialAccountBaseDataDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-03-26
 */
public interface IWxOfficialAccountService {

    /**
     * thirdType is null or thirdType <= 0 会在执行时设置为-1
     * thirdType = -1 表示取所有数据
     * 从缓存中读取所有数据
     * @return
     */
    Map<Integer, List<CfWxOfficialAccountBaseDataDO>> getAllThirdTypeFromCache(Integer thirdType);

    /**
     * 更新thumbMediaId
     * @param thumbMediaId
     * @param id
     * @return
     */
    int updateThumbMediaIdById(String thumbMediaId, Long id);

    /**
     * 更新tagId
     * @return
     */
    int updateTagIdById(Integer tagId, Long id);

    /**
     * 从db获取数据
     * @param thirdType
     * @return
     */
    Map<Integer, List<CfWxOfficialAccountBaseDataDO>> listWxOfficialAccountBaseDataDO(Integer thirdType);
}
