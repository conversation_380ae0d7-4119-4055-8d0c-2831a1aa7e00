package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdDailyObjectiveReachDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.BdDailyObjectiveReachService;
import com.shuidihuzhu.cf.dao.snapshot.BdDailyObjectiveReachDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 日目标完成情况(BdDailyObjectiveReach)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-02 15:00:08
 */
@Service("bdDailyObjectiveReachService")
public class BdDailyObjectiveReachServiceImpl implements BdDailyObjectiveReachService {
   
    @Resource
    private BdDailyObjectiveReachDao bdDailyObjectiveReachDao;

    @Override
    public BdDailyObjectiveReachDO queryById(long id) {
        return bdDailyObjectiveReachDao.queryById(id);
    }

    @Override
    public void addOrUpdate(BdDailyObjectiveReachDO bdDailyObjectiveReach) {
        if (bdDailyObjectiveReach.getId() > 0) {
            bdDailyObjectiveReachDao.updateReachValue(bdDailyObjectiveReach);
        } else {
            bdDailyObjectiveReachDao.insert(bdDailyObjectiveReach);
        }
    }

    @Override
    public BdDailyObjectiveReachDO getByTargetTypeAndUniqueCode(String dateKey, String uniqueCode, int targetType) {
        return bdDailyObjectiveReachDao.getByTargetTypeAndUniqueCode(dateKey, uniqueCode, targetType);
    }

    @Override
    public BdDailyObjectiveReachDO getByTargetTypeAndOrgId(String dateKey, long orgId, int targetType) {
        return bdDailyObjectiveReachDao.getByTargetTypeAndOrgId(dateKey, orgId, targetType);
    }

    @Override
    public long sumReachByOrgIds(String dateKey, List<Long> orgIds, int targetType) {
        return Optional.ofNullable(bdDailyObjectiveReachDao.sumReachByOrgIds(dateKey, orgIds, targetType)).orElse(0L);
    }

    @Override
    public List<BdDailyObjectiveReachDO> listMemberByOrgId(String dateKey, long orgId, int targetType) {
        return bdDailyObjectiveReachDao.listMemberByOrgId(dateKey, orgId, targetType);
    }

    @Override
    public boolean deleteById(long id) {
        return bdDailyObjectiveReachDao.deleteById(id) > 0;
    }
}
