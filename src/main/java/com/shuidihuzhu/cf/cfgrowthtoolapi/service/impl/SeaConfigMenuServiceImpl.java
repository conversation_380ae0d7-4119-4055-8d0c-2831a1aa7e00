package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ConfigCarteEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRoleCarteConfigModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRoleConfigModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRoleConfigVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRoleJurisdictionConfigModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.SeaConfigMenuService;
import com.shuidihuzhu.cf.dao.SeaConfigMenuDao;
import com.shuidihuzhu.cf.dao.SeaRoleJurisdictionConfigDao;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class SeaConfigMenuServiceImpl implements SeaConfigMenuService {

    @Autowired
    private SeaConfigMenuDao seaConfigMenuDao;

    @Autowired
    private SeaRoleJurisdictionConfigDao seaRoleJurisdictionConfigDao;


    @Override
    public List<CfRoleCarteConfigModel> getGroupList() {
        //查询分组明细
        return seaConfigMenuDao.getGroupList();
    }

    @Override
    public OpResult<Long> saveOrUpdateGroup(CfRoleCarteConfigModel roleGroupModel) {

        OpResult<Long> opResult;
        if (roleGroupModel.getId() != null && roleGroupModel.getId() != 0) {
            opResult = updateRoleGroup(roleGroupModel);
        } else {
            opResult = saveRoleGroup(roleGroupModel);
        }
        return opResult;
    }

    /**
     * 新建分组
     *
     * @param roleGroupModel
     * @return
     */
    private OpResult<Long> saveRoleGroup(CfRoleCarteConfigModel roleGroupModel) {

        OpResult checkResult = isRepetitionCheck(roleGroupModel);
        if (checkResult.isFail()) {
            log.info("checkRoleGroup :{}", JSON.toJSONString(checkResult));
            return checkResult;
        }

        roleGroupModel.setCarteType(0);
        seaConfigMenuDao.insert(roleGroupModel);
        List<CfRoleJurisdictionConfigModel> roleJurisdictionModel = buildJurisdictionModel(roleGroupModel);
        seaRoleJurisdictionConfigDao.batchInsert(roleJurisdictionModel);
        return OpResult.createSucResult(roleGroupModel.getId());
    }

    /**
     * 对唯一标识进行校验
     *
     * @param roleModel
     * @return
     */
    private OpResult isRepetitionCheck(CfRoleCarteConfigModel roleModel) {
        List<CfRoleCarteConfigModel> roleCarteModels = seaConfigMenuDao.selectAllList();
        Set<String> carteUniqueCodeSet = roleCarteModels.stream().map(CfRoleCarteConfigModel::getCarteUniqueCode).collect(Collectors.toSet());
        //Set<String> carteNameSet = roleCarteModels.stream().map(CfRoleCarteConfigModel::getCarteName).collect(Collectors.toSet());
        if (carteUniqueCodeSet.contains(roleModel.getCarteUniqueCode())) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ROLECARTECONFIG_UNIQUE_CODE_REPETITION);
        }
//        if (carteNameSet.contains(roleModel.getCarteName())) {
//            return OpResult.createFailResult(CfGrowthtoolErrorCode.ROLECARTECONFIG_NAME_REPETITION);
//        }
        return OpResult.createSucResult();

    }

    /**
     * 更新分组
     *
     * @param roleGroupModel
     * @return
     */
    private OpResult<Long> updateRoleGroup(CfRoleCarteConfigModel roleGroupModel) {
        CfRoleCarteConfigModel cfRoleCarteConfigModel = seaConfigMenuDao.selectWithId(roleGroupModel.getId());
        if (cfRoleCarteConfigModel == null) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ROLECARTECONFIG_NOT_FOUND_GROUP);
        }
        //不允许求改唯一标识
        if(!roleGroupModel.getCarteUniqueCode().equals(cfRoleCarteConfigModel.getCarteUniqueCode())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.DONOTMODIFY_UNIQUE_CODE);
        }
        seaConfigMenuDao.updateWithId(roleGroupModel);
        List<CfRoleJurisdictionConfigModel> roleJurisdictionModel = buildJurisdictionModel(roleGroupModel);
        seaRoleJurisdictionConfigDao.batchUpdateName(roleJurisdictionModel);
        return OpResult.createSucResult(roleGroupModel.getGroupId());
    }
    @Override
    public List<CfRoleCarteConfigModel> getCarteList(Long groupId) {
        //工作台所属模块顺序 > 展示顺序 > id倒序
        List<CfRoleCarteConfigModel> roleCarteConfigModelList = seaConfigMenuDao.getGroupListWithGroupId(groupId);
        if (CollectionUtils.isEmpty(roleCarteConfigModelList)) {
            return roleCarteConfigModelList;
        }
        if (roleCarteConfigModelList.get(0).getWorkBenchEnum() == null) {
            return roleCarteConfigModelList;
        }
        //工作台分组
        //根据工作台所属模块分组排序
        TreeMap<Integer, List<CfRoleCarteConfigModel>> workBenchMapRoleModel = roleCarteConfigModelList.stream().collect(Collectors.groupingBy(CfRoleCarteConfigModel::getWorkBenchEnum, TreeMap::new, Collectors.toList()));
        //根据展示顺序排序
        List<CfRoleCarteConfigModel> configModelList = new ArrayList<>();
        for (Map.Entry<Integer, List<CfRoleCarteConfigModel>> entry : workBenchMapRoleModel.entrySet()) {
            List<CfRoleCarteConfigModel> roleCarteModels = entry.getValue();
            List<CfRoleCarteConfigModel> sortRoleCarteModel = roleCarteModels.stream().sorted(Comparator.comparing(CfRoleCarteConfigModel::getCarteOrder)).collect(Collectors.toList());
            Map<Integer, List<CfRoleCarteConfigModel>> sortRoleMap = new HashMap<>();
            sortRoleMap.put(entry.getKey(), sortRoleCarteModel);
            List<List<CfRoleCarteConfigModel>> carteConfigList = new ArrayList<>(sortRoleMap.values());
            configModelList.addAll(carteConfigList.stream().flatMap(Collection::stream).collect(Collectors.toList()));

        }
        return configModelList;
    }

    @Override
    public OpResult<Long> saveOrUpdateCarteInfo(CfRoleCarteConfigModel roleCarteModel) {

        OpResult checkLegalResult = isLegalcheck(roleCarteModel);
        if (checkLegalResult.isFail()){
            log.info("updateRoleCarte checkLegalResult:{}",JSON.toJSONString(checkLegalResult));
            return checkLegalResult;
        }

       OpResult<Long> opResult;
       if (roleCarteModel.getId() == null){
           opResult =  saveRoleCarte(roleCarteModel);
       }else {
           opResult =  updateRoleCarte(roleCarteModel);
       }
        return opResult;
    }

    /**
     * 更新菜单
     * @param roleCarteModel
     * @return
     */
    private OpResult<Long> updateRoleCarte(CfRoleCarteConfigModel roleCarteModel) {
        CfRoleCarteConfigModel cfRoleCarteConfigModel = seaConfigMenuDao.selectWithId(roleCarteModel.getId());
        if (cfRoleCarteConfigModel == null) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ROLECARTECONFIG_NOT_FOUND_GROUP);
        }
        //不允许求改唯一标识
        if(!roleCarteModel.getCarteUniqueCode().equals(cfRoleCarteConfigModel.getCarteUniqueCode())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.DONOTMODIFY_UNIQUE_CODE);
        }
        roleCarteModel.setCarteType(1);
        seaConfigMenuDao.updateWithId(roleCarteModel);
        List<CfRoleJurisdictionConfigModel> roleJurisdictionModel = buildJurisdictionModel(roleCarteModel);
        seaRoleJurisdictionConfigDao.batchUpdateName(roleJurisdictionModel);
       return OpResult.createSucResult(cfRoleCarteConfigModel.getId());
    }

    /**
     * 构建角色权限model
     * @param roleCarteModel
     * @return
     */
    private List<CfRoleJurisdictionConfigModel> buildJurisdictionModel(CfRoleCarteConfigModel roleCarteModel) {
        List<CfRoleJurisdictionConfigModel> configModelList = new ArrayList<>();
        List<Integer> levelList = CrowdfundingVolunteerEnum.allLevel;
        for (Integer level : levelList) {
            CfRoleJurisdictionConfigModel configModel = new CfRoleJurisdictionConfigModel();

            configModel.setCarteId(roleCarteModel.getId());
            configModel.setCarteType(roleCarteModel.getCarteType());
            configModel.setCarteName(roleCarteModel.getCarteName());
            configModel.setCarteUniqueCode(roleCarteModel.getCarteUniqueCode());
            configModel.setGroupId(Optional.of(roleCarteModel).map(CfRoleCarteConfigModel::getGroupId).orElse(-1L));
            configModel.setIsJudge(ConfigCarteEnum.NOPERMISSION.getCode());
            configModel.setVolunteerLevel(level);
            configModelList.add(configModel);
        }
        return configModelList;
    }


    /**
     * 新建菜单
     * @param roleCarteModel
     * @return
     */
    private OpResult<Long> saveRoleCarte(CfRoleCarteConfigModel roleCarteModel) {
        OpResult checkRepetitionResult = isRepetitionCheck(roleCarteModel);
        if (checkRepetitionResult.isFail()){
            log.info("updateRoleCarte checkRepetitionResult:{}",JSON.toJSONString(checkRepetitionResult));
            return checkRepetitionResult;
        }
        roleCarteModel.setCarteType(1);
        seaConfigMenuDao.insert(roleCarteModel);
        List<CfRoleJurisdictionConfigModel> roleJurisdictionModel = buildJurisdictionModel(roleCarteModel);
        seaRoleJurisdictionConfigDao.batchInsert(roleJurisdictionModel);
        return OpResult.createSucResult(roleCarteModel.getId());
    }



    /**
     * 组织选择是否合法
     * @param roleCarteModel
     * @return
     */
    private OpResult isLegalcheck(CfRoleCarteConfigModel roleCarteModel) {
           if (roleCarteModel.getIsVisibleByCity() == 1){
               if (StringUtils.isBlank(roleCarteModel.getVisibleCityName()) ||StringUtils.isBlank(roleCarteModel.getVisibleCityId())){
                   return OpResult.createFailResult(CfGrowthtoolErrorCode.ROLECARTECONFIG_NOT_FOUND_ORG);
               }
           }
           return OpResult.createSucResult();
    }


    @Override
    public CfRoleConfigVo getRoleJurisdictionInfo(Integer level) {
        List<CfRoleConfigModel> configModelList = new ArrayList<>();
        List<CfRoleJurisdictionConfigModel> roleJurisdictionConfigModels = seaRoleJurisdictionConfigDao.selectWithLevel(level);
        TreeMap<Long, List<CfRoleJurisdictionConfigModel>> groupIdMapRoleModel = new TreeMap<>();
        for (CfRoleJurisdictionConfigModel configModel:roleJurisdictionConfigModels){
            Long groupId=0l;
            //组
            if (configModel.getCarteType() == 0){
                groupId = configModel.getCarteId();
            }
            //菜单
            if (configModel.getCarteType() == 1){
                groupId = configModel.getGroupId();
            }
            if(0 == groupId){
                continue;
            }
            if (groupIdMapRoleModel.containsKey(groupId)){
                groupIdMapRoleModel.get(groupId).add(configModel);
            }else {
                groupIdMapRoleModel.put(groupId,Lists.newArrayList(configModel));
            }
        }
        for (Map.Entry<Long,List<CfRoleJurisdictionConfigModel>> entry:groupIdMapRoleModel.entrySet()){
            //菜单
            List<CfRoleJurisdictionConfigModel> carteConfigModels = new ArrayList<>(entry.getValue());
            CfRoleConfigModel cfRoleConfigModel = new CfRoleConfigModel();
            List<CfRoleJurisdictionConfigModel> groupModel =  carteConfigModels.stream().filter(item ->item.getCarteId().equals(entry.getKey())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(groupModel)){
                log.error("groupModel:{}",groupModel);
                break;
            }
            cfRoleConfigModel.setId(groupModel.get(0).getId());
            cfRoleConfigModel.setCarteName(groupModel.get(0).getCarteName());
            cfRoleConfigModel.setCarteType(groupModel.get(0).getCarteType());
            cfRoleConfigModel.setCarteUniqueCode(groupModel.get(0).getCarteUniqueCode());
            cfRoleConfigModel.setCarteId(groupModel.get(0).getCarteId());
            cfRoleConfigModel.setIsJudge(groupModel.get(0).getIsJudge());
            cfRoleConfigModel.setCarteConfigModels(carteConfigModels.stream().filter(item -> !item.getCarteId().equals(entry.getKey())).collect(Collectors.toList()));
            configModelList.add(cfRoleConfigModel);
        }
        CfRoleConfigVo cfRoleConfigVo = new CfRoleConfigVo();
        cfRoleConfigVo.setConfigModelList(configModelList);
        return cfRoleConfigVo;
    }

    @Override
    public OpResult<Long> updateRoleJurisdictionInfo(String[] carteIds,Integer level) {
        List<String> carteIdList = new ArrayList<String>(carteIds.length);
        Collections.addAll(carteIdList, carteIds);
        //更改前，需要将当前角色重置
        seaRoleJurisdictionConfigDao.updateAllJudge(ConfigCarteEnum.NOPERMISSION.getCode(),level);
        int changeResult = seaRoleJurisdictionConfigDao.updateJudgeStatuesWithIds(carteIdList,ConfigCarteEnum.HAVEPERMISSION.getCode());
        if (changeResult == carteIds.length){
            return OpResult.createSucResult();
        }
        return OpResult.createFailResult(CfGrowthtoolErrorCode.PERMISSION_SAVE_FAILED);

    }
}
