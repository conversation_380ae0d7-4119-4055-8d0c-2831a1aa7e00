package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory.strategy;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.common.web.util.DateUtil;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/2/20  17:16
 */
public abstract class AbstractImportantMarksStrategy implements ImportantMarksStrategyInterface {

    public boolean isManagement(int level) {
        List<Integer> patientInventoryRoles = CrowdfundingVolunteerEnum.patientInventoryRoles;
        return patientInventoryRoles.contains(level);
    }

    public boolean isCommon(int level) {
        Set<Integer> patientInventoryRoles = Sets.newHashSet();
        patientInventoryRoles.add(CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel());
        patientInventoryRoles.add(CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_COMMON.getLevel());
        return patientInventoryRoles.contains(level);
    }

    public String chooseDay(String day, int addDay) {
        if (addDay == 0) {
            return DateUtil.formatDate(day);
        }
        Date date = DateUtil.addDay(day, addDay);
        return DateUtil.formatDate(date);
    }

    public String chooseDay(Date day, int addDay) {
        if (addDay == 0) {
            return DateUtil.formatDate(day);
        }
        Date date = DateUtil.addDay(day, addDay);
        return DateUtil.formatDate(date);
    }

}
