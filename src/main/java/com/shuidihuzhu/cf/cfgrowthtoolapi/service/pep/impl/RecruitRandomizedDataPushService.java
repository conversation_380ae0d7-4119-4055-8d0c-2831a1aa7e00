package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiRecruitClewBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiRecruitClewBaseDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 随机成功推送招募数据
 * @author: fengxuan
 * @create 2023-03-20 19:39
 **/
@Service
public class RecruitRandomizedDataPushService extends AbstractRecruitDataPushService {

    @Autowired
    private ICfKpiRecruitClewBaseDataService recruitClewBaseDataService;


    @Override
    List<CfKpiRecruitClewBaseDataDO> listRecruitData(String dayKey, String startTime, String endTime) {
        return recruitClewBaseDataService.listRandomizedByDateKey(dayKey, startTime, endTime);
    }

    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.recruit_success_data;
    }
}
