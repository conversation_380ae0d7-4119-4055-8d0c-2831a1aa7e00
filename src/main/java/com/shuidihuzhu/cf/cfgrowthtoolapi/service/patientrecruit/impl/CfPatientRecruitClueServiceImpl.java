package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientrecruit.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.patientrecruit.CfPatientRecruitClueInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfPatientRecruitEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.PepRealTimeLeaderInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitClueOrgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.ValidClewStatus;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DateQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.recruit.RecruitClewSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientrecruit.ICfPatientRecruitClueService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.dao.patientrecruit.CfPatientRecruitClueInfoDao;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.pr.patient.model.dto.clew.outside.PatientRequestParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-05-12
 */

@Slf4j
@Service
public class CfPatientRecruitClueServiceImpl implements ICfPatientRecruitClueService {

    @Autowired
    private CfPatientRecruitClueInfoDao cfPatientRecruitClueInfoDao;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;

    @Autowired
    private ICfVolunteerService volunteerService;


    @Override
    public CfPatientRecruitClueInfoDo addClew(CfPatientRecruitClueInfoDo cfPatientRecruitClueInfoDo) {
        cfPatientRecruitClueInfoDo.setEncryptPhone(oldShuidiCipher.aesEncrypt(cfPatientRecruitClueInfoDo.getEncryptPhone()));
        //设置对应的组织id
        ICrmMemberInfoService.BdCrmOrganizationDOWithChain rightBdCaseOrg = crmMemberInfoService.getRightBdCaseOrg(cfPatientRecruitClueInfoDo.getUniqueCode());
        if (rightBdCaseOrg != null) {
            cfPatientRecruitClueInfoDo.setOrgId(rightBdCaseOrg.getId());
        }
        cfPatientRecruitClueInfoDao.addClew(cfPatientRecruitClueInfoDo);
        return cfPatientRecruitClueInfoDo;
    }

    @Override
    public CfPatientRecruitClueInfoDo addClewByMaterialVo(PreposeMaterialModel.MaterialInfoVo materialInfoVo,
                                                          CrowdfundingVolunteer crowdfundingVolunteer,
                                                          ClewCrowdfundingReportRelation relation) {
        CfPatientRecruitClueInfoDo clueInfoDo = cfPatientRecruitClueInfoDao.getByMaterialIdNoMatterValid(relation.getPreposeMaterialId());
        if (clueInfoDo != null) {
            return clueInfoDo;
        }
        //找到对应的顾问
        int clewResource = CfPatientRecruitEnums.RecruitClewResourceEnum.before_prepose.getCode();
        if (Objects.equals(relation.getLaunchType(), ClewCrowdfundingReportRelation.LaunchTypeEnum.AFTER.getType())) {
            clewResource = CfPatientRecruitEnums.RecruitClewResourceEnum.after_prepose.getCode();
        }
        PatientRequestParam clueInfo = createPatientRequest(materialInfoVo);
        CfPatientRecruitClueInfoDo newClueInfoDo = CfPatientRecruitClueInfoDo.build(crowdfundingVolunteer, clueInfo, CfPatientRecruitEnums.UploadRecruitStatusEnum.PREPOSE_UN_LOAD);
        newClueInfoDo.setPreposeMaterialId(relation.getPreposeMaterialId());
        newClueInfoDo.setClewResource(clewResource);
        return addClew(newClueInfoDo);
    }


    public PatientRequestParam createPatientRequest(PreposeMaterialModel.MaterialInfoVo materialInfoVo) {
        PatientRequestParam patientRequestParam = new PatientRequestParam();
        String patientIdCard = materialInfoVo.getPatientIdCard();
        if (materialInfoVo.getPatientIdCardType() == 1 && StringUtils.isNotBlank(patientIdCard)) {
            int genderByIdCard = IdcardUtil.getGenderByIdCard(patientIdCard);
            DateTime birthDate = IdcardUtil.getBirthDate(patientIdCard);
            patientRequestParam.setBirthday(birthDate.toString(DatePattern.NORM_DATE_PATTERN));
            //2:女 1:男
            patientRequestParam.setSex(genderByIdCard == 0 ? 2 : 1);
        }
        patientRequestParam.setPhone(materialInfoVo.getRaiseMobile());
        patientRequestParam.setName(materialInfoVo.getPatientName());
        patientRequestParam.setIdCard(materialInfoVo.getPatientIdCard());
        patientRequestParam.setHospitalCityName(materialInfoVo.getRaiseCityName());
        patientRequestParam.setHospitalName(materialInfoVo.getHospital());
        patientRequestParam.setDepartmentName(materialInfoVo.getDepartment());
        return patientRequestParam;

    }


    @Override
    public int updateUploadRecruitStatusByClueIds(List<Long> clueIdList, Integer uploadRecruitStatus) {
        return cfPatientRecruitClueInfoDao.updateUploadRecruitStatusByClueIds(clueIdList, uploadRecruitStatus);
    }

    @Override
    public int updateRecruitWorkInfoById(CfPatientRecruitClueInfoDo clueInfoDo, Long id) {
        return cfPatientRecruitClueInfoDao.updateRecruitWorkInfoById(clueInfoDo, id);
    }


    @Override
    public CfPatientRecruitClueInfoDo getByPatientId(long patientId) {
        return cfPatientRecruitClueInfoDao.getByPatientId(patientId);
    }

    @Override
    public RecruitClueOrgModel countByOrgIds(RecruitClewSearchParam recruitClewSearchParam, List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return new RecruitClueOrgModel();
        }
        return cfPatientRecruitClueInfoDao.countByOrgIds(recruitClewSearchParam, orgIds);
    }

    @Override
    public List<RecruitClueOrgModel> groupByUniqueCode(RecruitClewSearchParam recruitClewSearchParam, List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Lists.newArrayList();
        }
        return cfPatientRecruitClueInfoDao.groupByUniqueCode(recruitClewSearchParam, orgIds);
    }

    @Override
    public int updateOrgId(String uniqueCode, long orgId) {
        if (StringUtils.isBlank(uniqueCode)) {
            return 0;
        }
        return cfPatientRecruitClueInfoDao.updatePatientRequestParam(uniqueCode, orgId);
    }

    @Override
    public CfPatientRecruitClueInfoDo getById(long clueId) {
        return cfPatientRecruitClueInfoDao.getById(clueId);
    }

    @Override
    public int updateClewStatusById(long id, int isDelete, int uploadRecruitStatus) {
        return cfPatientRecruitClueInfoDao.updateClewStatusById(id, isDelete, uploadRecruitStatus);
    }


    @Override
    public int updateValidStatus(long id, int clewValid, String invalidReason) {
        return cfPatientRecruitClueInfoDao.updateValidStatus(id, clewValid, invalidReason);
    }

    @Override
    public List<CfPatientRecruitClueInfoDo> getPatientRecruitClueInfoList(String startTime, String endTime, Integer pageSize, Long fetchId) {
        if (StringUtils.isAnyEmpty(startTime, endTime)) {
            return Lists.newArrayList();
        }
        log.info("查询参数为startTime, endTime, pageSize, fetchId:{}, {}, {}, {}", startTime, endTime, pageSize, fetchId);
        return cfPatientRecruitClueInfoDao.getPatientRecruitClueInfoList(startTime, endTime, pageSize, fetchId);
    }



    @Override
    public CfPatientRecruitClueInfoDo getByPhone(String phone) {
        String encryptPhone = oldShuidiCipher.aesEncrypt(phone);
        return cfPatientRecruitClueInfoDao.getByEncryptPhone(encryptPhone);
    }

    @Override
    public CfPatientRecruitClueInfoDo getClewInfoByUniqueCodeAndPhone(String uniqueCode, String phone) {
        String encryptPhone = oldShuidiCipher.aesEncrypt(phone);
        return cfPatientRecruitClueInfoDao.getClewInfoByUniqueCodeAndPhone(uniqueCode, encryptPhone);
    }

    @Override
    public int countClewInfoByType(String uniqueCode, int type, String keyWord) {
        if (type == 2) {
            keyWord = oldShuidiCipher.aesEncrypt(keyWord);
        }
        return cfPatientRecruitClueInfoDao.countClewInfoByType(uniqueCode, type, keyWord);
    }

    @Override
    public List<CfPatientRecruitClueInfoDo> listClewInfoByType(String uniqueCode, int type, String keyWord, int pageNo, int pageSize) {
        if (type == 2) {
            keyWord = oldShuidiCipher.aesEncrypt(keyWord);
        }
        int offset = 0;
        if (pageNo > 1) {
            offset = (pageNo - 1) * pageSize;
        }
        return cfPatientRecruitClueInfoDao.listClewInfoByType(uniqueCode, type, keyWord, offset, pageSize);
    }

    @Override
    public int countPageRecruitClew(RecruitClewSearchParam recruitClewSearchParam) {
        DateQueryParam dateQueryParam = recruitClewSearchParam.getDateQueryParam();
        if (dateQueryParam == null || StringUtils.isBlank(dateQueryParam.getStartTime()) || StringUtils.isBlank(dateQueryParam.getEndTime())) {
            return 0;
        }
        return cfPatientRecruitClueInfoDao.countPageRecruitClew(recruitClewSearchParam);
    }

    @Override
    public List<CfPatientRecruitClueInfoDo> pageRecruitClew(RecruitClewSearchParam recruitClewSearchParam) {
        DateQueryParam dateQueryParam = recruitClewSearchParam.getDateQueryParam();
        if (dateQueryParam == null || StringUtils.isBlank(dateQueryParam.getStartTime()) || StringUtils.isBlank(dateQueryParam.getEndTime())) {
            return Lists.newArrayList();
        }
        return cfPatientRecruitClueInfoDao.pageRecruitClew(recruitClewSearchParam);
    }

    @Override
    public ValidClewStatus queryValidCount(RecruitClewSearchParam recruitClewSearchParam) {
        DateQueryParam dateQueryParam = recruitClewSearchParam.getDateQueryParam();
        if (dateQueryParam == null || StringUtils.isBlank(dateQueryParam.getStartTime()) || StringUtils.isBlank(dateQueryParam.getEndTime())) {
            return new ValidClewStatus();
        }
        return Optional.ofNullable(cfPatientRecruitClueInfoDao.queryValidCount(recruitClewSearchParam)).orElse(new ValidClewStatus());
    }

    @Override
    public List<CfPatientRecruitClueInfoDo> listNeedUploadPatientRecruitClue(Date queryStartTime, Date queryEndTime, Integer uploadRecruitStatus) {
        return cfPatientRecruitClueInfoDao.listNeedUploadPatientRecruitClue(queryStartTime, queryEndTime, uploadRecruitStatus);
    }


    @Override
    public String leaderInfo(CfPatientRecruitClueInfoDo cfPatientRecruitClueInfoDo, Integer recruitWorkStatus, Integer recruitBizStatus) {
        String leaderInfo = cfPatientRecruitClueInfoDo.getLeaderInfo();
        //提交初筛时需要设置对应的人员信息
        List<PepRealTimeLeaderInfo> recruitRealTime = Lists.newArrayList();
        //首次提交初筛
        boolean firstSetLeaderInfo = (Objects.equals(recruitWorkStatus, 2) && Objects.equals(recruitBizStatus, 2))
                || (Objects.equals(recruitWorkStatus, 3) && Objects.equals(recruitBizStatus, 3));
        if (firstSetLeaderInfo && StringUtils.isBlank(leaderInfo)) {
            //需要设置对应的上级信息
            recruitRealTime = crmMemberInfoService.getRealTimeLeader(cfPatientRecruitClueInfoDo.getUniqueCode(), CrowdfundingVolunteerEnum.recruitLeaderRoles);
        }
        boolean needCheckLeader = ((Objects.equals(recruitWorkStatus, 3) && Objects.equals(recruitBizStatus, 2))
                || (Objects.equals(recruitWorkStatus, 6) && Objects.equals(recruitBizStatus, 2)))
                && StringUtils.isNoneBlank(leaderInfo);
        if (needCheckLeader) {
            //获取下当前上级
            List<PepRealTimeLeaderInfo> nowLeaderInfo = crmMemberInfoService.getRealTimeLeader(cfPatientRecruitClueInfoDo.getUniqueCode(), CrowdfundingVolunteerEnum.recruitLeaderRoles);
            if (CollectionUtils.isEmpty(nowLeaderInfo)) {
                log.info("当前顾问找不到组织链路,患者id:{}", cfPatientRecruitClueInfoDo.getPatientId());
            }
            List<PepRealTimeLeaderInfo> leaderInfoInDBs = JSON.parseArray(leaderInfo, PepRealTimeLeaderInfo.class);
            //检查是否有人员离职
            for (PepRealTimeLeaderInfo leaderInfoInDB : leaderInfoInDBs) {
                CrowdfundingVolunteer volunteer = volunteerService.getByUniqueCode(leaderInfoInDB.getLeaderUserId());
                if (Objects.equals(volunteer.getWorkStatus(), CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue())) {//用现在的上级替换下原来的上级
                    Optional<PepRealTimeLeaderInfo> replaceLeaderInfo = nowLeaderInfo.stream()
                            .filter(item -> Objects.equals(item.getLeaderRoleLevel(), volunteer.getLevel()))
                            .findFirst();
                    if (replaceLeaderInfo.isPresent()) {
                        leaderInfoInDB = replaceLeaderInfo.get();
                    } else {
                        //发出报警信息
                        log.info("既无当前上级,提交初筛的上级也离职,患者id:{}", cfPatientRecruitClueInfoDo.getPatientId());
                        AlarmBotService.sentText(GeneralConstant.RECRUIT_LEADER_ALARM, "顾问无上级,提交初筛的上级也离职,患者id:" + cfPatientRecruitClueInfoDo.getPatientId(),
                                null, null);
                    }
                }
                recruitRealTime.add(leaderInfoInDB);
            }
        }

        if (CollectionUtils.isNotEmpty(recruitRealTime)) {
            return JSON.toJSONString(recruitRealTime);
        }
        return null;
    }

    @Override
    public void updateLeaderInfo(long id, String leaderInfo) {
        cfPatientRecruitClueInfoDao.updateLeaderInfo(id, leaderInfo);
    }

}
