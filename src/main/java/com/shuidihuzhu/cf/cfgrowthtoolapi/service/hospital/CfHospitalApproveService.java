package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfHospitalApproveDo;

import java.util.List;

/**
 * 医院纠错审核表(CfHospitalApprove)表服务接口
 *
 * <AUTHOR>
 * @since 2020-11-09 15:45:37
 */
public interface CfHospitalApproveService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CfHospitalApproveDo queryById(long id);

    /**
     * 根据申请id获取审批流程
     */
    List<CfHospitalApproveDo> listByApplyId(int applyId);

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    List<CfHospitalApproveDo> queryAllByLimit(int offset, int limit);

    /**
     * 新增数据
     *
     * @param cfHospitalApproveDo 实例对象
     * @return 实例对象
     */
    CfHospitalApproveDo insert(CfHospitalApproveDo cfHospitalApproveDo);

    /**
     * 修改数据
     *
     * @param cfHospitalApproveDo 实例对象
     * @return 实例对象
     */
    CfHospitalApproveDo update(CfHospitalApproveDo cfHospitalApproveDo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);

}