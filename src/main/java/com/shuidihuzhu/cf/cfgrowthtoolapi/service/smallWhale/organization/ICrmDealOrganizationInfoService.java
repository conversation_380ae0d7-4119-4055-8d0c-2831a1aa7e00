package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;

import java.util.List;

/**
 * @author: x<PERSON><PERSON><PERSON>
 * @create 2023-07-18
 *
 * 针对crm组织的一些特殊处理
 **/
public interface ICrmDealOrganizationInfoService {

    /**
     * 根据角色判断是否将当前组织的名称拼接到对应的下级组织中
     * @return List<BdCrmOrganizationDO>
     */
    List<BdCrmOrganizationDO> appendOrgNameToOrgList(List<BdCrmOrganizationDO> orgList, List<BdCrmOrganizationDO> currentOrgInfoList, CrowdfundingVolunteerEnum.RoleEnum roleEnum, List<Integer> needAppendRoleList);

    /**
     * 直营组织名称重命名
     *
     * @param orgName
     * @return
     */
    String ownOrgNameRename(String orgName);


    /**
     * 蜂鸟组织名称重命名
     *
     * @param orgName
     * @return
     */
    String partnerOrgNameRename(String orgName);
}
