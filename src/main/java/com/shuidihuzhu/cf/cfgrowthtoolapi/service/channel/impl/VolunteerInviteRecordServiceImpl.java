package com.shuidihuzhu.cf.cfgrowthtoolapi.service.channel.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMasterForGrowthtoolDelegate;
import com.shuidihuzhu.cf.dao.CrowdfundingVolunteerCreateCaseRecordDao;
import com.shuidihuzhu.cf.dao.CrowdfundingVolunteerInviteUserRecordDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.channel.IVolunteerInviteRecordService;
import com.shuidihuzhu.cf.domain.dedicated.CrowdfundingVolunteerInviteUserRecordDO;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/4/30 11:05 AM
 */
@Service
public class VolunteerInviteRecordServiceImpl implements IVolunteerInviteRecordService {
    @Autowired
    private CrowdfundingVolunteerInviteUserRecordDao crowdfundingVolunteerInviteUserRecordDao;
    @Autowired
    private CrowdfundingVolunteerCreateCaseRecordDao crowdfundingVolunteerCreateCaseRecordDao;
    @Autowired
    private ICfMasterForGrowthtoolDelegate cfMasterForGrowthtoolDelegate;



    @Override
    public int saveCrowdfundingVolunteerInviteUserRecord(CrowdfundingVolunteerInviteUserRecordDO crowdfundingVolunteerInviteUserRecordDO) {
        return cfMasterForGrowthtoolDelegate.insertCrowdfundingVolunteerInviteUserRecordDO(crowdfundingVolunteerInviteUserRecordDO);
    }
    /**
     * 根据userid 获得 最近一次线下筹款顾问邀请 返回uniqueCode
     * @param userId
     * @return
     */
    @Override
    public String getlatelyVolunteerUniqueCodeByUserId(Long userId) {
        Map<String,Object> createCaseMap = crowdfundingVolunteerCreateCaseRecordDao.getUniqueCodeByUserId(userId);
        Map<String,Object> inviteUserMap = crowdfundingVolunteerInviteUserRecordDao.getUniqueCodeByUserId(userId);
        if (MapUtils.isEmpty(createCaseMap) && MapUtils.isEmpty(inviteUserMap)){
            return null;
        }
        if (MapUtils.isNotEmpty(createCaseMap) && MapUtils.isNotEmpty(inviteUserMap)){
            if (((Date)createCaseMap.get("createTime")).after((Date)inviteUserMap.get("createTime"))){
                return createCaseMap.get("uniqueCode").toString();
            }else {
                return inviteUserMap.get("uniqueCode").toString();
            }
        }
        return MapUtils.isNotEmpty(createCaseMap)?createCaseMap.get("uniqueCode").toString():inviteUserMap.get("uniqueCode").toString();
    }



    @Override
    public String getlatelyBdVolunteerUniqueCodeByUserId(long userId) {
        Map<String,Object> createCaseMap = crowdfundingVolunteerCreateCaseRecordDao.getUniqueCodeByUserId(userId);
        if (MapUtils.isEmpty(createCaseMap)){
            return null;
        }
        return createCaseMap.get("uniqueCode").toString();
    }



}
