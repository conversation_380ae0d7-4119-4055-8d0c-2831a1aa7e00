package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IClewPreproseMaterialService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.IBdTaskCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author: fengxuan
 * @create 2023-09-20 15:53
 **/
@Service
public class ScanReportTaskCheckImpl implements IBdTaskCheckService {

    @Autowired
    private IClewPreproseMaterialService clewPreproseMaterialService;

    @Override
    public CrmBdTaskDO.TaskTypeEnum getTaskType() {
        return CrmBdTaskDO.TaskTypeEnum.scan_report;
    }

    @Override
    public boolean checkNeedCreateTask(BdTaskContext bdTaskContext) {
        ClewCrowdfundingReportRelation reportRelation = bdTaskContext.getReportRelation();
        if (reportRelation == null) {
            //判断这个人员是否有代录入
            reportRelation = clewPreproseMaterialService.getReportedRelationsByInfoIdWithUniqueCode(bdTaskContext.getCaseId(), bdTaskContext.getUniqueCode());
        }
        return Objects.isNull(reportRelation);
    }

    @Override
    public boolean checkTaskComplete(BdTaskContext bdTaskContext) {
        return !checkNeedCreateTask(bdTaskContext);
    }

}
