package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCaseBaseDataDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CaseStatisticDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OrgValidCaseConfig;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OrgDataStatVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDataStatParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DateQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.CaseValidCommonSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CacheAdminValidCaseConfigService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCaseBaseDataService;
import com.shuidihuzhu.cf.dao.bdcrm.CfCaseBaseDataDao;
import com.shuidihuzhu.cf.repository.CfCaseBaseDataRepository;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020-07-03
 */
@Service
@Slf4j
public class CfCaseBaseDataServiceImpl implements ICfCaseBaseDataService {

    @Autowired
    private CfCaseBaseDataDao cfCaseBaseDataDao;

    @Autowired
    private CfCaseBaseDataRepository cfCaseBaseDataRepository;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private CacheAdminValidCaseConfigService validCaseConfigService;


    @Override
    public List<OrgDataStatVO> getOrgOverviewData(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList) {
        List<OrgDataStatVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orgIdList)) {
            return result;
        }
        //对组织id进行分组,根据不同的有效案例标准分组
        List<OrgValidCaseConfig> orgValidCaseConfigs = validCaseConfigService.partitionOrgIds(orgIdList);
        List<OrgDataStatVO> listDataFromDb = Lists.newArrayList();
        for (OrgValidCaseConfig orgValidCaseConfig : orgValidCaseConfigs) {
            log.debug("组织id:{},有效案例标准:{}", orgValidCaseConfig.getOrgIds(), orgValidCaseConfig.getSearchParam());
            CaseValidCommonSearchParam caseValidSearch = orgValidCaseConfig.getSearchParam();
            List<List<Long>> queryOrgIdList = Lists.partition(orgValidCaseConfig.getOrgIds(), 100);
            List<OrgDataStatVO> listDataFromDbSingle = queryOrgIdList.parallelStream().map(list -> cfCaseBaseDataRepository.getOrgOverviewData(crmDataStatParam, list, caseValidSearch))
                    .reduce((total, item) -> {
                        total.addAll(item);
                        return total;
                    }).orElse(Lists.newArrayList());
            listDataFromDb.addAll(listDataFromDbSingle);
        }
        result = OrgDataStatVO.reOrgDataByDateTime(result, listDataFromDb);
        return result;
    }


    @Override
    public List<OrgDataStatVO> getOrgDetailData4Person(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList) {
        List<OrgDataStatVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orgIdList)) {
            return result;
        }
        //对组织id进行分组,根据不同的有效案例标准分组
        List<OrgValidCaseConfig> orgValidCaseConfigs = validCaseConfigService.partitionOrgIds(orgIdList);
        for (OrgValidCaseConfig orgValidCaseConfig : orgValidCaseConfigs) {
            CaseValidCommonSearchParam caseValidSearch = orgValidCaseConfig.getSearchParam();
            result.addAll(cfCaseBaseDataRepository.getOrgDetailData4Person(crmDataStatParam, orgValidCaseConfig.getOrgIds(), caseValidSearch));
        }
        return result;
    }

    @Override
    public List<OrgDataStatVO> getOrgDetailData4Org(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList) {
        List<OrgDataStatVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orgIdList)) {
            return result;
        }
        //对组织id进行分组,根据不同的有效案例标准分组
        List<OrgValidCaseConfig> orgValidCaseConfigs = validCaseConfigService.partitionOrgIds(orgIdList);
        for (OrgValidCaseConfig orgValidCaseConfig : orgValidCaseConfigs) {
            CaseValidCommonSearchParam caseValidSearch = orgValidCaseConfig.getSearchParam();
            List<List<Long>> queryOrgIdList = Lists.partition(orgValidCaseConfig.getOrgIds(), 100);
            List<OrgDataStatVO> singleValidResult = queryOrgIdList.parallelStream().map(list -> cfCaseBaseDataRepository.getOrgDetailData4Org(crmDataStatParam, list, caseValidSearch))
                    .reduce((total, item) -> {
                        total.addAll(item);
                        return total;
                    }).orElse(Lists.newArrayList());
            result.addAll(singleValidResult);
        }
        return result;
    }

    @Override
    public List<OrgDataStatVO> getOrgOverviewData4Init(BdCrmDataStatParam crmDataStatParam, List<Integer> queryActCityIdList) {
        List<OrgDataStatVO> result = Lists.newArrayList();
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(), apolloService.getValidDonateNum());
        if (CollectionUtils.isEmpty(queryActCityIdList)) {
            return cfCaseBaseDataRepository.getOrgDetailData4InitOrg(crmDataStatParam, queryActCityIdList, caseValidSearch);
        }

        List<List<Integer>> queryCityIdList = Lists.partition(queryActCityIdList, 100);
        List<OrgDataStatVO> listDataFromDb = queryCityIdList.parallelStream().map(list -> cfCaseBaseDataRepository.getOrgDetailData4InitOrg(crmDataStatParam, list, caseValidSearch))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
        result = OrgDataStatVO.reOrgDataByDateTime(result, listDataFromDb);
        return result;
    }


    @Override
    public OrgDataStatVO getOrgDetailData4OrgCase(BdCrmDataStatParam crmDataStatParam, List<Integer> queryActCityList) {
        OrgDataStatVO result = new OrgDataStatVO();
        if (CollectionUtils.isEmpty(queryActCityList)) {
            return result;
        }
        List<List<Integer>> queryCityIdList = Lists.partition(queryActCityList, 100);
        List<OrgDataStatVO> listDataFromDb = Lists.newArrayList();
        queryCityIdList.parallelStream().forEach(list -> {
            listDataFromDb.add(cfCaseBaseDataRepository.getOrgDetailData4OrgCase(crmDataStatParam, list));
        });
        result = OrgDataStatVO.aggregatData(listDataFromDb);
        return result;
    }

    @Override
    public OrgDataStatVO getOrgDetailData4Market(BdCrmDataStatParam crmDataStatParam, List<Integer> queryActCityList) {
        OrgDataStatVO result = new OrgDataStatVO();
        if (CollectionUtils.isEmpty(queryActCityList)) {
            return result;
        }
        DateQueryParam dateQueryParam = getDateQueryParam(crmDataStatParam);
        log.info("startTime:{},endTime:{},queryActCityList:{}", dateQueryParam.getStartTime(), dateQueryParam.getEndTime(), queryActCityList);
        List<List<Integer>> queryCityIdList = Lists.partition(queryActCityList, 100);
        List<OrgDataStatVO> listDataFromDb = Lists.newArrayList();
        queryCityIdList.parallelStream().forEach(list -> {
            listDataFromDb.add(cfCaseBaseDataRepository.getOrgDetailData4Market(dateQueryParam.getStartTime(), dateQueryParam.getEndTime(), list));
        });
        result = OrgDataStatVO.aggregatData(listDataFromDb);
        return result;
    }

    @Override
    public CfCaseBaseDataDo getByCaseId(int caseId) {
        if (caseId <= 0) {
            return null;
        }
        return cfCaseBaseDataDao.getByCaseId(caseId);
    }

    @Override
    public List<CaseStatisticDataDO> getOrgDetailData4OrgCaseAndMarket(Set<Integer> cityIds, String start, String end) {
        if (CollectionUtils.isEmpty(cityIds)) {
            return Lists.newArrayList();
        }

        List<CaseStatisticDataDO> listDataFromDb = Lists.newArrayList();

        List<List<Integer>> queryCityIdList = Lists.partition(Lists.newArrayList(cityIds), 100);
        queryCityIdList.parallelStream().forEach(list -> {
            listDataFromDb.addAll(cfCaseBaseDataRepository.getOrgDetailData4OrgCaseAndMarket(list, start, end));
        });

        if (CollectionUtils.isEmpty(listDataFromDb)) {
            return Lists.newArrayList();
        }

        Map<String, CaseStatisticDataDO> map = Maps.newHashMap();
        for (CaseStatisticDataDO caseStatisticDataDO : listDataFromDb) {
            CaseStatisticDataDO exist = map.get(caseStatisticDataDO.getDayKey());
            if (exist == null) {
                map.put(caseStatisticDataDO.getDayKey(), caseStatisticDataDO);
            } else {
                exist.calcCaseSummary(caseStatisticDataDO);
            }
        }

        return Lists.newArrayList(map.values());
    }

    /**
     * 获取查询时间范围
     *
     * @param crmDataStatParam
     * @return
     */
    private DateQueryParam getDateQueryParam(BdCrmDataStatParam crmDataStatParam) {
        String endTime;
        DateQueryParam dateQueryParam = crmDataStatParam.getDateQueryParam();
        String startTime = dateQueryParam.getStartTime();
        Date endDate = DateUtil.addDay(DateUtil.getDateFromShortString(dateQueryParam.getEndTime()), -1);
        boolean isCurrentDay = DateUtils.isSameDay(endDate, DateUtil.getCurrentDate());
        if (isCurrentDay) {
            endTime = DateUtil.formatDate(endDate);
        } else {
            endTime = dateQueryParam.getEndTime();
        }
        DateQueryParam result = new DateQueryParam();
        result.setStartTime(startTime);
        result.setEndTime(endTime);
        return result;
    }

}
