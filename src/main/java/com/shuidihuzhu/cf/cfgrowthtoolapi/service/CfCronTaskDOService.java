package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCronTaskDO;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/2/19 下午7:50
 */
public interface CfCronTaskDOService{


    int insertSelective(CfCronTaskDO record);

    CfCronTaskDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CfCronTaskDO record);

        int updateStatus(Long id, int status);

        int updateRemarkContent(Long id, String remarkContent);

        long getSendTasksCount(Long sendKey, int bizType);

        List<CfCronTaskDO> getSendTasks(Long sendKey, int bizType, int pageNo, int pageSize);
    }
