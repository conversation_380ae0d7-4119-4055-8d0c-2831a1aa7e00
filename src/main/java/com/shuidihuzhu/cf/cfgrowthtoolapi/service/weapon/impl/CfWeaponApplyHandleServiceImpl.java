package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfActivityWeaponClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGrowthtoolApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.greenchannel.CfGreenChannelApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.GreenChannelApplyFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.greenchannel.ApproveFlowInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.greenchannel.fill.FilledSevenStep;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.greenchannel.fill.FilledSixthStep;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.message.GreenChannelWeaponMessage;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.message.WeaponCommonMessageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.GreenChannelApplyParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WeaponSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfGrowthtoolApproveService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.greenchannel.IGreenChannelApproveService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolCrusorQuery;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolWeaponUtil;
import com.shuidihuzhu.cf.dao.CrowdfundingVolunteerDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.charity.client.api.ThemeActivityClient;
import com.shuidihuzhu.charity.client.model.activity.theme.ThemeActivityAmountInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.enums.WeaponActivityResourceType;
import com.shuidihuzhu.client.cf.growthtool.enums.WeaponActivityTypeEnum;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.WeaponApplyCallBackModel;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-10-16 5:36 下午
 **/
@Slf4j
@Service
public class CfWeaponApplyHandleServiceImpl implements ICfWeaponApplyHandleService {

    @Autowired
    private ICfWeaponApplyRecordService applyRecordService;

    @Autowired
    private ICfWeaponBudgetService budgetService;

    @Autowired
    private ICfWeaponBudgetGroupService budgetGroupService;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Autowired
    private ICfGrowthtoolApproveService cfGrowthtoolApproveService;

    @Autowired
    private ICfVolunteerService volunteerService;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    @Autowired
    private ICfWeaponService cfWeaponService;

    @Autowired
    private ICfActivityWeaponClientDelegate activityClientDelegate;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private GreenChannelApplyFacade greenChannelApplyFacade;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired
    private ICfWeaponAssignRecordService cfWeaponAssignRecordService;

    @Autowired
    private ICrmOrganizationRelationService relationService;

    @Autowired
    private IGreenChannelApproveService channelApproveService;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private CrowdfundingVolunteerDao crowdfundingVolunteerDao;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private ICrmSelfBuiltOrgReadService organizationService;

    @Autowired
    private ThemeActivityClient themeActivityClient;

    private static final Set<Integer> directActivityTypeSet = Sets.newHashSet(WeaponActivityTypeEnum.DRAW_CASH_FEE_FREE.getCode(),
            WeaponActivityTypeEnum.ACTIVITY_DONATE.getCode(),
            WeaponActivityTypeEnum.ACTIVITY_TASK.getCode(),
            WeaponActivityTypeEnum.HOT_CASE.getCode(),
            WeaponActivityTypeEnum.HIGH_POTENTIAL.getCode(),
            WeaponActivityTypeEnum.zidingyi.getCode(),
            WeaponActivityTypeEnum.BONFIRE.getCode(),
            WeaponActivityTypeEnum.LOVE_HOME_THEME_ACTIVITY.getCode(),
            WeaponActivityTypeEnum.BUDGET_RESOURCE_ACTIVITY.getCode()

    );


    @Override
    public void handleAfterApply(int applyId) {
        log.info("顾问申请弹药后的审批:{}", applyId);
        CfWeaponApplyRecordDO applyRecordDO = applyRecordService.getById(applyId);
        if (applyRecordDO == null) {
            log.info("申请id:{}申请后查不到对应的记录信息", applyId);
            return;
        }
        CrowdfundingVolunteer volunteer = volunteerService.getByUniqueCode(applyRecordDO.getUniqueCode());
        //查找leader
        CrowdfundingVolunteer leaderVolunteer = null;
        if (applyRecordDO.getActivityType() == WeaponActivityTypeEnum.BUDGET_RESOURCE_ACTIVITY.getCode()) {
            leaderVolunteer = memberInfoService.listApplyLeaderWithExplicitByCaseId(applyRecordDO.getCaseId(), volunteer.getUniqueCode(),
                    CrowdfundingVolunteerEnum.optAndSuperRoles, GrowthtoolWeaponUtil.getContinueApproveRoleEnum(volunteer.getLevel()), null);
        } else {
            leaderVolunteer = findApplicantLeader(applyRecordDO, volunteer);
        }
        if (leaderVolunteer == null) {
            log.info("申请id:{}武器申请对应的申请人:{}上级为空", applyRecordDO.getId(), applyRecordDO.getMis());
            return;
        }
        WeaponActivityResourceType resourceType = WeaponActivityTypeEnum.findByCode(applyRecordDO.getActivityType()).getResourceType();
        if (WeaponActivityResourceType.greenchannel.equals(resourceType)) {
            GreenChannelApplyParam greenChannelApplyParam = GreenChannelApplyParam.parseByApplyExtInfo(applyRecordDO);
            if (Objects.nonNull(greenChannelApplyParam)) {
                //设置上级
                greenChannelApplyParam.setApprover(leaderVolunteer.getVolunteerName());
                applyRecordService.updateExtInfo(applyId, JSONObject.toJSONString(greenChannelApplyParam));
            }
        }
        createApproveAndSendMessage(applyRecordDO, volunteer, leaderVolunteer);
    }


    private void createApproveAndSendMessage(CfWeaponApplyRecordDO applyRecordDO, CrowdfundingVolunteer volunteer, CrowdfundingVolunteer leaderVolunteer) {
        int applyId = applyRecordDO.getId();
        //更新上级
        applyRecordService.addApplyLeader(applyId, leaderVolunteer.getUniqueCode());
        //插入对应的申请记录
        cfGrowthtoolApproveService.saveApproveContent(String.valueOf(applyRecordDO.getId()), CfGrowthtoolApproveDO.ApproveTypeEnum.APPROVE_WEAPON.getType(), volunteer, leaderVolunteer);
        CfWeaponDO weaponDO = cfWeaponService.getById(applyRecordDO.getWeaponId());
        //给上级发送消息
        appPushCrmCaseMsgService.applyWeaponTLeader(weaponDO.getName(), applyRecordDO.getApplyReason(), weaponDO.getApproveTimeLimit(), leaderVolunteer, applyRecordDO.getMisName());
    }


    //查找申请人上级
    private CrowdfundingVolunteer findApplicantLeader(CfWeaponApplyRecordDO applyRecordDO, CrowdfundingVolunteer volunteer) {
        return memberInfoService.listApplyLeaderWithDefaultExplicit(applyRecordDO.getUniqueCode(), GrowthtoolWeaponUtil.getContinueApproveRoleEnum(volunteer.getLevel()));
    }


    @Override
    public void handleByLeader(int applyId, boolean pass, String comment) {
        log.info("上级审批:{}申请,审批结果:{}", applyId, pass);
        //强校验当前申请是否在申请中,如果不是处理
        CfWeaponApplyRecordDO applyRecordDO = applyRecordService.getById(applyId);
        if (applyRecordDO == null) {
            log.info("申请id:{}对应的申请记录为空", applyId);
            return;
        }
        if (applyRecordDO.getApplyStatus() != WeaponEnums.BudgetApplyStatusEnum.applying.getStatus()) {
            log.info("上级审批时,申请id:{}不为审批中的状态", applyId);
            return;
        }
        CrowdfundingVolunteer volunteer = volunteerService.getByUniqueCode(applyRecordDO.getUniqueCode());
        if (pass) {
            CrowdfundingVolunteer currentApprover = volunteerService.getByUniqueCode(applyRecordDO.getLeaderUniqueCode());
            boolean needAreaLeaderApprove = needAreaLeaderApprove(applyRecordDO, currentApprover);
            if (needAreaLeaderApprove) {
                //给直接上级继续发送待办
                doContinueApproveByLeader(applyRecordDO, volunteer, currentApprover);
            } else {
                doPassByLeader(applyRecordDO);
            }
        } else {
            doRejectByLeader(applyRecordDO, comment);
        }
    }


    //需要逐层往上审批
    private boolean needAreaLeaderApprove(CfWeaponApplyRecordDO applyRecordDO, CrowdfundingVolunteer currentApproveVolunteer) {
        //判断当前审批人（如果已经为区域或者大区,不需要进一步审批）
        String uniqueCode = applyRecordDO.getLeaderUniqueCode();
        if (StringUtils.isBlank(uniqueCode) || currentApproveVolunteer == null) {
            return false;
        }
        WeaponActivityTypeEnum weaponActivityTypeEnum = WeaponActivityTypeEnum.findByCode(applyRecordDO.getActivityType());
        if (weaponActivityTypeEnum.getResourceType() != WeaponActivityResourceType.c_duan) {
            return false;
        }

        if (WeaponActivityTypeEnum.LOVE_HOME_THEME_ACTIVITY.getCode() == applyRecordDO.getActivityType()) {

            //获得活动使用金额
            long activityUsedMoney = applyRecordDO.getApplyMoney();

            return needApproveByLeaderForLoveTheme(currentApproveVolunteer, applyRecordDO.getCaseId(), applyRecordDO.getActivityType(), activityUsedMoney);
        } else if (applyRecordDO.getActivityType() == WeaponActivityTypeEnum.BUDGET_RESOURCE_ACTIVITY.getCode()) {
            return needApproveByLeaderForBudgetManagement(currentApproveVolunteer, applyRecordDO.getApplyMoney() / 100);
        }
        return needApproveByLeader(currentApproveVolunteer, applyRecordDO.getCaseId(), applyRecordDO.getActivityType());
    }


    @Override
    public boolean needApproveByLeader(CrowdfundingVolunteer cfVolunteer, int caseId, int activityType) {
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(cfVolunteer.getLevel());
        WeaponActivityTypeEnum activityTypeEnum = WeaponActivityTypeEnum.findByCode(activityType);
        // 补贴平台服务费用武器/退回平台服务费用武器 直营：审批至区域经理生效；渠道：审批至渠道区域经理生效（含代理商）
        boolean needApproveFreeServiceRole = roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.AREA_LEADER.getSortLevel();
        if (Objects.equals(activityTypeEnum, WeaponActivityTypeEnum.FREE_SERVICE_CHARGE) || Objects.equals(activityTypeEnum, WeaponActivityTypeEnum.RETURN_SERVICE_CHARGE)) {
            return needApproveFreeServiceRole;
        }
        //判断角色,当属于 区域及以上申请不需要 审批
        boolean noNeedApproveRole = roleEnum.getSortLevel() >= CrowdfundingVolunteerEnum.RoleEnum.AREA_LEADER.getSortLevel();
        if (noNeedApproveRole) {
            return false;
        }
        //高潜（爆款、潜力爆款）需要逐级审批
        if (GrowthtoolWeaponUtil.hotActivityList.contains(activityType)) {
            return true;
        }
        int patientAge = crowdFundingFeignDelegate.getPatientAge(caseId);
        CfBdCaseInfoDo bdCaseInfo = cfBdCaseInfoService.getBdCaseInfoByInfoId(caseId);
        boolean firstRaiser = true;
        if (bdCaseInfo != null) {
            firstRaiser = bdCaseInfo.getCaseDuplicateFlag() == 0;
        }
//        boolean noSpecialCase = firstRaiser && patientAge < 50;
        log.info("判断是否需要上级继续审批,特殊案例:{},患者年龄:{},活动类型:{},当前审批人级别:{}", firstRaiser, patientAge, activityTypeEnum, roleEnum);
        //爱心首页 非特殊案例 非顾问 不需要逐级
        if (Objects.equals(activityTypeEnum, WeaponActivityTypeEnum.LOVE_HOME)
                && !CrowdfundingVolunteerEnum.LoveActivityRoles.contains(roleEnum.getLevel())
                && firstRaiser) {
            return false;
        }

        //其他c端活动 非特殊案例 分区以上不需要逐级
        if (Objects.equals(activityTypeEnum.getResourceType(), WeaponActivityResourceType.c_duan)
                && !Objects.equals(activityTypeEnum, WeaponActivityTypeEnum.LOVE_HOME)
                && firstRaiser
                && roleEnum.getSortLevel() >= CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getSortLevel()) {
            return false;
        }
        return true;
    }

    @Override
    public boolean needApproveByLeaderForLoveTheme(CrowdfundingVolunteer cfVolunteer, int caseId, int activityType, long activityUsedMoney) {
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(cfVolunteer.getLevel());

        List<Long> orgId = Optional.of(crmOrganizationRelationService.listMemberOrgRelationByUniqueCode(cfVolunteer.getUniqueCode())
                .stream()
                .map(BdCrmOrgUserRelationDO::getOrgId)
                .collect(Collectors.toList())).orElse(Collections.emptyList());

        //查找到顾问生态运营所有下级组织包含本身
        List<Long> subordinateOrgIds = organizationService.listAllSubOrgIncludeSelf(GeneralConstant.volunteerEcologicalOperationOrgId)
                .stream()
                .map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toList());

        //判断该组织是否为顾问生态运营组织下属组织
        boolean isVolunteerEcologicalOperation = orgId.stream().anyMatch(subordinateOrgIds::contains);
        log.info("判断是否需要上级继续审批,活动使用金额:{},当前审批人级别:{},是否为长沙组织下属组织:{}", activityUsedMoney, roleEnum, isVolunteerEcologicalOperation);

        activityUsedMoney = activityUsedMoney / 100;
        if (!CrowdfundingVolunteerEnum.delegateRoles.contains(cfVolunteer.getLevel()) && !isVolunteerEcologicalOperation) {
            //处理直营非顾问生态运营
            if (activityUsedMoney >= 0 && activityUsedMoney < 4000) {
                if (roleEnum.getSortLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getSortLevel()) {
                    return false;
                }
                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getSortLevel();
            } else if (activityUsedMoney >= 4000 && activityUsedMoney < 7000) {
                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.AREA_LEADER.getSortLevel();
            } else if (activityUsedMoney >= 7000) {
                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.BIG_AREA_LEADER.getSortLevel();
            }

        } else if (CrowdfundingVolunteerEnum.delegateRoles.contains(cfVolunteer.getLevel()) && !isVolunteerEcologicalOperation) {
            //处理代理商非顾问生态运营
            if (activityUsedMoney >= 0 && activityUsedMoney < 4000) {
                if (roleEnum.getSortLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getSortLevel()) {
                    return false;
                }
                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getSortLevel();
            } else if (activityUsedMoney >= 4000 && activityUsedMoney < 7000) {
                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.AREA_LEADER.getSortLevel();
            } else if (activityUsedMoney >= 7000) {
                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.BIG_AREA_LEADER.getSortLevel();
            }
        } else {
            //处理顾问生态运营
            if (activityUsedMoney >= 0 && activityUsedMoney < 7000) {
                if (roleEnum.getSortLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getSortLevel()) {
                    return false;
                }
                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getSortLevel();
            } else if (activityUsedMoney >= 7000) {
                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.BIG_AREA_LEADER.getSortLevel();
            }
        }

        return false;
    }

    @Override
    public boolean needApproveByLeaderForBudgetManagement(CrowdfundingVolunteer cfVolunteer, long activityUsedMoney) {
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(cfVolunteer.getLevel());
        if (roleEnum == null) {
            return false;
        }

        switch (roleEnum) {
            case COMMON_LEADER:
            case PROVINCE_LEADER:
            case DELEGATE_BOSS:
            case DELEGATE_PROVINCE:
            case DELEGATE_COMMON:
                return true;
            case PARTITION_LEADER:
            case PARTNER_AREA_PROVINCE:
                return activityUsedMoney > 3000;
            case AREA_LEADER:
                return activityUsedMoney > 6000;
            case BIG_AREA_LEADER:
                return false;
            default:
                return false;
        }
    }

//    @Override
//    public boolean needApproveByLeaderForBudgetManagement(CrowdfundingVolunteer cfVolunteer, int caseId, int activityType, long activityUsedMoney) {
//        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(cfVolunteer.getLevel());
//
//        List<Long> orgId = Optional.of(crmOrganizationRelationService.listMemberOrgRelationByUniqueCode(cfVolunteer.getUniqueCode())
//                .stream()
//                .map(BdCrmOrgUserRelationDO::getOrgId)
//                .collect(Collectors.toList())).orElse(Collections.emptyList());
//
//        //查找到顾问生态运营所有下级组织包含本身
//        List<Long> subordinateOrgIds = organizationService.listAllSubOrgIncludeSelf(GeneralConstant.volunteerEcologicalOperationOrgId)
//                .stream()
//                .map(BdCrmOrganizationDO::getId)
//                .collect(Collectors.toList());
//
//        //判断该组织是否为顾问生态运营组织下属组织
//        boolean isVolunteerEcologicalOperation = orgId.stream().anyMatch(subordinateOrgIds::contains);
//        log.info("判断是否需要上级继续审批,活动使用金额:{},当前审批人级别:{},是否为长沙组织下属组织:{}", activityUsedMoney, roleEnum, isVolunteerEcologicalOperation);
//
//        activityUsedMoney = activityUsedMoney / 100;
//        if (!CrowdfundingVolunteerEnum.budgetDelegateRoles.contains(cfVolunteer.getLevel()) && !isVolunteerEcologicalOperation) {
//            //处理直营非顾问生态运营
//            if (activityUsedMoney > 0 && activityUsedMoney <= 3000) {
//                if (roleEnum.getSortLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getSortLevel()) {
//                    return false;
//                }
//                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getSortLevel();
//            } else if (activityUsedMoney > 3000 && activityUsedMoney <= 6000) {
//                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.AREA_LEADER.getSortLevel();
//            } else if (activityUsedMoney > 6000) {
//                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.BIG_AREA_LEADER.getSortLevel();
//            }
//
//        } else if (CrowdfundingVolunteerEnum.budgetDelegateRoles.contains(cfVolunteer.getLevel()) && !isVolunteerEcologicalOperation) {
//            //处理代理商非顾问生态运营
//            if (activityUsedMoney > 0 && activityUsedMoney <= 3000) {
//                if (roleEnum.getSortLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getSortLevel()) {
//                    return false;
//                }
//                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getSortLevel();
//            } else if (activityUsedMoney > 3000 && activityUsedMoney <= 6000) {
//                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.AREA_LEADER.getSortLevel();
//            } else if (activityUsedMoney > 6000) {
//                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.BIG_AREA_LEADER.getSortLevel();
//            }
//        } else {
//            //处理顾问生态运营
//            if (activityUsedMoney > 0 && activityUsedMoney <= 6000) {
//                if (roleEnum.getSortLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getSortLevel()) {
//                    return false;
//                }
//                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getSortLevel();
//            } else if (activityUsedMoney > 6000) {
//                return roleEnum.getSortLevel() < CrowdfundingVolunteerEnum.RoleEnum.BIG_AREA_LEADER.getSortLevel();
//            }
//        }
//
//        return false;
//    }

    @Override
    public void deductionApplyingResource(CfWeaponApplyRecordDO cfWeaponApplyRecordDO, int addApplyingResource) {
        //扣减预算对应使用中的名额
        budgetService.addChildApplyingResource(cfWeaponApplyRecordDO.getBudgetId(), addApplyingResource);
        //扣减预算组对应使用中的名额
        budgetGroupService.addApplyingResource(cfWeaponApplyRecordDO.getBudgetGroupId(), addApplyingResource);
    }

    @Override
    public long getActivityUsedMoney(int activityId, int caseId) {

        Response<ThemeActivityAmountInfo> res = themeActivityClient.getThemeActivityAmountInfo(activityId, caseId);

        log.info("获取活动使用金额,活动id:{},返回结果:{},案例id:{}", activityId, JSON.toJSONString(res), caseId);

        ThemeActivityAmountInfo themeActivityAmountInfo = Optional.ofNullable(res)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (themeActivityAmountInfo == null) {
            return 0;
        }

        //获得活动使用金额
        return Optional.of(themeActivityAmountInfo.getTargetAmount()).orElse(0L);
    }

    @Override
    public void preOccupancyFail(int activityId, int caseId) {
        themeActivityClient.preOccupancyFail(activityId, caseId);
    }


    private void doContinueApproveByLeader(CfWeaponApplyRecordDO applyRecordDO, CrowdfundingVolunteer volunteer, CrowdfundingVolunteer currentApprover) {
        //查找上级
        CrowdfundingVolunteer leaderVolunteer = null;
        if (applyRecordDO.getActivityType() == WeaponActivityTypeEnum.BUDGET_RESOURCE_ACTIVITY.getCode()) {
            leaderVolunteer = memberInfoService.listApplyLeaderWithExplicitByCaseId(applyRecordDO.getCaseId(), currentApprover.getUniqueCode(),
                    CrowdfundingVolunteerEnum.optAndSuperRoles, GrowthtoolWeaponUtil.getContinueApproveRoleEnum(currentApprover.getLevel()), null);
        } else {
            leaderVolunteer = memberInfoService.listApplyLeaderWithDefaultExplicit(currentApprover.getUniqueCode(), GrowthtoolWeaponUtil.getContinueApproveRoleEnum(currentApprover.getLevel()));
        }
        if (leaderVolunteer == null) {
            log.info("第二次审核,申请id:{}武器申请对应的申请人:{}上级为空", applyRecordDO.getId(), applyRecordDO.getMis());
            return;
        }
        createApproveAndSendMessage(applyRecordDO, volunteer, leaderVolunteer);
    }


    public void doPassByLeader(CfWeaponApplyRecordDO applyRecordDO) {
        //更新申请状态
        applyRecordService.updateApplyStatusFromLeader(applyRecordDO.getId(), WeaponEnums.BudgetApplyStatusEnum.leader_pass, "");

        WeaponActivityResourceType resourceType = WeaponActivityTypeEnum.findByCode(applyRecordDO.getActivityType()).getResourceType();
        if (Objects.isNull(resourceType)) {
            return;
        }
        //获取武器信息
        CfWeaponDO cfWeaponDO = cfWeaponService.getById(applyRecordDO.getWeaponId());
        WeaponAccessModel weaponAccessModel = new WeaponAccessModel();
        weaponAccessModel.setSuccess(false);
        switch (resourceType) {
            case c_duan:
                String uniqueCode = StringUtils.isNotBlank(applyRecordDO.getLeaderUniqueCode()) ? applyRecordDO.getLeaderUniqueCode() : applyRecordDO.getUniqueCode();
                ActivityJoinModel activityJoinModel = ActivityJoinModel.createByApplyInfo(applyRecordDO, cfWeaponDO);
                activityJoinModel.setApplyTime(applyRecordDO.getCreateTime().getTime());
                activityJoinModel.setUniqueCode(uniqueCode);
                //如果是爱心首页主题活动调用活动接口前 将申请中的数量扣减，已使用数量的处理依旧按照以前逻辑处理
                if (applyRecordDO.getActivityType() == WeaponActivityTypeEnum.LOVE_HOME_THEME_ACTIVITY.getCode()) {
                    this.deductionApplyingResource(applyRecordDO, -1);
                }
                //调用活动接口
                weaponAccessModel = activityClientDelegate.requireJoinActivity(activityJoinModel).getData();
                //获取活动名称
                String activityName = activityClientDelegate.getActivityName(applyRecordDO.getCaseId(), applyRecordDO.getActivityType());
                applyRecordDO.setActivityName(activityName);
                break;
            case greenchannel:
                GreenChannelApplyParam greenChannelApplyParam = GreenChannelApplyParam.parseByApplyExtInfo(applyRecordDO);
                OpResult<Boolean> opResult = greenChannelApplyFacade.addApplyRecord(greenChannelApplyParam);
                if (opResult.isFailOrNullData()) {
                    weaponAccessModel.setFailDesc(opResult.getMessage());
                } else {
                    weaponAccessModel.setSuccess(true);
                }
                break;
            case zhidingyi:
                weaponAccessModel.setSuccess(true);
                break;
            default:
                break;
        }

        //请求第3方失败，直接返回,不进行后续动作
        if (!weaponAccessModel.isSuccess()) {
            doRejectActivity(applyRecordDO, weaponAccessModel.getFailDesc(), null);
        } else {
            doPassByActivityDirect(applyRecordDO, "审核通过");
        }
    }


    public void doRejectByLeader(CfWeaponApplyRecordDO applyRecordDO, String comment) {
        String lockName = "weapon_reject_leader" + applyRecordDO.getId();
        RLock lock = null;
        try {
            lock = redissonHandler.getLock(lockName);
            if (!lock.tryLock()) {
                log.info("doPassByActivity get lock fail,key:{}", lockName);
                return;
            }
            if (StringUtils.isBlank(comment)) {
                comment = "上级审批驳回";
            }
            //重新获取下当前的审核状态,如果不为上级审核通过不进行后续操作
            CfWeaponApplyRecordDO recordDOFromDB = applyRecordService.getById(applyRecordDO.getId());
            if (recordDOFromDB == null || applyRecordDO.getApplyStatus() != WeaponEnums.BudgetApplyStatusEnum.applying.getStatus()) {
                log.info("doRejectByLeader key:{}当前的状态:{}非为审核中,无法进行后续操作", lockName, recordDOFromDB == null ? 0 : recordDOFromDB.getApplyStatus());
                return;
            }
            CfWeaponDO cfWeapon = cfWeaponService.getById(applyRecordDO.getWeaponId());
            //审批驳回爱心首页活动 通知活动方
            this.preOccupancyFail(cfWeapon.getActivityId(), applyRecordDO.getCaseId());
            if (recordDOFromDB.getActivityType() == WeaponActivityTypeEnum.BUDGET_RESOURCE_ACTIVITY.getCode()) {
                minusManagementApplyingMoney(applyRecordDO);
            } else {
                //处理对应的名额,金额  子预算 、 预算
                budgetService.modifyResource(applyRecordDO.getBudgetId(), 0, -1, 0, (applyRecordDO.getApplyMoney() * -1));
                budgetGroupService.modifyGroupResource(applyRecordDO.getBudgetGroupId(), 0, -1, 0, (applyRecordDO.getApplyMoney() * -1));
            }
            //更新申请状态
            applyRecordService.updateApplyStatusFromLeader(applyRecordDO.getId(), WeaponEnums.BudgetApplyStatusEnum.leader_reject, Optional.of(comment).orElse(""));
            CrowdfundingVolunteer applyVolunteer = cfVolunteerService.getByUniqueCode(applyRecordDO.getUniqueCode());
            appPushCrmCaseMsgService.notifyWeaponRejectByLeader(getWeaponName(applyRecordDO), comment, applyRecordDO.getCaseId(), applyVolunteer);
            //通知之前审核通过的上级-该申请被驳回https://wiki.shuiditech.com/pages/viewpage.action?pageId=883699090
            String finalComment = comment;
            cfGrowthtoolApproveService.listByContentAndType(CfGrowthtoolApproveDO.ApproveTypeEnum.APPROVE_WEAPON.getType(), String.valueOf(recordDOFromDB.getId()))
                    .stream()
                    .filter(item -> item.getApproveStatus() == CfGrowthtoolApproveDO.ApproveStatusEnum.PASS.getStatus())
                    .forEach(item -> {
                        String applyName = Optional.ofNullable(applyVolunteer).map(CrowdfundingVolunteer::getVolunteerName).orElse("");
                        String approveUniqueCode = item.getApproveUniqueCode();
                        CrowdfundingVolunteer approveVolunteer = volunteerService.getCrowdfundingVolunteerByUniqueCode(approveUniqueCode);
                        if (approveVolunteer != null) {
                            //发送消息
                            appPushCrmCaseMsgService.notifyWeaponApproveReject(getWeaponName(applyRecordDO), finalComment, applyRecordDO.getCaseId(), approveVolunteer, applyName);
                        }
                    });

        } finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    @Override
    public void doRejectByTimeOut(int applyId) {
        log.info("申请id:{}超过审批时间", applyId);
        CfWeaponApplyRecordDO applyRecordDO = applyRecordService.getById(applyId);
        if (applyRecordDO == null) {
            return;
        }
        CfWeaponDO weaponDO = cfWeaponService.getById(applyRecordDO.getWeaponId());
        if (weaponDO == null) {
            log.info("找不到对应的武器");
            return;
        }
        //爱心首页活动超时驳回 通知活动方
        if (weaponDO.getActivityType() == WeaponActivityTypeEnum.LOVE_HOME_THEME_ACTIVITY.getCode()) {
            this.preOccupancyFail(weaponDO.getActivityId(), applyRecordDO.getCaseId());
        }

        if (weaponDO.getActivityType() == WeaponActivityTypeEnum.BUDGET_RESOURCE_ACTIVITY.getCode()) {
            minusManagementApplyingMoney(applyRecordDO);
        } else {
            //处理对应的名额,金额  子预算 、 预算
            budgetService.modifyResource(applyRecordDO.getBudgetId(), 0, -1, 0, (applyRecordDO.getApplyMoney() * -1));
            budgetGroupService.modifyGroupResource(applyRecordDO.getBudgetGroupId(), 0, -1, 0, (applyRecordDO.getApplyMoney() * -1));
        }
        //更新申请状态
        applyRecordService.updateApplyStatusFromLeader(applyRecordDO.getId(), WeaponEnums.BudgetApplyStatusEnum.leader_reject, GrowthtoolWeaponUtil.TIME_OUT_REJECT);
        CrowdfundingVolunteer applyVolunteer = cfVolunteerService.getByUniqueCode(applyRecordDO.getUniqueCode());
        CrowdfundingVolunteer leaderVolunteer = cfVolunteerService.getByUniqueCode(applyRecordDO.getLeaderUniqueCode());
        appPushCrmCaseMsgService.notifyWeaponApplyTimeOut(weaponDO.getName(), weaponDO.getApproveTimeLimit(), applyRecordDO.getCaseId(), applyVolunteer, leaderVolunteer);
        //关闭对应的待办
        cfGrowthtoolApproveService.listByContentAndType(CfGrowthtoolApproveDO.ApproveTypeEnum.APPROVE_WEAPON.getType(), String.valueOf(applyRecordDO.getId()))
                .stream()
                .filter(item -> item.getApproveStatus() == CfGrowthtoolApproveDO.ApproveStatusEnum.COMMIT_WAIT_APPROVE.getStatus())
                .forEach(item -> {
                    log.info("系统自动驳回,关闭对应的待办:{}", item.getId());
                    cfGrowthtoolApproveService.updateApproveStatus(item.getId(), CfGrowthtoolApproveDO.ApproveStatusEnum.REJECT.getStatus());
                });
    }


    @Override
    public void handleByActivityCallBack(WeaponApplyCallBackModel callBackModel) {
        log.info("活动回调信息:{}", JSON.toJSONString(callBackModel));
        List<Integer> activityTypes = Lists.newArrayList(callBackModel.getActivityType());
        WeaponActivityTypeEnum typeEnum = WeaponActivityTypeEnum.findByCode(callBackModel.getActivityType());
        if (typeEnum == WeaponActivityTypeEnum.ACTIVITY_DONATE) {
            activityTypes.add(WeaponActivityTypeEnum.HOT_CASE.getCode());
            activityTypes.add(WeaponActivityTypeEnum.HIGH_POTENTIAL.getCode());
        }
        //检查下是否存在
        List<CfWeaponApplyRecordDO> cfWeaponApplyRecordDOS = applyRecordService
                .listByCaseIdAndActivityType(callBackModel.getCaseId(), activityTypes);
        if (CollectionUtils.isEmpty(cfWeaponApplyRecordDOS)) {
            log.info("此案例:{}对应活动:{}非武器库申请发起", callBackModel.getCaseId(), typeEnum);
            return;
        }
        List<CfWeaponApplyRecordDO> leaderPassedApplyList = cfWeaponApplyRecordDOS.stream()
                .filter(item -> item.getBudgetId() > 0)
                .filter(item -> item.getApplyStatus() == WeaponEnums.BudgetApplyStatusEnum.leader_pass.getStatus())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaderPassedApplyList)) {
            log.info("案例:{}对应的申请:{}已处理", callBackModel.getCaseId(), Lists.newArrayList(cfWeaponApplyRecordDOS));
            return;
        }
        if (leaderPassedApplyList.size() > 1) {
            log.info("系统存在同一个案例申请一个活动需要修复");
            return;
        }
        if (callBackModel.isApplyResult()) {
            doPassByActivity(leaderPassedApplyList.get(0), callBackModel.getComment(), (long) callBackModel.getApplyId());
        } else {
            doRejectActivity(leaderPassedApplyList.get(0), callBackModel.getComment(), (long) callBackModel.getApplyId());
        }
    }

    @Override
    public void doPassByActivity(CfWeaponApplyRecordDO cfWeaponApplyRecordDO, String comment, Long callBackId) {
        String lockName = "weapon_pass_activity" + cfWeaponApplyRecordDO.getId();
        RLock lock = null;
        try {
            lock = redissonHandler.getLock(lockName);
            if (!lock.tryLock()) {
                log.info("doPassByActivity get lock fail,key:{}", lockName);
                return;
            }
            //重新获取下当前的审核状态,如果不为上级审核通过不进行后续操作
            CfWeaponApplyRecordDO applyRecordDO = applyRecordService.getById(cfWeaponApplyRecordDO.getId());
            if (applyRecordDO == null || applyRecordDO.getApplyStatus() != WeaponEnums.BudgetApplyStatusEnum.leader_pass.getStatus()) {
                log.info("doPassByActivity key:{}当前的状态:{}非为上级审核通过,无法进行后续操作", lockName, applyRecordDO == null ? 0 : applyRecordDO.getApplyStatus());
                return;
            }
            //如果是爱心首页主题活动因为已经提前扣减过预算名额所以这里不需要扣减申请中的预算名额
            if(cfWeaponApplyRecordDO.getActivityType() == WeaponActivityTypeEnum.LOVE_HOME_THEME_ACTIVITY.getCode()) {
                log.info("爱心首页主题活动扣减金额:{}", applyRecordDO.getApplyMoney());
                budgetService.modifyResource(cfWeaponApplyRecordDO.getBudgetId(), 1, 0, applyRecordDO.getApplyMoney(), (applyRecordDO.getApplyMoney() * -1));
                budgetGroupService.modifyGroupResource(cfWeaponApplyRecordDO.getBudgetGroupId(), 1, 0, applyRecordDO.getApplyMoney(), (applyRecordDO.getApplyMoney() * -1));
            } else if (cfWeaponApplyRecordDO.getActivityType() == WeaponActivityTypeEnum.BUDGET_RESOURCE_ACTIVITY.getCode()) {
                modifyManagementMoney(cfWeaponApplyRecordDO);
            } else {
                //处理对应的名额,金额  子预算 、 预算
                budgetService.modifyResource(cfWeaponApplyRecordDO.getBudgetId(), 1, -1, 0, 0);
                budgetGroupService.modifyGroupResource(cfWeaponApplyRecordDO.getBudgetGroupId(), 1, -1, 0, 0);
            }
            applyRecordService.updateApplyStatusFromActivity(cfWeaponApplyRecordDO.getId(), cfWeaponApplyRecordDO.getActivityName() ,WeaponEnums.BudgetApplyStatusEnum.activity_pass, comment, callBackId);
            WeaponActivityTypeEnum weaponActivityTypeEnum = WeaponActivityTypeEnum.findByCode(cfWeaponApplyRecordDO.getActivityType());
            if (Objects.isNull(weaponActivityTypeEnum)) {
                return;
            }
            GreenChannelWeaponMessage greenChannelWeaponMessage = null;
            //c端武器由活动方发送消息
            switch (weaponActivityTypeEnum) {
                case person_to_public:
                    //获取applyId
                    if (callBackId != null && callBackId > 0) {
                        greenChannelWeaponMessage = new GreenChannelWeaponMessage();
                        CfGreenChannelApproveDO sevenStepApproveInfo = channelApproveService.getByApplyIdAndStepCode(callBackId.intValue(), CfGreenChannelEnums.StepCodeEnum.seven.getCode());
                        if (sevenStepApproveInfo != null) {
                            ApproveFlowInfoModel approveFlowInfoModel = JSON.parseObject(sevenStepApproveInfo.getFillInfo(), ApproveFlowInfoModel.class);
                            FilledSevenStep filledSevenStep = approveFlowInfoModel.getFilledSevenStep();
                            if (filledSevenStep != null) {
                                greenChannelWeaponMessage.setPredicateTime(filledSevenStep.getSevenStepPredicateTime());
                            }
                        }
                        CfGreenChannelApproveDO sixChannelApproveInfo = channelApproveService.getByApplyIdAndStepCode(callBackId.intValue(), CfGreenChannelEnums.StepCodeEnum.six.getCode());
                        if (sixChannelApproveInfo != null) {
                            ApproveFlowInfoModel approveFlowInfoModel = JSON.parseObject(sixChannelApproveInfo.getFillInfo(), ApproveFlowInfoModel.class);
                            FilledSixthStep filledSixthStep = approveFlowInfoModel.getFilledSixthStep();
                            if (filledSixthStep != null) {
                                greenChannelWeaponMessage.setLinkToPublish(filledSixthStep.getLinkToPublish());
                                greenChannelWeaponMessage.setTitleToPublish(filledSixthStep.getTitleToPublish());
                            }
                        }
                    }
                case flow:
                case LIVING_ALLOWANCE:
                case zidingyi:
                case BONFIRE:
                    //sendMsg
                    CrowdfundingVolunteer applyVolunteer = cfVolunteerService.getByUniqueCode(cfWeaponApplyRecordDO.getUniqueCode());
                    appPushCrmCaseMsgService.notifyWeaponPassByActivity(getWeaponName(cfWeaponApplyRecordDO), cfWeaponApplyRecordDO.getCaseId(), applyVolunteer, greenChannelWeaponMessage);
                    break;
                default:
                    break;
            }
        } finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public void doRejectActivity(CfWeaponApplyRecordDO cfWeaponApplyRecordDO, String comment, Long callBackId) {
        String lockName = "weapon_reject_activity" + cfWeaponApplyRecordDO.getId();
        RLock lock = null;
        try {
            lock = redissonHandler.getLock(lockName);
            if (!lock.tryLock()) {
                log.info("doRejectActivity get lock fail,key:{}", lockName);
                return;
            }
            //重新获取下当前的审核状态,如果不为上级审核通过不进行后续操作
            CfWeaponApplyRecordDO applyRecordDO = applyRecordService.getById(cfWeaponApplyRecordDO.getId());
            if (applyRecordDO == null || applyRecordDO.getApplyStatus() != WeaponEnums.BudgetApplyStatusEnum.leader_pass.getStatus()) {
                log.info("doRejectActivity key:{}当前的状态:{}非为上级审核通过,无法进行后续操作", lockName, applyRecordDO == null ? 0 : applyRecordDO.getApplyStatus());
                return;
            }
            //如果是爱心首页主题活动因为已经提前扣减过预算名额所以这里不需要扣减申请中的预算名额
            if(cfWeaponApplyRecordDO.getActivityType() == WeaponActivityTypeEnum.LOVE_HOME_THEME_ACTIVITY.getCode()) {
                budgetService.modifyResource(cfWeaponApplyRecordDO.getBudgetId(), 0, 0,0, (applyRecordDO.getApplyMoney() * -1));
                budgetGroupService.modifyGroupResource(cfWeaponApplyRecordDO.getBudgetGroupId(), 0, 0, 0, (applyRecordDO.getApplyMoney() * -1));
                CfWeaponDO cfWeapon = cfWeaponService.getById(cfWeaponApplyRecordDO.getWeaponId());
                //审批驳回爱心首页活动 通知活动方
                this.preOccupancyFail(cfWeapon.getActivityId(), cfWeaponApplyRecordDO.getCaseId());
            } else if (cfWeaponApplyRecordDO.getActivityType() == WeaponActivityTypeEnum.BUDGET_RESOURCE_ACTIVITY.getCode()) {
                minusManagementApplyingMoney(cfWeaponApplyRecordDO);
            } else {
                //处理对应的名额  子预算 、 预算
                budgetService.modifyResource(cfWeaponApplyRecordDO.getBudgetId(), 0, -1,0, 0);
                budgetGroupService.modifyGroupResource(cfWeaponApplyRecordDO.getBudgetGroupId(), 0, -1, 0, 0);
            }

            applyRecordService.updateApplyStatusFromActivity(cfWeaponApplyRecordDO.getId(), cfWeaponApplyRecordDO.getActivityName(), WeaponEnums.BudgetApplyStatusEnum.activity_reject, comment, callBackId);
            //获取剩余可用子预算
            CfWeaponBudgetDO budgetDO = budgetService.getById(cfWeaponApplyRecordDO.getBudgetId());
            if (budgetDO == null) {
                log.info("预算:{}作废或找不到对应的预算", cfWeaponApplyRecordDO.getBudgetId());
                return;
            }
            //发送消息
            CrowdfundingVolunteer applyVolunteer = cfVolunteerService.getByUniqueCode(cfWeaponApplyRecordDO.getUniqueCode());
            CrowdfundingVolunteer leaderVolunteer = cfVolunteerService.getByUniqueCode(cfWeaponApplyRecordDO.getLeaderUniqueCode());
            WeaponCommonMessageModel weaponCommonMessageModel = new WeaponCommonMessageModel(applyRecordDO, applyVolunteer, cfWeaponService.getById(applyRecordDO.getWeaponId()));
            weaponCommonMessageModel.setLeaderVolunteer(leaderVolunteer);
            weaponCommonMessageModel.setBudgetDO(budgetDO);
            weaponCommonMessageModel.setRejectMessage(comment);
            appPushCrmCaseMsgService.notifyWeaponRejectByActivity(weaponCommonMessageModel);
        } finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public void doPassByActivityDirect(CfWeaponApplyRecordDO cfWeaponApplyRecordDO, String comment) {
        //符合直接审核通过活动
        if (directActivityTypeSet.contains(cfWeaponApplyRecordDO.getActivityType())) {
            this.doPassByActivity(cfWeaponApplyRecordDO, comment, null);
        }
    }

    @Override
    public void repairResource() {

        List<CfWeaponApplyRecordDO> allRecordList = GrowthtoolCrusorQuery.queryByCursor(
                (beginId, limit) -> applyRecordService.listAllApplyRecord((int) beginId, limit),
                item -> (long) item.getId());

        //根据预算聚合
        Map<Integer, List<CfWeaponApplyRecordDO>> budgetTModels = allRecordList.stream().collect(Collectors.groupingBy(CfWeaponApplyRecordDO::getBudgetId));
        for (Integer budgetId : budgetTModels.keySet()) {
            //更新每个预算中的使用情况
            List<CfWeaponApplyRecordDO> cfWeaponApplyRecordDOS = budgetTModels.get(budgetId);
            //已使用的
            long usedCount = cfWeaponApplyRecordDOS.stream().filter(item -> item.getApplyStatus() == WeaponEnums.BudgetApplyStatusEnum.activity_pass.getStatus())
                    .count();
            //申请中的
            long applyingCount = cfWeaponApplyRecordDOS.stream()
                    .filter(item -> item.getApplyStatus() == WeaponEnums.BudgetApplyStatusEnum.applying.getStatus() || item.getApplyStatus() == WeaponEnums.BudgetApplyStatusEnum.leader_pass.getStatus())
                    .count();

            budgetService.repairResource(budgetId, (int) usedCount, (int) applyingCount);
        }
        //根据预算组聚合
        Map<Integer, List<CfWeaponApplyRecordDO>> budgetGroupTModels = allRecordList.stream().collect(Collectors.groupingBy(CfWeaponApplyRecordDO::getBudgetGroupId));
        for (Integer budgetGroupId : budgetGroupTModels.keySet()) {
            //更新每个预算中的使用情况
            List<CfWeaponApplyRecordDO> cfWeaponApplyRecordDOS = budgetGroupTModels.get(budgetGroupId);
            //已使用的
            long usedCount = cfWeaponApplyRecordDOS
                    .stream()
                    .filter(item -> item.getApplyStatus() == WeaponEnums.BudgetApplyStatusEnum.activity_pass.getStatus())
                    .count();
            //申请中的
            long applyingCount = cfWeaponApplyRecordDOS
                    .stream()
                    .filter(item -> item.getApplyStatus() == WeaponEnums.BudgetApplyStatusEnum.applying.getStatus() || item.getApplyStatus() == WeaponEnums.BudgetApplyStatusEnum.leader_pass.getStatus())
                    .count();

            budgetGroupService.repairResource(budgetGroupId, (int) usedCount, (int) applyingCount);
        }
    }

    @Override
    public void syncWeaponAssignOrg() {
        //找到所有的有效预算
        List<CfWeaponDO> cfWeaponDOS = cfWeaponService.listAllWeapon(null);
        List<Integer> budgetGroupIds = Lists.newArrayList();
        for (CfWeaponDO cfWeaponDO : cfWeaponDOS) {
            budgetGroupIds.addAll(budgetGroupService.listInUsingBudgetGroup(cfWeaponDO.getId())
                    .stream()
                    .map(CfWeaponBudgetGroupDO::getId)
                    .collect(Collectors.toList()));
        }
        //找到有效的子预算
        List<CfWeaponBudgetDO> budgetIdList = budgetService.listBudgetByBudgetGroupId(budgetGroupIds)
                .stream()
                .filter(CfWeaponBudgetDO::canUseByWhale)
                .collect(Collectors.toList());


        //根据子预算找到分配
        for (CfWeaponBudgetDO budgetDO : budgetIdList) {
            List<CfWeaponAssignRecordDO> assignRecordDOS = cfWeaponAssignRecordService.listCanAssignByBudgetId(budgetDO.getId())
                    .stream()
                    .filter(item -> item.getOrgId() == 0)
                    .collect(Collectors.toList());
            for (CfWeaponAssignRecordDO assignRecordDO : assignRecordDOS) {
                //重新设置orgId
                String uniqueCode = assignRecordDO.getUniqueCode();
                //找一个最新设置的
                Optional<BdCrmOrgUserRelationDO> relationDOOptional = relationService.listMemberOrgRelationByUniqueCode(uniqueCode)
                        .stream()
                        .max(Ordering.natural().onResultOf(BdCrmOrgUserRelationDO::getOrgId));
                relationDOOptional.ifPresent(item -> cfWeaponAssignRecordService.updateOrgId(assignRecordDO.getId(), (int) item.getOrgId()));

            }
        }

    }

    @Override
    public OpResult<String> checkCanPassByLeader(int applyId) {
        CfWeaponApplyRecordDO applyRecordDO = applyRecordService.getById(applyId);
        if (applyRecordDO == null) {
            return OpResult.createSucResult();
        }
        CfWeaponBudgetDO budgetDO = budgetService.getById(applyRecordDO.getBudgetId());
        if (budgetDO != null && budgetDO.getActivityType() == WeaponActivityTypeEnum.BUDGET_RESOURCE_ACTIVITY.getCode()) {
            return OpResult.createSucResult();
        }

        List<Integer> applySucCodeLists = Lists.newArrayList(WeaponEnums.BudgetApplyStatusEnum.leader_pass.getStatus(),
                WeaponEnums.BudgetApplyStatusEnum.activity_pass.getStatus());
        //判断下是否超过了每日使用限制,如果超过了需要调用reject
        List<CfWeaponApplyRecordDO> cfWeaponApplyRecordDOS = applyRecordService.todayApplyLimitRecord(applyRecordDO.getBudgetId())
                .stream()
                .filter(item -> applySucCodeLists.contains(item.getApplyStatus()))
                .collect(Collectors.toList());

        if (budgetDO != null && cfWeaponApplyRecordDOS.size() >= budgetDO.getLimitNumEveryDay()) {
            log.info("checkCanPassByLeader超过了每日使用限制:{}", applyId);
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR, "武器超出当日使用限制数量");
        }
        return OpResult.createSucResult();
    }


    /**
     * 爱心首页资源未使用提示
     * 先找预算-今天是否还有使用名额-今天有哪些人员没有申请
     */
    @Override
    public void remindLoveHomeUnUse() {
        List<Integer> needBeNotifyLevel = CrowdfundingVolunteerEnum.provinceRoles;

        DateTime now = DateTime.now();
        //先找到爱心首页武器-当月预算-子预算
        WeaponSearchParam weaponSearchParam = new WeaponSearchParam();
        weaponSearchParam.setActivityTypeList(Lists.newArrayList(WeaponActivityTypeEnum.LOVE_HOME.getCode()));
        weaponSearchParam.setWeaponStatus(WeaponEnums.WeaponUseStatusEnum.shangjia.getCode());
        List<CfWeaponDO> cfWeaponDOS = cfWeaponService.queryCfWeaponDoListByParam(weaponSearchParam);
        if (CollectionUtils.isEmpty(cfWeaponDOS)) {
            return;
        }
        Set<Integer> groupIds = Sets.newHashSet();
        //找到所有的分配人员-过滤未申请-只保留顾问+业务经理
        for (CfWeaponDO cfWeaponDO : cfWeaponDOS) {
            groupIds.addAll(budgetGroupService.listInUsingBudgetGroup(cfWeaponDO.getId())
                    .stream()
                    .filter(item -> now.isAfter(new DateTime(item.getStartTime())))
                    .map(CfWeaponBudgetGroupDO::getId)
                    .collect(Collectors.toList()));
        }

        List<CfWeaponBudgetDO> needNotifyBudgetList = Lists.newArrayList();
        Set<String> todayHasApplyUniqueCode = Sets.newHashSet();
        Map<String, CfWeaponBudgetDO> notApplySourceMap = Maps.newHashMap();
        for (Integer groupId : groupIds) {
            //判断下是否还有剩余名额 + 判断下今天是否还有剩余名额
            List<CfWeaponBudgetDO> cfWeaponBudgetDOS = budgetService.listByGroupId(groupId)
                    .stream()
                    .filter(CfWeaponBudgetDO::canUseByWhale)
                    .filter(item -> item.canApplyResource() > 0)
                    .filter(item -> {
                        List<CfWeaponApplyRecordDO> cfWeaponApplyRecordDOS = applyRecordService.todayApplyLimitRecord(item.getId());
                        todayHasApplyUniqueCode.addAll(cfWeaponApplyRecordDOS.stream().map(CfWeaponApplyRecordDO::getUniqueCode).collect(Collectors.toList()));
                        return item.getLimitNumEveryDay() > cfWeaponApplyRecordDOS.size();
                    })
                    .collect(Collectors.toList());

            needNotifyBudgetList.addAll(cfWeaponBudgetDOS);
        }

        for (CfWeaponBudgetDO budgetDO : needNotifyBudgetList) {
            //找到所有有分配权限的
            List<CfWeaponAssignRecordDO> assignRecordDOS = cfWeaponAssignRecordService.listCanAssignByBudgetId(budgetDO.getId());

            //排除已经申请过的人员
            assignRecordDOS.stream()
                    .filter(item -> !todayHasApplyUniqueCode.contains(item.getUniqueCode()))
                    .map(CfWeaponAssignRecordDO::getUniqueCode)
                    .forEach(item -> notApplySourceMap.put(item, budgetDO));
        }

        //找这些人的上级
        List<CrowdfundingVolunteer> volunteerList = volunteerService.getCfVolunteerDOByUniqueCodes(Lists.newArrayList(notApplySourceMap.keySet()))
                .stream()
                .filter(item -> needBeNotifyLevel.contains(item.getLevel()))
                .filter(item -> item.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.ON_THE_JOB.getValue())
                .collect(Collectors.toList());

        List<WarnLoveHomeMsgModel> models = Lists.newArrayList();

        for (CrowdfundingVolunteer volunteer : volunteerList) {
            CfWeaponBudgetDO budgetDO = notApplySourceMap.get(volunteer.getUniqueCode());
            if (budgetDO == null) {
                continue;
            }
            //找上级
            CrowdfundingVolunteer leader = memberInfoService.findApplyLeaderForApprove(volunteer.getUniqueCode());
            List<String> leaderUniqueCodeList = models.stream().map(WarnLoveHomeMsgModel::getLeader).map(CrowdfundingVolunteer::getUniqueCode).collect(Collectors.toList());
            if (leader != null) {
                if (!leaderUniqueCodeList.contains(leader.getUniqueCode())) {
                    models.add(new WarnLoveHomeMsgModel(leader, budgetDO, Sets.newHashSet(volunteer.getVolunteerName())));
                } else {
                    models.stream().filter(item -> Objects.equals(item.getLeader().getUniqueCode(), leader.getUniqueCode()))
                            .forEach(item -> item.getWhoNotApplyNameList().add(volunteer.getVolunteerName()));
                }
            }
        }
        log.info("爱心提醒消息条数:{}", models.size());
        for (WarnLoveHomeMsgModel model : models) {
            //发送消息
            appPushCrmCaseMsgService.warnLoveHomeUnUse(model.leader, model.budgetDO, model.getWhoNotApplyNameList());
        }
    }


    @AllArgsConstructor
    @Data
    static class WarnLoveHomeMsgModel {
        CrowdfundingVolunteer leader;
        CfWeaponBudgetDO budgetDO;
        Set<String> whoNotApplyNameList;
    }


    private String getWeaponName(CfWeaponApplyRecordDO applyRecordDO) {
        if (applyRecordDO == null) {
            return "";
        }
        return Optional.ofNullable(cfWeaponService.getById(applyRecordDO.getWeaponId()))
                .map(CfWeaponDO::getName)
                .orElse("");
    }

    private List<CfWeaponBudgetDO> getCfWeaponBudgetList(int budgetId) {
        CfWeaponBudgetDO cfWeaponBudgetDO = budgetService.getById(budgetId);
        if (cfWeaponBudgetDO == null) {
            return Lists.newArrayList();
        }
        //当前组织的所有上级组织
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = organizationService.listParentOrgAsChainOrder(cfWeaponBudgetDO.getOrgId());
        List<Integer> orgIdList = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).map(Long::intValue).collect(Collectors.toList());
        //通过组织查询到所有的子预算
        List<CfWeaponBudgetDO> cfWeaponBudgetDOList = budgetService.listByGroupIdAndOrgId(cfWeaponBudgetDO.getBudgetGroupId(), orgIdList);
        return cfWeaponBudgetDOList.stream().filter(CfWeaponBudgetDO::canUseByWhale).collect(Collectors.toList());
    }

    private boolean isManagement(String uniqueCode) {
        CrowdfundingVolunteer crowdfundingVolunteer = cfVolunteerService.getByUniqueCode(uniqueCode);
        if (crowdfundingVolunteer == null) {
            return false;
        }
        if (crowdfundingVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.AREA_LEADER.getLevel() ||
                crowdfundingVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getLevel() ||
                crowdfundingVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.BIG_AREA_LEADER.getLevel() ||
                crowdfundingVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getLevel() ||
                crowdfundingVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_BOSS.getLevel() ||
                crowdfundingVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_PROVINCE.getLevel() ||
                crowdfundingVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel()) {
            return true;
        }
        return false;
    }

    private void minusManagementApplyingMoney(CfWeaponApplyRecordDO applyRecordDO) {
        boolean management = isManagement(applyRecordDO.getUniqueCode());
        if (management) {
            budgetService.addManagementApplyingMoney(applyRecordDO.getBudgetId(), (applyRecordDO.getApplyMoney() * -1));
            List<CfWeaponBudgetDO> cfWeaponBudgetDOList = getCfWeaponBudgetList(applyRecordDO.getBudgetId());
            for (CfWeaponBudgetDO budgetDO : cfWeaponBudgetDOList) {
                //冻结相应预算金额
                budgetService.addManagementApplyingMoneySum(budgetDO.getId(), (applyRecordDO.getApplyMoney() * -1));
            }
        } else {
            List<CfWeaponBudgetDO> cfWeaponBudgetDOList = getCfWeaponBudgetList(applyRecordDO.getBudgetId());
            for (CfWeaponBudgetDO budgetDO : cfWeaponBudgetDOList) {
                //子预算更新完，子预算中的上级组织都要更新
                budgetService.modifyResource(budgetDO.getId(), 0, 0, 0, (applyRecordDO.getApplyMoney() * -1));
            }
        }
        //预算组的已使用金额更新
        budgetGroupService.modifyGroupResource(applyRecordDO.getBudgetGroupId(), 0, 0, 0, (applyRecordDO.getApplyMoney() * -1));
    }

    private void modifyManagementMoney(CfWeaponApplyRecordDO applyRecordDO) {
        boolean management = isManagement(applyRecordDO.getUniqueCode());
        if (management) {
            budgetService.modifyManagementMoney(applyRecordDO.getBudgetId(), applyRecordDO.getApplyMoney());
            List<CfWeaponBudgetDO> cfWeaponBudgetDOList = getCfWeaponBudgetList(applyRecordDO.getBudgetId());
            for (CfWeaponBudgetDO budgetDO : cfWeaponBudgetDOList) {
                budgetService.modifyManagementMoneySum(budgetDO.getId(), applyRecordDO.getApplyMoney());
            }
        } else {
            List<CfWeaponBudgetDO> cfWeaponBudgetDOList = getCfWeaponBudgetList(applyRecordDO.getBudgetId());
            for (CfWeaponBudgetDO budgetDO : cfWeaponBudgetDOList) {
                //子预算更新完，子预算中的上级组织都要更新
                budgetService.modifyResource(budgetDO.getId(), 0, 0, applyRecordDO.getApplyMoney(), (applyRecordDO.getApplyMoney() * -1));
            }
        }
        //预算组的已使用金额更新
        budgetGroupService.modifyGroupResource(applyRecordDO.getBudgetGroupId(), 0, 0, applyRecordDO.getApplyMoney(), (applyRecordDO.getApplyMoney() * -1));
    }

}
