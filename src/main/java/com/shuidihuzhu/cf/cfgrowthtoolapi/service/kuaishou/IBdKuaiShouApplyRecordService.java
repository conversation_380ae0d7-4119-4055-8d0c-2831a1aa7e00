package com.shuidihuzhu.cf.cfgrowthtoolapi.service.kuaishou;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfKuaiShouApplyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.CfKuaiShouApplySearchParams;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/25 7:47 PM
 * 快手筹款申请记录表服务接口-cf_kuaishou_apply_record
 */
public interface IBdKuaiShouApplyRecordService {

    int insert(CfKuaiShouApplyDO cfKuaiShouApplyDO);

    CfKuaiShouApplyDO getKuaiShouApplyByInfoId(String infoId);

    List<CfKuaiShouApplyDO> getCfKuaiShouApplyResultByParams(CfKuaiShouApplySearchParams params);

    Long getCfKuaiShouApplyCountByParams(CfKuaiShouApplySearchParams params);

    void modifyStatus(Long id, Integer dealResult, String dealReason, Date dealTime, String name, String otherReason);

    CfKuaiShouApplyDO getKuaiShouApplyById(Long id);


}

