package com.shuidihuzhu.cf.cfgrowthtoolapi.service.experiment.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.experiment.ExperimentHitJudgmentService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.experiment.SceneStrategy;
import com.shuidihuzhu.client.model.ExperimentHitResult;
import com.shuidihuzhu.client.model.ExperimentParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/7/31 2:22 PM
 */
@Slf4j
@Service
public class ExperimentHitJudgmentServiceImpl implements ExperimentHitJudgmentService {

    private final Map<Integer, SceneStrategy> strategies;

    @Autowired
    public ExperimentHitJudgmentServiceImpl(Map<Integer, SceneStrategy> strategies) {
        this.strategies = strategies;
    }
    @Override
    public ExperimentHitResult experimentHitJudge(ExperimentParam experimentParam) {

        // 选择对应场景执行的策略
        SceneStrategy strategy = strategies.get(experimentParam.getSceneId());
        if (strategy == null) {
            return null;
        }

        // 判断场景是否命中实验
        ExperimentHitResult experimentHitResult = strategy.isHit(experimentParam);
        log.info("ExperimentHitJudgmentServiceImpl experimentHitJudge experimentParam: {} experimentHitResult: {}",
                JSONObject.toJSONString(experimentParam), JSONObject.toJSONString(experimentHitResult));
        return experimentHitResult;
    }

}
