package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.bill.ModifyPartnerBillDetail;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.bill.ModifyPartnerBills;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.PartnerBillCreateMsg;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.dao.lovepartner.CfBdPartnerBillChangeHistoryDao;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2021-09-02 8:07 下午
 * 账单服务
 **/
@Slf4j
@Service
public class BillService {


    @Autowired
    private CfBdPartnerBillService cfBdPartnerBillService;

    @Autowired
    private CfBdPartnerBillSummaryService summaryService;

    @Autowired
    private CfBdPartnerBillOrgSummaryService orgSummaryService;

    @Autowired
    private ICrmSelfBuiltOrgReadService orgReadService;

    @Autowired
    private ICrmOrganizationRelationService ogRelationService;

    @Autowired
    private CfPartnerOrgMemberSnapshotService memberSnapshotService;

    @Autowired
    private CfPartnerCaseBillService partnerCaseBillService;

    @Autowired
    private CfPartnerAttendBillService attendBillService;

    @Autowired
    private CfPartnerCycleService cfPartnerCycleService;

    @Autowired
    private CfPartnerSnapshotService partnerSnapshotService;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Autowired
    private CfPartnerCaseRelationService caseRelationService;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    @Autowired
    private CfBdPartnerBillChangeHistoryDao cfBdPartnerBillChangeHistoryDao;

    @Autowired
    private CfBdPartnerBillChangeHistoryService cfBdPartnerBillChangeHistoryService;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class PartnerBillInfo {

        CfPartnerSnapshotDo partnerInfoDo;

        List<CfPartnerCaseBillDo> caseBillDos;

        List<CfPartnerAttendBillDo> attendBillDos;

        CfPartnerCycleDo cfPartnerCycleDo;
    }


    public void deleteByCycleId(long cycleId) {
        memberSnapshotService.deleteByCycleId(cycleId);
        partnerCaseBillService.deleteByCycleId(cycleId);
        attendBillService.deleteByCycleId(cycleId);
        summaryService.deleteByCycleId(cycleId);
        orgSummaryService.deleteByCycleId(cycleId);
        cfBdPartnerBillService.deleteByCycleId(cycleId);
    }


    /**
     * 账单生成日期 - 1/16生成 兼职账单
     */
    public void createPartnerBill(int cycleId) {
        //找到当前所有的兼职
        CfPartnerCycleDo cfPartnerCycleDo = cfPartnerCycleService.getByCycleId(cycleId);
        //获取兼职的案例、 考勤
        List<CfPartnerCaseBillDo> cfPartnerCaseBillDos = partnerCaseBillService.listByCycle(cycleId);
        List<CfPartnerAttendBillDo> cfPartnerAttendBillDos = attendBillService.listByCycle(cycleId);

        List<CfPartnerSnapshotDo> partnerSnapshotDoList = partnerSnapshotService.listByCycleId((long) cycleId);

        Map<String, List<CfPartnerSnapshotDo>> uniqueCodeMaps = partnerSnapshotDoList
                .stream()
                .collect(Collectors.groupingBy(CfPartnerSnapshotDo::getLeaderUniqueCode));

        List<CfBdPartnerBillDO> partnerBillDOS = uniqueCodeMaps.keySet().stream()
                .flatMap(item -> {
                    List<CfPartnerSnapshotDo> partnerSnapshots = uniqueCodeMaps.get(item);
                    List<CfBdPartnerBillDO> list = Lists.newArrayList();
                    for (CfPartnerSnapshotDo cfPartnerSnapshotDo : partnerSnapshots) {
                        List<CfPartnerCaseBillDo> caseBillDos = cfPartnerCaseBillDos.stream()
                                .filter(caseBillDo -> Objects.equals(caseBillDo.getUniqueCode(), cfPartnerSnapshotDo.getUniqueCode()))
                                .collect(Collectors.toList());
                        List<CfPartnerAttendBillDo> attendBillDos = cfPartnerAttendBillDos.stream()
                                .filter(caseBillDo -> Objects.equals(caseBillDo.getUniqueCode(), cfPartnerSnapshotDo.getUniqueCode()))
                                .collect(Collectors.toList());
                        PartnerBillInfo partnerBillInfo = new PartnerBillInfo(cfPartnerSnapshotDo, caseBillDos, attendBillDos, cfPartnerCycleDo);
                        list.add(CfBdPartnerBillDO.create(partnerBillInfo));
                    }
                    return list.stream();
                }).collect(Collectors.toList());

        cfBdPartnerBillService.insertBatch(partnerBillDOS);
        List<String> leaderUniqueCodes = partnerBillDOS.stream()
                .filter(item -> Objects.equals(item.getApproveStatus(), PartnerEnums.BillApproveEnums.approve.getCode()))
                .map(CfBdPartnerBillDO::getUniqueCode)
                .distinct()
                .collect(Collectors.toList());
        //需要自动生成账单
        leaderUniqueCodes.forEach(item -> createBdBill(item, cfPartnerCycleDo.getId().intValue()));

        //更新下所有案例的计算状态
        List<Integer> caseIds = cfPartnerCaseBillDos.stream().map(CfPartnerCaseBillDo::getCaseId).collect(Collectors.toList());
        caseRelationService.updateCaseCalStatus(caseIds, PartnerEnums.CaseCalcEnums.CALCULATED);

        List<String> uniqueCodeList = partnerBillDOS.stream()
                .map(CfBdPartnerBillDO::getUniqueCode)
                .distinct()
                .collect(Collectors.toList());

        //发消息给对应的顾问
        PartnerBillCreateMsg partnerBillCreateMsg = new PartnerBillCreateMsg(uniqueCodeList, cfPartnerCycleDo.getName(), "", "https://www.shuidichou.com/bd/love-partner/reward/launch");
        appPushCrmCaseMsgService.alarmPartnerBillCreate(partnerBillCreateMsg);
    }


    /**
     * 顾问提交底下账单 - 前置条件 底下的兼职生成了账单-且提交
     */
    public void createBdBill(String uniqueCode, int cycleId) {
        //查找这个顾问下所有的兼职账单
        List<CfBdPartnerBillDO> cfBdPartnerBillDOS = cfBdPartnerBillService.listByUniqueCode(uniqueCode, cycleId);
        if (CollectionUtils.isEmpty(cfBdPartnerBillDOS)) {
            log.info("uniqueCode:{}无兼职伙伴", uniqueCode);
            return;
        }
        //判断下当前是否都提交了
        if (judgeHasCommitBill(cfBdPartnerBillDOS)) {
            return;
        }
        CfBdPartnerBillSummaryDO summaryDO = CfBdPartnerBillSummaryDO.createByBills(cfBdPartnerBillDOS);
        if (summaryDO == null) {
            log.info("没有兼职账单,生成空账单");
            return;
        }
        //生成对应的顾问账单
        summaryService.insertOrUpdate(summaryDO);
        BdCrmOrganizationDO org = orgReadService.getCurrentOrgById(cfBdPartnerBillDOS.get(0).getOrgId());
        long orgId = org.getId();
        if (Objects.equals(org.getOrgAttribute(), OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode())) {
            orgId = org.getParentId();
        }
        createOrgBill(orgId, cycleId);
    }

    /**
     * 生成组织账单 - 组织下顾问所有的账单都生成了
     */
    public void createOrgBill(long orgId, int cycleId) {
        BdCrmOrganizationDO org = orgReadService.getCurrentOrgById(orgId);
        //如果是叶子节点 直接根据 顾问账单生成 组织账单
        List<BdCrmOrganizationDO> organizationDOS = orgReadService.listAllSubOrgIncludeSelf(orgId);
        List<Integer> orgIds = organizationDOS.stream()
                .map(item -> (int) item.getId())
                .collect(Collectors.toList());
        //找到组织下的所有兼职账单
        List<CfBdPartnerBillDO> cfBdPartnerBillDOS = cfBdPartnerBillService.listByOrgIds(orgIds, cycleId);
        if (judgeHasCommitBill(cfBdPartnerBillDOS)) {
            return;
        }
        //找到直接下级组织是否生成了账单,没有不需要生成
        List<BdCrmOrganizationDO> directSubOrgList = orgReadService.findDirectSubOrgByOrgId(orgId);
        List<CfBdPartnerBillOrgSummaryDO> billOrgSummaryDOList = orgSummaryService.listByOrgIdsAndCycleId(directSubOrgList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList()), cycleId);

        //业务经理所在层级的账单可以生成
        List<BdCrmOrganizationDO> hasPartnerOrgList = directSubOrgList.stream()
                .filter(item -> item.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode())
                .filter(item -> containsPartnerBill(item.getId(), cycleId))
                .collect(Collectors.toList());

        if (hasPartnerOrgList.size() > billOrgSummaryDOList.size()) {
            log.info("存在直接下级组织还有没有提交的账单,有兼职的下级组织:{},提交了账单的组织:{}", hasPartnerOrgList, billOrgSummaryDOList);
            return;
        }

        CfBdPartnerBillOrgSummaryDO orgSummaryDO = CfBdPartnerBillOrgSummaryDO.createByBills(cfBdPartnerBillDOS, org);
        if (orgSummaryDO == null) {
            log.info("没有兼职账单,组织账单无法生成");
            return;
        }
        int affectRows = orgSummaryService.insertOrUpdate(orgSummaryDO);
        if (affectRows > 0) {
            List<String> uniqueCodeList = ogRelationService.listRelationByOrgId(orgId)
                    .stream()
                    .map(BdCrmOrgUserRelationDO::getUniqueCode)
                    .collect(Collectors.toList());
            PartnerBillCreateMsg partnerBillCreateMsg = new PartnerBillCreateMsg(uniqueCodeList, orgSummaryDO.getBillName(), org.getOrgName(), "https://www.shuidichou.com/bd/love-partner/reward/record");
            appPushCrmCaseMsgService.alarmPartnerBillCreate(partnerBillCreateMsg);
        }
    }



    public Response<Void> confirmOrgBill(long orgId, int cycleId) {
        log.info("提交组织账单orgId:{}", orgId);
        List<BdCrmOrganizationDO> organizationDOS = orgReadService.listAllSubOrgExcludeSelf(orgId);
        List<Long> subOrgIds = organizationDOS.stream()
                .map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toList());
        //判断下面的组织账单是否都提交了
        boolean subOrgAllApprove = orgSummaryService.listByOrgIdsAndCycleId(subOrgIds, cycleId)
                .stream()
                .allMatch(item -> Objects.equals(item.getApproveStatus(), PartnerEnums.BillApproveEnums.approve.getCode()));
        if (!subOrgAllApprove) {
            log.info("还有下级组织账单未提交");
            return NewResponseUtil.makeFail("还有下级组织账单未提交!");
        }
        //将当前组织的状态改为审核通过
        orgSummaryService.updateApproveStatus(PartnerEnums.BillApproveEnums.approve.getCode(), orgId, cycleId);
        //如果是大区不需要往上继续生成组织账单
        BdCrmOrganizationDO parentOrg = orgReadService.getParentOrg(orgId);
        if (parentOrg == null || Objects.equals((int) parentOrg.getId(), GeneralConstant.CRM_ORG_ID)) {
            log.info("不需要继续往上生成组织账单");
            //将组下所有的顾问账单都更改为审核通过
            List<String> uniqueCodeList = memberInfoService.listAllSubMemberIncludeSelf((int) orgId)
                    .stream()
                    .map(BdCrmOrgUserRelationDO::getUniqueCode)
                    .collect(Collectors.toList());
            summaryService.updateApproveStatus(uniqueCodeList, cycleId, PartnerEnums.BillApproveEnums.approve.getCode());
            return NewResponseUtil.makeSuccess(null);
        }
        createOrgBill(parentOrg.getId(), cycleId);
        return NewResponseUtil.makeSuccess(null);
    }




    private boolean judgeHasCommitBill(List<CfBdPartnerBillDO> cfBdPartnerBillDOS) {
        //判断下当前是否都提交了
        List<CfBdPartnerBillDO> waitApproveBills = cfBdPartnerBillDOS.stream()
                .filter(item -> Objects.equals(item.getApproveStatus(), PartnerEnums.BillApproveEnums.wait_approve.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(waitApproveBills)) {
            List<String> partnerNames = waitApproveBills
                    .stream()
                    .map(CfBdPartnerBillDO::getPartnerVolunteerName)
                    .collect(Collectors.toList());
            log.info("存在兼职账单还未提交:{}", partnerNames);
            return true;
        }
        return false;
    }


    private boolean containsPartnerBill(long orgId, int cycleId) {
        List<BdCrmOrganizationDO> organizationDOS = orgReadService.listAllSubOrgIncludeSelf(orgId);
        List<Integer> orgIds = organizationDOS.stream()
                .map(item -> (int) item.getId())
                .collect(Collectors.toList());
        //查找这个顾问下所有的兼职账单
        List<CfBdPartnerBillDO> cfBdPartnerBillDOS = cfBdPartnerBillService.listByOrgIds(orgIds, cycleId);
        if (CollectionUtils.isEmpty(cfBdPartnerBillDOS)) {
            log.info("orgId:{}无兼职伙伴", orgId);
            return false;
        }
        return true;
    }


    public Response<Void> batchModifyPartnerBill(ModifyPartnerBills modifyPartnerBill) {
        List<ModifyPartnerBillDetail> modifyPartnerBillDetails = modifyPartnerBill.getModifyPartnerBillDetails();
        int cycleId = modifyPartnerBill.getCycleId();
        if (CollectionUtils.isEmpty(modifyPartnerBillDetails)) {
            return NewResponseUtil.makeSuccess(null);
        }
        List<String> partnerUniqueCodes = modifyPartnerBillDetails.stream()
                .map(ModifyPartnerBillDetail::getPartnerUniqueCode)
                .collect(Collectors.toList());
        List<CfBdPartnerBillDO> partnerBillDOS = cfBdPartnerBillService.listByPartnerUniqueCodes(partnerUniqueCodes, cycleId);
        //判断人员是否能修改,当前人员所在组织 - 是否是审核未通过
        List<Long> orgIds = modifyPartnerBill.getBdOrgList()
                .stream()
                .map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toList());
        List<Long> canModifySummaryOrgIds = orgSummaryService.listByOrgIdsAndCycleId(orgIds, cycleId)
                .stream()
                .filter(item -> item.getApproveStatus() == PartnerEnums.BillApproveEnums.wait_approve.getCode())
                .map(CfBdPartnerBillOrgSummaryDO::getOrgId)
                .collect(Collectors.toList());
        Response<Void> checkCanModifyPartnerBill = cfBdPartnerBillService.checkCanModifyPartnerBill(canModifySummaryOrgIds, cycleId, partnerBillDOS);
        if (checkCanModifyPartnerBill.notOk()) {
            return checkCanModifyPartnerBill;
        }

        Map<String, CfBdPartnerBillDO> partnerBillDOSMap = partnerBillDOS.stream().collect(Collectors.toMap(CfBdPartnerBillDO::getPartnerUniqueCode, item -> item));

        //修改当前账单
        for (ModifyPartnerBillDetail modifyPartnerBillDetail : modifyPartnerBillDetails) {
            CfBdPartnerBillDO cfBdPartnerBillDO = new CfBdPartnerBillDO();
            cfBdPartnerBillDO.setLastChangeCaseSalary(modifyPartnerBillDetail.getChangedCaseSalary());
            cfBdPartnerBillDO.setLastChangeAttendSalary(modifyPartnerBillDetail.getChangedAttendSalary());
            cfBdPartnerBillDO.setPartnerUniqueCode(modifyPartnerBillDetail.getPartnerUniqueCode());
            cfBdPartnerBillDO.setCycleId(cycleId);
            cfBdPartnerBillDO.setBillAmount(modifyPartnerBillDetail.getChangedCaseSalary() + modifyPartnerBillDetail.getChangedAttendSalary());

            cfBdPartnerBillService.updateSalaryByPartnerUniqueCode(cfBdPartnerBillDO);

            int caseSalary = Optional.of(partnerBillDOSMap.get(modifyPartnerBillDetail.getPartnerUniqueCode()).getLastChangeCaseSalary()).orElse(0);
            int changeCaseSalary = modifyPartnerBillDetail.getChangedCaseSalary();
            int attendSalary = Optional.of(partnerBillDOSMap.get(modifyPartnerBillDetail.getPartnerUniqueCode()).getLastChangeAttendSalary()).orElse(0);
            int changeAttendSalary = modifyPartnerBillDetail.getChangedAttendSalary();

            if (caseSalary == changeCaseSalary && attendSalary == changeAttendSalary) {
                continue;
            }

            CfBdPartnerBillChangeHistoryDO cfBdPartnerBillChangeHistoryDO = new CfBdPartnerBillChangeHistoryDO();
            cfBdPartnerBillChangeHistoryDO.setCycleId(cycleId);
            cfBdPartnerBillChangeHistoryDO.setPartnerUniqueCode(modifyPartnerBillDetail.getPartnerUniqueCode());
            cfBdPartnerBillChangeHistoryDO.setPartnerVolunteerName(Optional.ofNullable(partnerBillDOSMap.get(modifyPartnerBillDetail.getPartnerUniqueCode()).getPartnerVolunteerName()).orElse(""));
            cfBdPartnerBillChangeHistoryDO.setOperateUniqueCode(modifyPartnerBill.getVolunteer().getUniqueCode());
            cfBdPartnerBillChangeHistoryDO.setOperateName(modifyPartnerBill.getVolunteer().getVolunteerName());
            cfBdPartnerBillChangeHistoryDO.setCaseSalary(caseSalary);
            cfBdPartnerBillChangeHistoryDO.setChangeCaseSalary(changeCaseSalary);
            cfBdPartnerBillChangeHistoryDO.setAttendSalary(attendSalary);
            cfBdPartnerBillChangeHistoryDO.setChangeAttendSalary(changeAttendSalary);
            cfBdPartnerBillChangeHistoryDO.setChangeReason(modifyPartnerBillDetail.getChangeReason());

            //插入账单调整记录
            cfBdPartnerBillChangeHistoryService.insert(cfBdPartnerBillChangeHistoryDO);
        }
        //往上级逐级修改,重新绘制这个几个组织的账单,包含对应负责人账单

        //修改负责人账单
        List<String> uniqueCodeList = partnerBillDOS.stream()
                .map(CfBdPartnerBillDO::getUniqueCode)
                .distinct()
                .collect(Collectors.toList());
        for (String uniqueCode : uniqueCodeList) {
            createBdBill(uniqueCode, cycleId);
        }
        List<Long> needUpdateOrgIds = partnerBillDOS.stream()
                .map(CfBdPartnerBillDO::getOrgId)
                .distinct()
                .collect(Collectors.toList());
        //往上级逐级修改
        for (long needUpdateOrgId : needUpdateOrgIds) {
            List<BdCrmOrganizationDO> organizationDOS = orgReadService.listParentOrgAsChain(needUpdateOrgId);
            Collections.reverse(organizationDOS);
            for (BdCrmOrganizationDO organizationDO : organizationDOS) {
                //更新到这一层级需要停止
                if (canModifySummaryOrgIds.contains(organizationDO.getId())) {
                    break;
                }
                createOrgBill(organizationDO.getParentId(), cycleId);
            }
        }

        return NewResponseUtil.makeSuccess(null);
    }


}
