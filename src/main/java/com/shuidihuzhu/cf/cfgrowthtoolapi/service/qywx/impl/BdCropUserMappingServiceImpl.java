package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdCropUserMappingDO;
import com.shuidihuzhu.cf.dao.qywx.BdCropUserMappingDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.BdCropUserMappingService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 主体userId映射(BdCropUserMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-28 11:33:18
 */
@Service
public class BdCropUserMappingServiceImpl implements BdCropUserMappingService {
   
    @Resource
    private BdCropUserMappingDao bdCropUserMappingDao;

    @Override
    public BdCropUserMappingDO queryById(long id) {
        return bdCropUserMappingDao.queryById(id);
    }
    

    @Override
    public int insert(BdCropUserMappingDO bdCropUserMapping) {
        return bdCropUserMappingDao.insert(bdCropUserMapping);
    }


    @Override
    public boolean deleteById(long id) {
        return bdCropUserMappingDao.deleteById(id) > 0;
    }

    @Override
    public BdCropUserMappingDO queryByUniqueCode(String uniqueCode, int cropId) {
        return bdCropUserMappingDao.queryByUniqueCode(uniqueCode, cropId);
    }

    @Override
    public List<BdCropUserMappingDO> listByUniqueCodeList(List<String> uniqueCodes, int cropId) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Lists.newArrayList();
        }
        return bdCropUserMappingDao.listByUniqueCodeList(uniqueCodes, cropId);
    }
}
