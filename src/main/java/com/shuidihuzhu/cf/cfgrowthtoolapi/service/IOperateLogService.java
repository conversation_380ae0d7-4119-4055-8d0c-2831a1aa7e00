package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfOperatingRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OperateLogSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OperatorLogVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.CfMsgModelRecordVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.MsgQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.OperatingRecordQueryParam;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-10
 *
 */
public interface IOperateLogService {

    int saveOperateLog(CfOperatingRecordDO cfOperatingRecordDO);

    OpResult<CommonResultModel<CfMsgModelRecordVO>> listMsgModelRecord(MsgQueryParam msgQueryParam);

    int countRecordByQueryParam(OperatingRecordQueryParam queryParam);

    OpResult<List<CfOperatingRecordDO>> listRecordByQueryParam(OperatingRecordQueryParam queryParam);

    List<CfOperatingRecordDO> listOptLogByOptKeyAndOptType(String optKey, OperateTypeEnum operateTypeEnum);

    CommonResultModel<OperatorLogVO> getOperateLog(OperateLogSearchModel searchModel, List<Integer> operateTypeList);

    CfOperatingRecordDO findLastAddOpt(String optKey, List<OperateTypeEnum> operateTypeEnumList);

    List<CfOperatingRecordDO> listByOptTypes(String optKey, List<OperateTypeEnum> operateTypeEnumList);

    List<CfOperatingRecordDO> listOptLogBetweenTime(List<OperateTypeEnum> operateTypeEnumList, String startTime, String endTime);

    List<CfOperatingRecordDO> listOptLogGreaterId(String optKey, List<OperateTypeEnum> operateTypeEnumList, long greaterId);

    List<CfOperatingRecordDO> listOptLogBatchOptKeys(List<String> optKeys, int optType, String startTime, String endTime);

    List<CfOperatingRecordDO> listOptLogRecordByKey(String optKey, List<OperateTypeEnum> operateTypeEnumList);

    List<CfOperatingRecordDO> listOptLogByOptKeys(List<String> optKeys, List<OperateTypeEnum> operateTypeEnumList);
}
