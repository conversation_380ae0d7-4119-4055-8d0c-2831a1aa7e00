package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfUserDeviceVersionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfUserDeviceVersionService;
import com.shuidihuzhu.cf.dao.CfUserDeviceVersionDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import springfox.documentation.spring.web.json.Json;

import java.util.Date;

/**
 * 设备 版本记录
 * <AUTHOR>
 */

@Slf4j
@Service
public class CfUserDeviceVersionServiceImpl implements CfUserDeviceVersionService {

    @Autowired
    CfUserDeviceVersionDao cfUserDeviceVersionDao;


    @Override
    public int saveCfUserDeviceVersion(CfUserDeviceVersionDO param) {
        if (param == null || StringUtils.isEmpty(param.getDeviceId()) || StringUtils.isEmpty(param.getPlatform())){
            log.info("saveCfUserDeviceVersion error param :{}", JSON.toJSONString(param));
            return 0;
        }
        return cfUserDeviceVersionDao.insertSelective(param);
    }

    @Override
    public CfUserDeviceVersionDO queryCfUserDeviceByDeviceAndVersion(String deviceId, String version) {
        if (StringUtils.isEmpty(deviceId) || StringUtils.isEmpty(version)){
            return null;
        }
        return cfUserDeviceVersionDao.selectByDeviceIdAndVersion(deviceId,version);
    }


}
