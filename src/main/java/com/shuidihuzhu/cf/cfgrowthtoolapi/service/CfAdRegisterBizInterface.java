package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister;
import com.shuidihuzhu.cf.model.toufang.CfToufangRegister;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by wangsf on 17/4/27.
 */
public interface CfAdRegisterBizInterface {

	int add(CfAdRegister adRegister);

	int onlyAdd(CfAdRegister adRegister);

	List<CfAdRegister> findByTime(Timestamp start, Timestamp end, int offset, int limit);

	/**
	 * 根据手机号与Channel 先判断是否存在了
	 * @param
	 * @return
	 */
	CfAdRegister selectByChannelAndMobile(String mobile, List<String> listChannel);

	CfAdRegister findByMobile(String mobile);

	CfAdRegister getById(long id);

	List<CfToufangRegister> getBetween(long startId, long endId, long anchorId, int limit);
}
