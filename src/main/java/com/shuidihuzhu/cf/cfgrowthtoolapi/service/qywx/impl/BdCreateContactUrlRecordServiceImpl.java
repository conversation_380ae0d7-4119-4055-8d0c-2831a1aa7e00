package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdCreateContactUrlRecordDO;
import com.shuidihuzhu.cf.dao.qywx.BdCreateContactUrlRecordDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.BdCreateContactUrlRecordService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 给顾问生成联系我链接记录(BdCreateContactUrlRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-09 19:56:53
 */
@Service("bdCreateContactUrlRecordService")
public class BdCreateContactUrlRecordServiceImpl implements BdCreateContactUrlRecordService {
   
    @Resource
    private BdCreateContactUrlRecordDao bdCreateContactUrlRecordDao;

    @Override
    public BdCreateContactUrlRecordDO queryById(long id) {
        return bdCreateContactUrlRecordDao.queryById(id);
    }
    

    @Override
    public int insert(BdCreateContactUrlRecordDO bdCreateContactUrlRecord) {
        return bdCreateContactUrlRecordDao.insert(bdCreateContactUrlRecord);
    }

    @Override
    public int update(BdCreateContactUrlRecordDO bdCreateContactUrlRecord) {
        return bdCreateContactUrlRecordDao.update(bdCreateContactUrlRecord);
    }

    @Override
    public boolean deleteById(long id) {
        return bdCreateContactUrlRecordDao.deleteById(id) > 0;
    }

    @Override
    public BdCreateContactUrlRecordDO queryByUniqueCodeAndRobotUserId(String uniqueCode, String robotUserId) {
        return bdCreateContactUrlRecordDao.queryByUniqueCodeAndRobotUserId(uniqueCode, robotUserId);
    }

    @Override
    public int updateExternalUserIdById(long id, String externalUserId) {
        if (StringUtils.isBlank(externalUserId)) {
            return 0;
        }
        return bdCreateContactUrlRecordDao.updateExternalUserIdById(id, externalUserId);
    }

    @Override
    public Set<String> queryExistingUniqueCodesByBatch(List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Collections.emptySet();
        }
        
        List<String> existingUniqueCodes = bdCreateContactUrlRecordDao.queryExistingUniqueCodesByBatch(uniqueCodes);
        return new HashSet<>(existingUniqueCodes);
    }

    @Override
    public boolean cancelLink(long id, String cancelReason) {
        if (StringUtils.isBlank(cancelReason)) {
            return false;
        }
        return bdCreateContactUrlRecordDao.cancelLink(id, cancelReason) > 0;
    }

    @Override
    public List<BdCreateContactUrlRecordDO> queryByRobotUserId(String robotUserId) {
        if (StringUtils.isBlank(robotUserId)) {
            return Collections.emptyList();
        }
        return bdCreateContactUrlRecordDao.queryByRobotUserId(robotUserId);
    }

    @Override
    public List<BdCreateContactUrlRecordDO> queryByUniqueCode(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return Collections.emptyList();
        }
        return bdCreateContactUrlRecordDao.queryByUniqueCode(uniqueCode);
    }
}
