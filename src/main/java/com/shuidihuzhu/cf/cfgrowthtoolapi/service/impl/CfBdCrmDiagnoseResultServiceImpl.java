package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmDiagnoseResultMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmDiagnoseResultDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfBdCrmDiagnoseResultService;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2021/4/26 下午5:17
 */
@Service
public class CfBdCrmDiagnoseResultServiceImpl implements CfBdCrmDiagnoseResultService {

    @Resource
    private CfBdCrmDiagnoseResultMapper cfBdCrmDiagnoseResultMapper;

    @Override
    public int insert(CfBdCrmDiagnoseResultDO record) {
        if (record==null) return 0;
        CfBdCrmDiagnoseResultDO cfBdCrmDiagnoseResultDO = cfBdCrmDiagnoseResultMapper.getCfBdCrmDiagnoseResultDO(record.getOrgId(), record.getDateTime(),record.getCurDayRange(),record.getPreDateTime(),record.getPreDayRange(), record.getDataId(), record.getDataType());
        if (cfBdCrmDiagnoseResultDO!=null) {
            record.setId(cfBdCrmDiagnoseResultDO.getId());
            return cfBdCrmDiagnoseResultMapper.updateByPrimaryKeySelective(record);
        }
        return cfBdCrmDiagnoseResultMapper.insert(record);
    }

    @Override
    public void batchInsert(List<CfBdCrmDiagnoseResultDO> records) {
        records = Optional.ofNullable(records).orElse(Lists.newArrayList()).stream().filter(item -> item !=null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(records)) return;
        records.forEach(item -> insert(item));
    }

    @Override
    public CfBdCrmDiagnoseResultDO selectByPrimaryKey(long id) {
        return cfBdCrmDiagnoseResultMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CfBdCrmDiagnoseResultDO record) {
        return cfBdCrmDiagnoseResultMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public CfBdCrmDiagnoseResultDO getCfBdCrmDiagnoseResultDO(long orgId, String dateTime,int curDayRange,String preDateTime,int preDayRange, String dataId, int dataType) {
        return cfBdCrmDiagnoseResultMapper.getCfBdCrmDiagnoseResultDO(orgId, dateTime, curDayRange, preDateTime, preDayRange, dataId, dataType);
    }

    @Override
    public List<CfBdCrmDiagnoseResultDO> listCfBdCrmDiagnoseResultByOrgIdWithDateTimes(long orgId, List<String> dateTimes, String dataId, Integer dataType) {
        return cfBdCrmDiagnoseResultMapper.listCfBdCrmDiagnoseResultByOrgIdWithDateTimes(orgId, dateTimes, dataId, dataType);
    }

    @Override
    public List<CfBdCrmDiagnoseResultDO> getSubList( String dateTime, CfBdCrmDiagnoseResultDO diagnoseResultDO, boolean needSort) {
        List<CfBdCrmDiagnoseResultDO> subList = cfBdCrmDiagnoseResultMapper.getSubList(dateTime, diagnoseResultDO.getId());
        if (!needSort) return subList;
        if (diagnoseResultDO.getDiagnoseResult().getUpOrDownMarketShare()>=0){
            subList = subList.stream().sorted(Comparator.comparing(CfBdCrmDiagnoseResultDO::getSdUpDownContribution).reversed()).collect(Collectors.toList());
        }else {
            subList = subList.stream().map(item -> {item.getDiagnoseResult().setSdUpDownContribution(item.getSdUpDownContribution()*-1); return item;}).collect(Collectors.toList());
            subList = subList.stream().sorted(Comparator.comparing(CfBdCrmDiagnoseResultDO::getSdUpDownContribution)).collect(Collectors.toList());
        }
        return subList;
    }

    @Override
    public void updateParentId(Long newParentId, Long oldParentId, List<Long> dataIdList, int dataType) {
        cfBdCrmDiagnoseResultMapper.updateParentId(newParentId, oldParentId, dataIdList, dataType);
    }

}
