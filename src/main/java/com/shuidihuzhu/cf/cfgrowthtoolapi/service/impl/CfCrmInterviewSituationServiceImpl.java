package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.GdMapHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmBdInterviewDurationModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmHospitalInterviewDurationModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmOrgHospitalInterviewDurationModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICrowdfundingCityService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfCrmInterviewSituationService;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmBdInterviewSituationMapper;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmOrgHospitalInterviewSituationMapper;
import com.shuidihuzhu.cf.dao.bdcrm.CrmNewHospitalDao;
import com.shuidihuzhu.common.web.util.DateUtil;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmHospitalInterviewSituationMapper;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * @author: wanghui
 * @create: 2021/6/28 下午4:40
 */
@Service
public class CfCrmInterviewSituationServiceImpl implements CfCrmInterviewSituationService {

    @Resource
    private CfCrmBdInterviewSituationMapper cfCrmBdInterviewSituationMapper;
    @Resource
    private CfCrmHospitalInterviewSituationMapper cfCrmHospitalInterviewSituationMapper;
    @Resource
    private CfCrmOrgHospitalInterviewSituationMapper cfCrmOrgHospitalInterviewSituationMapper;
    @Autowired
    private ApolloService apolloService;
    @Autowired
    private ICrowdfundingCityService crowdfundingCityService;
    @Autowired
    private CrmNewHospitalDao crmNewHospitalDao;


    @Override
    public CfCrmBdInterviewSituationInfoVO getInterviewSituation(List<String> searchUniqueCodeList, int role,int showType, int maxRank) {
        List<Integer> roleList = Lists.newArrayList(role);
        CfCrmBdInterviewSituationInfoVO situationInfoVO = cfCrmBdInterviewSituationMapper.getInterviewSituation(searchUniqueCodeList, roleList, DateUtil.getCurrentDateStr());
        List<CfCrmBdInterviewSituationVO> bdInterviewSituationList = cfCrmBdInterviewSituationMapper.listBdInterviewSituationForShow(searchUniqueCodeList, roleList, showType, maxRank, DateUtil.getCurrentDateStr());
        if (CollectionUtils.isEmpty(bdInterviewSituationList)) {
            return situationInfoVO;
        }
        situationInfoVO.setMaxRank(bdInterviewSituationList.get(bdInterviewSituationList.size() - 1).getRank());
        situationInfoVO.setBdInterviewSituationList(bdInterviewSituationList);
        return situationInfoVO;
    }

    @Override
    public CfCrmHospitalInterviewSituationInfoVO getHospitalInterviewSituation(List<Integer> searchCityIdList, int maxRank) {
        CfCrmHospitalInterviewSituationInfoVO situationInfoVO = cfCrmHospitalInterviewSituationMapper.getHospitalInterviewSituation(searchCityIdList, maxRank, DateUtil.getCurrentDateStr());
        List<CfCrmHospitalInterviewSituationVO> hospitalInterviewSituationList = cfCrmHospitalInterviewSituationMapper.listHospitalInterviewSituation(searchCityIdList, maxRank, DateUtil.getCurrentDateStr());
        if (CollectionUtils.isEmpty(hospitalInterviewSituationList)) {
            return situationInfoVO;
        }
        situationInfoVO.setMaxRank(hospitalInterviewSituationList.get(hospitalInterviewSituationList.size() - 1).getRank());
        situationInfoVO.setHospitalInterviewSituationList(hospitalInterviewSituationList);
        return situationInfoVO;
    }

    @Override
    public CfCrmHospitalInterviewSituationInfoVO getHospitalInterviewSituationByVhospitalCodeList(List<String> vhospitalCodeList, int maxRank) {
        CfCrmHospitalInterviewSituationInfoVO situationInfoVO = cfCrmHospitalInterviewSituationMapper.getHospitalInterviewSituationByVhospitalCodeList(vhospitalCodeList, maxRank, DateUtil.getCurrentDateStr());
        List<CfCrmHospitalInterviewSituationVO> hospitalInterviewSituationList = cfCrmHospitalInterviewSituationMapper.listHospitalInterviewSituationByVhospitalCodeList(vhospitalCodeList, maxRank, DateUtil.getCurrentDateStr());
        if (CollectionUtils.isEmpty(hospitalInterviewSituationList)) return situationInfoVO;
        situationInfoVO.setMaxRank(hospitalInterviewSituationList.get(hospitalInterviewSituationList.size()-1).getRank());
        situationInfoVO.setHospitalInterviewSituationList(hospitalInterviewSituationList);
        return situationInfoVO;
    }

    @Override
    public CfCrmHospitalInterviewSituationInfoVO getHospitalInterviewSituationByOrgIdList(List<Long> orgIdList, int maxRank) {
        CfCrmHospitalInterviewSituationInfoVO situationInfoVO = new CfCrmHospitalInterviewSituationInfoVO();
        List<CfCrmHospitalInterviewSituationVO> hospitalInterviewSituationList = cfCrmOrgHospitalInterviewSituationMapper.listHospitalInterviewSituationByOrgIdList(orgIdList, maxRank, DateUtil.getCurrentDateStr());
        if (CollectionUtils.isEmpty(hospitalInterviewSituationList)) return situationInfoVO;
        situationInfoVO.setMaxRank(hospitalInterviewSituationList.get(hospitalInterviewSituationList.size()-1).getRank());
        situationInfoVO.setHospitalInterviewSituationList(hospitalInterviewSituationList);
        return situationInfoVO;
    }


    @Override
    public long countFromBdInterviewByDateTime(String currentDateStr) {
        return cfCrmBdInterviewSituationMapper.countFromBdInterviewByDateTime(currentDateStr);
    }

    @Override
    public void batchInsertBdInterview(List<CfCrmBdInterviewSituationDO> modelList) {
        if (CollectionUtils.isEmpty(modelList)) {
            return;
        }
        // 处理标签
        modelList.stream().forEach(model -> {
            model.setLabelType(calcLabelType(model.getType(), model.getDuration(), model.getLatestMeanDuration(), apolloService.getInterviewRate()));
        });
        List<String> uniqueCodeListInDB = cfCrmBdInterviewSituationMapper.listUniqueCodeForCheck(modelList.get(0).getDateTime(),
                modelList.stream().map(CfCrmBdInterviewSituationDO::getUniqueCode).collect(Collectors.toList()));
        List<CfCrmBdInterviewSituationDO> needInsertList;
        List<CfCrmBdInterviewSituationDO> needUpdateList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(uniqueCodeListInDB)){
            needInsertList = modelList;
        }else {
            needUpdateList = modelList.stream().filter(item -> uniqueCodeListInDB.contains(item.getUniqueCode())).collect(Collectors.toList());
            needInsertList = modelList.stream().filter(item -> !uniqueCodeListInDB.contains(item.getUniqueCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(needInsertList)) {
            cfCrmBdInterviewSituationMapper.batchInsertBdInterview(needInsertList);
        }
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            needUpdateList.parallelStream().forEach(item -> cfCrmBdInterviewSituationMapper.updateByDateTimeWithUniqueCode(item));
        }
    }

    @Override
    public List<CfCrmInterviewSituationRankModel> listBdInterviewSituationByDateTime(String dateTime) {
        List<CfCrmInterviewSituationRankModel> result = Lists.newArrayList();
        List<CfCrmInterviewSituationRankModel> list = cfCrmBdInterviewSituationMapper.listRankModelByDateTime(dateTime, 0);
        while (CollectionUtils.isNotEmpty(list)) {
            result.addAll(list);
            list = cfCrmBdInterviewSituationMapper.listRankModelByDateTime(dateTime, list.get(list.size()-1).getId());
        }
        return result;
    }

    @Override
    public List<Long> listBdInterviewIdByDateTime(String dateTime) {
        return cfCrmBdInterviewSituationMapper.listBdInterviewIdByDateTime(dateTime).stream().sorted(Long::compareTo).collect(Collectors.toList());
    }

    @Override
    public List<CfCrmBdInterviewDurationModel> listBdInterviewByIdRange(Long minId, Long maxId) {
        return cfCrmBdInterviewSituationMapper.listBdInterviewByIdRange(minId, maxId);
    }

    @Override
    public void batchUpdateBdDuration(List<CfCrmBdInterviewDurationModel> cfCrmBdInterviewSituationDOList) {
        if (CollectionUtils.isEmpty(cfCrmBdInterviewSituationDOList)) {
            return;
        }
        cfCrmBdInterviewSituationDOList.parallelStream().forEach(item -> {
            item.setLabelType(calcLabelType(item.getType(), item.getDuration(), item.getLatestMeanDuration(), apolloService.getInterviewRate()));
        });
        Lists.partition(cfCrmBdInterviewSituationDOList, 100).parallelStream().forEach(item -> cfCrmBdInterviewSituationMapper.batchUpdateDuration(item));
    }

    // 低于历史均值时，标记”下降“；覆盖时长为0 时，标记”未走访“；未登录、未授权时，标记“异常”。
    private int calcLabelType(Integer type, Integer duration, Integer latestMeanDuration, Double rate) {
        if (type==null) return 0;
        if (type == CfCrmBdLocationConditionDO.TypeEnum.WARN.getType()) {
            return CfCrmBdInterviewSituationDO.LabelEnum.EXCEPTION.getLabelType();
        }else if (duration!=null && duration==0) {
            return CfCrmBdInterviewSituationDO.LabelEnum.UN_DATA.getLabelType();
        }else if (duration!=null && latestMeanDuration!=null && duration<(latestMeanDuration* Optional.ofNullable(rate).orElse(1d))){
            return CfCrmBdInterviewSituationDO.LabelEnum.DOWN.getLabelType();
        }
        return 0;
    }

    @Override
    public void batchUpdateBdRank(List<CfCrmInterviewSituationRankModel> bdInterviewSituationList) {
        if (CollectionUtils.isEmpty(bdInterviewSituationList)) return;
        Lists.partition(bdInterviewSituationList, 100).parallelStream().forEach(item -> cfCrmBdInterviewSituationMapper.batchUpdateRank(item));
    }

    @Override
    public List<CfCrmHospitalInterviewDurationModel> listHospitalInterviewByIdRange(long minId, long maxId) {
        return cfCrmHospitalInterviewSituationMapper.listHospitalInterviewByIdRange(minId, maxId);
    }
    @Override
    public List<CfCrmOrgHospitalInterviewDurationModel> listOrgHospitalInterviewByIdRange(long minId, long maxId) {
        return cfCrmOrgHospitalInterviewSituationMapper.listHospitalInterviewByIdRange(minId, maxId);
    }

    @Override
    public List<Long> listHospitalInterviewIdByDateTime(String dateTime) {
        return cfCrmHospitalInterviewSituationMapper.listHospitalInterviewIdByDateTime(dateTime).stream().sorted(Long::compareTo).collect(Collectors.toList());
    }
    @Override
    public List<Long> listOrgHospitalInterviewIdByDateTime(String dateTime) {
        return cfCrmOrgHospitalInterviewSituationMapper.listHospitalInterviewIdByDateTime(dateTime).stream().sorted(Long::compareTo).collect(Collectors.toList());
    }
    @Override
    public List<CfCrmInterviewSituationRankModel> listHospitalRankModelByDateTime(String dateTime) {
        List<CfCrmInterviewSituationRankModel> result = Lists.newArrayList();
        List<CfCrmInterviewSituationRankModel> list = cfCrmHospitalInterviewSituationMapper.listRankModelByDateTime(dateTime, 0);
        while (CollectionUtils.isNotEmpty(list)) {
            result.addAll(list);
            list = cfCrmHospitalInterviewSituationMapper.listRankModelByDateTime(dateTime, list.get(list.size()-1).getId());
        }
        return result;
    }
    @Override
    public List<CfCrmInterviewSituationRankModel> listOrgHospitalRankModelByDateTime(String dateTime) {
        List<CfCrmInterviewSituationRankModel> result = Lists.newArrayList();
        List<CfCrmInterviewSituationRankModel> list = cfCrmOrgHospitalInterviewSituationMapper.listRankModelByDateTime(dateTime, 0);
        while (CollectionUtils.isNotEmpty(list)) {
            result.addAll(list);
            list = cfCrmOrgHospitalInterviewSituationMapper.listRankModelByDateTime(dateTime, list.get(list.size()-1).getId());
        }
        return result;
    }

    @Override
    public void batchUpdateHospitalDuration(List<CfCrmHospitalInterviewDurationModel> interviewSituationList) {
        if (CollectionUtils.isEmpty(interviewSituationList)) {
            return;
        }
        Lists.partition(interviewSituationList, 100).parallelStream().forEach(item -> cfCrmHospitalInterviewSituationMapper.batchUpdateHospitalDuration(item));
    }

    @Override
    public void batchUpdateOrgHospitalDuration(List<CfCrmOrgHospitalInterviewDurationModel> interviewSituationList) {
        if (CollectionUtils.isEmpty(interviewSituationList)) {
            return;
        }
        Lists.partition(interviewSituationList, 100).parallelStream().forEach(item -> cfCrmOrgHospitalInterviewSituationMapper.batchUpdateOrgHospitalDuration(item));
    }

    @Override
    public void batchUpdateHospitalRank(List<CfCrmInterviewSituationRankModel> interviewSituationList) {
        if (CollectionUtils.isEmpty(interviewSituationList)) {
            return;
        }
        Lists.partition(interviewSituationList, 100).parallelStream().forEach(item -> cfCrmHospitalInterviewSituationMapper.batchUpdateHospitalRank(item));
    }

    @Override
    public void batchUpdateOrgHospitalRank(List<CfCrmInterviewSituationRankModel> interviewSituationList) {
        if (CollectionUtils.isEmpty(interviewSituationList)) {
            return;
        }
        Lists.partition(interviewSituationList, 100).parallelStream().forEach(item -> cfCrmOrgHospitalInterviewSituationMapper.batchUpdateRank(item));
    }

    @Override
    public long countFromHospitalInterviewByDateTime(String currentDateStr) {
        return cfCrmHospitalInterviewSituationMapper.countFromHospitalInterviewByDateTime(currentDateStr);
    }

    @Override
    public void batchInsertHospitalInterview(List<CfCrmHospitalInterviewSituationDO> situationDOList) {
        if (CollectionUtils.isEmpty(situationDOList)) {
            return;
        }
        List<String> vhospitalCodeListInDB = cfCrmHospitalInterviewSituationMapper.listVhospitalCodeForCheck(situationDOList.get(0).getDateTime(),
                situationDOList.stream().map(CfCrmHospitalInterviewSituationDO::getVhospitalCode).collect(Collectors.toList()));
        List<CfCrmHospitalInterviewSituationDO> needInsertList;
        List<CfCrmHospitalInterviewSituationDO> needUpdateList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(vhospitalCodeListInDB)) {
            needInsertList = situationDOList;
        } else {
            needUpdateList = situationDOList.stream().filter(item -> vhospitalCodeListInDB.contains(item.getVhospitalCode())).collect(Collectors.toList());
            needInsertList = situationDOList.stream().filter(item -> !vhospitalCodeListInDB.contains(item.getVhospitalCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(needInsertList)) {
            cfCrmHospitalInterviewSituationMapper.batchInsertBdInterview(needInsertList);
        }
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            needUpdateList.parallelStream().forEach(item -> cfCrmHospitalInterviewSituationMapper.updateByDateTimeWithVhospitalCode(item));
        }
    }

    @Override
    public void saveCfCrmOrgHospitalInterviewSituation(HospitalSimpleVO hospitalSimpleVO, BdCrmOrganizationDO organizationDO) {
        if (hospitalSimpleVO==null || StringUtil.isBlank(hospitalSimpleVO.getVHospitalCode())) return;
        // 判断是否为标准库 且已人工check的医院
        GdMapHospitalDO gdMapHospitalDO = crmNewHospitalDao.getStandardHospitalDOByVVHospitalcode(hospitalSimpleVO.getVHospitalCode());
        if (gdMapHospitalDO==null) return;
        String currentDateStr = DateUtil.getCurrentDateStr();
        // 判断是否已存在
        Long id = cfCrmOrgHospitalInterviewSituationMapper.getByDateTimeWithVhospitalCode(currentDateStr, organizationDO.getParentId(), gdMapHospitalDO.getVhospitalCode());
        if (id!=null) return;
        CfCrmOrgHospitalInterviewSituationDO cfCrmOrgHospitalInterviewSituationDO = new CfCrmOrgHospitalInterviewSituationDO();
        cfCrmOrgHospitalInterviewSituationDO.setDateTime(currentDateStr);
        cfCrmOrgHospitalInterviewSituationDO.setOrgId(organizationDO.getParentId());
        cfCrmOrgHospitalInterviewSituationDO.setCityId(Optional.ofNullable(crowdfundingCityService.getCityNameMapCityId().get(hospitalSimpleVO.getGdCityname())).map(Long::valueOf).orElse(0L));
        cfCrmOrgHospitalInterviewSituationDO.setCityName(gdMapHospitalDO.getGdCityname());
        cfCrmOrgHospitalInterviewSituationDO.setVhospitalCode(gdMapHospitalDO.getVhospitalCode());
        cfCrmOrgHospitalInterviewSituationDO.setHospitalName(gdMapHospitalDO.getGdName());
        cfCrmOrgHospitalInterviewSituationMapper.insert(cfCrmOrgHospitalInterviewSituationDO);
    }

}
