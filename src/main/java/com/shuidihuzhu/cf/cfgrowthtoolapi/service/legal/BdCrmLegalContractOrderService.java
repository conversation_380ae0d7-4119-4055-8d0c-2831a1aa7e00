package com.shuidihuzhu.cf.cfgrowthtoolapi.service.legal;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.legal.BdCrmLegalContractOrderDO;

/**
 * 法律援助合同订单(BdCrmLegalContractOrder)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-19 16:48:37
 */
public interface BdCrmLegalContractOrderService {

    BdCrmLegalContractOrderDO queryById(long id);

    int insert(BdCrmLegalContractOrderDO bdCrmLegalContractOrder);

    int updatePayInfo(BdCrmLegalContractOrderDO bdCrmLegalContractOrder);

    boolean deleteById(long id);

    BdCrmLegalContractOrderDO getByOrderId(String orderId);

}
