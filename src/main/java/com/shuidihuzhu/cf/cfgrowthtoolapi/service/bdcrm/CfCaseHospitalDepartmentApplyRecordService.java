package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGrowthtoolApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfCaseHospitalDepartmentApplyRecordDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfGrowthtoolApproveContentVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.HospitalAreaBuildingParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.HospitalBuildingDepartmentParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;

/**
 * <AUTHOR>
 * @date 2024/9/27  14:23
 */
public interface CfCaseHospitalDepartmentApplyRecordService {

    CfCaseHospitalDepartmentApplyRecordDo get(long id);

    Response<Void> updateBuildingPicAudit(CrowdfundingVolunteer volunteer, HospitalAreaBuildingParam areaBuilding);

    Response<Void> deleteDepartmentAudit(CrowdfundingVolunteer volunteer, int departmentId, String adjustmentReason);

    Response<Void> editDepartmentAudit(CrowdfundingVolunteer volunteer, HospitalBuildingDepartmentParam departmentDO);

    void handleByLeader(CfGrowthtoolApproveDO cfGrowthtoolApproveDO, boolean pass);

    CfGrowthtoolApproveContentVo.CfCaseHospitalDepartmentApplyRecordContent buildCfCaseHospitalDepartmentApplyRecordContent(String approveContent);

    OpResult<String> checkRepeatability(CfGrowthtoolApproveDO cfGrowthtoolApproveDO);
}
