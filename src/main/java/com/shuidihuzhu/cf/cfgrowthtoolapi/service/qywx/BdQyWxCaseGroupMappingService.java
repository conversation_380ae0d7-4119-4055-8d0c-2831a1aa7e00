package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdQyWxCaseGroupMappingDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CfWeChatQywxChatGroupInfo;

import java.util.Date;
import java.util.List;

/**
 * 案例和企业微信群聊绑定信息(BdQyWxCaseGroupMapping)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-11 11:33:18
 */
public interface BdQyWxCaseGroupMappingService {

    void createGroupBind(CrowdfundingInfo crowdfundingInfo, CfBdCaseInfoDo cfBdCaseInfoDo);

    void bindGroupForOnline(CfWeChatQywxChatGroupInfo cfWeChatQywxChatGroupInfo);

    // 创建群聊后更新群聊ID
    void updateAfterCreate(BdQyWxCaseGroupMappingDO bdQyWxCaseGroupMapping);

    int insert(BdQyWxCaseGroupMappingDO bdQyWxCaseGroupMapping);
    
    /**
     * 根据案例ID软删除群聊绑定信息(将is_delete设为1)
     *
     * @param caseId 案例ID
     * @return 是否删除成功
     */
    boolean deleteByCaseId(int caseId);

    // 更新筹款人 externalUserId
    void updateExternalUserId(String chatId);

    BdQyWxCaseGroupMappingDO queryByCaseId(int caseId);

    BdQyWxCaseGroupMappingDO queryByChatId(String chatId);

    List<BdQyWxCaseGroupMappingDO> queryByUniqueCode(String uniqueCode);

    List<BdQyWxCaseGroupMappingDO> queryByUserId(String userId);
    
    /**
     * 根据创建时间范围查询企业微信群聊绑定信息
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 企业微信群聊绑定信息列表
     */
    List<BdQyWxCaseGroupMappingDO> queryByCreateTimeRange(Date startDate, Date endDate);
    
    /**
     * 根据任务ID查询企业微信群聊绑定信息
     *
     * @param taskId 任务ID
     * @return 企业微信群聊绑定信息
     */
    BdQyWxCaseGroupMappingDO queryByTaskId(String taskId);


    /**
     * 查询指定时间范围内拉群失败的记录（chat_id为空）
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 拉群失败的记录列表
     */
    List<BdQyWxCaseGroupMappingDO> queryFailedGroupsByTimeRange(Date startDate, Date endDate);

    /**
     * 处理拉群失败的记录：删除记录并重新创建群
     *
     * @param startTime 开始时间字符串 格式：yyyy-MM-dd HH:mm:ss
     * @param endTime   结束时间字符串 格式：yyyy-MM-dd HH:mm:ss
     * @return 处理结果信息
     */
    String processFailedGroupRecords(String startTime, String endTime);

    /**
     * 根据机器人用户ID统计拉群数量
     * @param robotUserId 机器人用户ID
     * @return 拉群数量
     */
    int countByRobotUserId(String robotUserId);
}