package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.ICfWeaponExtService;
import com.shuidihuzhu.cf.dao.weapon.CfWeaponExtDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-12-06 7:45 下午
 **/
@Service
public class CfWeaponExtServiceImpl implements ICfWeaponExtService {

    @Autowired
    private CfWeaponExtDao cfWeaponExtDao;


    @Override
    public int insertBatch(List<CfWeaponExtDO> weaponExtDOList) {
        if (CollectionUtils.isEmpty(weaponExtDOList)) {
            return 0;
        }
        return cfWeaponExtDao.insertBatch(weaponExtDOList);
    }

    @Override
    public int update(CfWeaponExtDO cfWeaponExtDO) {
        return 0;
    }

    @Override
    public int batchDelete(int weaponId) {
        return cfWeaponExtDao.batchDelete(weaponId);
    }

    @Override
    public List<CfWeaponExtDO> listByWeaponId(int weaponId) {
        return cfWeaponExtDao.listByWeaponId(weaponId);
    }


}
