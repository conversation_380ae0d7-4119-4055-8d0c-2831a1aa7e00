package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IWorkWeiXinDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGrowthtoolApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.ApproveContentSearchDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.SeaApproveContentVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.ApproveContentDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfGrowthtoolApproveService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.BdCrmContextUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.CfGrowthtoolApproveDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @author: wanghui
 * @create: 2020-06-03 15:36
 */
@Service
public class CfGrowthtoolApproveService implements ICfGrowthtoolApproveService {
	@Autowired
	private CfGrowthtoolApproveDao cfGrowthtoolApproveDao;

	@Autowired
	private IAppPushCrmCaseMsgService appPushCrmCaseMsgServiceImpl;

	@Autowired
	private IWorkWeiXinDelegate workWeiXinDelegate;

	@Override
	public int saveApproveContent(CfGrowthtoolApproveDO cfGrowthtoolApproveDO) {
		return cfGrowthtoolApproveDao.insert(cfGrowthtoolApproveDO);
	}

	@Override
	public List<CfGrowthtoolApproveDO> getApproveContent(String approveMis, String approveUniqueCode, int approveStatus) {
		//兼容使用mis
		if (StringUtils.isNotBlank(approveMis)) {
			return cfGrowthtoolApproveDao.getApproveContent(approveMis, approveStatus);
		}
		//使用uniqueCode兜底
		return cfGrowthtoolApproveDao.getApproveContentByUniqueCode(approveUniqueCode, approveStatus);
	}

	@Override
	public CfGrowthtoolApproveDO getApproveContentById(Long id) {
		return cfGrowthtoolApproveDao.getApproveContentById(id);
	}

	@Override
	public int updateApproveStatus(Long id, Integer approveStatus) {
		return cfGrowthtoolApproveDao.updateApproveStatus(id, approveStatus);
	}

	@Override
	public int updateApproveStatus(Long id, Integer approveStatus, String remark, String approveName) {
		return cfGrowthtoolApproveDao.updateApproveStatusWithRemark(id,approveStatus, remark,approveName);
	}
	@Override
	public int updateApproveStatusWithOrgPath(Long id, Integer approveStatus, String orgPath) {
		return cfGrowthtoolApproveDao.updateApproveStatusWithOrgPath(id,approveStatus, orgPath);
	}
	@Override
	public CfGrowthtoolApproveDO getGwApproveContent(String volunteerUniqueCode,
													 Integer type){
		return cfGrowthtoolApproveDao.getGwApproveContent(volunteerUniqueCode, type);
	}

	@Override
	public List<CfGrowthtoolApproveDO> listByTypeAndUniqueCode(Integer type, String volunteerUniqueCode, Integer approveStatus) {
		if (StringUtils.isEmpty(volunteerUniqueCode)) {
			return Lists.newArrayList();
		}
		return cfGrowthtoolApproveDao.listByTypeAndUniqueCode(type, volunteerUniqueCode, approveStatus);
	}

	@Override
	public List<CfGrowthtoolApproveDO> listByTypeAndApproveUniqueCode(Integer type, String approveUniqueCode, Integer approveStatus) {
		if (StringUtils.isEmpty(approveUniqueCode)) {
			return Lists.newArrayList();
		}
		return cfGrowthtoolApproveDao.listByTypeAndApproveUniqueCode(type, approveUniqueCode, approveStatus);
	}

	@Override
	public CfGrowthtoolApproveDO getWaitApproveContentByType(String volunteerUniqueCode,
																   Integer type){
		return cfGrowthtoolApproveDao.getWaitApproveContentByType(volunteerUniqueCode, type);
	}

	@Override
	public CfGrowthtoolApproveDO saveApproveContent(String approveContent, int type, CrowdfundingVolunteer crowdfundingVolunteer, CrowdfundingVolunteer superiorVolunteer) {
		CfGrowthtoolApproveDO cfGrowthtoolApproveDO = CfGrowthtoolApproveDO.builder()
				.approveContent(approveContent)
				.mis(crowdfundingVolunteer.getMis())
				.volunteerName(crowdfundingVolunteer.getVolunteerName())
				.volunteerUniqueCode(crowdfundingVolunteer.getUniqueCode())
				.approveMis(superiorVolunteer == null ? "system" : superiorVolunteer.getMis())
				.approveUniqueCode(superiorVolunteer == null ? "system" : superiorVolunteer.getUniqueCode())
				.approveName(superiorVolunteer == null ? "system" : superiorVolunteer.getVolunteerName())
				.approveStatus(CfGrowthtoolApproveDO.ApproveStatusEnum.COMMIT_WAIT_APPROVE.getStatus())
				.type(type)
				.role(crowdfundingVolunteer.getLevel())
				.build();
		if (type == CfGrowthtoolApproveDO.ApproveTypeEnum.APPROVE_HONOUR_PIC.getType() && CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel().equals(crowdfundingVolunteer.getLevel())) {
			cfGrowthtoolApproveDO.setApproveMis("operationalStaff");
		}
		saveApproveContent(cfGrowthtoolApproveDO);
		return cfGrowthtoolApproveDO;
	}


	@Override
	public CfGrowthtoolApproveDO saveOrUpdateApproveContent(String approveContent, int type, CrowdfundingVolunteer crowdfundingVolunteer, CrowdfundingVolunteer superiorVolunteer){
		CfGrowthtoolApproveDO waitApproveContentByType = this.getWaitApproveContentByType(crowdfundingVolunteer.getUniqueCode(), type);
		if (waitApproveContentByType==null){
			waitApproveContentByType = saveApproveContent(approveContent, type, crowdfundingVolunteer, superiorVolunteer);
		}else {
			waitApproveContentByType.setApproveContent(approveContent);
			waitApproveContentByType.setApproveStatus(CfGrowthtoolApproveDO.ApproveStatusEnum.COMMIT_WAIT_APPROVE.getStatus());
			waitApproveContentByType.setApproveMis(superiorVolunteer == null ? "system" : superiorVolunteer.getMis());
			waitApproveContentByType.setApproveUniqueCode(superiorVolunteer == null ? "system" : superiorVolunteer.getUniqueCode());
			waitApproveContentByType.setApproveName(superiorVolunteer == null ? "system" : superiorVolunteer.getVolunteerName());
			this.updateApproveContent(waitApproveContentByType);
		}
		CfGrowthtoolApproveDO.ApproveTypeEnum approveTypeEnum = CfGrowthtoolApproveDO.ApproveTypeEnum.parse(type);
		if (StringUtils.isNotBlank(approveContent) && superiorVolunteer!=null && approveTypeEnum!=null && approveTypeEnum.isNeedSendMsg()) {
			// 发消息通知省级经理
			appPushCrmCaseMsgServiceImpl.sendApproveContentMsg(crowdfundingVolunteer.getVolunteerName(),superiorVolunteer);
		}
		return waitApproveContentByType;
	}

	@Override
	public List<CfGrowthtoolApproveDO> getApproveContentByUniqueCodeAndStatus(String uniqueCode, int approveStatus) {
		return cfGrowthtoolApproveDao.getContentByUniqueCodeAndStatus(uniqueCode, approveStatus);
	}

	@Override
	public int updateApproveContent(CfGrowthtoolApproveDO waitApproveContentByType) {
		return cfGrowthtoolApproveDao.updateApproveStatusWithApproveContent(waitApproveContentByType);
	}

	@Override
	public void autoRejectForDimission(String uniqueCode){
		if (StringUtil.isBlank(uniqueCode)) return;
		cfGrowthtoolApproveDao.autoRejectForDimission(uniqueCode);
	}

	@Override
	public long countApproveContent(ApproveContentSearchDTO approveContentSearchDTO) {
		return cfGrowthtoolApproveDao.countApproveContent(approveContentSearchDTO);
	}

	@Override
	public List<SeaApproveContentVo> listApproveContent(ApproveContentSearchDTO approveContentSearchDTO) {
		return cfGrowthtoolApproveDao.listApproveContent(approveContentSearchDTO);
	}
	@Override
	public CfGrowthtoolApproveDO processData(CfGrowthtoolApproveDO cfGrowthtoolApproveDO, ApproveContentDTO approveContentDTO) {
		if (approveContentDTO.getType() == CfGrowthtoolApproveDO.ApproveTypeEnum.APPROVE_HEADER.getType()
				|| approveContentDTO.getType() == CfGrowthtoolApproveDO.ApproveTypeEnum.APPROVE_HONOUR_PIC.getType()
				|| approveContentDTO.getType() == CfGrowthtoolApproveDO.ApproveTypeEnum.APPROVE_WORK_CARD.getType()) {
			cfGrowthtoolApproveDO.setRemark(approveContentDTO.processRemark());
		} else {
			cfGrowthtoolApproveDO.setRemark(Optional.ofNullable(approveContentDTO.getReason()).orElse(""));
		}
		cfGrowthtoolApproveDO.setApproveStatus(approveContentDTO.getApproveStatus());
		if (StringUtils.isNotBlank(approveContentDTO.getImageUrls())){
			cfGrowthtoolApproveDO.setApproveContent(approveContentDTO.getImageUrls());
		}
		if (cfGrowthtoolApproveDO.getType() == CfGrowthtoolApproveDO.ApproveTypeEnum.APPROVE_WEAPON.getType()) {
			cfGrowthtoolApproveDO.setApproveContent(Optional.ofNullable(approveContentDTO.getReason()).orElse(""));
		}
		return cfGrowthtoolApproveDO;
	}

	@Override
	public CfGrowthtoolApproveDO getBeforeCommitApproveRecord(Long id, String uniqueCode, String approveUniqueCode, Integer type) {
		return cfGrowthtoolApproveDao.getBeforeCommitApproveRecord(id, uniqueCode, approveUniqueCode, type);
	}

	@Override
	public void updateApproveContent(Long id, String approveContent) {
		cfGrowthtoolApproveDao.updateApproveContent(id, approveContent);
	}

	@Override
	public void notifyHasUndoApprove(CrowdfundingVolunteer volunteer) {
		List<CfGrowthtoolApproveDO> approveContentList = getApproveContent(volunteer.getMis(), volunteer.getUniqueCode(), CfGrowthtoolApproveDO.ApproveStatusEnum.COMMIT_WAIT_APPROVE.getStatus());
		if (CollectionUtils.isNotEmpty(approveContentList)) {
			workWeiXinDelegate.sendByUser(Lists.newArrayList( "liusihan"), volunteer.getVolunteerName() + "(" + volunteer.getMis()  + ")已离职，有待办需要转移，请处理");
		}
	}

	@Override
	public void notifyDowngradingLevel(String role, String dbRole, CrowdfundingVolunteer volunteer) {
		List<CfGrowthtoolApproveDO> approveContentList = getApproveContent(volunteer.getMis(), volunteer.getUniqueCode(), CfGrowthtoolApproveDO.ApproveStatusEnum.COMMIT_WAIT_APPROVE.getStatus());
		if (CollectionUtils.isNotEmpty(approveContentList)) {
			workWeiXinDelegate.sendByUser(Lists.newArrayList("liusihan"), volunteer.getVolunteerName() + "(" + volunteer.getMis()  + ")已从" + dbRole + "降职为" + role + "，有待办需要转移");
		}
	}

	@Override
	public List<CfGrowthtoolApproveDO> listByContentAndType(int type, String content) {
		return cfGrowthtoolApproveDao.listByContentAndType(type, content);
	}
	@Override
	public long countHistoryApproveContent(String uniqueCode, List<Integer> statusList, Integer contentType) {
		if (StringUtils.isBlank(uniqueCode)) {
			return 0;
		}
		//只查询最近60天的数据
		String startTime = DateTime.now().withTimeAtStartOfDay().minusDays(60).toString(GrowthtoolUtil.ymdfmt);
		return cfGrowthtoolApproveDao.countHistoryApproveContent(uniqueCode, statusList, contentType, startTime);
	}

	@Override
	public List<CfGrowthtoolApproveDO> pageHistoryApproveContent(String uniqueCode, List<Integer> statusList, Integer contentType, int offset, int limit) {
		if (StringUtils.isBlank(uniqueCode)) {
			return Lists.newArrayList();
		}
		String startTime = DateTime.now().withTimeAtStartOfDay().minusDays(60).toString(GrowthtoolUtil.ymdfmt);
		return cfGrowthtoolApproveDao.pageHistoryApproveContent(uniqueCode, statusList, contentType, startTime, offset, limit);
	}


}
