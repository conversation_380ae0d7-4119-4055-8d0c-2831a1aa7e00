package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.memberBindOrg;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.MemberOrgRelationParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.handler.OptLimitHandler;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-05-13 15:29
 **/
@Slf4j
public class DeleteMemberLimitImpl extends AbstractOptMemberBindLimit {


    public DeleteMemberLimitImpl(OptLimitHandler optLimitHandler) {
        super(optLimitHandler.getCrmOrganizationService(), optLimitHandler.getCfVolunteerService(), optLimitHandler.getRelationService());
    }

    @Override
    Response<Boolean> doCheckOptMemberBind(MemberOrgRelationParam memberOrgRelationParam) {
        log.info("开始删除人员校验");
        return NewResponseUtil.makeSuccess(true);
    }

    @Override
    public List<OrganizationMemberOptEnum> getNeedCheckOptEnums() {
        return Lists.newArrayList(OrganizationMemberOptEnum.delete_member);
    }
}
