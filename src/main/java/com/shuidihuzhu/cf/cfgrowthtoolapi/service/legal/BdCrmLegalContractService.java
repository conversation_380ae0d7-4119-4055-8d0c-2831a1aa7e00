package com.shuidihuzhu.cf.cfgrowthtoolapi.service.legal;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.legal.BdCrmLegalContractDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.legal.LegalContractParam;

import java.util.List;

/**
 * 法律援助合同(BdCrmLegalContract)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-19 16:48:17
 */
public interface BdCrmLegalContractService {

    BdCrmLegalContractDO queryById(long id);

    int insert(BdCrmLegalContractDO bdCrmLegalContract);

    int updatePayInfo(BdCrmLegalContractDO bdCrmLegalContract);

    boolean cancelById(long id);

    List<BdCrmLegalContractDO> getByNameAndPhone(String contractName, String phone);

    int count(LegalContractParam legalContractParam);

    List<BdCrmLegalContractDO> page(LegalContractParam legalContractParam);

}
