package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfBdcrmVisitRecordDO;

import java.util.List;

public interface CfBdcrmVisitRecordService {

    /**
     * 获取被访问人到记录
     * @return
     */
    List<CfBdcrmVisitRecordDO> queryRecordByBeVisitUniqueCode(List<String> uniqueCodes);
    /**
     * 获取访问人到记录
     * @return
     */
    List<CfBdcrmVisitRecordDO> queryRecordByVisitUniqueCode(String uniqueCode, String dateTime, String endTime);

    int saveRecord(CfBdcrmVisitRecordDO recordDO);

    List<CfBdcrmVisitRecordDO> listRecordByLeaders(List<String> visitUniqueCodes,
                                                   String dateTime, String endTime);
}
