package com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.ISendAppPushCrmService;
import com.shuidihuzhu.cf.clinet.event.center.util.MsgUtil;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.account.v1.accountservice.MobileUserIdResponse;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: yangliming
 * @create: 2019/11/29
 */
@Slf4j
@Service
@RefreshScope
public class SendAppPushCrmServiceImpl implements ISendAppPushCrmService {

    @Autowired
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private MsgUtil msgUtil;

    @Override
    public void pushCrmMsg(List<String> mobiles, String title, String subTitle, String content) {
        log.info("pushCrmMsg request: mobiles {},title {},subTitle {},content {}", mobiles, title, subTitle, content);
        if (CollectionUtils.isEmpty(mobiles) || StringUtils.isBlank(content)) return;

        try {
            // 使用解密手机号
            List<MobileUserIdModel> userIdByMobile = simpleUserAccountServiceClient.getUserIdsByMobiles(mobiles);
            if (CollectionUtils.isEmpty(userIdByMobile)) {
                log.warn(this.getClass().getSimpleName() + " getUserIdsByMobiles param:{} result is {}", mobiles, userIdByMobile);
                return;
            }

            subTitle = handleSubTitle(subTitle, content);

            Map<Integer, String> params = Maps.newHashMap();
            params.put(1, title);
            params.put(2, subTitle);

            Map<Long, Map<Integer, String>> appMsgMap = Maps.newHashMap();
            for (MobileUserIdModel mobileUserIdModel : userIdByMobile) {
                appMsgMap.put(mobileUserIdModel.getUserId(), params);
            }

            if (MapUtils.isNotEmpty(appMsgMap)) {
                String modelNum = "UJR6200";
                msgUtil.sendAppParamsMsg(modelNum, appMsgMap);
            }
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " pushCrmMsg.sendAppPushMsg mobiles:" + mobiles + ",err:", e);
        }
    }

    private static String handleSubTitle(String subTitle, String content) {
        String templateContent = content;
        if (StringUtils.isNotBlank(content) && content.length() >= 210) {
            templateContent = content.substring(0, Math.min(content.length(), 210)) + "...";
        }
        if (StringUtils.isBlank(subTitle)) {
            subTitle = templateContent;
        }
        return subTitle;
    }

    @Override
    public void pushCrmMsg(String mobile, String title, String subTitle, String content) {
        log.info("pushCrmMsg request: mobile {},title {},subTitle {},content {}", mobile, title, subTitle, content);
        if (StringUtils.isBlank(mobile) || StringUtils.isBlank(content)) {
            return;
        }

        try {
            if (mobile.length() > 11) mobile = shuidiCipher.decrypt(mobile);
            MobileUserIdModel userIdByMobile = simpleUserAccountServiceClient.getUserIdByMobile(mobile);
            if (userIdByMobile == null) {
                log.warn(this.getClass().getSimpleName() + " pushCrmMsg.queryAppPushTemplateByTemplateId result is {}", mobile);
                return;
            }

            subTitle = handleSubTitle(subTitle, content);

            Map<Integer, String> params = Maps.newHashMap();
            params.put(1, title);
            params.put(2, subTitle);

            Map<Long, Map<Integer, String>> appMsgMap = Maps.newHashMap();

            appMsgMap.put(userIdByMobile.getUserId(), params);

            if (MapUtils.isNotEmpty(appMsgMap)) {
                String modelNum = "UJR6200";
                msgUtil.sendAppParamsMsg(modelNum, appMsgMap);
            }
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + " pushCrmMsg.sendAppPushMsg mobile:" + mobile + ",err:", e);
        }
    }

}
