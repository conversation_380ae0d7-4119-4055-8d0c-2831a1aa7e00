package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCaseBaseDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.KpiIllegalCaseService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepGrowthtoolCaseIdTemplate;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-03-20 16:52
 **/
@Service
public class CaseBusinessDataPushService extends AbstractPushDataService {

    @Autowired
    private ICfKpiCaseBaseDataService cfKpiCaseBaseDataService;

    @Autowired
    private KpiIllegalCaseService kpiIllegalCaseService;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private ApolloService apolloService;


    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.case_data;
    }

    @Override
    public DateTime getWhichDayToPush(LotInfo lotInfo) {
        //统一按照批次结束时间来
        DateTime dateTime = new DateTime(lotInfo.getUseDataTime());
        if (dateTime.isAfter(DateTime.now())) {
            dateTime = DateTime.now();
        }
        return dateTime;
    }

    @Override
    public List<PepGrowthtoolCaseIdTemplate> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        String startTime = new DateTime(lotInfo.getLotStartTime()).toString(GrowthtoolUtil.ymdhmsfmt);
        String endTime = new DateTime(lotInfo.getLotEndTime()).toString(GrowthtoolUtil.ymdhmsfmt);

        List<CfKpiCaseBaseDataDO> dataList = cfKpiCaseBaseDataService.getCaseBaseMapByUniqueCode(startTime, endTime, pushWhichDay.toString(GrowthtoolUtil.ymdfmt));

        Map<Integer, CfBdCaseInfoDo> caseIdMap = cfBdCaseInfoService.listCaseInfoByCaseIds(dataList.stream().map(item -> item.getCaseId().intValue()).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(CfBdCaseInfoDo::getCaseId, item -> item, (a, b) -> a));
        List<CfCrmMemberSnapshotDO> cfMemberKpiCalcInfoModels = bdMemberSnapshotService.listAllMemberSnapshotByMonth(pushWhichDay.toString(GrowthtoolUtil.ymfmt));

        List<KpiIllegalCaseDO> kpiIllegalCaseDOS = kpiIllegalCaseService.listByMonthKey(pushWhichDay.toString(GrowthtoolUtil.ymfmt));
        Map<Integer, String> reAttributeCase = apolloService.reAttributeCase();
        //案例修改为线上发起
        List<Long> onlineCaseList = apolloService.getKpiOnlineCaseList();
        List<PepGrowthtoolCaseIdTemplate> result = Lists.newArrayList();
        for (CfKpiCaseBaseDataDO cfKpiCaseBaseDataDO : dataList) {
            Optional<CfCrmMemberSnapshotDO> first = cfMemberKpiCalcInfoModels.stream()
                    .filter(item -> Objects.equals(item.getUniqueCode(), cfKpiCaseBaseDataDO.getUniqueCode()))
                    .findFirst();
            //离职后的数据就不要推送了
            if (first.isPresent()
                    && first.get().getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue()
                    && first.get().getLeaveTime().before(cfKpiCaseBaseDataDO.getFirstApproveTime())) {
                continue;
            }
            PepGrowthtoolCaseIdTemplate casePepTemplate = new PepGrowthtoolCaseIdTemplate();
            String uniqueCode = reAttributeCase.get(cfKpiCaseBaseDataDO.getCaseId().intValue());
            casePepTemplate.setUserId(cfKpiCaseBaseDataDO.getUniqueCode());
            if (StringUtils.isNotBlank(uniqueCode)) {
                casePepTemplate.setUserId(uniqueCode);
            }
            casePepTemplate.setCase_id(cfKpiCaseBaseDataDO.getCaseId());
            casePepTemplate.setInfo_uuid(cfKpiCaseBaseDataDO.getInfoUuid());
            casePepTemplate.setPatient_name(cfKpiCaseBaseDataDO.getPatientName());
            casePepTemplate.setTitle(cfKpiCaseBaseDataDO.getTitle());
            casePepTemplate.setCase_create_time(cfKpiCaseBaseDataDO.getCaseCreateTime());
            casePepTemplate.setCase_end_time(cfKpiCaseBaseDataDO.getCaseEndTime());
            casePepTemplate.setFirst_approve_time(cfKpiCaseBaseDataDO.getFirstApproveTime());
            casePepTemplate.setFirst_share_time(cfKpiCaseBaseDataDO.getFirstShareTime());
            casePepTemplate.setIs_offline_channel(convertToLong(cfKpiCaseBaseDataDO.getIsOfflineChannel()));
            casePepTemplate.setDonate_user_num(convertToLong(cfKpiCaseBaseDataDO.getDonateUserNum()));
            casePepTemplate.setFundraiser_share_num(cfKpiCaseBaseDataDO.getFundraiserShareDays());
            casePepTemplate.setCase_duplicate_flag(cfKpiCaseBaseDataDO.getCaseDuplicateFlag());
            CfBdCaseInfoDo cfBdCaseInfoDo = caseIdMap.get(cfKpiCaseBaseDataDO.getCaseId().intValue());
            if (cfBdCaseInfoDo != null) {
                casePepTemplate.setCase_end_status(cfBdCaseInfoDo.getCaseEndStatus());
                if (StringUtils.isBlank(cfKpiCaseBaseDataDO.getPatientName())) {
                    casePepTemplate.setPatient_name(cfBdCaseInfoDo.getPatientName());
                }
            }
            if (cfKpiCaseBaseDataDO.getValidAmount() <= 0) {
                casePepTemplate.setCase_amount(cfKpiCaseBaseDataDO.getCaseAmount() / 100);
                casePepTemplate.setDonate_num(convertToLong(cfKpiCaseBaseDataDO.getDonateNum()));
            } else {
                casePepTemplate.setCase_amount(cfKpiCaseBaseDataDO.getValidAmount() / 100);
                casePepTemplate.setDonate_num(cfKpiCaseBaseDataDO.getValidDonateNum());
            }
            if (onlineCaseList.contains(cfKpiCaseBaseDataDO.getCaseId())) {
                casePepTemplate.setIs_offline_channel(0);
            }
            casePepTemplate.setLaunch_pic_num(convertToLong(cfKpiCaseBaseDataDO.getLaunchPicNum()));
            casePepTemplate.setClew_source_type(convertToLong(cfKpiCaseBaseDataDO.getClewSourceType()));
            casePepTemplate.setFundraiser_share_days(convertToLong(cfKpiCaseBaseDataDO.getFundraiserShareDays()));
            casePepTemplate.setLotId(lotInfo.getLotId());
            int illegalTag = kpiIllegalCaseDOS.stream()
                    .anyMatch(item -> Objects.equals(item.getCaseId(), cfKpiCaseBaseDataDO.getCaseId().intValue())) ?
                    1 : 0;
            casePepTemplate.setIllegalTag(illegalTag);
            casePepTemplate.setCase_content_words(convertToLong(cfKpiCaseBaseDataDO.getCaseContentWords()));

            //todo 长期来看可以做成系统，本次通过配置操作
            //手动配置线上线索转线下案例，判断案例是否是线上线索转线下，是 1，否 0
            List<Long> leadsOnlineToOfflineCaseList = apolloService.getLeadsOnlineToOfflineCaseList();
            if (CollectionUtils.isNotEmpty(leadsOnlineToOfflineCaseList) && leadsOnlineToOfflineCaseList.contains(cfKpiCaseBaseDataDO.getCaseId())) {
                casePepTemplate.setIs_leads_online_to_offline(1);
            }

            result.add(casePepTemplate);
        }
        return result;
    }
}
