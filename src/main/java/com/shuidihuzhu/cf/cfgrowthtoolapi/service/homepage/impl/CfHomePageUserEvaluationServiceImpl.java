package com.shuidihuzhu.cf.cfgrowthtoolapi.service.homepage.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.homepage.CfUserEvaluationVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.homepage.UserEvaluationDetailVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.homepage.CfHomePageUserEvaluationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.CrowdfundingUtil;
import com.shuidihuzhu.cf.dao.CfHomePageUserEvaluationDao;
import com.shuidihuzhu.client.cf.growthtool.model.CfHomePageUserEvaluationDo;
import com.shuidihuzhu.client.cf.workorder.model.UgcHandleOrderParam;
import com.vdurmont.emoji.EmojiParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-06-02
 */

@Service
@Slf4j
public class CfHomePageUserEvaluationServiceImpl implements CfHomePageUserEvaluationService {

    @Autowired
    private CfHomePageUserEvaluationDao cfHomePageUserEvaluationDao;

    @Autowired
    private IAccountServiceDelegate accountServiceDelegate;

    @Autowired
    private ApolloService apolloService;


    @Override
    public int insert(CfHomePageUserEvaluationDo homePageUserEvaluationDo) {
        return cfHomePageUserEvaluationDao.insert(homePageUserEvaluationDo);
    }

    @Override
    public int updateByWorkOrderId(UgcHandleOrderParam handleOrderParam, List<Long> workOrderIds) {
        return cfHomePageUserEvaluationDao.updateHandleStatusByWorkOrderId(handleOrderParam.getHandleResult(),workOrderIds);
    }

    @Override
    public CfUserEvaluationVo getUserEvaluation(String uniqueCode) {
        List<CfHomePageUserEvaluationDo> userEvaluationList = cfHomePageUserEvaluationDao.listUserEvaluationByUniqueCode(uniqueCode,apolloService.getUserEvaluationAuditPassType(),apolloService.getUserEvaluationDisplayCount());
        if (CollectionUtils.isEmpty(userEvaluationList)){
            return null;
        }
        CfUserEvaluationVo cfUserEvaluationVo = new CfUserEvaluationVo();
        Map<Long,UserInfoModel> userInfoMap = accountServiceDelegate.getUserInfoByUserIdBatch(userEvaluationList.stream().map(CfHomePageUserEvaluationDo::getUserId).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity(),(oldVal,newVal)->newVal));
        List<UserEvaluationDetailVo> evaluationList = Lists.newArrayList();
        Set<String> tagSet = Sets.newHashSet();
        for (CfHomePageUserEvaluationDo evaluationDo : userEvaluationList){
            UserEvaluationDetailVo userEvaluationDetailVo = new UserEvaluationDetailVo();
            UserInfoModel userInfoModel = userInfoMap.get(evaluationDo.getUserId());
            userEvaluationDetailVo.setComment(evaluationDo.getComment());
            if (Objects.isNull(userInfoModel)){
                userEvaluationDetailVo.setHeadImg(apolloService.getUserDefaultHeadImg());
                userEvaluationDetailVo.setNickName("***");
            }else{
                if (StringUtils.isNotEmpty(userInfoModel.getHeadImgUrl())){
                    userEvaluationDetailVo.setHeadImg(userInfoModel.getHeadImgUrl());
                }else{
                    userEvaluationDetailVo.setHeadImg(apolloService.getUserDefaultHeadImg());
                }
                if (StringUtils.isNotEmpty(userInfoModel.getNickname())){
                    if (EmojiParser.removeAllEmojis(userInfoModel.getNickname()).length()!=userInfoModel.getNickname().length()){
                        userEvaluationDetailVo.setNickName("***");
                    }else {
                        userEvaluationDetailVo.setNickName(CrowdfundingUtil.getNameMask(userInfoModel.getNickname()));
                    }
                }else{
                    userEvaluationDetailVo.setNickName("***");
                }
            }
            evaluationList.add(userEvaluationDetailVo);
            tagSet.addAll(Splitter.on(",").splitToList(evaluationDo.getTag()));
        }
        //去除空字符串
        tagSet.removeIf(StringUtils::isEmpty);
        cfUserEvaluationVo.setEvaluationList(evaluationList);
        cfUserEvaluationVo.setTagList(Lists.newArrayList(tagSet));
        return cfUserEvaluationVo;
    }
}
