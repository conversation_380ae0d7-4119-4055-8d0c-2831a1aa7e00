package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.export.impl.CfBdCrmExcelExpoetUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.export.BudgetDetailExportModelForBase;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.export.BudgetDetailExportModelForLiving;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.AbstractWeaponExcelService;
import com.shuidihuzhu.client.cf.growthtool.enums.WeaponActivityTypeEnum;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-12-10 10:30
 **/
@Slf4j
@Service
public class WeaponExcelServiceForLiving extends AbstractWeaponExcelService {

    @Autowired
    private CfBdCrmExcelExpoetUtil cfBdCrmExcelExpoetUtil;

    @Override
    public boolean supportActivityType(int activityType) {
        return ObjectUtils.nullSafeEquals(activityType, WeaponActivityTypeEnum.LIVING_ALLOWANCE.getCode());
    }

    @Override
    public List<BudgetDetailExportModelForBase> postProcess(List<BudgetDetailExportModelForBase> modelForBaseList, ExcelNeedModel needModel) {
        return modelForBaseList.stream().map(BudgetDetailExportModelForLiving::buildByExcelBase).collect(Collectors.toList());
    }

    @Override
    protected void doExportExcel(HttpServletResponse response, List<BudgetDetailExportModelForBase> baseList, long adminLongUserId) throws IOException {
        //构造表头
        List<String> headerList = Lists.newArrayList();
        headerList.add("proofPoverty");
        headerList.add("foundation");
        headerList.add("launchCount");

        //调用导出
        cfBdCrmExcelExpoetUtil.exportV3(adminLongUserId, baseList, headerList, BudgetDetailExportModelForLiving.class, "预算使用明细");
    }
}
