package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmDiagnoseDataMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmDiagnoseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfBdCrmDiagnoseDataService;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * @author: wanghui
 * @create: 2021/4/15 下午5:27
 */
@Service
public class CfBdCrmDiagnoseDataServiceImpl implements CfBdCrmDiagnoseDataService {

    @Resource
    private CfBdCrmDiagnoseDataMapper cfBdCrmDiagnoseDataMapper;

    @Override
    public int insertSelective(CfBdCrmDiagnoseDataDO record) {
        return cfBdCrmDiagnoseDataMapper.insertSelective(record);
    }

    @Override
    public void batchInsertOrUpdate(List<CfBdCrmDiagnoseDataDO> list) {
        list.stream().forEach(item -> {
            CfBdCrmDiagnoseDataDO cfBdCrmDiagnoseDataDO = cfBdCrmDiagnoseDataMapper.selectByDateTimeWithOrgIdWithCityId(item.getDateTime(),item.getCurDayRange(),item.getPreDateTime(),item.getPreDayRange(), item.getOrgId(), Optional.ofNullable(item.getCityId()).orElse(0L));
            if (cfBdCrmDiagnoseDataDO!=null) {
                item.setId(cfBdCrmDiagnoseDataDO.getId());
                cfBdCrmDiagnoseDataMapper.updateByPrimaryKeySelective(item);
            }else {
                cfBdCrmDiagnoseDataMapper.insertSelective(item);
            }
        });
    }

    @Override
    public int updateByPrimaryKeySelective(CfBdCrmDiagnoseDataDO record) {
        return cfBdCrmDiagnoseDataMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<CfBdCrmDiagnoseDataDO> listByDateTime(String curDateTime,
                                                      Integer curDayRange,
                                                      String preDateTime,
                                                      Integer preDayRange) {
        return cfBdCrmDiagnoseDataMapper.listByDateTime(curDateTime, curDayRange, preDateTime, preDayRange);
    }


}
