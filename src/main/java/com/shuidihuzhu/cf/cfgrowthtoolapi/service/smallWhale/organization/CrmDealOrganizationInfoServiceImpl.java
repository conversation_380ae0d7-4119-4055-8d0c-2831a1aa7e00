package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrmDealOrganizationInfoServiceImpl implements ICrmDealOrganizationInfoService{

    @Override
    public List<BdCrmOrganizationDO> appendOrgNameToOrgList(List<BdCrmOrganizationDO> orgList, List<BdCrmOrganizationDO> currentOrgInfoList, CrowdfundingVolunteerEnum.RoleEnum roleEnum, List<Integer> needAppendRoleList) {
        if (!needAppendRoleList.contains(roleEnum.getLevel())) {
            return orgList;
        }
        Map<Long, BdCrmOrganizationDO> crmOrganizationDOMap = currentOrgInfoList.stream().collect(Collectors.toMap(BdCrmOrganizationDO::getId, Function.identity()));
        List<BdCrmOrganizationDO> dealResult = Lists.newArrayList();
        orgList.forEach(item -> {
            BdCrmOrganizationDO bdCrmOrganizationDO = new BdCrmOrganizationDO();
            BeanUtils.copyProperties(item, bdCrmOrganizationDO);
            String parentOrgName = Optional.ofNullable(crmOrganizationDOMap.get(item.getParentId())).map(BdCrmOrganizationDO::getOrgName).orElse("");
            bdCrmOrganizationDO.setOrgName(parentOrgName + bdCrmOrganizationDO.getOrgName());
            dealResult.add(bdCrmOrganizationDO);
        });
        return dealResult;
    }

    @Override
    public String ownOrgNameRename(String orgName) {
        return "直营" + orgName + "域";
    }

    @Override
    public String partnerOrgNameRename(String orgName) {
        return "渠道" + orgName;
    }

}
