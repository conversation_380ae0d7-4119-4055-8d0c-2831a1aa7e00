package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.impl.CfVolunteerServiceImpl;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.excel.EasyExcelUtil;
import com.shuidihuzhu.cf.dao.CrowdfundingVolunteerLabelDao;
import com.shuidihuzhu.cf.data.platform.client.ExportLargeExcelClient;
import com.shuidihuzhu.cf.data.platform.model.SmartExportParam;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerLabelEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CrowdfundingVolunteerLabelVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CrowdfundingVolunteerLabelDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.PageReturnModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrowdfundingVolunteerLabelServiceImpl implements CrowdfundingVolunteerLabelService {
    @Autowired
    private CrowdfundingVolunteerLabelDao crowdfundingVolunteerLabelDao;
    @Autowired
    private CfVolunteerServiceImpl cfVolunteerService;
    @Autowired
    private ICrmSelfBuiltOrgReadService organizationService;
    @Autowired
    private ICrmOrganizationRelationService organizationRelationService;
    @Autowired
    private MaskUtil maskUtil;
    @Autowired
    private ExportLargeExcelClient exportExcelClient;

    @Override
    public CrowdfundingVolunteerLabelVo queryByUniqueCode(String uniqueCode) {
        CrowdfundingVolunteerLabelVo vo = new CrowdfundingVolunteerLabelVo();
        if (StringUtils.isEmpty(uniqueCode)) {
            return null;
        }
        List<CrowdfundingVolunteerLabelDo> list = crowdfundingVolunteerLabelDao.selectByUniqueCodes(Lists.newArrayList(uniqueCode));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        CrowdfundingVolunteerLabelDo data = list.get(0);
        vo.setUniqueCode(data.getUniqueCode());
        List<Integer> labels = new ArrayList<>();
        for (String label : data.getVolunteerLabel().split(",")) {
            labels.add(Integer.parseInt(label));
        }
        vo.setLabels(labels);
        vo.setName(data.getName());
        vo.setCreateTime(data.getCreateTime());
        vo.setUpdateTime(data.getUpdateTime());
        return vo;
    }

    @Override
    public List<CrowdfundingVolunteerLabelVo> queryVolunteerByNameOnWorking(String name) {
        List<CrowdfundingVolunteerLabelVo> res = new ArrayList<>();
        List<CrowdfundingVolunteer> volunteerList = cfVolunteerService.fuzzyQueryByVolunteerName(name);
        for (CrowdfundingVolunteer volunteer : volunteerList) {
            CrowdfundingVolunteerLabelVo vo = new CrowdfundingVolunteerLabelVo();
            vo.setUniqueCode(volunteer.getUniqueCode());
            vo.setMis(volunteer.getMis());
            vo.setName(volunteer.getVolunteerName());
            vo.setPhoneMask(maskUtil.buildByEncryptPhone(volunteer.getMobile()));
            res.add(vo);
        }
        return res;
    }

    @Override
    public PageReturnModel<CrowdfundingVolunteerLabelVo> queryByName(String name, int pageNum, int pageSize){
        PageReturnModel<CrowdfundingVolunteerLabelVo> res = new PageReturnModel<>();
        long count = crowdfundingVolunteerLabelDao.countNameLike(name);
        if (count <= 0) {
            return res;
        }
        int offset = (pageNum - 1) * pageSize;
        List<CrowdfundingVolunteerLabelDo> volunteerLabelDoList = crowdfundingVolunteerLabelDao.getVolunteerLabelByNameLikePage(name, offset, pageSize);
        List<CrowdfundingVolunteerLabelVo> dataList = buildVos(volunteerLabelDoList);
        res.setTotal(count);
        res.setList(dataList);
        return res;
    }
    @Override
    public Response<Void> importData(MultipartFile file) {
        try {
            List<CrowdfundingVolunteerLabelImportExcel> dataList = EasyExcel.read(file.getInputStream(), CrowdfundingVolunteerLabelImportExcel.class, null).sheet().doReadSync();
            List<String> uniqueCodes = dataList.stream().map(CrowdfundingVolunteerLabelImportExcel::getUniqueCode).distinct().collect(Collectors.toList());
            Map<String, CrowdfundingVolunteer> volunteerMap = cfVolunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodes).stream().collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity()));

            Response<Void> validImportData = validImportData(dataList, volunteerMap);
            if (validImportData.notOk()) {
                return validImportData;
            }
            Set<String> existUniqueCodes = Optional.ofNullable(crowdfundingVolunteerLabelDao.selectByUniqueCodes(uniqueCodes))
                    .orElse(new ArrayList<>()).stream().map(CrowdfundingVolunteerLabelDo::getUniqueCode).collect(Collectors.toSet());

            List<CrowdfundingVolunteerLabelDo> insertList = new ArrayList<>();
            for (CrowdfundingVolunteerLabelImportExcel importExcel : dataList) {
                CrowdfundingVolunteerLabelDo labelDo = new CrowdfundingVolunteerLabelDo();
                StringJoiner stringJoiner = new StringJoiner(",");
                stringJoiner.add(String.valueOf(CrowdfundingVolunteerLabelEnum.HONOR_LABEL.getType()));
                labelDo.setVolunteerLabel(stringJoiner.toString());
                labelDo.setName(importExcel.getName());
                labelDo.setUniqueCode(importExcel.getUniqueCode());
                if (!existUniqueCodes.contains(importExcel.getUniqueCode())) {
                    existUniqueCodes.add(importExcel.getUniqueCode());
                    insertList.add(labelDo);
                } else {
                    crowdfundingVolunteerLabelDao.update(labelDo);
                }
            }
            if (CollectionUtils.isNotEmpty(insertList)) {
                crowdfundingVolunteerLabelDao.insertBatch(insertList);
            }
        } catch (Exception e) {
            log.error("顾问标签导入失败", e);
            return NewResponseUtil.makeFail("导入标签失败");
        }
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<Void> exportData(long seaUserId) {
        try {
            List<CrowdfundingVolunteerLabelExportExcel> datalist = new ArrayList<>();
            int offset = 0;
            int size = 200;
            List<CrowdfundingVolunteerLabelDo> all = new ArrayList<>();
            List<CrowdfundingVolunteerLabelDo> volunteerLabelByNameLikePage = new ArrayList<>();
            while (offset == 0 || (volunteerLabelByNameLikePage != null && size == volunteerLabelByNameLikePage.size())) {
                volunteerLabelByNameLikePage = crowdfundingVolunteerLabelDao.getByPage(offset, size);
                all.addAll(volunteerLabelByNameLikePage);
                offset += size;
            }
            List<CrowdfundingVolunteerLabelVo> vos = buildVos(all);
            for (CrowdfundingVolunteerLabelVo vo : vos) {
                datalist.add(CrowdfundingVolunteerLabelExportExcel.transferFromVo(vo));
            }
            SmartExportParam exportParam = new SmartExportParam();
            exportParam.setHeader(EasyExcelUtil.convertEasyExcelHeaders(CrowdfundingVolunteerLabelExportExcel.class));
            exportParam.setFileName("顾问荣誉标签");
            exportParam.setContent((List) datalist);
            exportExcelClient.writeExcelSmart(seaUserId, exportParam);
        } catch (Exception e) {
            log.error("导出顾问荣誉标签失败", e);
            return NewResponseUtil.makeFail("导出顾问荣誉标签失败");
        }
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<Void> addLabel(CrowdfundingVolunteerLabelVo volunteerLabelVo) {
        CrowdfundingVolunteerLabelDo crowdfundingVolunteerLabelDo = new CrowdfundingVolunteerLabelDo();
        crowdfundingVolunteerLabelDo.setUniqueCode(volunteerLabelVo.getUniqueCode());
        crowdfundingVolunteerLabelDo.setName(volunteerLabelVo.getName());
        StringJoiner stringJoiner = new StringJoiner(",");
        for (Integer label : volunteerLabelVo.getLabels()) {
            stringJoiner.add(String.valueOf(label));
        }
        crowdfundingVolunteerLabelDo.setVolunteerLabel(stringJoiner.toString());
        List<CrowdfundingVolunteerLabelDo> dos = crowdfundingVolunteerLabelDao.selectByUniqueCodes(Lists.newArrayList(volunteerLabelVo.getUniqueCode()));
        if (CollectionUtils.isNotEmpty(dos)) {
            return NewResponseUtil.makeFail("该人员已有标签");
        }
        crowdfundingVolunteerLabelDao.insert(crowdfundingVolunteerLabelDo);
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<Void> deleteLabel(long id) {
        crowdfundingVolunteerLabelDao.deleteById(id);
        return NewResponseUtil.makeSuccess();
    }

    private List<CrowdfundingVolunteerLabelVo> buildVos(List<CrowdfundingVolunteerLabelDo> dataList) {
        List<CrowdfundingVolunteerLabelVo> res = new ArrayList<>();
        Map<String, CrowdfundingVolunteerLabelDo> labelDoMap = dataList.stream().collect(Collectors.toMap(CrowdfundingVolunteerLabelDo::getUniqueCode, Function.identity()));
        List<String> uniqueCodes = dataList.stream().map(CrowdfundingVolunteerLabelDo::getUniqueCode).distinct().collect(Collectors.toList());
        List<CrowdfundingVolunteer> volunteerList = cfVolunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodes);
        Map<String, BdCrmOrgUserRelationDO> relationsMap = organizationRelationService.listByUniqueCodes(volunteerList.stream().map(CrowdfundingVolunteer::getUniqueCode).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(BdCrmOrgUserRelationDO::getUniqueCode, Function.identity(), (o1, o2) -> o2));
        Map<Long, String> orgMap = organizationService.listChainByOrgIdsWithDefaultSplitter(relationsMap.values().stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList()));
        for (CrowdfundingVolunteer volunteer : volunteerList) {
            CrowdfundingVolunteerLabelVo vo = new CrowdfundingVolunteerLabelVo();
            CrowdfundingVolunteerLabelDo volunteerLabelDo = labelDoMap.get(volunteer.getUniqueCode());
            vo.setId(volunteerLabelDo.getId());
            vo.setCreateTime(volunteerLabelDo.getCreateTime());
            vo.setUniqueCode(volunteer.getUniqueCode());
            vo.setUpdateTime(volunteerLabelDo.getUpdateTime());
            vo.setMis(volunteer.getMis());
            vo.setLevelStr(CrowdfundingVolunteerEnum.RoleEnum.parse(volunteer.getLevel()).getDesc());
            vo.setName(volunteer.getVolunteerName());
            for (String label : volunteerLabelDo.getVolunteerLabel().split(",")) {
                vo.getLabels().add(CrowdfundingVolunteerLabelEnum.parseLabel(Integer.parseInt(label)).getType());
            }
            if (relationsMap.get(volunteer.getUniqueCode()) == null) {
                vo.setOrgPath("");
            } else {
                vo.setOrgPath(orgMap.get(relationsMap.get(volunteer.getUniqueCode()).getOrgId()));
            }
            res.add(vo);
        }
        res.sort((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()));
        return res;
    }

    public Response<Void> validImportData(List<CrowdfundingVolunteerLabelImportExcel> dataList, Map<String, CrowdfundingVolunteer> volunteerMap) {
        if (dataList.stream().anyMatch(data -> StringUtils.isAnyBlank(data.getName(), data.getUniqueCode()))) {
            return NewResponseUtil.makeFail("字段内容不能为空");
        }
        for (CrowdfundingVolunteerLabelImportExcel importExcel : dataList) {
            if (!volunteerMap.containsKey(importExcel.getUniqueCode())) {
                return NewResponseUtil.makeFail("「" + importExcel.getName() + "」姓名及uniquecode不一致");
            }
            if (!StringUtils.equals(volunteerMap.get(importExcel.getUniqueCode()).getVolunteerName(), importExcel.getName())) {
                return NewResponseUtil.makeFail("「" + importExcel.getName() + "」姓名及uniquecode不一致");
            }
        }
        return NewResponseUtil.makeSuccess();
    }
    @Data
    public static class CrowdfundingVolunteerLabelImportExcel {
        @ExcelProperty(value = "姓名", index = 0)
        private String name;
        @ExcelProperty(value = "uniquecode", index = 1)
        private String uniqueCode;
    }

    @Data
    public static class CrowdfundingVolunteerLabelExportExcel {
        @ExcelProperty("姓名")
        private String name;
        @ExcelProperty("uniquecode")
        private String uniqueCode;
        @ExcelProperty("mis账号")
        private String mis;
        @ExcelProperty("所在组织")
        private String orgPath;
        @ExcelProperty("人员职级")
        private String levelStr;
        @ExcelProperty("荣誉标签")
        private String label;

        public static CrowdfundingVolunteerLabelExportExcel transferFromVo(CrowdfundingVolunteerLabelVo vo) {
            CrowdfundingVolunteerLabelExportExcel res = new CrowdfundingVolunteerLabelExportExcel();
            res.setName(vo.getName());
            res.setUniqueCode(vo.getUniqueCode());
            res.setMis(vo.getMis());
            res.setOrgPath(vo.getOrgPath());
            res.setLevelStr(vo.getLevelStr());
            StringJoiner stringJoiner = new StringJoiner(",");
            for (Integer label : vo.getLabels()) {
                stringJoiner.add(CrowdfundingVolunteerLabelEnum.parseLabel(label).getDesc());
            }
            res.setLabel(stringJoiner.toString());
            return res;
        }
    }
}
