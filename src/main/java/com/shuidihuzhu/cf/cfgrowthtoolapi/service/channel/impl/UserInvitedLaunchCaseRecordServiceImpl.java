package com.shuidihuzhu.cf.cfgrowthtoolapi.service.channel.impl;

import com.shuidihuzhu.cf.dao.CfUserInvitedLaunchCaseRecordDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfUserInvitedLaunchCaseRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.channel.IUserInvitedLaunchCaseRecordService;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/4/29 4:08 PM
 */
@Service
public class UserInvitedLaunchCaseRecordServiceImpl implements IUserInvitedLaunchCaseRecordService {
    @Autowired
    CfUserInvitedLaunchCaseRecordDao cfUserInvitedLaunchCaseRecordDao;

    @Async
    @Override
    public Integer saveUserInvitedLaunchCaseRecord(CfUserInvitedLaunchCaseRecordDO cfUserInvitedLaunchCaseRecordDO) {
        return cfUserInvitedLaunchCaseRecordDao.insert(cfUserInvitedLaunchCaseRecordDO);
    }
    @Override
    public Integer updateChannelByInfoIdWithUserId(CfUserInvitedLaunchCaseRecordDO cfUserInvitedLaunchCaseRecordDO) {
        return cfUserInvitedLaunchCaseRecordDao.updateChannelByInfoIdWithUserId(cfUserInvitedLaunchCaseRecordDO);
    }
    @Override
    public List<CfUserInvitedLaunchCaseRecordModel> getCfUserInvitedLaunchCaseRecordByInfoIds(List<Long> infoIds) {
        return  cfUserInvitedLaunchCaseRecordDao.getCfUserInvitedLaunchCaseRecordsModel(infoIds);
    }
    @Override
    public List<CfUserInvitedLaunchCaseRecordDO> getCfUserInvitedLaunchCaseRecordDOByInfoIds(List<Long> infoIds) {
        return cfUserInvitedLaunchCaseRecordDao.getCfUserInvitedLaunchCaseRecordDOByInfoIds(infoIds);
    }
    @Override
    public CfUserInvitedLaunchCaseRecordModel getCfUserInvitedLaunchCaseRecordModel(Long infoId, Long userId){
        return cfUserInvitedLaunchCaseRecordDao.getCfUserInvitedLaunchCaseRecordModel(infoId,userId);
    }
}
