package com.shuidihuzhu.cf.cfgrowthtoolapi.service.starrock;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitBoardModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitThoughSuccessModel;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2023-03-01 16:49
 **/
public interface IRecruitBoardService {

    RecruitBoardModel aggregateByOrgIds(List<Long> orgIds, String startTime, String endTime);

    List<RecruitThoughSuccessModel> groupByUniqueCode(String startTime);

}
