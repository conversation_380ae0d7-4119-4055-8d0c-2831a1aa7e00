package com.shuidihuzhu.cf.cfgrowthtoolapi.service.msg.impl;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.SeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthMsgEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.CfMsgBodyVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.CfMsgModelVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.MsgQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.msg.ICfMsgBodyService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.msg.ICfMsgModelService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.msg.CfMsgModelDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020-08-10
 */

@Service
@Slf4j
public class CfMsgModelServiceImpl implements ICfMsgModelService {

    @Autowired
    private CfMsgModelDao cfMsgModelDao;
    @Autowired
    private SeaAccountServiceDelegate seaAccountServiceDelegate;
    @Autowired
    private CustomEventPublisher customEventPublisher;
    @Autowired
    private IMqProducerService mqProducerServiceImpl;
    @Autowired
    private ICfMsgBodyService cfMsgBodyServiceImpl;

    @Override
    public OpResult<CommonResultModel<CfMsgModelVO>> listMsgModel(MsgQueryParam msgQueryParam) {
        OpResult<Void> checkOpResult = msgQueryParam.checkParam4listModel();
        if (checkOpResult.isFail()){
            return OpResult.createFailResult(checkOpResult.getErrorCode());
        }
        long total = cfMsgModelDao.getMsgModelCount(msgQueryParam);
        List<CfMsgModelVO> modelList = cfMsgModelDao.listMsgModel(msgQueryParam);
        modelList.forEach(item ->{
            item.checkAndSetDefaultValue();
        });
        CommonResultModel<CfMsgModelVO> commonResultModel = new CommonResultModel<CfMsgModelVO>();
        commonResultModel.setTotal(total);
        commonResultModel.setModelList(modelList);
        return OpResult.createSucResult(commonResultModel);
    }

    @Override
    public OpResult<Long> saveOrUpdateMsgModel(CfMsgModelVO cfMsgModelVO) {
        OpResult<Void> opResult = this.setOperatNameAndUserId(cfMsgModelVO);
        if (opResult.isFail()){
            return OpResult.createFailResult(opResult.getErrorCode());
        }
        JSONObject jsonObject = new JSONObject();
        if (Objects.nonNull(cfMsgModelVO.getId())){
            CfMsgModelVO msgModelFromDb = cfMsgModelDao.getMsgModelById(cfMsgModelVO.getId());
            if (Objects.isNull(msgModelFromDb)){
                return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
            }
            cfMsgModelDao.updateMsgModel(cfMsgModelVO);
            // 添加操作日志
            jsonObject.put("preContent",msgModelFromDb.queryOperateLog(OperateTypeEnum.EDIT_MS_MODEL));
            jsonObject.put("curContent",cfMsgModelVO.queryOperateLog(OperateTypeEnum.EDIT_MS_MODEL));
            customEventPublisher.publish(new OperateLogEvent(this,
                    String.valueOf(cfMsgModelVO.getId()),
                    OperateTypeEnum.EDIT_MS_MODEL.getDesc(),
                    OperateTypeEnum.EDIT_MS_MODEL,
                    jsonObject.toJSONString(),
                    cfMsgModelVO.getOperateUserId(),
                    cfMsgModelVO.getMisName()));
        }else{
            //业务类型
            cfMsgModelVO.setBizType(CfGrowthMsgEnums.BizTypeEnum.OFFLINE.getCode());
            cfMsgModelDao.insertMsgModel(cfMsgModelVO);
            //添加操作日志
            jsonObject.put("preContent","");
            jsonObject.put("curContent",cfMsgModelVO.queryOperateLog(OperateTypeEnum.SAVE_MS_MODEL));
            customEventPublisher.publish(new OperateLogEvent(this,
                    String.valueOf(cfMsgModelVO.getId()),
                    OperateTypeEnum.SAVE_MS_MODEL.getDesc(),
                    OperateTypeEnum.SAVE_MS_MODEL,
                    jsonObject.toJSONString(),
                    cfMsgModelVO.getOperateUserId(),
                    cfMsgModelVO.getMisName()));
        }
        return OpResult.createSucResult(cfMsgModelVO.getId());
    }

    @Override
    public OpResult<Void> saveModelSendMethod(CfMsgModelVO cfMsgModelVO) {

        OpResult<Void> opResult = this.setOperatNameAndUserId(cfMsgModelVO);
        if (opResult.isFail()){
            return OpResult.createFailResult(opResult.getErrorCode());
        }
        CfGrowthMsgEnums.SendStatusEnum sendStatusEnum = CfGrowthMsgEnums.SendStatusEnum.parse(cfMsgModelVO.getSendStatus());
        CfGrowthMsgEnums.SendMethodEnum sendMethodEnum = CfGrowthMsgEnums.SendMethodEnum.parse(cfMsgModelVO.getSendMethod());
        if (Objects.isNull(cfMsgModelVO.getId()) || Objects.isNull(sendStatusEnum) || Objects.isNull(sendMethodEnum) || !sendStatusEnum.isManuChange()){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfMsgModelVO msgModelFromDb = cfMsgModelDao.getMsgModelById(cfMsgModelVO.getId());
        if (Objects.isNull(msgModelFromDb)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        //业务场景触发
        if (CfGrowthMsgEnums.SendTypeEnum.BIZ.getCode().equals(msgModelFromDb.getSendType())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.MSG_SENDTYPE_ERROR);
        }
        //改成定时发送时
        if (CfGrowthMsgEnums.SendMethodEnum.CRON.equals(sendMethodEnum)){
            //待发送时，必须设置时间
            if (StringUtils.isBlank(cfMsgModelVO.getCronTime()) && sendStatusEnum.isMustCronTime()){
                return OpResult.createFailResult(CfGrowthtoolErrorCode.MSG_CRON_NULL);
            }
            //校验时间是否一致
            if (msgModelFromDb.getCronTime().equalsIgnoreCase(cfMsgModelVO.getCronTime())){
                return OpResult.createFailResult(CfGrowthtoolErrorCode.MSG_CRON_REPEAT);
            }
        }
        //消息已经发送,不允许再次修改
        if (CfGrowthMsgEnums.SendStatusEnum.SENDCOMPLETE.getCode().equals(msgModelFromDb.getSendStatus())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.MSG_SENDEND);
        }
        cfMsgModelDao.updateModelSendMethod(cfMsgModelVO);
        // 添加操作日志
        JSONObject jsonObject = new JSONObject();
        //设置为取消发送后,不用发mq
        if (CfGrowthMsgEnums.SendStatusEnum.SENDCANCEL.equals(sendStatusEnum)){
            jsonObject.put("preContent",msgModelFromDb.queryOperateLog(OperateTypeEnum.MS_CANCEL));
            jsonObject.put("curContent",cfMsgModelVO.queryOperateLog(OperateTypeEnum.MS_CANCEL));
            customEventPublisher.publish(new OperateLogEvent(this,
                    String.valueOf(cfMsgModelVO.getId()),
                    OperateTypeEnum.MS_CANCEL.getDesc(),
                    OperateTypeEnum.MS_CANCEL,
                    jsonObject.toJSONString(),
                    cfMsgModelVO.getOperateUserId(),
                    cfMsgModelVO.getMisName()));
            return opResult;
        }else{
            jsonObject.put("preContent",msgModelFromDb.queryOperateLog(OperateTypeEnum.MS_SNED));
            jsonObject.put("curContent",cfMsgModelVO.queryOperateLog(OperateTypeEnum.MS_SNED));
            customEventPublisher.publish(new OperateLogEvent(this,
                    String.valueOf(cfMsgModelVO.getId()),
                    OperateTypeEnum.MS_SNED.getDesc(),
                    OperateTypeEnum.MS_SNED,
                    jsonObject.toJSONString(),
                    cfMsgModelVO.getOperateUserId(),
                    cfMsgModelVO.getMisName()));
        }
        long targetTime;
        if (CfGrowthMsgEnums.SendMethodEnum.CRON.equals(sendMethodEnum)){
            targetTime = DateUtil.parseDateTime(cfMsgModelVO.getCronTime()).getTime();
        }else{
            targetTime = 0;
        }
        msgModelFromDb.setCronTime(cfMsgModelVO.getCronTime());
        msgModelFromDb.setSendStatus(cfMsgModelVO.getSendStatus());
        msgModelFromDb.setSendMethod(cfMsgModelVO.getSendMethod());
        //发送mq
        return mqProducerServiceImpl.sendSelfBuiltCfModelMsg(msgModelFromDb,targetTime);
    }

    @Override
    public OpResult<Void> saveModelStatus(CfMsgModelVO cfMsgModelVO) {
        OpResult<Void> opResult = this.setOperatNameAndUserId(cfMsgModelVO);
        if (opResult.isFail()){
            return OpResult.createFailResult(opResult.getErrorCode());
        }
        CfGrowthMsgEnums.OnlineStatusEnum onlineStatusEnum = CfGrowthMsgEnums.OnlineStatusEnum.parse(cfMsgModelVO.getModelOnlineStatus());
        if (Objects.isNull(cfMsgModelVO.getId()) || Objects.isNull(onlineStatusEnum)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfMsgModelVO msgModelFromDb = cfMsgModelDao.getMsgModelById(cfMsgModelVO.getId());
        if (Objects.isNull(msgModelFromDb)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        List<CfMsgBodyVO> cfMsgBodyList = cfMsgBodyServiceImpl.listMsgBodyByModelId(cfMsgModelVO.getId());
        if (CollectionUtils.isEmpty(cfMsgBodyList)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.MSG_BODY_NULL);
        }
        cfMsgModelDao.updateModelStatus(cfMsgModelVO);
        //添加操作日志
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("preContent",msgModelFromDb.queryOperateLog(OperateTypeEnum.MS_MODEL_STATUS));
        jsonObject.put("curContent",cfMsgModelVO.queryOperateLog(OperateTypeEnum.MS_MODEL_STATUS));
        customEventPublisher.publish(new OperateLogEvent(this,
                String.valueOf(cfMsgModelVO.getId()),
                OperateTypeEnum.MS_MODEL_STATUS.getDesc(),
                OperateTypeEnum.MS_MODEL_STATUS,
                jsonObject.toJSONString(),
                cfMsgModelVO.getOperateUserId(),
                cfMsgModelVO.getMisName()));
        return opResult;
    }

    @Override
    public CfMsgModelVO getMsgModelById(Long id) {
        return cfMsgModelDao.getMsgModelById(id);
    }

    @Override
    public int updateSendComplete(Long id, Integer sendStatus) {
        return cfMsgModelDao.updateSendComplete(id,sendStatus);
    }

    @Override
    public OpResult<CfMsgModelVO> getMsgModel(MsgQueryParam msgQueryParam) {
        CfMsgModelVO msgModelFromDb = cfMsgModelDao.getMsgModelById(msgQueryParam.getModelId());
        if (Objects.isNull(msgModelFromDb)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        msgModelFromDb.checkAndSetDefaultValue();
        return OpResult.createSucResult(msgModelFromDb);
    }

    private OpResult<Void> setOperatNameAndUserId(CfMsgModelVO cfMsgModelVO){
        long adminUserId = AuthSaasContext.getAuthSaasUserId();
        AdminUserAccountModel validUserAccountById = seaAccountServiceDelegate.getValidUserAccountById((long)adminUserId);
        if (Objects.isNull(validUserAccountById)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        }
        cfMsgModelVO.setOperateUserId(adminUserId);
        cfMsgModelVO.setMisName(validUserAccountById.getName());
        return OpResult.createSucResult();
    }
}
