package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.alarm.IAlarmClientFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICrowdfundingCityService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.comparators.FixedOrderComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2022-09-20 14:15
 * 防止循环依赖,提取一个设置快照上组织和上级相关信息的类
 **/
@Slf4j
@Service
public class MemberSnapshotHandlerService {


    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private CrmOrganizationRelationServiceImpl crmOrganizationRelationService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;

    @Autowired
    private ICrowdfundingCityService crowdfundingCityService;

    @Autowired
    private AlarmClient alarmClient;

    @Autowired
    private IAlarmClientFacade alarmClientFacade;

    @Autowired
    private ApolloService apolloService;

    /**
     * 设置组织相关属性
     *
     * @param relationDO
     * @param snapshotDO
     * @param currentOrg
     * @param volunteerLevel
     */
    public void setOrgRelation(BdCrmOrgUserRelationDO relationDO, CfCrmMemberSnapshotDO snapshotDO, BdCrmOrganizationDO currentOrg, int volunteerLevel) {
        if (currentOrg == null) {
            return;
        }
        List<BdCrmOrganizationDO> bdCrmOrganizationDOS = crmSelfBuiltOrgReadService.listParentOrgAsChain(currentOrg.getId());
        snapshotDO.setOrgPath(bdCrmOrganizationDOS.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.joining("-")));
        snapshotDO.setOrgId(relationDO.getOrgId());
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(volunteerLevel);
        //当前组织属性
        if (currentOrg.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode()) {
            //需要设置对应的leader和城市名称
            BdCrmOrganizationDO parentOrg = crmSelfBuiltOrgReadService.getParentOrg(relationDO.getOrgId());
            List<BdCrmOrgUserRelationDO> leaderRelations = crmOrganizationRelationService.listRelationByOrgId(parentOrg.getId());
            String leaderUniqueCode = "";
            if (CollectionUtils.isEmpty(leaderRelations)) {
                log.info("组织:{}找不到上级管理员", currentOrg.getOrgName());
            } else {
                //人员按照级别排序
                List<String> uniqueCodes = leaderRelations.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).collect(Collectors.toList());
                List<CrowdfundingVolunteer> volunteerList = cfVolunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodes);
                FixedOrderComparator<Integer> ordering = new FixedOrderComparator<Integer>(CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel(),
                        CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.AREA_PROVINCE.getLevel());
                ordering.setUnknownObjectBehavior(FixedOrderComparator.UnknownObjectBehavior.AFTER);
                Optional<CrowdfundingVolunteer> first = volunteerList.stream().min(Ordering.from(ordering).onResultOf(CrowdfundingVolunteer::getLevel));
                if (first.isPresent()) {
                    leaderUniqueCode = first.get().getUniqueCode();
                } else {
                    log.info("组织:{}找不到对应的上级或者当前上级非省级", currentOrg.getOrgName());
                    String msg = String.format("当前组织（叶子结点）:%s找不到对应的上级,请检查", snapshotDO.getOrgPath());
                    alarmClient.sendByGroup(GeneralConstant.ALARMGROUPID, msg);
                }
            }
            snapshotDO.setLeaderUniqueCode(leaderUniqueCode);
            snapshotDO.setCity(currentOrg.getCityName());
        } else {
            setLeaderUniqueCode(relationDO, snapshotDO, currentOrg, roleEnum);
            setProvince(relationDO, snapshotDO, roleEnum);
        }
    }


    private void setLeaderUniqueCode(BdCrmOrgUserRelationDO relationDO, CfCrmMemberSnapshotDO snapshotDO, BdCrmOrganizationDO currentOrg, CrowdfundingVolunteerEnum.RoleEnum roleEnum) {
        BdCrmOrganizationDO parentOrg = crmSelfBuiltOrgReadService.getParentOrg(relationDO.getOrgId());
        if (Objects.isNull(parentOrg)) {
            return;
        }
        //排除超管和运营
        List<Integer> levelOrdering = Arrays.stream(CrowdfundingVolunteerEnum.RoleEnum.values())
                .filter(item -> !CrowdfundingVolunteerEnum.optAndSuperRoles.contains(item.getLevel()) && item.getSortLevel() >= roleEnum.getSortLevel())
                .sorted(Ordering.natural().onResultOf(CrowdfundingVolunteerEnum.RoleEnum::getSortLevel))
                .map(CrowdfundingVolunteerEnum.RoleEnum::getLevel)
                .collect(Collectors.toList());
        FixedOrderComparator<Integer> ordering = new FixedOrderComparator<Integer>(levelOrdering);
        ordering.setUnknownObjectBehavior(FixedOrderComparator.UnknownObjectBehavior.AFTER);
        List<BdCrmOrgUserRelationDO> leaderRelations = crmOrganizationRelationService.listRelationByOrgId(parentOrg.getId());
        String leaderUniqueCode = "";
        if (CollectionUtils.isEmpty(leaderRelations)) {
            log.info("组织:{}找不到上级管理员", currentOrg.getOrgName());
        } else {
            //人员按照级别排序
            List<String> uniqueCodes = leaderRelations.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).collect(Collectors.toList());
            Optional<CrowdfundingVolunteer> first = cfVolunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodes)
                    .stream()
                    .min(Ordering.from(ordering).onResultOf(CrowdfundingVolunteer::getLevel));
            if (first.isPresent()) {
                leaderUniqueCode = first.get().getUniqueCode();
            } else {
                log.info("组织:{}找不到对应的上级", currentOrg.getOrgName());
                String msg = String.format("当前组织（非叶子结点）:%s找不到对应的上级,请检查", snapshotDO.getOrgPath());
                alarmClient.sendByGroup(GeneralConstant.ALARMGROUPID, msg);
            }
        }
        snapshotDO.setLeaderUniqueCode(leaderUniqueCode);
    }

    /**
     * 设置省份
     *
     * @param relationDO
     * @param snapshotDO
     */
    private void setProvince(BdCrmOrgUserRelationDO relationDO, CfCrmMemberSnapshotDO snapshotDO, CrowdfundingVolunteerEnum.RoleEnum roleEnum) {
        List<BdCrmOrganizationDO> allLeafOrg = crmMemberInfoService.listAllOrgModelByUniqueCode(relationDO.getUniqueCode())
                .stream()
                .filter(item -> item.getOrgAttribute() == 0)
                .collect(Collectors.toList());
        //查找城市
        List<Integer> cityIds = allLeafOrg.stream().map(BdCrmOrganizationDO::getCityId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cityIds)) {
            return;
        }
        Set<Integer> provinceIdSet = crowdfundingCityService.getCityByCityIds(cityIds)
                .stream()
                .filter(item -> item.getLevel() == 1)
                .map(CrowdfundingCity::getParentId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(provinceIdSet)) {
            return;
        }
        List<CrowdfundingCity> provinceList = crowdfundingCityService.getCityByCityIds(Lists.newArrayList(provinceIdSet));
        int size = provinceList.size();
        //报警修改
        if (size <= 0) {
            String msg = String.format("组织:%s,姓名:%s,职级:%s,找不到对应的省份,请检查", snapshotDO.getOrgPath(), snapshotDO.getVolunteerName(), roleEnum.getDesc());
            log.info("BdMemberSnapshotJobServiceImpl_setProvince_msg:{}", msg);
            //发送报警
            alarmClientFacade.sendByUser(apolloService.getBdKpiAlarmUsers(), msg);
        } else if (size == 1) {
            String chooseProvince = provinceList.get(0).getName();
            snapshotDO.setProvince(chooseProvince);
            snapshotDO.setCity(chooseProvince);
        } else {
            String chooseProvince = provinceList.get(0).getName();
            String manageProvince = provinceList.stream().map(CrowdfundingCity::getName).collect(Collectors.joining(","));
            String content = String.format("组织:%s,姓名:%s,职级:%s,分管:%s,系统选择的是%s", snapshotDO.getOrgPath(), snapshotDO.getVolunteerName(), roleEnum.getDesc(), manageProvince, chooseProvince);
            log.info("BdMemberSnapshotJobServiceImpl_setProvince_content:{}", content);
            //非测试区域发送报警
            //if (!snapshotDO.getOrgPath().contains("水滴测试大区")) {
            //    alarmClientFacade.sendByUser(apolloService.getBdKpiAlarmUsers(), content);
            //}
            snapshotDO.setProvince(chooseProvince);
            snapshotDO.setCity(chooseProvince);
        }
    }
}
