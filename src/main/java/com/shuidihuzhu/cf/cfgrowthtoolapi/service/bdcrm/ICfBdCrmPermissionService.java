package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020/9/29 3:13 下午
 */
public interface ICfBdCrmPermissionService {

    OpResult checkPermission(int level, String permission);

    OpResult checkTimeRange(int level, String startTime, String endTime);

    List<String> getDataPermissionsByRole(int roleCode);

    List<Integer> getRoleListByPermissions(String permission);

    boolean reportPermission(CrowdfundingVolunteer volunteer, ClewCrowdfundingReportRelation reportRelation);

    boolean reportPermissionByReportId(CrowdfundingVolunteer volunteer, Long reportId);

    boolean casePermissionByInfoId(CrowdfundingVolunteer volunteer, String infoId);

}
