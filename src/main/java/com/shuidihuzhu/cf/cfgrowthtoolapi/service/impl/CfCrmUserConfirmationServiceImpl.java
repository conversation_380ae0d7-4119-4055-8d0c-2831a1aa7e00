package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.message.IMsgClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCrmUserConfirmSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCrmUserConfirmationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ClewPreproseMaterialEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.UserConfirmRemindMessage;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.confirmation.ConfirmationPageParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.dao.CfCrmUserConfirmationDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 确权信息表(CfCrmUserConfirmation)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-03 19:28:16
 */
@Service
public class CfCrmUserConfirmationServiceImpl implements CfCrmUserConfirmationService {

    @Resource
    private CfCrmUserConfirmationDao cfCrmUserConfirmationDao;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    @Autowired
    private IMsgClientDelegate msgClientV2;

    @Autowired
    private ICfVolunteerService volunteerService;

    @Autowired
    private CfCrmUserConfirmSnapshotService userConfirmSnapshotService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public CfCrmUserConfirmationDO queryById(long id) {
        return cfCrmUserConfirmationDao.queryById(id);
    }


    @Override
    public int insert(CfCrmUserConfirmationDO cfCrmUserConfirmation) {
        return cfCrmUserConfirmationDao.insert(cfCrmUserConfirmation);
    }

    @Override
    public void addOrUpdateByPrepose(PreposeMaterialModel.MaterialInfoVo materialInfoVo, CrowdfundingVolunteer crowdfundingVolunteer) {
        //通过materialId判断是否存在
        Long preposeId = materialInfoVo.getId();
        if (preposeId == null || preposeId == 0) {
            return;
        }
        CfCrmUserConfirmationDO modelCreateByMaterial = CfCrmUserConfirmationDO.createByMaterialVO(materialInfoVo, oldShuidiCipher);
        modelCreateByMaterial.setVolunteerName(crowdfundingVolunteer.getVolunteerName());
        modelCreateByMaterial.setUniqueCode(crowdfundingVolunteer.getUniqueCode());
        //填充对应的人员信息
        CfCrmUserConfirmationDO userConfirmationDOFromDB = cfCrmUserConfirmationDao.getByPreposeMaterialId(preposeId);
        innerAddOrUpdate(modelCreateByMaterial, userConfirmationDOFromDB);
    }

    @Override
    public void addOrUpdateByDraft(CrowdfundingBaseInfoBackup crowdfundingBaseInfoBackup, CrowdfundingVolunteer crowdfundingVolunteer) {
        CfCrmUserConfirmationDO modelCreateByDraft = CfCrmUserConfirmationDO.createByCrowdfundingBaseInfoBackup(crowdfundingBaseInfoBackup);
        modelCreateByDraft.setVolunteerName(crowdfundingVolunteer.getVolunteerName());
        modelCreateByDraft.setUniqueCode(crowdfundingVolunteer.getUniqueCode());
        //根据bundleId查询确认信息
        CfCrmUserConfirmationDO crmUserConfirmationDO = getByDraftId(crowdfundingBaseInfoBackup.getId());
        innerAddOrUpdate(modelCreateByDraft, crmUserConfirmationDO);
        //使用旧加密替换新加密
        String decrypt = shuidiCipher.decrypt(modelCreateByDraft.getEncryptRaisePhone());
        modelCreateByDraft.setEncryptRaisePhone(oldShuidiCipher.aesEncrypt(decrypt));

        CfCrmUserConfirmSnapshotDO cfCrmUserConfirmSnapshotDO = new CfCrmUserConfirmSnapshotDO();
        cfCrmUserConfirmSnapshotDO.setDateType(CfCrmUserConfirmSnapshotDO.ConfirmSnapshotDataType.new_type.getCode());

        if (crmUserConfirmationDO == null) {
            UserConfirmRemindMessage userConfirmRemindMessage = new UserConfirmRemindMessage();
            userConfirmRemindMessage.setConfirmId(modelCreateByDraft.getId());
            userConfirmRemindMessage.setUniqueCode(modelCreateByDraft.getUniqueCode());
            sendRemindMsg(userConfirmRemindMessage);

            cfCrmUserConfirmSnapshotDO.setDateType(CfCrmUserConfirmSnapshotDO.ConfirmSnapshotDataType.udpate_type.getCode());
        }
        //保存草稿快照
        cfCrmUserConfirmSnapshotDO.setConfirmId(modelCreateByDraft.getId());
        cfCrmUserConfirmSnapshotDO.setEncryptRaisePhone(modelCreateByDraft.getEncryptRaisePhone());
        cfCrmUserConfirmSnapshotDO.setDataSource(2);
        cfCrmUserConfirmSnapshotDO.setDataStr(JSON.toJSONString(crowdfundingBaseInfoBackup.getRaiseCaseParam()));
        userConfirmSnapshotService.insert(cfCrmUserConfirmSnapshotDO);
    }

    private void innerAddOrUpdate(CfCrmUserConfirmationDO newUserConfirmation, CfCrmUserConfirmationDO userConfirmationDOFromDB) {
        CfCrmUserConfirmSnapshotDO cfCrmUserConfirmSnapshotDO = new CfCrmUserConfirmSnapshotDO();
        if (userConfirmationDOFromDB == null) {
            newUserConfirmation.setUpdateCount(1);
            insert(newUserConfirmation);
            cfCrmUserConfirmSnapshotDO.setDateType(CfCrmUserConfirmSnapshotDO.ConfirmSnapshotDataType.new_type.getCode());
        } else if (Objects.equals(userConfirmationDOFromDB.getConfirmStatus(), ClewPreproseMaterialEnums.ReportStatusEnum.INFO_WAIT_CONFIRM.getStatus())) {
            newUserConfirmation.setId(userConfirmationDOFromDB.getId());
            newUserConfirmation.setUpdateCount(userConfirmationDOFromDB.getUpdateCount() + 1);
            update(newUserConfirmation);
            cfCrmUserConfirmSnapshotDO.setDateType(CfCrmUserConfirmSnapshotDO.ConfirmSnapshotDataType.udpate_type.getCode());
        }
        cfCrmUserConfirmSnapshotDO.setConfirmId(newUserConfirmation.getId());
        cfCrmUserConfirmSnapshotDO.setDataSource(newUserConfirmation.getSourceType());
        cfCrmUserConfirmSnapshotDO.setEncryptRaisePhone(newUserConfirmation.getEncryptRaisePhone());
        cfCrmUserConfirmSnapshotDO.setPreposeMaterialId(newUserConfirmation.getPreposeMaterialId());
        cfCrmUserConfirmSnapshotDO.setDataStr(JSON.toJSONString(newUserConfirmation));
        userConfirmSnapshotService.insert(cfCrmUserConfirmSnapshotDO);
    }

    @Override
    public void sendRemindMsg(UserConfirmRemindMessage confirmRemindMessage) {
        CrowdfundingVolunteer crowdfundingVolunteer = volunteerService.getByUniqueCode(confirmRemindMessage.getUniqueCode());
        //如果是上级使用模板QEI0383,顾问使用模板JDB3175
        String templateCode = "JDB3175";
        if (confirmRemindMessage.isLeader()) {
            templateCode = "QEI0383";
        }
        msgClientV2.sendSms(templateCode, crowdfundingVolunteer.getMobile(), Map.of(1, ""));
        appPushCrmCaseMsgService.remindConfirmMsg(crowdfundingVolunteer, confirmRemindMessage);
        mqProducerService.sendConfirmMsg(confirmRemindMessage, System.currentTimeMillis() + DateUtils.MILLIS_PER_MINUTE * apolloService.getConfirmMsgRateMinute());
    }

    @Override
    public int update(CfCrmUserConfirmationDO cfCrmUserConfirmation) {
        return cfCrmUserConfirmationDao.update(cfCrmUserConfirmation);
    }


    @Override
    public int updatSanpshotPic(long id, String imageUrl) {
        if (StringUtils.isBlank(imageUrl)) {
            return 0;
        }
        return cfCrmUserConfirmationDao.updateSnapshotPic(id, imageUrl);
    }

    @Override
    public boolean deleteById(long id) {
        return cfCrmUserConfirmationDao.deleteById(id) > 0;
    }

    @Override
    public CfCrmUserConfirmationDO getByDraftId(long draftId) {
        return cfCrmUserConfirmationDao.getByDraftId(draftId);
    }

    @Override
    public CfCrmUserConfirmationDO getByPreposeMaterialId(long preposeMaterialId) {
        return cfCrmUserConfirmationDao.getByPreposeMaterialId(preposeMaterialId);
    }

    @Override
    public void updateStatus(long id, int status) {
        cfCrmUserConfirmationDao.updateStatus(id, status);
    }

    @Override
    public long countPage(ConfirmationPageParam confirmationPageParam) {
        return Optional.ofNullable(cfCrmUserConfirmationDao.countPage(confirmationPageParam)).orElse(0L);
    }

    @Override
    public List<CfCrmUserConfirmationDO> listPage(ConfirmationPageParam confirmationPageParam) {
        return cfCrmUserConfirmationDao.listPage(confirmationPageParam);
    }

}
