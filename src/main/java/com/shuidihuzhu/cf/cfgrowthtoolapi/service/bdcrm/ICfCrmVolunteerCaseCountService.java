package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmVolunteerCaseCountDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmDateTimeCaseCountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmVolunteerCaseCountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmVolunteerCaseCountSimpleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmSearchParam;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/11/28 3:50 PM
 */
public interface ICfCrmVolunteerCaseCountService {

    CfCrmVolunteerCaseCountDO getCfCrmVolunteerCaseCountDOByDateTimeWithAmountWithUniqueCode(String dateTime,
                                                                                             int amount,
                                                                                             String volunteerUniqueCode);

    List<CfCrmVolunteerCaseCountModel> getCaseCountModelGroupByUniqueCode(List<String> dateTimes, List<Integer> orgIdList, BdCrmSearchParam bdCrmSearchParam);

    List<CfCrmVolunteerCaseCountSimpleModel> getCfCrmVolunteerCaseCountSimpleModel(List<String> dateTimes, int amount,
                                                                                   String uniqueCode, Integer firstApprovePass);

    List<CfCrmDateTimeCaseCountModel> getCfCrmDateTimeCaseCountModel(List<String> dateTimes, int amount,
                                                                     List<Integer> orgIdList, int notFirstApprovePass);

    CfCrmVolunteerCaseCountModel getOrgCrmCrowdfundingModel(List<String> dateTimes, List<Integer> orgIdList, BdCrmSearchParam bdCrmSearchParam);

    List<CfCrmVolunteerCaseCountSimpleModel> aggregateByUniqueCodeAndDateTime(List<String> dateTimes, List<String> uniqueCodeList);
}
