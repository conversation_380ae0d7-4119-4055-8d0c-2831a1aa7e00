package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerAwardInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerServiceInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.PartnerServiceSearchParam;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2021-09-09 11:30 上午
 **/
public interface ICfLovePartnerService {

    /**
     * 公众号-工作台
     * @return
     */
    CommonPageModel<PartnerServiceInfo> serviceData(PartnerServiceSearchParam serviceSearchParam);
}
