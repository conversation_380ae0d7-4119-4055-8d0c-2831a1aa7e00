package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

public interface CfTouFangSignInterface {
    int insertList(List<CfTouFangSign> cfTouFangSigns);

    CfTouFangSign selectByCryptoMobile(String mobile);
}
