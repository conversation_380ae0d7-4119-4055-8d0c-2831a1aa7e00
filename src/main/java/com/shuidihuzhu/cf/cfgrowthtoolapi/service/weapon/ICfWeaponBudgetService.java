package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponBudgetDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponBudgetUseResultVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponAccessModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: fengxuan
 * @create 2020-10-14 9:36 下午
 **/
public interface ICfWeaponBudgetService {

    /**
     * 批量插入
     *
     * @param cfWeaponBudgetDOS
     * @return
     */
    void insertBatch(List<CfWeaponBudgetDO> cfWeaponBudgetDOS);


    /**
     * 返回所有的子预算,不论是否有效还是取消
     *
     * @param budgetGroupId
     * @return
     */
    List<CfWeaponBudgetDO> listAllBudgetByGroupId(int budgetGroupId);

    /**
     * 只返回目前有效的子预算
     *
     * @param budgetGroupId
     * @return
     */
    List<CfWeaponBudgetDO> listByGroupId(int budgetGroupId);

    CfWeaponBudgetDO getById(int budgetId);

    List<CfWeaponBudgetDO> listIds(List<Integer> ids);

    /**
     * 查看预算组对应的各种状态的子预算
     *
     * @param budgetGroupId
     * @param status        nullable, 参考
     * @param useStatus     nullable
     * @return
     */
    List<CfWeaponBudgetDO> listByGroupAndStatus(int budgetGroupId, Integer status, Integer useStatus);

    /**
     * 查询作废子预算
     * @param budgetGroupId
     * @return
     */
    List<CfWeaponBudgetDO> listInvalidBudget(int budgetGroupId);

    int invalidBudget(int budget);

    int cancelBudget(List<Integer> budgetIds);

    /**
     * 下发预算
     *
     * @param budgetId
     * @return
     */
    int useBudget(int budgetId);

    /**
     * 更新子预算总数/每日限制数
     */
    int updateResource(CfWeaponBudgetDO cfWeaponBudgetDO);

    /**
     * 根据预算组id查询子预算 按照 BudgetGroupId 分组 取第一个
     *
     * @param budgetGroupIds
     * @return
     */
    List<CfWeaponBudgetDO> listBudgetByBudgetGroupId(List<Integer> budgetGroupIds);

    /**
     * 根据预算组id查询子预算 按照 BudgetGroupId 分组 取第一个
     *
     * @param budgetIdSet
     * @return
     */
    List<CfWeaponBudgetDO> listBudgetByIds(Set<Integer> budgetIdSet);

    /**
     * 修改已使用名额，和冻结名额，addUseResource和addApplyingResource允许为负数 爱心首页主题活动武器由于存在提前扣减 冻结名额还有冻结金额传0
     *
     * @param id
     * @param addUseResource      增加已使用名额
     * @param addApplyingResource 增加冻结名额,当归还时 为负数
     * @param addUseMoney         增加已使用金额
     * @param addApplyingMoney    增加冻结金额,当归还时 为负数
     * @return
     */
    int modifyResource(int id, int addUseResource, int addApplyingResource, int addUseMoney, int addApplyingMoney);

    /**
     * 冻结预算
     *
     * @param budgetId
     * @return
     */
    int addChildApplyingResource(int budgetId, int childApplyingResource);

    int addChildApplyingMoney(int budgetId, int childApplyingMoney);

    int modifyChildUsedMoney(int budgetId, int childUnUsedMoney);

    /**
     * 借调资源,resourceNum资源数
     */
    void loanResource(int budgetId, int resourceNum);

    /**
     * 借调金额,loanMoney金额数
     */
    void loanMoney(int budgetId, int loanMoney);

    /**
     * 被借调资源,resourceNum资源数
     */
    void beLoanResource(int budgetId, int resourceNum);

    /**
     * 被借调金额,loanMoney金额数
     */
    void beLoanMoney(int budgetId, int loanMoney);

    WeaponAccessModel checkValidBudget(int budgetId);

    /**
     * 根据武器id找到所有子预算
     */
    List<CfWeaponBudgetDO> listByWeaponId(int weaponId);

    int repairResource(int id, int usedResource, int applyingResource);

    List<CfWeaponBudgetDO> listBudgetIdsByOrgIds(List<Integer> orgIds);

    CfWeaponBudgetDO getByBudgetGroupIdAndOrgId(int budgetGroupId, int orgId);

    int updateTotalMoneyById(int id, double childTotalMoney);

    List<CfWeaponBudgetUseResultVo> listAllByGroupIdAndOrgId(int budgetGroupId, List<Integer> orgIdList);

    List<CfWeaponBudgetDO> listByGroupIdAndOrgId(int budgetGroupId, List<Integer> orgIdList);

    List<CfWeaponBudgetDO> listByWeaponIdAndOrgId(int weaponId, List<Integer> orgIdList);

    List<CfWeaponBudgetDO> listByGroupIdsAndOrgId(List<Integer> budgetGroupIds, List<Integer> orgIdList);

    int modifyManagementMoney(int id, int modifyManagementMoney);

    int modifyManagementMoneySum(int id, int modifyManagementMoney);

    int addManagementApplyingMoney(int id, int managementApplyingMoney);

    int addManagementApplyingMoneySum(int id, int managementApplyingMoney);

}
