package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.KpiIllegalCaseDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.KpiIllegalCaseService;
import com.shuidihuzhu.cf.dao.bdkpi.KpiIllegalCaseDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 不合规案例(KpiIllegalCase)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-20 17:23:28
 */
@Service("kpiIllegalCaseService")
public class KpiIllegalCaseServiceImpl implements KpiIllegalCaseService {
   
    @Resource
    private KpiIllegalCaseDao kpiIllegalCaseDao;

    @Override
    public KpiIllegalCaseDO queryById(long id) {
        return kpiIllegalCaseDao.queryById(id);
    }
    

    @Override
    public int insert(KpiIllegalCaseDO kpiIllegalCase) {
        return kpiIllegalCaseDao.insert(kpiIllegalCase);
    }

    @Override
    public void batchInsert(List<KpiIllegalCaseDO> kpiIllegalCaseList) {
        if (CollectionUtils.isEmpty(kpiIllegalCaseList)) {
            return;
        }
        String monthKey = kpiIllegalCaseList.get(0).getMonthKey();
        List<KpiIllegalCaseDO> illegalCaseInDB = kpiIllegalCaseDao.queryMonthKey(monthKey);
        Set<Integer> caseIds = illegalCaseInDB.stream()
                .map(KpiIllegalCaseDO::getCaseId)
                .collect(Collectors.toSet());
        List<KpiIllegalCaseDO> kpiIllegalCaseDOS = kpiIllegalCaseList.stream()
                .filter(item -> item.getCaseId() > 0)
                .collect(Collectors.toList());
        for (KpiIllegalCaseDO kpiIllegalCaseDO : kpiIllegalCaseDOS) {
            if (!caseIds.contains(kpiIllegalCaseDO.getCaseId())) {
                kpiIllegalCaseDao.insert(kpiIllegalCaseDO);
            }
            caseIds.add(kpiIllegalCaseDO.getCaseId());
        }
    }

    @Override
    public List<KpiIllegalCaseDO> listByMonthKey(String monthKey) {
        return kpiIllegalCaseDao.queryMonthKey(monthKey);
    }

    @Override
    public int countAll(Date beginTime) {
        return Optional.of(kpiIllegalCaseDao.countAll(beginTime)).orElse(0);
    }

    @Override
    public List<KpiIllegalCaseDO> pageList(Date beginTime, int offset, int limit) {
        return kpiIllegalCaseDao.pageList(beginTime, offset, limit);
    }

}
