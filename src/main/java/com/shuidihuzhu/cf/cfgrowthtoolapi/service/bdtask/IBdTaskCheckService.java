package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdSubTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdTaskContext;

/**
 * @author: fengxuan
 * @create 2023-09-20 11:36
 **/
public interface IBdTaskCheckService {

    CrmBdTaskDO.TaskTypeEnum getTaskType();

    boolean checkNeedCreateTask(BdTaskContext bdTaskContext);

    boolean checkTaskComplete(BdTaskContext bdTaskContext);

    /**
     * 生成子任务
     */
    default void createSubTask(BdSubTaskContext bdSubTaskContext) {

    }

}
