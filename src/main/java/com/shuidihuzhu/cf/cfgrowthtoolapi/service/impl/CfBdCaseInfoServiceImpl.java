package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.*;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.AccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGwClewTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminOrganization;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.PepRealTimeLeaderInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCfSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.BdCaseTagService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CacheAdminValidCaseConfigService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.ISendAppPushCrmService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCaseBaseDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerCaseRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice.workwx.WorkWeixinContentBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.prepose.BdCaseInfoFillService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolWeaponUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCaseInfoDao;
import com.shuidihuzhu.cf.dao.bdcrm.PreVolunteerOrgInfoRelationDao;
import com.shuidihuzhu.cf.dao.clew.CfGwClewTaskDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.risk.client.risk.BlacklistVerifyClient;
import com.shuidihuzhu.cf.risk.client.rpc.PreposeMaterialRiskClient;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistCallPhaseEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistLunchCaseDto;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.*;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerCaseRelationDo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: zgq
 * @Date: 2019-05-29 15:15
 * @Description:
 */
@Service
@Slf4j
@RefreshScope
public class CfBdCaseInfoServiceImpl extends ESCfBdCaseInfoService implements ICfBdCaseInfoService {

    @Autowired
    private CfBdCaseInfoDao cfBdCaseInfoDao;
    @Autowired
    private ICfVolunteerService cfVolunteerServiceImpl;
    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegateImpl;
    @Value("${apollo.bdcrm.case.es:false}")
    private boolean useCaseEs;
    @Autowired
    private IWorkWeiXinDelegate workWeiXinDelegate;

    @Resource
    private AccountServiceDelegate accountServiceDelegate;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private ICfPatientMaterialClientDelegate patientMaterialClientDelegate;

    @Autowired
    private ICfKpiCaseBaseDataService cfKpiCaseBaseDataService;

    @Autowired
    private PreVolunteerOrgInfoRelationDao preVolunteerOrgInfoRelationDao;

    @Autowired
    private ICrmSelfBuiltOrgReadService organizationService;

    @Autowired
    private CfGwClewTaskDao cfGwClewTaskDao;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private ICfPartnerCaseAttributeDelegate partnerCaseAttributeDelegate;

    @Autowired
    private CfPartnerCaseRelationService partnerCaseRelationService;

    @Autowired
    private ICrmOrgConvertService crmOrgConvertService;

    @Autowired
    private BdCaseTagService bdCaseTagService;

    @Autowired
    private BlacklistVerifyClient blacklistVerifyClient;

    @Autowired
    private ICfVolunteerService volunteerService;

    @Autowired
    private ICfCrowdfundingCityDelegate cfCrowdfundingCityDelegate;

    @Autowired
    private PreposeMaterialRiskClient preposeMaterialRiskClient;

    @Autowired
    private BdCaseInfoFillService bdCaseInfoFillService;

    @Autowired
    private ICfBdCaseAttributionService cfBdCaseAttributionService;

    @Autowired
    private CfFirstApproveFeignClient firstApproveClient;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Autowired
    private BdCooperationCaseInfoService bdCooperationCaseInfoService;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private CacheAdminValidCaseConfigService validCaseConfigService;

    @Autowired
    private ISendAppPushCrmService appPushCrmService;

    @Autowired
    private ICfBdCrmMsgService cfBdCrmMsgService;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Override
    public CfBdCaseInfoDo getBdCaseInfoByInfoUuid(String uuid) {
        return cfBdCaseInfoDao.getBdCaseInfoByInfoUuid(uuid);
    }

    @Override
    public CfBdCaseInfoDo getBdCaseInfoByInfoId(int infoId) {
        return cfBdCaseInfoDao.getBdCaseInfoByInfoId(infoId);
    }

    @Override
    public CfBdCaseInfoDo getLatelyDateCreatedBdCaseInfoByInfoId(int infoId) {
        return cfBdCaseInfoDao.getLatelyDateCreatedBdCaseInfoByInfoId(infoId);
    }

    @Override
    public List<CfBdCaseInfoDo> getBdCaseInfoByInfoUuids(List<String> uuids) {
        if (CollectionUtils.isEmpty(uuids)) {
            return Collections.emptyList();
        }
        List<String> distinctuuids = uuids.stream().distinct().collect(Collectors.toList());
        List<List<String>> listList = Lists.partition(distinctuuids, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream()
                .map(list -> cfBdCaseInfoDao.getBdCaseInfoByInfoUuids(list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                })
                .orElse(Lists.newArrayList());
    }

    @Override
    public int insert(CfBdCaseInfoDo cfBdCaseInfoDo, CrowdfundingVolunteer volunteer, CrowdfundingInfo crowdfundingInfo) {
        if (cfBdCaseInfoDo.getCaseId() > 0) {
            CfBdCaseInfoDo bdCaseInfoInDb = this.getBdCaseInfoByInfoId(cfBdCaseInfoDo.getCaseId());
            if (bdCaseInfoInDb != null) {
                log.info("已经存在该案例:{}的CfBdCaseInfoDo", cfBdCaseInfoDo.getCaseId());
                return 0;
            }
            bdCaseInfoFillService.fillBdInfoByMaterial(cfBdCaseInfoDo);
            fillInfoByCrowdfunding(cfBdCaseInfoDo);
            //判断是否是学生案例 0否 1是
            int studentCase = this.getIsStudentCase(crowdfundingInfo, volunteer);
            log.info("getIsStudentCase:{}", studentCase);
            fillOrgInfo(cfBdCaseInfoDo, volunteer, studentCase);
        }

        if (StringUtils.isBlank(cfBdCaseInfoDo.getDateTime())) {
            cfBdCaseInfoDo.setDateTime(DateUtil.getYMDStrFromTimestamp(cfBdCaseInfoDo.getDateCreated()));
        }
        //找到对应的渠道信息
        fillPrimaryChannel(cfBdCaseInfoDo);
        int res = cfBdCaseInfoDao.insert(cfBdCaseInfoDo);
        if (res > 0) {
            //处理下爱心伙伴案例
            handlerPartnerCase(cfBdCaseInfoDo);
            // 记录筹款合作案例信息
            try {
                bdCooperationCaseInfoService.dealCooperationCaseInfo(cfBdCaseInfoDo);
            } catch (Exception e) {
                log.error("dealCooperationCaseInfo error", e);
            }
            // 发送校验案例归属离职/物料报警消息
            mqProducerService.sendBdCaseDismissVolunteerAlarmMsg(cfBdCaseInfoDo);
            
            // 发送案例生成代办消息，第二天早上9点触发
            try {
                // 直接调用消息服务发送案例生成代办消息
                mqProducerService.sendBdCaseAgencyMsg(cfBdCaseInfoDo.getCaseId(), 0);
                log.info("发送案例代理消息成功，caseId={}", cfBdCaseInfoDo.getCaseId());
            } catch (Exception e) {
                log.error("发送案例代理消息失败，caseId={}", cfBdCaseInfoDo.getCaseId(), e);
            }
        }
        return res;
    }

    private void fillOrgInfo(CfBdCaseInfoDo cfBdCaseInfoDo, CrowdfundingVolunteer volunteer, int studentCase) {
        BdCaseTagDO bdCaseTagDO = new BdCaseTagDO();
        //查询组织信息
        ICrmMemberInfoService.BdCrmOrganizationDOWithChain rightBdCaseOrg = crmMemberInfoService.getRightBdCaseOrg(cfBdCaseInfoDo.getUniqueCode());
        if (rightBdCaseOrg != null) {
            cfBdCaseInfoDo.setOrgId((int) rightBdCaseOrg.getId());
            cfBdCaseInfoDo.setOrgPath(rightBdCaseOrg.getChain());
            bdCaseTagDO.setOrgTag(GrowthtoolUtil.partnerOrgTag(rightBdCaseOrg.getChainName()));
        } else {
            PreVolunteerOrgInfoRelationDO preVolunteerOrgInfoRelationDO = preVolunteerOrgInfoRelationDao.getPreVolunteerOrgInfoRelationDO(cfBdCaseInfoDo.getUniqueCode());
            if (preVolunteerOrgInfoRelationDO != null) {
                cfBdCaseInfoDo.setOrgId(preVolunteerOrgInfoRelationDO.getOrgId());
                Map<Long, String> orgPathMap = organizationService.listChainByOrgIds(Lists.newArrayList(Long.valueOf(preVolunteerOrgInfoRelationDO.getOrgId())), "-");
                if (orgPathMap.containsKey(Long.valueOf(preVolunteerOrgInfoRelationDO.getOrgId()))) {
                    cfBdCaseInfoDo.setOrgPath(orgPathMap.get(Long.valueOf(preVolunteerOrgInfoRelationDO.getOrgId())));
                } else {
                    cfBdCaseInfoDo.setOrgPath(preVolunteerOrgInfoRelationDO.getOrgName());
                }
            }
        }
        //绑定对应的组织和人员角色到bd_case_tag
        bdCaseTagDO.setCaseId(cfBdCaseInfoDo.getCaseId());
        bdCaseTagDO.setRoleLevel(Optional.ofNullable(volunteer).map(CrowdfundingVolunteer::getLevel).orElse(CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel()));
        bdCaseTagDO.setCityId(getCityId(cfBdCaseInfoDo.getOrgId()));
        bdCaseTagDO.setDateCreated(cfBdCaseInfoDo.getDateCreated());
        bdCaseTagDO.setStudentCase(studentCase);
        //填充下实时组织架构信息
        if (volunteer != null) {
            List<PepRealTimeLeaderInfo> realTimeLeader = crmMemberInfoService.getRealTimeLeader(volunteer.getUniqueCode(), CrowdfundingVolunteerEnum.recruitLeaderRoles);
            if (CollectionUtils.isNotEmpty(realTimeLeader)) {
                bdCaseTagDO.setLeaderInfo(JSON.toJSONString(realTimeLeader));
            }
        }
        bdCaseTagService.insert(bdCaseTagDO);
    }

    public int getIsStudentCase(CrowdfundingInfo crowdfundingInfo, CrowdfundingVolunteer volunteer) {
        if (volunteer == null || CrowdfundingVolunteerEnum.delegateRoles.contains(volunteer.getLevel())) {
            return 0;
        }
        FeignResponse<CfFirsApproveMaterial> response = firstApproveClient.getCfFirstApproveMaterialByCaseId(crowdfundingInfo.getId());
        CfFirsApproveMaterial cfFirsApproveMaterial = Optional.ofNullable(response)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData).orElse(null);
        //过滤掉出生证发起
        if (cfFirsApproveMaterial != null && UserIdentityType.birth.getCode() == cfFirsApproveMaterial.getPatientIdType()) {
            return 0;
        }
        //判断年龄是否满足6岁-30岁
        int patientAge = crowdFundingFeignDelegate.getPatientAge(crowdfundingInfo.getId());
        String title = crowdfundingInfo.getTitle();
        String content = crowdfundingInfo.getContent();
        //判断筹款标题和内容是否包含关键字
        List<String> keywordList = apolloService.getStudentCaseKeyWordList();
        boolean anyMatch = keywordList.stream().anyMatch(word -> content.contains(word) || title.contains(word));
        if (anyMatch && patientAge >= 6 && patientAge <= 30) {
            return 1;
        }
        return 0;
    }


    private void fillInfoByCrowdfunding(CfBdCaseInfoDo cfBdCaseInfoDo) {
        CrowdfundingInfo caseInfo = crowdFundingFeignDelegateImpl.getCaseInfoById(cfBdCaseInfoDo.getCaseId());
        if (caseInfo != null) {
            UserInfoModel userInfoModel = accountServiceDelegate.getUserInfoModelByUserId(caseInfo.getUserId());
            if (userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
                cfBdCaseInfoDo.setRaiserPhone(userInfoModel.getCryptoMobile());
            }
        }
        //查询患者信息
        Map<Integer, CfFirsApproveMaterial> authorInfoMap = crowdFundingFeignDelegateImpl.getMapByInfoIds(Lists.newArrayList(cfBdCaseInfoDo.getCaseId()));
        if (MapUtils.isNotEmpty(authorInfoMap)) {
            CfFirsApproveMaterial approveMaterial = authorInfoMap.get(cfBdCaseInfoDo.getCaseId());
            if (approveMaterial != null) {
                cfBdCaseInfoDo.setPatientName(approveMaterial.getPatientRealName());
            }
        }
    }



    private void handlerPartnerCase(CfBdCaseInfoDo cfBdCaseInfoDo) {
        //案例绑定兼职
        ImmutablePair<String, Boolean> pair = partnerCaseAttributeDelegate.getAttributePartnerByCaseId(cfBdCaseInfoDo.getCaseId(), cfBdCaseInfoDo.getUniqueCode(), cfBdCaseInfoDo.getRaiserPhone());
        if (StringUtils.isNotBlank(pair.getLeft())) {
            cfBdCaseInfoDao.updatePartnerUniqueCodeByInfoUuid(cfBdCaseInfoDo.getInfoUuid(), pair.getLeft());
        }
        if (pair.getRight()) {
            CfPartnerCaseRelationDo caseRelationDo = CfPartnerCaseRelationDo.builder()
                    .caseId(cfBdCaseInfoDo.getCaseId())
                    .uniqueCode(pair.getLeft())
                    .leaderUniqueCode(cfBdCaseInfoDo.getUniqueCode())
                    .build();
            partnerCaseRelationService.insert(caseRelationDo);
        }
    }


    private int getCityId(Integer orgId) {
        if (orgId == null) {
            return 0;
        }
        BdCrmOrganizationDO bdCrmOrganizationDO = organizationService.getCurrentOrgById(orgId);
        if (bdCrmOrganizationDO != null) {
            if (Objects.equals(bdCrmOrganizationDO.getOrgAttribute(), OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode())) {
                return bdCrmOrganizationDO.getCityId();
            }
            return organizationService.listAllSubOrgExcludeSelf(orgId)
                    .stream()
                    .filter(item -> Objects.equals(item.getOrgAttribute(), OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode()))
                    .map(BdCrmOrganizationDO::getCityId)
                    .filter(item -> item > 0)
                    .findFirst()
                    .orElse(0);
        }
        return 0;
    }


    private void fillPrimaryChannel(CfBdCaseInfoDo cfBdCaseInfoDo) {
        DateTime caseCreateTime = new DateTime(cfBdCaseInfoDo.getDateCreated());
        CfGwClewTaskDO lastGwTask = cfGwClewTaskDao.findLastGwTask(cfBdCaseInfoDo.getRaiserPhone(), cfBdCaseInfoDo.getUniqueCode(), caseCreateTime.minusDays(30).toString(GrowthtoolUtil.ymdfmt), caseCreateTime.toString(GrowthtoolUtil.ymdfmt));
        log.info("case_id:{}对应的渠道信息为:{}", cfBdCaseInfoDo.getCaseId(), lastGwTask);
        //设置默认值
        cfBdCaseInfoDo.setSourceType(-1);
        if (lastGwTask != null) {
            String primaryChannel = lastGwTask.getPrimaryChannel();
            int sourceType = lastGwTask.getSourceType();
            int selfRaise = GrowthtoolUtil.notSelfRaise.contains(lastGwTask.getSourceType()) ? 1 : 0;
            cfBdCaseInfoDo.setPrimaryChannel(primaryChannel);
            cfBdCaseInfoDo.setSourceType(sourceType);
            cfBdCaseInfoDo.setSelfRaise(selfRaise);
        }
    }


    @Override
    public int updateCaseField(CfBdCaseInfoDo cfBdCaseInfoDo) {
        return cfBdCaseInfoDao.updateCaseField(cfBdCaseInfoDo);
    }

    @Override
    public int updateCountIncrement(String infoId) {
        return cfBdCaseInfoDao.updateCountIncrement(infoId);
    }

    @Override
    public List<CfBdCaseInfoDo> getBdCaseInfoByUniqueCodeWithFlag(String uniqueCode, int flag, Date startTime, Date endTime) {
        return cfBdCaseInfoDao.getBdCaseInfoByUniqueCodeWithFlag(uniqueCode, flag, startTime, endTime);
    }


    @Override
    public List<CfBdCaseInfoDo> getCfBdCaseInfoByOrgListWithFlag(List<Integer> orgIdList, int flag, String startTime, String endTime, Integer pageNo, Integer pageSize) {
        int offset = (pageNo - 1) * pageSize;
        return cfBdCaseInfoDao.getCfBdCaseInfoByOrgListWithFlag(orgIdList, flag, startTime, endTime, offset, pageSize);
    }

    @Override
    public List<CfBdCaseInfoDo> getBdCaseInfoByUniqueCode(String uniqueCode, Date startTime, Date endTime, Integer pageNo, Integer pageSize) {
        int offset = (pageNo - 1) * pageSize;
        return cfBdCaseInfoDao.getBdCaseInfoByUniqueCode(uniqueCode, startTime, endTime, offset, pageSize);
    }



    @Override
    public List<VolunteerInfoUuidModel> getVolunteerInfoUuidModelbyUniqueCodeListWithTime(String startTime,
                                                                                          String endTime,
                                                                                          List<String> volunteerUniqueCodes) {
        List<String> distinctvolunteerUniqueCodes = volunteerUniqueCodes.stream().distinct().collect(Collectors.toList());
        List<List<String>> listList = Lists.partition(distinctvolunteerUniqueCodes, GeneralConstant.MAX_PAGE_SIZE);
        List<VolunteerInfoUuidModel> result = listList.parallelStream().map(list -> cfBdCaseInfoDao.getVolunteerInfoUuidModelbyUniqueCodeListWithTime(startTime, endTime, list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                })
                .get();
        return result;
    }

    @Override
    public List<VolunteerCaseModel> listCaseModelByUniqueCodesWithTime(String startTime,
                                                                       String endTime,
                                                                       List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Lists.newArrayList();
        }
        List<String> uniqueCodeSet = uniqueCodes.stream().distinct().collect(Collectors.toList());
        List<List<String>> listList = Lists.partition(uniqueCodeSet, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> cfBdCaseInfoDao.listCaseModelByUniqueCodesWithTime(startTime, endTime, list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                })
                .get();
    }

    @Override
    public List<VolunteerInfoUuidModel> getVolunteerInfoUuidModelbyVolunteerTypeWithTime(String startTime,
                                                                                         String endTime,
                                                                                         int volunteerType) {
        return cfBdCaseInfoDao.getVolunteerInfoUuidModelbyVolunteerTypeWithTime(startTime, endTime, volunteerType);
    }

    @Override
    public int saveOrUpdateCfBdCaseInfo(CfInfoExt cfInfoExt) {
        if (StringUtils.isBlank(cfInfoExt.getVolunteerUniqueCode())) {
            return 0;
        }
        CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegateImpl.getCaseInfoById(cfInfoExt.getCaseId());
        CfBdCaseInfoDo caseInfo = cfBdCaseInfoDao.getBdCaseInfoByInfoUuid(cfInfoExt.getInfoUuid());
        if (caseInfo == null) {
            caseInfo = new CfBdCaseInfoDo();
            caseInfo.setInfoUuid(cfInfoExt.getInfoUuid());
            caseInfo.setDateCreated(cfInfoExt.getDateCreated());
            caseInfo.setDateTime(DateUtil.getYMDStrFromTimestamp(cfInfoExt.getDateCreated()));
            caseInfo.setUniqueCode(cfInfoExt.getVolunteerUniqueCode());
            caseInfo.setCaseFirstCount(1);
            caseInfo.setCaseId(cfInfoExt.getCaseId());
            caseInfo.setBdFollowed(cfInfoExt.getBdFollowed());
            caseInfo.setAmount(crowdfundingInfo == null ? null : crowdfundingInfo.getAmount());
            //判断下案例是几发
            int caseDuplicateNum = patientMaterialClientDelegate.caseDuplicateNum(cfInfoExt.getCaseId());
            caseInfo.setCaseDuplicateFlag(caseDuplicateNum);
            CrowdfundingVolunteer volunteer = cfVolunteerServiceImpl.getVolunteerByUniqueCode(cfInfoExt.getVolunteerUniqueCode());
            if (volunteer != null) {
                caseInfo.setVolunteerType(volunteer.getVolunteerType());
                caseInfo.setMisId(volunteer.getMis());
                caseInfo.setWhalePartner(volunteer.getPartnerTag());
                AdminOrganization userOrganization = crmOrgConvertService.getUserOrganizationByUniqueCode(volunteer.getUniqueCode());
                caseInfo.setOrgId(userOrganization == null ? null : ((int) userOrganization.getId()));
                log.info(this.getClass().getSimpleName() + " saveOrUpdateCfBdCaseInfo getVolunteerByUniqueCode({})", cfInfoExt.getVolunteerUniqueCode());
            }
            this.insert(caseInfo, volunteer, crowdfundingInfo);
        } else {
            if (Objects.nonNull(crowdfundingInfo)) {
                caseInfo.setAmount(crowdfundingInfo.getAmount());
                //修改金额
                this.updateCfBdCaseInfoAmount(Lists.newArrayList(caseInfo));
                //修改捐单
                cfBdCaseInfoDao.updateDonateNum(caseInfo.getId(), crowdfundingInfo.getDonationCount());
                //判断是否是有效案例
                boolean validCase = validCase(crowdfundingInfo, caseInfo);
                if (!validCase) {
                    cfBdCaseInfoDao.updateValidCase(caseInfo.getId(), ValidCaseEnum.NON_VALID.getCode());
                }
            }
        }
        return 0;
    }

    private boolean validCase(CrowdfundingInfo crowdfundingInfo, CfBdCaseInfoDo cfBdCaseInfoDo) {
        if (Objects.isNull(cfBdCaseInfoDo)) {
            return false;
        }
        //判断是否是有效案例
        AdminValidCaseConfigDO adminValidCaseConfigDO = validCaseConfigService.getAdminValidCaseConfigDO(cfBdCaseInfoDo.getOrgId());
        return crowdfundingInfo.getAmount() >= adminValidCaseConfigDO.getValidAmount()
                && crowdfundingInfo.getDonationCount() >= adminValidCaseConfigDO.getValidDonateNum();
    }

    @Override
    public int updateBdFollowedByInfoUuidWithUniqueCode(String infoUuid, String uniqueCode, int bdFollowed) {
        return cfBdCaseInfoDao.updateBdFollowedByInfoUuidWithUniqueCode(infoUuid, uniqueCode, bdFollowed);
    }



    @Override
    public void repairCfBdCaseInfoAmount(String startTime){
        int limit = 100;
        List<CfBdCaseInfoDo> cfBdCaseInfoDos = cfBdCaseInfoDao.getNoAmount(startTime, 0, limit);
        while (CollectionUtils.isNotEmpty(cfBdCaseInfoDos)) {
            List<Integer> caesIds = cfBdCaseInfoDos.stream().map(CfBdCaseInfoDo::getCaseId).distinct().collect(Collectors.toList());
            List<CrowdfundingInfo> crowdfundingList = crowdFundingFeignDelegateImpl.getCrowdfundingListById(caesIds);
            Map<Integer, List<CrowdfundingInfo>> caseIdMap = crowdfundingList.stream().collect(Collectors.groupingBy(CrowdfundingInfo::getId, Collectors.toList()));
            for (CfBdCaseInfoDo cfBdCaseInfoDo : cfBdCaseInfoDos) {
                if (cfBdCaseInfoDo.getCaseId() == 0 || CollectionUtils.isEmpty(caseIdMap.get(cfBdCaseInfoDo.getCaseId()))) {
                    continue;
                }
                List<CrowdfundingInfo> crowdfundingInfos = caseIdMap.get(cfBdCaseInfoDo.getCaseId());
                CrowdfundingInfo crowdfundingInfo = crowdfundingInfos.get(crowdfundingInfos.size() - 1);
                cfBdCaseInfoDo.setAmount(crowdfundingInfo.getAmount());
            }
            Integer maxId = cfBdCaseInfoDos.get(cfBdCaseInfoDos.size() - 1).getId();
            Integer minId = cfBdCaseInfoDos.get(0).getId();
            log.info(this.getClass().getName() + " 当前处理的 cf_bd_case_info id：{}-{} ", minId, maxId);
            cfBdCaseInfoDao.updateCfBdCaseInfoAmount(cfBdCaseInfoDos);
            cfBdCaseInfoDos = cfBdCaseInfoDao.getNoAmount(startTime, maxId, limit);
        }
    }

    @Override
    public int updateCfBdCaseInfoAmount(List<CfBdCaseInfoDo> cfBdCaseInfoDos) {
        cfBdCaseInfoDos = cfBdCaseInfoDos.stream().filter(model -> model.getAmount() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cfBdCaseInfoDos)) {
            return 0;
        }
        return cfBdCaseInfoDao.updateCfBdCaseInfoAmount(cfBdCaseInfoDos);
    }

    @Override
    public long getCountByUniqueCodeList(List<String> uniqueCodeList, String startTime, String endTime) {
        if (useCaseEs) {
            OpResult<Long> fromEs = getCountByUniqueCodeListFromEs(uniqueCodeList, startTime, endTime);
            return fromEs.isSuccess() ? fromEs.getData() : 0L;
        }
        List<String> distinctvolunteerUniqueCodes = uniqueCodeList.stream().distinct().collect(Collectors.toList());
        List<List<String>> listList = Lists.partition(distinctvolunteerUniqueCodes, GeneralConstant.MAX_PAGE_SIZE);
        long result = listList.parallelStream().map(list -> cfBdCaseInfoDao.getCountbyUniqueCodeListWithTime(startTime, endTime, list))
                .reduce((total, item) -> total += item)
                .get();
        return result;
    }

    @Override
    public List<CfBdCaseInfoDo> listCaseIdByOrgIdList(List<Integer> orgIdList, String startTime, String endTime) {
        //只使用es查询
        return listCaseIdByOrgIdListFromEs(orgIdList, startTime, endTime);
    }


    @Override
    public List<CfBdCaseInfoDo> listCaseIdByUniqueCodeList(List<String> uniqueCodeList, String startTime, String endTime) {
        //只使用es查询
        return listCaseIdByUniqueCodesFromEs(uniqueCodeList, startTime, endTime);
    }


    @Override
    public List<CfBdCaseInfoDo> getBdCaseInfoByUniqueCodeAndTimeAndCfAmount(List<String> uniqueCodeList, Integer orgId, Date startTime, Date endTime, BdCrmSearchParam bdCrmSearchParam, Integer pageNo, Integer pageSize) {
        int offset = (pageNo - 1) * pageSize;
        return cfBdCaseInfoDao.getBdCaseInfoByUniqueCodeAndTimeAndCfAmountRange(uniqueCodeList, orgId, startTime, endTime, bdCrmSearchParam, offset, pageSize);
    }


    @Override
    public long getCountByUniqueCodeAndTimeAndCfAmount(List<String> uniqueCodeList, Integer orgId, Date startTime, Date endTime, BdCrmSearchParam bdCrmSearchParam) {
        if (useCaseEs) {
            OpResult<Long> fromEs = getCountByUniqueCodeAndTimeAndCfAmountFromEs(uniqueCodeList, orgId, startTime, endTime, bdCrmSearchParam);
            // 先查 es  如果es查询失败  再查dao
            return fromEs.isSuccess() ? fromEs.getData() : 0L;
        }
        return getCountByUniqueCodeAndTimeAndCfAmountFromDao(uniqueCodeList, startTime, endTime, bdCrmSearchParam);
    }

    @Override
    public long countCfByCfSearchParam(BdCfSearchParam caseSearchParam) {
        if (caseSearchParam == null) {
            return 0L;
        }
        return cfBdCaseInfoDao.countCfByCfSearchParam(caseSearchParam);
    }

    @Override
    public List<CfBdCaseInfoDo> listCfByBdCfSearchParam(BdCfSearchParam bdCfSearchParam) {
        if (bdCfSearchParam == null) {
            return Lists.newArrayList();
        }
        //解密手机号
        List<CfBdCaseInfoDo> cfBdCaseInfoDos = cfBdCaseInfoDao.listCfByBdCfSearchParam(bdCfSearchParam);
//        cfBdCaseInfoDos.forEach(item -> item.setRaiserPhone(shuidiCipher.decrypt(item.getRaiserPhone())));
        return cfBdCaseInfoDos;
    }


    private long getCountByUniqueCodeAndTimeAndCfAmountFromDao(List<String> uniqueCodeList, Date startTime, Date endTime, BdCrmSearchParam bdCrmSearchParam) {
        List<String> distinctUniqueCodes = uniqueCodeList.stream().distinct().collect(Collectors.toList());
        List<List<String>> listList = Lists.partition(distinctUniqueCodes, GeneralConstant.MAX_PAGE_SIZE);
        long result = listList.parallelStream().map(list -> cfBdCaseInfoDao.getCountByUniqueCodeAndTimeAndCfAmountRange(list, startTime, endTime, bdCrmSearchParam))
                .reduce((total, item) -> total += item)
                .get();
        return result;
    }

    @Async
    @Override
    public void repairCfBdCaseInfo(List<String> infoUuids) {
        for (String infouuid : infoUuids) {
            CfInfoExt cfInfoExt = crowdFundingFeignDelegateImpl.getCfInfoExtByuuid(infouuid);
            if (infouuid == null) {
                log.warn(this.getClass().getSimpleName() + " repairCfBdCaseInfo  getCfInfoExtByuuid result is null");
                continue;
            }
            CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegateImpl.getCrowdfundingInfoByInfouuid(infouuid);
            if (crowdfundingInfo == null) {
                continue;
            }
            CrowdfundingVolunteer volunteer = cfVolunteerServiceImpl.getVolunteerByUniqueCode(cfInfoExt.getVolunteerUniqueCode());
            if (volunteer == null) {
                log.warn(this.getClass().getSimpleName() + " repairCfBdCaseInfo  getVolunteerByUniqueCode fail");
                continue;
            }
            //查询当前案例是否存在
            CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoDao.getBdCaseInfoByInfoUuid(infouuid);
            if (cfBdCaseInfoDo == null) {
                cfBdCaseInfoDo = new CfBdCaseInfoDo();
                cfBdCaseInfoDo.setVolunteerType(volunteer.getVolunteerType());
                cfBdCaseInfoDo.setInfoUuid(infouuid);
                cfBdCaseInfoDo.setMisId(StringUtils.isEmpty(volunteer.getMis()) ? "" : volunteer.getMis());
                cfBdCaseInfoDo.setWhalePartner(volunteer.getPartnerTag());
                cfBdCaseInfoDo.setDateCreated(cfInfoExt.getDateCreated());
                cfBdCaseInfoDo.setDateTime(DateUtil.getYMDStrFromTimestamp(cfInfoExt.getDateCreated()));
                cfBdCaseInfoDo.setUniqueCode(volunteer.getUniqueCode());
                cfBdCaseInfoDo.setCaseId(cfInfoExt.getCaseId());
                cfBdCaseInfoDo.setBdFollowed(cfInfoExt.getBdFollowed());
                cfBdCaseInfoDo.setAmount(crowdfundingInfo.getAmount());
                //设置初审通过时间
                cfBdCaseInfoDo.setFirstApproveTime(cfInfoExt.getFirstApproveTime());
                this.insert(cfBdCaseInfoDo, volunteer, crowdfundingInfo);
            }
            workWeiXinDelegate.sendByUser(Lists.newArrayList("fengxuan"), "案例:{} 已修补到cf_bd_case_info中:" + infouuid);
        }
    }

    @Override
    public long getMinId(String caseStartTime, String caseEndTime) {
        return cfBdCaseInfoDao.getMinId(caseStartTime, caseEndTime);
    }

    @Override
    public void updateCaseStatus(CfBdCaseInfoDo cfBdCaseInfoDo) {
        if (cfBdCaseInfoDo == null || cfBdCaseInfoDo.getCaseId() <= 0) {
            return;
        }
        if (cfBdCaseInfoDo.getFirstApproveStatus() <= 0
                && cfBdCaseInfoDo.getMaterialApproveStatus() <= 0
                && cfBdCaseInfoDo.getCaseEndStatus() <= 0
                && cfBdCaseInfoDo.getFirstApproveTime() == null) {
            log.info("caseId:{}没有需要修改的案例状态信息", cfBdCaseInfoDo.getCaseId());
            return;
        }
        cfBdCaseInfoDao.updateCaseStatusByCaseId(cfBdCaseInfoDo);
    }

    @Override
    public List<CfBdCaseInfoDo> needSyncRaiseInfoBdCase(long id, int limit) {
        return cfBdCaseInfoDao.needSyncRaiseInfoBdCase(id, limit);
    }

    @Override
    public void syncReportInfo(CfBdCaseInfoDo cfBdCaseInfoDo) {
        try {
            caseHospitalDepartmentCheck(cfBdCaseInfoDo);
        } catch (Exception e) {
            log.error("syncReportInfo error", e);
        }

        if (cfBdCaseInfoDo.getCaseStatus() == 0
                && StringUtils.isBlank(cfBdCaseInfoDo.getHospitalName())
                && StringUtils.isBlank(cfBdCaseInfoDo.getDepartmentName())
                && StringUtils.isBlank(cfBdCaseInfoDo.getDiseaseName())
                && StringUtils.isBlank(cfBdCaseInfoDo.getRaiserPhone())
                && StringUtils.isBlank(cfBdCaseInfoDo.getCityName())
                && StringUtils.isBlank(cfBdCaseInfoDo.getVhospitalCode())) {
            log.info("无能更新的有效数据");
            return;
        }
        if (cfBdCaseInfoDo.getCaseId() == null || cfBdCaseInfoDo.getCaseId() <= 0) {
            log.info("caseId为0,无需更新");
            return;
        }
        cfBdCaseInfoDao.syncReportInfoByCaseId(cfBdCaseInfoDo);
    }


    @Override
    public OpResult<BdServiceInfoVo> getBdServiceInfoVoByUniqueCode(String uniqueCode) {
        return this.getBdServiceInfoVoByUniqueCodeFromES(uniqueCode);
    }

    @Override
    public OpResult<BdServiceInfoVo> getBdServiceInfoVoOnrYearAgoByUniqueCode(String uniqueCode, String createTime){
        return this.getBdServiceInfoVoByUniqueCodeFromESOneYearAgo(uniqueCode, createTime);
    }

    @Override
    public List<Integer> getMaxAmountCaseIdList(String uniqueCode, long amount, String dateCreated) {
        return cfBdCaseInfoDao.getMaxAmountCaseIdList(uniqueCode, amount, dateCreated);
    }

    @Override
    public void updateNoOrgIdMis(String uniqueCode, String mis, int orgId, String orgPath) {
        if (StringUtils.isBlank(uniqueCode)) {
            return;
        }
        mis = Optional.ofNullable(mis).orElse("");
        orgPath = Optional.ofNullable(orgPath).orElse("");
        cfBdCaseInfoDao.updateNoOrgIdMis(uniqueCode, mis, orgId, orgPath);
    }


    /**
     * 获得 key=uniqueCode  value=List<案例id>
     *
     * @param startTimeStr
     * @param uniqueCodeList
     * @return
     */
    @Override
    public Map<String, List<Long>> getUniqueCodeMapInfoIdsForOneDay(String startTimeStr, List<String> uniqueCodeList) {
        //  查询当天发起的案例
        String endTimeStr = DateUtil.formatDate(DateUtils.addDays(DateUtil.parseDate(startTimeStr), 1));
        List<VolunteerCaseModel> volunteerCaseModels = this.listCaseModelByUniqueCodesWithTime(startTimeStr, endTimeStr, uniqueCodeList);
        Map<String, List<Long>> resultMap = Maps.newHashMap();

        Map<String, List<VolunteerCaseModel>> uniqueCodeMapInfoUuids = volunteerCaseModels
                .stream()
                .collect(Collectors.groupingBy(VolunteerCaseModel::getVolunteerUniqueCode, Collectors.toList()));
        for (String uniqueCode : uniqueCodeList) {
            List<VolunteerCaseModel> volunteerCaseModelList = uniqueCodeMapInfoUuids.get(uniqueCode);
            if (CollectionUtils.isEmpty(volunteerCaseModelList)) {
                resultMap.put(uniqueCode, Lists.newArrayList());
                continue;
            }
            resultMap.put(uniqueCode, volunteerCaseModelList.stream().map(VolunteerCaseModel::getCaseId).collect(Collectors.toList()));
        }
        return resultMap;
    }

    @Override
    public List<CfBdCaseInfoDo> listWithTimeRange(Date startTime, Date endTime, int offset, int limit) {
        return cfBdCaseInfoDao.listWithTimeRange(startTime, endTime, offset, limit);
    }


    @Override
    public int syncCaseStatusBatch(List<CfBdCaseInfoDo> cfBdCaseInfoDoList) {
        if (CollectionUtils.isEmpty(cfBdCaseInfoDoList)) {
            log.info("cfBdCaseInfoDo为空");
            return 0;
        }
        Map<Integer, CfBdCaseInfoDo> caseIdTBdCaseInfoFromDB = cfBdCaseInfoDoList.stream().collect(Collectors.toMap(CfBdCaseInfoDo::getCaseId, Function.identity(), (before, after) -> after));
        List<Integer> caseIds = Lists.newArrayList(caseIdTBdCaseInfoFromDB.keySet());
        //查询案例状态
        Map<Integer, CfBdCaseInfoDo> caseIdTCaseStatusRealTime = obtainCaseStatus(caseIds);
        int affectRows = 0;
        for (Integer caseId : caseIdTBdCaseInfoFromDB.keySet()) {
            CfBdCaseInfoDo caseStatusRealTime = caseIdTCaseStatusRealTime.get(caseId);
            CfBdCaseInfoDo bdCaseInfoDo = caseIdTBdCaseInfoFromDB.get(caseId);
            if (caseStatusRealTime != null &&
                    (!ObjectUtils.nullSafeEquals(bdCaseInfoDo.getFirstApproveStatus(), caseStatusRealTime.getFirstApproveStatus())
                            || !ObjectUtils.nullSafeEquals(bdCaseInfoDo.getMaterialApproveStatus(), caseStatusRealTime.getMaterialApproveStatus())
                            || !ObjectUtils.nullSafeEquals(bdCaseInfoDo.getCaseEndStatus(), caseStatusRealTime.getCaseEndStatus())
                            || !ObjectUtils.nullSafeEquals(bdCaseInfoDo.getFirstApproveTime(), caseStatusRealTime.getFirstApproveTime())
                            || !ObjectUtils.nullSafeEquals(bdCaseInfoDo.getAmount(), caseStatusRealTime.getAmount()))
            ) {
                //更新信息
                log.info("更新bd_case_info中的信息,caseId:{},案例实时状态初审:{},材料审核:{},案例结束:{}初审通过时间:{}",
                        caseId, caseStatusRealTime.getFirstApproveStatus(), caseStatusRealTime.getMaterialApproveStatus(),
                        caseStatusRealTime.getCaseEndStatus(), caseStatusRealTime.getFirstApproveTime());
                updateCaseStatus(caseStatusRealTime);
                affectRows++;
            }
        }
        return affectRows;
    }

    @Override
    public void updateValidAmount(int caseId, int validAmount, int validDonate) {
        if (caseId <= 0) {
            return;
        }
        cfBdCaseInfoDao.updateValidAmount(caseId, validAmount, validDonate);
    }

    @Override
    public boolean getIsBdDirtctManage(int caseId) {
        CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoDao.getBdCaseInfoByInfoId(caseId);
        if (Objects.isNull(cfBdCaseInfoDo)) {
            return false;
        }
        CrowdfundingVolunteer volunteer = volunteerService.getVolunteerByUniqueCode(cfBdCaseInfoDo.getUniqueCode());
        if (Objects.isNull(volunteer)) {
            return false;
        }
        return CrowdfundingVolunteerEnum.bdDirectManageRoles.contains(volunteer.getLevel()) || CrowdfundingVolunteerEnum.delegateRoles.contains(volunteer.getLevel());
    }


    @Override
    public Map<Integer, CfBdCaseInfoDo> obtainCaseStatus(List<Integer> caseIds) {
        Map<Integer, CfBdCaseInfoDo> caseIdTCaseStatus = Maps.newHashMap();
        FeignResponse<List<CrowdfundingInfo>> crowdfundingListById = crowdfundingFeignClient.getCrowdfundingListById(caseIds);
        if (crowdfundingListById.notOk() || CollectionUtils.isEmpty(crowdfundingListById.getData())) {
            log.warn("批量查询案例异常,response:{}", JSON.toJSONString(crowdfundingListById));
            return caseIdTCaseStatus;
        }
        Map<Integer, CrowdfundingInfo> caseIdTCfInfo = crowdfundingListById.getData().stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (before, after) -> after));
        //暂时先用点查ext
        for (Integer caseId : caseIds) {
            CrowdfundingInfo crowdfundingInfo = caseIdTCfInfo.get(caseId);
            if (crowdfundingInfo == null) {
                log.warn("查不到对应的案例信息");
                continue;
            }
            CfBdCaseInfoDo cfBdCaseInfoDo = new CfBdCaseInfoDo();
            CfCaseStatusChangeMqModel cfCaseStatusChangeMqModel = new CfCaseStatusChangeMqModel();
            CrowdfundingStatus status = crowdfundingInfo.getStatus();
            Date endTime = crowdfundingInfo.getEndTime();

            cfCaseStatusChangeMqModel.setCaseId(caseId);
            cfBdCaseInfoDo.setCaseId(caseId);

            cfBdCaseInfoDo.setAmount(crowdfundingInfo.getAmount());

            cfCaseStatusChangeMqModel.setMaterialAuditStatusCode(status.value());
            cfBdCaseInfoDo.setMaterialApproveStatus(status.value());

            boolean caseEnd = endTime == null || endTime.before(new Date());
            cfCaseStatusChangeMqModel.setCaseEndStatus(caseEnd);
            cfBdCaseInfoDo.setCaseEndStatus(caseEnd ? CfStatusEnums.CaseEndStatusEnum.end.getCode() : CfStatusEnums.CaseEndStatusEnum.not_end.getCode());

            FeignResponse<CfInfoExt> cfInfoExtByCaseId = crowdfundingFeignClient.getCfInfoExtByCaseId(caseId);
            if (cfInfoExtByCaseId.notOk() || cfInfoExtByCaseId.getData() == null) {
                log.warn("查不到对应的ext信息");
            } else {
                int firstApproveStatus = cfInfoExtByCaseId.getData().getFirstApproveStatus();
                cfCaseStatusChangeMqModel.setFirstApproveStatusCode(firstApproveStatus);

                cfBdCaseInfoDo.setFirstApproveStatus(firstApproveStatus);
                Timestamp firstApproveTime = cfInfoExtByCaseId.getData().getFirstApproveTime();
                if (firstApproveTime != null) {
                    cfBdCaseInfoDo.setFirstApproveTime(firstApproveTime);
                    cfBdCaseInfoDo.setApproveDateTime(new DateTime(firstApproveTime).toString("yyyy-MM-dd"));
                }
            }
            CfStatusEnums.BDCaseInfoStatusEnum caseInfoStatusEnum = CfStatusEnums.buildByCaseStatusChangeMqModel(cfCaseStatusChangeMqModel);

            cfBdCaseInfoDo.setCaseStatus(caseInfoStatusEnum.getCode());
            caseIdTCaseStatus.put(caseId, cfBdCaseInfoDo);
        }
        return caseIdTCaseStatus;
    }

    @Override
    public List<CfBdCaseInfoDo> listNotEndCaseInfoForQrCode(String uniqueCode, int offset, int pageSize) {
        return cfBdCaseInfoDao.listNotEndCaseInfoForQrCode(uniqueCode, offset, pageSize);
    }

    @Override
    public List<CfBdCaseInfoDo> listCaseInfoByUniqueCode(String uniqueCode, int size) {
        return cfBdCaseInfoDao.listCaseInfoByUniqueCode(uniqueCode, size);
    }

    @Override
    public void updateShareTitleAndContent(Integer id, String title, String content) {
        cfBdCaseInfoDao.updateShareTitleAndContent(id, title, content);
    }

    @Override
    public List<Integer> getHomePageCaseByOrgIdList(List<Integer> orgIdList, int size, String dateCreated) {
        List<CfCaseAmountInfoModel> caseAmountInfoModelList = cfBdCaseInfoDao.getHomePageCaseByOrgIdList(orgIdList, dateCreated);
        if (CollectionUtils.isEmpty(caseAmountInfoModelList)) {
            return Lists.newArrayList();
        }
        return caseAmountInfoModelList
                .stream()
                .sorted(Comparator.comparing(CfCaseAmountInfoModel::getAmount).reversed())
                .limit(size)
                .map(CfCaseAmountInfoModel::getCaseId)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getCaseNumGreaterThan500UniqueCode() {
        return cfBdCaseInfoDao.getCaseNumGreaterThan500UniqueCode();
    }

    @Override
    public int updateReportOrderHandleInfo(Integer id, Date reportOrderHandleTime, int reportOrderHandleStatus) {
        if (Optional.ofNullable(id).orElse(0) <= 0) {
            return 0;
        }
        if (Objects.isNull(reportOrderHandleTime)) {
            log.error(this.getClass().getSimpleName() + " updateReportOrderHandleInfo reportOrderHandleTime is null");
            reportOrderHandleTime = DateUtil.nowDate();
        }
        return cfBdCaseInfoDao.updateReportOrderHandleInfo(id, reportOrderHandleTime, reportOrderHandleStatus);
    }

    @Override
    public long countReportCaseInfo(String uniqueCode, Date startTime, Date endTime) {
        return cfBdCaseInfoDao.countReportCaseInfo(uniqueCode, startTime, endTime);
    }

    @Override
    public List<CfBdCaseInfoDo> listCfReportCaseInfo(String uniqueCode, Date startTime, Date endTime) {
        //解密手机号
        List<CfBdCaseInfoDo> cfBdCaseInfoDos = cfBdCaseInfoDao.listCfReportCaseInfo(uniqueCode, startTime, endTime);
        cfBdCaseInfoDos.forEach(item -> item.setRaiserPhone(shuidiCipher.decrypt(item.getRaiserPhone())));
        return cfBdCaseInfoDos;
    }

    @Override
    public void updateFundraiserShareNum(int caseId, Integer fundraiserShareNum) {
        cfBdCaseInfoDao.updateFundraiserShareNum(caseId, fundraiserShareNum);
    }

    @Override
    public void incrFundraiserShareDays(int caseId) {
        cfBdCaseInfoDao.incrFundraiserShareDays(caseId);
    }

    @Override
    public void updateDonateNum(int id, Integer donateNum) {
        cfBdCaseInfoDao.updateDonateNum(id, donateNum);
    }

    @Override
    public void updateValidCase(int id, int validCase) {
        cfBdCaseInfoDao.updateValidCase(id, validCase);
    }

    @Override
    public int batchUpdateValidCase(List<Integer> ids, int validCase) {
        return cfBdCaseInfoDao.batchUpdateValidCase(ids, validCase);
    }

    @Override
    public List<CfCrmVolunteerCaseCountSimpleModel> getBdCaseStatistics(String startTime, String endTime, String uniqueCode) {
        return getBdCaseStatisticsFromEs(startTime, endTime, uniqueCode);
    }


    //同步案例维度-爆款1000,爆款2000达到时间
    //wiki: https://wiki.shuiditech.com/pages/viewpage.action?pageId=846618728
    @Override
    public void updateReachDonateDate(int caseId) {
        CaseReachDonateDate caseReachDonateDate = cfKpiCaseBaseDataService.selectReachDonateDate(caseId);
        if (caseReachDonateDate == null) {
            return;
        }
        caseReachDonateDate.setCaseId(caseId);
        if (Objects.equals(caseReachDonateDate.getReachOneThusDate(), "2200-01-01")) {
            caseReachDonateDate.setReachOneThusDate("");
        }
        if (Objects.equals(caseReachDonateDate.getReachTwoThusDate(), "2200-01-01")) {
            caseReachDonateDate.setReachTwoThusDate("");
        }
        if (Objects.equals(caseReachDonateDate.getReachThreeThusDate(), "2200-01-01")) {
            caseReachDonateDate.setReachThreeThusDate("");
        }
        if (Objects.equals(caseReachDonateDate.getReachFourThusDate(), "2200-01-01")) {
            caseReachDonateDate.setReachFourThusDate("");
        }
        String reachOneThusDate = caseReachDonateDate.getReachOneThusDate();
        String reachTwoThusDate = caseReachDonateDate.getReachTwoThusDate();
        String reachThreeThusDate = caseReachDonateDate.getReachThreeThusDate();
        String reachFourThusDate = caseReachDonateDate.getReachFourThusDate();
        if (StringUtils.isBlank(reachOneThusDate) && StringUtils.isBlank(reachTwoThusDate) &&
                StringUtils.isBlank(reachThreeThusDate) && StringUtils.isBlank(reachFourThusDate)) {
            log.info("案例:{}还未达到爆款", caseId);
            return;
        }
        cfBdCaseInfoDao.updateReachDonateDate(caseReachDonateDate);
    }

    @Override
    public List<CfBdCaseInfoDo> getBdCaseInfoByUniqueCodeWithPhoneAndTime(List<String> uniqueCodeList, String phone, Date startTime, Date endTime) {
        if (CollectionUtils.isEmpty(uniqueCodeList) || StringUtils.isBlank(phone)) {
            return Lists.newArrayList();
        }
        String encryptPhone = oldShuidiCipher.aesEncrypt(phone);
        return cfBdCaseInfoDao.getBdCaseInfoByUniqueCodeWithPhoneAndTime(uniqueCodeList, encryptPhone, startTime, endTime);
    }

    @Override
    public int updatePartnerUniqueCodeById(Long id, String partnerUniqueCode) {
        return cfBdCaseInfoDao.updatePartnerUniqueCodeById(id, partnerUniqueCode);
    }

    @Override
    public List<String> listHospitalNameOfLatestMonth(String uniqueCode, Date monthDate, List<Long> caseIdList) {
        return cfBdCaseInfoDao.listHospitalNameOfLatestMonth(uniqueCode, monthDate, caseIdList);
    }

    @Override
    public int getCaseCountByCaseTime(String caseStartTime, String caseEndTime) {
        return cfBdCaseInfoDao.getCaseCountByCaseTime(caseStartTime, caseEndTime);
    }

    @Override
    public long countCfByPhoneVolunteer(String phone, Date queryStartTime, Date queryEndTime, List<Integer> chushenPassList, List<CrowdfundingVolunteer> queryVolunteerList) {
        if (CollectionUtils.isEmpty(queryVolunteerList)) {
            return 0;
        }
        String encryptPhone = null;
        if (StringUtils.isNotEmpty(phone)) {
            encryptPhone = ShuidiCipherUtils.encrypt(phone);
        }
        List<String> uniqueCodeList = queryVolunteerList.stream().map(CrowdfundingVolunteer::getUniqueCode).collect(Collectors.toList());
        return cfBdCaseInfoDao.countCfByPhoneVolunteer(encryptPhone, queryStartTime, queryEndTime, chushenPassList, uniqueCodeList);
    }

    @Override
    public List<CfBdCaseInfoDo> listCfByPhoneVolunteer(String phone, Date queryStartTime, Date queryEndTime, List<Integer> chushenPassList, List<CrowdfundingVolunteer> queryVolunteerList, int pageNo, int pageSize) {
        if (CollectionUtils.isEmpty(queryVolunteerList)) {
            return Lists.newArrayList();
        }
        String encryptPhone = null;
        if (StringUtils.isNotEmpty(phone)) {
            encryptPhone = ShuidiCipherUtils.encrypt(phone);
        }
        List<String> uniqueCodeList = queryVolunteerList.stream().map(CrowdfundingVolunteer::getUniqueCode).collect(Collectors.toList());
        int offset = (pageNo - 1) * pageSize;
        List<CfBdCaseInfoDo> cfBdCaseInfoDos = cfBdCaseInfoDao.listCfByPhoneVolunteer(encryptPhone, queryStartTime, queryEndTime, chushenPassList, uniqueCodeList, offset, pageSize);
        //解密手机号
        cfBdCaseInfoDos.forEach(item -> item.setRaiserPhone(shuidiCipher.decrypt(item.getRaiserPhone())));
        return cfBdCaseInfoDos;
    }

    @Override
    public List<CfBdCaseInfoDo> listCaseInfoByCaseIds(List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }
        List<CfBdCaseInfoDo> result = Lists.newArrayList();
        Lists.partition(caseIds, 500)
                .forEach(item -> result.addAll(cfBdCaseInfoDao.listCaseInfoByCaseIds(item)));
        return result;
    }


    @Override
    public void updateLocalCityTag(int caseId, int localCity, int remoteCity) {
        cfBdCaseInfoDao.updateLocalCityTag(caseId, localCity, remoteCity);
        bdCooperationCaseInfoService.updateLocalCityTag(caseId, localCity);
    }

    @Override
    public List<CfBdCaseInfoDo> listCaseInfoByCaseIdsAndAmountAndDonate(List<Integer> caseIdList, Integer validAmount, Integer donateNum) {
        if (CollectionUtils.isEmpty(caseIdList)) {
            return Lists.newArrayList();
        }
        return cfBdCaseInfoDao.listCaseInfoByCaseIdsAndAmountAndDonate(caseIdList, validAmount, donateNum);
    }

    @Override
    public int moveCaseData(long originOrgId, long moveOrgId, String orgPath) {
        return cfBdCaseInfoDao.moveCaseData(originOrgId, moveOrgId, orgPath);
    }

    @Override
    public OpResult<CrmCrowdfundingModel> statByOrg(long orgId) {
        CrmCrowdfundingModel countModel;
        if (useCaseEs) {
            countModel = statByOrgUseEs(orgId);
        } else {
            countModel = cfBdCaseInfoDao.statByOrg(orgId);
        }
        return OpResult.createSucResult(countModel);
    }

    @Override
    public boolean blackVerify(BlacklistLunchCaseDto blacklistLunchCaseDto) {
        BlacklistCallPhaseEnum phaseEnum = BlacklistCallPhaseEnum.LAUNCH_CASE;
        blacklistLunchCaseDto.setBlacklistCallPhase(phaseEnum.getCode());
        blacklistLunchCaseDto.setCaseInitiateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        Response<Boolean> response = blacklistVerifyClient.launchCase(blacklistLunchCaseDto);
        //false  表示没有拦截
        return Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(false);
    }

    @Override
    public OpResult<HighRiskTipModel> getRiskTip(ClewPreposeMaterialSaveOrUpdateModel.ClewPreposeMaterialModel clewPreposeMaterialModel) {
        HighRiskJudgeInfoPrePoseModel riskJudgeInfoPrePoseModel = buildRiskJudgeInfo(clewPreposeMaterialModel);
        Response<HighRiskJudgeResult> highRiskJudgeResult = preposeMaterialRiskClient.judgeHighRiskV2PrePose(riskJudgeInfoPrePoseModel);
        log.info(this.getClass().getSimpleName() + "getHighRiskTipResult:{}", highRiskJudgeResult);
        if (highRiskJudgeResult.notOk()) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_ERROR);
        }
        HighRiskJudgeResult highRiskData = highRiskJudgeResult.getData();
        HighRiskTipModel highRiskTipModel = new HighRiskTipModel();
        highRiskTipModel.setIsHaveRisk(highRiskData.isHighRisk());
        String totalRiskTip = highRiskData.getTotalRiskTip();
        highRiskTipModel.setRiskTipContent(totalRiskTip);
        if (highRiskData.isHighRisk()) {
            Set<String> hitCodes = highRiskData.getHitCodes();
            Set<String> contentSet = Sets.newHashSet();
            List<HighRiskTipModel.riskTipDetail> riskTipDetailList = Lists.newArrayList();
            for (String hitCode : hitCodes) {
                HighRiskTipEnums.HighRiskTipEnum riskTipEnum = HighRiskTipEnums.HighRiskTipEnum.parse(hitCode);
                if (riskTipEnum != null) {
                    contentSet.add(riskTipEnum.getDesc());
                }
                List<HighRiskTipModel.riskTipDetail> riskTipDetailResult = HighRiskTipEnums.HighRiskTipDetailEnum.parse(hitCode);
                riskTipDetailList.addAll(riskTipDetailResult);
            }
            String content = StringUtils.join(contentSet.toArray(), "、");
            // 如果未命中code码返回空字符串
            if (StringUtils.isEmpty(content)) {
                highRiskTipModel.setRiskTipDetail(riskTipDetailList.stream().distinct().collect(Collectors.toList()));
            } else {
                if (StringUtils.isBlank(totalRiskTip)) {
                    if (atNight()) {
                        highRiskTipModel.setRiskTipContent(content + "信息需做进一步核实，审核人员将于早8:30后优先进行审核，请告知筹款人注意接听电话");
                    } else {
                        highRiskTipModel.setRiskTipContent(content + "信息在审核时将会跟筹款人进一步核实，请告知筹款人注意接听电话");
                    }
                }
                highRiskTipModel.setRiskTipDetail(riskTipDetailList.stream().distinct().collect(Collectors.toList()));
            }
        } else {
            if (StringUtils.isBlank(totalRiskTip) && CollectionUtils.isNotEmpty(highRiskData.getRiskTips())) {
                highRiskTipModel.setRiskTipContent(highRiskData.getRiskTips().get(0));
            }
        }

        return OpResult.createSucResult(highRiskTipModel);
    }

    @Override
    public List<CfBdCaseInfoDo> listByValidCase(String startTime, String endTime) {
        return cfBdCaseInfoDao.listByValidCase(startTime, endTime);
    }

    @Override
    public int updateHospitalDepartmentInfo(int caseId, String vhospitalCode, int departmentId, String hospitalName, String departmentName) {
        if (caseId <= 0) {
            return 0;
        }
        return cfBdCaseInfoDao.updateHospitalDepartmentInfo(caseId, vhospitalCode, departmentId, hospitalName, departmentName);
    }

    @Override
    public List<CfBdCaseInfoDo> listByUniqueCodeDateCreated(String uniqueCode, String startTime, String endTime) {
        if (StringUtils.isBlank(uniqueCode) || StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return Lists.newArrayList();
        }
        return cfBdCaseInfoDao.listByUniqueCodeDateCreated(uniqueCode, startTime, endTime);
    }

    @Override
    public int updateHasOfflineBreakWaveTask(String infoUuid, Integer hasOfflineBreakWaveTask) {
        if (StringUtils.isBlank(infoUuid) || hasOfflineBreakWaveTask == null) {
            log.warn("updateHasOfflineBreakWaveTask参数不合法, infoUuid:{}, hasOfflineBreakWaveTask:{}", infoUuid, hasOfflineBreakWaveTask);
            return 0;
        }
        return cfBdCaseInfoDao.updateHasOfflineBreakWaveTask(infoUuid, hasOfflineBreakWaveTask);
    }

    private boolean atNight() {
        LocalTime now = LocalTime.now();
        LocalTime begin = LocalTime.of(23, 50);
        LocalTime end = LocalTime.of(8, 0);
        return now.isAfter(begin) || now.isBefore(end) || now.equals(begin) || now.equals(end);
    }

    private HighRiskJudgeInfoPrePoseModel buildRiskJudgeInfo(ClewPreposeMaterialSaveOrUpdateModel.ClewPreposeMaterialModel clewPreposeMaterialModel) {
        HighRiskJudgeInfoPrePoseModel highRiskJudgeInfoPrePoseModel = new HighRiskJudgeInfoPrePoseModel();
        String uniqueCode = clewPreposeMaterialModel.getVolunteerUniqueCode();
        // 如果是身份证发起设置城市code
        if (clewPreposeMaterialModel.getPatientIdCardType() != null && clewPreposeMaterialModel.getPatientIdCardType() == 1) {
            String patientIdCard = clewPreposeMaterialModel.getPatientIdCard();
            String patientCityCode = getCityCodeByIdCard(patientIdCard);
            highRiskJudgeInfoPrePoseModel.setPatientCityCode(patientCityCode);
        }
        CrowdfundingCity crowdfundingCity = getVolunteerCityByUniqueCode(uniqueCode);
        if (Objects.nonNull(crowdfundingCity)) {
            String cityCode = crowdfundingCity.getCode();
            highRiskJudgeInfoPrePoseModel.setBdCityCode(cityCode);
        }
        highRiskJudgeInfoPrePoseModel.setContent(Objects.requireNonNullElse(clewPreposeMaterialModel.getContent(), ""));
        highRiskJudgeInfoPrePoseModel.setWorkOrderId(Objects.requireNonNullElse(clewPreposeMaterialModel.getId(), 0L));
        highRiskJudgeInfoPrePoseModel.setSource(HighRiskJudgeConst.Source.PREPOSE);
//        if (clewPreposeMaterialModel.isUseNetWorth()) {
            Integer houseNetAssetsInYuan = Optional.ofNullable(clewPreposeMaterialModel.getHouseNetWorthValue()).orElse(0) + Optional.ofNullable(clewPreposeMaterialModel.getSelfHouseValue()).orElse(0);
            highRiskJudgeInfoPrePoseModel.setHouseNetAssetsInYuan(houseNetAssetsInYuan);
//        } else {
//            Integer houseAmountInYuan = Optional.ofNullable(clewPreposeMaterialModel.getHouseValue()).orElse(0) + Optional.ofNullable(clewPreposeMaterialModel.getSelfHouseValue()).orElse(0);
//            highRiskJudgeInfoPrePoseModel.setHouseAmountInYuan(houseAmountInYuan);
//        }
        Integer houseCount = Optional.ofNullable(clewPreposeMaterialModel.getHouseNum()).orElse(0) + Optional.ofNullable(clewPreposeMaterialModel.getSelfHouseNum()).orElse(0);
        highRiskJudgeInfoPrePoseModel.setHouseCount(houseCount);
        Integer houseSellCount = Optional.ofNullable(clewPreposeMaterialModel.getHouseSellingCount()).orElse(0) + Optional.ofNullable(clewPreposeMaterialModel.getSelfHouseSellingCount()).orElse(0);
        highRiskJudgeInfoPrePoseModel.setHouseSellCount(houseSellCount);
        Integer houseSellAmountInYuan = Optional.ofNullable(clewPreposeMaterialModel.getHouseSellingAmount()).orElse(0) + Optional.ofNullable(clewPreposeMaterialModel.getSelfHouseSellingAmount()).orElse(0);
        highRiskJudgeInfoPrePoseModel.setHouseSellAmountInYuan(houseSellAmountInYuan);
        highRiskJudgeInfoPrePoseModel.setCarAmountInYuan(Objects.requireNonNullElse(clewPreposeMaterialModel.getCarValue(), 0));
        highRiskJudgeInfoPrePoseModel.setCarSellCount(Objects.requireNonNullElse(clewPreposeMaterialModel.getCarSellingCount(), 0));
        highRiskJudgeInfoPrePoseModel.setCarSellAmountInYuan(Objects.requireNonNullElse(clewPreposeMaterialModel.getCarSellingAmount(), 0));
        highRiskJudgeInfoPrePoseModel.setCarCount(Objects.requireNonNullElse(clewPreposeMaterialModel.getCarNum(), 0));
        highRiskJudgeInfoPrePoseModel.setYearIncome(Objects.requireNonNullElse(clewPreposeMaterialModel.getHomeIncome(), 0));
        highRiskJudgeInfoPrePoseModel.setMonetaryAssertInYuan(Objects.requireNonNullElse(clewPreposeMaterialModel.getFinancialAssetsAmount(), 0));
        highRiskJudgeInfoPrePoseModel.setDebtInYuan(Objects.requireNonNullElse(clewPreposeMaterialModel.getHomeOwningAmount(), 0));
        highRiskJudgeInfoPrePoseModel.setRemainAmountInYuan(Objects.requireNonNullElse(clewPreposeMaterialModel.getRemainAmount(), 0));
        highRiskJudgeInfoPrePoseModel.setPersonalInsurance(Optional.ofNullable(clewPreposeMaterialModel.getHasPersonalInsurance()).orElse(0) == 1);
        highRiskJudgeInfoPrePoseModel.setTargetAmountInYuan(Objects.requireNonNullElse(clewPreposeMaterialModel.getTargetAmount(), 0));
        highRiskJudgeInfoPrePoseModel.setPatientIdentity(Objects.requireNonNullElse(clewPreposeMaterialModel.getPatientIdentity(), 0));
        highRiskJudgeInfoPrePoseModel.setNormalCase(Optional.ofNullable(clewPreposeMaterialModel.getAccidentType()).orElse(0) == PreposeMaterialModel.AccidentType.DEFAULT.getCode());
        highRiskJudgeInfoPrePoseModel.setCaseId(0);
        highRiskJudgeInfoPrePoseModel.setAuthenticityIndicator(clewPreposeMaterialModel.getAuthenticityIndicator());
        highRiskJudgeInfoPrePoseModel.setPaidAmount(Optional.ofNullable(clewPreposeMaterialModel.getPaidAmount()).orElse(0));
        highRiskJudgeInfoPrePoseModel.setHouseOtherCount(clewPreposeMaterialModel.getHouseNum());
        highRiskJudgeInfoPrePoseModel.setPatientMaritalStatus(clewPreposeMaterialModel.getPatientMaritalStatus());
        highRiskJudgeInfoPrePoseModel.setMarriedChildrenCount(clewPreposeMaterialModel.getMarriedChildrenCount());
        highRiskJudgeInfoPrePoseModel.setMarriedChildrenStatus(clewPreposeMaterialModel.getMarriedChildrenStatus());
        highRiskJudgeInfoPrePoseModel.setPatientParentStatus(clewPreposeMaterialModel.getPatientParentStatus());
        return highRiskJudgeInfoPrePoseModel;
    }

    // 获取线下顾问所属城市code
    private CrowdfundingCity getVolunteerCityByUniqueCode(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return null;
        }
        CrowdfundingVolunteer volunteer = volunteerService.getByUniqueCode(uniqueCode);
        if (volunteer == null) {
            return null;
        }
        int cityId = volunteer.getCityId();
        if (cityId <= 0) {
            log.info("no city");
            return null;
        }
        // 获取该cityId对应的市级
        List<CrowdfundingCity> crowdfundingCityList = cfCrowdfundingCityDelegate.getListByIds(Lists.newArrayList(cityId));

        if (CollectionUtils.isEmpty(crowdfundingCityList)) {
            log.info("crowdfundingCityList null");
            return null;
        }
        return crowdfundingCityList.get(0);
    }

    private String getCityCodeByIdCard(String idCard) {
        try {
            if (StringUtils.isEmpty(idCard)) {
                return "";
            }
            return StringUtils.substring(idCard, 0, 6);
        } catch (Exception e) {
            log.error("getCityCodeByIdCard source {}", idCard, e);
        }
        return "";
    }


    /**
     * - 有顾问修改了案例的医院科室信息，在【小鲸鱼】消息中心增加消息通知业务经理
     * - 通知人；该顾问的业务经理
     * - 通知类型【案例医院科室变更通知】
     * - 列表页文案描述："新增一条案例医院科室更新"
     * - 点击进入详情：
     * - 显示【案例医院科室变更通知】历史通知给此人的所有消息
     * - 最近一条通知在最上方
     * - 字段「案例ID」「案例发起顾问」「修改医院科室时间」
     * - 点击'去跟进'，跳转该案例列表页。
     */
    private void caseHospitalDepartmentCheck(CfBdCaseInfoDo cfBdCaseInfoDo) {
        BdCaseTagDO bdCaseTagDO = bdCaseTagService.queryByCaseId(cfBdCaseInfoDo.getCaseId());
        if (bdCaseTagDO == null || bdCaseTagDO.getHospitalDepartmentCheck() == BdCaseTagDO.HospitalDepartmentCheckEnum.NOT_CHECK.getValue()) {
            return;
        }

        CfBdCaseInfoDo bdCaseInfoDo = cfBdCaseInfoDao.getBdCaseInfoByInfoId(cfBdCaseInfoDo.getCaseId());
        //医院科室未修改 不做处理
        if (bdCaseInfoDo == null || (cfBdCaseInfoDo.getHospitalName().equals(bdCaseInfoDo.getHospitalName()) && cfBdCaseInfoDo.getDepartmentName().equals(bdCaseInfoDo.getDepartmentName()))) {
            return;
        }
        CfInfoExt cfInfoExt = crowdFundingFeignDelegateImpl.getCfInfoExtById(cfBdCaseInfoDo.getCaseId());
        if (cfInfoExt == null) {
            return;
        }


        CrowdfundingVolunteer crowdfundingVolunteer = volunteerService.getByUniqueCode(cfInfoExt.getVolunteerUniqueCode());
        if (crowdfundingVolunteer == null) {
            return;
        }

        String title = "【案例医院科室变更通知】新增一条案例医院科室更新";
        String url = "https://www.shuidichou.com/bd/case/detail?infoUuid=" + cfInfoExt.getInfoUuid() + "&showCheck=true";
        WorkWeixinContentBuilder cb = WorkWeixinContentBuilder.create()
                .subject(title)
                .payload("案例ID", cfInfoExt.getInfoUuid())
                .payload("案例发起顾问", crowdfundingVolunteer.getVolunteerName())
                .payload("修改医院科室时间", DateUtil.getCurrentDateTimeStr())
                .payload("去跟进", "<a href=\"" + url + "\">" + "立即查看" + "</a>");
        String content = cb.build();
        if (crowdfundingVolunteer.getLevel() != CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel()) {
            //查找上级
            CrowdfundingVolunteer leaderVolunteer = memberInfoService.listApplyLeaderWithDefaultExplicit(crowdfundingVolunteer.getUniqueCode(),
                    GrowthtoolWeaponUtil.getContinueApproveRoleEnum(crowdfundingVolunteer.getLevel()));
            if (leaderVolunteer == null || leaderVolunteer.getLevel() != CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel()) {
                return;
            }
            crowdfundingVolunteer = leaderVolunteer;
        }
        appPushCrmService.pushCrmMsg(crowdfundingVolunteer.getMobile(), title, "", content);
        cfBdCrmMsgService.saveCfBdCrmMsg(title, "", content, crowdfundingVolunteer.getMis(), crowdfundingVolunteer.getUniqueCode());
    }

    @Override
    public boolean isCreatedWithinSixDays(CfBdCaseInfoDo cfBdCaseInfoDo) {
        if (cfBdCaseInfoDo == null || cfBdCaseInfoDo.getDateCreated() == null) {
            return false;
        }
        
        try {
            // 获取案例创建时间，转换为LocalDate
            Timestamp dateCreated = cfBdCaseInfoDo.getDateCreated();
            LocalDate createdDate = dateCreated.toLocalDateTime().toLocalDate();
            
            // 获取当前时间的LocalDate
            LocalDate currentDate = LocalDate.now();
            
            // 使用ChronoUnit计算日期差（天）
            long daysDiff = ChronoUnit.DAYS.between(createdDate, currentDate);
            
            // 判断日期差是否在6天内（小于等于6天）
            return daysDiff <= 5;
        } catch (Exception e) {
            log.error("计算案例创建时间是否在6天内时出错, caseId: {}, error: {}", 
                    cfBdCaseInfoDo.getCaseId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Integer countTaskCaseCount(String uniqueCode, Integer hasOfflineBreakWaveTask, Date limitTime) {
        if(StringUtils.isBlank(uniqueCode) || hasOfflineBreakWaveTask == null || limitTime == null) {
            return 0;
        }
        return cfBdCaseInfoDao.countTaskCaseCount(uniqueCode,hasOfflineBreakWaveTask,limitTime);
    }

}
