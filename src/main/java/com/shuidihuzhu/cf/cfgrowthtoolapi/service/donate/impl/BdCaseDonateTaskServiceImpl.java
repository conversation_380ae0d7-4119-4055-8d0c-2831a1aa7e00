package com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.BdCaseDonateTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.CfDonateEndCaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.CfDonateUnEndCaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.CrmDonateTaskModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.DonateTaskBaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.donate.BdCaseDonateTaskParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate.IBdCaseDonateTaskService;
import com.shuidihuzhu.cf.dao.donate.BdCaseDonateTaskDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 捐转案例分层-任务生成配置(BdCaseDonateTask)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-27 17:59:22
 */
@Service("bdCaseDonateTaskService")
public class BdCaseDonateTaskServiceImpl implements IBdCaseDonateTaskService {

    @Resource
    private BdCaseDonateTaskDao bdCaseDonateTaskDao;


    @Override
    public int insert(BdCaseDonateTaskDO bdCaseDonateTask) {
        return bdCaseDonateTaskDao.insert(bdCaseDonateTask);
    }


    @Override
    public boolean deleteById(long id) {
        return bdCaseDonateTaskDao.deleteById(id) > 0;
    }

    @Override
    public void addShareCount(long id) {
        bdCaseDonateTaskDao.addShareCount(id);
    }

    @Override
    public void updateHandleStatus(List<Long> ids, int taskStatus, int handleStatus) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        bdCaseDonateTaskDao.updateHandleStatus(ids, taskStatus, handleStatus);
    }

    @Override
    public void updateHandleStatusWithReason(List<Long> ids, int taskStatus, int handleStatus, String failReason) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        bdCaseDonateTaskDao.updateHandleStatusWithReason(ids, taskStatus, handleStatus, failReason);
    }

    @Override
    public BdCaseDonateTaskDO queryById(long id) {
        return bdCaseDonateTaskDao.queryById(id);
    }

    @Override
    public List<BdCaseDonateTaskDO> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return bdCaseDonateTaskDao.listByIds(ids);
    }

    @Override
    public BdCaseDonateTaskDO getByCaseIdAndDayKey(int caseId, String dayKey) {
        return bdCaseDonateTaskDao.getByCaseIdAndDayKey(caseId, dayKey);
    }

    @Override
    public List<BdCaseDonateTaskDO> listByDayKey(String dayKey) {
        return bdCaseDonateTaskDao.listByDayKey(dayKey);
    }

    @Override
    public List<BdCaseDonateTaskDO> listByCaseId(int caseId) {
        return bdCaseDonateTaskDao.listByCaseId(caseId);
    }

    @Override
    public CrmDonateTaskModel getTaskModelByOrgIds(List<String> dateTimes, List<Long> orgIdList) {
        if (CollectionUtils.isEmpty(dateTimes) || CollectionUtils.isEmpty(orgIdList)) {
            return new CrmDonateTaskModel();
        }
        return Optional.ofNullable(bdCaseDonateTaskDao.getTaskModelByOrgIds(dateTimes, orgIdList)).orElse(new CrmDonateTaskModel());
    }

    @Override
    public DonateTaskBaseModel getTaskModelByUniqueCode(List<String> dateTimes, String uniqueCode) {
        if (CollectionUtils.isEmpty(dateTimes) || StringUtils.isBlank(uniqueCode)) {
            return null;
        }
        return bdCaseDonateTaskDao.getTaskModelByUniqueCode(dateTimes, uniqueCode);
    }

    @Override
    public List<CrmDonateTaskModel> groupByUniqueCode(List<String> dateTimes, long orgId, BdCaseDonateTaskParam bdCaseDonateTaskParam) {
        if (CollectionUtils.isEmpty(dateTimes) || orgId <= 0) {
            return Lists.newArrayList();
        }
        return bdCaseDonateTaskDao.groupByUniqueCode(dateTimes, orgId);
    }

    @Override
    public int pageCountTask(List<String> dateTimes, BdCaseDonateTaskParam bdCaseDonateTaskParam) {
        if (CollectionUtils.isEmpty(dateTimes) || StringUtils.isBlank(bdCaseDonateTaskParam.getUniqueCode())) {
            return 0;
        }
        return bdCaseDonateTaskDao.pageCountTask(dateTimes, bdCaseDonateTaskParam);
    }

    @Override
    public List<BdCaseDonateTaskDO> pageListTask(List<String> dateTimes, BdCaseDonateTaskParam bdCaseDonateTaskParam) {
        if (CollectionUtils.isEmpty(dateTimes) || StringUtils.isBlank(bdCaseDonateTaskParam.getUniqueCode())) {
            return Lists.newArrayList();
        }
        return bdCaseDonateTaskDao.pageListTask(dateTimes, bdCaseDonateTaskParam);
    }

    @Override
    public List<BdCaseDonateTaskDO> listByCityConfigId(long cityConfigId, int taskStatus) {
        return bdCaseDonateTaskDao.listByCityConfigId(cityConfigId, taskStatus);
    }

    @Override
    public List<BdCaseDonateTaskDO> getFinishTaskByCaseId(List<Integer> caseIds) {
        return bdCaseDonateTaskDao.getFinishTaskByCaseId(caseIds);
    }

    @Override
    public CfDonateUnEndCaseModel getDonateUnEndCaseByOrgIds(List<String> dateTimes, List<Long> orgIdList) {
        return Optional.ofNullable(bdCaseDonateTaskDao.getDonateUnEndCaseByOrgIds(dateTimes, orgIdList)).orElse(new CfDonateUnEndCaseModel());
    }

    @Override
    public CfDonateEndCaseModel getDonateEndCaseByOrgIds(List<String> dateTimes, List<Long> orgIdList) {
        return Optional.ofNullable(bdCaseDonateTaskDao.getDonateEndCaseByOrgIds(dateTimes, orgIdList)).orElse(new CfDonateEndCaseModel());
    }

    @Override
    public CfDonateUnEndCaseModel getDonateUnEndCaseByUniqueCodes(List<String> dateTimes, String uniqueCode) {
        if (CollectionUtils.isEmpty(dateTimes) || StringUtils.isBlank(uniqueCode)) {
            return new CfDonateUnEndCaseModel();
        }
        return Optional.ofNullable(bdCaseDonateTaskDao.getDonateUnEndCaseByUniqueCode(dateTimes, uniqueCode)).orElse(new CfDonateUnEndCaseModel());
    }

    @Override
    public CfDonateEndCaseModel getDonateEndCaseByUniqueCodes(List<String> dateTimes, String uniqueCode) {
        if (CollectionUtils.isEmpty(dateTimes) || StringUtils.isBlank(uniqueCode)) {
            return new CfDonateEndCaseModel();
        }
        return Optional.ofNullable(bdCaseDonateTaskDao.getDonateEndCaseByUniqueCode(dateTimes, uniqueCode)).orElse(new CfDonateEndCaseModel());
    }


}
