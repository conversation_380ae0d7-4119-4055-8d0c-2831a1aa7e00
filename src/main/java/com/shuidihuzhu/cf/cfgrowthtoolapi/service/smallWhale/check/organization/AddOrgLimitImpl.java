package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.organization;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.OrgOptParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-05-09 17:23
 **/
@Service
@RefreshScope
@Slf4j
public class AddOrgLimitImpl extends AbstractOptOrgLimit {


    @Override
    public Response<Boolean> doCanOptCheck(OrgOptParam orgOptParam) {
        log.info("开始新增组织校验");
        //检查名称
        Response<Boolean> checkHasSameName = checkHasSameName(orgOptParam);
        if (checkHasSameName.notOk()) {
            return checkHasSameName;
        }
        //添加到的节点检查
        Response<Boolean> basicAdd = basicAdd(orgOptParam);
        if (basicAdd.notOk()) {
            return basicAdd;
        }
        return NewResponseUtil.makeSuccess(true);
    }



    @Override
    public List<OrganizationMemberOptEnum> getNeedCheckOptEnums() {
        return Lists.newArrayList(OrganizationMemberOptEnum.add_single_node);
    }
}
