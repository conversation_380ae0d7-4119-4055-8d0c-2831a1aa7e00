package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IRiskControlDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.caserefund.CfCaseRefundTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.caserefund.CfWaitDealTypeRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerRecruitInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mapper.BdCrmOrganizationDoMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.LovePartnerInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.bill.BillBaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdLovePartnerParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.LovePartnerInfoParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.QRCodeService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund.CfCaseRefundTaskService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund.CfWaitDealTypeRecordService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerRecruitInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill.CfBdPartnerBillService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice.workwx.WorkWeixinContentBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GenerateRandomStrUtil;
import com.shuidihuzhu.cf.controller.feign.QRCodeFeignController;
import com.shuidihuzhu.cf.dao.lovepartner.*;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.chaifenbeta.wxgrpcdelegate.WeiXinFeginClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant.base64Logo;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */

@Service
@Slf4j
public class CfPartnerInfoServiceImpl implements CfPartnerInfoService {

    @Autowired
    private CfPartnerInfoDao cfPartnerInfoDao;

    @Autowired
    private PartnerAttendInfoDao partnerAttendInfoDao;

    @Autowired
    private PartnerApproveDao partnerApproveDao;

    @Autowired
    private IRiskControlDelegate riskControlDelegate;

    @Autowired
    private WeiXinFeginClient weiXinFeginClient;

    @Autowired
    private QRCodeService qrCodeService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgServiceImpl;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private ICrmOrganizationRelationService organizationRelationService;

    @Autowired
    private CfPartnerRecruitInfoService partnerRecruitInfoService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private CfBdPartnerBillService cfPartnerBillService;

    @Autowired
    private CfWaitDealTypeRecordService cfWaitDealTypeRecordService;

    @Autowired
    private CfCaseRefundTaskService cfCaseRefundTaskService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrganizationService;

    @Autowired
    private ICrmDealOrganizationInfoService crmDealOrganizationInfoService;

    @Autowired
    private BdCrmOrganizationDoMapper bdCrmOrganizationDoMapper;


    @Override
    public CfPartnerInfoDo getPartnerInfoByUniqueCode(String uniqueCode) {
        return cfPartnerInfoDao.getPartnerInfoByUniqueCode(uniqueCode);
    }

    @Override
    public PartnerCountModel searchPartnerCount(List<BdCrmOrganizationDO> bdOrgModelList, CrowdfundingVolunteer cfVolunteer) {
        String userId = cfVolunteer.getUniqueCode();
        List<Long> orgIds = bdOrgModelList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
        int teamPartnerCount = 0;
        for (Long orgId:orgIds){
          teamPartnerCount += findPartnerCount(orgId, WorkStatusEnum.ON_OFFICE.getCode());
        }
        //展示审核通过人数
        int myPartnerCount = cfPartnerInfoDao.getMyPartenrCountByWorkStatus(userId, WorkStatusEnum.ON_OFFICE.getCode());
        //展示待审批人数
        int applyPartnerCount = cfPartnerInfoDao.getPartnerCount(userId, ApproveStatusEnum.UN_DOING.getCode());
        //申请的伙伴出勤信息数
        int applyPartnerAttendCount = partnerAttendInfoDao.getApplyPartnerAttendCount(userId, ApproveStatusEnum.UN_DOING.getCode());
        //审批的伙伴信息边变更数
        int approvePartnerCount = partnerApproveDao.getApprovePartnerCount(userId, ApproveStatusEnum.UN_DOING.getCode(), 1);
        //审批伙伴出勤信息数
        int approvePartnerAttendCount = partnerApproveDao.getApprovePartnerCount(userId, ApproveStatusEnum.UN_DOING.getCode(), 2);
        //审批招募伙伴数
        int recruitPartnerCount = partnerRecruitInfoService.getRecruitPartnerCount(userId, CfStatusEnums.PartnerRecruitApproveStatusEnum.NEED_HANDLE);

        //申请的伙伴服务奖励
        int applyBillCount = 0;
        List<BillBaseModel> bill = cfPartnerBillService.listMyPartnerSumBill(cfVolunteer);
        if (CollectionUtils.isNotEmpty(bill) && bill.get(0).getApplyStatus() < PartnerEnums.BillApproveEnums.approve.getCode()) {
            applyBillCount = 1;
        }

        //审批的伙伴服务奖励
        int approveBillCount = 0;
        List<BillBaseModel> billBaseModels = cfPartnerBillService.listMyOrgSumBill(bdOrgModelList, cfVolunteer);
        if (CollectionUtils.isNotEmpty(billBaseModels)){
            approveBillCount = (int)billBaseModels.stream().filter(b -> b.getApplyStatus() < PartnerEnums.BillApproveEnums.approve.getCode()).count();
        }

        PartnerCountModel partnerCountModel = new PartnerCountModel();
        partnerCountModel.setTeamPartnerCount(teamPartnerCount);
        partnerCountModel.setMyPartnerCount(myPartnerCount);
        partnerCountModel.setApplyPartnerCount(applyPartnerCount);
        partnerCountModel.setApplyPartnerAttendCount(applyPartnerAttendCount);
        partnerCountModel.setApplyPartnerAwardCount(applyBillCount);
        partnerCountModel.setApprovePartnerCount(approvePartnerCount);
        partnerCountModel.setApprovePartnerAttendCount(approvePartnerAttendCount);
        partnerCountModel.setRecruitPartnerCount(recruitPartnerCount);
        partnerCountModel.setApprovePartnerAwardCount(approveBillCount);
        return partnerCountModel;
    }


    @Override
    public OpResult<Integer> insertPartnerInfo(LovePartnerInfoParam lovePartnerInfoParam, CrowdfundingVolunteer cfVolunteer, long orgId) {
        //兼职信息：lovePartnerInfoParam
        //顾问信息：cfVolunteer
        //审批人/经理信息：approveVolunteer
        if (!apolloService.isCreateLovePartner()){
           return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
        }
        //新增的，生成兼职人员唯一标识
        String uniqueCode = GenerateRandomStrUtil.generateRandomStr();
        String orgChainName = crmSelfBuiltOrgReadService.listParentOrgAsChain(orgId).stream().map(item -> String.valueOf(item.getOrgName())).collect(Collectors.joining("-"));
        //进行身份校验
        OpResult<Integer> checkResult = identityCheck(lovePartnerInfoParam,true);
        if (checkResult.getErrorCode() != CfErrorCode.SUCCESS){
            return checkResult;
        }
        //将信息存入人员信息表中
        lovePartnerInfoParam.setUniqueCode(uniqueCode);
        lovePartnerInfoParam.setLeaderName(cfVolunteer.getVolunteerName());
        lovePartnerInfoParam.setLeaderUniqueCode(cfVolunteer.getUniqueCode());
        lovePartnerInfoParam.setOrgId(orgId);
        lovePartnerInfoParam.setEntryTime(new Date());
        lovePartnerInfoParam.setApproveStatus(ApproveStatusEnum.UN_DOING.getCode());
        //待入职状态
        lovePartnerInfoParam.setWorkStatus(WorkStatusEnum.TO_BE_HIRED.getCode());
        lovePartnerInfoParam.setOrgChainName(orgChainName);
        CrowdfundingVolunteer approveVolunteer = memberInfoService.listApplyLeaderWithDefaultExplicit(cfVolunteer.getUniqueCode(), null);
        this.setApproveInfo(approveVolunteer,lovePartnerInfoParam);
        if (Objects.equals(cfVolunteer.getLevel(),CrowdfundingVolunteerEnum.RoleEnum.OPERATOR.getLevel())) {
            //将信息存入人员信息表中
            lovePartnerInfoParam.setApproveStatus(ApproveStatusEnum.APPROVE.getCode());
            //修改为在职
            lovePartnerInfoParam.setWorkStatus(WorkStatusEnum.ON_OFFICE.getCode());
            cfPartnerInfoDao.insertPartnerInfo(lovePartnerInfoParam);
            //更新招募兼职信息
            partnerRecruitInfoService.updateFromSubmit(lovePartnerInfoParam, cfVolunteer);
            return OpResult.createSucResult(1);
        }
        cfPartnerInfoDao.insertPartnerInfo(lovePartnerInfoParam);
        //审批信息入库
        this.insertPartnerApprove(lovePartnerInfoParam, ApproveStatusEnum.UN_DOING.getCode(), approveVolunteer);
        this.sendApprovePartnerInfoMsg(lovePartnerInfoParam,approveVolunteer);
        //更新招募兼职信息
        partnerRecruitInfoService.updateFromSubmit(lovePartnerInfoParam,cfVolunteer);
        return OpResult.createSucResult(1);
    }

    @Override
    public OpResult<Integer> updatePartnerInfo(LovePartnerInfoParam lovePartnerInfoParam, CrowdfundingVolunteer cfVolunteer, long orgId){
        String uniqueCode = lovePartnerInfoParam.getUniqueCode();
        if (!apolloService.isCreateLovePartner()){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
        }
        //进行唯一兼职校验
        CfPartnerInfoDo partnerInfoDo = cfPartnerInfoDao.getPartnerInfoByUniqueCode(uniqueCode);
        if (Objects.isNull(partnerInfoDo)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ILLEGALLY);
        }
        //已离职
        if (Objects.equals(partnerInfoDo.getWorkStatus(),WorkStatusEnum.LEAVE_OFFICE.getCode())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.PARTNER_LEAVE_OFFICE);
        }
        //正在审核中
        if (Objects.equals(partnerInfoDo.getApproveStatus(),ApproveStatusEnum.UN_DOING.getCode())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.PARTNER_VERIFING);
        }
        //非直属上级操作
        if (!Objects.equals(cfVolunteer.getUniqueCode(),partnerInfoDo.getLeaderUniqueCode())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ILLEGALLY);
        }
        String idCardOld = shuidiCipher.decrypt(partnerInfoDo.getEncryptIdCard());
        //进行身份校验
        OpResult<Integer> checkResult = identityCheck(lovePartnerInfoParam,!idCardOld.equals(lovePartnerInfoParam.getIdCard()));
        if (checkResult.getErrorCode() != CfErrorCode.SUCCESS){
            return checkResult;
        }
        String orgChainName = crmSelfBuiltOrgReadService.listParentOrgAsChain(orgId).stream().map(item -> String.valueOf(item.getOrgName())).collect(Collectors.joining("-"));
        CrowdfundingVolunteer approveVolunteer = memberInfoService.listApplyLeaderWithDefaultExplicit(cfVolunteer.getUniqueCode(), null);
        lovePartnerInfoParam.setApproveStatus(ApproveStatusEnum.UN_DOING.getCode());
        lovePartnerInfoParam.setOrgId(orgId);
        lovePartnerInfoParam.setOrgChainName(orgChainName);
        lovePartnerInfoParam.setId(partnerInfoDo.getId());
        lovePartnerInfoParam.setLeaderUniqueCode(partnerInfoDo.getLeaderUniqueCode());
        lovePartnerInfoParam.setLeaderName(partnerInfoDo.getLeaderName());
        this.setApproveInfo(approveVolunteer,lovePartnerInfoParam);
        if (Objects.equals(cfVolunteer.getLevel(),CrowdfundingVolunteerEnum.RoleEnum.OPERATOR.getLevel())) {
            //将信息存入人员信息表中
            lovePartnerInfoParam.setApproveStatus(ApproveStatusEnum.APPROVE.getCode());
            cfPartnerInfoDao.updatePartnerInfo(lovePartnerInfoParam, uniqueCode);
            return OpResult.createSucResult(1);
        }
        //生成一条审批记录,审批信息入库
        this.insertPartnerApprove(lovePartnerInfoParam,ApproveStatusEnum.UN_DOING.getCode(),approveVolunteer);
        cfPartnerInfoDao.updateApproveStatus(partnerInfoDo.getId(), ApproveStatusEnum.UN_DOING.getCode(),null);
        //发送审批消息
        this.sendApprovePartnerInfoMsg(lovePartnerInfoParam,approveVolunteer);
        return OpResult.createSucResult(1);
    }

    @Override
    public int updateApproveOrPartner(int approveState,String remark,long approveId) {
        PartnerApproveDTO approveDTO = partnerApproveDao.getInfoById(approveId);
        CfPartnerInfoDo partnerDo = cfPartnerInfoDao.getPartnerInfoByPartnerId(approveDTO.getBizId());
        if (approveState == 2) {
            //更新审批表+人员表 审批状态为驳回
            partnerApproveDao.updateApproveStatus(approveId, remark, ApproveStatusEnum.REJECT.getCode());
            cfPartnerInfoDao.updateApproveStatus(approveDTO.getBizId(),ApproveStatusEnum.REJECT.getCode(),null);
            PartnerApproveDTO approveDo = partnerApproveDao.getInfoById(approveId);
            String title = "【爱心伙伴驳回】";
            String url = "https://www.shuidichou.com/bd/love-partner/personnel/launch?status=2";
            WorkWeixinContentBuilder cb = WorkWeixinContentBuilder.create()
                    .subject(title)
                    .payload("姓名",partnerDo.getName())
                    .payload("状态", "驳回")
                    .payload("审批人", approveDTO.getApproveName())
                    .payload("审批时间", DateUtil.formatDateTime(approveDo.getUpdateTime()))
                    .payload("操作", "<a href=\"" + url + "\">点击查看</a>");
            String content = cb.build();
            CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(partnerDo.getLeaderUniqueCode());
            appPushCrmCaseMsgServiceImpl.sendMsg2Bd(volunteer, content, title, "");
        } else {
            //更新审批表+人员表，审批状态为通过，工作状态为在职，生成二维码(需要)
            partnerApproveDao.updateApproveStatus(approveId, remark, ApproveStatusEnum.APPROVE.getCode());
            cfPartnerInfoDao.updateApproveStatus(approveDTO.getBizId(), ApproveStatusEnum.APPROVE.getCode(),WorkStatusEnum.ON_OFFICE.getCode());
            LovePartnerInfoParam lovePartnerInfoParam = JSON.parseObject(approveDTO.getApproveContent(),LovePartnerInfoParam.class);
            cfPartnerInfoDao.updatePartnerInfo(lovePartnerInfoParam,partnerDo.getUniqueCode());
            if (lovePartnerInfoParam.getIsGenerate() == 1) {
                OpResult<String> qrCodeOpResult = generateQrCode(partnerDo.getUniqueCode());
                if (qrCodeOpResult.isSuccess()){
                    updateQrCode(qrCodeOpResult.getData(),partnerDo.getUniqueCode());
                }
            }
            //兼职审批通过后发送公众号消息
            CfPartnerInfoDo cfPartnerInfoDo = cfPartnerInfoDao.getPartnerInfoByPartnerId(approveDTO.getBizId());
            if (Objects.nonNull(cfPartnerInfoDo)){
                partnerRecruitInfoService.sendPartnerApproveMsg(cfPartnerInfoDo);
            }
        }
        return 1;
    }

    @Override
    public List<PartnerInfoListModel> searchMyPartnerInfoList(String leaderUniqueCode, WorkStatusEnum workStatusEnum) {
        //在人员信息表中查找负责人id = leaderUniqueCode,状态为在职,并倒序
        List<PartnerInfoListModel> partnerInfoListModelList = cfPartnerInfoDao.searchMyPartnerInfo(leaderUniqueCode, workStatusEnum.getCode());
        if (CollectionUtils.isEmpty(partnerInfoListModelList)) {
            return partnerInfoListModelList;
        }
        for (PartnerInfoListModel model : partnerInfoListModelList) {
            model.setEncryptPhone(shuidiCipher.decrypt(model.getEncryptPhone()));
        }
        return partnerInfoListModelList;
    }

    @Override
    public PartnerInfoModel searchMypartnerInfo(long partnerId) {
        PartnerInfoModel partnerInfoModel = new PartnerInfoModel();
        CfPartnerInfoDo cfPartnerInfoDo = cfPartnerInfoDao.getPartnerInfoByPartnerId(partnerId);//人员详情
        //一个人员id，对应多个审批记录
        //根据创建时间，正序拿出来,前端逆序
        List<PartnerApproveDTO> approveDTO = partnerApproveDao.getInfoByBizId(cfPartnerInfoDo.getId(),1);
        List<PartnerApproveModel> approveModelList = new ArrayList<>();
        //在审批流程中添加提交人信息
        PartnerApproveModel approve = new PartnerApproveModel();
        approve.setApproveName(cfPartnerInfoDo.getLeaderName());
        approve.setApproveStatus(-2);//提交状态
        approve.setRemark("");
        approve.setUpdateTime(cfPartnerInfoDo.getCreateTime());
        approveModelList.add(0,approve);
        for (PartnerApproveDTO model : approveDTO){
            PartnerApproveModel partnerApproveModel = new PartnerApproveModel();
            partnerApproveModel.setApproveName(model.getApproveName());
            partnerApproveModel.setApproveStatus(model.getApproveStatus());
            partnerApproveModel.setRemark(model.getRemark());
            partnerApproveModel.setUpdateTime(model.getUpdateTime());
            approveModelList.add(partnerApproveModel);
        }
        partnerInfoModel.setApproveModelList(approveModelList);
        BeanUtils.copyProperties(cfPartnerInfoDo,partnerInfoModel);
        partnerInfoModel.setEncryptIdCard(shuidiCipher.decrypt(partnerInfoModel.getEncryptIdCard()));
        partnerInfoModel.setEncryptPhone(shuidiCipher.decrypt(partnerInfoModel.getEncryptPhone()));
        return partnerInfoModel;
    }

    @Override
    public List<PartnerInfoListModel> searchApplyPartnerInfo(int approveStatus, String leaderUniqueCode) {
        List<PartnerInfoListModel> partnerInfoListModelList = new ArrayList<>();
        //当状态为0,全部
        if (approveStatus == 0){
           partnerInfoListModelList = cfPartnerInfoDao.getPartnerInfoByLeaderId(leaderUniqueCode);
        }else {
            partnerInfoListModelList = cfPartnerInfoDao.listInfoByApproveStatusAndUniqueCode(approveStatus,leaderUniqueCode);
        }
        if (CollectionUtils.isEmpty(partnerInfoListModelList)){
            return partnerInfoListModelList;
        }
        for (PartnerInfoListModel model:partnerInfoListModelList){
            model.setEncryptPhone(shuidiCipher.decrypt(model.getEncryptPhone()));
        }
        return partnerInfoListModelList;
    }

    @Override
    public CfPartnerInfoDo getPartnerInfoByIdCard(String encryptIdCard) {
        return cfPartnerInfoDao.getPartnerInfoByIdCard(encryptIdCard,WorkStatusEnum.ON_OFFICE.getCode());
    }

    @Override
    public List<CfPartnerInfoDo> listAllValidPartner() {
        return cfPartnerInfoDao.listAllValidPartner();
    }

    @Override
    public List<CfWhalePartnerInfoModel> listWhalePartnerInfoByUniqueCodes(List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)){
            return Lists.newArrayList();
        }
        List<List<String>> listList = Lists.partition(uniqueCodes, GeneralConstant.MAX_PAGE_SIZE);
        Map<String,CfPartnerInfoDo> partnerInfoMap = listList.parallelStream().map(list -> cfPartnerInfoDao.listPartnerByUniqueCodes(list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(CfPartnerInfoDo::getUniqueCode, Function.identity(),(oldVal,newVal)->newVal));
        List<CfWhalePartnerInfoModel> resultList = Lists.newArrayListWithExpectedSize(partnerInfoMap.size());
        Map<String, CfPartnerRecruitInfoDo> recruitInfoDoMap = partnerRecruitInfoService.listPartnerByUniqueCodes(uniqueCodes)
                .stream()
                .collect(Collectors.toMap(CfPartnerRecruitInfoDo::getUniqueCode, Function.identity(),(oldVal,newVal)->newVal));
        for (Map.Entry<String,CfPartnerInfoDo> entry : partnerInfoMap.entrySet()){
            CfPartnerInfoDo cfPartnerInfoDo = entry.getValue();
            CfWhalePartnerInfoModel cfWhalePartnerInfoModel = new CfWhalePartnerInfoModel();
            BeanUtils.copyProperties(cfPartnerInfoDo,cfWhalePartnerInfoModel);
            cfWhalePartnerInfoModel.setIdCard(shuidiCipher.decrypt(cfPartnerInfoDo.getEncryptIdCard()));
            cfWhalePartnerInfoModel.setPhone(shuidiCipher.decrypt(cfPartnerInfoDo.getEncryptPhone()));
            CfPartnerRecruitInfoDo partnerRecruitInfoDo = recruitInfoDoMap.get(entry.getKey());
            if (Objects.nonNull(partnerRecruitInfoDo)){
                BeanUtils.copyProperties(partnerRecruitInfoDo,cfWhalePartnerInfoModel);
            }
            resultList.add(cfWhalePartnerInfoModel);
        }
        return resultList;
    }

    @Override
    public int countLovePartnerInfo(BdLovePartnerParam bdLovePartnerParam) {
        return cfPartnerInfoDao.countLovePartnerInfo(bdLovePartnerParam);
    }

    @Override
    public List<CfPartnerInfoModel> listLovePartnerInfo(BdLovePartnerParam bdLovePartnerParam) {
        return cfPartnerInfoDao.listLovePartnerInfo(bdLovePartnerParam);
    }

    @Override
    public CfPartnerInfoDo getPartnerInfoByEncryptPhone(String encryptPhone) {
        return cfPartnerInfoDao.getPartnerInfoByEncryptPhone(encryptPhone);
    }

    @Override
    public List<TeamPartnerModel> searTeamPartnerList(List<Long> orgIds, int level, long orgId) {
        List<TeamPartnerModel> teamPartnerModelList = new ArrayList<>();
        //根据orgIds拿到人员信息,存在多个人员
        List<BdCrmOrgUserRelationDO> userModelList = organizationRelationService.listByOrgIdsFromDB(orgIds);
        //查到父节点为orgid的组织list
        List<BdCrmOrganizationDO> orgModelList = crmSelfBuiltOrgReadService.findDirectSubOrgByOrgIdList(orgIds);
        if (CollectionUtils.isEmpty(userModelList) && CollectionUtils.isEmpty(orgModelList)) {
            return null;
        }
        int workStatus = WorkStatusEnum.ON_OFFICE.getCode();
        //获取兼职总数
        if (CollectionUtils.isNotEmpty(userModelList)){
            Map<String, Integer> uniqueCodeTOCount = cfPartnerInfoDao.getCountByWorkStatus(userModelList.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).collect(Collectors.toList()),workStatus)
                    .stream().collect(Collectors.toMap(CfPartnerCountDo::getLeaderUniqueCode,CfPartnerCountDo::getNum, (before, after) -> before));
            for (BdCrmOrgUserRelationDO userModel : userModelList) {
                if (uniqueCodeTOCount.containsKey(userModel.getUniqueCode())) {
                    TeamPartnerModel userPartnerModel = new TeamPartnerModel();
                    userPartnerModel.setOrgOrMis(1);
                    userPartnerModel.setUniqueKey(userModel.getUniqueCode());
                    userPartnerModel.setName(userModel.getMisName());
                    userPartnerModel.setPartnerCount(uniqueCodeTOCount.get(userModel.getUniqueCode()));
                    teamPartnerModelList.add(userPartnerModel);
                }
            }
        }

        Map<Long, String> crmOrganizationDOMap = Maps.newHashMap();
        if (level == CrowdfundingVolunteerEnum.RoleEnum.AREA_LEADER.getLevel()) {
            if (orgId == 0 && orgIds.size() > 1) {
                List<BdCrmOrganizationDO> crmOrganizationDOList = crmSelfBuiltOrganizationService.getOrgInfoList(orgIds);
                crmOrganizationDOMap = crmOrganizationDOList.stream().collect(Collectors.toMap(BdCrmOrganizationDO::getId, BdCrmOrganizationDO::getOrgName));
            }
        }

        List<Long> ownOrgIdList = crmSelfBuiltOrganizationService.listAllOwnOrg();

        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = bdCrmOrganizationDoMapper.copyList(orgModelList);
        for (BdCrmOrganizationDO orgmodel : bdCrmOrganizationDOList) {
            TeamPartnerModel orgPartnerModel = new TeamPartnerModel();
            orgPartnerModel.setOrgOrMis(0);

            if (MapUtils.isNotEmpty(crmOrganizationDOMap)) {
                String orgName = crmOrganizationDOMap.get(orgmodel.getParentId());

                String name = "";
                if (ownOrgIdList.contains(orgmodel.getParentId())) {
                    name = crmDealOrganizationInfoService.ownOrgNameRename(orgName);
                } else {
                    name = crmDealOrganizationInfoService.partnerOrgNameRename(orgName);
                }
                orgPartnerModel.setName(name + "-" + orgmodel.getOrgName());
            } else {
                orgPartnerModel.setName(orgmodel.getOrgName());
            }

            orgPartnerModel.setUniqueKey(Long.toString(orgmodel.getId()));
            orgPartnerModel.setOrgAttribute(orgmodel.getOrgAttribute());
            orgPartnerModel.setPartnerCount(findPartnerCount((orgmodel.getId()),workStatus));
            teamPartnerModelList.add(orgPartnerModel);
        }
        return teamPartnerModelList;
    }

    @Override
    public void sendApprovePartnerInfoMsg(LovePartnerInfoParam lovePartnerInfoParam,CrowdfundingVolunteer approveVolunteer) {
        if (Objects.isNull(approveVolunteer)){
            return;
        }
        String title = "【爱心伙伴审批】";
        String url = "https://www.shuidichou.com/bd/love-partner/personnel/record?status=1";
        WorkWeixinContentBuilder cb = WorkWeixinContentBuilder.create()
                .subject(title)
                .payload("姓名",lovePartnerInfoParam.getName())
                .payload("人员身份", PartnerTypeEnum.getEnumDesc(lovePartnerInfoParam.getAccountType()))
                .payload("负责人", lovePartnerInfoParam.getLeaderName())
                .payload("操作", "<a href=\"" + url + "\">点击查看</a>");
        String content = cb.build();
        appPushCrmCaseMsgServiceImpl.sendMsg2Bd(approveVolunteer, content, title, "");
    }

    @Override
    public CfPartnerInfoDo getPartnerInfoById(Long id) {
        return cfPartnerInfoDao.getPartnerInfoByPartnerId(id);
    }

    @Override
    public int adjustLeaderInfo(Long id, String leaderUniqueCode, long orgId, String orgChainName, String leaderName) {
        return cfPartnerInfoDao.adjustLeaderInfoById(leaderUniqueCode,orgId,orgChainName,leaderName,id);
    }

    @Override
    public int adjustPartnerApproveName(Long id, CrowdfundingVolunteer approveVolunteer,String approveOrgName) {
        return cfPartnerInfoDao.adjustApproveNameById(approveVolunteer.getVolunteerName(),approveOrgName,id);
    }

    @Override
    public List<CfPartnerInfoDo> listPartnerInfoByUniqueCodes(List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)){
            return Lists.newArrayList();
        }
        List<List<String>> listList = Lists.partition(uniqueCodes, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> cfPartnerInfoDao.listPartnerByUniqueCodes(list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
    }

    @Override
    public OpResult<Void> cancelCooperation(String leaderUniqueCode, String uniqueCode) {
        int res = cfPartnerInfoDao.cancelCooperation(leaderUniqueCode,uniqueCode,WorkStatusEnum.LEAVE_OFFICE.getCode(),new Date());
        if (res > 0){
            //同步释放公众号爱心伙伴
            partnerRecruitInfoService.cancelCooperation(leaderUniqueCode,uniqueCode);
            return OpResult.createSucResult();
        }else{
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
        }
    }

    @Override
    public OpResult<String> importLovePartnreInfo(MultipartFile file) {

        ArrayList<LovePartnerInfoModel> lovePartnerInfoModelList = Lists.newArrayList();
        try {
            EasyExcel.read(file.getInputStream(), LovePartnerInfoModel.class, new AnalysisEventListener<LovePartnerInfoModel>() {
                @Override
                public void invoke(LovePartnerInfoModel lovePartnerInfoModel, AnalysisContext analysisContext) {
                    lovePartnerInfoModelList.add(lovePartnerInfoModel);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                }
            }).sheet("Sheet1").doRead();
            for (LovePartnerInfoModel lovePartnerInfoParam : lovePartnerInfoModelList) {
                Long id = lovePartnerInfoParam.getId();
                int costType = lovePartnerInfoParam.getCostType();
                cfPartnerInfoDao.updateCostType(costType, id);
            }
            return OpResult.createSucResult();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_ERROR);
    }

    @Override
    public OpResult<CfRefundPartnerInfoMode> getPartnerInfoByTraceNo(String traceNo) {
        CfCaseRefundTaskDO caseRefundTaskInfo = cfCaseRefundTaskService.getCaseRefundTaskInfo(traceNo);
        if (Objects.isNull(caseRefundTaskInfo)) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        CfWaitDealTypeRecordDO waitDealResultById = cfWaitDealTypeRecordService.getWaitDealTypeById(caseRefundTaskInfo.getWaitDealId());
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(waitDealResultById.getUniqueCode());
        CfRefundPartnerInfoMode cfRefundPartnerInfoMode = new CfRefundPartnerInfoMode();
        cfRefundPartnerInfoMode.setName(volunteer.getVolunteerName());
        cfRefundPartnerInfoMode.setUniqueCode(waitDealResultById.getUniqueCode());
        cfRefundPartnerInfoMode.setFollowTime(caseRefundTaskInfo.getCreateTime());
        return OpResult.createSucResult(cfRefundPartnerInfoMode);
    }

    //查找当前组织下的在职的所有兼职人数
    public int findPartnerCount(long orgId,int workStatus){
        //找到该orgId下的所有组织id 包括自己
        List<Long> orgIds = Lists.newArrayList();
        List<BdCrmOrganizationDO> orgModelList = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(orgId);
        if(CollectionUtils.isNotEmpty(orgModelList)) {
            for (BdCrmOrganizationDO bdCrmOrganizationDO : orgModelList) {
                orgIds.add(bdCrmOrganizationDO.getId());
            }
        }else {
            return 0;
        }
        return  cfPartnerInfoDao.getAllPartnerCount(orgIds,workStatus);

    }

    /**
     * 生成带水滴筹logo的二维码
     * @param uniqueCode
     * @return
     */
    public OpResult<String> generateQrCode(String uniqueCode) {
        String scene = "cf_volunteer_" + uniqueCode;
        String ticket = null;
        try{
            Response<String> response = weiXinFeginClient.generateQrcode(scene);
            if(response.ok()){
                ticket = response.getData();
            }
            log.info("generateQrcode fegin接口返回信息:{}", JSON.toJSONString(response));
        }catch (Exception e){
            log.error("weiXinFeginClient.generateQrcode error",e);
        }
        if (StringUtils.isEmpty(ticket)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.FEIGN_TIMEOUT);
        }
        String qrCode = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket;
        String qrCodeWithLogo = qrCodeService.generateQrCode(qrCode, base64Logo, uniqueCode).getData();
        return OpResult.createSucResult(qrCodeWithLogo);
    }

    /**
     * 更新二维码信息
     * @param qrCodeWithLogo
     * @param uniqueCode
     */
    public void updateQrCode(String qrCodeWithLogo,String uniqueCode){
        //将二维码上传
        cfPartnerInfoDao.updateQrCode(qrCodeWithLogo,uniqueCode);
        //更新招募信息表中的qrCode
        partnerRecruitInfoService.updateQrCodeByUniqueCode(qrCodeWithLogo,uniqueCode);
    }

    /**
     * 身份证与兼职唯一校验
     * @param lovePartnerInfoParam
     * @param isCheckPartner
     * @return
     */
    private OpResult<Integer> identityCheck(LovePartnerInfoParam lovePartnerInfoParam,boolean isCheckPartner){
        //进行姓名和身份证号校验
        String idCard = lovePartnerInfoParam.getIdCard();
        String name = lovePartnerInfoParam.getName();
        int length = Math.min(idCard.length(), 14);
        String substring = idCard.substring(6, length);
        CfGrowthtoolErrorCode verifyIdCardResult = riskControlDelegate.verifyIdCard(name, idCard, Long.parseLong(substring));
        if (verifyIdCardResult != CfGrowthtoolErrorCode.SUCCESS) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ID_CARD_NOT_MATCH_NAME, CfGrowthtoolErrorCode.ID_CARD_NOT_MATCH_NAME.getMsg());
        }
        String encryptIdCard = oldShuidiCipher.aesEncrypt(lovePartnerInfoParam.getIdCard());
        String encryptPhone = oldShuidiCipher.aesEncrypt(lovePartnerInfoParam.getPhone());
        lovePartnerInfoParam.setIdCard(encryptIdCard);
        lovePartnerInfoParam.setPhone(encryptPhone);
        if (isCheckPartner){
            //用身份证 + 在职状态 进行兼职唯一的检验
            CfPartnerInfoDo cfPartnerInfoDo = cfPartnerInfoDao.getPartnerInfoByIdCard(encryptIdCard, WorkStatusEnum.ON_OFFICE.getCode());
            if (Objects.nonNull(cfPartnerInfoDo)) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.DUPLICATE_ENTRY, CfGrowthtoolErrorCode.DUPLICATE_ENTRY.getMsg());
            }
        }
        return OpResult.createSucResult(CfGrowthtoolErrorCode.SUCCESS.getCode());
    }

    /**
     * 审批信息入库
     * @param lovePartnerInfoParam
     * @param approveStatus
     * @param approveVolunteer
     */
    private void insertPartnerApprove(LovePartnerInfoParam lovePartnerInfoParam, int approveStatus,CrowdfundingVolunteer approveVolunteer) {
        if (Objects.isNull(approveVolunteer)){
            String content = String.format("%s下的顾问:%s,提交的爱心伙伴:%s 找不到对应的审批人,请联系运营处理",
                    lovePartnerInfoParam.getOrgChainName(),lovePartnerInfoParam.getLeaderName(),lovePartnerInfoParam.getName());
            if (applicationService.isDevelopment()){
                content = "测试环境:" + System.lineSeparator() +
                        String.format("%s下的顾问:%s,提交的爱心伙伴:%s 找不到对应的审批人,请联系测试处理",
                                lovePartnerInfoParam.getOrgChainName(), lovePartnerInfoParam.getLeaderName(), lovePartnerInfoParam.getName())
                        ;
            }
            AlarmBotService.sentText("a7021cd6-ea5b-4ba2-8d74-3ecc09bbb2d3", content, null, null);
            return;
        }
        //获取审批人信息
        PartnerApproveDTO partnerApproveDTO = new PartnerApproveDTO();
        partnerApproveDTO.setUniqueCode(lovePartnerInfoParam.getLeaderUniqueCode());
        partnerApproveDTO.setApplyName(lovePartnerInfoParam.getLeaderName());
        partnerApproveDTO.setType(1);
        //审核内容是 id对应的人员信息model 的字符串
        String approveContent = JSON.toJSONString(lovePartnerInfoParam);
        partnerApproveDTO.setApproveContent(approveContent);
        partnerApproveDTO.setBizId(lovePartnerInfoParam.getId());
        partnerApproveDTO.setApproveMis(approveVolunteer.getMis());
        partnerApproveDTO.setApproveName(approveVolunteer.getVolunteerName());
        partnerApproveDTO.setApproveUniqueCode(approveVolunteer.getUniqueCode());
        partnerApproveDTO.setApproveStatus(approveStatus);
        //将审批信息插入审批表中
        partnerApproveDao.insert(partnerApproveDTO);
    }

    /**
     * 设置审批人信息
     * @param approveVolunteer
     * @param lovePartnerInfoParam
     */
    private void setApproveInfo(CrowdfundingVolunteer approveVolunteer, LovePartnerInfoParam lovePartnerInfoParam){
        if (Objects.nonNull(approveVolunteer)){
            lovePartnerInfoParam.setApproveName(approveVolunteer.getVolunteerName());
            List<BdCrmOrganizationDO> bdCrmOrganizationList = memberInfoService.listForCByUniqueCode(approveVolunteer.getUniqueCode()).stream().sorted(Comparator.comparing(BdCrmOrganizationDO::depth)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(bdCrmOrganizationList)){
                lovePartnerInfoParam.setApproveOrgName(crmSelfBuiltOrgReadService.listParentOrgAsChain(bdCrmOrganizationList.get(0).getId()).stream().map(item -> String.valueOf(item.getOrgName())).collect(Collectors.joining("-")));
            }else{
                lovePartnerInfoParam.setApproveOrgName("");
            }
        }else{
            lovePartnerInfoParam.setApproveName("");
            lovePartnerInfoParam.setApproveOrgName("");
        }
    }
}
