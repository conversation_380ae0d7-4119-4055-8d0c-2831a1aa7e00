package com.shuidihuzhu.cf.cfgrowthtoolapi.service.homepage;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.homepage.CfUserEvaluationVo;
import com.shuidihuzhu.client.cf.growthtool.model.CfHomePageUserEvaluationDo;
import com.shuidihuzhu.client.cf.workorder.model.UgcHandleOrderParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-06-02
 */
public interface CfHomePageUserEvaluationService {

    /**
     * 插入数据
     * @param homePageUserEvaluationDo
     * @return
     */
    int insert(CfHomePageUserEvaluationDo homePageUserEvaluationDo);

    /**
     * 更新工单处理状态
     * @param handleOrderParam
     * @param workOrderIds
     * @return
     */
    int updateByWorkOrderId(UgcHandleOrderParam handleOrderParam, List<Long> workOrderIds);

    /**
     * 查询用户评价
     * @param uniqueCode
     * @return
     */
    CfUserEvaluationVo getUserEvaluation(String uniqueCode);
}
