package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponMoneyInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponMoneyParam;

import java.util.List;
import java.util.Map;

public interface ICfWeaponInfoService {

    WeaponMoneyInfo getMoneyInfo(WeaponMoneyParam weaponMoneyParam);

    List<WeaponMoneyInfo> listMoneyInfo(List<WeaponMoneyParam> weaponMoneyParamList);

    Map<Integer, WeaponMoneyInfo> mapMoneyInfoByBudgetId(List<WeaponMoneyParam> weaponMoneyParamList);

    WeaponMoneyInfo getNoLeafMoneyInfo(WeaponMoneyParam weaponMoneyParam);

    List<WeaponMoneyInfo> listNoLeafMoneyInfo(List<WeaponMoneyParam> weaponMoneyParamList);

    Map<Integer, WeaponMoneyInfo> mapNoLeafMoneyInfoByBudgetGroupId(List<WeaponMoneyParam> weaponMoneyParamList);

}
