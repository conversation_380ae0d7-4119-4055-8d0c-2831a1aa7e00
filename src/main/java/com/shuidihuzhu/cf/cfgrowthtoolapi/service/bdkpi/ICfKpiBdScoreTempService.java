package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiBdScoreDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.*;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-08-03 17:05
 */
public interface ICfKpiBdScoreTempService {
    List<CfKpiBdScoreDO> getKpiBdList(Integer level, String monthKey, List<String> uniqueCodeList);

    int batchInsert(int level, String monthKey, List<CfCrmMemberSnapshotDO> cfCrmMemberSnapshotDOS, List<CustomPerformanceScoreModel> customPerformanceScoreModels);

    int batchUpdate(List<CfKpiBdScoreDO> kpiBdList, List<CustomPerformanceScoreModel> customPerformanceScoreModels);

    int batchUpdateForC(List<CfKpiBdScoreDO> kpiBdList, CfKPIBdScoreModel cfKPIBdScoreModel);

    CfKpiBdScoreDO getScore(String uniqueCode, String monthKey);

    OpResult checkIsBdScore(Integer level, String monthKey);

    List<CfKpiBdScoreDO> listKpiBdByMonthAndUniqueCodes(String monthKey, List<String> uniqueCodeList);

    int batchInsertAndLevel(int level, String monthKey, List<CfCrmMemberSnapshotDO> cfCrmMemberSnapshotDOS, List<CustomPerformanceScoreModel> customPerformanceScoreModels, int circulateLevel, CfKPICommissionAwardCollectModel awardAmounts);

    int batchUpdateCirculateLevel(List<CfKpiBdScoreDO> kpiBdList, int circulateLevel);

    List<CfKpiBdScoreDO> listKpiBdByMonthKey(String monthKey);
}
