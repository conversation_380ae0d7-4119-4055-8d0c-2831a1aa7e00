package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiDeviceAdConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiOperateRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfFangbiandaiOrderDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfJiekongDeviceDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfFangbiandaiOfficialAccountDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfFangbiandaiUserFollowRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.FangbiandaiDeviceParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.FangbiandaiOrderParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.client.baseservice.pay.model.PayResultV2;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-05-30 16:45
 */
public interface ICfFangbiandaiService {
	CfJiekongDeviceDO getCfJiekongDevice(String uniqueCode);

	CfJiekongDeviceDO getCfJiekongDevice(long id);

	int saveDevice(CfJiekongDeviceDO cfJiekongDeviceDO);

	int updateFangbiandaiDevice(long id, String qrCode);

	List<CfFangbiandaiOrderDO> getCfFangbiandaiOrderByUnionId(String unionId, Date startTime);

	int saveOrder(CfFangbiandaiOrderDO cfFangbiandaiOrderDO);

	CfFangbiandaiOrderDO getCfFangbiandaiOrderByOrderId(String orderId);

	void updateOrder(CfFangbiandaiOrderDO cfFangbiandaiOrderDO);

	int decrStockNum(String deviceId, String deviceSupplier);

	int addStockNum(long id, Integer stockNum);

	CfJiekongDeviceDO getByDeviceId(String deviceId,String deviceSupplier);

	CfJiekongDeviceDO getByDeviceIdWithMachineId(String deviceId, String machineId);


	//支付回调更新支付状态
	void updateRealPayStatus(String orderId, int payStatus, String transactionId, int jiekongStatus, int status,String payOrderId);
	//立即退款时更新状态
	void updateRefundStatus(String orderId, int refundStatus, int payStatus,String refundOrderId);

    List<CfJiekongDeviceDO> getFangbiandaiDevice(String machineId, int pageNo, int pageSize);

	List<CfJiekongDeviceDO> getFangbiandaiDeviceByMachineId(String machineId);

	void offlineDevice(long id);

	int modDevice(FangbiandaiDeviceParam fangbiandaiDeviceParam);

	CommonResultModel<CfFangbiandaiOrderDO> getOrderList(FangbiandaiOrderParam fangbiandaiOrderParam);

    int saveCfFangbiandaiOperateRecord(CfFangbiandaiOperateRecordDO cfFangbiandaiOperateRecordDO);

	List<CfFangbiandaiOperateRecordDO> getCfFangbiandaiOperateRecordList(int operateType, String machineId);

    List<String> getAllDeviceMachineIds();

    long getFangbiandaiDeviceCount(String machineId);

    List<CfFangbiandaiOrderDO> getCurrentDayNoStatusCfFangbiandaiOrder(String currentDay);

    int updateDeviceSupplier(List<Integer> ids, String deviceSupplier);


    int updateDeviceId(String deviceId, long id);

	List<CfFangbiandaiDeviceAdConfigDO> getCfFangbiandaiDeviceAdConfigDO(String deviceId, String deviceSupplier);

	int insertCfFangbiandaiDeviceAdConfigDO(CfFangbiandaiDeviceAdConfigDO cfFangbiandaiDeviceAdConfigDO);

    List<CfFangbiandaiOfficialAccountDO> listAllOfficialAccount();

    CfFangbiandaiOfficialAccountDO selectByThirdType(String thirdType);

    void saveUserFollowRecord(CfFangbiandaiUserFollowRecordDO record);

	List<String> listFollowThirdTypeByUnionId(String unionId);

	void updateFollowRecordOrderIdById(Long id, String orderId);

	CfFangbiandaiUserFollowRecordDO getFollowRecordByOpenIdWithThirdType(String openId, String thirdType);

	Integer countSuccessOrder(String unionId,
						   String startTime);
}
