package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseScoreDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiCaseScoreVO;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
public interface ICfKpiCaseScoreService {

    OpResult<List<CfKpiCaseScoreVO>> queryCaseScoreDetail(String monthkey, String uniqueCode);

    int batchSaveOrUpdate(List<CfKpiCaseScoreDO> kpiCaseScoreList, String monthKey, String uniqueCode);

    OpResult<List<CfKpiCaseScoreVO>> queryCaseScoreWhaleDetail(String monthKey, String uniqueCode, long validAmount, int validDonateNum);

    int batchDeleteByUniqueCode(String uniqueCode, String monthKey);
}
