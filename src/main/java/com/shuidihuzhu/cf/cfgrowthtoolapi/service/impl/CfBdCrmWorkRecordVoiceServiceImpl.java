package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfWorkRecordVoiceModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.PreposeMaterialVoiceCountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WorkRecordVoiceParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCrmWorkRecordVoiceService;
import com.shuidihuzhu.cf.dao.bdcrm.CfWorkRecordVoiceDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: yangliming
 * @create: 2019/12/25
 */
@Service
public class CfBdCrmWorkRecordVoiceServiceImpl implements ICfBdCrmWorkRecordVoiceService {

    @Autowired
    private CfWorkRecordVoiceDao cfWorkRecordVoiceDao;


    @Override
    public int addWorkRecordVoice(WorkRecordVoiceParam param) {
        return cfWorkRecordVoiceDao.addWorkRecordVoice(param);
    }

    @Override
    public List<CfWorkRecordVoiceModel> getWorkRecordVoiceByUserIdAndCaseId(String volunteerUniqueCode, Long preposeMaterialId) {
        return cfWorkRecordVoiceDao.getWorkRecordVoiceByUserIdAndCaseId(volunteerUniqueCode, preposeMaterialId);
    }

    @Override
    public Long getVoiceDurationByUserIdAndCaseId(String volunteerUniqueCode, Long preposeMaterialId) {
        return cfWorkRecordVoiceDao.getVoiceDurationByUserIdAndCaseId(volunteerUniqueCode, preposeMaterialId);
    }
    @Override
    public List<Long> getVoicePreposeMaterialIdByPreposeMaterialIds(List<Long> preposeMaterialIds){
        List<List<Long>> listList = Lists.partition(preposeMaterialIds, GeneralConstant.MAX_PAGE_SIZE);
        List<Long> result = listList.parallelStream().map(list -> cfWorkRecordVoiceDao.getVoicePreposeMaterialIdByPreposeMaterialIds(list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).get();
        return result;
    }
    @Override
    public List<PreposeMaterialVoiceCountModel> getPreposeMaterialVoiceCountModel(List<Long> preposeMaterialIds){
        if (CollectionUtils.isEmpty(preposeMaterialIds)){
            return Lists.newArrayList();
        }
        return cfWorkRecordVoiceDao.getPreposeMaterialVoiceCountModel(preposeMaterialIds);
    }

    @Override
    public List<CfWorkRecordVoiceModel> getWorkRecordVoiceByPreposeMaterial(long preposeMaterial) {
        return cfWorkRecordVoiceDao.getWorkRecordVoiceByPreposeMaterial(preposeMaterial);
    }
}
