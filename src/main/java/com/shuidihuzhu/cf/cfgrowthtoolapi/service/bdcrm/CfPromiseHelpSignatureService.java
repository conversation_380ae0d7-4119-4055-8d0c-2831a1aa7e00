package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.common.CommonPermissionConfigEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfPromiseHelpSignatureDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.CommonPermissionConfigDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.common.CommonPermissionConfigService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolEncryptUtil;
import com.shuidihuzhu.cf.dao.bdcrm.CfPromiseHelpSignatureDao;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.model.CfPromiseHelpSignatureSelfVo;
import com.shuidihuzhu.client.model.CfPromiseHelpSignatureVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/22  15:52
 */
@Service
@Slf4j
public class CfPromiseHelpSignatureService {

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private CfPromiseHelpSignatureDao cfPromiseHelpSignatureDao;

    @Autowired
    private CommonPermissionConfigService commonPermissionConfigService;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private ICrmSelfBuiltOrgReadService orgReadService;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    private static final int URL_VALID_DAYS = 30;

    private static final String URL = "https://www.shuidichou.com/raise/new-launch/e-sign?info=";

    public Response<String> getUrl(String phone, CrowdfundingVolunteer crowdfundingVolunteer) {
        //校验参数
        Response<Void> checkParamResponse = checkParam(phone, crowdfundingVolunteer);
        if (checkParamResponse.notOk()) {
            return NewResponseUtil.makeFail(checkParamResponse.getMsg());
        }
        //加密手机号
        Response<String> encryptResponse = encrypt(phone);
        if (encryptResponse.notOk()) {
            return NewResponseUtil.makeFail(encryptResponse.getMsg());
        }
        String generatedEncryptPhone = encryptResponse.getData();

        CfPromiseHelpSignatureDo cfPromiseHelpSignatureDo = cfPromiseHelpSignatureDao.queryByGeneratedEncryptPhone(generatedEncryptPhone);
        Boolean signature = isSignature(cfPromiseHelpSignatureDo);
        if (signature == null) {
            return NewResponseUtil.makeFail("未生成求助承诺链接");
        }
        if (signature) {
            return NewResponseUtil.makeFail("已签署求助承诺");
        }
        return NewResponseUtil.makeSuccess(cfPromiseHelpSignatureDo.getUrl());
    }

    public Response<Void> create(String phone, CrowdfundingVolunteer crowdfundingVolunteer) {
        String key = "cf_promise_help_signature_create_" + phone;
        String tryLock = null;
        try {
            tryLock = cfRedissonHandler.tryLock(key, 0, TimeUnit.SECONDS.toMillis(1));
        } catch (Exception e) {
            log.error("CfPromiseHelpSignatureService tryLock error. key:{}", key, e);
        }

        log.info("CfPromiseHelpSignatureService tryLock is:{}", tryLock);
        if (StringUtils.isBlank(tryLock)) {
            log.info("CfPromiseHelpSignatureService tryLock is null. key:{}", key);
            return NewResponseUtil.makeFail("请勿重复提交");
        }

        try {
            //校验参数
            Response<Void> checkParamResponse = checkParam(phone, crowdfundingVolunteer);
            if (checkParamResponse.notOk()) {
                return NewResponseUtil.makeFail(checkParamResponse.getMsg());
            }
            //加密手机号
            Response<String> encryptResponse = encrypt(phone);
            if (encryptResponse.notOk()) {
                return NewResponseUtil.makeFail(encryptResponse.getMsg());
            }
            String generatedEncryptPhone = encryptResponse.getData();

            CfPromiseHelpSignatureDo cfPromiseHelpSignatureDo = cfPromiseHelpSignatureDao.queryByGeneratedEncryptPhone(generatedEncryptPhone);
            Boolean signature = isSignature(cfPromiseHelpSignatureDo);
            if (signature != null) {
                return NewResponseUtil.makeSuccess();
            }

            String encryptInfo = GrowthtoolEncryptUtil.encryptByPromiseHelpSignature(phone, crowdfundingVolunteer.getUniqueCode());
            try {
                String decodeEncryptInfo = URLDecoder.decode(encryptInfo, StandardCharsets.UTF_8);
                int res = insert(generatedEncryptPhone, crowdfundingVolunteer.getUniqueCode(), decodeEncryptInfo, URL + encryptInfo);
                if (res == 0) {
                    return NewResponseUtil.makeFail("自动生成求助承诺链接失败，请重新进入页面！");
                }
            } catch (Exception e) {
                log.error("CfPromiseHelpSignatureService create phone:{} error", phone, e);
            }
        } catch (Exception e) {
            log.info("CfPromiseHelpSignatureService create error. phone:{}", phone, e);
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    cfRedissonHandler.unLock(key, tryLock);
                }
            } catch (Exception e) {
                log.info("CfPromiseHelpSignatureService unLock error. key:{}, tryLock:{}", key, tryLock, e);
            }
        }
        return NewResponseUtil.makeSuccess();
    }

    public Response<Boolean> check(String phone, CrowdfundingVolunteer crowdfundingVolunteer) {
        //校验参数
        Response<Void> checkParamResponse = checkParam(phone, crowdfundingVolunteer);
        if (checkParamResponse.notOk()) {
            return NewResponseUtil.makeFail(checkParamResponse.getMsg());
        }
        //加密手机号
        Response<String> encryptResponse = encrypt(phone);
        if (encryptResponse.notOk()) {
            return NewResponseUtil.makeFail(encryptResponse.getMsg());
        }
        String generatedEncryptPhone = encryptResponse.getData();
        CfPromiseHelpSignatureDo cfPromiseHelpSignatureDo = cfPromiseHelpSignatureDao.queryByGeneratedEncryptPhone(generatedEncryptPhone);
        Boolean signature = isSignature(cfPromiseHelpSignatureDo);
        return NewResponseUtil.makeSuccess(signature);
    }

    public Response<Boolean> permission(CrowdfundingVolunteer crowdfundingVolunteer) {
        if (crowdfundingVolunteer == null) {
            return NewResponseUtil.makeSuccess();
        }

        CommonPermissionConfigDo byConfigType = commonPermissionConfigService.getByConfigType(CommonPermissionConfigEnums.ConfigType.ADVISOR_COMPLIANCE.getCode());
        if (byConfigType == null || StringUtils.isBlank(byConfigType.getPermissionValue())) {
            return NewResponseUtil.makeSuccess();
        }

        //查询顾问所在组织
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOList = crmOrganizationRelationService.listMemberOrgRelationByUniqueCode(crowdfundingVolunteer.getUniqueCode());
        if (CollectionUtils.isEmpty(bdCrmOrgUserRelationDOList)) {
            return NewResponseUtil.makeSuccess();
        }

        List<String> orgIdList = Splitter.on(",").splitToList(byConfigType.getPermissionValue());
        boolean pass = false;
        for (String orgId : orgIdList) {
            List<BdCrmOrganizationDO> bdCrmOrganizationDOList = orgReadService.listAllSubOrgIncludeSelf(Long.parseLong(orgId));
            Set<Long> orgIdSet = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toSet());
            pass = bdCrmOrgUserRelationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).anyMatch(orgIdSet::contains);
            if (pass) {
                break;
            }
        }
        return NewResponseUtil.makeSuccess(pass);
    }

    public Response<CfPromiseHelpSignatureSelfVo> isSelf(String info, String phone) {
        info = replaceEncryptInfo(info);
        CfPromiseHelpSignatureDo cfPromiseHelpSignatureDo = cfPromiseHelpSignatureDao.queryByEncryptInfo(info);
        if (cfPromiseHelpSignatureDo == null) {
            return NewResponseUtil.makeFail("求助承诺链接不存在");
        }
        String decryptInfo = GrowthtoolEncryptUtil.decryptByPromiseHelpSignature(info);
        if (StringUtils.isEmpty(decryptInfo)) {
            return NewResponseUtil.makeFail("解密失败");
        }
        CfPromiseHelpSignatureSelfVo cfPromiseHelpSignatureSelfVo = new CfPromiseHelpSignatureSelfVo();
        if (decryptInfo.equals(phone)) {
            cfPromiseHelpSignatureSelfVo.setSelf(true);
        } else {
            cfPromiseHelpSignatureSelfVo.setSelf(false);
            cfPromiseHelpSignatureSelfVo.setPhone(decryptInfo);
        }
        return NewResponseUtil.makeSuccess(cfPromiseHelpSignatureSelfVo);
    }

    public Response<CfPromiseHelpSignatureVo> getPromiseHelp(String info, String phone) {
        info = replaceEncryptInfo(info);
        String decryptInfo = GrowthtoolEncryptUtil.decryptByPromiseHelpSignature(info);
        if (StringUtils.isEmpty(decryptInfo)) {
            return NewResponseUtil.makeFail("解密失败");
        }
        if (!decryptInfo.equals(phone)) {
            return NewResponseUtil.makeFail("手机号不匹配");
        }

        CfPromiseHelpSignatureDo cfPromiseHelpSignatureDo = cfPromiseHelpSignatureDao.queryByEncryptInfo(info);
        Boolean signature = isSignature(cfPromiseHelpSignatureDo);
        CfPromiseHelpSignatureVo cfPromiseHelpSignatureVo = new CfPromiseHelpSignatureVo();
        if (signature != null) {
            cfPromiseHelpSignatureVo.setValid(true);
            cfPromiseHelpSignatureVo.setSignatureUrl(signature ? cfPromiseHelpSignatureDo.getSignatureUrl() : "");
            cfPromiseHelpSignatureVo.setCountdownComplete(cfPromiseHelpSignatureDo.getCountdownCompleteTime() != null);
        }
        return NewResponseUtil.makeSuccess(cfPromiseHelpSignatureVo);
    }

    public Response<Void> signature(String info, String signatureEncryptPhone, String signatureUrl) {
        info = replaceEncryptInfo(info);
        CfPromiseHelpSignatureDo cfPromiseHelpSignatureDo = cfPromiseHelpSignatureDao.queryByEncryptInfo(info);
        Boolean signature = isSignature(cfPromiseHelpSignatureDo);
        if (signature == null) {
            return NewResponseUtil.makeFail("求助承诺链接已过期");
        }
        if (signature) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.PROMISE_SIGN_REPEAT);
        }
        int res = cfPromiseHelpSignatureDao.updateByEncryptInfo(info, signatureEncryptPhone, signatureUrl, new Date());
        if (res == 0) {
            return NewResponseUtil.makeFail("签署失败");
        }
        return NewResponseUtil.makeSuccess();
    }

    public Response<Void> countdownComplete(String info) {
        info = replaceEncryptInfo(info);
        int res = cfPromiseHelpSignatureDao.updateCountdownCompleteTimeByEncryptInfo(info, new Date());
        if (res == 0) {
            return NewResponseUtil.makeFail("倒计时失败");
        }
        return NewResponseUtil.makeSuccess();
    }

    private int insert(String generatedEncryptPhone, String uniqueCode, String encryptInfo, String url) {
        CfPromiseHelpSignatureDo cfPromiseHelpSignatureDo = new CfPromiseHelpSignatureDo();
        cfPromiseHelpSignatureDo.setGeneratedEncryptPhone(generatedEncryptPhone);
        cfPromiseHelpSignatureDo.setUrlGeneratedTime(new Date());
        cfPromiseHelpSignatureDo.setUniqueCode(uniqueCode);
        cfPromiseHelpSignatureDo.setUrl(url);
        cfPromiseHelpSignatureDo.setEncryptInfo(encryptInfo);
        return cfPromiseHelpSignatureDao.insert(cfPromiseHelpSignatureDo);
    }

    private Boolean isSignature(CfPromiseHelpSignatureDo cfPromiseHelpSignatureDo) {
        //用户没有待签署链接
        if (cfPromiseHelpSignatureDo == null) {
            return null;
        }
        //用户待签署链接已经过期
        if (DateUtil.addDay(cfPromiseHelpSignatureDo.getUrlGeneratedTime(), URL_VALID_DAYS).getTime() < System.currentTimeMillis()) {
            return null;
        }
        //用户已经签署
        return StringUtils.isNotBlank(cfPromiseHelpSignatureDo.getSignatureUrl());
    }

    private Response<Void> checkParam(String phone, CrowdfundingVolunteer crowdfundingVolunteer) {
        if (StringUtils.isEmpty(phone) || crowdfundingVolunteer == null) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess();
    }

    private Response<String> encrypt(String phone) {
        String generatedEncryptPhone = oldShuidiCipher.aesEncrypt(phone);
        if (StringUtils.isEmpty(generatedEncryptPhone)) {
            return NewResponseUtil.makeFail("手机号加密失败");
        }
        return NewResponseUtil.makeSuccess(generatedEncryptPhone);
    }

    public String replaceEncryptInfo(String encryptInfo) {
        return StringUtils.replace(encryptInfo, " ", "+");
    }
}
