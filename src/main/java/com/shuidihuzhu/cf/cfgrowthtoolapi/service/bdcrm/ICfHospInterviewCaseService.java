package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseHospitalData;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDataStatPageParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDataStatParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-03
 */
public interface ICfHospInterviewCaseService {

    /**
     * 获取医院相关数据
     * @param crmDataStatParam
     * @param orgIdList
     * @return
     */
    List<OrgDataStatVO> getOrgOverviewData4Hosp(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList);

    /**
     * 按人统计数据
     * @param crmDataStatParam
     * @param orgIdList
     * @return
     */
    List<OrgDataStatVO> getOrgDetailData4Person(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList);

    /**
     * 按组织统计数据
     * @param crmDataStatParam
     * @param orgIdList
     * @return
     */
    List<OrgDataStatVO> getOrgDetailData4Org(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList);

    /**
     * 获取人的医院明细数据
     * @param crmDataStatParam
     * @return
     */
    List<HospDataStatBaseVO> getHospDetailData4Person(BdCrmDataStatParam crmDataStatParam);

    /**
     * 获取人的医院明细数据
     * @param crmDataStatParam
     * @return
     */
    List<HospDataStatBaseVO> getVisitSubHospData4Person(BdCrmDataStatParam crmDataStatParam);

    /**
     * 获取人的医院数据
     * @param crmDataStatParam
     * @return
     */
    List<HospDataStatBaseVO> getVisitHospData4Person(BdCrmDataStatParam crmDataStatParam);


    List<HospDataStatBaseVO> getVisitData4PersonGroupByHospitalAndDate(BdCrmDataStatParam crmDataStatParam);

    /**
     * 根据组织查找并对医院去重
     */
    long hospitalCount(BdCrmDataStatPageParam crmDataStatParam);

    long hospitalAreaCount(BdCrmDataStatPageParam crmDataStatParam);

    /**
     * 根据组织查找,按照医院分类
     * @param crmDataStatParam
     * @return
     */
    List<HospDataStatBaseVO> findByOrgIdGroupByHospital(BdCrmDataStatPageParam crmDataStatParam);

    List<HospDataStatBaseVO> findAreaByOrgIdGroupByHospital(BdCrmDataStatPageParam crmDataStatParam);

    /**
     * 根据医院查找,按照时间分类
     * @param crmDataStatParam
     * @return
     */
    List<CampaignMapTrendView> findByHospitalGroupByDate(BdCrmDataStatParam crmDataStatParam);

    /**
     * 根据医院查找，按照人员分类
     * @param crmDataStatParam
     * @return
     */
    List<HospDataStatBaseVO> findByHospitalGroupByVolunteer(BdCrmDataStatParam crmDataStatParam);

    /**
     * 根据人员查找,按照日期分类
     */
    List<HospDataStatBaseVO> findByUniqueCodeGroupByDate(BdCrmDataStatParam crmDataStatParam);

    /**
     * 按照城市市场数据-医院数据
     * @param crmDataStatParam
     * @param cityIds
     * @return
     */
    List<OrgDataStatVO> getCityOverviewData4Hosp(BdCrmDataStatParam crmDataStatParam, List<Integer> cityIds);

    /**
     * 按照城市市场数据-医院数据
     * @param crmDataStatParam
     * @param cityIds
     * @return
     */
    List<OrgDataStatVO> listHospDataGroupByCity(BdCrmDataStatParam crmDataStatParam, List<Integer> cityIds);

    /**
     * 医院市场-单一城市按医院维度分组数据
     * @param crmDataStatParam
     * @param cityIds
     * @param vvhospcodeList
     * @return
     */
    List<HospDataStatBaseVO> listHospDataGroupByHosp(BdCrmDataStatPageParam crmDataStatParam, List<Integer> cityIds, List<String> vvhospcodeList);

    /**
     * 医院市场-单一医院按组织分组数据
     * @param crmDataStatParam
     * @return
     */
    List<OrgDataStatVO> listHospDataGroupByOrg(BdCrmDataStatParam crmDataStatParam);

    /**
     * 根据城市查找并对医院去重
     * @param crmDataStatParam
     * @param cityIds
     * @return
     */
    long hospitalCountByCity(BdCrmDataStatPageParam crmDataStatParam, List<Integer> cityIds);

    long hospitalAreaCountByCityId(BdCrmDataStatPageParam crmDataStatParam, List<Integer> cityIds);

    List<HospDataStatBaseVO> listHospDataGroupByVvhospcode(BdCrmDataStatPageParam crmDataStatParam, List<Integer> cityIds);

    List<HospDataStatBaseVO> listVvHospCodeListGroupByHosp(BdCrmDataStatPageParam crmDataStatParam, List<Integer> cityIds);

    List<CfBdCrmDiagnoseHospitalData> findByCityIdListGroupByHospital(List<Long> cityIdList, List<String> dateTimes);


    /**
     * 设置statList返回数据
     * @param statList
     * @param vvhospcodeList
     * @param statDataFromDb
     */
    void setStatList(List<HospDataStatBaseVO> statList, List<String> vvhospcodeList, List<HospDataStatBaseVO> statDataFromDb);
}
