package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmUserExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.BdGreyFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.BdHomePageVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CrmUserExtModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCrmUserExtService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmUserExtDao;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-02-05 16:14
 */
@Service
public class CfCrmUserExtService implements ICfCrmUserExtService {
    @Autowired
    private CfCrmUserExtDao cfCrmUserExtDao;

    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private ICfVolunteerService cfVolunteerServiceImpl;

    @Override
    public int saveCfCrmUserExtDO(CfCrmUserExtDO cfCrmUserExtDO) {
        return cfCrmUserExtDao.save(cfCrmUserExtDO);
    }

    @Override
    public int updateCfCrmUserExtDO(CfCrmUserExtDO cfCrmUserExtDO) {
        if (cfCrmUserExtDO.getCustomContent()==null&&cfCrmUserExtDO.getEncryptPhone()==null){
            return 0;
        }
        return cfCrmUserExtDao.updateByUniqueCode(cfCrmUserExtDO);
    }
    @Override
    public int updateByIdForBackDoor(CfCrmUserExtDO cfCrmUserExtDO) {
        if (cfCrmUserExtDO.getId()==null || cfCrmUserExtDO.getId()==0L){
            return 0;
        }
        return cfCrmUserExtDao.updateById(cfCrmUserExtDO);
    }

    @Override
    public CrmUserExtModel getCrmUserExtModelByUniqueCode(String uniqueCode) {
        CrmUserExtModel crmUserExtModel = cfCrmUserExtDao.getCrmUserExtModelByUniqueCode(uniqueCode);
        return crmUserExtModel == null ? null : crmUserExtModel.getCrmUserExtModel(shuidiCipher);
    }

    @Override
    public List<CfCrmUserExtDO> getCrmUserExtModelByUniqueCodes(List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)){
            return Lists.newArrayList();
        }
        return cfCrmUserExtDao.getCfCrmUserExtDOByUniqueCodes(uniqueCodes);
    }

    @Override
    public CfCrmUserExtDO getCfCrmUserExtDOByUniqueCode(String uniqueCode) {
        return fullEncryptVphone(cfCrmUserExtDao.getCfCrmUserExtDOByUniqueCode(uniqueCode));
    }

    @Override
    public CfCrmUserExtDO getCfCrmUserExtDOByWeixincodeOrEncryptPhoneC(String str){
        CfCrmUserExtDO cfCrmUserExtDO = cfCrmUserExtDao.getCfCrmUserExtDOByEncryptPhoneC(str);
        if (cfCrmUserExtDO!=null){
            return fullEncryptVphone(cfCrmUserExtDO);
        }
        cfCrmUserExtDO = cfCrmUserExtDao.getCfCrmUserExtDOByWeixincode(str);
        return fullEncryptVphone(cfCrmUserExtDO);
    }


    @Override
    public int updateIsDelete(String uniqueCode, int isDelete){
        return cfCrmUserExtDao.updateIsDelete(uniqueCode, isDelete);
    }
    @Override
    public int updateHeadUrl(String uniqueCode, String headUrl){
        return cfCrmUserExtDao.updateHeadUrl(uniqueCode, headUrl);
    }
    @Override
    public int updateWeixincode(String uniqueCode, String weixincode){
        return cfCrmUserExtDao.updateWeixincode(uniqueCode, weixincode);
    }

    @Override
    public int updateQrCode(String uniqueCode, String qrCode){
        return cfCrmUserExtDao.updateQrCode(uniqueCode, qrCode);
    }

    @Override
    public int updateQyWechatQrCode(String uniqueCode, String qyWechatQrCode) {
        return cfCrmUserExtDao.updateQyWechatQrCode(uniqueCode, qyWechatQrCode);
    }

    @Override
    public int updateEncryptPhoneC(String volunteerUniqueCode, String mobile) {
        return cfCrmUserExtDao.updateEncryptPhoneC(volunteerUniqueCode,mobile);
    }

    @Override
    public List<String> getAll() {
        return cfCrmUserExtDao.getAll();
    }

    @Override
    public List<CfCrmUserExtDO> getAllByPage(Long currentId, Integer size) {
        if (currentId == null || size == null){
            return null;
        }
        return cfCrmUserExtDao.getAllUserExtLimit(currentId,size);
    }

    public CfCrmUserExtDO fullEncryptVphone(CfCrmUserExtDO cfCrmUserExtDO) {
        if (cfCrmUserExtDO == null) {
            return null;
        }
        String encryptVPhone = cfVolunteerServiceImpl.getEncryptVPhone(cfCrmUserExtDO.getMis());
        if (StringUtils.isNotBlank(encryptVPhone)) {
            cfCrmUserExtDO.setEncryptPhoneC(encryptVPhone);
        }
        return cfCrmUserExtDO;
    }
    @Override
    public void updateLabels(String uniqueCode, String labels){
        cfCrmUserExtDao.updateLabels(uniqueCode, labels);
    }
    @Override
    public void updateSceneServiceHospitals(String uniqueCode, String sceneServiceHospital){
        cfCrmUserExtDao.updateSceneServiceHospitals(uniqueCode, sceneServiceHospital);
    }
    @Override
    public void updateWorkCardUrl(String uniqueCode, String workCardUrl){
        cfCrmUserExtDao.updateWorkCardUrl(uniqueCode, workCardUrl);
    }
    @Override
    public void updateCaseIds(String uniqueCode, String caseIds){
        cfCrmUserExtDao.updateCaseIds(uniqueCode, caseIds);
    }

    @Override
    public void updateAchievement(String uniqueCode, String achievementTypes) {
        cfCrmUserExtDao.updateAchievement(uniqueCode, achievementTypes);
    }

}
