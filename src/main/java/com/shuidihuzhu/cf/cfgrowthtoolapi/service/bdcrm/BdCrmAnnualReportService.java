package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.starrocks.RptCfBdAnnualReportDataDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdAnnualReportDataVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.homepage.HomePageService;
import com.shuidihuzhu.cf.dao.starrocks.CfBdAnnualReportDataDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/1/13  11:30
 */
@Service
public class BdCrmAnnualReportService {

    @Autowired
    private CfBdAnnualReportDataDao cfBdAnnualReportDataDao;

    @Autowired
    private HomePageService homePageService;

    @Autowired
    private ICfVolunteerService cfVolunteerServiceImpl;

    public Response<CfBdAnnualReportDataVo> get2024(CrowdfundingVolunteer crowdfundingVolunteer) {
        //参数校验
        if (crowdfundingVolunteer == null || StringUtils.isEmpty(crowdfundingVolunteer.getUniqueCode())) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        //获取顾问头像、顾问title
        CfBdAnnualReportDataVo cfBdAnnualReportDataVo = homePageService.getCfBdAnnualReportDataVo(crowdfundingVolunteer);
        //通过uniqueCode查询数据
        RptCfBdAnnualReportDataDo rptCfBdAnnualReportDataDo = cfBdAnnualReportDataDao.getByUniqueCode(crowdfundingVolunteer.getUniqueCode());
        if (rptCfBdAnnualReportDataDo == null) {
            //查询顾问表获取入职时间
            CrowdfundingVolunteer onWorkVolunteerByUniqueCode = cfVolunteerServiceImpl.getOnWorkVolunteerByUniqueCode(crowdfundingVolunteer.getUniqueCode());
            long entryDay = 0L;
            if (onWorkVolunteerByUniqueCode != null) {
                entryDay = getEntryDay(DateUtil.formatDate(onWorkVolunteerByUniqueCode.getEntryTime()));
            }
            CfBdAnnualReportDataVo result = CfBdAnnualReportDataVo.build(crowdfundingVolunteer.getVolunteerName(),
                    cfBdAnnualReportDataVo.getHeadUrl(),
                    cfBdAnnualReportDataVo.getLabelList(),
                    entryDay,
                    0,
                    0,
                    0);
            return NewResponseUtil.makeSuccess(result);
        }
        //获取入职天数
        long entryDay = getEntryDay(rptCfBdAnnualReportDataDo.getEntryTime());
        //发起案例数为null or 0
        if (rptCfBdAnnualReportDataDo.getCaseCnt() == null || rptCfBdAnnualReportDataDo.getCaseCnt() == 0) {
            CfBdAnnualReportDataVo result = CfBdAnnualReportDataVo.build(crowdfundingVolunteer.getVolunteerName(),
                    cfBdAnnualReportDataVo.getHeadUrl(),
                    cfBdAnnualReportDataVo.getLabelList(),
                    entryDay,
                    0,
                    0,
                    0);
            return NewResponseUtil.makeSuccess(result);
        }
        //获取发起案例数、个人年度累计捐款金额、个人年度累计捐单
        int caseCnt = rptCfBdAnnualReportDataDo.getCaseCnt();
        long donateCnt = Optional.ofNullable(rptCfBdAnnualReportDataDo.getDonateCnt()).orElse(0L);
        long donateAmt = Optional.ofNullable(rptCfBdAnnualReportDataDo.getDonateAmt()).orElse(0L);
        //构建结果
        CfBdAnnualReportDataVo result = CfBdAnnualReportDataVo.build(crowdfundingVolunteer.getVolunteerName(),
                cfBdAnnualReportDataVo.getHeadUrl(),
                cfBdAnnualReportDataVo.getLabelList(),
                entryDay,
                caseCnt,
                donateAmt,
                donateCnt);
        return NewResponseUtil.makeSuccess(result);
    }

    private long getEntryDay(String entryTime) {
        if (StringUtils.isEmpty(entryTime)) {
            return 0L;
        }
        return ChronoUnit.DAYS.between(LocalDate.parse(entryTime), LocalDate.now()) + 1L;
    }

}
