package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import brave.Tracing;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmVolunteerPreposeMaterialCountDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CrmVolunteerPreposeMaterialCaseCountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCrmVolunteerPreposeMaterialCountService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolAsyncPoolConstants;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmVolunteerPreposeMaterialCountDao;
import com.shuidihuzhu.cf.dao.bdcrm.ClewCrowdfundingReportRelationDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/11/6 7:20 PM
 */
@Service
@Slf4j
public class CfCrmVolunteerPreposeMaterialCountService implements ICfCrmVolunteerPreposeMaterialCountService {
    @Autowired
    private CfCrmVolunteerPreposeMaterialCountDao cfCrmVolunteerPreposeMaterialCountDao;
    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;
    @Autowired
    private ClewCrowdfundingReportRelationDao clewCrowdfundingReportRelationDao;
    @Resource(name = GrowthtoolAsyncPoolConstants.CRM_VOLUNTEER_POOL)
    private ExecutorService executorService ;

    @Override
    public List<CrmVolunteerPreposeMaterialCaseCountModel> getCountByParam(List<String> dateTimes, List<String> uniqueCodeList) {

        List<String> distinctUniqueCodeList = uniqueCodeList.stream().distinct().collect(Collectors.toList());
        List<List<String>> listList = Lists.partition(distinctUniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        List<CrmVolunteerPreposeMaterialCaseCountModel> caseCountModels = listList.parallelStream()
                .map(list -> cfCrmVolunteerPreposeMaterialCountDao.getCountByParam(dateTimes, list))
                .reduce((total, item) -> {
                            total.addAll(item);
                            return total;
                        }
                )
                .get();
        return caseCountModels;
    }

    @Override
    public CfCrmVolunteerPreposeMaterialCountDO getCfCrmVolunteerPreposeMaterialCountDOByParam(String dateTime, String volunteerUniqueCode) {
        return cfCrmVolunteerPreposeMaterialCountDao.getCfCrmVolunteerPreposeMaterialCountDOByParam(dateTime,volunteerUniqueCode);
    }

    @Override
    public void batchInsert(List<CfCrmVolunteerPreposeMaterialCountDO> needInsertList) {
        cfCrmVolunteerPreposeMaterialCountDao.batchInsert(needInsertList);
    }

    @Override
    public void batchUpdateCaseAmount(List<CfCrmVolunteerPreposeMaterialCountDO> needUpdateList) {
        cfCrmVolunteerPreposeMaterialCountDao.batchUpdateCaseAmount(needUpdateList);
    }

    @Override
    public void handlePreposeMaterialCount(CrowdfundingVolunteer crowdfundingVolunteer) {
        if (crowdfundingVolunteer==null){
            return;
        }
        executorService.submit(() ->{
            CfCrmVolunteerPreposeMaterialCountDO crmVolunteerPreposeMaterialCount = getCrmVolunteerPreposeMaterialCount(crowdfundingVolunteer);
            if (crmVolunteerPreposeMaterialCount==null){
                return;
            }
            CfCrmVolunteerPreposeMaterialCountDO modelFromDB = getCfCrmVolunteerPreposeMaterialCountDOByParam(DateUtil.getCurrentDateStr(), crowdfundingVolunteer.getUniqueCode());
            if (modelFromDB==null){
                this.batchInsert(Lists.newArrayList(crmVolunteerPreposeMaterialCount));
            }else {
                crmVolunteerPreposeMaterialCount.setId(modelFromDB.getId());
                this.batchUpdateCaseAmount(Lists.newArrayList(crmVolunteerPreposeMaterialCount));
            }
        });

    }

    /**
     * 查询 线下筹款顾问 当天发起案例以及案例中已报备的数量
     * @param crowdfundingVolunteer
     * @return
     */

    private CfCrmVolunteerPreposeMaterialCountDO getCrmVolunteerPreposeMaterialCount(CrowdfundingVolunteer crowdfundingVolunteer) {
        if (crowdfundingVolunteer==null){
            return null;
        }
        String startTimeStr = DateUtil.getCurrentDateStr();
        //  查询当天发起的案例
        Map<String, List<Long>> uniqueCodeMapInfoIds = cfBdCaseInfoService.getUniqueCodeMapInfoIdsForOneDay(startTimeStr, Lists.newArrayList(crowdfundingVolunteer.getUniqueCode()));
        return buildCfCrmVolunteerPreposeMaterialCountDO(startTimeStr,crowdfundingVolunteer,uniqueCodeMapInfoIds);
    }

    @Override
    public CfCrmVolunteerPreposeMaterialCountDO buildCfCrmVolunteerPreposeMaterialCountDO(String startTimeStr, CrowdfundingVolunteer crowdfundingVolunteer, Map<String, List<Long>> uniqueCodeMapInfoIds){
        CfCrmVolunteerPreposeMaterialCountDO model = new CfCrmVolunteerPreposeMaterialCountDO();
        List<Long> infoIds = uniqueCodeMapInfoIds.get(crowdfundingVolunteer.getUniqueCode());
        if (CollectionUtils.isNotEmpty(infoIds)){
            //  查询当天发起的案例中 已报备的数量
            Date before7Day = DateUtils.addDays(DateUtil.parseDate(startTimeStr), -6);
            Date after7Day = DateUtils.addDays(DateUtil.parseDate(startTimeStr), +6);
            List<Long> reportedInfoIds = clewCrowdfundingReportRelationDao.getReportedInfoIdsByInfoIds(crowdfundingVolunteer.getUniqueCode(),infoIds,before7Day,after7Day);
            model.setPreposeMaterialCaseAmount(reportedInfoIds.size());
            model.setReportedInfoIds(org.apache.commons.lang.StringUtils.join(reportedInfoIds,','));
        }
        model.setCaseAmount(CollectionUtils.isNotEmpty(infoIds)?infoIds.size():0);
        model.setDateTime(startTimeStr);
        model.setVolunteerUniqueCode(crowdfundingVolunteer.getUniqueCode());
        model.setMis(crowdfundingVolunteer.getMis());
        return model;
    }
}
