package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdLoginExceptionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdLoginExceptionService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrgConvertService;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdLoginExceptionDao;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2020/12/2 5:10 下午
 */
@Service
public class CfBdLoginExceptionService implements ICfBdLoginExceptionService {
    @Autowired
    private CfBdLoginExceptionDao cfBdLoginExceptionDao;
    @Autowired
    private ICrmOrgConvertService crmOrgConvertService;


    @Override
    public void saveCfBdLoginException(CrowdfundingVolunteer crowdfundingVolunteer,String loginDeviceId, String exceptionDesc) {
        List<BdCrmVolunteerOrgnizationSimpleModel> simpleModelList = crmOrgConvertService.getByUniqueCodeList(Lists.newArrayList(crowdfundingVolunteer.getUniqueCode()));

        CfBdLoginExceptionDO cfBdLoginExceptionDO = new CfBdLoginExceptionDO(crowdfundingVolunteer.getMis(),
                crowdfundingVolunteer.getVolunteerName(),
                CollectionUtils.isNotEmpty(simpleModelList)?simpleModelList.get(0).getOrgName():"",
                CollectionUtils.isNotEmpty(simpleModelList)?simpleModelList.get(0).getOrgId():0L,
                new Date(),
                loginDeviceId,
                exceptionDesc);
        cfBdLoginExceptionDao.insert(cfBdLoginExceptionDO);
    }
}
