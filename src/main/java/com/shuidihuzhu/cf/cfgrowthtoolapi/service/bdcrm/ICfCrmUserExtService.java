package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmUserExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.BdHomePageVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CrmUserExtModel;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-02-05 16:11
 */
public interface ICfCrmUserExtService {
    int saveCfCrmUserExtDO(CfCrmUserExtDO cfCrmUserExtDO);

    int updateCfCrmUserExtDO(CfCrmUserExtDO cfCrmUserExtDO);

	int updateByIdForBackDoor(CfCrmUserExtDO cfCrmUserExtDO);

	CrmUserExtModel getCrmUserExtModelByUniqueCode(String uniqueCode);

	CfCrmUserExtDO getCfCrmUserExtDOByUniqueCode(String uniqueCode);

    List<CfCrmUserExtDO> getCrmUserExtModelByUniqueCodes(List<String> uniqueCodes);

	CfCrmUserExtDO getCfCrmUserExtDOByWeixincodeOrEncryptPhoneC(String str);

	int updateIsDelete(String uniqueCode, int isDelete);

	int updateHeadUrl(String uniqueCode, String headUrl);

	int updateWeixincode(String uniqueCode, String weixincode);

	int updateQrCode(String uniqueCode, String qrCode);

	int updateQyWechatQrCode(String uniqueCode, String qrCode);

    int updateEncryptPhoneC(String volunteerUniqueCode, String mobile);

    List<String> getAll();

    List<CfCrmUserExtDO> getAllByPage(Long currentId,Integer size);

    void updateLabels(String uniqueCode, String labels);

    void updateSceneServiceHospitals(String uniqueCode, String sceneServiceHospital);

    void updateWorkCardUrl(String uniqueCode, String workCardUrl);

    void updateCaseIds(String uniqueCode, String caseIds);

    void updateAchievement(String uniqueCode, String achievementTypes);
}
