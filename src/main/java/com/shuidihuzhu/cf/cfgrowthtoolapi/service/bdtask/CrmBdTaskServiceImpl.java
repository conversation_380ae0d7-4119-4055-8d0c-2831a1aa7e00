package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.TaskStatDetail;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.bdtask.CrmBdTaskParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.bdtask.CrmManagerBaseParam;
import com.shuidihuzhu.cf.dao.bdtask.CrmBdTaskDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 顾问任务信息(CrmBdTask)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-20 11:05:28
 */
@Service("crmBdTaskService")
public class CrmBdTaskServiceImpl implements ICrmBdTaskService {

    @Resource
    private CrmBdTaskDao crmBdTaskDao;

    @Override
    public CrmBdTaskDO queryById(long id) {
        return crmBdTaskDao.queryById(id);
    }


    @Override
    public int insert(CrmBdTaskDO crmBdTask) {
        return crmBdTaskDao.insert(crmBdTask);
    }

    @Override
    public int update(CrmBdTaskDO crmBdTask) {
        return crmBdTaskDao.update(crmBdTask);
    }

    @Override
    public CrmBdTaskDO getByTaskTypeAndCaseId(int taskType, long caseId) {
        return crmBdTaskDao.getByTaskTypeAndCaseId(taskType, caseId);
    }

    @Override
    public void updateWhenOverTime(long id) {
        crmBdTaskDao.updateWhenOverTime(id);
    }

    @Override
    public void updateWhenComplete(long id) {
        crmBdTaskDao.updateWhenComplete(id);
    }

    @Override
    public List<CrmBdTaskDO> listByCaseIds(List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }
        return crmBdTaskDao.listByCaseIds(caseIds);
    }

    @Override
    public List<TaskStatDetail> getMemberOrOrgModel(CrmManagerBaseParam crmManagerBaseParam) {
        if (crmManagerBaseParam == null || CollectionUtils.isEmpty(crmManagerBaseParam.getSingleSubOrgList())) {
            return Lists.newArrayList();
        }
        return crmBdTaskDao.getMemberOrOrgModel(crmManagerBaseParam);
    }

    @Override
    public List<TaskStatDetail> groupByUniqueCode(CrmManagerBaseParam crmManagerBaseParam) {
        if (crmManagerBaseParam == null || crmManagerBaseParam.getSingleOrgId() <= 0) {
            return Lists.newArrayList();
        }
        return crmBdTaskDao.groupByUniqueCode(crmManagerBaseParam);
    }

    @Override
    public List<CrmBdTaskDO> pageTaskList(CrmBdTaskParam crmBdTaskParam) {
        return crmBdTaskDao.pageTaskList(crmBdTaskParam);
    }

    @Override
    public int countTaskList(CrmBdTaskParam crmBdTaskParam) {
        return Optional.ofNullable(crmBdTaskDao.countTaskList(crmBdTaskParam)).orElse(0);
    }

    @Override
    public boolean isExistTask(String uniqueCode) {
        return countUnFinishTaskByCaseId(uniqueCode) > 0;
    }

    @Override
    public int countUnFinishTaskByCaseId(String uniqueCode) {
        return Optional.ofNullable(crmBdTaskDao.countUnFinishByUniqueCode(uniqueCode)).orElse(0) ;
    }
}
