package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdDailyObjectiveTargetDO;

import java.util.List;

/**
 * 组织的目标项和目标值(BdDailyObjectiveTarget)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-02 15:00:08
 */
public interface BdDailyObjectiveTargetService {

    BdDailyObjectiveTargetDO queryById(long id);

    int batchInsert(List<BdDailyObjectiveTargetDO> targetDOList);

    int batchUpdate(List<BdDailyObjectiveTargetDO> targetDOList);

    boolean deleteById(long id);

    List<BdDailyObjectiveTargetDO> listByOrgIds(String dateKey, List<Long> orgIds);

}
