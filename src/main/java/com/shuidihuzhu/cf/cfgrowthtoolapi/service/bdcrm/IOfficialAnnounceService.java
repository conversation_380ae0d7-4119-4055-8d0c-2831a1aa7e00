package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdcrmOfficialAnnounceDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdcrmOfficialAnnountStatisticModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdcrmOfficialAnnounceSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.*;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2020/12/25 上午10:43
 */
public interface IOfficialAnnounceService {
    void saveOfficialAnnounce(CfBdcrmOfficialAnnounceDO cfBdcrmOfficialAnnounceDO);

    void updateOfficialAnnounce(CfBdcrmOfficialAnnounceDO cfBdcrmOfficialAnnounceDO);

    long getListCountForSea(CfBdcrmOfficialAnnounceSearchModel searchModel);

    List<CfSeaOfficialAnnounceVO> getListForSea(CfBdcrmOfficialAnnounceSearchModel searchModel);

    OpResult withdrawSendMsg(Long officialAnnounceId);

    void delOfficialAnnounce(Long officialAnnounceId);

    /**
     * 发送官宣消息 给顾问
     * @param officialAnnounceId 官宣id
     * @param simpleModelList 要发送的人员名单
     * @param misMapVolunteer 用来查询 uniqueCode
     * @param operatorId
     * @param operatorName
     */
    void sendOfficialAnnounceMsg2Gw(Long officialAnnounceId, List<BdCrmVolunteerOrgnizationSimpleModel> simpleModelList, Map<String, CrowdfundingVolunteer> misMapVolunteer, long operatorId, String operatorName);

    long getSendRecordsCount(Long officialAnnounceId, int readStatus);

    List<CfBdcrmOfficialAnnounceSendRecordVO> getSendRecords(Long officialAnnounceId, int readStatus, int pageNo, int pageSize);

    BdcrmOfficialAnnountStatisticModel getSendStatistic(Long officialAnnounceId);

    long getUnReadNum(CrowdfundingVolunteer cfVolunteer);

    void saveKnowledgeComment(Long knowledgeId, Long commentId, String comment, CrowdfundingVolunteer cfVolunteer);

    OpResult checkPermissionToComment(Long knowledgeId, CrowdfundingVolunteer cfVolunteer);

    long getMsgListCount(Integer readStatus, CrowdfundingVolunteer cfVolunteer);

    List<Long> getUnReadOfficialAnnounceId(String uniqueCode);

    CfBdcrmOfficialAnnounceVO getLastNoPicAnnounce(List<Long> announceIds);

    List<CfBdcrmOfficialAnnounceVO> getMsgList(Integer readStatus, CrowdfundingVolunteer cfVolunteer, int pageNo, int pageSize);

    List<CfBdcrmKnowledgeCommentVO> getKnowledgeCommentList(Long knowledgeId, int type, String uniqueCode, int pageNo, int pageSize);

    long querySubCommentByCommentIdCount(long commentId);

    List<CfBdcrmKnowledgeCommentVO> querySubCommentByCommentId(long commentId, int pageNo, int pageSize);

    long getKnowledgeCommentListCount(Long knowledgeId);

    OpResult likeKnowledgeComment(Long knowledgeId, Long commentId, int type, CrowdfundingVolunteer cfVolunteer);

    void readOfficialAnnounce(String uniqueCode,List<Long> officialAnnounceIds);

    void updateWorkStatus(String uniqueCode, int workStatus);

    CfBdcrmOfficialAnnounceDO getOfficialAnnounceById(Long id);

    List<Long> getOfficialAnnounceIdOfImage(List<Long> unReadOfficialAnnounceId);

    List<CfBdcrmOfficialAnnounceVO> listReadForHomePageScroll(String uniqueCode, int offset, int limit);
}
