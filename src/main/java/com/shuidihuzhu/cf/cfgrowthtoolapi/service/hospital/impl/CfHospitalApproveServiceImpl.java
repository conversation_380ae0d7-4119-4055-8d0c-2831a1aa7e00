package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.impl;

import com.shuidihuzhu.cf.dao.hospital.CfHospitalApproveDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfHospitalApproveDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.CfHospitalApproveService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 医院纠错审核表(CfHospitalApprove)表服务实现类
 *
 * <AUTHOR>
 * @since 2020-11-09 15:45:37
 */
@Service("cfHospitalApproveService")
public class CfHospitalApproveServiceImpl implements CfHospitalApproveService {

    @Resource
    private CfHospitalApproveDao cfHospitalApproveDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public CfHospitalApproveDo queryById(long id) {
        return this.cfHospitalApproveDao.queryById(id);
    }

    @Override
    public List<CfHospitalApproveDo> listByApplyId(int applyId) {
        return cfHospitalApproveDao.listByApplyId(applyId);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    @Override
    public List<CfHospitalApproveDo> queryAllByLimit(int offset, int limit) {
        return this.cfHospitalApproveDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param cfHospitalApproveDo 实例对象
     * @return 实例对象
     */
    @Override
    public CfHospitalApproveDo insert(CfHospitalApproveDo cfHospitalApproveDo) {
        this.cfHospitalApproveDao.insert(cfHospitalApproveDo);
        return cfHospitalApproveDo;
    }

    /**
     * 修改数据
     *
     * @param cfHospitalApproveDo 实例对象
     * @return 实例对象
     */
    @Override
    public CfHospitalApproveDo update(CfHospitalApproveDo cfHospitalApproveDo) {
        this.cfHospitalApproveDao.update(cfHospitalApproveDo);
        return this.queryById(cfHospitalApproveDo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return this.cfHospitalApproveDao.deleteById(id) > 0;
    }
}