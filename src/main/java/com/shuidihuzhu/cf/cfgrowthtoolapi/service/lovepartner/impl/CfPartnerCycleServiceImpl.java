package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerCycleDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerCycleService;
import com.shuidihuzhu.cf.dao.lovepartner.CfPartnerCycleDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */

@Service
@Slf4j
public class CfPartnerCycleServiceImpl implements CfPartnerCycleService {

    @Autowired
    private CfPartnerCycleDao partnerCycleDao;

    @Override
    public int insertPartnerCycle(CfPartnerCycleDo partnerCycleDo) {
        return partnerCycleDao.insert(partnerCycleDo);
    }

    @Override
    public CfPartnerCycleDo getPartnerCycleByQueryTime(Date queryTime) {
        return partnerCycleDao.getPartnerCycleByQueryTime(queryTime);
    }

    @Override
    public CfPartnerCycleDo getByCycleId(int cycleId) {
        return partnerCycleDao.getById(cycleId);
    }


    @Override
    public List<CfPartnerCycleDo> getCycleList() {
        return partnerCycleDao.getCycleList();
    }

}
