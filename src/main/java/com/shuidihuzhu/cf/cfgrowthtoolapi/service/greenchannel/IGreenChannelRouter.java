package com.shuidihuzhu.cf.cfgrowthtoolapi.service.greenchannel;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.greenchannel.CfGreenChannelApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGreenChannelEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.greenchannel.StepHandlerModel;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-10-27 12:08 下午
 **/
public interface IGreenChannelRouter {

    List<StepHandlerModel> listAllHandler(int type);

    /**
     * 获取当前审核环节
     */
    CfGreenChannelApproveDO getCurrentStepCode(List<CfGreenChannelApproveDO> approveDOList);

    /**
     * 判断当前环节能否被打回上一级
     */
    boolean canBackToLastStep(List<CfGreenChannelApproveDO> approveDOList, int currentStepCode);


    /**
     * 获取下一步的code码
     */
    Integer getNextStepCode(int activityType, int stepCode);

    /**
     * 获取上一步的stepCode
     */
    Integer getLastStepCode(int activityType, int stepCode);

    /**
     * 生成新的审核时
     */
    CfGreenChannelEnums.ApplyStatusEnum getApplyStatusWhenCreate(int activityType, int stepCode);

    /**
     * 驳回时
     */
    CfGreenChannelEnums.ApplyStatusEnum getApplyStatusWhenReject(int activityType, int stepCode);


    /**
     * 返回上一步对应的状态
     * stepCode : 上一步的code
     */
    CfGreenChannelEnums.ApplyStatusEnum getApplyStatusWhenBackToLast(int activityType, int stepCode);

    /**
     * 获取有编辑权限的人
     * @param activityType
     * @param stepCode
     * @return
     */
    List<String> listOperator(int activityType, int stepCode);

    /**
     * 获取发送机器人
     * @param activityType
     * @return
     */
    String getRobot(int activityType);

}
