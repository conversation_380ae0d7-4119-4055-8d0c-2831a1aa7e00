package com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.wx.CfWxOfficialAccountApplyInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WxOfficialAccountEnums;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-03-29
 */
public interface IWxOfficialAccountApplyInfoService {


    /**
     * 新增申请
     * @param cfWxOfficialAccountApplyInfoDO
     * @return
     */
    int insertOrUpdate(CfWxOfficialAccountApplyInfoDO cfWxOfficialAccountApplyInfoDO);

    /**
     * 更新applyStatus和url
     * @param applyStatusEnum
     * @param articleUrl
     * @param id
     * @return
     */
    int updateApplyStatusAndAriticalUrlById(WxOfficialAccountEnums.ApplyStatusEnum applyStatusEnum, String articleUrl, Long id);

    /**
     * 更新申请信息
     * @param cfWxOfficialAccountApplyInfoDO
     * @return
     */
    int updateApplyInfoSucc(CfWxOfficialAccountApplyInfoDO cfWxOfficialAccountApplyInfoDO);

    /**
     * 记录失败原因
     * @param applyStatusEnum
     * @param applyFailReasonEnum
     * @param id
     * @return
     */
    int updateApplyFailById(WxOfficialAccountEnums.ApplyStatusEnum applyStatusEnum, WxOfficialAccountEnums.ApplyFailReasonEnum applyFailReasonEnum, Long id);

    /**
     * 获取申请信息
     * @param msgId
     * @param thirdType
     * @return
     */
    List<CfWxOfficialAccountApplyInfoDO> listWxOfficialAccountApplyByMsgIdAndThirdType(Long msgId, int thirdType);

    /**
     * 根据infoUuid获取信息
     * @param infoUuid
     * @return
     */
    CfWxOfficialAccountApplyInfoDO getWxApplyInfoByInfoUuid(String infoUuid);

    /**
     * 校验是否可以申请wx链接
     * @param infoUuid
     * @return
     */
    OpResult<Void> checkIsCanApplyWxUrl(String infoUuid);

    /**
     * 更新删除素材状态
     * @param materialDeleteStatusEnum
     * @param id
     * @return
     */
    int updateMaterialStatusDelete(WxOfficialAccountEnums.MaterialDeleteStatusEnum materialDeleteStatusEnum, Long id);

    /**
     * 获取待删除素材数据
     * @param materialDeleteStatus
     * @param startTime
     * @param endTime
     * @return
     */
    List<CfWxOfficialAccountApplyInfoDO> listWxOfficialByMaterialAndTime(Integer materialDeleteStatus, Date startTime, Date endTime);
}
