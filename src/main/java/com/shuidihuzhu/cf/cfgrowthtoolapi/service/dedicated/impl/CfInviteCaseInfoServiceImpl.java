package com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.impl;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.*;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.AccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.VerifyCodeClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.shorturl.ShortUrlDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfInviteCaseInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRecommendStatusModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfToufangInviteService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfCrowdfundingInviteAwardBiz;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfInviteCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.CosUploadUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.excel.ExcelUtil;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import com.shuidihuzhu.cf.client.feign.CfFirstApproveFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.dao.dedicated.CfInviteCaseInfoDao;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInviteCaseRelationDO;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.growthtool.enums.InvitorDrawStatus;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-02-10
 */
@Service
@Slf4j
@RefreshScope
public class CfInviteCaseInfoServiceImpl implements ICfInviteCaseInfoService {

    @Autowired
    private CfInviteCaseInfoDao cfInviteCaseInfoDao;
    @Autowired
    private VerifyCodeClientDelegate verifyCodeClientDelegate;
    @Autowired
    private ICfToufangInviteService cfToufangInviteServiceImpl;
    @Autowired
    private ShortUrlDelegate shortUrlDelegate;
    @Resource
    private AlarmClient alarmClient;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private ApplicationService applicationService;
    @Resource
    private AccountServiceDelegate accountServiceDelegate;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private CfFinanceCapitalAccountFeignClient financeFeignClient;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private CfFirstApproveFeignClient cfFirstApproveFeignClient;

    @Value("${raiser.recommend.end.time:2020-05-18}")
    private String raiserRecommendEndTime;

    @Autowired
    private ICfCrowdfundingInviteAwardBiz cfInviteAwardBiz;

    @Autowired
    private ICrowdFundingFeignDelegate cfFeignClientDelegate;

    @Autowired
    private MaskUtil maskUtil;


    private static List<Integer> needIgnoreCaseIds = Lists.newArrayList(
            2351029,2354166,2352766,2353874,2352394,2352553,2351030,2347946,2348813,2348819,2353136,2356225,2350087,2347432,
            2349110,2348682,2348510,2349193,2348253,2348112,2349054,2348191,2350187,2348585,2348324,2349055,2353081,2349810,2350932,2351813,2352531,
            2349097,2350888,2349982,2352593,2354714,2354979,2355060,2356450,2354985,2355731,2356270,2348691,2351724,2351689,2352261,2352083,2348678,
            2349922,2350705,2356034,2354868,2358690,2357683,2356929,2356072,2357974,2357281,2357004,2356235,2359763,2360501,2360504,2359156,2361365,
            2361227,2367423,2367295,2366800,2365763,2369697,2369572,2368943,2362178,2368607,2362897,2369019,2362178,2351347,2369786,2370937,2369961
    );

    @Override
    public OpResult<Boolean> checkFilled(long userId, String infoUuid) {
        if (StringUtils.isEmpty(infoUuid)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfInviteCaseInfoDO cfInviteCaseInfoDO = cfInviteCaseInfoDao.getCfInviteCaseInfoDOByUserIdAndInfoId(userId,infoUuid);
        if (cfInviteCaseInfoDO != null){
            return OpResult.createSucResult(true);
        }else{
            return OpResult.createSucResult(false);
        }
    }

    @Override
    public OpResult<Void> fillIfo(long userId, String mobile, String verifyCode, String idCard, String name, String infoUuid, String clientIp) {
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(verifyCode) || StringUtils.isEmpty(idCard) || StringUtils.isEmpty(name) || StringUtils.isEmpty(infoUuid)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        //校验验证码
        OpResult<Void> verifyCodeOpResult = verifyCodeClientDelegate.strictCheckVerifyCodeIsSuccess(mobile,verifyCode,userId,clientIp);
        if (verifyCodeOpResult.isFail()){
            return OpResult.createFailResult(verifyCodeOpResult.getErrorCode());
        }
        //检验是否是合格案例
        CfToufangInviteCaseRelationDO cfToufangInviteCaseRelationDO = cfToufangInviteServiceImpl.getInviteCaseRelationByUserIdAndInfoId(userId,infoUuid);
        if (cfToufangInviteCaseRelationDO == null){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        //检验是否是已存在该邀请提现信息
        CfInviteCaseInfoDO cfInviteCaseInfoDO = cfInviteCaseInfoDao.getCfInviteCaseInfoDOByUserIdAndInfoId(userId,infoUuid);
        if (cfInviteCaseInfoDO == null){
            cfInviteCaseInfoDO = new CfInviteCaseInfoDO();
            cfInviteCaseInfoDO.setUserId(userId);
            cfInviteCaseInfoDO.setInfoUuid(infoUuid);
            cfInviteCaseInfoDO.setUserName(name);
            cfInviteCaseInfoDO.setEncryptPhone(ShuidiCipherUtils.encrypt(mobile));
            cfInviteCaseInfoDO.setEncryptIdCard(ShuidiCipherUtils.encrypt(idCard));
            cfInviteCaseInfoDO.setAmount(CfInviteCaseInfoDO.InvitorAmountType.normal_amount.getDefaultAmount());
            cfInviteCaseInfoDao.insert(cfInviteCaseInfoDO);
            return OpResult.createSucResult();
        }else{
            return OpResult.createFailResult(CfGrowthtoolErrorCode.REPORT_SHARE_LINK_INFOUUID_EXISTED);
        }
    }

    @Override
    public OpResult<Void> fillInfoAuto(long userId) {
        //校验是否存在已满足的推荐案例（给推荐人发过消息且满足取款）
        Optional<CfToufangInviteCaseRelationDO> firstSatisfy = cfToufangInviteServiceImpl.getInviteCaseRelation(userId)
                .stream()
                .filter(relationInfo -> relationInfo.getCompleteFlag().equals(1) && relationInfo.getInvitorType() == CfToufangInviteCaseRelationDO.InvitorTypeEnum.raiser.getCode())
                .min(Ordering.natural().onResultOf(CfToufangInviteCaseRelationDO::getCreateTime));
        if (!firstSatisfy.isPresent()){
            log.warn("推荐未满足条件,userId:{}", userId);
            return OpResult.createFailResult(CfGrowthtoolErrorCode.REPORT_SHARE_CONDITION_NO_SATISFY);
        }

        //获取用户信息
        UserInfoModel userInfoModel = accountServiceDelegate.getUserInfoModelByUserId(userId);
        log.debug("获取用户信息userInfoModel:{}", JSON.toJSONString(userInfoModel));
        //检验是否是已存在该邀请提现信息
        CfInviteCaseInfoDO cfInviteCaseInfoDO = cfInviteCaseInfoDao.getCfInviteCaseInfoDOByUserIdAndInfoId(userId, firstSatisfy.get().getInfoUuid());
        if (cfInviteCaseInfoDO == null){
            cfInviteCaseInfoDO = new CfInviteCaseInfoDO();
            cfInviteCaseInfoDO.setUserId(userId);
            cfInviteCaseInfoDO.setInfoUuid(firstSatisfy.get().getInfoUuid());
            cfInviteCaseInfoDO.setUserName(Optional.ofNullable(userInfoModel).map(UserInfoModel::getRealName).orElse(""));
            cfInviteCaseInfoDO.setEncryptPhone(Optional.ofNullable(userInfoModel).map(UserInfoModel::getCryptoMobile).orElse(""));
            cfInviteCaseInfoDO.setEncryptIdCard(Optional.ofNullable(userInfoModel).map(UserInfoModel::getCryptoIdCard).orElse(""));
            int calRecommendAmount = calRecommendAmount(firstSatisfy.get().getInfoUuid());
            cfInviteCaseInfoDO.setAmount(calRecommendAmount);
            cfInviteCaseInfoDO.setInvitorCaseId(firstSatisfy.get().getInvitorCaseId());
            cfInviteCaseInfoDao.insert(cfInviteCaseInfoDO);
            return OpResult.createSucResult();
        }else{
            return OpResult.createFailResult(CfGrowthtoolErrorCode.REPORT_SHARE_LINK_INFOUUID_EXISTED);
        }
    }

    @Override
    public OpResult<CfRecommendStatusModel> showRecommendStatus(long userId) {
        CfRecommendStatusModel statusModel = new CfRecommendStatusModel();
        if (StringUtils.isBlank(raiserRecommendEndTime)) {
            statusModel.setRecommendAvailable(false);
            return OpResult.createSucResult();
        }
        DateTime validTime = DateTime.parse(raiserRecommendEndTime, DateTimeFormat.forPattern("yyyy-MM-dd"));
        if (validTime.isBefore(DateTime.now())) {
            statusModel.setRecommendAvailable(false);
            return OpResult.createSucResult();
        }
        //获取推荐人所有的案例
        List<CrowdfundingInfo> cfList = Optional.ofNullable(crowdfundingFeignClient.getCrowdfundingListByUserId(userId))
                .map(FeignResponse::getData)
                .orElse(Lists.newArrayList());

        Map<Integer, CfInfoExt> caseIdTExt = cfList.stream().map(cfInfo -> Optional.ofNullable(crowdfundingFeignClient.getCfInfoExtByCaseId(cfInfo.getId())).map(FeignResponse::getData).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(CfInfoExt::getCaseId, Function.identity(), (before, after) -> before));

        //不符合推荐条件
        boolean notMatchRecommend = cfList.stream().anyMatch(cfInfo -> {
            if (needIgnoreCaseIds.contains(cfInfo.getId())) {
                return true;
            }
            String dbRaiserChannel = Optional.ofNullable(caseIdTExt.get(cfInfo.getId()))
                    .map(CfInfoExt::getPrimaryChannel)
                    .orElse("");
            return dbRaiserChannel.equals("BD");
        });

        if (notMatchRecommend) {
            log.debug("userId:{}不符合推荐人条件", userId);
            statusModel.setRecommendAvailable(false);
            return OpResult.createSucResult();
        }

        boolean isMatchRecommend = cfList.stream().anyMatch(cfInfo -> {
            Integer firstApproveStatus = Optional.ofNullable(caseIdTExt.get(cfInfo.getId()))
                    .map(CfInfoExt::getFirstApproveStatus)
                    .orElse(FirstApproveStatusEnum.DEFAULT.getCode());
            //非db发起的在筹案例
            return firstApproveStatus.equals(FirstApproveStatusEnum.APPLY_SUCCESS.getCode());
        });

        if (!isMatchRecommend) {
            log.debug("userId:{}不符合推荐人条件", userId);
            statusModel.setRecommendAvailable(false);
            return OpResult.createSucResult();
        }

        statusModel.setRecommendAvailable(true);
        //实时去更新下对应的cf_toufang_invite_case_relation 中的状态
        cfInviteAwardBiz.realTimeChangeRelationStatus(userId);
        //校验是否存在已满足的推荐案例
        Optional<CfToufangInviteCaseRelationDO> firstSatisfy = cfToufangInviteServiceImpl.getInviteCaseRelation(userId)
                .stream()
                .filter(relationInfo -> relationInfo.getCompleteFlag().equals(1) && relationInfo.getInvitorType() == CfToufangInviteCaseRelationDO.InvitorTypeEnum.raiser.getCode())
                .min(Ordering.natural().onResultOf(CfToufangInviteCaseRelationDO::getCreateTime));
        if (!firstSatisfy.isPresent()){
            log.debug("showRecommendStatus推荐未满足条件,userId:{}", userId);
            statusModel.setRecommendStatus(CfRecommendStatusModel.RecommendStatusEnum.no_recommend.getCode());
            statusModel.setRecommendAmount(CfInviteCaseInfoDO.InvitorAmountType.raiser_amount.getDefaultAmount());
            return OpResult.createSucResult(statusModel);
        }
        //计算已筹金额
        int recommendAmount = calRecommendAmount(firstSatisfy.get().getInfoUuid());

        //查看是否录入信息
        CfInviteCaseInfoDO inviteCaseInfoDO = cfInviteCaseInfoDao.getCfInviteCaseInfoDOByUserIdAndInfoId(userId, firstSatisfy.get().getInfoUuid());
        if (inviteCaseInfoDO == null) {
            log.debug("未录入信息,userId:{},firstSatisfy:{}", userId, JSON.toJSONString(firstSatisfy.get()));
            statusModel.setRecommendStatus(CfRecommendStatusModel.RecommendStatusEnum.has_recommend.getCode());
            statusModel.setRecommendAmount(recommendAmount);
            return OpResult.createSucResult(statusModel);
        }

        statusModel.setRecommendStatus(CfRecommendStatusModel.RecommendStatusEnum.finish_recommend.getCode());
        statusModel.setRecommendAmount(inviteCaseInfoDO.getAmount());
        return OpResult.createSucResult(statusModel);
    }

    //需求发生变化,pm 确定使用固定金额
    private int calRecommendAmount(String infoId) {
        //if (useDefaultCalInvitorAmount) {
        //    FeignResponse<CfCapitalAccount> capitalAccountFeignResponse = financeFeignClient.capitalAccountGetByInfoUuid(infoId);
        //    if (capitalAccountFeignResponse.notOk()) {
        //        log.warn("调用资金接口异常,infoId:{}", infoId);
        //        return 0;
        //    }
        //    int canObtainAmount = (int) ((Optional.ofNullable(capitalAccountFeignResponse.getData()).map(CfCapitalAccount::getDonationAmountInFen).orElse(0)) * 0.0006);
        //    return Math.min(canObtainAmount, CfInviteCaseInfoDO.InvitorAmountType.raiser_amount.getMaxAmount());
        //}
        return CfInviteCaseInfoDO.InvitorAmountType.raiser_amount.getDefaultAmount();
    }

    @Override
    public void doExecute(Date startTime, Date endTime) {
        List<CfInviteCaseInfoDO> resultList = Lists.newArrayList();
        int offset = 0;
        int pageSize = 100;
        do{
            List<CfInviteCaseInfoDO> tmpList = cfInviteCaseInfoDao.getCfInviteCaseInfoDOByCreateTime(startTime,endTime,offset,pageSize);
            if(CollectionUtils.isEmpty(tmpList) || tmpList.size()<pageSize){
                if(tmpList.size()>0){
                    resultList.addAll(tmpList);
                }
                break;
            }
            resultList.addAll(tmpList);
            offset+=pageSize;
        }while(true);
        if (CollectionUtils.isEmpty(resultList)){
            String content = "病友社群推荐项目提现报表:今日暂时无人提交提现信息";
            if (applicationService.isProduction()){
                alarmClient.sendByUser(Lists.newArrayList("zhouxuan","liuhonghao","minfanfan","lichengjin"), content);
            }else{
                alarmClient.sendByUser(Lists.newArrayList("minfanfan","lichengjin","cairuifang","chenwanxia"), content);
            }
            return;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        String[] headers = {"填写日期","被推荐人案例id","推荐人user_id","推荐人手机号","推荐人姓名","推荐人身份证号","推荐人应获金额"};
        List<Map<String,Object>> collections = Lists.newArrayList();
        List<Map<String, Object>> dataForQaTest = Lists.newArrayList();
        for(CfInviteCaseInfoDO cfInviteCaseInfoDO: resultList){
            Map<String,Object> line = Maps.newHashMap();
            line.put(headers[0], DateUtil.getDate2Str("yyyy-MM-dd HH:mm:ss",cfInviteCaseInfoDO.getCreateTime()));
            line.put(headers[1], cfInviteCaseInfoDO.getInfoUuid());
            line.put(headers[2], cfInviteCaseInfoDO.getUserId());
            line.put(headers[3], shuidiCipher.decrypt(cfInviteCaseInfoDO.getEncryptPhone()));
            line.put(headers[4], cfInviteCaseInfoDO.getUserName());
            line.put(headers[5], shuidiCipher.decrypt(cfInviteCaseInfoDO.getEncryptIdCard()));
            line.put(headers[6], cfInviteCaseInfoDO.getAmount() == 0 ? CfInviteCaseInfoDO.InvitorAmountType.normal_amount.getDefaultAmount() : cfInviteCaseInfoDO.getAmount());
            boolean isQaUserId = cfInviteAwardBiz.getQaUserIds().contains(cfInviteCaseInfoDO.getUserId());
            if (isQaUserId) {
                dataForQaTest.add(line);
            } else {
                collections.add(line);
            }
        }
        //真实数据
        if (CollectionUtils.isNotEmpty(collections)) {
            ExcelUtil.exportExcel(headers,collections,out);
            String fileName = "病友社群推荐项目提现报表"+DateUtil.getDate2Str("yyyyMMddHH",startTime)+"-"+DateUtil.getDate2Str("yyyyMMddHH",endTime)+".xls";
            String url = CosUploadUtil.uploadCosFile(fileName,out);
            log.info("CosUploadUtil.uploadCosFile url:{}",url);
            if(StringUtils.isNotBlank(url)) {
                String content = "病友社群推荐项目提现报表:"+shortUrlDelegate.process(url);
                if (applicationService.isProduction()){
                    alarmClient.sendByUser(Lists.newArrayList("zhouxuan","liuhonghao","minfanfan","lichengjin","zhangqianqian","fengxuan"), content);
                }else{
                    alarmClient.sendByUser(Lists.newArrayList("minfanfan","lichengjin","cairuifang","chenwanxia"), content);
                }
            }
        }
        //测试数据
        if (CollectionUtils.isNotEmpty(dataForQaTest)) {
            ByteArrayOutputStream outForTest = new ByteArrayOutputStream();
            ExcelUtil.exportExcel(headers, dataForQaTest, outForTest);
            String fileNameForTest = "(测试使用)病友社群推荐项目提现报表"+DateUtil.getDate2Str("yyyyMMddHH",startTime)+"-"+DateUtil.getDate2Str("yyyyMMddHH",endTime)+".xls";
            String urlForTest = CosUploadUtil.uploadCosFile(fileNameForTest, outForTest);
            log.info("CosUploadUtil.uploadCosFile urlForTest:{}", urlForTest);
            if(StringUtils.isNotBlank(urlForTest)) {
                String contentForTest = "(测试使用)病友社群推荐项目提现报表:"+shortUrlDelegate.process(urlForTest);
                alarmClient.sendByUser(Lists.newArrayList("cairuifang", "mawentao","zhangqianqian","fengxuan"), contentForTest);
            }
        }
    }

    //推荐人是否是筹款人
    @Override
    public Response<Boolean> isRaiserInvitor(long invitorId) {
        boolean isRaiserInvitor = cfToufangInviteServiceImpl.getInviteCaseRelation(invitorId)
                .stream()
                .anyMatch(item -> item.getInvitorType() == CfToufangInviteCaseRelationDO.InvitorTypeEnum.raiser.getCode());
        return NewResponseUtil.makeSuccess(isRaiserInvitor);
    }

    //推荐人状态
    @Override
    public Response<CfRaiserInvitorStatus> getRaiserInvitorStatus(long invitorId) {
        List<CfToufangInviteCaseRelationDO> relationDOList = cfToufangInviteServiceImpl.getInviteCaseRelation(invitorId);
        List<CfToufangInviteCaseRelationDO> raiserRecommends = relationDOList.stream()
                .filter(item -> item.getInvitorType() == CfToufangInviteCaseRelationDO.InvitorTypeEnum.raiser.getCode())
                .collect(Collectors.toList());

        //非筹款推荐人直接返回null
        if (CollectionUtils.isEmpty(raiserRecommends)) {
            return NewResponseUtil.makeSuccess(null);
        }
        CfRaiserInvitorStatus invitorStatus = new CfRaiserInvitorStatus();
        Optional<CfToufangInviteCaseRelationDO> firstSatisfy = raiserRecommends.stream()
                .filter(item -> item.getCompleteFlag().equals(1))
                .min(Ordering.natural().onResultOf(CfToufangInviteCaseRelationDO::getCreateTime));
        if (firstSatisfy.isPresent()) {
            invitorStatus.setReachStandard(true);
            CfInviteCaseInfoDO inviteCaseInfoDO = cfInviteCaseInfoDao.getCfInviteCaseInfoDOByUserIdAndInfoId(invitorId, firstSatisfy.get().getInfoUuid());
            invitorStatus.setRecommendAmount(calRecommendAmount(firstSatisfy.get().getInfoUuid()));
            if (inviteCaseInfoDO != null) {
                invitorStatus.setFinishRecommend(true);
            }
        }
        return NewResponseUtil.makeSuccess(invitorStatus);
    }

    //推荐人推荐案例列表
    @Override
    public Response<PageReturnModel<CfInvitedCaseInfo>> listInvitedCase(long invitorId, int current, int pageSize) {
        PageReturnModel<CfToufangInviteCaseRelationDO> relationPaging = cfToufangInviteServiceImpl.listRelationPaging(invitorId, current, pageSize);
        PageReturnModel<CfInvitedCaseInfo> pageReturnModel = new PageReturnModel<>();
        List<CfInvitedCaseInfo> caseInfoList = Lists.newArrayList();

        List<String> infoIds = relationPaging.getList().stream().map(CfToufangInviteCaseRelationDO::getInfoUuid).collect(Collectors.toList());
        Map<String, CrowdfundingInfo> cfMap = cfFeignClientDelegate.getMapByInfoUuIds(infoIds);
        Map<Integer, CrowdfundingAuthor> authorMap = cfFeignClientDelegate.getCrowdfundingAuthorByInfoIdList(cfMap.values().stream().map(CrowdfundingInfo::getId).collect(Collectors.toList()));
        Map<String, CfCaseCommonInfo> caseCommonInfoMap = cfFeignClientDelegate.getCaseCommonInfoByCaseIds(cfMap.values().stream().map(CrowdfundingInfo::getId).collect(Collectors.toList()));
        for (CfToufangInviteCaseRelationDO relation : relationPaging.getList()) {
            CrowdfundingInfo cfInfo = cfMap.get(relation.getInfoUuid());
            if (cfInfo == null) {
                continue;
            }
            CfInvitedCaseInfo cfInvitedCaseInfo = new CfInvitedCaseInfo();
            String raiserCryptoMobile = Optional.ofNullable(cfFeignClientDelegate.getUserInfoByUserId(cfInfo.getUserId()))
                    .map(UserInfoModel::getCryptoMobile)
                    .orElse("");
            cfInvitedCaseInfo.setCaseId(cfInfo.getId());
            cfInvitedCaseInfo.setReachRecommend(relation.getCompleteFlag() == 1);
            if (relation.getCompleteFlag() == 1) {
                cfInvitedCaseInfo.setRecommendTime(relation.getUpdateTime());
            }
            cfInvitedCaseInfo.setCreateTime(cfInfo.getCreateTime());
            Integer donateCount = cfFeignClientDelegate.getdonationCountByInfoUuId(cfInfo.getId());
            cfInvitedCaseInfo.setDonateCount(donateCount);
            cfInvitedCaseInfo.setAmount(cfInfo.getAmount() / 100);
            NumberMaskVo numberMaskVo = maskUtil.buildByEncryptPhone(raiserCryptoMobile);
            cfInvitedCaseInfo.setRaiserMobileMask(numberMaskVo);
            cfInvitedCaseInfo.setRaiserMobile(null);
            cfInvitedCaseInfo.setMaterialAuditStatus(cfInfo.getStatus().value());
            cfInvitedCaseInfo.setPatientName(Optional.ofNullable(authorMap.get(cfInfo.getId())).map(CrowdfundingAuthor::getName).orElse(""));
            boolean isFirstRaise = false;
            CfCaseCommonInfo cfCaseCommonInfo = caseCommonInfoMap.get(cfInfo.getInfoId());

            if (cfCaseCommonInfo != null && cfCaseCommonInfo.getCfFirsApproveMaterial() != null) {
                CfFirsApproveMaterial cfFirsApproveMaterial = cfCaseCommonInfo.getCfFirsApproveMaterial();
                CfFirsApproveMaterial queryParam = new CfFirsApproveMaterial();
                if (StringUtils.isNotBlank(cfFirsApproveMaterial.getPatientCryptoIdcard())) {
                    queryParam.setPatientCryptoIdcard(cfFirsApproveMaterial.getPatientCryptoIdcard());
                }
                if (StringUtils.isNotBlank(cfFirsApproveMaterial.getPatientBornCard())) {
                    queryParam.setPatientBornCard(cfFirsApproveMaterial.getPatientBornCard());
                }
                Optional<CfFirsApproveMaterial> firstApproveMaterial = Optional.ofNullable(cfFirstApproveFeignClient.getCfFirstApproveMaterialsByParam(queryParam))
                        .map(FeignResponse::getData)
                        .orElse(Lists.newArrayList())
                        .stream()
                        .filter(fam -> fam.getInfoId() > 0)
                        .min(Ordering.natural().onResultOf(CfFirsApproveMaterial::getCreateTime));
                if (firstApproveMaterial.isPresent()) {
                    isFirstRaise = firstApproveMaterial.get().getInfoId() == cfInfo.getId();
                }
            }

            cfInvitedCaseInfo.setFirstRaise(isFirstRaise);
            caseInfoList.add(cfInvitedCaseInfo);
        }
        pageReturnModel.setTotal(relationPaging.getTotal());
        pageReturnModel.setList(caseInfoList);
        return NewResponseUtil.makeSuccess(pageReturnModel);
    }

    @Override
    public Response<List<CfInvitorPaymentInfo>> getInvitorPaymentInfo(List<Integer> caseIds) {
        List<CfInvitorPaymentInfo> paymentInfoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(caseIds)) {
            return NewResponseUtil.makeSuccess(paymentInfoList);
        }
        Map<Integer, List<CfInviteCaseInfoDO>> caseId2InviteInfo = cfInviteCaseInfoDao.listUnDrawInviteCaseInfo(caseIds)
                .stream()
                .collect(Collectors.groupingBy(CfInviteCaseInfoDO::getInvitorCaseId));
        for (Integer caseId : caseId2InviteInfo.keySet()) {
            List<CfInviteCaseInfoDO> cfInviteCaseInfoDOS = caseId2InviteInfo.get(caseId);
            CfInvitorPaymentInfo invitorPaymentInfo = new CfInvitorPaymentInfo();
            invitorPaymentInfo.setCaseId(caseId);
            invitorPaymentInfo.setUserId(cfInviteCaseInfoDOS.stream().findFirst().map(CfInviteCaseInfoDO::getUserId).orElse(0L));
            invitorPaymentInfo.setPayAmountInFen(cfInviteCaseInfoDOS.stream().mapToInt(item -> item.getAmount() * 100).sum());
            invitorPaymentInfo.setInviteCaseInfoIds(cfInviteCaseInfoDOS.stream().map(item -> (long)item.getId()).collect(Collectors.toList()));
            paymentInfoList.add(invitorPaymentInfo);
        }

        return NewResponseUtil.makeSuccess(paymentInfoList);
    }

    @Override
    public Response<Boolean> changeInvitorPaymentStatus(int caseId, int targetDrawStatus) {
        List<CfInviteCaseInfoDO> invitorCaseIdList = cfInviteCaseInfoDao.listByInvitorCaseId(caseId);
        StringBuffer msg = new StringBuffer();
        InvitorDrawStatus drawStatus = InvitorDrawStatus.parseByCode(targetDrawStatus);
        boolean isAllMatchChange = invitorCaseIdList.stream().allMatch(item -> {
            InvitorDrawStatus currentDrawStatus = InvitorDrawStatus.parseByCode(item.getDrawStatus());
            boolean canChange = InvitorDrawStatus.judgeCanChange(currentDrawStatus, drawStatus);
            if (canChange) {
                log.info("提现信息:{}不符合修改状态,目前状态:{},目标状态：{}", JSON.toJSONString(item), currentDrawStatus, drawStatus);
                msg.append("案例:")
                        .append(item.getInvitorCaseId())
                        .append(", 当前状态:")
                        .append(currentDrawStatus);
            }
            return InvitorDrawStatus.judgeCanChange(currentDrawStatus, drawStatus);
        });
        if (isAllMatchChange) {
            cfInviteCaseInfoDao.updateByInvitorCaseId(caseId, targetDrawStatus);
            return NewResponseUtil.makeSuccess(true);
        } else {
            msg.append("不符合状态修改为:").append(drawStatus);
            log.info(msg.toString());
            return NewResponseUtil.makeResponse(0, msg.toString(), false);
        }
    }
}
