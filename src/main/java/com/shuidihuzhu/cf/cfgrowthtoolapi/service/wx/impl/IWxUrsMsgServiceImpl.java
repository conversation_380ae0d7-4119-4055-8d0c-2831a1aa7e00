package com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.wx.CfWxOfficialAccountApplyInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.wx.CfWxOfficialAccountBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthMsgEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WxOfficialAccountEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.wx.IShuidiWxFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx.IWxOfficialAccountApplyInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx.IWxUrsMsgService;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant.KEY4WXAPPLY;

/**
 * <AUTHOR>
 * @Date 2022/4/11 11:16 AM
 */
@Service
@Slf4j
public class IWxUrsMsgServiceImpl implements IWxUrsMsgService {

    @Autowired
    private Map<String,IShuidiWxFacade> shuidiWxFacadeMap;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService ;

    @Autowired
    private IWxOfficialAccountApplyInfoService wxOfficialAccountApplyInfoService;

    @Resource(name = "cfOlapCaseStat")
    private RedissonHandler cfOlapRedissonHandler;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private AlarmClient alarmClient;

    @Autowired
    private ApolloService apolloService;

    private static final List<String> alarmUsers = Lists.newArrayList("lichengjin","zenghuan");

    @Override
    public OpResult getWxMedia(String infoId, String channel, Integer caseType) {
        log.info("{} payload:{}",this.getClass().getSimpleName(),infoId);
        if (StringUtils.isEmpty(infoId) || StringUtils.isEmpty(channel) || caseType < 0){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfBdCaseInfoDo cfBdCaseInfoDo = null;
        if (caseType == 2) {
            cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoUuid(infoId);
            if (Objects.isNull(cfBdCaseInfoDo)) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
            }
        }
        String lockName = String.format(KEY4WXAPPLY, WxOfficialAccountEnums.ApplyActionEnum.lock.getDesc(),"");
        String key = String.format(KEY4WXAPPLY, WxOfficialAccountEnums.ApplyActionEnum.willapply.getDesc(),infoId);
        RLock lock = null;
        try {
            lock = cfOlapRedissonHandler.getLock(lockName);
            if (!lock.tryLock()) {
                log.info("{} consumeMessage get lock fail,lockName:{},payload:{}", this.getClass().getSimpleName(),lockName,infoId);
                mqProducerService.sendCfApplyWxUrlMsg(infoId);
                return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
            }
            //挑选thirdType
            Integer thirdType = pickThirdType();
            if (Objects.isNull(thirdType)){
                log.error("{} consumeMessage get thirdType is null payload:{}",this.getClass().getSimpleName(),infoId);
                //删除key 重新申请
                cfOlapRedissonHandler.del(key);
                return OpResult.createFailResult(CfGrowthtoolErrorCode.WX_APPLY_URL_THRESHOLD);
            }
            String uniqueCode = "";
            if (!Objects.isNull(cfBdCaseInfoDo)) {
               uniqueCode = cfBdCaseInfoDo.getUniqueCode();
            }
            CfWxOfficialAccountApplyInfoDO cfWxOfficialAccountApplyInfoDO = new CfWxOfficialAccountApplyInfoDO();
            cfWxOfficialAccountApplyInfoDO.setInfoUuid(infoId);
            cfWxOfficialAccountApplyInfoDO.setApplyStatus(WxOfficialAccountEnums.ApplyStatusEnum.applying.getStatus());
            cfWxOfficialAccountApplyInfoDO.setVolunteerUniqueCode(uniqueCode);
            cfWxOfficialAccountApplyInfoDO.setThirdType(thirdType);
            cfWxOfficialAccountApplyInfoDO.setBizType(CfGrowthMsgEnums.BizTypeEnum.OFFLINE.getCode());
            cfWxOfficialAccountApplyInfoDO.setMaterialDeleteStatus(WxOfficialAccountEnums.MaterialDeleteStatusEnum.SUBSISTING.getStatus());
            if (StringUtils.isNotBlank(channel)) {
                cfWxOfficialAccountApplyInfoDO.setChannel(channel);
                cfWxOfficialAccountApplyInfoDO.setApplyType(WxOfficialAccountEnums.ApplyTypeEnum.manualApply.getStatus());
            }
            wxOfficialAccountApplyInfoService.insertOrUpdate(cfWxOfficialAccountApplyInfoDO);
            String thirdKey = String.format(KEY4WXAPPLY, WxOfficialAccountEnums.ApplyActionEnum.thirdtype.getDesc(),thirdType);
            //插入数据库成功后,则认为请求微信接口次数+1
            boolean exists = cfOlapRedissonHandler.exists(thirdKey);
            if (exists){
                cfOlapRedissonHandler.incrBy(thirdKey, 1);
            }else{
                long timeMills = DateUtil.addDay(DateUtil.getCurrentDate(), 1).getTime() - DateUtil.getCurrentTimestamp().getTime();
                cfOlapRedissonHandler.setNX(thirdKey, NumberUtils.INTEGER_ONE, timeMills);
            }
            IShuidiWxFacade shuidiWxFacade = getShuidiWxFacade(apolloService.getWxApplyVideoClassName());
//            CrowdfundingInfo
            shuidiWxFacade.uploadNewsAndSendAll4Single(cfWxOfficialAccountApplyInfoDO,cfBdCaseInfoDo, channel);
        } catch (Exception e){
            log.error("{} consumeMessage payload:{} error",this.getClass().getSimpleName(),infoId,e);
        } finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            //不管最终结果是否成功，都删除key
            cfOlapRedissonHandler.del(key);
        }
        return OpResult.createSucResult();
    }

    public Integer pickThirdType() {
        Integer pickThirdType = null;
        IShuidiWxFacade shuidiWxFacade = getShuidiWxFacade(apolloService.getWxApplyVideoClassName());
        Map<Integer, List<CfWxOfficialAccountBaseDataDO>> officialMap = shuidiWxFacade.getAllThirdType();
        List<String> officialAccoutKey = officialMap.keySet().stream().map(item -> String.format(KEY4WXAPPLY, WxOfficialAccountEnums.ApplyActionEnum.thirdtype.getDesc(),item)).collect(Collectors.toList());
        Map<String, Integer> unSortMap = Maps.newHashMap();
        try {
            unSortMap = cfOlapRedissonHandler.mget(officialAccoutKey);
        }catch (Exception e){
            log.error("{} pickThirdType from redis error",this.getClass().getSimpleName());
        }
        //对Map的value进行升序排序
        List<String> sortList = unSortMap.entrySet().stream()
                .sorted(Comparator.comparingInt(Map.Entry::getValue))
                .map(Map.Entry::getKey).collect(Collectors.toList());
        String format = String.format(KEY4WXAPPLY, WxOfficialAccountEnums.ApplyActionEnum.thirdtype.getDesc(),"");
        if (sortList.size()==officialAccoutKey.size()){
            for (String key : sortList){
                Integer applyCount = unSortMap.get(key);
                Integer thirdType = Integer.valueOf(key.substring(format.length()));
                //申请次数达到阈值
                if (applyCount >= officialMap.get(thirdType).get(0).getRequestLimit()){
                    log.warn("thirdType:{} has reached threshold:{}",thirdType,officialMap.get(thirdType).get(0).getRequestLimit());
                    String alarmContent = "thirdType:"+thirdType+" has reached threshold:"+applyCount;
                    alarmClient.sendByUser(alarmUsers,alarmContent);
                    continue;
                }
                if (applyCount >= apolloService.getWxRequestThreshold()){
                    String alarmContent = "thirdType:"+thirdType+" has reached apollo threshold:"+applyCount;
                    alarmClient.sendByUser(alarmUsers,alarmContent);
                }
                pickThirdType = thirdType;
                break;
            }
        }else{
            List<Integer> thirdTypeFromRedis = sortList.stream().map(item -> Integer.valueOf(item.substring(format.length()))).collect(Collectors.toList());
            List<Integer> thirdTypeFromDb = Lists.newArrayList(officialMap.keySet());
            thirdTypeFromDb.removeAll(thirdTypeFromRedis);
            pickThirdType = thirdTypeFromDb.get(0);
        }
        return pickThirdType;
    }

    /**
     * 获取实现类
     * @param className
     * @return
     */
    public IShuidiWxFacade getShuidiWxFacade(String className){
        return shuidiWxFacadeMap.get(className);
    }
}
