package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patient;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.patient.BdPatientServePtDO;

import java.util.Date;
import java.util.List;

/**
 * 患者服务平台(BdPatientServePt)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-24 16:49:21
 */
public interface BdPatientServePtService {

    BdPatientServePtDO queryById(long id);

    List<BdPatientServePtDO> listByPhoneAndScene(String encryptPhone, int scene, Date startTime);

    int insert(BdPatientServePtDO bdPatientServePt);

    boolean deleteById(long id);

}
