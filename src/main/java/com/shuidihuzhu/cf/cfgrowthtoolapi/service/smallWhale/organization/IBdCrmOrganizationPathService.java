package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationPathDO;

import java.util.List;

/**
 * crm自建组织信息(BdCrmOrganizationPath)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-25 15:09:15
 */
public interface IBdCrmOrganizationPathService {

    BdCrmOrganizationPathDO queryById(long id);

    List<BdCrmOrganizationPathDO> listByIds(List<Long> listByIds);

    int updateOrInsert(BdCrmOrganizationPathDO bdCrmOrganizationPath);

    BdCrmOrganizationPathDO queryByOrgPath(String orgPath);

}