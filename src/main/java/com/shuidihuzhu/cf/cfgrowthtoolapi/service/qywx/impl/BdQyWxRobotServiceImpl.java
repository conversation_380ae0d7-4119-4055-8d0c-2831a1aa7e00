package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.impl;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdQyWxRobotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.qywx.QywxGroupRobotQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.BdQyWxRobotService;
import com.shuidihuzhu.cf.dao.qywx.BdQyWxRobotDao;

/**
 * 企业微信机器人信息(BdQyWxRobot)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-11 11:33:18
 */
@Service
public class BdQyWxRobotServiceImpl implements BdQyWxRobotService {
   
    @Resource
    private BdQyWxRobotDao bdQyWxRobotDao;

    @Override
    public BdQyWxRobotDO queryById(long id) {
        return bdQyWxRobotDao.queryById(id);
    }

    @Override
    public BdQyWxRobotDO queryValidById(long id) {
        BdQyWxRobotDO robot = queryById(id);
        if (robot != null && robot.getAccountStatus() == 0) {
            return robot;
        }
        return null;
    }

    @Override
    public List<BdQyWxRobotDO> listByCaseSourceType(int caseSourceType) {
        return bdQyWxRobotDao.listByCaseSourceType(caseSourceType);
    }

    @Override
    public int insert(BdQyWxRobotDO bdQyWxRobot) {
        return bdQyWxRobotDao.insert(bdQyWxRobot);
    }

    @Override
    public int update(BdQyWxRobotDO bdQyWxRobot) {
        return bdQyWxRobotDao.update(bdQyWxRobot);
    }

    @Override
    public BdQyWxRobotDO queryByExternalUserId(String externalUserId) {
        return bdQyWxRobotDao.queryByExternalUserId(externalUserId);
    }

    @Override
    public List<BdQyWxRobotDO> queryByCorpId(String corpId) {
        return bdQyWxRobotDao.queryByCorpId(corpId);
    }

    @Override
    public List<BdQyWxRobotDO> listRobotByPage(QywxGroupRobotQueryParam queryParam) {
        return bdQyWxRobotDao.listRobotByPage(queryParam);
    }

    @Override
    public int countRobot(QywxGroupRobotQueryParam queryParam) {
        return bdQyWxRobotDao.countRobot(queryParam);
    }

    @Override
    public int switchStatus(long id, int accountStatus) {
        return bdQyWxRobotDao.switchStatus(id, accountStatus);
    }

    @Override
    public List<BdQyWxRobotDO> queryByExternalUserIds(List<String> externalUserIds) {
        if (CollectionUtils.isEmpty(externalUserIds)) {
            return Collections.emptyList();
        }
        return bdQyWxRobotDao.queryByExternalUserIds(externalUserIds);
    }
} 