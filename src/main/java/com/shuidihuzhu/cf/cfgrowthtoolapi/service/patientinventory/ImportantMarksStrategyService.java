package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.PatientInventoryPatientFollowUpRecordDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.PatientInventoryPatientInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory.strategy.ImportantMarksStrategyInterface;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20  20:05
 */
@Service
public class ImportantMarksStrategyService {

    @Autowired
    private List<ImportantMarksStrategyInterface> importantMarksStrategyInterfaces;

    public boolean getNeedToFollowUpTodayLabel(PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo,
                                               String dateline,
                                               int volunteerLevel,
                                               int departmentType) {
        ImportantMarksStrategyInterface activityStrategy = getImportantMarksStrategyInterface(departmentType);
        if (activityStrategy == null) {
            return false;
        }
        return activityStrategy.getNeedToFollowUpTodayLabel(patientInventoryPatientFollowUpRecordDo, dateline, volunteerLevel);
    }

    public boolean getPayCloseAttentionToLabel(List<PatientInventoryPatientFollowUpRecordDo> patientInventoryPatientFollowUpRecordDoList,
                                               PatientInventoryPatientInfoDo patientInventoryPatientInfoDo,
                                               String dateline,
                                               int volunteerLevel,
                                               int departmentType) {
        ImportantMarksStrategyInterface activityStrategy = getImportantMarksStrategyInterface(departmentType);
        if (activityStrategy == null) {
            return false;
        }
        return activityStrategy.getPayCloseAttentionToLabel(patientInventoryPatientFollowUpRecordDoList,
                patientInventoryPatientInfoDo, dateline, volunteerLevel);
    }

    private ImportantMarksStrategyInterface getImportantMarksStrategyInterface(int departmentType) {
        return importantMarksStrategyInterfaces.stream().filter(item -> item.support(departmentType)).findFirst().orElse(null);
    }

}
