package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponBudgetDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponBudgetUseResultVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponAccessModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.ICfWeaponBudgetService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.weapon.CfWeaponBudgetDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.cfgrowthtoolapi.constants.WeaponAccessErrorMsgConstant.BUDGETNOTEXIST;
import static com.shuidihuzhu.cf.cfgrowthtoolapi.constants.WeaponAccessErrorMsgConstant.BUDGETNOTVALID;

/**
 * @author: fengxuan
 * @create 2020-10-15 8:13 下午
 **/
@Service
public class CfWeaponBudgetServiceImpl implements ICfWeaponBudgetService {

    @Autowired
    private CfWeaponBudgetDao cfWeaponBudgetDao;

    @Override
    public void insertBatch(List<CfWeaponBudgetDO> cfWeaponBudgetDOS) {
        List<List<CfWeaponBudgetDO>> partition = Lists.partition(cfWeaponBudgetDOS, 20);
        partition.forEach(item -> cfWeaponBudgetDao.insertBatch(item));
    }

    @Override
    public List<CfWeaponBudgetDO> listAllBudgetByGroupId(int budgetGroupId) {
        if (budgetGroupId <= 0) {
            return Lists.newArrayList();
        }
        return cfWeaponBudgetDao.listAllNoMatterDeleteByGroupId(budgetGroupId);
    }

    @Override
    public List<CfWeaponBudgetDO> listByGroupId(int budgetGroupId) {
        if (budgetGroupId <= 0) {
            return Lists.newArrayList();
        }
        return cfWeaponBudgetDao.listByGroupId(budgetGroupId);
    }

    @Override
    public CfWeaponBudgetDO getById(int budgetId) {
        return cfWeaponBudgetDao.getById(budgetId);
    }

    @Override
    public List<CfWeaponBudgetDO> listIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return cfWeaponBudgetDao.listByIds(ids);
    }

    @Override
    public List<CfWeaponBudgetDO> listByGroupAndStatus(int budgetGroupId, Integer status, Integer useStatus) {
        if (budgetGroupId <= 0) {
            return Lists.newArrayList();
        }
        return cfWeaponBudgetDao.listByGroupAndStatus(budgetGroupId, status, useStatus);
    }

    @Override
    public List<CfWeaponBudgetDO> listInvalidBudget(int budgetGroupId) {
        return cfWeaponBudgetDao.listInvalidBudget(budgetGroupId);
    }

    @Override
    public int invalidBudget(int budget) {
        if (budget <= 0) {
            return 0;
        }
        return cfWeaponBudgetDao.invalidBudget(budget);
    }

    @Override
    public int cancelBudget(List<Integer> budgetIds) {
        if (CollectionUtils.isEmpty(budgetIds)) {
            return 0;
        }
        return cfWeaponBudgetDao.cancelBudget(budgetIds);
    }

    @Override
    public int useBudget(int budgetId) {
        if (budgetId <= 0) {
            return 0;
        }
        return cfWeaponBudgetDao.useBudget(budgetId);
    }

    /**
     * 编辑子预算时修改 总预算、每日使用限制
     */
    @Override
    public int updateResource(CfWeaponBudgetDO cfWeaponBudgetDO) {
        if (cfWeaponBudgetDO.getChildTotalResource() <= 0 && cfWeaponBudgetDO.getLimitNumEveryDay() < 0) {
            return 0;
        }
        return cfWeaponBudgetDao.editBudget(cfWeaponBudgetDO.getId(), cfWeaponBudgetDO.getChildTotalResource(), cfWeaponBudgetDO.getLimitNumEveryDay(), (int) cfWeaponBudgetDO.getChildTotalMoney());
    }

    /**
     * 根据预算组id查询查询出所有未作废子预算
     */
    @Override
    public List<CfWeaponBudgetDO> listBudgetByBudgetGroupId(List<Integer> budgetGroupIds) {
        if (CollectionUtils.isEmpty(budgetGroupIds)) {
            return Lists.newArrayList();
        }
        return cfWeaponBudgetDao.listByGroupIds(budgetGroupIds)
                .stream()
                .filter(CfWeaponBudgetDO::canUseByWhale).collect(Collectors.toList());
    }

    @Override
    public List<CfWeaponBudgetDO> listBudgetByIds(Set<Integer> budgetIdSet) {
        if (CollectionUtils.isEmpty(budgetIdSet)) {
            return Lists.newArrayList();
        }
        return listIds(Lists.newArrayList(budgetIdSet));
    }

    @Override
    public int modifyResource(int id, int addUseResource, int addApplyingResource, int addUseMoney, int addApplyingMoney) {
        return cfWeaponBudgetDao.modifyResource(id, addUseResource, addApplyingResource, addUseMoney, addApplyingMoney);
    }

    @Override
    public int addChildApplyingResource(int id, int childApplyingResource) {
        if (id <= 0) {
            return 0;
        }
        return cfWeaponBudgetDao.addChildApplyingResource(id, childApplyingResource);
    }

    @Override
    public int addChildApplyingMoney(int budgetId, int childApplyingMoney) {
        return cfWeaponBudgetDao.addChildApplyingMoney(budgetId, childApplyingMoney);
    }

    @Override
    public int modifyChildUsedMoney(int budgetId, int childUnUsedMoney) {
        return cfWeaponBudgetDao.modifyChildUsedMoney(budgetId, childUnUsedMoney);
    }

    @Override
    public void loanResource(int budgetId, int resourceNum) {
        if (resourceNum < 0) {
            return;
        }
        cfWeaponBudgetDao.loanResource(budgetId, resourceNum);
    }

    @Override
    public void loanMoney(int budgetId, int loanMoney) {
        if (loanMoney < 0) {
            return;
        }
        cfWeaponBudgetDao.loanMoney(budgetId, loanMoney);
    }

    @Override
    public void beLoanResource(int budgetId, int resourceNum) {
        if (resourceNum < 0) {
            return;
        }
        cfWeaponBudgetDao.beLoanResource(budgetId, resourceNum);
    }

    @Override
    public void beLoanMoney(int budgetId, int loanMoney) {
        if (loanMoney < 0) {
            return;
        }
        cfWeaponBudgetDao.beLoanMoney(budgetId, loanMoney);
    }

    @Override
    public WeaponAccessModel checkValidBudget(int budgetId) {
        CfWeaponBudgetDO cfWeaponBudgetDO = cfWeaponBudgetDao.getById(budgetId);
        WeaponAccessModel weaponAccessModel = new WeaponAccessModel();
        if (Objects.isNull(cfWeaponBudgetDO)) {
            weaponAccessModel.setSuccess(false);
            weaponAccessModel.setFailDesc(BUDGETNOTEXIST);
            return weaponAccessModel;
        }
        //预算作废
        if (WeaponEnums.BudgetValidStatusEnum.invalid.getCode() == cfWeaponBudgetDO.getStatus()) {
            weaponAccessModel.setSuccess(false);
            weaponAccessModel.setFailDesc(BUDGETNOTVALID);
            return weaponAccessModel;
        }
        weaponAccessModel.setSuccess(true);
        return weaponAccessModel;
    }

    @Override
    public List<CfWeaponBudgetDO> listByWeaponId(int weaponId) {
        return cfWeaponBudgetDao.listByWeaponId(weaponId);
    }

    @Override
    public int repairResource(int id, int usedResource, int applyingResource) {
        return cfWeaponBudgetDao.repairResource(id, usedResource, applyingResource);
    }

    @Override
    public List<CfWeaponBudgetDO> listBudgetIdsByOrgIds(List<Integer> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Lists.newArrayList();
        }
        List<CfWeaponBudgetDO> result = Lists.newArrayList();
        Lists.partition(orgIds, GrowthtoolUtil.ONE_QUERY_LIMIT)
                .forEach(item -> result.addAll(cfWeaponBudgetDao.listByOrgIds(item)));
        return result;
    }

    @Override
    public CfWeaponBudgetDO getByBudgetGroupIdAndOrgId(int budgetGroupId, int orgId) {
        if (budgetGroupId <= 0 || orgId <= 0) {
            return null;
        }
        return cfWeaponBudgetDao.getByBudgetGroupIdAndOrgId(budgetGroupId, orgId);
    }

    @Override
    public int updateTotalMoneyById(int id, double childTotalMoney) {
        if (id <= 0) {
            return 0;
        }
        return cfWeaponBudgetDao.updateTotalMoneyById(id, childTotalMoney);
    }

    @Override
    public List<CfWeaponBudgetUseResultVo> listAllByGroupIdAndOrgId(int budgetGroupId, List<Integer> orgIdList) {
        if (budgetGroupId <= 0) {
            return Lists.newArrayList();
        }
        return cfWeaponBudgetDao.listAllByGroupIdAndOrgId(budgetGroupId, orgIdList);
    }

    @Override
    public List<CfWeaponBudgetDO> listByGroupIdAndOrgId(int budgetGroupId, List<Integer> orgIdList) {
        if (budgetGroupId <= 0 || CollectionUtils.isEmpty(orgIdList)) {
            return Lists.newArrayList();
        }
        return cfWeaponBudgetDao.listByGroupIdAndOrgId(budgetGroupId, orgIdList);
    }

    @Override
    public List<CfWeaponBudgetDO> listByWeaponIdAndOrgId(int weaponId, List<Integer> orgIdList) {
        if (weaponId <= 0 || CollectionUtils.isEmpty(orgIdList)) {
            return Lists.newArrayList();
        }
        return cfWeaponBudgetDao.listByWeaponIdAndOrgId(weaponId, orgIdList);
    }

    @Override
    public List<CfWeaponBudgetDO> listByGroupIdsAndOrgId(List<Integer> budgetGroupIds, List<Integer> orgIdList) {
        if (CollectionUtils.isEmpty(budgetGroupIds) || CollectionUtils.isEmpty(orgIdList)) {
            return Lists.newArrayList();
        }
        return cfWeaponBudgetDao.listByGroupIdsAndOrgId(budgetGroupIds, orgIdList);
    }

    @Override
    public int modifyManagementMoney(int id, int modifyManagementMoney) {
        if (id <= 0) {
            return 0;
        }
        return cfWeaponBudgetDao.modifyManagementMoney(id, modifyManagementMoney);
    }

    @Override
    public int modifyManagementMoneySum(int id, int modifyManagementMoney) {
        if (id <= 0) {
            return 0;
        }
        return cfWeaponBudgetDao.modifyManagementMoneySum(id, modifyManagementMoney);
    }

    @Override
    public int addManagementApplyingMoney(int id, int managementApplyingMoney) {
        if (id <= 0) {
            return 0;
        }
        return cfWeaponBudgetDao.addManagementApplyingMoney(id, managementApplyingMoney);
    }

    @Override
    public int addManagementApplyingMoneySum(int id, int managementApplyingMoney) {
        if (id <= 0) {
            return 0;
        }
        return cfWeaponBudgetDao.addManagementApplyingMoneySum(id, managementApplyingMoney);
    }

}
