package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfTianRunVirtualPhonePoolDO;

/**
 * @author: wanghui
 * @create: 2020-05-27 19:55
 */
public interface ICfTianRunVirtualPhonePoolService {
	/**
	 * 保存
	 * @param cfTianRunVirtualPhonePoolDO
	 * @return
	 */
	int saveCfTianRunVirtualPhonePoolDO( CfTianRunVirtualPhonePoolDO cfTianRunVirtualPhonePoolDO);

	/**
	 * 更新绑定状态
	 * @param bindStatus
	 * @param vEncryptMobile
	 * @return
	 */
	int updateBindStatus(int bindStatus, String vEncryptMobile);

	/**
	 * 获得一个 可分配的虚拟号
	 * @param city
	 * @return
	 */
	CfTianRunVirtualPhonePoolDO getCanAssignDataByCity(String city);

	/**
	 * 查询该虚拟号的数据
	 * @param vEncryptMobile
	 * @return
	 */
	CfTianRunVirtualPhonePoolDO getCfTianRunVirtualPhonePoolDOByVEncryptMobile( String vEncryptMobile);
}
