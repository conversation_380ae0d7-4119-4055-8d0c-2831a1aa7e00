package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.configuration.EsDataSoureConfigure;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.PatientInventoryDepartmentsBedSearch;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.PatientInventoryRecordSearch;
import com.shuidihuzhu.esdk.EsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.collapse.CollapseBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/17  19:50
 */
@Service
@Slf4j
public class AdminPatientInventorySearchService {

    @Resource(name = EsDataSoureConfigure.ES_BEAN_NAME)
    private EsClient esClient;

    public Pair<Long, List<Long>> search(PatientInventoryDepartmentsBedSearch patientInventoryDepartmentsBedSearch) {
        return searchFromEs(patientInventoryDepartmentsBedSearch);
    }

    /**
     * 查询es
     *
     * @param
     * @return
     */
    private Pair<Long, List<Long>> searchFromEs(PatientInventoryDepartmentsBedSearch patientInventoryDepartmentsBedSearch) {
        if (Objects.isNull(patientInventoryDepartmentsBedSearch)) {
            return Pair.of(0L, Lists.newArrayList());
        }

        String indexName = "shuidi_cf_clewtrack_patient_inventory_departments_bed_v1_alias";

        BoolQueryBuilder boolQueryBuilder = buildQuery(patientInventoryDepartmentsBedSearch);

        int current = (patientInventoryDepartmentsBedSearch.getCurrent() - 1) * patientInventoryDepartmentsBedSearch.getPageSize();
        int pageSize = patientInventoryDepartmentsBedSearch.getPageSize();
        Map<String, SortOrder> sortOrderMap = Maps.newLinkedHashMap();
        sortOrderMap.put(patientInventoryDepartmentsBedSearch.getOrder(), patientInventoryDepartmentsBedSearch.getSortOrder());

        Optional<Pair<Long, List<Map<String, Object>>>> optional = getQueryResultFromEs(indexName, boolQueryBuilder, current, pageSize, sortOrderMap);
        if (optional.isEmpty()) {
            return Pair.of(0L, Lists.newArrayList());
        }

        Pair<Long, List<Map<String, Object>>> pair = optional.get();
        List<Long> idList = build(pair.getRight());
        return Pair.of(pair.getLeft(), idList);
    }

    private BoolQueryBuilder buildQuery(PatientInventoryDepartmentsBedSearch patientInventoryDepartmentsBedSearch) {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        if (StringUtils.isNotBlank(patientInventoryDepartmentsBedSearch.getProvince())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("province", patientInventoryDepartmentsBedSearch.getProvince()));
        }
        if (StringUtils.isNotBlank(patientInventoryDepartmentsBedSearch.getCity())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("city", patientInventoryDepartmentsBedSearch.getCity()));
        }
        if (StringUtils.isNotBlank(patientInventoryDepartmentsBedSearch.getHospitalName())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("hospital_name", patientInventoryDepartmentsBedSearch.getHospitalName()));
        }
        if (StringUtils.isNotBlank(patientInventoryDepartmentsBedSearch.getDepartmentsName())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("departments_name", patientInventoryDepartmentsBedSearch.getDepartmentsName()));
        }
        if (patientInventoryDepartmentsBedSearch.getValid() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("valid", patientInventoryDepartmentsBedSearch.getValid()));
        }
        boolQueryBuilder.filter(QueryBuilders.termQuery("is_delete", 0));
        return boolQueryBuilder;
    }


    private Optional<Pair<Long, List<Map<String, Object>>>> getQueryResultFromEs(String indexName, QueryBuilder queryBuilder, int from, int size, Map<String, SortOrder> sortOrderMap) {

        try {
            SearchResponse searchResponse = esClient.pullByQueryBuilders("es-common", indexName, queryBuilder, from, size, sortOrderMap);
            if (isEsResultEmpty(searchResponse)) {
                log.info("PlatformEsService.{} es查询返回值为空 param:{}", indexName, JSON.toJSONString(searchResponse));
                return Optional.of(Pair.of(0L, Lists.newArrayList()));
            }

            return buildEsResultAsPair(searchResponse);
        } catch (Exception e) {
            log.info("PlatformEsService.{} es查询异常", indexName, e);
        }

        return Optional.of(Pair.of(0L, Lists.newArrayList()));
    }

    private boolean isEsResultEmpty(SearchResponse searchResponse) {
        return searchResponse == null
                || searchResponse.getHits() == null
                || searchResponse.getHits().getHits() == null
                || searchResponse.getHits().getHits().length == 0;
    }

    private Optional<Pair<Long, List<Map<String, Object>>>> buildEsResultAsPair(SearchResponse searchResponse) {
        if (searchResponse == null) {
            return Optional.of(Pair.of(0L, Lists.newArrayList()));
        }

        SearchHits hits = null;
        SearchHit[] searchHits = null;
        if ((hits = searchResponse.getHits()) == null || (searchHits = hits.getHits()) == null || searchHits.length == 0) {
            return Optional.of(Pair.of(0L, Lists.newArrayList()));
        }

        List<Map<String, Object>> sources = Arrays.stream(hits.getHits()).map(SearchHit::getSourceAsMap).collect(Collectors.toList());
        return Optional.of(Pair.of(hits.getTotalHits().value, sources));
    }

    private List<Long> build(List<Map<String, Object>> esRes) {
        List<Long> bedPatientRecordIdList = Lists.newArrayList();
        for (Map<String, Object> map : esRes) {
            long id = Optional.ofNullable(map).map(v -> v.get("id")).map(v -> Long.parseLong(v.toString())).orElse(0L);
            if (id > 0L) {
                bedPatientRecordIdList.add(id);
            }
        }
        return bedPatientRecordIdList;
    }

}
