package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiRecruitClewBaseDataDO;

import java.util.List;

/**
 * 绩效招募信息表(CfKpiRecruitClewBaseData)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-31 11:19:42
 */
public interface ICfKpiRecruitClewBaseDataService {

    CfKpiRecruitClewBaseDataDO queryById(long id);

    int insert(CfKpiRecruitClewBaseDataDO cfKpiRecruitClewBaseData);

    List<CfKpiRecruitClewBaseDataDO> listRandomizedByDateKey(String dayKey, String startTime, String endTime);

    List<CfKpiRecruitClewBaseDataDO> listRecruitByDateKey(String dayKey, String startTime, String endTime);

    List<CfKpiRecruitClewBaseDataDO> listByPatientIds(String dateTime, List<Long> patientIds);
    int updateLeaderInfo(String dayKey, long patientId, String leaderInfo);


}
