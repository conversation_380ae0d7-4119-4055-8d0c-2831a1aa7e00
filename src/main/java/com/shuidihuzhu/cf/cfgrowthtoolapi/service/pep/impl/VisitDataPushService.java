package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiVisitBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiVisitBaseDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepGrowthtoolVisitIdTemplate;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-03-20 17:16
 **/
@Service
public class VisitDataPushService extends AbstractPushDataService {

    @Autowired
    private ICfKpiVisitBaseDataService cfKpiVisitBaseDataService;


    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.visit_data;
    }

    @Override
    protected List<PepGrowthtoolVisitIdTemplate> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        List<PepGrowthtoolVisitIdTemplate> result = Lists.newArrayList();
        Date lotStartTime = lotInfo.getLotStartTime();
        Map<String, List<CfKpiVisitBaseDataDO>> visitMap = cfKpiVisitBaseDataService.listValidByTime(new DateTime(lotStartTime).toString(GrowthtoolUtil.ymfmt), pushWhichDay.toString(GrowthtoolUtil.ymdfmt));
        List<CfKpiVisitBaseDataDO> visitBaseDataDOList = visitMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        for (CfKpiVisitBaseDataDO cfKpiVisitBaseDataDO : visitBaseDataDOList) {
            PepGrowthtoolVisitIdTemplate pepVisitIdTemplate = new PepGrowthtoolVisitIdTemplate();
            pepVisitIdTemplate.setUserId(cfKpiVisitBaseDataDO.getUniqueCode());
            pepVisitIdTemplate.setVisit_id(cfKpiVisitBaseDataDO.getVisitId());
            pepVisitIdTemplate.setUnique_code(cfKpiVisitBaseDataDO.getUniqueCode());
            pepVisitIdTemplate.setBe_visit_unique_code(cfKpiVisitBaseDataDO.getBeVisitUniqueCode());
            pepVisitIdTemplate.setVisit_duration(cfKpiVisitBaseDataDO.getVisitDuration());
            pepVisitIdTemplate.setVisit_start_time(cfKpiVisitBaseDataDO.getVisitStartTime());
            pepVisitIdTemplate.setValid_visit(cfKpiVisitBaseDataDO.getValidVisit());
            pepVisitIdTemplate.setLotId(lotInfo.getLotId());
            result.add(pepVisitIdTemplate);
        }
        return result;
    }
}
