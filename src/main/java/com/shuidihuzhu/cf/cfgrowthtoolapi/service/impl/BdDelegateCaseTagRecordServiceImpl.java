package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdDelegateCaseTagRecordDO;
import com.shuidihuzhu.cf.dao.BdDelegateCaseTagRecordDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.BdDelegateCaseTagRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 代理商发起案例标签(BdDelegateCaseTagRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-13 10:54:09
 */
@Service
public class BdDelegateCaseTagRecordServiceImpl implements BdDelegateCaseTagRecordService {
   
    @Resource
    private BdDelegateCaseTagRecordDao bdDelegateCaseTagRecordDao;

    @Override
    public BdDelegateCaseTagRecordDO queryById(long id) {
        return bdDelegateCaseTagRecordDao.queryById(id);
    }
    

    @Override
    public int insert(BdDelegateCaseTagRecordDO bdDelegateCaseTagRecordDO) {
        return bdDelegateCaseTagRecordDao.insert(bdDelegateCaseTagRecordDO);
    }


    @Override
    public int updateByApproveInfo(long approveId, int approveStatus, String rejectReason, String operator) {
        return bdDelegateCaseTagRecordDao.updateByApproveInfo(approveId, approveStatus,
                rejectReason, new Date(), Optional.ofNullable(operator).orElse(""));
    }

    @Override
    public List<BdDelegateCaseTagRecordDO> listByCaseIdAndSource(int caseId, int applySource) {
        return bdDelegateCaseTagRecordDao.listByCaseIdAndSource(caseId, applySource);
    }

    @Override
    public List<BdDelegateCaseTagRecordDO> listByCaseId(int caseId) {
        return bdDelegateCaseTagRecordDao.listByCaseId(caseId);
    }

    @Override
    public List<BdDelegateCaseTagRecordDO> listByCaseIds(List<Integer> caseIds) {
        return bdDelegateCaseTagRecordDao.listByCaseIds(caseIds);
    }

}
