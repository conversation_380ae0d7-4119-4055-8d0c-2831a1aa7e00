package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdQyWxMsgRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.BdQyWxMsgRecordService;
import com.shuidihuzhu.cf.dao.qywx.BdQyWxMsgRecordDao;

/**
 * 企业微信消息发送记录(BdQyWxMsgRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-11 11:33:18
 */
@Service
public class BdQyWxMsgRecordServiceImpl implements BdQyWxMsgRecordService {

    private static final Logger log = LoggerFactory.getLogger(BdQyWxMsgRecordServiceImpl.class);
    @Resource
    private BdQyWxMsgRecordDao bdQyWxMsgRecordDao;
    
    @Override
    public int insert(BdQyWxMsgRecordDO bdQyWxMsgRecord) {
        return bdQyWxMsgRecordDao.insert(bdQyWxMsgRecord);
    }

    @Override
    public int update(BdQyWxMsgRecordDO bdQyWxMsgRecord) {
        return bdQyWxMsgRecordDao.update(bdQyWxMsgRecord);
    }

    @Override
    public BdQyWxMsgRecordDO queryById(long id) {
        return bdQyWxMsgRecordDao.queryById(id);
    }


    @Override
    public List<BdQyWxMsgRecordDO> queryByCaseId(int caseId) {
        return bdQyWxMsgRecordDao.queryByCaseId(caseId);
    }

    @Override
    public List<BdQyWxMsgRecordDO> queryByCaseIdAndMsgType(int caseId, int msgType) {
        return bdQyWxMsgRecordDao.queryByCaseIdAndMsgType(caseId, msgType);
    }

    @Override
    public BdQyWxMsgRecordDO queryByMsgId(String msgId) {
        return bdQyWxMsgRecordDao.queryByMsgId(msgId);
    }
    
    @Override
    public List<BdQyWxMsgRecordDO> queryByCaseIdAndMsgTypeAndTimeRange(int caseId, int msgType, Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return new ArrayList<>();
        }
        return bdQyWxMsgRecordDao.queryByCaseIdAndMsgTypeAndTimeRange(caseId, msgType, startTime, endTime);
    }

    @Override
    public void updateSendStatus(String msgId, int sendStatus) {
        bdQyWxMsgRecordDao.updateSendStatus(msgId, sendStatus);
    }

    @Override
    public void updateRealSendTime(long id, Date realSendTime) {
        bdQyWxMsgRecordDao.updateRealSendTime(id, realSendTime);
    }

    @Override
    public void updateShareContent(long id, String shareContent) {
        log.info("updateShareContent:{},id:{}", shareContent, id);
        bdQyWxMsgRecordDao.updateShareContent(id, shareContent);
    }
}