package com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice.workwx;

import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-15
 */
public interface WorkWeiXinService {

    OpResult sendByUser(List<String> operatorNameList, String s);

	OpResult sendByVolunteers(List<CrowdfundingVolunteer> volunteers, String content);

}
