package com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.campaignv2.BdViewAttributeDO;

/**
 * 个人配置表(BdViewAttribute)表服务接口
 *
 * <AUTHOR>
 * @since 2021-08-26 23:53:49
 */
public interface BdViewAttributeService {

    /**
     * @return 实例对象
     */
    BdViewAttributeDO queryByUniqueCode(String uniqueCode);




    /**
     * 新增数据
     * @param bdViewAttribute 实例对象
     * @return 实例对象
     */
    int insertOrUpdate(BdViewAttributeDO bdViewAttribute);

    /**
     * 修改数据
     *
     * @param bdViewAttribute 实例对象
     * @return 实例对象
     */
    int update(BdViewAttributeDO bdViewAttribute);

}