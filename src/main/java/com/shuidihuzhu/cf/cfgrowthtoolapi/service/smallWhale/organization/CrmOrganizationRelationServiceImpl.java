package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgOptLogDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolCrusorQuery;
import com.shuidihuzhu.cf.dao.bdcrm.BdCrmOrgUserRelationDao;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-05-13 16:29
 **/
@Service
@Slf4j
public class CrmOrganizationRelationServiceImpl implements ICrmOrganizationRelationService {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired
    private BdCrmOrgUserRelationDao bdCrmOrgUserRelationDao;

    @Autowired
    private ICrmOptLogService crmOptLogService;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    public List<BdCrmOrgUserRelationDO> getFromRedis(long orgId) {
        List<BdCrmOrgUserRelationDO> relationDOList = Lists.newArrayList();
        try {
            String msg = redissonHandler.get(String.format(GeneralConstant.cfCrmBdOrgUserRelation, orgId), String.class);
            if (StringUtils.isNotBlank(msg)) {
                relationDOList = JSON.parseArray(msg, BdCrmOrgUserRelationDO.class);
            } else {
                log.info("从redis查到的组织:{}数据为空", orgId);
            }
        } catch (Exception e) {
            log.error("从redis中获取组织下成员异常但不影响数据查询,组织id:{}", orgId, e);
        }
        return relationDOList;
    }


    public Map<Long, List<BdCrmOrgUserRelationDO>> listFromRedis(List<Long> orgIds) {
        if (CollectionUtils.isNotEmpty(orgIds)) {
            return Maps.newHashMap();
        }
        Map<Long, List<BdCrmOrgUserRelationDO>> resultMap = Maps.newHashMap();
        List<String> keys = Lists.newArrayList();
        for (Long orgId : orgIds) {
            keys.add(String.format(GeneralConstant.cfCrmBdOrgUserRelation, orgId));
        }
        Map<String, Object> redisResult = redissonHandler.mget(keys);
        if (MapUtils.isNotEmpty(redisResult)) {
            redisResult.forEach((key, value) -> {
                if (StringUtils.isNotBlank((String) value)) {
                    List<BdCrmOrgUserRelationDO> relationDOS = JSON.parseArray(((String) value), BdCrmOrgUserRelationDO.class);
                    if (CollectionUtils.isNotEmpty(relationDOS)) {
                        resultMap.put(relationDOS.get(0).getOrgId(), relationDOS);
                    }
                }
            });
        }
        return resultMap;
    }


    public void cleanRedis(long orgId, String mis, String uniqueCode) {
        try {
            log.info("清空redis中组织:{}的成员信息", orgId);
            redissonHandler.del(String.format(GeneralConstant.cfCrmBdOrgUserRelation, orgId));
            cleanMemberRedisByUniqueCode(mis, uniqueCode);
        } catch (Exception e) {
            log.error("清空redis组织成员异常,请手动触发清理,链接:{}", "", e);
        }
    }

    private void cleanMemberRedisByUniqueCode(String mis, String uniqueCode) {
        if (StringUtils.isNotBlank(mis)){
            redissonHandler.del(GeneralConstant.VOLUNTEER_VIEW_SUB_ORGS + mis);
            log.info("删除" + GeneralConstant.VOLUNTEER_VIEW_SUB_ORGS + mis + "redis 中的数据");
        }

        redissonHandler.del(GeneralConstant.VOLUNTEER_VIEW_SUB_ORG_UNIQUECODE + uniqueCode);
        log.info("删除" + GeneralConstant.VOLUNTEER_VIEW_SUB_ORG_UNIQUECODE + uniqueCode + "redis 中的数据");
    }


    @Async
    public void syncTRedis(long orgId, List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDaoList) {
        try {
            if (CollectionUtils.isEmpty(bdCrmOrgUserRelationDaoList)) {
                return;
            }
            if (bdCrmOrgUserRelationDaoList.size() > 500) {
                log.error("组织:{}中的人员超过限制,请确认", orgId, new RuntimeException("redis可能存在大value"));
            }
            redissonHandler.setEX(String.format(GeneralConstant.cfCrmBdOrgUserRelation, orgId), JSON.toJSONString(bdCrmOrgUserRelationDaoList), RedissonHandler.ONE_HOUR);
        } catch (Exception e) {
            log.error("redis同步组织下成员异常,组织id:{}", orgId);
        }
    }


    @Override
    public void addMember(BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO, long adminUserId) {
        bdCrmOrgUserRelationDao.insert(bdCrmOrgUserRelationDO);
        commitAddMemberLog(bdCrmOrgUserRelationDO, adminUserId);
        cleanRedis(bdCrmOrgUserRelationDO.getOrgId(), bdCrmOrgUserRelationDO.getMis(), bdCrmOrgUserRelationDO.getUniqueCode());
        //同步 案例，线索, 报备进行更新对应原来orgId=0的数据
        customEventPublisher.publish(new OrgAddMemberEvent(this, bdCrmOrgUserRelationDO));
        customEventPublisher.publish(new OrgOrMemberChangeEvent(this, bdCrmOrgUserRelationDO, null));
    }

    @Override
    public void deleteMemberById(long relationId, long adminUserId) {
        BdCrmOrgUserRelationDO relationDO = bdCrmOrgUserRelationDao.getByRelationId(relationId);
        if (relationDO == null) {
            log.info("删除的org_user_relation:{}不存在", relationId);
            return;
        }
        bdCrmOrgUserRelationDao.delete(relationId, OrganizationUserEnums.RelationDeleteCode.delete_by_sea.getCode());
        commitDeleteMemberLog(relationDO, adminUserId, null);
        cleanRedis(relationDO.getOrgId(), relationDO.getMis(), relationDO.getUniqueCode());
        customEventPublisher.publish(new OrgOrMemberChangeEvent(this, relationDO, null));
    }

    @Override
    public void autoDeleteMemberId(String uniqueCode, String comment) {
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = bdCrmOrgUserRelationDao.listMemberOrgRelationByUniqueCode(uniqueCode);
        for (BdCrmOrgUserRelationDO relationDO : bdCrmOrgUserRelationDOS) {
            bdCrmOrgUserRelationDao.delete(relationDO.getId(), OrganizationUserEnums.RelationDeleteCode.delele_when_leveal.getCode());
            commitDeleteMemberLog(relationDO, 0, comment);
            cleanRedis(relationDO.getOrgId(), relationDO.getMis(), relationDO.getUniqueCode());
            customEventPublisher.publish(new OrgOrMemberChangeEvent(this, relationDO, null));
        }
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listMemberOrgRelationByUniqueCode(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return Lists.newArrayList();
        }
        return bdCrmOrgUserRelationDao.listMemberOrgRelationByUniqueCode(uniqueCode);
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listMemberOrgRelationNoMatterDelete(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return Lists.newArrayList();
        }
        return bdCrmOrgUserRelationDao.listMemberOrgRelationNoMatterDelete(uniqueCode);
    }


    @Override
    public List<BdCrmOrgUserRelationDO> listByUniqueCodes(List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Lists.newArrayList();
        }
        return bdCrmOrgUserRelationDao.listByUniqueCodes(uniqueCodes);
    }

    @Override
    public BdCrmOrgUserRelationDO getByUniqueCode(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return null;
        }
        return bdCrmOrgUserRelationDao.getByUniqueCode(uniqueCode);
    }


    @Override
    public List<BdCrmOrgUserRelationDO> listMemberOrgRelationByMis(String mis) {
        if (StringUtils.isBlank(mis)) {
            return Lists.newArrayList();
        }
        return bdCrmOrgUserRelationDao.listMemberOrgRelationByMis(mis);
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listMemberOrgRelationByMisName(String misName) {
        if (StringUtils.isBlank(misName)) {
            return Lists.newArrayList();
        }
        return bdCrmOrgUserRelationDao.listMemberOrgRelationByMisName(misName);
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listMemberOrgRelationNoMatterDeleteByUniqueCode(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return Lists.newArrayList();
        }
        return bdCrmOrgUserRelationDao.listMemberOrgRelationNoMatterDeleteByUniqueCode(uniqueCode);
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listRelationByOrgId(long orgId) {
        if (orgId <= 0) {
            return Lists.newArrayList();
        }
        List<BdCrmOrgUserRelationDO> relationDaoList = getFromRedis(orgId);
        if (CollectionUtils.isNotEmpty(relationDaoList)) {
            return relationDaoList;
        } else {
            relationDaoList = bdCrmOrgUserRelationDao.listRelationByOrgId(orgId);
        }
        syncTRedis(orgId, relationDaoList);
        return relationDaoList;
    }


    @Override
    public Map<Long, List<BdCrmOrgUserRelationDO>> batchListRelation(List<Long> orgIds) {
        return listByOrgIdsFromDB(orgIds)
                .stream()
                .collect(Collectors.groupingBy(BdCrmOrgUserRelationDO::getOrgId));
    }


    @Override
    public List<BdCrmOrgUserRelationDO> listByOrgIdsFromDB(List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Lists.newArrayList();
        }
        List<BdCrmOrgUserRelationDO> relationDOList = Lists.newArrayList();
        Lists.partition(orgIds, 500)
                .forEach(
                        item -> relationDOList.addAll(bdCrmOrgUserRelationDao.listRelationByOrgIds(item))
                );
        return relationDOList;
    }


    @Override
    public List<BdCrmOrgUserRelationDO> pageRelationByOrgId(long orgId, int offset, int limit) {
        if (orgId <= 0) {
            return Lists.newArrayList();
        }
        return bdCrmOrgUserRelationDao.pageRelationByOrgId(orgId, offset, limit);
    }

    @Override
    public int countRelationByOrgId(long orgId) {
        if (orgId <= 0) {
            return 0;
        }
        List<BdCrmOrgUserRelationDO> fromRedis = getFromRedis(orgId);
        if (CollectionUtils.isNotEmpty(fromRedis)) {
            return fromRedis.size();
        } else {
            return bdCrmOrgUserRelationDao.countRelationByOrgId(orgId);
        }
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listByOrgIds(List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Lists.newArrayList();
        }
        List<BdCrmOrgUserRelationDO> relationDOList = Lists.newArrayList();
        for (Long orgId : Sets.newHashSet(orgIds)) {
            relationDOList.addAll(listRelationByOrgId(orgId));
        }
        return relationDOList;
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listByMisList(Collection<String> misList) {
        if (CollectionUtils.isEmpty(misList)) {
            return Lists.newArrayList();
        }
        List<String> distinctMisList = misList.stream().distinct().collect(Collectors.toList());
        List<BdCrmOrgUserRelationDO> relationDOS = Collections.synchronizedList(Lists.newArrayList());
        List<List<String>> onceQueryMisList = Lists.partition(distinctMisList, GeneralConstant.MAX_PAGE_SIZE);
        onceQueryMisList.parallelStream().forEach(item -> relationDOS.addAll(bdCrmOrgUserRelationDao.listByMisList(item)));
        return relationDOS;
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listByUniqueCodes(Collection<String> uniqueCodeList) {
        if (CollectionUtils.isEmpty(uniqueCodeList)) {
            return Lists.newArrayList();
        }
        List<String> distinctMisList = uniqueCodeList.stream().distinct().collect(Collectors.toList());
        List<BdCrmOrgUserRelationDO> relationDOS = Collections.synchronizedList(Lists.newArrayList());
        List<List<String>> onceQueryMisList = Lists.partition(distinctMisList, GeneralConstant.MAX_PAGE_SIZE);
        onceQueryMisList.parallelStream().forEach(item -> relationDOS.addAll(bdCrmOrgUserRelationDao.listByUniqueCodes(item)));
        return relationDOS;
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listByMisNameList(List<String> misNameList) {
        if (CollectionUtils.isEmpty(misNameList)) {
            return Lists.newArrayList();
        }
        List<String> distinctMisList = misNameList.stream().distinct().collect(Collectors.toList());
        List<BdCrmOrgUserRelationDO> relationDOS = Collections.synchronizedList(Lists.newArrayList());
        List<List<String>> onceQueryMisList = Lists.partition(distinctMisList, GeneralConstant.MAX_PAGE_SIZE);
        onceQueryMisList.parallelStream().forEach(item -> relationDOS.addAll(bdCrmOrgUserRelationDao.listByMisNameList(item)));
        return relationDOS;
    }

    @Override
    public List<BdCrmOrgUserRelationDO> getAllValidRelation() {
        return GrowthtoolCrusorQuery.queryByCursor((beginId, limit) -> bdCrmOrgUserRelationDao.listAllValidRelation((long) beginId, limit),
                BdCrmOrgUserRelationDO::getId);
    }


    @Override
    public List<BdCrmOrgUserRelationDO> listAllValidRelation(long id,int limit) {
        return bdCrmOrgUserRelationDao.listAllValidRelation(id, limit);
    }

    @Override
    public List<BdCrmOrgUserRelationDO> getUserByLikeUserName(String likeValue) {
        return bdCrmOrgUserRelationDao.getUserByLikeUserName(likeValue);
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listAutoDeleteMember(List<Long> orgIds, String startTime, String endTime) {
        return bdCrmOrgUserRelationDao.listAutoDeleteMember(orgIds, startTime, endTime);
    }

    @Override
    public BdCrmOrgUserRelationDO getByRelationId(long id) {
        return bdCrmOrgUserRelationDao.getByRelationId(id);
    }


    private void commitAddMemberLog(BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO, long adminUserId) {
        BdCrmOrgOptLogDO bdCrmOrgOptLogDO = new BdCrmOrgOptLogDO();
        bdCrmOrgOptLogDO.setAdminUserId(adminUserId);
        bdCrmOrgOptLogDO.setOptType(OrganizationMemberOptEnum.add_member.getCode());
        bdCrmOrgOptLogDO.setOrgId(bdCrmOrgUserRelationDO.getOrgId());
        bdCrmOrgOptLogDO.setMis(bdCrmOrgUserRelationDO.getMis());
        bdCrmOrgOptLogDO.setUniqueCode(bdCrmOrgUserRelationDO.getUniqueCode());
        bdCrmOrgOptLogDO.setComment("添加成员: " + String.format("新增成员%s", bdCrmOrgUserRelationDO.getMisName()));
        crmOptLogService.addOptLog(bdCrmOrgOptLogDO);
    }


    private void commitDeleteMemberLog(BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO, long adminUserId, @Nullable String consumeComment) {
        BdCrmOrgOptLogDO bdCrmOrgOptLogDO = new BdCrmOrgOptLogDO();
        bdCrmOrgOptLogDO.setAdminUserId(adminUserId);
        bdCrmOrgOptLogDO.setOptType(OrganizationMemberOptEnum.delete_member.getCode());
        bdCrmOrgOptLogDO.setOrgId(bdCrmOrgUserRelationDO.getOrgId());
        bdCrmOrgOptLogDO.setMis(bdCrmOrgUserRelationDO.getMis());
        bdCrmOrgOptLogDO.setUniqueCode(bdCrmOrgUserRelationDO.getUniqueCode());
        if (StringUtils.isNotBlank(consumeComment)) {
            bdCrmOrgOptLogDO.setComment(consumeComment);
        } else {
            bdCrmOrgOptLogDO.setComment("删除成员: " + String.format("删除成员%s", bdCrmOrgUserRelationDO.getMisName()));
        }
        crmOptLogService.addOptLog(bdCrmOrgOptLogDO);
    }
}
