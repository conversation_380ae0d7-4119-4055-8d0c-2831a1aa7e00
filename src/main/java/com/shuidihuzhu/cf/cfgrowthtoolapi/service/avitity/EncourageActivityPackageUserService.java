package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.EncourageActivityPackageUserDO;

import java.util.List;

/**
 * 激励活动-预圈选人群包(EncourageActivityPackageUser)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-23 15:32:57
 */
public interface EncourageActivityPackageUserService {

    void batchHandle(long activityId, List<EncourageActivityPackageUserDO> packageUserDOList);

    int update(EncourageActivityPackageUserDO encourageActivityPackageUser);

}
