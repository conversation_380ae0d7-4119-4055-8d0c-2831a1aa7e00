package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.EncourageActivityOperateLogDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.param.EncourageActivityQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.param.EncourageActivitySaveParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.vo.EncourageActivityListVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.vo.EncourageActivityResultVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OperatorLogVO;
import com.shuidihuzhu.client.param.PageResult;

import java.util.List;

public interface EncourageActivityService {

    CommonResultModel<EncourageActivityListVO> list(EncourageActivityQueryParam query);

    EncourageActivitySaveParam detail(long activityId);

    Boolean addOrUpdate(EncourageActivitySaveParam param);

    Boolean cancel(long activityId, long adminUserId);

    List<EncourageActivityResultVO> result(long activityId, int ruleId);

    List<EncourageActivityOperateLogDO> operateLog(long activityId);

}
