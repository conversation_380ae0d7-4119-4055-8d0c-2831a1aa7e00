package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.OrgOptParam;
import com.shuidihuzhu.common.web.model.Response;

/**
 * @author: fengxuan
 * @create 2020-07-14 12:01 下午
 **/
public interface ICrmSelfBuiltOrgWriteService {

    /**
     * 本接口针对单个 partnerNode 操作,如 (批量)增加节点, 不包含涉及多个partnerNode 的操作如: 迁移节点
     * @param orgOptParam
     * @return
     */
    Response<Boolean> addOrg(OrgOptParam orgOptParam);

    Response<Boolean> moveOrgNode(OrgOptParam orgOptParam);

    Response<Boolean> editCurrentNode(BdCrmOrganizationDO currentOrg, OrganizationMemberOptEnum optEnum, long adminUser);

    int updateCityAndCityNameById(long currentOrgId, int cityId, String cityName);
}
