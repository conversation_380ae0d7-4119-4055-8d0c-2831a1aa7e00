package com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.wx.CfWxOfficialAccountZombieFanDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx.IWxOfficialAccountZombieService;
import com.shuidihuzhu.cf.dao.wx.CfWxOfficialAccountZombieFanDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-05-24
 */

@Service
@Slf4j
public class WxOfficialAccountZombieServiceImpl implements IWxOfficialAccountZombieService {

    @Autowired
    private CfWxOfficialAccountZombieFanDao wxOfficialAccountZombieFanDao;

    @Override
    public List<CfWxOfficialAccountZombieFanDO> listFanByThirdType(Integer thirdType) {
        if (Objects.isNull(thirdType)){
            return Lists.newArrayList();
        }
        return wxOfficialAccountZombieFanDao.listFanByThirdType(thirdType);
    }

    @Override
    public int updateUseCount(List<CfWxOfficialAccountZombieFanDO> zombieFanList) {
        return wxOfficialAccountZombieFanDao.updateUseCount(zombieFanList);
    }

    @Override
    public Long getMaxPrimaryKey() {
        return wxOfficialAccountZombieFanDao.getMaxPrimaryKey();
    }

    @Override
    public int resetUseCount(long maxId) {
        return wxOfficialAccountZombieFanDao.resetUseCountById(maxId);
    }
}
