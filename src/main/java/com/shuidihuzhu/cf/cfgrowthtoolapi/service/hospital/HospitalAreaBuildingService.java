package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital;


import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalAreaBuildingDO;

import java.util.List;


/**
 * 院区-楼宇信息(HospitalAreaBuilding)表服务接口
 *
 * <AUTHOR>
 * @since 2021-01-04 17:25:36
 */
public interface HospitalAreaBuildingService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    HospitalAreaBuildingDO queryById(long id);


    List<HospitalAreaBuildingDO> listByHospitalCode(List<String> vhospitalCodes);

    List<HospitalAreaBuildingDO> listByHospitalCodeAndBuildingNameLike(String vhospitalCode, String buildingName);

    List<HospitalAreaBuildingDO> listByHospitalCodeAndBuildingName(String vhospitalCode, String buildingName);

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    List<HospitalAreaBuildingDO> queryAllByLimit(int offset, int limit);

    /**
     * 新增数据
     *
     * @param hospitalAreaBuilding 实例对象
     */
    int insert(HospitalAreaBuildingDO hospitalAreaBuilding);

    /**
     * 修改数据
     *
     * @param hospitalAreaBuilding 实例对象
     * @return 实例对象
     */
    int update(HospitalAreaBuildingDO hospitalAreaBuilding);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);

    List<HospitalAreaBuildingDO> listByIds(List<Long> ids);

    //更新最大修改记录
    void updateMaxChangeId(int buildingId, long maxChangeId);

    int updateBuildingFloorArea(int id, String buildingFloorArea);

    /**
     * 删除数据也能查询到
     *
     * @param id
     * @return
     */
    HospitalAreaBuildingDO getById(long id);

}