package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.GrowthSimpleUserVo;
import com.shuidihuzhu.client.auth.saas.model.dto.SimpleGroupVo;
import com.shuidihuzhu.client.auth.saas.model.dto.SimpleUserVo;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.client.cf.growthtool.model.OrgInfoModel;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: fengxuan
 * @create 2020-06-16 14:59
 **/
public class OrgConvertUtil {


    public static BdCrmVolunteerOrgnizationSimpleModel convertTSimpleModel(BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO, BdCrmOrganizationDO organizationDO, String path) {
        BdCrmVolunteerOrgnizationSimpleModel simpleModel = new BdCrmVolunteerOrgnizationSimpleModel();
        //simpleModel.setMisId(); //sea 后台的 mis_id 暂时不知道使用地方
        simpleModel.setMis(bdCrmOrgUserRelationDO.getMis());
        simpleModel.setMisName(bdCrmOrgUserRelationDO.getMisName());
        simpleModel.setOrgName(organizationDO.getOrgName());
        simpleModel.setUniqueCode(bdCrmOrgUserRelationDO.getUniqueCode());
        if (StringUtils.isNotBlank(path)) {
            simpleModel.setOrgName(path);
            String[] splitPath = StringUtils.split(path, "-");
            // 设置当前组织上级组织名称
            if (splitPath.length > 2) {
                simpleModel.setParentOrgName(splitPath[splitPath.length - 2]);
            }
        }

        simpleModel.setShortOrgName(organizationDO.getOrgName());
        simpleModel.setOrgId((int) bdCrmOrgUserRelationDO.getOrgId());
        return simpleModel;
    }

    public static OrgInfoModel convertTOrgInfoModel(BdCrmOrganizationDO bdCrmOrganizationDO) {
        OrgInfoModel orgInfoModel = new OrgInfoModel();
        orgInfoModel.setOrgName(bdCrmOrganizationDO.getOrgName());
        orgInfoModel.setOrgId((int) bdCrmOrganizationDO.getId());
        orgInfoModel.setOrgAttribute(bdCrmOrganizationDO.getOrgAttribute());
        orgInfoModel.setParentId((int) bdCrmOrganizationDO.getParentId());
        return orgInfoModel;
    }


    public static SimpleGroupVo convertTSimpleOrgVo(BdCrmOrganizationDO bdCrmOrganizationDO) {
        SimpleGroupVo simpleOrgVo = new SimpleGroupVo();
        simpleOrgVo.setGroupBizId(bdCrmOrganizationDO.getId());
        simpleOrgVo.setGroupName(bdCrmOrganizationDO.getOrgName());
        return simpleOrgVo;
    }

    public static SimpleUserVo convertTSimpleUserVo(BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO) {
        SimpleUserVo simpleUserVo = new SimpleUserVo();
        simpleUserVo.setName(bdCrmOrgUserRelationDO.getMisName());
        simpleUserVo.setMis(bdCrmOrgUserRelationDO.getMis());
        return simpleUserVo;
    }

    public static GrowthSimpleUserVo convertTGrowthSimpleUserVo(BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO) {
        GrowthSimpleUserVo simpleUserVo = new GrowthSimpleUserVo();
        simpleUserVo.setName(bdCrmOrgUserRelationDO.getMisName());
        simpleUserVo.setMis(bdCrmOrgUserRelationDO.getMis());
        simpleUserVo.setUniqueCode(bdCrmOrgUserRelationDO.getUniqueCode());
        return simpleUserVo;
    }


    /**
     * 尽量填充,先不设置人员状态
     *
     * @param bdCrmOrgUserRelationDO
     * @param organizationDO
     * @return
     */
    public static CfBdcrmVolunteerOrgnizationCopyDO convertTCopyDo(BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO, BdCrmOrganizationDO organizationDO) {
        CfBdcrmVolunteerOrgnizationCopyDO copyDO = new CfBdcrmVolunteerOrgnizationCopyDO();
        copyDO.setId(bdCrmOrgUserRelationDO.getId());
        copyDO.setCreateTime(bdCrmOrgUserRelationDO.getCreateTime());
        copyDO.setMis(bdCrmOrgUserRelationDO.getMis());
        copyDO.setMisName(bdCrmOrgUserRelationDO.getMisName());
        copyDO.setOrgName(organizationDO.getOrgName());
        copyDO.setOrgId((int) bdCrmOrgUserRelationDO.getOrgId());
        //copyDO.setAccountStatus();
        //copyDO.setXrxsEmployeeId();
        //copyDO.setEncryptPhone();
        //copyDO.setEntryDate();
        //copyDO.setResignDate();
        //copyDO.setXrxsOrgId();
        //copyDO.setXrxsOrgName();
        return copyDO;
    }
}
