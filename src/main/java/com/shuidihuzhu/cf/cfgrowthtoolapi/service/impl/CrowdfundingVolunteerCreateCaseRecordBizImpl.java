package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfClewtrackDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMasterForGrowthtoolDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.AccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrowdfundingVolunteerCreateCaseRecordExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCaseAttributionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.IRegisterBdCrmClewFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdCrmScanRecordModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.CrowdfundingUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.dao.CrowdfundingVolunteerCreateCaseRecordDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CrowdfundingVolunteerCreateCaseRecordBiz;
import com.shuidihuzhu.cf.lion.client.risk.model.CrowdfundingVolunteerCreateCaseRecordExtFeginDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingVolunteerCreateCaseRecord;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.FuwuMemberInfo;
import com.shuidihuzhu.client.model.enums.CaseEndReasonEnum;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by lixiaoshuang on 2018/6/29.
 */
@Service
@Slf4j
public class CrowdfundingVolunteerCreateCaseRecordBizImpl implements CrowdfundingVolunteerCreateCaseRecordBiz {
    @Autowired
    private CrowdfundingVolunteerCreateCaseRecordDao crowdfundingVolunteerCreateCaseRecordDao;
    @Autowired
    private ICfMasterForGrowthtoolDelegate cfMasterForGrowthtoolDelegate;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private IRegisterBdCrmClewFacade registerClewFacadeImpl;

    @Autowired
    private CrowdfundingVolunteerCreateCaseRecordBiz createCaseRecordBiz;

    @Autowired
    private ICfClewtrackDelegate cfClewtrackDelegate;

    @Resource
    private AccountServiceDelegate accountServiceDelegate;

    @Override
    public int insertVolunteerCreateCaseRecord(CrowdfundingVolunteerCreateCaseRecord crowdfundingVolunteerCreateCaseRecord, long userId,String encryptPhone) {
        int result = 0;
        try {
            CrowdfundingVolunteerCreateCaseRecordExtDO domain = new CrowdfundingVolunteerCreateCaseRecordExtDO(crowdfundingVolunteerCreateCaseRecord,
                    encryptPhone, CfBdCaseAttributionDO.AttributionTypeEnum.SAOMA.getType());
            cfMasterForGrowthtoolDelegate.insertVolunteerCreateCaseRecord(domain);
            result = domain.getId();
        } catch (Exception e) {
            log.error("CrowdfundingVolunteerCreateCaseRecordBizImpl insertVolunteerCreateCaseRecord failure",e);
        }
        crowdfundingVolunteerCreateCaseRecord.setCreateTime(new Date());
        //判断下是否有线上保护期
        FuwuMemberInfo fuwuMemberInfo = null;
        if (StringUtils.isNotEmpty(encryptPhone)) {
            fuwuMemberInfo = cfClewtrackDelegate.getFuwuMemberInfo(shuidiCipher.decrypt(encryptPhone));
            log.info("扫码是否有线上服务:{}", fuwuMemberInfo);
        }
        if (fuwuMemberInfo == null) {
            redissonHandler.setEX(GeneralConstant.VOLUNTEER_KEY + userId, crowdfundingVolunteerCreateCaseRecord, GrowthtoolUtil.TIMEMILLS);
        }
        return result;
    }

    @Override
    public int insertVolunteerCreateCaseRecordByUrl(CrowdfundingVolunteerCreateCaseRecord crowdfundingVolunteerCreateCaseRecord, long userId) {
        int result = 0;
        try {
            CrowdfundingVolunteerCreateCaseRecordExtDO domain = new CrowdfundingVolunteerCreateCaseRecordExtDO(crowdfundingVolunteerCreateCaseRecord,
                    null,
                    CfBdCaseAttributionDO.AttributionTypeEnum.DAILURU_QUEREN.getType());
            result = cfMasterForGrowthtoolDelegate.insertVolunteerCreateCaseRecord(domain);
        } catch (Exception e) {
            log.error("CrowdfundingVolunteerCreateCaseRecordBizImpl insertVolunteerCreateCaseRecord failure",e);
        }
        crowdfundingVolunteerCreateCaseRecord.setCreateTime(new Date());
        redissonHandler.setEX(GeneralConstant.VOLUNTEER_KEY_URL + userId, crowdfundingVolunteerCreateCaseRecord, GrowthtoolUtil.TIMEMILLS);
        return result;
    }

    @Override
    public int insertVolunteerCreateCaseRecordWithPhone(long userId, String volunteerUnique, String phone) {
        CrowdfundingVolunteerCreateCaseRecordExtDO caseRecordExtDO = new CrowdfundingVolunteerCreateCaseRecordExtDO();
        int result = 0;
        try {
            caseRecordExtDO.setUserId(userId);
            caseRecordExtDO.setVolunteerUnique(volunteerUnique);
            caseRecordExtDO.setEncryptPhone(ShuidiCipherUtils.encrypt(phone));
            caseRecordExtDO.setAttributionType(CfBdCaseAttributionDO.AttributionTypeEnum.DAILURU_FEI_QUEREN.getType());
            result = cfMasterForGrowthtoolDelegate.insertVolunteerCreateCaseRecordWithPhone(caseRecordExtDO);
        } catch (Exception e) {
            log.error("CrowdfundingVolunteerCreateCaseRecordBizImpl insertVolunteerCreateCaseRecord failure",e);
        }
        caseRecordExtDO.setCreateTime(new Date());
        redissonHandler.setEX(GeneralConstant.VOLUNTEER_KEY_PHONE + phone, caseRecordExtDO, GrowthtoolUtil.TIMEMILLS);
        return result;
    }

    @Override
    public long getVolunteerKeyTimemills() {
        return GrowthtoolUtil.TIMEMILLS;
    }

    @Override
    public void transferCfVolunteer(long fromUserId, long toUserId, String userMobile, String channel){
        try {
            if (fromUserId == toUserId) {
                return;
            }
            CrowdfundingVolunteerCreateCaseRecord crowdfundingVolunteerCreateCaseRecord =
                    redissonHandler.get(GeneralConstant.VOLUNTEER_KEY + fromUserId, CrowdfundingVolunteerCreateCaseRecord.class);
            log.info("CrowdfundingV4InfoUpdateController transferCfVolunteer fromUserId:{},toUserId:{} get crowdfundingVolunteerCreateCaseRecord:{}",
                    fromUserId, toUserId, JSON.toJSONString(crowdfundingVolunteerCreateCaseRecord));
            if (crowdfundingVolunteerCreateCaseRecord == null) {
                return;
            }
            CrowdfundingVolunteerCreateCaseRecord newRecord = new CrowdfundingVolunteerCreateCaseRecord();
            newRecord.setUserId(toUserId);
            newRecord.setVolunteerUnique(crowdfundingVolunteerCreateCaseRecord.getVolunteerUnique());
            String encryptMobile = oldShuidiCipher.aesEncrypt(userMobile);
            int caseRecordId = this.insertVolunteerCreateCaseRecord(newRecord, toUserId, encryptMobile);
            log.info("CrowdfundingV4InfoUpdateController transferCfVolunteer fromUserId:{},toUserId:{},reset crowdfundingVolunteerCreateCaseRecord:{}",
                    fromUserId, toUserId, JSON.toJSONString(newRecord));
            String scanTime = DateUtil.formatDateTime(new Date());
            //用户当前账号已经绑定手机号
            registerClewFacadeImpl.sendMq2CfClewtrackUseChannelAndPhone(toUserId, crowdfundingVolunteerCreateCaseRecord.getVolunteerUnique(), userMobile, channel, "", null, String.valueOf(caseRecordId), scanTime);
        } catch (Exception e) {
            log.error("转移线下筹款顾问失败,fromUserId:{},toUserId:{}", fromUserId, toUserId, e);
        }
    }

    @Override
    public String getUniqueCodeByUserId(Long userId) {
        Map<String, Object> result = crowdfundingVolunteerCreateCaseRecordDao.getUniqueCodeByUserId(userId);
        if(result == null || !result.containsKey("uniqueCode")){
            return null;
        }
        return String.valueOf(result.get("uniqueCode"));
    }

    @Override
    public CrowdfundingVolunteerCreateCaseRecordExtDO getByUserIdWithVolunteerUnique(long userId, String volunteerUnique){
        return crowdfundingVolunteerCreateCaseRecordDao.getByUserIdWithVolunteerUnique(userId, volunteerUnique);
    }

    @Override
    public CrowdfundingVolunteerCreateCaseRecordExtDO getByEncryptPhoneWithVolunteerUnique(String encryptPhone, String volunteerUnique) {
        return crowdfundingVolunteerCreateCaseRecordDao.getByEncryptPhoneWithVolunteerUnique(encryptPhone, volunteerUnique);
    }

    @Override
    public int updateNoClewCreateDesc(long id, String noClewCreateDesc) {
        return cfMasterForGrowthtoolDelegate.updateNoClewCreateDesc(id, noClewCreateDesc);
    }

    @Override
    public int updateClewId(long id, long clewId) {
        return cfMasterForGrowthtoolDelegate.updateClewId(id,clewId);
    }

    @Override
    public CrowdfundingVolunteerCreateCaseRecordExtDO getRecordByUserId(long userId) {
        return crowdfundingVolunteerCreateCaseRecordDao.getSaomaRecordByUserId(userId);
    }

    @Override
    public long getScanCount(String phone, String uniqueCode, Date startTime, Date endTime) {
        String encryptPhone = "";
        if (StringUtils.isNotEmpty(phone)){
            encryptPhone = oldShuidiCipher.aesEncrypt(phone);
        }
        return crowdfundingVolunteerCreateCaseRecordDao.getSaomaScanCount(encryptPhone, uniqueCode, startTime, endTime);
    }

    @Override
    public void fillPhone(long id, String encryptPhone, int fillPhoneScene) {
        CrowdfundingVolunteerCreateCaseRecordExtFeginDO createCaseRecordExtFeginDO = new CrowdfundingVolunteerCreateCaseRecordExtFeginDO();
        createCaseRecordExtFeginDO.setId((int) id);
        createCaseRecordExtFeginDO.setEncryptPhone(encryptPhone);
        createCaseRecordExtFeginDO.setFillPhoneScene(fillPhoneScene);
        cfMasterForGrowthtoolDelegate.updateCreateCaseRecord(createCaseRecordExtFeginDO);
    }

    @Override
    public void fillPhoneByJob() {
        DateTime endDateTime = DateTime.now().withTimeAtStartOfDay().minusDays(1);
        //查询扫码且手机号为空的记录
        List<CrowdfundingVolunteerCreateCaseRecordExtDO> recordExtList = crowdfundingVolunteerCreateCaseRecordDao.listNoPhoneRecord(endDateTime.minusDays(1).toDate(), endDateTime.toDate());
        recordExtList.forEach(item -> {
            if (item.getUserId() > 0) {
                //没有流转给bd
                UserInfoModel userInfo = accountServiceDelegate.getUserInfoModelByUserId(item.getUserId());
                if (userInfo != null && StringUtils.isNotEmpty(userInfo.getCryptoMobile())) {
                    fillPhone(item.getId(), userInfo.getCryptoMobile(), CfBdCaseAttributionDO.FillPhoneScenEnum.job_fill.getCode());
                }
            }
        });
    }

    @Override
    public List<BdCrmScanRecordModel> listScanRecord(String phone, String uniqueCode, Date startTime, Date endTime, int pageNo, int pageSize) {
        String encryptPhone = "";
        if (StringUtils.isNotEmpty(phone)){
            encryptPhone = oldShuidiCipher.aesEncrypt(phone);
        }
        int offset = (pageNo-1)*pageSize;
        List<CrowdfundingVolunteerCreateCaseRecordExtDO> recordExtList = crowdfundingVolunteerCreateCaseRecordDao.listSaomaScanRecord(encryptPhone, uniqueCode, startTime, endTime, offset, pageSize);
        List<BdCrmScanRecordModel> result = Lists.newArrayList();
        recordExtList.forEach(item ->{
            BdCrmScanRecordModel bdCrmScanRecordModel = new BdCrmScanRecordModel();
            bdCrmScanRecordModel.setPhone(CrowdfundingUtil.getTelephoneMask(shuidiCipher.decrypt(item.getEncryptPhone())));
            if (item.getClewId() > 0){
                bdCrmScanRecordModel.setClewId(item.getClewId().intValue());
                bdCrmScanRecordModel.setHasClewCreate(true);
            }else{
                bdCrmScanRecordModel.setClewId(null);
                bdCrmScanRecordModel.setHasClewCreate(false);
            }
            bdCrmScanRecordModel.setNoClewCreateDesc(item.getNoClewCreateDesc());
            bdCrmScanRecordModel.setScanTime(DateUtil.formatDateTime(item.getCreateTime()));
            result.add(bdCrmScanRecordModel);
        });
        return result;
    }

    @Override
    public List<CrowdfundingVolunteerCreateCaseRecordExtDO> listSaomaRecordByUserId(long userId, String startTime, String endTime) {
        if (userId <= 0 || StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return Lists.newArrayList();
        }
        return crowdfundingVolunteerCreateCaseRecordDao.listSaomaRecordByUserId(userId, startTime, endTime);
    }
}
