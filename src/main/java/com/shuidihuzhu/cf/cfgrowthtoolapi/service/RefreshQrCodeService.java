package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IWorkWeiXinDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.VolunteerQrCodeTemplate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.ICfVolunteerFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.volunteer.VolunteerSyncService;
import com.shuidihuzhu.client.cf.growthtool.client.CfGenerateQRCodeFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Base64;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2024-10-09 11:25
 **/
@Slf4j
@Service
public class RefreshQrCodeService {
    @Autowired
    private VolunteerSyncService volunteerSyncService;
    @Autowired
    private QRCodeService qrCodeService;
    @Autowired
    private ICfVolunteerFacade cfVolunteerFacade;
    @Autowired
    private IWorkWeiXinDelegate weiXinDelegate;
    @Autowired
    private VolunteerQrCodeTemplateService volunteerQrCodeTemplateService;

    public static final String base64Image = "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";

    public static ByteArrayInputStream bais;

    public static BufferedImage originalImage;

    public static Image scaledImage;

    static {
        try {
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);
            bais = new ByteArrayInputStream(imageBytes);
            originalImage = ImageIO.read(bais);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 调整图像大小为180x180
        scaledImage = originalImage.getScaledInstance(180, 180, Image.SCALE_SMOOTH);
    }

    public void refreshAllVolunteer() {
        List<CrowdfundingVolunteer> volunteers = volunteerSyncService.listAllOnWorkVolunteer();
        for (CrowdfundingVolunteer volunteer : volunteers) {
            //重新生成二维码
            if (StringUtils.isNotBlank(volunteer.getQrCode()) && StringUtils.isNotBlank(volunteer.getUniqueCode())) {
                VolunteerQrCodeTemplate exitCodeTemplate = volunteerQrCodeTemplateService.queryByUniqueCode(volunteer.getUniqueCode());
                String newQrCode = "";
                if (exitCodeTemplate != null) {
                    newQrCode = exitCodeTemplate.getQrCode();
                } else {
                    newQrCode = createNewQrCode(volunteer);
                }
                log.info("CrowdfundingVolunteerController generateQRCode qrCodeWithLogo:{}", newQrCode);
                if (StringUtils.isBlank(newQrCode)) {
                    log.info("二维码生成异常,姓名:{},uniqueCode:{}", volunteer.getVolunteerName(), volunteer.getUniqueCode());
                    continue;
                }
                cfVolunteerFacade.updateQrCode(newQrCode, volunteer.getId());
                //先暂存到临时表中
                //VolunteerQrCodeTemplate volunteerQrCodeTemplate = new VolunteerQrCodeTemplate();
                //volunteerQrCodeTemplate.setQrCode(newQrCode);
                //volunteerQrCodeTemplate.setUniqueCode(volunteer.getUniqueCode());
                //if (exitCodeTemplate != null) {
                //    exitCodeTemplate.setQrCode(newQrCode);
                //    volunteerQrCodeTemplateService.update(exitCodeTemplate);
                //    continue;
                //}
                //volunteerQrCodeTemplateService.insert(volunteerQrCodeTemplate);
            }
        }
    }


    public String createNewQrCode(CrowdfundingVolunteer volunteer) {
        String base64Logo = drawBase64Logo(volunteer.getVolunteerName());
        if (StringUtils.isBlank(base64Logo)) {
            log.info("生成base64Logo异常,姓名:{},uniqueCode:{}", volunteer.getVolunteerName(), volunteer.getUniqueCode());
            return "";
        }
        String scene = "cf_volunteer_" + volunteer.getUniqueCode();
        String ticket = weiXinDelegate.generateQrcode(scene);
        String qrCode = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket;
        //调用生成带有logo二维码的方法
        String qrCodeWithLogo = qrCodeService.generateQrCode(qrCode, base64Logo, volunteer.getUniqueCode()).getData();
        if (Strings.isEmpty(qrCodeWithLogo)) {
            log.info("带有logo二维码异常,姓名:{},uniqueCode:{}", volunteer.getVolunteerName(), volunteer.getUniqueCode());
            return "";
        }
        return qrCodeWithLogo;
    }


    private static String getName(String name) {
        if (StringUtils.isNotBlank(name) && name.length() <= 5) {
            return name;
        }
        String[] parts = name.split("·");
        String firstPart = parts[0];
        if (StringUtils.isNotBlank(firstPart) && firstPart.length() <= 5) {
            return firstPart;
        }
        return "水滴";
    }

    public static String drawBase64Logo(String volunteerName) {
        volunteerName = getName(volunteerName);
        try {
            // 创建一个180x180的图像
            BufferedImage resizedImage = new BufferedImage(180, 180, BufferedImage.TYPE_INT_ARGB);
            // 绘制图像（这里假设你有一个Image对象img）
            //Image img = Toolkit.getDefaultToolkit().getImage("path/to/your/image.jpg");
            Graphics2D g2d = resizedImage.createGraphics();
            g2d.drawImage(scaledImage, 0, 0, null);
            g2d.dispose();

            g2d = resizedImage.createGraphics();
            // 设置字体
            // 创建颜色对象
            Color customColor = new Color(0, 113, 254);
            g2d.setFont(new Font("PingFangSC-Medium", Font.BOLD, 34));
            g2d.setColor(customColor);

            // 设置文本对齐方式
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            //g2d.setTransform(true);
            // 创建颜色对象
            g2d.setPaint(customColor);

            // 绘制文本
            FontMetrics fm = g2d.getFontMetrics();
            int left = resizedImage.getWidth() / 2;
            int top = resizedImage.getHeight() / 2 + 45;

            g2d.drawString(volunteerName, left - fm.stringWidth(volunteerName) / 2, top);

            // 释放资源
            g2d.dispose();

            // 将图像转换为Base64编码
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(resizedImage, "png", baos);
            byte[] imageInByte = baos.toByteArray();
            return Base64.getEncoder().encodeToString(imageInByte);

        } catch (IOException e) {
            log.error("加工logo异常", e);
        }
        return null;
    }


    public static void main(String[] args) {
        String s = drawBase64Logo("靳路");
        System.out.println(s);
    }
}
