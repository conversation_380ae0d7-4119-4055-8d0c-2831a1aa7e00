package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmMsgDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmMsgVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.MsgCategoryGroup;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/12/5 5:18 PM
 */
public interface ICfBdCrmMsgService {
    int saveCfBdCrmMsg(String title, String subTitle, String content, String mis, String uniqueCode);

    int updateReadStatusByUniqueCode(long id, String uniqueCode, Integer categoryId);

    int updateReadStatusByUniqueCode(String uniqueCode, Date before1Month, Integer categoryId);

    List<CfBdCrmMsgVo> listByUniqueCode(String uniqueCode, int offset, int pageSize, Integer categoryId, Date startDate, Date endDate);

    long countNoReadMsgByUniqueCode(String uniqueCode, Date createTime);

    CfBdCrmMsgDO getCfBdCrmMsgDOById(long id);

    List<CfBdCrmMsgDO> listByIds(List<Long> ids);

    List<MsgCategoryGroup> groupByCategory(String uniqueCode, Date createTime);
}
