package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiManulInputCaseDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-03
 */
public interface ICfKpiManulInputCaseService {

    /**
     * 批量插入
     * @param kpiManulInputCaseDoList
     */
    void addBatch(List<CfKpiManulInputCaseDO> kpiManulInputCaseDoList);

    List<CfKpiManulInputCaseDO> listManulInputCase(String monthKey);

    List<CfKpiManulInputCaseDO> listByCaseId(List<Long> caseIds);

    int countOperateDetail(Date startTime);

    List<CfKpiManulInputCaseDO> pageOperateDetail(Date startTime, int offset, int limit);
}
