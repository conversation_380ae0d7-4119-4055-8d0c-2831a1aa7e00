package com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.CfBdCaseDonateLevelConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate.IBdCaseDonateLevelConfigService;
import com.shuidihuzhu.cf.dao.donate.CfBdCaseDonateLevelConfigDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2022-06-30 15:23
 **/
@Service
public class BdCaseDonateLevelConfigServiceImpl implements IBdCaseDonateLevelConfigService {

    @Resource
    private CfBdCaseDonateLevelConfigDao levelConfigDao;


    @Override
    public List<CfBdCaseDonateLevelConfigDO> listByCityConfigIds(List<Long> cityConfigIds) {
        if (CollectionUtils.isEmpty(cityConfigIds)) {
            return Lists.newArrayList();
        }
        return levelConfigDao.listByCityConfigIds(cityConfigIds);
    }
}
