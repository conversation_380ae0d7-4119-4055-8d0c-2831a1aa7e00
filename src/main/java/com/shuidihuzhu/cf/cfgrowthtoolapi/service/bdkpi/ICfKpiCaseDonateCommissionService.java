package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseDonateCommissionDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiCaseCommisionVO;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-04
 */
public interface ICfKpiCaseDonateCommissionService {

    /**
     * 获取案例捐单提成总数
     *
     * @param monthKey
     * @param uniqueCode
     * @return
     */
    long queryCaseDonateCommissionCount(String monthKey, String uniqueCode, long validAmount, int validDonateNum);

    /**
     * 获取案例捐单提成明细
     *
     * @param monthKey
     * @param uniqueCode
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<CfKpiCaseCommisionVO> queryCaseDonateCommissionDetail(String monthKey, String uniqueCode, long validAmount, int validDonateNum, int pageNo, int pageSize);

    OpResult<List<CfKpiCaseCommisionVO>> queryCaseCommissionDetail(String monthkey, String uniqueCode);

    int batchSaveOrUpdate(List<CfKpiCaseDonateCommissionDo> kpiCaseDonateCommissionDoList, String uniqueCode, String dateKey);

    List<CfKpiCaseDonateCommissionDo> listCaseDonateCommission(String monthKey, List<String> uniqueCodeList);
}
