package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPerformanceCityRuleDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiStatVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiBdScoreTempService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiPerformanceCityRuleService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiPerformanceCityRuleDao;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2020-08-03 14:24
 */
@Service
@Slf4j
public class CfKpiPerformanceCityRuleServiceImpl implements ICfKpiPerformanceCityRuleService {
    @Resource
    private CfKpiPerformanceCityRuleDao cfKpiPerformanceCityRuleDao;
    @Autowired
    private ICfKpiBdScoreTempService cfKpiBdScoreTempService;
    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;
    @Autowired
    private ICrmSelfBuiltOrgReadService selfBuiltOrgReadService;

    @Override
    public CfKpiPerformanceCityRuleDO getPerformanceCityname(String monthKey, Integer cityType) {
        return cfKpiPerformanceCityRuleDao.getPerformanceCityname(monthKey,cityType);
    }

    @Override
    public CfKpiPerformanceCityRuleDO getPerformance(CfKpiPerformanceCityRuleDO cfKPIPerformance) {
        CfKpiPerformanceCityRuleDO result = null;
        if (cfKPIPerformance.getId()>0){
            result = this.getPerformance(cfKPIPerformance.getId());
        }
        if (result!=null){
            return result;
        }
        //单个城市+特定省份 可以存在多个值
        if (CfBdKpiEnums.CityTypeEnum.DAN_GE.getType().equals(cfKPIPerformance.getCityType()) || CfBdKpiEnums.CityTypeEnum.SP_PROVINCE.getType().equals(cfKPIPerformance.getCityType())) {
            return result;
        }else{
            result = cfKpiPerformanceCityRuleDao.getPerformance(cfKPIPerformance.getMonthKey(),cfKPIPerformance.getLevel(),cfKPIPerformance.getCityType());
            log.info(this.getClass().getName()+" getPerformance param:{} result:{}",cfKPIPerformance,result);
        }
        return result;
    }
    @Override
    public OpResult<CfKpiPerformanceCityRuleDO> getPerformanceByCityName(String monthKey, Integer level, Integer cityType, String cityName) {
        CfKpiPerformanceCityRuleDO result = cfKpiPerformanceCityRuleDao.getPerformanceByCityTypeWithLikeCityName(level, monthKey, cityType, cityName);
        log.info(this.getClass().getName()+" getPerformanceByCityName param:{} result:{}",String.join(GeneralConstant.splitChar,monthKey,
                level+"",
                cityName),result);
        if (result==null){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.NOT_FOUND_PERFORMANCE);
        }
        return OpResult.createSucResult(result);
    }

    @Override
    public OpResult<Long> saveOrUpdatePerformance(CfKpiPerformanceCityRuleDO data) {
        if (data.getId() > 0) {
            // 判断 当月是否已经打分，如果已经打分 则不允许修改规则
            OpResult opResult = cfKpiBdScoreTempService.checkIsBdScore(data.getLevel(), data.getMonthKey());
            log.info(this.getClass().getName() + " checkIsBdScore result:{}", opResult);
            if (opResult.isFail()) {
                return OpResult.createFailResult(opResult.getErrorCode());
            }
            cfKpiPerformanceCityRuleDao.update(data);
            return OpResult.createSucResult(data.getId());
        }
        cfKpiPerformanceCityRuleDao.insert(data);
        handleCityNameExist(data);
        return OpResult.createSucResult(data.getId());
    }

    private void handleCityNameExist(CfKpiPerformanceCityRuleDO data){
        if (data.getPerformanceType() == CfBdKpiEnums.PerformanceTypeEnum.factor_type.getCode()) {
            Predicate<CfKpiPerformanceCityRuleDO> predicate = v -> v.getPerformanceType() == CfBdKpiEnums.PerformanceTypeEnum.factor_type.getCode();
            handleDuplicateNameCity(data, predicate);
            return;
        }
        Predicate<CfKpiPerformanceCityRuleDO> notFactorTypePredicate = v -> v.getPerformanceType() != CfBdKpiEnums.PerformanceTypeEnum.factor_type.getCode();
        handleDuplicateNameCity(data, notFactorTypePredicate);
    }


    private void handleDuplicateNameCity(CfKpiPerformanceCityRuleDO data, Predicate<CfKpiPerformanceCityRuleDO> predicate) {
        for (String cityName : data.getCityNameStr().split(GeneralConstant.splitChar)) {
            List<CfKpiPerformanceCityRuleDO> cfKpiPerformanceCityRuleDOList = cfKpiPerformanceCityRuleDao.getPerformanceLikeCityNames(data.getLevel(), data.getMonthKey(), cityName, data.getId());
            List<CfKpiPerformanceCityRuleDO> mayDuplicateRuleList = cfKpiPerformanceCityRuleDOList.stream()
                    .filter(predicate)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mayDuplicateRuleList)) {
                continue;
            }
            // 处理 重复的数据
            for (CfKpiPerformanceCityRuleDO cfKpiPerformanceCityRuleDO : mayDuplicateRuleList) {
                ArrayList<String> cityNameList = Lists.newArrayList(cfKpiPerformanceCityRuleDO.getCityNameStr().split(GeneralConstant.splitChar));
                cityNameList.remove(cityName);
                if (CollectionUtils.isEmpty(cityNameList)){
                    log.info(this.getClass().getName()+" needDeleteRuleIdList result:{}", JSON.toJSONString(cfKpiPerformanceCityRuleDO));
                    cfKpiPerformanceCityRuleDao.updateIsDelete(Lists.newArrayList(cfKpiPerformanceCityRuleDO.getId()), BooleanUtils.toInteger(true));
                }else {
                    cfKpiPerformanceCityRuleDO.setCityNameStr(cityNameList.stream().collect(Collectors.joining(GeneralConstant.splitChar)));
                    log.info(this.getClass().getName()+" needUpdateRuleDOList result:{}",JSON.toJSONString(cfKpiPerformanceCityRuleDO));
                    cfKpiPerformanceCityRuleDao.batchUpdateCityNameStr(Lists.newArrayList(cfKpiPerformanceCityRuleDO));
                }
            }
        }
    }

    @Override
    public CfKpiPerformanceCityRuleDO getPerformance(Long id) {
        return cfKpiPerformanceCityRuleDao.getPerformanceById(id);
    }

    @Override
    public CommonResultModel<CfKPIPerformanceVO> getPerformanceList(KpiSearchParam searchParam) {
        CommonResultModel<CfKPIPerformanceVO> commonResultModel = new CommonResultModel();
        int offset = (searchParam.getPageNo()-1)*searchParam.getPageSize();
        commonResultModel.setModelList(cfKpiPerformanceCityRuleDao.getPerformanceList(searchParam.getLevel(),
                searchParam.getCityType(),
                searchParam.getCityName(),
                searchParam.getStatus(),
                searchParam.getMonthKey(),
                searchParam.getPerformanceType(),
                offset,searchParam.getPageSize()));
        commonResultModel.setTotal(cfKpiPerformanceCityRuleDao.getPerformanceListCount(searchParam.getLevel(),
                searchParam.getCityType(),
                searchParam.getCityName(),
                searchParam.getStatus(),
                searchParam.getMonthKey(),
                searchParam.getPerformanceType()));
        return commonResultModel;
    }


    @Override
    public List<CfKpiPerformanceCityRuleDO> listAllRule(String monthKey) {
        return cfKpiPerformanceCityRuleDao.listAllRule(monthKey);
    }

    @Override
    public void changeStatusOtherMonthKey(String monthKey) {
        cfKpiPerformanceCityRuleDao.updateStatusByMonthkey(monthKey,CfBdKpiEnums.RuleStatusEnum.SHENGXIAO.getStatus());
        List<Long> ids = cfKpiPerformanceCityRuleDao.getStatusOf1(monthKey);
        log.info(this.getClass().getName()+" getStatusOf1 result:{}",ids);
        if (CollectionUtils.isNotEmpty(ids)){
            cfKpiPerformanceCityRuleDao.updateStatus(ids, CfBdKpiEnums.RuleStatusEnum.WEI_SHENGXIAO.getStatus());
        }
    }

    @Override
    public int repairePreformanceCustomType(long id){
        CfKpiPerformanceCityRuleDO performance = this.getPerformance(id);
        if (performance==null){
            log.info("repairePreformanceCustomType id :{} 不存在",id);
            return 0;
        }
        CfKPIPerformanceRuleModel cfKPIPerformanceRuleModel = performance.showAssessmentRule();
        List<CustomPerformanceModel> customPerformance = cfKPIPerformanceRuleModel.getCustomPerformance();
        if (CollectionUtils.isEmpty(customPerformance)){
            log.info("repairePreformanceCustomType id :{} 不需要修复",id);
            return 0;
        }
        customPerformance.forEach(customPerformanceModel -> {
            CfBdKpiEnums.CustomTypeEnum typeEnum = CfBdKpiEnums.CustomTypeEnum.parse(performance.getLevel(), customPerformanceModel.getCustomName());
            if (typeEnum==null){
                log.info(" CfBdKpiEnums.CustomTypeEnum.parse level:{}  customName:{} result is null",performance.getLevel(), customPerformanceModel.getCustomName());
                return;
            }
            //如果ScoreBySea = 1覆盖自定义得分
            if (customPerformanceModel.getScoreBySea() == CfBdKpiEnums.ScoreSource.sea_import.getCode()) {
                typeEnum = CfBdKpiEnums.levelToImportType.get(performance.getLevel());
                if (typeEnum==null){
                    log.info(" CfBdKpiEnums.CustomTypeEnum.parse level:{}  customName:{} result is null",performance.getLevel(), customPerformanceModel.getCustomName());
                    return;
                }
            }
            CfBdKpiEnums.CustomTypeEnum finalTypeEnum = typeEnum;
            customPerformanceModel.setCustomType(finalTypeEnum.getType());
            customPerformanceModel.getKpiCustomRules().forEach(kpiCustomRuleModel -> kpiCustomRuleModel.setCustomType(finalTypeEnum.getType()));
        });
        cfKPIPerformanceRuleModel.setCustomPerformance(customPerformance);
        performance.setAssessmentRuleJson(JSON.toJSONString(cfKPIPerformanceRuleModel));
        return cfKpiPerformanceCityRuleDao.update(performance);
    }

    @Override
    public List<CfKpiPerformanceCityRuleDO> listPerformanceByCfKpiStatVO(CfKpiStatVO cfKpiStatVO) {
        String city = "";
        if (CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel().equals(cfKpiStatVO.getLevel())){
            List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationList = crmOrganizationRelationService.listMemberOrgRelationByUniqueCode(cfKpiStatVO.getUniqueCode());
            if (CollectionUtils.isEmpty(bdCrmOrgUserRelationList)){
                return Lists.newArrayList();
            }
            BdCrmOrganizationDO bdCrmOrganizationDO = selfBuiltOrgReadService.getCurrentOrgById(bdCrmOrgUserRelationList.get(0).getOrgId());
            if (Objects.isNull(bdCrmOrganizationDO)){
                return Lists.newArrayList();
            }
            city = bdCrmOrganizationDO.getOrgName();
        }
        return cfKpiPerformanceCityRuleDao.listPerformanceByCityAndLevel(cfKpiStatVO.getMonthKey(),cfKpiStatVO.getLevel(),city);
    }

    @Override
    public CfKpiPerformanceCityRuleDO getPerformanceNew(CfKpiPerformanceCityRuleDO cfKPIPerformance) {
        CfKpiPerformanceCityRuleDO result = null;
        if (cfKPIPerformance.getId() > 0) {
            result = this.getPerformance(cfKPIPerformance.getId());
        }

        if (Objects.nonNull(result)) {
            return result;
        }

        //单个城市+特定省份 可以存在多个值
        if (CfBdKpiEnums.CityTypeEnum.DAN_GE.getType().equals(cfKPIPerformance.getCityType()) || CfBdKpiEnums.CityTypeEnum.SP_PROVINCE.getType().equals(cfKPIPerformance.getCityType())) {
            return null;
        }

        //如果已经存在同一城市类型的绩效系数,直接返回
        if (cfKPIPerformance.getPerformanceType() == CfBdKpiEnums.PerformanceTypeEnum.factor_type.getCode()) {
            return cfKpiPerformanceCityRuleDao.getPerformanceByType(cfKPIPerformance.getMonthKey()
                    , cfKPIPerformance.getLevel(), cfKPIPerformance.getCityType(), cfKPIPerformance.getPerformanceType());
        }

        List<CfKpiPerformanceCityRuleDO> performanceCityRuleDOList = cfKpiPerformanceCityRuleDao.getPerformances(cfKPIPerformance.getMonthKey(), cfKPIPerformance.getLevel(), cfKPIPerformance.getCityType());
        result = performanceCityRuleDOList.stream().filter(v -> v.getPerformanceType() != CfBdKpiEnums.PerformanceTypeEnum.factor_type.getCode()).findFirst().orElse(null);

        log.info(this.getClass().getName() + " getPerformance param:{} result:{}", cfKPIPerformance, result);
        return result;
    }

    @Override
    public List<CfKpiPerformanceCityRuleDO> getPerformanceRulesByType(String monthKey, Integer level, int performanceType) {
        return cfKpiPerformanceCityRuleDao.getPerformanceRulesByType(monthKey, level, performanceType);
    }
}
