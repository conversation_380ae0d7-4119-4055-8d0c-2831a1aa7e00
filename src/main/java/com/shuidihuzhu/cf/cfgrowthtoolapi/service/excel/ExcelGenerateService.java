package com.shuidihuzhu.cf.cfgrowthtoolapi.service.excel;

import com.shuidihuzhu.client.model.ExcelGenerateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Description: Excel生成服务
 * @Author: panghairui
 * @Date: 2022/9/14 3:51 下午
 */
public interface ExcelGenerateService {

    /**
     * @param alarmUser 生成excel链接后发送给谁
     * @param headers excel表头
     * @param collections excel表值
     * @param fileName 文件名
     */
    void excelGenerate(ExcelGenerateParam excelGenerateParam);

}
