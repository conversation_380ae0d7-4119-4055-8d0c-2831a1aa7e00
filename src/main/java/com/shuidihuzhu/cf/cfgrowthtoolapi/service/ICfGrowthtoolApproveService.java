package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGrowthtoolApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.ApproveContentSearchDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.SeaApproveContentVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.ApproveContentDTO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-06-03 15:36
 */
public interface ICfGrowthtoolApproveService {
	int saveApproveContent(CfGrowthtoolApproveDO cfGrowthtoolApproveDO);

	List<CfGrowthtoolApproveDO> getApproveContent(String approveMis, String approveUniqueCode, int approveStatus);

    CfGrowthtoolApproveDO getApproveContentById(Long id);

	int updateApproveStatus(Long id, Integer approveStatus);

    int updateApproveStatus(Long id, Integer approveStatus, String remark, String approveName);

    int updateApproveStatusWithOrgPath(Long id, Integer approveStatus, String orgPath);

    CfGrowthtoolApproveDO getGwApproveContent(String volunteerUniqueCode, Integer type);

	List<CfGrowthtoolApproveDO> listByTypeAndUniqueCode(Integer type,String volunteerUniqueCode, Integer approveStatus);

	List<CfGrowthtoolApproveDO> listByTypeAndApproveUniqueCode(Integer type,String approveUniqueCode, Integer approveStatus);


	CfGrowthtoolApproveDO getWaitApproveContentByType(String volunteerUniqueCode, Integer type);

	/**
	 * 不做去重处理，去重需要在调用这个方法前做
	 * @return
	 */
	CfGrowthtoolApproveDO saveApproveContent(String approveContent, int type, CrowdfundingVolunteer crowdfundingVolunteer, CrowdfundingVolunteer superiorVolunteer);

	CfGrowthtoolApproveDO saveOrUpdateApproveContent(String approveContent, int type, CrowdfundingVolunteer crowdfundingVolunteer, CrowdfundingVolunteer superiorVolunteer);

	List<CfGrowthtoolApproveDO> getApproveContentByUniqueCodeAndStatus(String uniqueCode, int approveStatus);

	int updateApproveContent(CfGrowthtoolApproveDO waitApproveContentByType);

	void autoRejectForDimission(String uniqueCode);

    long countApproveContent(ApproveContentSearchDTO approveContentSearchDTO);

	List<SeaApproveContentVo> listApproveContent(ApproveContentSearchDTO approveContentSearchDTO);

	CfGrowthtoolApproveDO processData(CfGrowthtoolApproveDO cfGrowthtoolApproveDO, ApproveContentDTO approveContentDTO);

    CfGrowthtoolApproveDO getBeforeCommitApproveRecord(Long id, String uniqueCode, String approveUniqueCode, Integer type);

	void updateApproveContent(Long id, String approveContent);

	void notifyHasUndoApprove(CrowdfundingVolunteer volunteer);

	void notifyDowngradingLevel(String role, String dbRole, CrowdfundingVolunteer volunteer);

	List<CfGrowthtoolApproveDO> listByContentAndType(int type, String content);

	long countHistoryApproveContent(String uniqueCode, List<Integer> statusList, Integer contentType);

	List<CfGrowthtoolApproveDO> pageHistoryApproveContent(String uniqueCode, List<Integer> statusList, Integer contentType, int offset, int limit);
}
