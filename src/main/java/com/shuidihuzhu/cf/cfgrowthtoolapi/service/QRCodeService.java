package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.google.zxing.*;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2024-10-09 19:44
 **/
@Slf4j
@Service
public class QRCodeService {

    @Autowired
    private QrCodeUtils qrCodeUtils;

    @Autowired
    private CosUploadUtil cosUploadUtil;

    public Response<String> generateQrCode(String content, String base64logo, String uniqueCode) {
        log.info("generateQrCode content:{},uniqueCode:{}", content, uniqueCode);
        int width = 430; // 图像宽度
        int height = 430; // 图像高度
        Map<EncodeHintType, Object> hints = new HashMap<>();
        //内容编码格式
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        //指定纠错等级
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        //设置二维码边的空度，非负数
        hints.put(EncodeHintType.MARGIN, 1);

        BitMatrix bitMatrix = null;
        try {
            bitMatrix = new MultiFormatWriter().encode(GrowthtoolUtil.decodeQR(content), BarcodeFormat.QR_CODE, width, height, hints);
        } catch (WriterException e) {
            log.info("二维码生成错误 ", e);
        }
        MatrixToImageConfig matrixToImageConfig = new MatrixToImageConfig(0xFF000001, 0xFFFFFFFF);
        /*
            问题：生成二维码正常,生成带logo的二维码logo变成黑白
            原因：MatrixToImageConfig默认黑白，需要设置BLACK、WHITE
            解决：https://ququjioulai.iteye.com/blog/2254382
         */
        BufferedImage imagelogo = base64ToBufferedImage(base64logo);
        BufferedImage bufferedImage = logoMatrix(MatrixToImageWriter.toBufferedImage(bitMatrix, matrixToImageConfig), imagelogo);
        InputStream inputStream = qrCodeUtils.bufferedImageToInputStream(bufferedImage);
        String resultQrCode = cosUploadUtil.doUploadImage(inputStream, uniqueCode + ".jpg");//输出带logo的图片
        return NewResponseUtil.makeSuccess(resultQrCode);

    }


    /**
     * 二维码添加logo
     *
     * @param matrixImage 源二维码图片
     * @param logo        logo图片
     * @return 返回带有logo的二维码图片
     * 参考：https://blog.csdn.net/weixin_39494923/article/details/79058799
     */
    public static BufferedImage logoMatrix(BufferedImage matrixImage, BufferedImage logo) {
        /**
         * 读取二维码图片，并构建绘图对象
         */
        Graphics2D g2 = matrixImage.createGraphics();

        int matrixWidth = matrixImage.getWidth();
        int matrixHeigh = matrixImage.getHeight();

        /**
         * 读取Logo图片
         */
        //开始绘制图片
        g2.drawImage(logo, matrixWidth / 5 * 2, matrixHeigh / 5 * 2, matrixWidth / 5, matrixHeigh / 5, null);//绘制
        BasicStroke stroke = new BasicStroke(5, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND);
        g2.setStroke(stroke);// 设置笔画对象
        //指定弧度的圆角矩形
        RoundRectangle2D.Float round = new RoundRectangle2D.Float(matrixWidth / 5 * 2, matrixHeigh / 5 * 2, matrixWidth / 5, matrixHeigh / 5, 20, 20);
        g2.setColor(Color.white);
        g2.draw(round);// 绘制圆弧矩形

        //设置logo 有一道灰色边框
        BasicStroke stroke2 = new BasicStroke(1, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND);
        g2.setStroke(stroke2);// 设置笔画对象
        RoundRectangle2D.Float round2 = new RoundRectangle2D.Float(matrixWidth / 5 * 2 + 2, matrixHeigh / 5 * 2 + 2, matrixWidth / 5 - 4, matrixHeigh / 5 - 4, 20, 20);
        g2.setColor(new Color(128, 128, 128));
        g2.draw(round2);// 绘制圆弧矩形

        g2.dispose();
        matrixImage.flush();
        return matrixImage;
    }

    private static BufferedImage base64ToBufferedImage(String base64) {
        org.apache.commons.codec.binary.Base64 base = new Base64();
        byte[] image = base.decode(base64.replace("data:image/png;base64,", ""));
        InputStream stream = new ByteArrayInputStream(image);
        BufferedImage bufferedImage = null;
        try {
            bufferedImage = ImageIO.read(stream);
        } catch (IOException e) {
            log.info("base64ToBufferedImage", e);
        }
        return bufferedImage;
    }
}
