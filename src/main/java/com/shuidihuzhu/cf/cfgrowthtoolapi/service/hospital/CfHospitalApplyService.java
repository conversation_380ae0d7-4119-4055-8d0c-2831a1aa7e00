package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfHospitalApplyDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.ApplyStatusDataModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.HospitalOrgInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.HospitalApplySearchParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 医院纠错申报表(CfHospitalApply)表服务接口
 *
 * <AUTHOR>
 * @since 2020-11-09 15:44:54
 */
public interface CfHospitalApplyService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CfHospitalApplyDo queryById(long id);

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    List<CfHospitalApplyDo> queryAllByLimit(int offset, int limit);

    /**
     * 新增数据
     *
     * @param cfHospitalApplyDo 实例对象
     * @return 实例对象
     */
    CfHospitalApplyDo insert(CfHospitalApplyDo cfHospitalApplyDo);

    /**
     * 根据组织聚合数据
     *
     * @return
     */
    List<HospitalOrgInfo> groupByOrgId();


    List<ApplyStatusDataModel> listGroupByStatus(HospitalApplySearchParam searchParam);


    List<CfHospitalApplyDo> listBySearchParam(HospitalApplySearchParam searchParam);


    int countBySearchParam(HospitalApplySearchParam searchParam);


    int updateApplyStatus(int applyId, int applyStatus);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);

    List<CfHospitalApplyDo> listByHospitalNameAndCity(String hospitalName, String city);

    List<CfHospitalApplyDo> listByOrgIdAndHospitalName(@Param("orgId") int orgId, @Param("hospitalName") String hospitalName);

}