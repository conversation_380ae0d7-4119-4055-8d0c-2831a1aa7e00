package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.CommonMessageHelperService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IClewTrackProducer;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.MessageBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IRegisterClewFacadeInterface;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.model.clewtrack.ClewReceiveModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.Consts;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.msg.model.SmsRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RefreshScope
public class RegisterClewFacadeImpl implements IRegisterClewFacadeInterface {
    @Autowired
    private CommonMessageHelperService commonMessageHelperService;

    @Autowired(required = false)
    private IClewTrackProducer clewTrackProducer;


    @Override
    public void sendRegisterSuccessDelayMessage(SmsRecord smsRecord) {
        if(StringUtils.isBlank(smsRecord.getMobile())){
            return ;
        }
        if(!Consts.pMobile.matcher(smsRecord.getMobile()).find()){
            //不合法的手机号不发送延迟消息
            log.info("sendRegisterSuccessDelayMessage mobile exception :{}", smsRecord.getMobile());
            return;
        }
        //触发延迟信息
        Message<SmsRecord> msg = MessageBuilder.createWithPayload(smsRecord)
                .setTags(MQTagCons.CLICK_ON_DELAY_PAGE_THR_MIN_MSG)
                .addKey(smsRecord.getMobile(), System.currentTimeMillis())
                .build();

        this.commonMessageHelperService.sendDelayByDelayTime(msg, 30 * 60 * 1000L);
    }

    @Override
    public OpResult<ClewReceiveModel> sendNewClewMsg(ClewReceiveModel clewReceiveModel) {
        return clewTrackProducer.sendNewClewMsg(clewReceiveModel);
    }
}
