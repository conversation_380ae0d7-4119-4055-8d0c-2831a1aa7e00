package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.SeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfOperatingRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.calresult.EncourageActivityUserResultDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.param.EncourageActivityQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.param.EncourageActivitySaveParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.vo.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICrowdfundingCityService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.calresult.EncourageActivityUserResultService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolAsyncPoolConstants;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("encourageActivityService")
@Slf4j
public class EncourageActivityServiceImpl implements EncourageActivityService {

    @Autowired
    private EncourageActivityBaseConfigService encourageActivityBaseConfigService;

    @Autowired
    private EncourageActivityCalculateRuleService encourageActivityCalculateRuleService;

    @Autowired
    private IOperateLogService operateLogService;

    @Autowired
    private SeaAccountServiceDelegate seaAccountServiceDelegate;

    @Autowired
    private ICrowdfundingCityService crowdfundingCityService;

    @Autowired
    private EncourageActivityUserResultService encourageActivityUserResultService;

    @Autowired
    private EncourageActivityPackageUserService packageUserService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private ICrmOrganizationRelationService relationService;

    @Autowired
    private ICfVolunteerService volunteerService;

    @Resource(name = GrowthtoolAsyncPoolConstants.BACK_DOOR_POOL)
    private ExecutorService executorService;


    @Override
    public CommonResultModel<EncourageActivityListVO> list(EncourageActivityQueryParam query) {
        CommonResultModel<EncourageActivityListVO> result = new CommonResultModel<>();
        List<EncourageActivityListVO> voList = Lists.newArrayList();
        log.info("EncourageActivityServiceImpl query:{}", query);

        List<EncourageActivityBaseConfigDO> activityList = encourageActivityBaseConfigService.listByCondition(query);
        log.info("activityList:{}", activityList);
        if (CollectionUtils.isEmpty(activityList)) {
            return result;
        }

        Date now = new Date();
        List<EncourageActivityListVO> invalidVoList = Lists.newArrayList();
        for (EncourageActivityBaseConfigDO activity : activityList) {
            EncourageActivityListVO vo = new EncourageActivityListVO();
            BeanUtils.copyProperties(activity, vo);

            // 活动范围补充
            if (activity.getActivityType() == EncourageActivityEnums.ActivityTypeEnum.by_org.getCode()) {
                vo.setActivityRange(activity.getActivityOrgPath());
            }
            if (activity.getActivityType() == EncourageActivityEnums.ActivityTypeEnum.by_city.getCode()) {
                vo.setActivityRange(activity.getActivityCity());
            }

            // 待生效和生效中的活动状态赋值
            vo.setActivityStatus(EncourageActivityEnums.ActivityStatusEnum.wait_effect.getCode());
            if (activity.getActivityStatus() == EncourageActivityEnums.ActivityAbolishEnum.Abolish.getCode()) {
                vo.setActivityStatus(EncourageActivityEnums.ActivityStatusEnum.invalid.getCode());
                invalidVoList.add(vo);
                continue;
            }
            if (activity.getEndTime().before(now)) {
                vo.setActivityStatus(EncourageActivityEnums.ActivityStatusEnum.invalid.getCode());
                invalidVoList.add(vo);
                continue;
            }
            if (activity.getStartTime().before(now)) {
                vo.setActivityStatus(EncourageActivityEnums.ActivityStatusEnum.effect.getCode());
                voList.add(vo);
                continue;
            }
            voList.add(vo);
        }
        // 活动列表排序规则：失效的在最后
        voList = voList.stream().sorted(Comparator.comparing(EncourageActivityListVO::getCreateTime).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(invalidVoList)) {
            voList.addAll(invalidVoList.stream().sorted(Comparator.comparing(EncourageActivityListVO::getCreateTime).reversed()).collect(Collectors.toList()));
        }
        // 过滤活动状态
        if (query.getActivityStatus() != null) {
            voList = voList.stream().filter(vo -> vo.getActivityStatus() == query.getActivityStatus()).collect(Collectors.toList());
        }

        result.setTotal(voList.size());
        int offset = (query.getPageNo() - 1) * query.getPageSize();
        result.setModelList(voList.subList(offset, Math.min(offset + query.getPageSize(), voList.size())));

        return result;
    }

    @Nullable
    private List<String> transOrgId2OrgName(String filter, String orgIdString) {
        List<String> orgNames = Lists.newArrayList();
        List<Long> orgIds = Lists.newArrayList(orgIdString.split(",")).stream().map(Long::parseLong).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgIds)) {
            return null;
        }
        if (StringUtils.isNotEmpty(filter) && !orgIds.contains(Long.valueOf(filter))) {
            return null;
        }
        if (orgIds.contains(99999L)) {
            orgNames.add("总部");
            orgIds.remove(99999L);
        }
        for (Long orgId : orgIds) {
            List<BdCrmOrganizationDO> orgs = crmSelfBuiltOrgReadService.listParentOrgAsChain(orgId);
            if (CollectionUtils.isNotEmpty(orgs)) {
                List<String> pathList = orgs.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.toList());
                orgNames.add(String.join("-", pathList));
            }
        }
        return orgNames;
    }

    @Nullable
    private List<String> transOrgCityId2OrgCityName(String orgIdString) {
        List<String> orgNames = Lists.newArrayList();

        if (StringUtils.isBlank(orgIdString)) {
            return orgNames;
        }
        List<Integer> orgIds = Lists.newArrayList(orgIdString.split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgIds)) {
            return orgNames;
        }

        List<CrowdfundingCity> orgs = crowdfundingCityService.getCityByCityIds(orgIds);
        if (CollectionUtils.isNotEmpty(orgs)) {
            orgNames.addAll(orgs.stream().map(CrowdfundingCity::getName).collect(Collectors.toList()));
        }
        return orgNames;
    }

    @Override
    public EncourageActivitySaveParam detail(long activityId) {
        EncourageActivitySaveParam vo = new EncourageActivitySaveParam();

        EncourageActivityBaseConfigDO activity = encourageActivityBaseConfigService.queryById(activityId);
        if (activity == null) {
            return vo;
        }
        BeanUtils.copyProperties(activity, vo);
        log.info("EncourageActivityBaseConfigDO:{}, vo:{}", activity, vo);
        vo.setIgnoreType(activity.getIgnoreType());
        List<EncourageActivityCalculateRuleDO> ruleList = encourageActivityCalculateRuleService.listByActivityId(activityId);
        if (CollectionUtils.isEmpty(ruleList)) {
            return vo;
        }
        List<EncourageActivityCalculateRuleVO> ruleVOList = Lists.newArrayList();
        for (EncourageActivityCalculateRuleDO rule : ruleList) {
            EncourageActivityCalculateRuleVO ruleVO = new EncourageActivityCalculateRuleVO();
            BeanUtils.copyProperties(rule, ruleVO);
            ruleVO.setRuleId(rule.getId());
            ruleVOList.add(ruleVO);
        }
        vo.setRuleList(ruleVOList);
        return vo;
    }

    @Override
    public Boolean addOrUpdate(EncourageActivitySaveParam param) {
        boolean add = false;
        if (param.getActivityId() == null) {
            add = true;
        }
        EncourageActivityBaseConfigDO activity = new EncourageActivityBaseConfigDO();
        BeanUtils.copyProperties(param, activity);
        // 增加org_path
        if (param.getActivityType() == EncourageActivityEnums.ActivityTypeEnum.by_org.getCode()) {
            List<String> orgNames = transOrgId2OrgName(null, activity.getActivityOrgId());
            if (CollectionUtils.isNotEmpty(orgNames)) {
                activity.setActivityOrgPath(String.join(",", orgNames));
            }
        }
        // 增加city_path
        if (param.getActivityType() == EncourageActivityEnums.ActivityTypeEnum.by_city.getCode()) {
            List<String> orgNames = transOrgCityId2OrgCityName(activity.getActivityCityId());
            if (CollectionUtils.isNotEmpty(orgNames)) {
                activity.setActivityCity(String.join(",", orgNames));
            }
        }
        // 增加发起方path
        activity.setActivityRaiser("");
        if (activity.getActivityRaiserOrgId() == 99999) {
            activity.setActivityRaiser("总部");
        } else {
            List<BdCrmOrganizationDO> orgs = crmSelfBuiltOrgReadService.getOrgInfoList(Lists.newArrayList(activity.getActivityRaiserOrgId()));
            if (CollectionUtils.isNotEmpty(orgs)) {
                activity.setActivityRaiser(orgs.get(0).getOrgName());
            }
        }

        if (StringUtils.isBlank(param.getActivityOrgIdStr())) {
            activity.setActivityOrgIdStr("");
        }

        // 处理caseStartTime和caseEndTime没有值的情况
        if (param.getCaseStartTime() == null) {
            activity.setCaseStartTime(param.getDonateStartTime());
        }
        if (param.getCaseEndTime() == null) {
            activity.setCaseEndTime(param.getDonateEndTime());
        }

        int res = 0;
        if (add) {
            String name = "";
            if (param.getOperatorId() > 0) {
                name = Optional.ofNullable(seaAccountServiceDelegate.getValidUserAccountById(param.getOperatorId()))
                        .map(AdminUserAccountModel::getName)
                        .orElse("");
            }
            activity.setActivityCreator(name);
            res = encourageActivityBaseConfigService.insert(activity);
            saveLog(activity.getId(), param.getOperatorId(), OperateTypeEnum.INCENTIVE_ACTIVITY_ADD);

            if (res == 0) {
                log.info("add encourageActivityBaseConfigService fail, param:{}", param);
                return false;
            }
            // 查询出插入后的id
            EncourageActivityQueryParam query = new EncourageActivityQueryParam();
            query.setActivityName(param.getActivityName());
            query.setActivityRaiser(param.getActivityRaiserOrgId());
            EncourageActivityBaseConfigDO activityDO = encourageActivityBaseConfigService.getLastOneByCondition(query);
            if (activityDO == null) {
                log.info("addOrUpdate encourageActivityBaseConfigService fail, param:{}", param);
                return false;
            }
            activity.setId(activityDO.getId());
            executorService.submit(() -> packageUserService.batchHandle(activityDO.getId(), createByActivityBaseInfo(activity)));
            for (EncourageActivityCalculateRuleVO rule : param.getRuleList()) {
                EncourageActivityCalculateRuleDO ruleDO = new EncourageActivityCalculateRuleDO();
                BeanUtils.copyProperties(rule, ruleDO);
                ruleDO.setActivityId(activity.getId());
                List<String> cityNames = transOrgCityId2OrgCityName(rule.getCityId());
                if (CollectionUtils.isNotEmpty(cityNames)) {
                    ruleDO.setCityName(String.join(",", cityNames));
                }
                encourageActivityCalculateRuleService.insert(ruleDO);
            }
            return true;

        }
        activity.setId(param.getActivityId());
        res = encourageActivityBaseConfigService.update(activity);

        executorService.submit(() -> packageUserService.batchHandle(activity.getId(), createByActivityBaseInfo(activity)));

        saveLog(activity.getId(), param.getOperatorId(), OperateTypeEnum.INCENTIVE_ACTIVITY_EDIT);

        List<EncourageActivityCalculateRuleDO> ruleDOS = encourageActivityCalculateRuleService.listByActivityId(activity.getId());
        List<Long> ruleIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ruleDOS)) {
            ruleIds = ruleDOS.stream().map(EncourageActivityCalculateRuleDO::getId).collect(Collectors.toList());
        }
        for (EncourageActivityCalculateRuleVO rule : param.getRuleList()) {
            EncourageActivityCalculateRuleDO ruleDO = new EncourageActivityCalculateRuleDO();
            BeanUtils.copyProperties(rule, ruleDO);
            ruleDO.setActivityId(activity.getId());
            List<String> cityNames = transOrgCityId2OrgCityName(rule.getCityId());
            if (CollectionUtils.isNotEmpty(cityNames)) {
                ruleDO.setCityName(String.join(",", cityNames));
            }
            if (rule.getRuleId() == null) {
                encourageActivityCalculateRuleService.insert(ruleDO);
            } else {
                ruleDO.setId(rule.getRuleId());
                encourageActivityCalculateRuleService.update(ruleDO);
                ruleIds.remove(rule.getRuleId());
            }
        }
        // 删除规则
        for (Long ruleId : ruleIds) {
            encourageActivityCalculateRuleService.deleteById(ruleId);
        }
        return true;
    }


    private List<EncourageActivityPackageUserDO> createByActivityBaseInfo(EncourageActivityBaseConfigDO baseConfigDO) {
        //如果是按照 orgId 计算
        List<Long> orgList = Lists.newArrayList();
        if (baseConfigDO.getActivityType() == EncourageActivityEnums.ActivityTypeEnum.by_org.getCode()) {
            List<Long> activityOrgIds = baseConfigDO.parseActivityOrgId();
            for (Long orgId : activityOrgIds) {
                orgList.addAll(crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(orgId).stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList()));
            }
        }
        if (baseConfigDO.getActivityType() == EncourageActivityEnums.ActivityTypeEnum.by_city.getCode()) {
            orgList.addAll(crmSelfBuiltOrgReadService.queryOrgIdListByCities(baseConfigDO.parseActivityCityList()));
        }
        if (CollectionUtils.isEmpty(orgList)) {
            return Lists.newArrayList();
        }
        Map<Long, String> orgMap = crmSelfBuiltOrgReadService.listChainByOrgIdsWithDefaultSplitter(orgList);
        //查找人员
        int bdLevel = baseConfigDO.getBdLevel();
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = relationService.listByOrgIds(orgList);
        if (CrowdfundingVolunteerEnum.commonRoles.contains(bdLevel)) {
            bdCrmOrgUserRelationDOS = bdCrmOrgUserRelationDOS.stream()
                    .filter(item -> item.getUserRole() == OrganizationUserEnums.UserRoleEnum.normal.getCode())
                    .collect(Collectors.toList());
        }
        Map<Long, List<BdCrmOrgUserRelationDO>> relationGroup = bdCrmOrgUserRelationDOS.stream()
                .collect(Collectors.groupingBy(BdCrmOrgUserRelationDO::getOrgId));
        List<String> uniqueCodeList = bdCrmOrgUserRelationDOS.stream()
                .map(BdCrmOrgUserRelationDO::getUniqueCode)
                .distinct().collect(Collectors.toList());
        Map<String, CrowdfundingVolunteer> volunteerMap = volunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodeList)
                .stream()
                .collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity(), (v1, v2) -> v1));
        //按照职级过滤
        List<EncourageActivityPackageUserDO> result = Lists.newArrayList();
        //一个人员只插入一次
        Set<String> uniqueCodes = Sets.newHashSet();
        log.info("bdCrmOrgUserRelationDOS size:{}", bdCrmOrgUserRelationDOS.size());
        for (BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO : bdCrmOrgUserRelationDOS) {
            if (uniqueCodes.contains(bdCrmOrgUserRelationDO.getUniqueCode())) {
                continue;
            }
            CrowdfundingVolunteer crowdfundingVolunteer = volunteerMap.get(bdCrmOrgUserRelationDO.getUniqueCode());
            if (crowdfundingVolunteer != null && Objects.equals(crowdfundingVolunteer.getLevel(), bdLevel)) {
                //如果是合作团队需要只展示一个
                if (CrowdfundingVolunteerEnum.delegateLeaderRoles.contains(bdLevel)) {
                    List<BdCrmOrgUserRelationDO> relationDOList = relationGroup.get(bdCrmOrgUserRelationDO.getOrgId());
                    if (relationDOList.size() > 1) {
                        log.info("relationDOList size:{}", relationDOList.size());
                        //只找一个
                        Optional<CrowdfundingVolunteer> first = relationDOList.stream()
                                .map(item -> volunteerMap.get(item.getUniqueCode()))
                                .filter(item -> item != null && item.getEntryTime() != null)
                                .min(Comparator.comparing(CrowdfundingVolunteer::getEntryTime));
                        //如果不是最早入职的
                        if (first.isEmpty() || !first.get().getUniqueCode().equals(bdCrmOrgUserRelationDO.getUniqueCode())) {
                            continue;
                        }
                    }
                }
                String orgPath = orgMap.getOrDefault(bdCrmOrgUserRelationDO.getOrgId(), "");
                EncourageActivityPackageUserDO userDO = new EncourageActivityPackageUserDO();
                userDO.setActivityId(baseConfigDO.getId());
                userDO.setUniqueCode(bdCrmOrgUserRelationDO.getUniqueCode());
                userDO.setOrgId(bdCrmOrgUserRelationDO.getOrgId());
                userDO.setOrgPath(orgPath);
                result.add(userDO);
                uniqueCodes.add(bdCrmOrgUserRelationDO.getUniqueCode());
            }
        }
        return result;
    }

    @Override
    public Boolean cancel(long activityId, long adminUserId) {
        boolean res = encourageActivityBaseConfigService.updateStatusById(activityId, EncourageActivityEnums.ActivityAbolishEnum.Abolish.getCode());
        if (res) {
            saveLog(activityId, adminUserId, OperateTypeEnum.INCENTIVE_ACTIVITY_DELETE);
            encourageActivityCalculateRuleService.deleteByActivityId(activityId);
            return true;
        }
        return false;
    }

    private void saveLog(long activityId, long adminUserId, OperateTypeEnum typeEnum) {
        CfOperatingRecordDO record = new CfOperatingRecordDO();
        record.setOperateKey(String.valueOf(activityId));
        record.setOperateType(typeEnum.getType());
        record.setOperateDesc(typeEnum.getDesc());
        record.setContent("");
        record.setOperateUserId(adminUserId);
        String name = "";
        if (adminUserId > 0) {
            name = Optional.ofNullable(seaAccountServiceDelegate.getValidUserAccountById(adminUserId))
                    .map(AdminUserAccountModel::getName)
                    .orElse("");
        }
        record.setOperateName(name);
        operateLogService.saveOperateLog(record);
    }

    @Override
    public List<EncourageActivityResultVO> result(long activityId, int ruleId) {
        List<EncourageActivityResultVO> resultVOS = Lists.newArrayList();
        EncourageActivityBaseConfigDO activity = encourageActivityBaseConfigService.queryById(activityId);
        if (activity == null) {
            log.info("EncourageActivityBaseConfigDO is null, activityId:{}", activityId);
            return resultVOS;
        }
        EncourageActivityCalculateRuleDO ruleDO = encourageActivityCalculateRuleService.queryByActivityIdAndRuleId(activityId, ruleId);
        if (ruleDO == null) {
            log.info("EncourageActivityCalculateRuleDO is null, activityId:{}, ruleId:{}", activityId, ruleId);
            return resultVOS;
        }
        List<EncourageActivityUserResultDO> userResultDOS = encourageActivityUserResultService.listByActivityIdAndRuleId(activityId, ruleId);
        if (CollectionUtils.isEmpty(userResultDOS)) {
            log.info("EncourageActivityUserResultDO is null, activityId:{}, ruleId:{}", activityId, ruleId);
            return resultVOS;
        }
        List<String> uniqueCodeList = userResultDOS.stream()
                .map(EncourageActivityUserResultDO::getUniqueCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, CrowdfundingVolunteer> volunteerMap = volunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodeList)
                .stream()
                .collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity(), (v1, v2) -> v1));
        for (EncourageActivityUserResultDO userResult : userResultDOS) {
            EncourageActivityResultVO vo = new EncourageActivityResultVO();
            vo.setRuleId(ruleDO.getId());
            CrowdfundingVolunteer crowdfundingVolunteer = volunteerMap.get(userResult.getUniqueCode());
            if (crowdfundingVolunteer != null) {
                vo.setActivityName(crowdfundingVolunteer.getVolunteerName());
            }
            vo.setActivityOrg(userResult.getOrgPath());
            vo.setEncourageAmount(userResult.getCalResult());
            vo.setTotalDonationNum(userResult.getDonateNum());
            vo.setTotalCaseNum(userResult.getCaseNum());
            resultVOS.add(vo);
        }
        return resultVOS;
    }

    @Override
    public List<EncourageActivityOperateLogDO> operateLog(long activityId) {

        List<EncourageActivityOperateLogDO> result = Lists.newArrayList();
        EncourageActivityBaseConfigDO activity = encourageActivityBaseConfigService.queryById(activityId);
        if (activity == null) {
            return result;
        }

        List<OperateTypeEnum> operateTypeEnumList = Lists.newArrayList(OperateTypeEnum.INCENTIVE_ACTIVITY_ADD,
                OperateTypeEnum.INCENTIVE_ACTIVITY_EDIT,
                OperateTypeEnum.INCENTIVE_ACTIVITY_DELETE);
        List<CfOperatingRecordDO> recordList = operateLogService.listByOptTypes(String.valueOf(activityId), operateTypeEnumList);
        if (CollectionUtils.isEmpty(recordList)) {
            return result;
        }

        for (CfOperatingRecordDO record : recordList) {
            EncourageActivityOperateLogDO log = new EncourageActivityOperateLogDO();
            log.setActivityId(activityId);
            log.setOperator(record.getOperateName());
            if (OperateTypeEnum.parse(record.getOperateType()) != null) {
                log.setOperateType(OperateTypeEnum.parse(record.getOperateType()).getDesc());
            }
            log.setCreateTime(record.getCreateTime());
            result.add(log);
        }
        // 增加上下线活动信息
        Date now = new Date();
        if (activity.getStartTime().before(now)) {
            EncourageActivityOperateLogDO log = new EncourageActivityOperateLogDO();
            log.setActivityId(activityId);
            log.setOperator("系统");
            log.setOperateType(OperateTypeEnum.INCENTIVE_ACTIVITY_ONLINE.getDesc());
            log.setCreateTime(activity.getStartTime());
            result.add(log);
        }
        if (activity.getEndTime().before(now)) {
            EncourageActivityOperateLogDO log = new EncourageActivityOperateLogDO();
            log.setActivityId(activityId);
            log.setOperator("系统");
            log.setOperateType(OperateTypeEnum.INCENTIVE_ACTIVITY_OFFLINE.getDesc());
            log.setCreateTime(activity.getEndTime());
            result.add(log);
        }

        return result;
    }
}
