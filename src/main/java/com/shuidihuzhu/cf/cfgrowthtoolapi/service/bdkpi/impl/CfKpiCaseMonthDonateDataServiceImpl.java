package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfKpiCaseMonthDonateData;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiCaseMonthDonateDataDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiCaseMonthDonateDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * kpi-月捐单数据统计(CfKpiCaseMonthDonateData)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-04 16:54:44
 */
@Service("cfKpiCaseMonthDonateDataService")
public class CfKpiCaseMonthDonateDataServiceImpl implements CfKpiCaseMonthDonateDataService {

    @Resource
    private CfKpiCaseMonthDonateDataDao cfKpiCaseMonthDonateDataDao;

    @Override
    public CfKpiCaseMonthDonateData queryById(long id) {
        return cfKpiCaseMonthDonateDataDao.queryById(id);
    }


    @Override
    public int insert(CfKpiCaseMonthDonateData cfKpiCaseMonthDonateData) {
        List<CfKpiCaseMonthDonateData> donateDataList = cfKpiCaseMonthDonateDataDao.queryByCaseIdAndDateKey(cfKpiCaseMonthDonateData.getCaseId(), cfKpiCaseMonthDonateData.getDayKey());
        //判断下是否重复插入,有重复的直接删除掉
        if (CollectionUtils.isNotEmpty(donateDataList)) {
            cfKpiCaseMonthDonateDataDao.deleteByIds(donateDataList.stream().map(CfKpiCaseMonthDonateData::getId).collect(Collectors.toList()));
        }
        return cfKpiCaseMonthDonateDataDao.insert(cfKpiCaseMonthDonateData);
    }

    @Override
    public int update(CfKpiCaseMonthDonateData cfKpiCaseMonthDonateData) {
        return cfKpiCaseMonthDonateDataDao.update(cfKpiCaseMonthDonateData);
    }

    @Override
    public List<CfKpiCaseMonthDonateData> listDonateDataByDayKey(String dayKey, String monthKey) {
        return cfKpiCaseMonthDonateDataDao.listDonateDataByDayKey(dayKey, monthKey);
    }

}
