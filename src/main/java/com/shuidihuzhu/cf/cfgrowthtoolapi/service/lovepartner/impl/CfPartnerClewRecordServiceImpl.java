package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PartnerTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdCrmWhaleClewInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerServiceInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.PartnerServiceSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.CaseValidCommonSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerClewRecordService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerInfoService;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCaseInfoDao;
import com.shuidihuzhu.cf.dao.lovepartner.CfPartnerClewRecordDao;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackApiClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerClewRecordDo;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */

@Service
@Slf4j
public class CfPartnerClewRecordServiceImpl implements CfPartnerClewRecordService {

    @Autowired
    private CfPartnerClewRecordDao cfPartnerClewRecordDao;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CfBdCaseInfoDao cfBdCaseInfoDao;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private CfClewtrackApiClient cfClewtrackApiClient;

    @Autowired
    private CfPartnerInfoService partnerInfoService;

    @Autowired
    private ApolloService apolloService;

    @Override
    public int insert(CfPartnerClewRecordDo cfPartnerClewRecordDo) {
        //查找下对应的volunteer信息
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(cfPartnerClewRecordDo.getLeaderUniqueCode());
        if (volunteer != null) {
            cfPartnerClewRecordDo.setLeaderName(volunteer.getVolunteerName());
        }
        return cfPartnerClewRecordDao.insert(cfPartnerClewRecordDo);
    }

    @Override
    public List<CfPartnerClewRecordDo> listPartnerClewByUniqueCodeAndEncryptPhoneWithTime(String leaderUniqueCode, String encryptPhone, Date startTime, Date endTime) {
        return cfPartnerClewRecordDao.listPartnerClewByUniqueCodeAndEncryptPhoneWithTime(leaderUniqueCode, encryptPhone, startTime, endTime);
    }

    @Override
    public List<CfPartnerClewRecordDo> listPartnerClewByEncryptPhoneWithTime(String encryptPhone, Date startTime, Date endTime) {
        return cfPartnerClewRecordDao.listPartnerClewByEncryptPhoneWithTime(encryptPhone, startTime, endTime);
    }

    @Override
    public List<CfPartnerClewRecordDo> listPartnerClewByClewIds(List<Long> clewIds) {
        if (CollectionUtils.isEmpty(clewIds)){
            return Lists.newArrayList();
        }
        List<List<Long>> listList = Lists.partition(clewIds, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> cfPartnerClewRecordDao.listPartnerClewByClewIds(list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).get();
    }

    @Override
    public List<BdCrmWhaleClewInfoModel> clewManageWithMaskPhone(Date startTime, Date endTime, String leaderUniqueCode, String partnerUniqueCode) {
        List<BdCrmWhaleClewInfoModel> result = Lists.newArrayList();
        List<CfPartnerClewRecordDo> clewRecordList = cfPartnerClewRecordDao.listPartnerClewByUniqueCodeAndPartnerUniqueCodeWithTime(leaderUniqueCode, partnerUniqueCode, startTime, endTime);
        if (CollectionUtils.isEmpty(clewRecordList)){
            return result;
        }
        List<Long> clewIds = clewRecordList.stream().map(CfPartnerClewRecordDo::getClewId).collect(Collectors.toList());
        Response<List<CfClewBaseInfoDO>> response = cfClewtrackApiClient.listCfClewBaseInfo(Lists.newArrayList(clewIds));
        if(response==null || response.notOk() || CollectionUtils.isEmpty(response.getData())){
            return result;
        }
        CfPartnerInfoDo cfPartnerInfoDo = partnerInfoService.getPartnerInfoByUniqueCode(partnerUniqueCode);
        Map<Long, CfClewBaseInfoDO> cfClewBaseInfoMap = response.getData().stream().collect(Collectors.toMap(CfClewBaseInfoDO::getId, Function.identity(),(oldObj, newObj)->newObj));
        for (Long clewId : clewIds){
            BdCrmWhaleClewInfoModel whaleClewInfoModel = new BdCrmWhaleClewInfoModel();
            whaleClewInfoModel.fullByPartnerInfo(cfPartnerInfoDo);
            whaleClewInfoModel.fullByCfClewBaseInfoDO(cfClewBaseInfoMap.get(clewId),shuidiCipher);
            result.add(whaleClewInfoModel);
        }
        return result;
    }

    @Override
    public CommonPageModel<PartnerServiceInfo> serviceData(PartnerServiceSearchParam serviceSearchParam) {
        if (serviceSearchParam.getSearchTab() == PartnerTypeEnum.ServiceSearchEnum.clew.getCode()) {
            return searchByClew(serviceSearchParam);
        }
        return searchByBdCaseInfo(serviceSearchParam);
    }



    private CommonPageModel<PartnerServiceInfo> searchByClew(PartnerServiceSearchParam serviceSearchParam) {
        CommonPageModel<PartnerServiceInfo> result = new CommonPageModel<PartnerServiceInfo>();
        int count = Optional.ofNullable(cfPartnerClewRecordDao.countByServiceData(serviceSearchParam)).orElse(0);
        result.setCount(count);
        if (count == 0) {
            return result;
        }
        DateTime now = DateTime.now();
        List<CfPartnerClewRecordDo> clewRecordDos = cfPartnerClewRecordDao.listByServiceData(serviceSearchParam);
        List<String> encryptPhones = clewRecordDos.stream().map(CfPartnerClewRecordDo::getEncryptPhone).collect(Collectors.toList());
        Map<String, CfBdCaseInfoDo> phoneMapCaseInfo = cfBdCaseInfoDao.listByRaiserPhone(encryptPhones, serviceSearchParam.getPartnerUniqueCode())
                .stream()
                .collect(Collectors.toMap(CfBdCaseInfoDo::getRaiserPhone, Function.identity(), (before, after) -> before));
        List<PartnerServiceInfo> partnerServiceInfoList = clewRecordDos
                .stream()
                .map(item -> {
                    CfBdCaseInfoDo cfBdCaseInfoDo = phoneMapCaseInfo.get(item.getEncryptPhone());
                    PartnerServiceInfo partnerServiceInfo = new PartnerServiceInfo();
                    partnerServiceInfo.setCreateTime(item.getCreateTime());
                    partnerServiceInfo.setPhone(shuidiCipher.decrypt(item.getEncryptPhone()));
                    boolean validClew = now.minusHours(72).isBefore(new DateTime(item.getCreateTime()));
                    partnerServiceInfo.setLeaderUniqueName(item.getLeaderName());
                    partnerServiceInfo.setPatientName(item.getPatientName());
                    partnerServiceInfo.setLab(validClew ? "" : "已失效");
                    partnerServiceInfo.fillByBdCaseInfo(cfBdCaseInfoDo);
                    return partnerServiceInfo;
                }).collect(Collectors.toList());
        result.setList(partnerServiceInfoList);
        return result;
    }


    private CommonPageModel<PartnerServiceInfo> searchByBdCaseInfo(PartnerServiceSearchParam serviceSearchParam) {
        CommonPageModel<PartnerServiceInfo> result = new CommonPageModel<PartnerServiceInfo>();
        CaseValidCommonSearchParam caseValidSearch = CaseValidCommonSearchParam.buildCaseValidSearch(apolloService.getValidAmount(),apolloService.getValidDonateNum());
        int count = Optional.ofNullable(cfBdCaseInfoDao.countByServiceData(serviceSearchParam,caseValidSearch)).orElse(0);
        result.setCount(count);
        if (count == 0) {
            return result;
        }
        List<CfBdCaseInfoDo> cfBdCaseInfoDos = cfBdCaseInfoDao.listByServiceData(serviceSearchParam,caseValidSearch);

        List<String> uniqueCodeList = cfBdCaseInfoDos.stream()
                .map(CfBdCaseInfoDo::getUniqueCode)
                .collect(Collectors.toList());
        Map<String, CrowdfundingVolunteer> uniqueCodeMapVolunteer = cfVolunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodeList)
                .stream()
                .collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity(), (before, after) -> before));

        List<PartnerServiceInfo> partnerServiceInfoList = cfBdCaseInfoDos.stream()
                .map(item -> {
                    PartnerServiceInfo partnerServiceInfo = new PartnerServiceInfo();
                    partnerServiceInfo.fillByBdCaseInfo(item);
                    partnerServiceInfo.setPhone(shuidiCipher.decrypt(item.getRaiserPhone()));
                    partnerServiceInfo.setCreateTime(item.getCreateTime());
                    partnerServiceInfo.setLeaderUniqueName(Optional.ofNullable(
                            uniqueCodeMapVolunteer.get(item.getUniqueCode()))
                            .map(CrowdfundingVolunteer::getVolunteerName).orElse(""));
                    //查找对应的
                    return partnerServiceInfo;
                })
                .collect(Collectors.toList());
        result.setList(partnerServiceInfoList);
        return result;
    }
}
