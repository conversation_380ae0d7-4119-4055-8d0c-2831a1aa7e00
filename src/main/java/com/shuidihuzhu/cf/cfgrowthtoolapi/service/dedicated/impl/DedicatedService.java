package com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.biz.CfServiceStaffBiz;
import com.shuidihuzhu.cf.cfgrowthtoolapi.biz.CfWorkImGroupUserBiz;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMasterForGrowthtoolDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IQywxSdkDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.AccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.message.IMsgClientDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.WorkImGroupInfoAggregationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfServiceStaffTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.dedicated.impl.DedicatedFacadeImpl;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.GroupChangeInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.MdcFriendInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.WorkImGroupInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfClewForRecallModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffFriendCountDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfServiceStaffFriendDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfShuidichouWechatFriendDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupChangeRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupDetailModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupMemberModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfWorkImGroupUserDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.AddExternalContactModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.JudgeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.Tag;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.PatientSheQunTagServiceImpl;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.IDedicatedService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.mdc.impl.WeChatFriendServiceImpl;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.client.feign.cipher.CipherFeignClient;
import com.shuidihuzhu.cf.client.feign.cipher.FeignResponse;
import com.shuidihuzhu.cf.dao.MdcFriendInfoMapper;
import com.shuidihuzhu.cf.dao.WorkImGroupInfoAggregationDao;
import com.shuidihuzhu.cf.dao.dedicated.CfClewForRecallDao;
import com.shuidihuzhu.cf.dao.dedicated.CfServiceStaffFriendDao;
import com.shuidihuzhu.cf.dao.dedicated.CfShuidichouWechatFriendDao;
import com.shuidihuzhu.cf.dao.dedicated.CfWorkImGroupChangeRecordDao;
import com.shuidihuzhu.cf.dao.dedicated.CfWorkImGroupInfoDao;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSimpleTrueOrFalseEnum;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.api.model.CfServiceStaffFriendModel;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewQyWxCorpDO;
import com.shuidihuzhu.client.cf.growthtool.model.CustomerModel;
import com.shuidihuzhu.client.cf.growthtool.model.TagGroupModel;
import com.shuidihuzhu.client.cf.patientcrm.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.msg.vo.rpc.MsgRecord;
import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import com.shuidihuzhu.wx.grpc.client.feign.WxSubscribeEventServiceClient;
import com.shuidihuzhu.wx.grpc.client.feign.WxUserEventStatusServiceClient;
import com.shuidihuzhu.wx.grpc.model.WxMpSubscribeModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.joda.time.Days;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/1/3 11:09 PM
 */
@Service
@Slf4j
@RefreshScope
public class DedicatedService implements IDedicatedService {
    @Autowired
    private CfServiceStaffBiz cfServiceStaffBiz;
    @Autowired
    private CfServiceStaffFriendDao cfServiceStaffFriendDao;

    @Autowired
    private WxUserEventStatusServiceClient wxUserEventStatusServiceClient;

    @Autowired
    private CfShuidichouWechatFriendDao cfShuidichouWechatFriendDao;

    @Resource
    private AccountServiceDelegate accountServiceDelegate;

    @Autowired
    private WxSubscribeEventServiceClient wxSubscribeEventServiceClient;
    @Resource
    private IMsgClientDelegate msgClientV2;

    @Autowired
    private Environment environment;
    @Autowired
    private IQywxSdkDelegate qywxSdkDelegate;

    @Autowired
    private PatientCrmApiFeignClient patientCrmApiFeignClient;

    @Autowired
    private CfClewForRecallDao cfClewForRecallDao;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private ICfMasterForGrowthtoolDelegate cfMasterForGrowthtoolDelegate;

    @Autowired
    private CfWorkImGroupInfoDao cfWorkImGroupInfoDao;

    @Autowired
    private CfWorkImGroupChangeRecordDao cfWorkImGroupChangeRecordDao;

    @Autowired
    private CfWorkImGroupUserBiz cfWorkImGroupUserBiz;

    @Autowired
    private WorkImGroupInfoAggregationDao workImGroupInfoAggregationDao;
    @Autowired
    private IMqProducerService mqProducerService;
    @Autowired
    private WeChatFriendServiceImpl weChatFriendServiceImpl;
    @Autowired
    private PatientSheQunTagServiceImpl patientSheQunTagService;
    @Autowired
    private MdcFriendInfoMapper mdcFriendInfoMapper;
    @Autowired
    private AlarmClient alarmClient;
    @Autowired
    private CipherFeignClient cipherFeignClient;


    @Value("${apollo.modfriendphone.teststaff:FengSha,MengBuZhiJiaoBuBuXi,chenwanxia,NiYanJingXiaoQiLaiZhenHaoKan,XiaoQian,FeiZai,lifeifei,chenbing}")
    private String teststaffStr;

    private static final Set<Long> TESTUSERIDS = Sets.newHashSet(244114036L, 269382306L, 269384744L);


    public static Map<String, String> SQ_CORP_IDS = Maps.newHashMap();
    static {
        SQ_CORP_IDS.put("ww7e123ba6d95a5ffc", "BfZr_ZqpxhZDfy3MDHHPA-nA5ij-KhPsPFLLzo3sR7U");//水滴筹病友之家
        SQ_CORP_IDS.put("wwb8cc2f5c0fc58917", "WWsq-yLgsFsy8na7M14Z6K45lxjgBLyfNw15hIuRfrE");//水滴筹
        SQ_CORP_IDS.put("wwf67888370c3563f8", "7DqVM86hSCvfUQTuQGqTSe_sp039gEQ7WuX5WYjltx0");//水滴筹爱心平台
        SQ_CORP_IDS.put("wwb72bdb1577c65797", "D7m-P5p1VJvtBDUU0LSf_ygHFS5vI0UYFXgd3USzUgc");//水滴筹大健康
        SQ_CORP_IDS.put("ww54aed0dc0e0d80ad", "WNzhUcNbuH0MdHJLoi23-Euj-_SjsVFBK11Y2ccKrYU");//水滴筹服务
        SQ_CORP_IDS.put("ww70c287e38e3cd1b8", "GsbVMiwnjqymd5HNX3LWDipCxzKJB0nUwHzD8hH4KNE");//水滴筹大病救助
        SQ_CORP_IDS.put("ww2860595d3bb712e9", "VHQ_BoTvE_N4mqwl9jDp8yaCA7RNyucfaceDJvpaz6Q");//水滴康复
    }

    private LoadingCache<Integer,List<CfServiceStaffFriendCountDO>> cfServiceStaffFriendCountDOResultsCache = CacheBuilder
            .newBuilder()
            .maximumSize(10)
            .refreshAfterWrite(2, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer,List<CfServiceStaffFriendCountDO>>() {
                @Override
                public List<CfServiceStaffFriendCountDO> load(Integer type) throws Exception {
                    return getCfServiceStaffFriendAmountNew(type);
                }
            });
    /**
     * 水滴筹健康生活（61）>水滴筹健康（115）>水滴筹健康管家（116）>水滴健康保障（17）>水滴筹（3）
     */
    private static List<Integer> majorMp = Arrays.asList(
            AccountThirdTypeEnum.WX_CF_SMALL_MP_61.getCode(),
            AccountThirdTypeEnum.WX_CF_MP_HEALTH.getCode(),
            AccountThirdTypeEnum.WX_CF_MP_INSURANCE.getCode(),
            AccountThirdTypeEnum.WX_CF_SERIOUS_ILLNESS.getCode(),
            GeneralConstant.FUNDRAISER_THIRD_TYPE);

    @Override
    public OpResult<CfServiceStaffFriendDO> getCfServiceStaffFriendByUserId(Long userId) {
        CfServiceStaffFriendDO cfServiceStaffFriendDO = cfServiceStaffFriendDao.getCfServiceStaffFriendByUserId(userId);
        Optional.ofNullable(cfServiceStaffFriendDO)
                .filter(r -> StringUtils.isNotBlank(r.getQyWechatUserIdEncrypt()))
                .ifPresent(r -> r.setQyWechatUserId(ShuidiCipherUtils.decrypt(r.getQyWechatUserIdEncrypt())));
        return cfServiceStaffFriendDO==null? OpResult.createFailResult(CfErrorCode.DEFAULT): OpResult.createSucResult(cfServiceStaffFriendDO);
    }

    @Override
    public OpResult<List<CfServiceStaffFriendDO>> getAllCfServiceStaffFriendsByUserId(Long userId) {
        List<CfServiceStaffFriendDO> retList = cfServiceStaffFriendDao.getAllCfServiceStaffFriendsByUserId(userId);
        if(CollectionUtils.isEmpty(retList)){
            return OpResult.createSucResult(null);
        }
        retList.stream()
                .filter(r -> StringUtils.isNotBlank(r.getQyWechatUserIdEncrypt()))
                .forEach(r -> r.setQyWechatUserId(ShuidiCipherUtils.decrypt(r.getQyWechatUserIdEncrypt())));
        return OpResult.createSucResult(retList);
    }

    @Override
    public OpResult<List<CfShuidichouWechatFriendDO>> getCfServiceStaffFriendByUnionId(String unionId) {
        List<CfShuidichouWechatFriendDO> cfShuidichouWechatFriendDOList = cfShuidichouWechatFriendDao.getCfShuidichouWechatFriendByUnionId(unionId);
        return CollectionUtils.isEmpty(cfShuidichouWechatFriendDOList)? OpResult.createFailResult(CfErrorCode.DEFAULT): OpResult.createSucResult(cfShuidichouWechatFriendDOList);
    }

    @Override
    public int saveCfServiceStaffFriend(CfServiceStaffFriendDO cfServiceStaffFriendDO) {
        return cfMasterForGrowthtoolDelegate.insertCfServiceStaffFriendDO(cfServiceStaffFriendDO);
    }
    @Override
    public int saveCfShuidichouWechatFriend(CfShuidichouWechatFriendDO cfShuidichouWechatFriendDO) {
        return cfShuidichouWechatFriendDao.insert(cfShuidichouWechatFriendDO);
    }

    @Override
    public int updateCfServiceStaffFriendByCommonUserId(CfServiceStaffFriendDO cfServiceStaffFriendDO) {
        if (cfServiceStaffFriendDO.getIsApply()==null && StringUtils.isEmpty(cfServiceStaffFriendDO.getQyWechatUserId())){
            return 0;
        }
        return cfMasterForGrowthtoolDelegate.updateQyWechatUserIdByUserId(cfServiceStaffFriendDO);
    }

    @Override
    public OpResult<List<CfServiceStaffDO>> getCfServiceStaffList(Integer type) {
        // 查询专属客服   2代表专属客服
        List<CfServiceStaffDO> cfServiceStaffDOList = cfServiceStaffBiz.getCfServiceStaffList(type);
        return CollectionUtils.isEmpty(cfServiceStaffDOList) ? OpResult.createFailResult(CfErrorCode.DEFAULT) : OpResult.createSucResult(cfServiceStaffDOList);
    }

    @Override
    public OpResult<CfServiceStaffDO> getCfServiceStaffByQyWechatUserId(String qyWechatUserId) {
        CfServiceStaffDO cfServiceStaffDO = cfServiceStaffBiz.getByQyWechatUserId(qyWechatUserId);
        return cfServiceStaffDO == null ? OpResult.createFailResult(CfErrorCode.DEFAULT) : OpResult.createSucResult(cfServiceStaffDO);
    }


    @Override
    public OpResult<List<CfServiceStaffDO>> getCfServiceStaffByQyWechatUserIds(List<String> qyWechatUserIds) {
        List<CfServiceStaffDO> cfServiceStaffDOS = cfServiceStaffBiz.getCfServiceStaffByQyWechatUserIds(qyWechatUserIds);
        if (CollectionUtils.isEmpty(cfServiceStaffDOS)) {
            return OpResult.createSucResult(null);
        }
        return OpResult.createSucResult(cfServiceStaffDOS);
    }

    @Override
    public List<CfServiceStaffFriendCountDO> getCfServiceStaffFriendAmount(Date time, List<CfServiceStaffDO> dedicatedCfServiceStaff) {
        if (CollectionUtils.isEmpty(dedicatedCfServiceStaff)) {
            return Lists.newArrayList();
        }
        List<String> qyWechatUserIds = dedicatedCfServiceStaff.stream().map(CfServiceStaffDO::getQyWechatUserId).distinct().collect(Collectors.toList());
        List<CfServiceStaffFriendCountDO> dedicatedCustomerSelectedModels;
        Map<String, String> encrypts = Optional.ofNullable(cipherFeignClient.encryptContentLegacyBatch(qyWechatUserIds))
                .filter(r -> r.ok() && MapUtils.isNotEmpty(r.getData()))
                .map(FeignResponse::getData)
                .orElse(Collections.emptyMap());
        dedicatedCustomerSelectedModels = cfServiceStaffFriendDao.getCfServiceStaffFriendAmountNewEncrypt(DateUtil.getCurrentDate(), Lists.newArrayList(encrypts.values()));
        // 翻转加密后的企业微信用户ID映射
        Map<String, String> reverseEncrypts = encrypts.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
        // 将加密后的企业微信用户ID替换回原始ID
        dedicatedCustomerSelectedModels.forEach(model -> {
            String encryptedId = model.getQyWechatUserIdEncrypt();
            model.setQyWechatUserId(reverseEncrypts.get(encryptedId));
        });

        Map<String, CfServiceStaffFriendCountDO> qyWechatUserIdMap = dedicatedCustomerSelectedModels.stream().collect(Collectors.toMap(CfServiceStaffFriendCountDO::getQyWechatUserId, Function.identity()));
        for (String qyWechatUserId : qyWechatUserIds) {
            CfServiceStaffFriendCountDO dedicatedCustomerSelectedModel = qyWechatUserIdMap.get(qyWechatUserId);
            if (dedicatedCustomerSelectedModel == null) {
                CfServiceStaffFriendCountDO selectedModel = new CfServiceStaffFriendCountDO();
                selectedModel.setQyWechatUserId(qyWechatUserId);
                dedicatedCustomerSelectedModels.add(selectedModel);
            }
        }
        return dedicatedCustomerSelectedModels.stream()
                .sorted(Comparator.comparing(CfServiceStaffFriendCountDO::getFriendAmount))
                .collect(Collectors.toList());
    }

    private List<CfServiceStaffFriendCountDO> getCfServiceStaffFriendAmountNew(Integer type) {
        return this.getCfServiceStaffFriendAmount(DateUtil.getCurrentDate(), cfServiceStaffBiz.getCfServiceStaffList(type));
    }

    @Override
    public List<CfServiceStaffFriendCountDO> getCfServiceStaffFriendAmountNew(Boolean isFromApp) {
        List<CfServiceStaffFriendCountDO> daoList = null;
        try {
            Integer type = CfServiceStaffTypeEnum.ONE_TO_ONE_FORC.getType();
            if (isFromApp) {
                type = CfServiceStaffTypeEnum.APP_RECOMMEND.getType();
            }
            daoList = cfServiceStaffFriendCountDOResultsCache.get(type);
        } catch (ExecutionException e) {
            log.error(this.getClass().getSimpleName() + " cfServiceStaffFriendCountDOResultsCache.get err:", e);
        }
        log.info("getCfServiceStaffFriendAmountNew finally result daoList:{}", JSON.toJSONString(daoList));
        return daoList;
    }

    @Override
    public List<String> getExternalUseridByQyWechatUserIdWithPhone(String qyWechatUserId, String phone) {
        return cfShuidichouWechatFriendDao.getExternalUseridByQyWechatUserIdWithEncryptPhone(qyWechatUserId, ShuidiCipherUtils.encrypt(phone));
    }

    @Override
    public List<CfServiceStaffFriendModel> getCfServiceStaffFriendByUnionIdWithQyWechatUserId(String unionId,
                                                                                              String qyWechatUserId) {
        List<CfShuidichouWechatFriendDO> cfShuidichouWechatFriendDOList = cfShuidichouWechatFriendDao.getCfShuidichouWechatFriendByUnionIdWithQyWechatUserId(unionId, qyWechatUserId);
        return CfShuidichouWechatFriendDO.getCfServiceStaffFriendModelList(cfShuidichouWechatFriendDOList, shuidiCipher);
    }

    @Override
    public CfServiceStaffDO getByQyWechatUserId(String qyWechatUserId) {
        return cfServiceStaffBiz.getByQyWechatUserId(qyWechatUserId);
    }

    @Override
    public List<CfShuidichouWechatFriendDO> getCfShuidichouWechatFriendByExternalUseridWithQyWechatUserId(String externalUserid,
                                                                                                          String qyWechatUserId) {
        return cfShuidichouWechatFriendDao.getCfShuidichouWechatFriendByExternalUseridWithQyWechatUserId(externalUserid, qyWechatUserId);
    }


    @Override
    public List<CfShuidichouWechatFriendDO> getWechatFriendByExternalUseridWithQyWechatUserIdIgnoreDelete(String externalUserid,
                                                                                                          String qyWechatUserId) {
        return cfShuidichouWechatFriendDao.getWechatFriendByExternalUseridWithQyWechatUserIdIgnoreDelete(externalUserid, qyWechatUserId);
    }

    @Override
    public int saveCfServiceStaffDO(CfServiceStaffDO cfServiceStaffDO) {
        return cfMasterForGrowthtoolDelegate.insertCfServiceStaffDO(cfServiceStaffDO);
    }

    @Override
    public List<WxMpSubscribeModel> listUserSubscribeOfMajor(long userId) {
        try {


            List<WxMpSubscribeModel> subscribeUserModels = wxUserEventStatusServiceClient.listSubscribeByUserId(userId, majorMp);

            log.info("listSubscribeByUserId userId:{} subscribe:{}", userId, JSON.toJSONString(subscribeUserModels));
            List<WxMpSubscribeModel> result = subscribeUserModels.stream()
                    .filter(wxMpSubscribeModel -> wxMpSubscribeModel.isSubscribe())
                    .collect(Collectors.toList());
            return result;
        } catch (Exception e) {
            log.error("userId:{} listUserSubscribeOfMajor error ", userId, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public void updateServiceStaffUserInfo(String qywechatid, Integer serviceType, String qrCode, String headUrl, Integer id, Integer helpPatients, Integer raiseAmount, String labels,String qyWechatQrCode) {
        cfMasterForGrowthtoolDelegate.updateServiceStaffUserInfo(qywechatid, serviceType, qrCode, headUrl, id,helpPatients,raiseAmount,labels,"99%",qyWechatQrCode);
    }
    @Override
    public int updateShowTimeByQyWechatUserId(String qywechatid,String showTime){
        return cfMasterForGrowthtoolDelegate.updateShowTimeByQyWechatUserId(qywechatid, showTime);
    }

    @Override
    public OpResult<CfServiceStaffFriendDO> getCfServiceStaffFriendByUserIdFromApp(long userId, Date startDate, Date endDate,Boolean isFromApp) {
        List<CfServiceStaffFriendDO> cfServiceStaffFriendDOList = cfServiceStaffFriendDao.getCfServiceStaffFriendByUserIdFromApp(userId,startDate,endDate);
        if(CollectionUtils.isEmpty(cfServiceStaffFriendDOList)){
            return OpResult.createFailResult(CfErrorCode.DEFAULT);
        }
        cfServiceStaffFriendDOList.stream()
                .filter(r -> StringUtils.isNotBlank(r.getQyWechatUserIdEncrypt()))
                .forEach(r -> r.setQyWechatUserId(ShuidiCipherUtils.decrypt(r.getQyWechatUserIdEncrypt())));
        Set<String> qywechatUserIds = cfServiceStaffFriendDOList.stream().map(CfServiceStaffFriendDO::getQyWechatUserId).collect(Collectors.toSet());
        List<CfServiceStaffDO> cfServiceStaffDOS = cfServiceStaffBiz.getCfServiceStaffByQyWechatUserIds(Lists.newArrayList(qywechatUserIds));
        if(CollectionUtils.isEmpty(cfServiceStaffDOS)){
            return OpResult.createFailResult(CfErrorCode.DEFAULT);
        }
        if(isFromApp) {
            cfServiceStaffDOS = cfServiceStaffDOS.stream().filter(s -> CfServiceStaffTypeEnum.APP_RECOMMEND.getType().equals(s.getType())).filter(s -> StringUtils.isNotBlank(s.getWeiXinCode())).collect(Collectors.toList());
        }else{
            cfServiceStaffDOS = cfServiceStaffDOS.stream().filter(s -> CfServiceStaffTypeEnum.ONE_TO_ONE_FORC.getType().equals(s.getType())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(cfServiceStaffDOS)){
            return OpResult.createFailResult(CfErrorCode.DEFAULT);
        }
        qywechatUserIds = cfServiceStaffDOS.stream().map(CfServiceStaffDO::getQyWechatUserId).collect(Collectors.toSet());

        CfServiceStaffFriendDO first = null;
        for(CfServiceStaffFriendDO cfServiceStaffFriendDO : cfServiceStaffFriendDOList){
            if(qywechatUserIds.contains(cfServiceStaffFriendDO.getQyWechatUserId())){
                first = cfServiceStaffFriendDO;
                break;
            }
        }
        if(first == null){
            return OpResult.createFailResult(CfErrorCode.DEFAULT);
        }

        return OpResult.createSucResult(first);
    }

    /**
     * 将 数据库中已经存在但是没有phone且is_service_user=0的好友 回写一下phone
     */
    @Override
    public void fullPhoneForCfServiceStaffFriendInDB(Date startDay,Date endDay,Integer callBackId){
        String accessToken = "";
        List<CfShuidichouWechatFriendDO> noPhoneList = cfShuidichouWechatFriendDao.getNoEncryptPhoneByCallBackId(startDay,endDay,callBackId);
        if (CollectionUtils.isEmpty(noPhoneList)){
            return;
        }
        for (CfShuidichouWechatFriendDO wechatFriendDO : noPhoneList) {
            CfClewQyWxCorpDO cfClewQyWxCorpDO = qywxSdkDelegate.getWxCorpMsgByCallbackId(wechatFriendDO.getCallBackId());
            if (cfClewQyWxCorpDO != null) {
                accessToken = qywxSdkDelegate.getAccessToken(CfSimpleTrueOrFalseEnum.FALSE, cfClewQyWxCorpDO.getCorpId(), cfClewQyWxCorpDO.getAppSecret());
            }
            if (StringUtils.isEmpty(accessToken)){
                continue;
            }
            JSONObject resultJsonObject = qywxSdkDelegate.getExternalContactResult(accessToken, wechatFriendDO.getExternalUserid(), cfClewQyWxCorpDO);
            if (null != resultJsonObject) {
                ImmutableTriple<String, String, String> immutablePair = qywxSdkDelegate.getPhoneAndPassTimeByExternalContactResult(resultJsonObject, wechatFriendDO.getQyWechatUserId(),wechatFriendDO.getCallBackId() ,qywxSdkDelegate.getUnionIdByExternalContactResult(resultJsonObject));
                log.info("resultJsonObject:{},phone:{}",resultJsonObject,immutablePair.getLeft());
                wechatFriendDO.setEncryptPhone(ShuidiCipherUtils.encrypt(immutablePair.getLeft()));
            }
        }
        this.batchUpdateServiceStaffFriendOfPhone(noPhoneList);
    }



    private CfServiceStaffFriendDO.isServiceUserEnum getIsServiceUserEnum(Date passTime,String phone){
        if (StringUtils.isNotBlank(phone)){
            return CfServiceStaffFriendDO.isServiceUserEnum.YES;
        }
        if (passTime.after(DateUtil.addDay(DateUtil.getCurrentDate(),-9))){
            return CfServiceStaffFriendDO.isServiceUserEnum.YES;
        }
        return CfServiceStaffFriendDO.isServiceUserEnum.NO;
    }

    @Override
    public int handleServiceFriendsSendMsg() {
        int count = 0;
        Date today = DateUtil.getCurrentDate();
        Date begin = DateUtils.addDays(today,-3);
        Date end = DateUtils.addDays(today,-2);

        List<CfShuidichouWechatFriendDO> cfShuidichouWechatFriendDOList = this.getServiceStaffFriendsByDate(begin, end);
        if(CollectionUtils.isEmpty(cfShuidichouWechatFriendDOList)){
            return count;
        }

        for(CfShuidichouWechatFriendDO cfShuidichouWechatFriendDO : cfShuidichouWechatFriendDOList){
            try{
                if(StringUtils.isEmpty(cfShuidichouWechatFriendDO.getUnionId())){
                    continue;
                }
                UserInfoModel userThirdModel = accountServiceDelegate.getByUnionId(cfShuidichouWechatFriendDO.getUnionId());
                if(userThirdModel == null){
                    log.info("handleServiceFriendsSendMsg getThirdModelWithOpenId {} null",cfShuidichouWechatFriendDO.getUnionId());
                    continue;
                }
                if (environment.acceptsProfiles("production")) {
                    handleMsgStatus(userThirdModel.getUserId(), "1530model2");
                    count++;
                }else{
                    if(TESTUSERIDS.contains(userThirdModel.getUserId())){
                        handleMsgStatus(userThirdModel.getUserId(),"1530model2");
                        count++;
                    }
                }
            }catch (Exception e){
                log.info("handleServiceFriendsSendMsg handle fail {}", JSON.toJSONString(cfShuidichouWechatFriendDO));
            }
        }
        return count;
    }

    private void handleMsgStatus(long userId, String modelNum) {
        log.info("handleMsgStatus 入参：userId:{},modelNum:{}", userId, modelNum);
        //关注过五大主号任意一个
        //todo 方法缺失 待资源方上线后补充
        int thirdType = GeneralConstant.FUNDRAISER_THIRD_TYPE;

        List<com.shuidihuzhu.wx.grpc.model.WxSubscribeModel> subscribeModelList = wxSubscribeEventServiceClient.batchCheckSubscribeByUserId(userId, Lists.newArrayList(899, 17, 115, 116, 61));
        if (CollectionUtils.isNotEmpty(subscribeModelList)) {
            thirdType = subscribeModelList.get(0).getThirdType();
        }

        MsgRecord msgRecord = new MsgRecord()
                .buildUserId(userId)
                .buildThirdType(thirdType);
        MsgRecordBatch msgRecordBatch = MsgRecordBatch.build(modelNum, "", Arrays.asList(msgRecord));
        msgClientV2.saveBatch(msgRecordBatch);
    }

    @Override
    public List<String> getAllQyWechatUserId() {
        return cfServiceStaffBiz.getAllQyWechatUserId();
    }


    @Override
    public List<CfShuidichouWechatFriendDO> getYesterdayServiceStaffFriends() {
        Date today = DateUtil.getCurrentDate();
        Date yestoryday = DateUtils.addDays(today, -1);
        return cfShuidichouWechatFriendDao.getCfShuidichouWechatFriendsByDate(yestoryday, today);
    }

    @Override
    public List<CfShuidichouWechatFriendDO> getServiceStaffFriendsByDate(Date begin, Date end) {
        if (begin == null || end == null || !begin.before(end)) {
            return Lists.newArrayList();
        }

        return cfShuidichouWechatFriendDao.getCfShuidichouWechatFriendsByDate(begin, end);
    }

    private int batchUpdateServiceStaffFriendOfPhone(List<CfShuidichouWechatFriendDO> cfShuidichouWechatFriendDOList) {
        int count = 0;
        int num = cfShuidichouWechatFriendDOList.size() / 500;
        int modulo = cfShuidichouWechatFriendDOList.size() % 500;
        for (int i = 0; i < num; i++) {
            int result = cfShuidichouWechatFriendDao.batchUpdateServiceStaffFriendOfPhone(cfShuidichouWechatFriendDOList.subList(i * 500, (i + 1) * 500));
            count += result;
        }
        if (modulo > 0) {
            int result = cfShuidichouWechatFriendDao.batchUpdateServiceStaffFriendOfPhone(cfShuidichouWechatFriendDOList.subList(cfShuidichouWechatFriendDOList.size() - modulo, cfShuidichouWechatFriendDOList.size()));
            count += result;
        }
        return count;
    }

    @Override
    public String getQyWechatUserIdByuserEncryPhone(Date currentTime, String userEncryPhone) {
        return ShuidiCipherUtils.decrypt(cfServiceStaffFriendDao.getQyWechatUserIdEncryptByuserEncryPhone(currentTime, userEncryPhone));
    }

    @Override
    public String getQyWechatUserIdByuserId(Date currentTime, long userId) {
        return ShuidiCipherUtils.decrypt(cfServiceStaffFriendDao.getQyWechatUserIdEncryptByuserId(currentTime, userId));
    }

    @Override
    public CfClewForRecallModel getCfClewForRecallModelByClewDisplayId(String clewDisplayId) {
        return cfClewForRecallDao.getCfClewForRecallModelByClewDisplayId(clewDisplayId);
    }

    @Override
    public void updateIsHandledById(List<Integer> ids) {
        cfClewForRecallDao.updateIsHandledById(ids);
    }


    @Override
    public List<CfClewForRecallModel> getModelForSendByNoHandled(int id, int limit) {
        return cfClewForRecallDao.getModelForSendByNoHandled(id, limit);
    }

    @Override
    public int batchUpdateClewDisplayIdWithEagleStatus(List<CfClewForRecallModel> cfClewForRecallModels) {
        return cfClewForRecallDao.batchUpdateClewDisplayIdWithEagleStatus(cfClewForRecallModels);
    }

    @Override
    public List<CfClewForRecallModel> getModelByClewPhone(String clewPhone) {
        return cfClewForRecallDao.getModelByClewPhone(clewPhone);
    }

    @Override
    public List<CfServiceStaffDO> getCfServiceStaffByShowTime(List<CfServiceStaffDO> cfServiceStaffDOS, Date currentDayStartTime, Date currentDayEndTime) {
        if (currentDayEndTime.before(currentDayStartTime)) {
            log.error("getCfServiceStaffByShowTime 参数不合法 currentDayStartTime:{}  应该小于  currentDayEndTime:{}",
                    DateUtil.getYmdhmsFromTimestamp(currentDayStartTime.getTime()),
                    DateUtil.getYmdhmsFromTimestamp(currentDayEndTime.getTime()));
            return Lists.newArrayList();
        }
        Date currentDate = new Date();
        int currentDayOfWeek = DateUtil.getWeekNumber(currentDate);
        List<CfServiceStaffDO> accordCfserviceStaffList = Lists.newArrayList();
        // 时间在 昨日21点30分到今天9点30分  则选择当天 周期的人
        if (currentDate.before(currentDayStartTime)) {
            accordCfserviceStaffList = cfServiceStaffDOS.stream().filter(cfServiceStaffDO -> {
                if (Lists.newArrayList(cfServiceStaffDO.getShowTime().split(GeneralConstant.splitChar)).contains(String.valueOf(currentDayOfWeek))) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            log.info("时间在 当天：{}之前  则选择当天 周期的人 {} {}", DateUtil.getYmdhmsFromTimestamp(currentDayStartTime.getTime()),
                    currentDayOfWeek, accordCfserviceStaffList);
        }
        // 时间在 当天19点后 则选择 当天周期+1 的人
        if (currentDate.after(currentDayEndTime)) {
            accordCfserviceStaffList = cfServiceStaffDOS.stream().filter(cfServiceStaffDO -> {
                if (Lists.newArrayList(cfServiceStaffDO.getShowTime().split(GeneralConstant.splitChar)).contains(String.valueOf(currentDayOfWeek % Days.SEVEN.getDays() + 1))) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            log.info("时间在 当天：{}之后 则选择 当天周期+1 的人 {} {}", DateUtil.getYmdhmsFromTimestamp(currentDayEndTime.getTime()),
                    currentDayOfWeek + 1, accordCfserviceStaffList);
        }
        return accordCfserviceStaffList;
    }

    @Override
    public CustomerModel buildCustomerModel(CfServiceStaffDO cfServiceStaffDO) {
        CustomerModel customerModel = CustomerModel.builder()
                .headUrl(cfServiceStaffDO.getHeadUrl())
                .customerName(cfServiceStaffDO.getName())
                .qrCode(cfServiceStaffDO.getQrCode())
                .labelList(Lists.newArrayList(cfServiceStaffDO.getLabels().split(",")))
                .userId(cfServiceStaffDO.getQyWechatUserId())
                .helpPatients(cfServiceStaffDO.getHelpPatients())
                //单位:(万元)
                .raiseAmount(Double.valueOf(cfServiceStaffDO.getRaiseAmount() / 10000))
                .favorableRate(cfServiceStaffDO.getFavorableRate())
                .raiseAmountText(DedicatedFacadeImpl.getRaiseMountText(cfServiceStaffDO.getRaiseAmount()))
                .qyWechatQrCode(cfServiceStaffDO.getQyWechatQrCode())
                .weiXinCode(cfServiceStaffDO.getWeiXinCode())
                .build();
        return customerModel;
    }

    @Override
    public int updateServiceStaffInfo(CfServiceStaffDO cfServiceStaffDO) {
        return cfMasterForGrowthtoolDelegate.updateCfServiceStaffById(cfServiceStaffDO);
    }

    @Override
    public OpResult<Long> modStaffFriendPhoneBackDoor(String qyWechatUserId, String externalUserid, String phone) {
        if (MobileUtil.illegal(phone)) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.PHONE_IS_ERROR);
        }
        List<String> testStaffList = Splitter.on(",").splitToList(teststaffStr);
        //不在白名单中，认为是非法用户
        if (!testStaffList.contains(qyWechatUserId)) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ILLEGALLY);
        }
        List<CfShuidichouWechatFriendDO> list = this.getCfShuidichouWechatFriendByExternalUseridWithQyWechatUserId(externalUserid, qyWechatUserId);
        if (CollectionUtils.isEmpty(list)) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        CfShuidichouWechatFriendDO cfShuidichouWechatFriendDO = list.get(0);
        cfShuidichouWechatFriendDO.setEncryptPhone(ShuidiCipherUtils.encrypt(phone));
        int ret = cfShuidichouWechatFriendDao.updateEncryptPhone(cfShuidichouWechatFriendDO);
        if (ret > 0) {
            return OpResult.createSucResult(Long.valueOf(cfShuidichouWechatFriendDO.getId()));
        } else {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
        }
    }

    @Override
    public int updateEncryptPhoneByIds(List<Integer> ids, String encryptPhone) {
        return cfShuidichouWechatFriendDao.updateEncryptPhoneByIds(ids, encryptPhone);
    }

    @Override
    public int updateUnionid(List<Integer> ids,
                             String unionid) {
        return cfShuidichouWechatFriendDao.updateUnionid(ids, unionid);
    }

    @Override
    public List<String> getExternalUseridByQyWechatUserIdsWithPhone(List<String> qyWechatUserIds, String phone) {
        if (CollectionUtils.isEmpty(qyWechatUserIds) || StringUtils.isBlank(phone)) {
            return Lists.newArrayList();
        }
        return cfShuidichouWechatFriendDao.getExternalUseridByQyWechatUserIdsWithPhone(qyWechatUserIds, ShuidiCipherUtils.encrypt(phone));
    }

    @Override
    public int insert(CfWorkImGroupInfoDO cfWorkImGroupInfoDO) {
        int ret = cfWorkImGroupInfoDao.insert(cfWorkImGroupInfoDO);
        this.saveOrUpdateWorkImGroupInfoAggregationDO(cfWorkImGroupInfoDO);
        return ret;
    }

    @Override
    public int updateToDelte(String chatId) {
        return cfWorkImGroupInfoDao.updateToDelte(chatId);
    }

    @Override
    public int insert(CfWorkImGroupChangeRecordDO cfWorkImGroupChangeRecordDO) {
        return cfWorkImGroupChangeRecordDao.insert(cfWorkImGroupChangeRecordDO);
    }

    @Override
    public CfWorkImGroupChangeRecordDO queryByChatIdAndDateStr(String chatId, String dateStr) {
        return cfWorkImGroupChangeRecordDao.queryByChatIdAndDateStr(chatId, dateStr);
    }

    @Override
    public int updateChangeCount(long id, int changeIn, int changeOut) {
        return cfWorkImGroupChangeRecordDao.updateChangeCount(id, changeIn, changeOut);
    }

    @Override
    public int updateToDelte(String userID, String externalUserID, int type) {
        return cfShuidichouWechatFriendDao.updateToDelte(userID, externalUserID, type);
    }

    @Override
    public int updateExternalUserName(List<CfShuidichouWechatFriendDO> friendList, String externalUserName, String externalUserTags) {
        List<Integer> idList = friendList.stream().map(CfShuidichouWechatFriendDO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)){
            return 0;
        }
        Optional<String> externalUserTagOption = friendList.stream()
                .map(CfShuidichouWechatFriendDO::getExternalUserTags)
                .filter(StringUtils::isNotEmpty)
                .findFirst();
        try{
            //数据库中存储的标签都为空或企业微信最新标签为空,则直接使用企业微信最新标签
            if (externalUserTagOption.isEmpty() || StringUtils.isEmpty(externalUserTags)){
                return cfShuidichouWechatFriendDao.updateExternalUserName(idList, externalUserName, externalUserTags);
            }else{
                //数据库中存储的标签不为空
                Map<String,Tag> tagsInDbMap = JSON.parseArray(externalUserTagOption.get(), Tag.class)
                        .stream()
                        .collect(Collectors.toMap(Tag::getTag_id,Function.identity(),(oldVal,newVal)->newVal));
                List<Tag> tagsNewFromQw = JSON.parseArray(externalUserTags, Tag.class);
                for (Tag tag : tagsNewFromQw){
                    Tag tagInDb = tagsInDbMap.get(tag.getTag_id());
                    if (Objects.nonNull(tagInDb)){
                        tag.setPushFlag(tagInDb.getPushFlag());
                    }
                }
                return cfShuidichouWechatFriendDao.updateExternalUserName(idList, externalUserName, JSONArray.toJSONString(tagsNewFromQw));
            }
        }catch (Exception e){
            log.warn(this.getClass().getSimpleName()+" updateExternalUserName friendList:{},externalUserTags:{} error",JSON.toJSONString(friendList),externalUserTags,e);
            alarmClient.sendByUser(Lists.newArrayList("lichengjin"),String.format(this.getClass().getSimpleName()+" updateExternalUserName friendList:%s,externalUserTags:%s error",JSON.toJSONString(friendList),externalUserTags));
        }
        return 0;
    }

    @Override
    public int updateRemark(List<Integer> idList, String remark) {
        if (StringUtils.isBlank(remark)) return 0;
        return cfShuidichouWechatFriendDao.updateRemark(idList, remark);
    }

    @Override
    public int update(CfWorkImGroupInfoDO cfWorkImGroupInfoDO) {
        this.saveOrUpdateWorkImGroupInfoAggregationDO(cfWorkImGroupInfoDO);
        return cfWorkImGroupInfoDao.update(cfWorkImGroupInfoDO);
    }

    private void saveOrUpdateWorkImGroupInfoAggregationDO(CfWorkImGroupInfoDO cfWorkImGroupInfoDO) {
        try {
            WorkImGroupInfoAggregationDO dbAggregationDO = workImGroupInfoAggregationDao.getImGroupInfoRelationById(cfWorkImGroupInfoDO.getChatId());
            if (dbAggregationDO == null) {
                WorkImGroupInfoAggregationDO aggregationDO = new WorkImGroupInfoAggregationDO();
                aggregationDO.setBeforeGroupName("");
                aggregationDO.setGroupCreateTime(cfWorkImGroupInfoDO.getGroupCreateTime());
                aggregationDO.setGroupName(cfWorkImGroupInfoDO.getName());
                aggregationDO.setWxGroupChatid(cfWorkImGroupInfoDO.getChatId());
                workImGroupInfoAggregationDao.insert(aggregationDO);
            } else {
                if (dbAggregationDO.getGroupName().compareTo(cfWorkImGroupInfoDO.getName()) != 0) {
                    WorkImGroupInfoModel model = new WorkImGroupInfoModel();
                    model.setBeforeGroupName(dbAggregationDO.getGroupName());
                    model.setGroupName(cfWorkImGroupInfoDO.getName());
                    model.setWxGroupChatid(cfWorkImGroupInfoDO.getChatId());
                    workImGroupInfoAggregationDao.updateImGroupInfo(model);
                }
            }
        } catch (Exception e) {
            log.error("workImGroupInfoAggregationDao.insert exception:{}", e, JSON.toJSONString(cfWorkImGroupInfoDO));
        }
    }

    @Override
    public void syncGroupInfo(String cursor, CfClewQyWxCorpDO cfClewQyWxCorpDO) {
        if (StringUtils.isBlank(cursor)) {
            return;
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put("limit", 20);
        if (!"first".equals(cursor)) {
            param.put("cursor", cursor);
        }

        CfWorkImGroupInfoModel cfWorkImGroupInfoModel = qywxSdkDelegate.queryGroupInfo(param, cfClewQyWxCorpDO);
        if (cfWorkImGroupInfoModel == null
                || cfWorkImGroupInfoModel.getErrcode() != 0
                || CollectionUtils.isEmpty(cfWorkImGroupInfoModel.getGroup_chat_list())) {
            return;
        }

        List<CfWorkImGroupInfoDO> toAdd = Lists.newArrayList();
        //调用群详情
        for (CfWorkImGroupModel cfWorkImGroupModel : cfWorkImGroupInfoModel.getGroup_chat_list()) {
            CfWorkImGroupDetailModel cfWorkImGroupDetailModel = qywxSdkDelegate.getGroupChatDetail(cfWorkImGroupModel.getChat_id(), cfClewQyWxCorpDO);
            if (cfWorkImGroupDetailModel == null) {
                continue;
            }
            CfWorkImGroupInfoDO info = new CfWorkImGroupInfoDO();
            info.setChatId(cfWorkImGroupModel.getChat_id());
            info.setName(cfWorkImGroupDetailModel.getName());
            info.setNotice(cfWorkImGroupDetailModel.getNotice());
            info.setOwnerId(cfWorkImGroupDetailModel.getOwner());
            info.setGroupCreateTime(new Date(cfWorkImGroupDetailModel.getCreate_time() * 1000));
            info.setCorpId(cfClewQyWxCorpDO.getCorpId());
            this.insert(info);
            toAdd.add(info);
        }

        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            log.error("sync group info sleep error..", e);
        }

        String nextCursor = cfWorkImGroupInfoModel.getNext_cursor();
        syncGroupInfo(nextCursor, cfClewQyWxCorpDO);
    }


    @Override
    public void syncGroupUser(List<String> chatIds, String corpId) {
        if (!SQ_CORP_IDS.containsKey(corpId)) {
            return;
        }
        CfClewQyWxCorpDO cfClewQyWxCorpDO = new CfClewQyWxCorpDO();
        cfClewQyWxCorpDO.setCorpId(corpId);
        cfClewQyWxCorpDO.setAppSecret(SQ_CORP_IDS.get(corpId));
        for (String chatId : chatIds) {
            handleGroupUserData(chatId, cfClewQyWxCorpDO);
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                log.error("handleGroupUserData sleep error..", e);
            }
        }
    }

    @Override
    public void fixGroupUser(List<String> chatIds, String corpId) {
        if (!SQ_CORP_IDS.containsKey(corpId)) {
            return;
        }
        CfClewQyWxCorpDO cfClewQyWxCorpDO = new CfClewQyWxCorpDO();
        cfClewQyWxCorpDO.setCorpId(corpId);
        cfClewQyWxCorpDO.setAppSecret(SQ_CORP_IDS.get(corpId));
        for (String chatId : chatIds) {
            fixGroupUserData(chatId, cfClewQyWxCorpDO);
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                log.error("handleGroupUserData sleep error..", e);
            }
        }
    }

    @Override
    public void insertGroupInfoByChatId(List<String> chatIds, String corpId) {
        CfClewQyWxCorpDO cfClewQyWxCorpDO = new CfClewQyWxCorpDO();
        cfClewQyWxCorpDO.setCorpId(corpId);
        cfClewQyWxCorpDO.setAppSecret(SQ_CORP_IDS.get(corpId));
        for (String chatId : chatIds) {
            CfWorkImGroupDetailModel cfWorkImGroupDetailModel = qywxSdkDelegate.getGroupChatDetail(chatId, cfClewQyWxCorpDO);
            log.info("insertGroupInfoByChatId groupdetailModel:{}", JSON.toJSONString(cfWorkImGroupDetailModel));
            if (cfWorkImGroupDetailModel == null) {
                continue;
            }
            CfWorkImGroupInfoDO info = new CfWorkImGroupInfoDO();
            info.setChatId(chatId);
            info.setName(cfWorkImGroupDetailModel.getName());
            info.setNotice(cfWorkImGroupDetailModel.getNotice());
            info.setOwnerId(cfWorkImGroupDetailModel.getOwner());
            info.setGroupCreateTime(new Date(cfWorkImGroupDetailModel.getCreate_time() * 1000));
            info.setCorpId(cfClewQyWxCorpDO.getCorpId());
            cfWorkImGroupInfoDao.insert(info);
        }
    }

    @Override
    public void addDefaultTag(AddExternalContactModel model, CfClewQyWxCorpDO cfClewQyWxCorpDO) {
        List<TagGroupModel> tagGroupModelList = patientSheQunTagService.getAllGroupList(cfClewQyWxCorpDO);
        if (CollectionUtils.isEmpty(tagGroupModelList)) {
            return;
        }
        patientSheQunTagService.handleMarkTagModelByGroupName("进群状态","未入群",cfClewQyWxCorpDO,model.getUserID(),model.getExternalUserID());
        patientSheQunTagService.handleMarkTagModelByGroupName("会话存档状态","不同意",cfClewQyWxCorpDO,model.getUserID(),model.getExternalUserID());
        patientSheQunTagService.handleMarkTagModelByGroupName("好友状态","正常",cfClewQyWxCorpDO,model.getUserID(),model.getExternalUserID());
    }

    @Override
    public void saveFriendChannelInfo(CfShuidichouWechatFriendDO cfShuidichouWechatFriendDO) {
        if (cfShuidichouWechatFriendDO.getCallBackId() != 127 || cfShuidichouWechatFriendDO.getId() <= 0) {
            return;
        }
        QyWechatFriendModel friendModel = new QyWechatFriendModel();
        friendModel.setExternalUserId(cfShuidichouWechatFriendDO.getExternalUserid());
        friendModel.setQyWechatUserId(cfShuidichouWechatFriendDO.getQyWechatUserId());
        friendModel.setUnionId(cfShuidichouWechatFriendDO.getUnionId());
        friendModel.setCallbackId(cfShuidichouWechatFriendDO.getCallBackId());
        friendModel.setPassTime(cfShuidichouWechatFriendDO.getPassTime());
        Response<Channel1v1InfoDo> response = patientCrmApiFeignClient.selectChannel1v1Info(friendModel);
        log.info("saveFriendChannelInfo_selectChannel1v1Info_friendModel:{},response:{}", JSON.toJSONString(friendModel), JSON.toJSONString(response));
        if (response.notOk() || Objects.isNull(response.getData())) {
            return;
        }
        MdcFriendInfoDo mdcFriendInfoDo = buildMdcFriendInfoDo(cfShuidichouWechatFriendDO.getId(), response.getData());
        log.info("saveFriendChannelInfo_insertSelective_mdcFriendInfoDo:{}", JSON.toJSONString(mdcFriendInfoDo));
        int count = mdcFriendInfoMapper.insertSelective(mdcFriendInfoDo);
        log.info("saveFriendChannelInfo_insertSelective_mdcFriendInfoDo:{},count:{}", JSON.toJSONString(mdcFriendInfoDo), count);
    }

    private MdcFriendInfoDo buildMdcFriendInfoDo(int id, Channel1v1InfoDo channel1v1InfoDo) {
        MdcFriendInfoDo friendInfoDo = new MdcFriendInfoDo();
        friendInfoDo.setFriendId((long) id);
        friendInfoDo.setChannel(channel1v1InfoDo.getChannel());
        friendInfoDo.setUniqueCode(channel1v1InfoDo.getUniqueCode());
        return friendInfoDo;
    }

    private void fixGroupUserData(String chatId, CfClewQyWxCorpDO cfClewQyWxCorpDO) {
        CfWorkImGroupDetailModel cfWorkImGroupDetailModel = qywxSdkDelegate.getGroupChatDetail(chatId, cfClewQyWxCorpDO);
        if (cfWorkImGroupDetailModel == null || CollectionUtils.isEmpty(cfWorkImGroupDetailModel.getMember_list())) {
            return;
        }
        List<CfWorkImGroupMemberModel> memberBoList = cfWorkImGroupDetailModel.getMember_list();
        //查询未退群的群成员
        List<CfWorkImGroupUserDO> groupUserList = cfWorkImGroupUserBiz.findByChatIdAndValid(chatId, JudgeEnum.NO.getStatus());
        if (CollectionUtils.isEmpty(groupUserList)) {
            //因为本接口是补充一些原来值为空的字段，所以如果数据库记录不存在群用户的话，就不需要处理了
            return;
        }
        Map<String, CfWorkImGroupUserDO> userMap = Maps.newHashMap();
        for (CfWorkImGroupMemberModel memberBo : memberBoList) {
            CfWorkImGroupUserDO user = new CfWorkImGroupUserDO();
            user.setChatId(chatId);
            user.setUserOpenId(memberBo.getUserid());
            user.setUnionId(StringUtils.defaultIfBlank(memberBo.getUnionid(), ""));
            user.setJoinTime(new Date(memberBo.getJoin_time() * 1000));
            user.setUserType(memberBo.getType());
            user.setJoinScene(memberBo.getJoin_scene());
            user.setUserName(StringUtils.isNotBlank(memberBo.getName()) ? memberBo.getName() : "");
            user.setNickName(StringUtils.isNotBlank(memberBo.getGroup_nickname()) ? memberBo.getGroup_nickname() : "");
            user.setValid(JudgeEnum.NO.getStatus());
            user.setInvitorUserid("");
            user.setState(StringUtils.isNotBlank(memberBo.getState()) ? memberBo.getState() : "");
            if (memberBo.getInvitor() != null && StringUtils.isNotBlank(memberBo.getInvitor().getUserid())) {
                user.setInvitorUserid(memberBo.getInvitor().getUserid());
            }
            userMap.put(user.getUserOpenId(), user);
        }

        for (CfWorkImGroupUserDO groupUserDO : groupUserList) {
            CfWorkImGroupUserDO newDo = userMap.get(groupUserDO.getUserOpenId());
            if (newDo == null) {
                continue;
            }
            cfWorkImGroupUserBiz.updateFixParamsById(groupUserDO.getId(), newDo.getUserName(), newDo.getNickName(), newDo.getInvitorUserid(), newDo.getState());
        }

    }

    private void handleGroupUserData(String chatId, CfClewQyWxCorpDO cfClewQyWxCorpDO) {
        log.info("handleGroupUserData chatId:{},corpId:{}", chatId, cfClewQyWxCorpDO.getCorpId());
        CfWorkImGroupDetailModel cfWorkImGroupDetailModel = qywxSdkDelegate.getGroupChatDetail(chatId, cfClewQyWxCorpDO);
        if (cfWorkImGroupDetailModel == null || CollectionUtils.isEmpty(cfWorkImGroupDetailModel.getMember_list())) {
            log.info("handleGroupUserData cfWorkImGroupDetailModel is null");
            return;
        }
        List<CfWorkImGroupMemberModel> memberBoList = cfWorkImGroupDetailModel.getMember_list()
                .stream()
                .filter(item -> item.getJoin_time() > 0)
                .collect(Collectors.toList());
        //校验该群是否为群活码及疾病关联群
        GroupChannelParam groupChannelParam = new GroupChannelParam();
        groupChannelParam.setChatId(cfWorkImGroupDetailModel.getChat_id());
        groupChannelParam.setName(cfWorkImGroupDetailModel.getName());
        groupChannelParam.setCreateTime(cfWorkImGroupDetailModel.getCreate_time());
        groupChannelParam.setOwner(cfWorkImGroupDetailModel.getOwner());
        mqProducerService.sendGroupInfoCheckConfigEithChat(groupChannelParam);
        Map<String, GroupChannelInfoDo> groupChannelInfoDoMap = selectGroupChannelInfo(memberBoList, cfWorkImGroupDetailModel);
        //判断是否为mdc运营群
        boolean kChatFlag = checkKratosChatByChatId(chatId);
        //查询未退群的群成员
        List<CfWorkImGroupUserDO> groupUserList = cfWorkImGroupUserBiz.findByChatIdAndValid(chatId, JudgeEnum.NO.getStatus());
        if (CollectionUtils.isEmpty(groupUserList)) {
            groupUserList = Lists.newArrayList();
            for (CfWorkImGroupMemberModel memberBo : memberBoList) {
                CfWorkImGroupUserDO user = buildCfWorkImGroupUserDO(memberBo,chatId,groupChannelInfoDoMap);
                //处理用户在群状态标签相关
                if (kChatFlag && StringUtils.isNotBlank(user.getUnionId())) {
                    sendGroupChangeUnionIdMsg(chatId, user.getUnionId(), cfClewQyWxCorpDO.getCorpId(), cfClewQyWxCorpDO.getAppSecret(), JudgeEnum.NO.getStatus());
                }
                groupUserList.add(user);
            }
            if (CollectionUtils.isNotEmpty(groupUserList)) {
                cfWorkImGroupUserBiz.insertBatch(groupUserList);
            }
            return;
        }

        Map<String, CfWorkImGroupUserDO> groupUserMap = groupUserList.stream().collect(Collector.of(HashMap::new,
                (map, item) -> map.put(item.getUserOpenId(), item), (k, v) -> v, Collector.Characteristics.IDENTITY_FINISH));

        List<CfWorkImGroupUserDO> newUserList = Lists.newArrayList();
        for (CfWorkImGroupMemberModel memberBo : memberBoList) {
            CfWorkImGroupUserDO user = groupUserMap.get(memberBo.getUserid());
            //新用户
            if (user == null) {
                user = buildCfWorkImGroupUserDO(memberBo,chatId,groupChannelInfoDoMap);
                //处理用户在群状态标签相关
                if (kChatFlag && StringUtils.isNotBlank(user.getUnionId())) {
                    sendGroupChangeUnionIdMsg(chatId, user.getUnionId(), cfClewQyWxCorpDO.getCorpId(), cfClewQyWxCorpDO.getAppSecret(), JudgeEnum.NO.getStatus());
                }
                newUserList.add(user);
                continue;
            }
            groupUserMap.remove(memberBo.getUserid());
            //判断是否重新进群
            Date joinTime = new Date(memberBo.getJoin_time() * 1000);
            if (joinTime.after(user.getJoinTime())) {
                cfWorkImGroupUserBiz.updateAllById(memberBo.getType(), memberBo.getJoin_scene(), joinTime, JudgeEnum.NO.getStatus(), user.getId());
            }
        }
        if (CollectionUtils.isNotEmpty(newUserList)) {
            cfWorkImGroupUserBiz.insertBatch(newUserList);
        }
        if (MapUtils.isNotEmpty(groupUserMap)) {
            List<Long> ids = groupUserMap.values().stream().map(CfWorkImGroupUserDO::getId).collect(Collectors.toList());
            cfWorkImGroupUserBiz.updateValidByIds(JudgeEnum.YES.getStatus(), ids);
        }
        //处理用户在群状态标签相关
        for (Map.Entry<String, CfWorkImGroupUserDO> entry : groupUserMap.entrySet()) {
            sendGroupChangeUnionIdMsg(chatId, entry.getValue().getUnionId(), cfClewQyWxCorpDO.getCorpId(), cfClewQyWxCorpDO.getAppSecret(), JudgeEnum.YES.getStatus());
        }
    }

    private CfWorkImGroupUserDO buildCfWorkImGroupUserDO(CfWorkImGroupMemberModel memberBo, String chatId, Map<String, GroupChannelInfoDo> groupChannelInfoDoMap) {
        CfWorkImGroupUserDO user = new CfWorkImGroupUserDO();
        user.setChatId(chatId);
        user.setUserOpenId(memberBo.getUserid());
        user.setUnionId(StringUtils.defaultIfBlank(memberBo.getUnionid(), ""));
        user.setJoinTime(new Date(memberBo.getJoin_time() * 1000));
        user.setUserType(memberBo.getType());
        user.setJoinScene(memberBo.getJoin_scene());
        user.setUserName(StringUtils.isNotBlank(memberBo.getName()) ? memberBo.getName() : "");
        user.setNickName(StringUtils.isNotBlank(memberBo.getGroup_nickname()) ? memberBo.getGroup_nickname() : "");
        user.setValid(JudgeEnum.NO.getStatus());
        user.setInvitorUserid("");
        user.setState(StringUtils.isNotBlank(memberBo.getState()) ? memberBo.getState() : "");
        if (memberBo.getInvitor() != null && StringUtils.isNotBlank(memberBo.getInvitor().getUserid())) {
            user.setInvitorUserid(memberBo.getInvitor().getUserid());
        }
        user.setChannel("");
        user.setSubChannel("");
        user.setConfigId("");
        user.setUniqueCode("");
        //关联用户在群意向表
        if (StringUtils.isBlank(user.getUnionId()) || groupChannelInfoDoMap.get(user.getUnionId()) == null) {
            return user;
        }
        //关联用户在群意向表
        user.setChannel(StringUtils.isNotBlank(groupChannelInfoDoMap.get(user.getUnionId()).getChannel()) ? groupChannelInfoDoMap.get(user.getUnionId()).getChannel() : "");
        user.setSubChannel(StringUtils.isNotBlank(groupChannelInfoDoMap.get(user.getUnionId()).getSubChannel()) ? groupChannelInfoDoMap.get(user.getUnionId()).getSubChannel() : "");
        user.setConfigId(StringUtils.isNotBlank(groupChannelInfoDoMap.get(user.getUnionId()).getConfigId()) ? groupChannelInfoDoMap.get(user.getUnionId()).getConfigId() : "");
        user.setUniqueCode(StringUtils.isNotBlank(groupChannelInfoDoMap.get(user.getUnionId()).getUniqueCode()) ? groupChannelInfoDoMap.get(user.getUnionId()).getUniqueCode() : "");
        return user;
    }

    private void sendGroupChangeUnionIdMsg(String chatId, String unionId, String corpId, String appSecret, int valid) {
        GroupChangeInfo groupChangeInfo = new GroupChangeInfo();
        groupChangeInfo.setChatId(chatId);
        groupChangeInfo.setUnionId(unionId);
        groupChangeInfo.setCorpId(corpId);
        groupChangeInfo.setValid(valid);
        groupChangeInfo.setAppSecret(appSecret);
        mqProducerService.sendGroupChangeUnionIdMsg(groupChangeInfo);
    }


    private Map<String, GroupChannelInfoDo> selectGroupChannelInfo(List<CfWorkImGroupMemberModel> memberBoList, CfWorkImGroupDetailModel cfWorkImGroupDetailModel) {
        log.info("selectGroupChannelInfo_param1:{},param2:{}", JSON.toJSONString(memberBoList), JSON.toJSONString(cfWorkImGroupDetailModel));
        Map<String, GroupChannelInfoDo> result = new HashMap<>();
        if (CollectionUtils.isEmpty(memberBoList) || Objects.isNull(cfWorkImGroupDetailModel)) {
            return result;
        }
        GroupChannelParam groupChannelParam = new GroupChannelParam();
        groupChannelParam.setChatId(cfWorkImGroupDetailModel.getChat_id());
        groupChannelParam.setCreateTime(cfWorkImGroupDetailModel.getCreate_time());
        groupChannelParam.setName(cfWorkImGroupDetailModel.getName());
        List<GroupMemberModel> groupMemberModelList = new ArrayList<>();
        for (CfWorkImGroupMemberModel model : memberBoList) {
            GroupMemberModel groupMemberModel = new GroupMemberModel();
            groupMemberModel.setJoin_time(model.getJoin_time());
            groupMemberModel.setUnionId(model.getUnionid());
            groupMemberModelList.add(groupMemberModel);
        }
        groupChannelParam.setMemberModelList(groupMemberModelList);
        //unionId ->userId -> 关联用户入群意向表，匹配该用户距离入群时间最近1h的入群意向信息
        com.shuidihuzhu.common.web.model.Response<List<GroupChannelInfoDo>> groupChannelResponse = patientCrmApiFeignClient.selectGroupChannelInfo(groupChannelParam);
        log.info("selectGroupChannelInfo_groupChannelResponse:{}", JSON.toJSONString(groupChannelResponse));
        if (groupChannelResponse != null && groupChannelResponse.ok()) {
            List<GroupChannelInfoDo> groupChannelInfoDoList = groupChannelResponse.getData().stream().filter(Objects::nonNull).collect(Collectors.toList());
            result = groupChannelInfoDoList.stream().collect(Collectors.toMap(GroupChannelInfoDo::getUnionId, Function.identity(), (key1, key2) -> key2));
        }
        return result;
    }



    public Boolean checkKratosChatByChatId(String chatId) {
        log.info("checkKratosChatByChatId chatId:{}", chatId);
        List<String> kratosTagRelChatIdList = new ArrayList<>();
        try {
            kratosTagRelChatIdList = weChatFriendServiceImpl.listYiLiaoChatFromCache();
        } catch (Exception e) {
            log.warn("checkKratosChatByChatId listYiLiaoChatFromCache e:", e);
        }
        if (CollectionUtils.isNotEmpty(kratosTagRelChatIdList) && kratosTagRelChatIdList.contains(chatId)) {
            return true;
        }
        return false;
    }
}
