package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerRecruitInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ApproveStatusEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfStatusEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerBaseInfoVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.RecruitPartnerInfoVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.LovePartnerInfoParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerRecruitInfoService;
import com.shuidihuzhu.cf.dao.lovepartner.CfPartnerRecruitInfoDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackBdCrmFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCrmClueParam;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.msg.vo.MessageFeedBack;
import com.shuidihuzhu.msg.vo.MsgResponse;
import com.shuidihuzhu.msg.vo.rpc.MsgRecord;
import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-09-09
 */

@Service
@Slf4j
public class CfPartnerRecruitInfoServiceImpl implements CfPartnerRecruitInfoService {

    @Autowired
    private CfPartnerRecruitInfoDao partnerRecruitInfoDao;

    @Autowired
    private CfPartnerInfoService partnerInfoService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CfClewtrackBdCrmFeignClient cfClewtrackBdCrmFeignClient;

    @Autowired
    private MsgClientV2 msgClientV2;

    @Autowired
    private ApolloService apolloService;

    @Override
    public OpResult<Void> collectInfo(CfPartnerRecruitInfoDo cfPartnerRecruitInfoDo) {
        if (!apolloService.isCreateLovePartner()){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
        }
        //根据userId查询收集信息记录
        CfPartnerRecruitInfoDo recruitInfoByUserId = partnerRecruitInfoDao.getInfoByUserId(cfPartnerRecruitInfoDo.getUserId());
        if (Objects.nonNull(recruitInfoByUserId)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.LOVE_PARTNER_EXIST);
        }
        //根据身份证号查询收集信息记录
        CfPartnerRecruitInfoDo recruitInfoByIdCard = partnerRecruitInfoDao.getInfoByIdCard(cfPartnerRecruitInfoDo.getEncryptIdCard());
        if (Objects.nonNull(recruitInfoByIdCard)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.LOVE_PARTNER_EXIST);
        }
        //根据身份证号查询兼职信息
        CfPartnerInfoDo cfPartnerInfoDo = partnerInfoService.getPartnerInfoByIdCard(cfPartnerRecruitInfoDo.getEncryptIdCard());
        //存在,更新approveStatus,uniqueCode
        if (Objects.nonNull(cfPartnerInfoDo)){
            cfPartnerRecruitInfoDo.setUniqueCode(cfPartnerInfoDo.getUniqueCode());
            cfPartnerRecruitInfoDo.setApproveStatus(CfStatusEnums.PartnerRecruitApproveStatusEnum.SYS_HANDLED.getStatus());
            cfPartnerRecruitInfoDo.setLeaderUniqueCode(cfPartnerInfoDo.getLeaderUniqueCode());
            cfPartnerRecruitInfoDo.setLeaderName(cfPartnerInfoDo.getLeaderName());
            cfPartnerRecruitInfoDo.setQrCode(cfPartnerInfoDo.getQrCode());
            CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(cfPartnerInfoDo.getLeaderUniqueCode());
            cfPartnerRecruitInfoDo.setLeaderEncryptPhone(Optional.ofNullable(volunteer).map(CrowdfundingVolunteer::getMobile).orElse(""));
        }
        //已审核通过,发送消息
        if (Objects.nonNull(cfPartnerInfoDo) && ApproveStatusEnum.parsePartnerApproveStatus(cfPartnerInfoDo.getApproveStatus()).getIsApprovePass()){
            //兼职审批通过后发送公众号消息
            sendPartnerApproveMsg(cfPartnerRecruitInfoDo.getUserId(),cfPartnerInfoDo);
        }
        //存储数据
        int res = partnerRecruitInfoDao.insert(cfPartnerRecruitInfoDo);
        if (res > 0){
            return OpResult.createSucResult(null);
        }else{
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
        }
    }

    @Override
    public OpResult<PartnerBaseInfoVo> getBaseInfo(long userId) {
        OpResult<ImmutablePair<CfPartnerRecruitInfoDo, CfPartnerInfoDo>> checkOpResult = checkIllegalUser(userId);
        if (checkIllegalUser(userId).isFail()) {
            return OpResult.createFailResult(checkOpResult.getErrorCode());
        }
        ImmutablePair<CfPartnerRecruitInfoDo, CfPartnerInfoDo> pair = checkOpResult.getData();
        CfPartnerRecruitInfoDo recruitInfoDo = pair.getLeft();
        CfPartnerInfoDo cfPartnerInfoDo = pair.getRight();
        PartnerBaseInfoVo baseInfoVo = new PartnerBaseInfoVo();
        baseInfoVo.setUniqueCode(cfPartnerInfoDo.getUniqueCode());
        baseInfoVo.setPartnerName(cfPartnerInfoDo.getName());
        baseInfoVo.setCityName(recruitInfoDo.getCityName());
        baseInfoVo.setProvinceName(recruitInfoDo.getProvinceName());
        baseInfoVo.setQrCode(cfPartnerInfoDo.getQrCode());
        baseInfoVo.setLeaderName(cfPartnerInfoDo.getLeaderName());
        baseInfoVo.setAccountType(cfPartnerInfoDo.getAccountType());
        baseInfoVo.setLeaderPhone(shuidiCipher.decrypt(recruitInfoDo.getLeaderEncryptPhone()));
        return OpResult.createSucResult(baseInfoVo);
    }

    private OpResult<ImmutablePair<CfPartnerRecruitInfoDo,CfPartnerInfoDo>> checkIllegalUser(long userId){
        //根据userId查询招募表
        CfPartnerRecruitInfoDo partnerRecruitInfoDo = partnerRecruitInfoDao.getInfoByUserId(userId);
        ////不存在记录，报人员未申请过
        if (Objects.isNull(partnerRecruitInfoDo)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.LOVE_PARTNER_NOT_EXIST);
        }
        //存在记录,查询兼职表,判断审核状态,报审核未通过
        if (StringUtils.isBlank(partnerRecruitInfoDo.getUniqueCode())){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.LOVE_PARTNER_NOT_PASS);
        }
        //未审核通过
        if (!CfStatusEnums.parsePartnerRecruitApproveStatus(partnerRecruitInfoDo.getApproveStatus()).getIsApprovePass()){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.LOVE_PARTNER_NOT_PASS);
        }
        CfPartnerInfoDo cfPartnerInfoDo = partnerInfoService.getPartnerInfoByUniqueCode(partnerRecruitInfoDo.getUniqueCode());
        if (Objects.isNull(cfPartnerInfoDo)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.LOVE_PARTNER_NOT_EXIST);
        }
        //未审核通过
        if (!ApproveStatusEnum.parsePartnerApproveStatus(cfPartnerInfoDo.getApproveStatus()).getIsApprovePass()){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.LOVE_PARTNER_NOT_PASS);
        }
        return OpResult.createSucResult(ImmutablePair.of(partnerRecruitInfoDo,cfPartnerInfoDo));
    }

    @Override
    public OpResult<Void> recommendClue(long userId, String phone, String patientName) {
        //校验合法性
        OpResult<ImmutablePair<CfPartnerRecruitInfoDo,CfPartnerInfoDo>> checkOpResult =checkIllegalUser(userId);
        if (checkIllegalUser(userId).isFail()){
            return OpResult.createFailResult(checkOpResult.getErrorCode());
        }
        ImmutablePair<CfPartnerRecruitInfoDo,CfPartnerInfoDo> pair = checkOpResult.getData();
        CfPartnerInfoDo cfPartnerInfoDo = pair.getRight();
        CrowdfundingVolunteer cfVolunteer = cfVolunteerService.getByUniqueCode(cfPartnerInfoDo.getLeaderUniqueCode());
        if (Objects.isNull(cfVolunteer)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL,"未找到对应的顾问");
        }
        //调取插入线索feign接口，入参 phone,patientName,partnerUniqueCode,顾问uniqueCode
        CfBdCrmClueParam cfBdCrmClueParam = CfBdCrmClueParam.builder()
                .fundraisingObject(patientName)
                .phone(phone)
                .channel("BD_sale_bd_partner_insert")
                .cfVolunteer(cfVolunteer)
                .address("")
                .addSickbed("")
                .content("")
                .diseaseName("")
                .hosptialName("")
                .partnerUniqueCode(cfPartnerInfoDo.getUniqueCode())
                .remark("")
                .revisitTaskId(0L)
                .sickbed("")
                .sickroom("")
                .build();
        Response<Void> response = cfClewtrackBdCrmFeignClient.createClew(cfBdCrmClueParam);
        if (response.notOk()){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL,response.getMsg());
        }
        return OpResult.createSucResult();
    }

    @Override
    public int getRecruitPartnerCount(String leaderUniqueCode, CfStatusEnums.PartnerRecruitApproveStatusEnum recruitApproveStatusEnum) {
        return partnerRecruitInfoDao.getRecruitPartnerCountWithLeadUniqueCode(leaderUniqueCode,recruitApproveStatusEnum.getStatus(),0, Lists.newArrayList());
    }

    @Override
    public CommonResultModel<RecruitPartnerInfoVo> listRecruitPartner(CrowdfundingVolunteer volunteer, int approveStatus, int pageNo, int pageSize) {
        String leaderUniqueCode = volunteer.getUniqueCode();
        int count = partnerRecruitInfoDao.getRecruitPartnerCountWithLeadUniqueCode(leaderUniqueCode,approveStatus,null,CfStatusEnums.listStatus4Whale());
        CommonResultModel<RecruitPartnerInfoVo> commonResultModel = new CommonResultModel<RecruitPartnerInfoVo>();
        if (count <= 0){
            commonResultModel.setTotal(count);
            return commonResultModel;
        }
        int offset = (pageNo - 1) * pageSize;
        List<RecruitPartnerInfoVo> modelList = partnerRecruitInfoDao.pageRecruitPartnerByLeaderUniqueCode(leaderUniqueCode,approveStatus,offset,pageSize,null,CfStatusEnums.listStatus4Whale())
                .stream()
                .map(item -> {
                    RecruitPartnerInfoVo recruitPartnerInfoVo = new RecruitPartnerInfoVo();
                    BeanUtils.copyProperties(item,recruitPartnerInfoVo);
                    recruitPartnerInfoVo.setPhone(shuidiCipher.decrypt(item.getEncryptPhone()));
                    recruitPartnerInfoVo.setIdCard(shuidiCipher.decrypt(item.getEncryptIdCard()));
                    return recruitPartnerInfoVo;
                })
                .collect(Collectors.toList());
        commonResultModel.setModelList(modelList);
        return commonResultModel;
    }

    @Override
    public RecruitPartnerInfoVo recruitPartnerDetail(CrowdfundingVolunteer volunteer, long id) {
        String leaderUniqueCode = volunteer.getUniqueCode();
        CfPartnerRecruitInfoDo cfPartnerRecruitInfoDo = partnerRecruitInfoDao.recruitPartnerDetail(leaderUniqueCode,id);
        if (Objects.isNull(cfPartnerRecruitInfoDo)){
            return null;
        }
        RecruitPartnerInfoVo recruitPartnerInfoVo = new RecruitPartnerInfoVo();
        BeanUtils.copyProperties(cfPartnerRecruitInfoDo,recruitPartnerInfoVo);
        recruitPartnerInfoVo.setPhone(shuidiCipher.decrypt(cfPartnerRecruitInfoDo.getEncryptPhone()));
        recruitPartnerInfoVo.setIdCard(shuidiCipher.decrypt(cfPartnerRecruitInfoDo.getEncryptIdCard()));
        return recruitPartnerInfoVo;
    }

    @Override
    public OpResult<Void> recruitCancel(CrowdfundingVolunteer volunteer, long id) {
        String leaderUniqueCode = volunteer.getUniqueCode();
        int res = partnerRecruitInfoDao.recruitCancel(CfStatusEnums.PartnerRecruitApproveStatusEnum.CANCEL.getStatus(),leaderUniqueCode,CfStatusEnums.PartnerRecruitApproveStatusEnum.NEED_HANDLE.getStatus(),id);
        if (res > 0){
            return OpResult.createSucResult();
        }else{
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
        }
    }

    @Override
    public int updateFromSubmit(LovePartnerInfoParam lovePartnerInfoParam, CrowdfundingVolunteer cfVolunteer) {
        CfPartnerRecruitInfoDo cfPartnerRecruitInfoDo = partnerRecruitInfoDao.getInfoByIdCard(lovePartnerInfoParam.getIdCard());
        if (Objects.isNull(cfPartnerRecruitInfoDo)){
            return 0;
        }
        cfPartnerRecruitInfoDo.setUniqueCode(lovePartnerInfoParam.getUniqueCode());
        cfPartnerRecruitInfoDo.setApproveStatus(CfStatusEnums.PartnerRecruitApproveStatusEnum.HANDLED.getStatus());
        cfPartnerRecruitInfoDo.setLeaderUniqueCode(lovePartnerInfoParam.getLeaderUniqueCode());
        cfPartnerRecruitInfoDo.setLeaderName(lovePartnerInfoParam.getLeaderName());
        cfPartnerRecruitInfoDo.setLeaderEncryptPhone(cfVolunteer.getMobile());
        return partnerRecruitInfoDao.updateRecruitInfo(cfPartnerRecruitInfoDo);
    }

    @Override
    public int updateQrCodeByUniqueCode(String qrCode, String uniqueCode) {
        if (StringUtils.isBlank(qrCode) || StringUtils.isBlank(uniqueCode)){
            return 0;
        }
        return partnerRecruitInfoDao.updateQrCodeByUniqueCode(qrCode,uniqueCode);
    }

    @Override
    public void sendPartnerApproveMsg(CfPartnerInfoDo cfPartnerInfoDo) {
        CfPartnerRecruitInfoDo cfPartnerRecruitInfoDo = partnerRecruitInfoDao.getInfoByIdCard(cfPartnerInfoDo.getEncryptIdCard());
        if (Objects.nonNull(cfPartnerRecruitInfoDo)){
            sendPartnerApproveMsg(cfPartnerRecruitInfoDo.getUserId(),cfPartnerInfoDo);
        }
    }

    @Override
    public List<CfPartnerRecruitInfoDo> listPartnerByUniqueCodes(List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)){
            return Lists.newArrayList();
        }
        List<List<String>> listList = Lists.partition(uniqueCodes, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> partnerRecruitInfoDao.listPartnerByUniqueCodes(list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
    }

    @Override
    public int adjustLovePartnerByPartnerUniqueCode(String partnerUniqueCode, String leaderUniqueCode, String leaderName, String leaderMobile) {
        return partnerRecruitInfoDao.adjustLovePartnerByUniqueCode(partnerUniqueCode,leaderUniqueCode,leaderName,leaderMobile);
    }

    @Override
    public void cancelCooperation(String leaderUniqueCode, String uniqueCode) {
        partnerRecruitInfoDao.cancelCooperation(leaderUniqueCode,uniqueCode,CfStatusEnums.PartnerRecruitApproveStatusEnum.CANCEL.getStatus());
    }

    private void sendPartnerApproveMsg(long userId,CfPartnerInfoDo cfPartnerInfoDo) {
        HashMap<Integer, String> params = Maps.newHashMap();
        params.put(1,cfPartnerInfoDo.getName());
        params.put(2,Optional.ofNullable(shuidiCipher.decrypt(cfPartnerInfoDo.getEncryptPhone())).orElse(""));
        params.put(3, DateUtil.getYearMonthDayStr(cfPartnerInfoDo.getUpdateTime()));
        params.put(4,"已通过");

        List<MsgRecord> msgRecords = Lists.newArrayList();
        MsgRecord msgRecord = new MsgRecord(params, userId, AccountThirdTypeEnum.SD_CHOU_JIAN_KUANG.getCode());
        msgRecords.add(msgRecord);
        MsgRecordBatch msgRecordBatch = MsgRecordBatch.build("DVA7540", "", msgRecords);
        try{
            MsgResponse<List<MessageFeedBack>> response = msgClientV2.saveBatchV2(msgRecordBatch);
            log.info("DVA7540 msg response:{}", JSON.toJSONString(response));
        }catch (Exception e){
            log.error("DVA7540 msg error",e);
        }
    }
}
