package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdGpsErrorReportDO;

/**
 * bd gps error report(BdGpsErrorReportDO)表服务接口
 *
 * <AUTHOR>
 * @since 2021-07-07 16:51:36
 */
public interface BdGpsErrorReportService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    BdGpsErrorReportDO queryById(long id);


    /**
     * 新增数据
     *
     * @param BdGpsErrorReportDO 实例对象
     * @return 实例对象
     */
    int insert(BdGpsErrorReportDO BdGpsErrorReportDO);

    /**
     * 修改数据
     *
     * @param BdGpsErrorReportDO 实例对象
     * @return 实例对象
     */
    int update(BdGpsErrorReportDO BdGpsErrorReportDO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);

}