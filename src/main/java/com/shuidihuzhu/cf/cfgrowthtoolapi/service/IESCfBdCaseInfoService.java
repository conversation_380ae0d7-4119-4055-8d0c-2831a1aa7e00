package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.BdServiceInfoVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.excel.DepartmentExcelModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.SimpleHospitalCodeModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDepartmentDataParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DepartmentCmpParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DepartmentHospitalParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/12/16 3:54 PM
 */
public interface IESCfBdCaseInfoService {

    OpResult<List<CfCrmVolunteerCaseCountModel>> getCfCrmVolunteerCaseCountModelGroupByFromEs(List<String> dateTimes, List<Integer> orgIdList, BdCrmSearchParam bdCrmSearchParam);

    OpResult<CfCrmVolunteerCaseCountModel> getOrgCrmCrowdfundingModelFromEs(List<String> dateTimes, List<Integer> orgIdList, BdCrmSearchParam bdCrmSearchParam);

    OpResult<List<CfCrmVolunteerCaseCountSimpleModel>> getCfCrmVolunteerCaseCountSimpleModelFromEs(List<String> dateTimes, long amount, String uniqueCode, Integer firstApprovePass);

    List<CfCrmVolunteerCaseCountSimpleModel> aggregateByUniqueCodeAndDateTimeFromEs(List<String> dateTimes, List<String> uniqueCodes);

    OpResult<List<CfCrmDateTimeCaseCountModel>> getCfCrmDateTimeCaseCountModelFromEs(List<String> dateTimes,
                                                                                     long amount,
                                                                                     List<Integer> orgIdList, int notFirstApprovePass);

    OpResult<Long> getCountByUniqueCodeListFromEs(List<String> uniqueCodeList, String startTime, String endTime);

    List<CfBdCaseInfoDo> listCaseIdByOrgIdListFromEs(List<Integer> orgIdList, String startTime, String endTime);

    List<CfBdCaseInfoDo> listCaseIdByUniqueCodesFromEs(List<String> uniqueCodeList, String startTime, String endTime);

    OpResult<Long> getCountByUniqueCodeAndTimeAndCfAmountFromEs(List<String> uniqueCodeList, Integer orgId, Date startTime, Date endTime, BdCrmSearchParam bdCrmSearchParam);

    OpResult<BdServiceInfoVo> getBdServiceInfoVoByUniqueCodeFromES(String uniqueCode);

    OpResult<BdServiceInfoVo> getBdServiceInfoVoByUniqueCodeFromESOneYearAgo(String uniqueCode, String createTime);

    List<DepartmentExcelModel> groupByHospitalNameDepartment(BdCrmDepartmentDataParam departmentDataParam);

    List<CfCrmVolunteerCaseCountSimpleModel> getBdCaseStatisticsFromEs(String startTime, String endTime, String uniqueCode);

    List<DepartmentDayCaseModel> departmentGbCaseCreateTime(DepartmentCmpParam departmentCmpParam);

    List<DepartmentDayCaseModel> departmentGbFirstApproveTime(DepartmentCmpParam departmentCmpParam);

    List<DepartmentDayCaseModel> departmentGbHot(DepartmentCmpParam departmentCmpParam);

    /**
     * 查找科室上的医院
     */
    List<SimpleHospitalCodeModel> listHospitalForDepartment(DepartmentHospitalParam departmentHospitalParam);

    /**
     * 根据科室聚合数据
     */
    List<Integer> findImportFlagDepartment(int id);

    CrmCrowdfundingModel statByOrgUseEs(long orgId);


}
