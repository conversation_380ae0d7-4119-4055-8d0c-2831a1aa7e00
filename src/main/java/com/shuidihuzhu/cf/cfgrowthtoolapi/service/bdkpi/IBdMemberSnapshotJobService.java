package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-08-03 4:35 下午
 *
 * job调用生成人员快照的信息
 **/
public interface IBdMemberSnapshotJobService {

    /**
     * 初始化数据
     */
    void initData();


    List<CfCrmMemberSnapshotDO> getAllMemberSnapshotInfo();


    /**
     * 构建snapshot
     * @param volunteer : 是否新员工
     * @return 返回结果可能为空
     */
    @Nullable
    CfCrmMemberSnapshotDO createMemberSnapshot(CrowdfundingVolunteer volunteer);



    CfCrmMemberSnapshotDO createMemberSnapshotV2(CrowdfundingVolunteer volunteer);




    /**
     * 构建只展示，不进行计算snapshot
     * @param relationDO
     * @param volunteer
     * @return
     */
    CfCrmMemberSnapshotDO buildOnlyShowMemberSnapshot(BdCrmOrgUserRelationDO relationDO, CrowdfundingVolunteer volunteer);

}
