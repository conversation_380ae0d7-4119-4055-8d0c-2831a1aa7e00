package com.shuidihuzhu.cf.cfgrowthtoolapi.service.kuaishou.Impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfKuaiShouApplyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.CfKuaiShouApplySearchParams;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.kuaishou.IBdKuaiShouApplyRecordService;
import com.shuidihuzhu.cf.dao.CfKuaiShouApplyRecordDao;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.xml.crypto.Data;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/25 7:48 PM
 */
@Service("bdKuaiShouApplyRecordService")
public class IBdKuaiShouApplyRecordServiceImpl implements IBdKuaiShouApplyRecordService {

    @Resource
    private CfKuaiShouApplyRecordDao cfKuaiShouApplyRecordDao;


    @Override
    public int insert(CfKuaiShouApplyDO cfKuaiShouApplyDO) {
        return cfKuaiShouApplyRecordDao.insert(cfKuaiShouApplyDO);
    }

    @Override
    public CfKuaiShouApplyDO getKuaiShouApplyByInfoId(String infoId) {
        if (StringUtils.isEmpty(infoId)) {
            return new CfKuaiShouApplyDO();
        }
        return cfKuaiShouApplyRecordDao.getKuaiShouApplyDetail(infoId);
    }

    @Override
    public List<CfKuaiShouApplyDO> getCfKuaiShouApplyResultByParams(CfKuaiShouApplySearchParams params) {
        return cfKuaiShouApplyRecordDao.getCfKuaiShouApplyResultByParams(params);
    }

    @Override
    public Long getCfKuaiShouApplyCountByParams(CfKuaiShouApplySearchParams params) {
        return cfKuaiShouApplyRecordDao.getCfKuaiShouApplyCountByParams(params);
    }

    @Override
    public void modifyStatus(Long id, Integer dealResult, String dealReason, Date dealTime, String name, String otherReason) {
        cfKuaiShouApplyRecordDao.modifyStatus(id, dealResult, dealReason, dealTime, name, otherReason);
    }

    @Override
    public CfKuaiShouApplyDO getKuaiShouApplyById(Long id) {
        return cfKuaiShouApplyRecordDao.getKuaiShouApplyById(id);
    }

    private Date controlTime() {
        Date startTime = new Date();
        return DateUtil.addDays(startTime, -90);
    }

}
