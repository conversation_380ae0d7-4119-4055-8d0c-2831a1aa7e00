package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientrecruit;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.patientrecruit.CfPatientRecruitClueInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitClueOrgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.ValidClewStatus;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.recruit.RecruitClewSearchParam;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-12
 */
public interface ICfPatientRecruitClueService {

    /**
     * 新增招募线索
     * @return
     */
    CfPatientRecruitClueInfoDo addClew(CfPatientRecruitClueInfoDo cfPatientRecruitClueInfoDo);


    CfPatientRecruitClueInfoDo addClewByMaterialVo(PreposeMaterialModel.MaterialInfoVo materialInfoVo, CrowdfundingVolunteer crowdfundingVolunteer, ClewCrowdfundingReportRelation relation);

    /**
     * 批量更新uploadRecruitStatus
     *
     * @param clueIdList
     * @param uploadRecruitStatus
     * @return
     */
    int updateUploadRecruitStatusByClueIds(List<Long> clueIdList, Integer uploadRecruitStatus);

    /**
     * 根据手机号查询线索信息
     *
     * @param phone
     * @return
     */
    CfPatientRecruitClueInfoDo getByPhone(String phone);

    /**
     * 根据手机号+uniqueCode查询线索信息
     *
     * @param uniqueCode
     * @param phone
     * @return
     */
    @Deprecated
    CfPatientRecruitClueInfoDo getClewInfoByUniqueCodeAndPhone(String uniqueCode, String phone);

    /**
     * 根据uniqueCode+type查询线索总数
     *
     * @param uniqueCode
     * @param type
     * @param keyWord
     * @return
     */
    int countClewInfoByType(String uniqueCode, int type, String keyWord);

    /**
     * 根据uniqueCode+type查询线索信息
     *
     * @param uniqueCode
     * @param type
     * @param keyWord
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<CfPatientRecruitClueInfoDo> listClewInfoByType(String uniqueCode, int type, String keyWord, int pageNo, int pageSize);

    int countPageRecruitClew(RecruitClewSearchParam recruitClewSearchParam);

    List<CfPatientRecruitClueInfoDo> pageRecruitClew(RecruitClewSearchParam recruitClewSearchParam);

    ValidClewStatus queryValidCount(RecruitClewSearchParam recruitClewSearchParam);

    /**
     * 获取所有待重传线索
     *
     * @param queryStartTime
     * @param queryEndTime
     * @param uploadRecruitStatus
     * @return
     */
    List<CfPatientRecruitClueInfoDo> listNeedUploadPatientRecruitClue(Date queryStartTime, Date queryEndTime, Integer uploadRecruitStatus);


    /**
     * 更新招募信息
     *
     * @param clueInfoDo
     * @param id
     * @return
     */
    int updateRecruitWorkInfoById(CfPatientRecruitClueInfoDo clueInfoDo, Long id);

    CfPatientRecruitClueInfoDo getByPatientId(long patientId);

    RecruitClueOrgModel countByOrgIds(RecruitClewSearchParam recruitClewSearchParam, List<Long> orgIds);

    List<RecruitClueOrgModel> groupByUniqueCode(RecruitClewSearchParam recruitClewSearchParam, List<Long> orgIds);

    int updateOrgId(String uniqueCode, long orgId);

    CfPatientRecruitClueInfoDo getById(long clueId);

    int updateClewStatusById(long id, int isDelete, int uploadRecruitStatus);

    int updateValidStatus(long id, int clewValid, String invalidReason);

    List<CfPatientRecruitClueInfoDo> getPatientRecruitClueInfoList(String startTime, String endTime, Integer pageSize, Long fetchId);

    String leaderInfo(CfPatientRecruitClueInfoDo cfPatientRecruitClueInfoDo, Integer recruitWorkStatus, Integer recruitBizStatus);

    void updateLeaderInfo(long id, String leaderInfo);
}
