package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.patientrecruit.CfPatientRecruitClueInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.SimpleMaterialInfoVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.CfPatientRecruitClewVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDataStatPageParam;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.model.dedicated.CfVolunteerCheckModel;
import com.shuidihuzhu.cf.model.dedicated.CfVolunteerMaterialModel;
import com.shuidihuzhu.cf.risk.model.risk.diease.InfoReasonableAmountResultVo;
import com.shuidihuzhu.cf.risk.model.risk.diease.SpecialDiseaseChoiceInfoVo;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerCheckModelDto;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialModelDto;
import com.shuidihuzhu.client.cf.growthtool.model.vo.InfoReasonableAmountResultVO;
import com.shuidihuzhu.client.cf.growthtool.model.vo.SpecialDiseaseChoiceInfoVO;
import org.checkerframework.checker.units.qual.A;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR> Yuhang
 * @Date 2023/3/2 15:30
 */
@Mapper(componentModel = "spring")
public interface CopyMapper {
    InfoReasonableAmountResultVO toInfoReasonableAmountResultVO(InfoReasonableAmountResultVo infoReasonableAmountResultVo);

    SpecialDiseaseChoiceInfoVO toSpecialDiseaseChoiceInfoVO(SpecialDiseaseChoiceInfoVo specialDiseaseChoiceInfoVo);

    CfVolunteerCheckModelDto toCfVolunteerCheckModelDto(CfVolunteerCheckModel cfVolunteerCheckModel);

    CfVolunteerMaterialModelDto toCfVolunteerMaterialModelDto(CfVolunteerMaterialModel cfVolunteerMaterialModelDto);

    CfPatientRecruitClewVo toCfPatientRecruitClewVo(CfPatientRecruitClueInfoDo cfPatientRecruitClueInfoDo);

    BdCrmDataStatPageParam toBdCrmDataStatPageParam(BdCrmDataStatPageParam bdCrmDataStatPageParam);

    @Mapping(source = "materialInfoVo.id", target = "preposeMaterialId")
    @Mapping(source = "materialInfoVo.diseaseName", target = "patientDisease")
    @Mapping(source = "materialInfoVo.raiseCityName", target = "raiseCity")
    @Mapping(source = "materialInfoVo.vhospitalCode", target = "vHospitalCode")
    @Mapping(source = "materialInfoVo.hospital", target = "raiseHospital")
    @Mapping(source = "materialInfoVo.department", target = "raiseDepartment")

    SimpleMaterialInfoVo toSimpleMaterialInfoVo(PreposeMaterialModel.MaterialInfoVo materialInfoVo);


}
