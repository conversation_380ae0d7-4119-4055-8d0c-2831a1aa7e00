package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmDiagnoseCronTaskDO;

import java.util.List;
import java.util.stream.Stream;

/**
 * @author: wanghui
 * @create: 2021/6/10 下午2:56
 */
public interface CfBdCrmDiagnoseCronTaskService{


    int saveCronTask(CfBdCrmDiagnoseCronTaskDO record);

    CfBdCrmDiagnoseCronTaskDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CfBdCrmDiagnoseCronTaskDO record);

    List<CfBdCrmDiagnoseCronTaskDO> getHistoryForDiagnose(Long orgId, String uniqueCode);

    List<CfBdCrmDiagnoseCronTaskDO> listCronTask(Long orgId, String curDateTime, Integer curDayRange, String preDateTime, Integer preDayRange);

    List<CfBdCrmDiagnoseCronTaskDO> listWaitDiagnoseTask(String curDateTime, Integer curDayRange, String preDateTime, Integer preDayRange);

    void batchUpdateStatus(List<Long> idList, int status);

    CfBdCrmDiagnoseCronTaskDO getCronTaskForDiagnose(Long id, String uniqueCode);
}
