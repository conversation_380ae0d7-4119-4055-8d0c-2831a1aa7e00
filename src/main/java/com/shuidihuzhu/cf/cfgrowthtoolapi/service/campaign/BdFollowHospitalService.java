package com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.campaignv2.BdFollowHospitalDO;

import java.util.List;

/**
 * 个人关注医院表(BdFollowHospital)表服务接口
 *
 * <AUTHOR>
 * @since 2021-08-26 23:54:18
 */
public interface BdFollowHospitalService {

    List<BdFollowHospitalDO> listByUniqueCode(String uniqueCode);

    BdFollowHospitalDO queryByUniqueAndHosp(String uniqueCode, String hospitalCode);

    int insert(BdFollowHospitalDO bdFollowHospital);

    int update(BdFollowHospitalDO bdFollowHospital);

    int updateDeleteStatus(long id, int deleteCode);

}