package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdQyWxBindMappingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.qywx.QywxBindingQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.BdQyWxBindMappingService;
import com.shuidihuzhu.cf.dao.qywx.BdQyWxBindMappingDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 顾问与企业微信绑定信息(BdQyWxAdvisorMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-11 11:33:18
 */
@Service
public class BdQyWxBindMappingServiceImpl implements BdQyWxBindMappingService {
   
    @Resource
    private BdQyWxBindMappingDao bdQyWxBindMappingDao;

    @Override
    public BdQyWxBindMappingDO queryById(long id) {
        return bdQyWxBindMappingDao.queryById(id);
    }
    
    @Override
    public int insert(BdQyWxBindMappingDO bdQyWxAdvisorMapping) {
        return bdQyWxBindMappingDao.insert(bdQyWxAdvisorMapping);
    }

    @Override
    public int updateNickName(long id, String nickName, String avatarUrl) {
        return bdQyWxBindMappingDao.updateNickName(id, nickName, avatarUrl);
    }

    @Override
    public int update(BdQyWxBindMappingDO bdQyWxAdvisorMapping) {
        return bdQyWxBindMappingDao.update(bdQyWxAdvisorMapping);
    }
    

    @Override
    public List<BdQyWxBindMappingDO> listByUniqueCode(String uniqueCode) {
        return bdQyWxBindMappingDao.listByUniqueCode(uniqueCode);
    }

    @Override
    public List<BdQyWxBindMappingDO> queryByRobotUserId(String robotUserId) {
        return bdQyWxBindMappingDao.queryByRobotUserId(robotUserId);
    }

    @Override
    public BdQyWxBindMappingDO getByRobotUserIdAndUniqueCode(String robotUserId, String uniqueCode) {
        return bdQyWxBindMappingDao.getByRobotUserIdAndUniqueCode(robotUserId, uniqueCode);
    }

    @Override
    public BdQyWxBindMappingDO queryByExternalUserId(String externalUserId) {
        return bdQyWxBindMappingDao.queryByExternalUserId(externalUserId);
    }

    @Override
    public List<BdQyWxBindMappingDO> queryByName(String name) {
        return bdQyWxBindMappingDao.queryByName(name);
    }

    @Override
    public List<BdQyWxBindMappingDO> listBindingByPage(QywxBindingQueryParam queryParam) {
        return bdQyWxBindMappingDao.listBindingByPage(queryParam);
    }

    @Override
    public int countBinding(QywxBindingQueryParam queryParam) {
        return bdQyWxBindMappingDao.countBinding(queryParam);
    }

    @Override
    public int deleteById(long id) {
        return bdQyWxBindMappingDao.deleteById(id);
    }

    @Override
    public int deleteByRobotUserId(String robotUserId) {
        if (StringUtils.isBlank(robotUserId)) {
            return 0;
        }
        return bdQyWxBindMappingDao.deleteByRobotUserId(robotUserId);
    }

    @Override
    public int countByRobotUserId(String robotUserId) {
        if (StringUtils.isBlank(robotUserId)) {
            return 0;
        }
        Integer count = bdQyWxBindMappingDao.countByRobotUserId(robotUserId);
        return count != null ? count : 0;
    }
} 