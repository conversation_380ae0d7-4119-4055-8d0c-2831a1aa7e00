package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfBdcrmVisitConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfBdcrmVisitTemplateDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfBdcrmVisitConfigModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfBdcrmVisitModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfBdcrmVisitConfigService;
import com.shuidihuzhu.cf.dao.CfBdcrmVisitConfigDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 陪访模版信息
 */
@Slf4j
@Service
public class CfBdcrmVisitConfigServiceImpl implements CfBdcrmVisitConfigService {


    @Autowired
    private CfBdcrmVisitConfigDao bdcrmVisitConfigDao;

    @Override
    public List<CfBdcrmVisitConfigModel> queryAllLatestTemplateConfigList() {
        List<CfBdcrmVisitConfigDO> visitConfigDOList = bdcrmVisitConfigDao.selectLastestTempalte(null);
        List<CfBdcrmVisitTemplateDO> templateDOS = bdcrmVisitConfigDao.selectAllTemplate(null);
        Map<Long, String> templateMap = templateDOS.stream().collect(Collectors.toMap(CfBdcrmVisitTemplateDO::getId, CfBdcrmVisitTemplateDO::getTemplateName, (o, n) -> o));
        return visitConfigDOList.stream().map(s -> CfBdcrmVisitConfigModel.doCovertModel(s,templateMap)).collect(Collectors.toList());
    }


    @Override
    public List<CfBdcrmVisitConfigModel> queryVisitConfigByIdList(Set<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return Lists.newArrayList();
        }
        List<CfBdcrmVisitTemplateDO> templateDOS = bdcrmVisitConfigDao.selectAllTemplate(null);
        Map<Long, String> templateMap = templateDOS.stream().collect(Collectors.toMap(CfBdcrmVisitTemplateDO::getId, CfBdcrmVisitTemplateDO::getTemplateName, (o, n) -> o));
        List<CfBdcrmVisitConfigDO> configDOS = bdcrmVisitConfigDao.selectByPrimaryKeyList(idList);
        return configDOS.stream().map(s->CfBdcrmVisitConfigModel.doCovertModel(s,templateMap)).collect(Collectors.toList());
    }


}
