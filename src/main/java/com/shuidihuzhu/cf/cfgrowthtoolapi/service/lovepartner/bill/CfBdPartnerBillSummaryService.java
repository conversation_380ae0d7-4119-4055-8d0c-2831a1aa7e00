package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfBdPartnerBillSummaryDO;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2021-09-02 8:04 下午
 **/
public interface CfBdPartnerBillSummaryService {

    int insertOrUpdate(CfBdPartnerBillSummaryDO cfBdPartnerBillSummary);

    int updateApproveStatus(List<String> uniqueCodeList, long cycleId, int approveStatus);

    List<CfBdPartnerBillSummaryDO> listByUniqueCodes(List<String> uniqueCodes, long cycleId);

    int deleteByCycleId(long cycleId);
}
