package com.shuidihuzhu.cf.cfgrowthtoolapi.service.clew.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGwClewTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.clew.ICfGwClewTaskService;
import com.shuidihuzhu.cf.dao.clew.CfGwClewTaskDao;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-12
 */

@Slf4j
@Service
public class CfGwClewTaskServiceImpl implements ICfGwClewTaskService {

    @Autowired
    private CfGwClewTaskDao cfGwClewTaskDao;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Override
    public List<Long> listClewIdByPhoneAndUniqueCode(String uniqueCode, String phone) {
        String encryptPhone = oldShuidiCipher.aesEncrypt(phone);
        return cfGwClewTaskDao.listClewIdByPhoneAndUniqueCode(uniqueCode,encryptPhone);
    }

    @Override
    public List<CfGwClewTaskDO> getGwClewTaskByPhone(String encryptPhone, Date startDate) {
        if (StringUtils.isBlank(encryptPhone)) {
            return Lists.newArrayList();
        }
        return cfGwClewTaskDao.getGwClewTaskByPhone(encryptPhone, startDate);
    }
}
