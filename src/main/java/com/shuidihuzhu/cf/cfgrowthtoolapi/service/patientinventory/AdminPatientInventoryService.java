package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OperateLogSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OperatorLogVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.PatientInventoryDepartmentsBedParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WashBaseParam;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/6  15:22
 */
public interface AdminPatientInventoryService {

    Response<PatientInventoryHospitalDuty> getDutyUniqueInfoList(String vhospitalCode);

    Response<Void> saveOrUpdateDutyUniqueCodeList(long id, String vhospitalCode, String dutyUniqueCodes);

    int saveOrUpdatePatientInventoryDepartments(int departmentId, String bindingUniqueCode, String bindingMisName, String labels, int departmentType);

    List<PatientInventoryDepartmentsDo> listByDepartmentsId(List<Integer> departmentsIds);

    Response<List<PatientInventoryHospitalDetailModel>> getHospitalDepartmentsInfo(String province, String cityName);

    Response<Void> saveOrUpdate(PatientInventoryDepartmentsBedParam param, long authSaasUserId);

    Response<PatientInventoryDepartmentsBedVo> getBedInfo(long id);

    Response<Map<String, Object>> departmentsBedList(String province, String city, String hospitalName,
                                                     String departmentsName, Integer valid, int pageNum, int pageSize);

    Response<Map<String, Object>> departmentsBedListOrder(String hospitalName, String departmentsName, Integer valid, int pageNum, int pageSize);

    Response<List<PatientInventoryDepartmentsBedPatientVo>> departmentsBedDetail(int bedId);

    Response<PatientInventoryHospitalInfo> departmentsBedFilter(String province, String city, String hospitalName,
                                                                String departmentsName, int limit);

    Response<Void> updateValid(long id, int valid);

    Response<Integer> updateValidBatch(String ids, int valid);

    Response<CommonResultModel<OperatorLogVO>> getOperateLog(OperateLogSearchModel searchModel);

    void batchDelete(List<Integer> departmentsIds);

    Response<Void> importBedInfo(MultipartFile file, List<PatientInventoryDepartmentsBedTemplate> dataList, long authSaasUserId);

    Response<Void> downFail(HttpServletResponse response, long authSaasUserId);

    Response<Void> exportBedInfo(String province, String city, String hospitalName, String departmentsName, long authSaasUserId);

    Response<Integer> departmentBedOrder(int departmentId, long bedId, long order);

    void washBedOrder();

    void washInitialIntention(WashBaseParam washBaseParam);
}
