package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdCreateContactUrlRecordDO;

import java.util.List;
import java.util.Set;

/**
 * 给顾问生成联系我链接记录(BdCreateContactUrlRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-09 19:56:53
 */
public interface BdCreateContactUrlRecordService {

    BdCreateContactUrlRecordDO queryById(long id);

    int insert(BdCreateContactUrlRecordDO bdCreateContactUrlRecord);

    int update(BdCreateContactUrlRecordDO bdCreateContactUrlRecord);

    boolean deleteById(long id);

    BdCreateContactUrlRecordDO queryByUniqueCodeAndRobotUserId(String uniqueCode, String robotUserId);

    /**
     * 联系我回调场景：更新external_userId
     * @param id 记录ID
     * @param externalUserId 外部用户ID
     * @return 更新行数
     */
    int updateExternalUserIdById(long id, String externalUserId);

    /**
     * 批量查询已生成活码的uniqueCode
     * @param uniqueCodes uniqueCode列表
     * @return 已生成活码的uniqueCode集合
     */
    Set<String> queryExistingUniqueCodesByBatch(List<String> uniqueCodes);

    /**
     * 取消链接（软删除）
     * @param id 记录ID
     * @param cancelReason 取消原因
     * @return 是否成功
     */
    boolean cancelLink(long id, String cancelReason);

    /**
     * 根据机器人用户ID查询所有未删除的记录
     * @param robotUserId 机器人用户ID
     * @return 联系方式记录列表
     */
    List<BdCreateContactUrlRecordDO> queryByRobotUserId(String robotUserId);

    /**
     * 根据顾问唯一编码查询所有未删除的记录
     * @param uniqueCode 顾问唯一编码
     * @return 联系方式记录列表
     */
    List<BdCreateContactUrlRecordDO> queryByUniqueCode(String uniqueCode);

}
