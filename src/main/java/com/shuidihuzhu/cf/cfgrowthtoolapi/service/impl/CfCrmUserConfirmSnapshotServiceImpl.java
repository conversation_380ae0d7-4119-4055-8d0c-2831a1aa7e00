package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCrmUserConfirmSnapshotDO;
import com.shuidihuzhu.cf.dao.CfCrmUserConfirmSnapshotDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfCrmUserConfirmSnapshotService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 确权快照表(CfCrmUserConfirmSnapshot)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-16 19:34:33
 */
@Service("cfCrmUserConfirmSnapshotService")
public class CfCrmUserConfirmSnapshotServiceImpl implements CfCrmUserConfirmSnapshotService {
   
    @Resource
    private CfCrmUserConfirmSnapshotDao cfCrmUserConfirmSnapshotDao;

    @Override
    public CfCrmUserConfirmSnapshotDO queryById(long id) {
        return cfCrmUserConfirmSnapshotDao.queryById(id);
    }
    

    @Override
    public int insert(CfCrmUserConfirmSnapshotDO cfCrmUserConfirmSnapshotDO) {
        return cfCrmUserConfirmSnapshotDao.insert(cfCrmUserConfirmSnapshotDO);
    }

}
