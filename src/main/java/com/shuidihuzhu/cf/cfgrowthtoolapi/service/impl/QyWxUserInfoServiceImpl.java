package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGwAgreeClauseRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.QywxUserInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmLoginExceptionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IQyWxUserInfoService;
import com.shuidihuzhu.cf.dao.CfGwAgreeClauseRecordDao;
import com.shuidihuzhu.cf.dao.qywx.CfCrmLoginExceptionDao;
import com.shuidihuzhu.cf.dao.qywx.QyWxUserInfoDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class QyWxUserInfoServiceImpl implements IQyWxUserInfoService {

    @Autowired
    private QyWxUserInfoDao qyWxUserInfoDao;
    @Autowired
    private CfCrmLoginExceptionDao cfCrmLoginExceptionDao;
    @Autowired
    private CfGwAgreeClauseRecordDao cfGwAgreeClauseRecordDao;


    @Override
    public int insert(QywxUserInfoDO qywxUserInfoDO) {
        return qyWxUserInfoDao.insert(qywxUserInfoDO);
    }

    @Override
    public QywxUserInfoDO getByToken(String token) {
        return qyWxUserInfoDao.getByToken(token);
    }

    @Override
    public QywxUserInfoDO getByUniqueCodeWithDeviceId(String uniqueCode, String deviceId){
        return qyWxUserInfoDao.getByUniqueCodeWithDeviceId(uniqueCode, deviceId);
    }

    @Override
    public List<QywxUserInfoDO> getLoginTokenByUniqueCodeWithNotEqualsDeviceId(String uniqueCode, String deviceId){
        return qyWxUserInfoDao.getLoginTokenByUniqueCodeWithNotEqualsDeviceId(uniqueCode, deviceId);
    }

    @Override
    public int updateLoginStatus(List<String> tokens, int loginStatus){
        if (CollectionUtils.isEmpty(tokens)){
            return 0;
        }
        return qyWxUserInfoDao.updateLoginStatus(tokens, loginStatus);
    }

    @Override
    public int fillMis(String uniqueCode, String mis) {
        if (StringUtils.isBlank(uniqueCode) || StringUtils.isBlank(mis)) {
            return 0;
        }
        return qyWxUserInfoDao.fillMis(uniqueCode, mis);
    }

    @Override
    public int saveOrUpdateLoginExceptionCount(String dateTime,
                                               CrowdfundingVolunteer crowdfundingVolunteer,
                                               String remark){
        if (crowdfundingVolunteer == null) {
            return 0;
        }
        int result;
        CfCrmLoginExceptionDO loginExceptionDO = cfCrmLoginExceptionDao.getLoginExceptionByUniqueCode(dateTime, crowdfundingVolunteer.getUniqueCode());
        if (loginExceptionDO==null){
            loginExceptionDO = new CfCrmLoginExceptionDO();
            loginExceptionDO.setMis(crowdfundingVolunteer.getMis());
            loginExceptionDO.setUniqueCode(crowdfundingVolunteer.getUniqueCode());
            loginExceptionDO.setRemark(remark);
            loginExceptionDO.setCount(1);
            loginExceptionDO.setDateTime(dateTime);
            result = cfCrmLoginExceptionDao.insert(loginExceptionDO);
        }else {
            result = cfCrmLoginExceptionDao.updateCount(dateTime, crowdfundingVolunteer.getUniqueCode(), remark);
        }
        return result;
    }
    @Override
    public CfCrmLoginExceptionDO getLoginExceptionByUniqueCode(String dateTime, String uniqueCode){
        return cfCrmLoginExceptionDao.getLoginExceptionByUniqueCode(dateTime, uniqueCode);
    }

    @Override
    public int updateCfCrmLoginExceptionIsDelete(CrowdfundingVolunteer crowdfundingVolunteer){
        try{
            if (crowdfundingVolunteer.getAccountStatus()== CrowdfundingVolunteerEnum.AccountStatusEnum.DEFAULT.getValue()
                    && StringUtils.isNotBlank(crowdfundingVolunteer.getUniqueCode())){
                return cfCrmLoginExceptionDao.updateIsDelete(DateUtil.getCurrentDateStr(), crowdfundingVolunteer.getUniqueCode());
            }
        }catch (Exception e){
            log.warn(this.getClass().getName()+" updateCfCrmLoginExceptionIsDelete fail :",e);
        }
        return 0;
    }

    @Override
    public List<String> getOnlineMis(List<String> misList){
        return qyWxUserInfoDao.getOnlineMis(misList);
    }

    @Override
    public int changeLoginStatusWhenLevelChange(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return 0;
        }
        return qyWxUserInfoDao.changeLoginStatusWhenLevelChange(uniqueCode);
    }

    @Override
    public int updateUniqueCode(long id, String volunteerUniqueCode){
        return qyWxUserInfoDao.updateUniqueCode(id, volunteerUniqueCode);
    }

    @Override
    public int addAgreeClauseRecord(CfGwAgreeClauseRecordDO recordDO) {
        try {
            int ret = cfGwAgreeClauseRecordDao.insert(recordDO);
            return ret;
        }catch (Exception e){
            log.warn("addAgreeClauseRecord exception",e);
        }
        return 0;
    }

    @Override
    public List<String> getOnlineByUniqueCode(List<String> uniqueCodeList) {
        return qyWxUserInfoDao.getOnlineByUniqueCode(uniqueCodeList);
    }
}
