package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponApplyRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponAccessModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.WeaponApplyCallBackModel;

/**
 * @author: fengxuan
 * @create 2020-10-16 4:06 下午
 **/
public interface ICfWeaponApplyHandleService {

    /**
     * 申请子弹后的处理
     * @param applyId:申请记录id
     */
    void handleAfterApply(int applyId);

    /**
     * 上级审核
     * @param applyId
     * @param pass ： true:审核通过, false:审核驳回
     * @param comment
     */
    void handleByLeader(int applyId, boolean pass, String comment);

    /**
     * 申请超时处理
     */
    void doRejectByTimeOut(int appId);

    /**
     * 活动方回调处理
     */
    void handleByActivityCallBack(WeaponApplyCallBackModel callBackModel);

    /**
     * 活动方审核通过生效
     */
    void doPassByActivity(CfWeaponApplyRecordDO cfWeaponApplyRecordDO, String comment,Long callBackId);

    /**
     * 活动方审核驳回 未生效
     */
    void doRejectActivity(CfWeaponApplyRecordDO cfWeaponApplyRecordDO, String comment,Long callBackId);

    /**
     * 活动方审核通过直接生效
     */
    void doPassByActivityDirect(CfWeaponApplyRecordDO cfWeaponApplyRecordDO, String comment);


    /**
     * 修复资源使用情况
     */
    void repairResource();


    void syncWeaponAssignOrg();


    OpResult<String> checkCanPassByLeader(int applyId);


    /**
     * 爱心首页资源未使用提示
     */
    void remindLoveHomeUnUse();

    /**
     * 判断是否需要上级审核
     */
    boolean needApproveByLeader(CrowdfundingVolunteer cfVolunteer, int caseId, int activityType);

    boolean needApproveByLeaderForLoveTheme(CrowdfundingVolunteer cfVolunteer, int caseId, int activityType, long activityUsedMoney);

    boolean needApproveByLeaderForBudgetManagement(CrowdfundingVolunteer cfVolunteer, long activityUsedMoney);

    void deductionApplyingResource(CfWeaponApplyRecordDO cfWeaponApplyRecordDO, int addApplyingResource);

    long getActivityUsedMoney(int activityId, int caseId);

    void preOccupancyFail(int activityId, int caseId);

}
