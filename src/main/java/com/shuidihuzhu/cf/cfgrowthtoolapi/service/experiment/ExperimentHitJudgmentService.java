package com.shuidihuzhu.cf.cfgrowthtoolapi.service.experiment;

import com.google.common.collect.Maps;
import com.shuidihuzhu.client.model.ExperimentHitResult;
import com.shuidihuzhu.client.model.ExperimentParam;

import java.util.Map;

/**
 * @Description: 根据入参场景，判断是否命中实验
 * @Author: panghair<PERSON>
 * @Date: 2024/7/31 2:20 PM
 */
public interface ExperimentHitJudgmentService {

    /**
     * 实验命中判断
     */
    ExperimentHitResult experimentHitJudge(ExperimentParam experimentParam);

}
