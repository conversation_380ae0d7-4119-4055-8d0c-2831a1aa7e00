package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmBdHomepageCaseDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmBdHonourDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfCrmHonourVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IBdCrmHomePageService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmBdHomepageCaseDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmBdHonourDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2020-05-14 16:44
 */
@Service
public class BdCrmHomePageServiceImpl implements IBdCrmHomePageService {

    @Autowired
    private CfCrmBdHonourDao cfCrmBdHonourDao;
    @Autowired
    private CfCrmBdHomepageCaseDao cfCrmBdHomepageCaseDao;


    @Override
    public List<CfCrmBdHomepageCaseDO> getCase(String uniqueCode) {
        return cfCrmBdHomepageCaseDao.getCase(uniqueCode);
    }

    @Override
    public List<CfCrmBdHomepageCaseDO> getCaseByCaseIdList(List<Long> caseIdList, String uniqueCode) {
        if (CollectionUtils.isEmpty(caseIdList)) {
            return Lists.newArrayList();
        }
        return cfCrmBdHomepageCaseDao.getCaseByCaseIdList(caseIdList, uniqueCode);
    }

    @Override
    public List<CfCrmBdHomepageCaseDO> getCaseByCaseIds(List<Long> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }
        return cfCrmBdHomepageCaseDao.getCaseByCaseIds(caseIds);
    }


    @Override
    public List<CfCrmBdHomepageCaseDO> getCaseByCreateTime(String uniqueCode, String createTime) {
        return cfCrmBdHomepageCaseDao.getCaseByCreateTime(uniqueCode, createTime);
    }

    @Override
    public void saveCfCrmBdHonour(CfCrmBdHonourDO cfCrmBdHonourDO) {
        cfCrmBdHonourDao.insert(cfCrmBdHonourDO);
    }

    @Override
    public CfCrmBdHonourDO getCfCrmBdHonourDOById(Long id) {
        return cfCrmBdHonourDao.getCfCrmBdHonourDOById(id);
    }

    @Override
    public void updateApproveStatus(Long id, Integer approveStatus) {
        cfCrmBdHonourDao.updateApproveStatus(id, approveStatus);
    }

    @Override
    public List<CfCrmHonourVo> getHonourPicWaitApprove(String approveMis, int status) {
        return cfCrmBdHonourDao.getHonourPicWaitApprove(approveMis, status);
    }

    @Override
    public CfCrmBdHonourDO getCfCrmBdHonourDOByUniqueCode(String uniqueCode) {
        return cfCrmBdHonourDao.getCfCrmBdHonourDOByUniqueCode(uniqueCode);
    }

    @Override
    public CfCrmBdHonourDO getCfCrmBdHonourDOByUniqueCodeWithApproveStatus(String uniqueCode, Integer approveStatus) {
        return cfCrmBdHonourDao.getCfCrmBdHonourDOByUniqueCodeWithApproveStatus(uniqueCode, approveStatus);
    }

    @Override
    public List<CfCrmBdHomepageCaseDO> getCaseListByCityId(Integer cityId, int limit) {
        return cfCrmBdHomepageCaseDao.getCaseListByCityId(cityId, limit);
    }

    @Override
    public List<CfCrmBdHomepageCaseDO> getCaseListByProvinceId(Integer provinceId, int limit) {
        return cfCrmBdHomepageCaseDao.getCaseListByProvinceId(provinceId, limit);
    }

    @Override
    public int saveCfBdHomepageCase(List<CfCrmBdHomepageCaseDO> cfCrmBdHomepageCaseDOS) {
        int result = 0;
        if (CollectionUtils.isEmpty(cfCrmBdHomepageCaseDOS)) {
            return result;
        }
        cfCrmBdHomepageCaseDOS = cfCrmBdHomepageCaseDOS.stream()
                .filter(GrowthtoolUtil.distinctByKey(CfCrmBdHomepageCaseDO::getUniqueKey)).collect(Collectors.toList());
        for (CfCrmBdHomepageCaseDO model : cfCrmBdHomepageCaseDOS) {
            CfCrmBdHomepageCaseDO resultDB = cfCrmBdHomepageCaseDao.getCaseByUniqueCodeWithCaseId(model.getUniqueCode(), model.getCaseId());
            if (resultDB == null) {
                result += this.saveBdHomepageCase(model);
            } else {
                if (resultDB.getCaseType() != model.getCaseType() && model.getCaseType() == 0) {
                    cfCrmBdHomepageCaseDao.updateCaseType(resultDB.getId(), model.getCaseType());
                }
            }
        }
        return result;
    }

    private int saveBdHomepageCase(CfCrmBdHomepageCaseDO model) {
        if (model.getCaseType() != 0) {
            return cfCrmBdHomepageCaseDao.insert(model);
        } else {
            CfCrmBdHomepageCaseDO cfCrmBdHomepageCaseDO = cfCrmBdHomepageCaseDao.getUnSelfCase(model.getUniqueCode());
            if (cfCrmBdHomepageCaseDO == null) {
                return cfCrmBdHomepageCaseDao.insert(model);
            } else {
                model.setId(cfCrmBdHomepageCaseDO.getId());
                return cfCrmBdHomepageCaseDao.update(model);
            }
        }
    }

    @Override
    public int updateCfCrmBdHomepageCase(CfCrmBdHomepageCaseDO cfCrmBdHomepageCaseDO) {
        return cfCrmBdHomepageCaseDao.update(cfCrmBdHomepageCaseDO);
    }

    @Override
    public CfCrmHonourVo getBdHonourPic(String uniqueCode) {
        return cfCrmBdHonourDao.getBdHonourPic(uniqueCode);
    }


    @Override
    public void updateCfCrmBdHonourDO(CfCrmBdHonourDO cfCrmBdHonourDO) {
        cfCrmBdHonourDao.update(cfCrmBdHonourDO);
    }

    @Override
    public int updateIsdelete(long caseId) {
        return cfCrmBdHomepageCaseDao.updateIsdelete(caseId);
    }

    @Override
    public List<Long> getCaseIdListExcludeChangedCaseIdList(String uniqueCode, List<Long> changedCaseIdList) {
        return cfCrmBdHomepageCaseDao.getCaseIdListExcludeChangedCaseIdList(uniqueCode, changedCaseIdList);
    }

    @Override
    public long countSelfCase(String uniqueCode) {
        return cfCrmBdHomepageCaseDao.countSelfCase(uniqueCode);
    }
}
