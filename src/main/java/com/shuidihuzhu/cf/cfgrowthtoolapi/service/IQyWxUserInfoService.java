package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGwAgreeClauseRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.QywxUserInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmLoginExceptionDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

public interface IQyWxUserInfoService {
    /**
     * 插入一条记录
     * @param qywxUserInfoDO
     * @return
     */
    int insert(QywxUserInfoDO qywxUserInfoDO);

    /**
     * 根据token获取用户信息
     * @param token
     * @return
     */
    QywxUserInfoDO getByToken(String token);

    QywxUserInfoDO getByUniqueCodeWithDeviceId(String uniqueCode, String deviceId);

    List<QywxUserInfoDO> getLoginTokenByUniqueCodeWithNotEqualsDeviceId(String uniqueCode, String deviceId);

    int updateLoginStatus(List<String> tokens, int loginStatus);

    int fillMis(String uniqueCode, String mis);

    int saveOrUpdateLoginExceptionCount(String dateTime,
                                        CrowdfundingVolunteer crowdfundingVolunteer,
                                        String remark);

    CfCrmLoginExceptionDO getLoginExceptionByUniqueCode(String dateTime, String uniqueCode);


    /**
     * 根据冻结状态 将cf_crm_login_exception 表中记录删除
     * @param crowdfundingVolunteer
     * @return
     */
    int updateCfCrmLoginExceptionIsDelete(CrowdfundingVolunteer crowdfundingVolunteer);

	List<String> getOnlineMis(List<String> misList);

    /**
     * 当职级调整时需要将token设置为失效
     */
    int changeLoginStatusWhenLevelChange(String uniqueCode);

    int updateUniqueCode(long id, String volunteerUniqueCode);

    int addAgreeClauseRecord(CfGwAgreeClauseRecordDO recordDO);

    List<String> getOnlineByUniqueCode(List<String> uniqueCodeList);
}
