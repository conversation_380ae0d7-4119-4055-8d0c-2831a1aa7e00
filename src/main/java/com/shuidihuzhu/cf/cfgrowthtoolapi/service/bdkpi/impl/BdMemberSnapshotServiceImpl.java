package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseTimeDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CrowdfundingVolunteerChangeEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfCrmMemberSnapshotModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfMemberKpiCalcInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.xrxs.SnapshotRepairData;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.IBdMemberSnapshotService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCaseTimeService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.bdkpi.CfCrmMemberSnapshotDao;
import com.shuidihuzhu.client.cf.growthtool.enums.VolunteerEnums;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-08-03 11:19 上午
 **/
@Slf4j
@Service
public class BdMemberSnapshotServiceImpl implements IBdMemberSnapshotService {

    @Autowired
    private CfCrmMemberSnapshotDao snapshotDao;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private ICfKpiCaseTimeService cfKpiCaseTimeService;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Autowired
    private MemberSnapshotHandlerService memberSnapshotHandlerService;

    @Override
    public void addBatch(List<CfCrmMemberSnapshotDO> snapshotDOList) {
        if (CollectionUtils.isEmpty(snapshotDOList)) {
            return;
        }
        snapshotDOList = snapshotDOList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        String dateKey = snapshotDOList.get(0).getDateKey();
        //执行前过滤掉重复的uniqueCode,以及已经存在的uniqueCode
        List<CfCrmMemberSnapshotDO> cfCrmMemberSnapshotDOS = this.listAllMemberSnapshotByMonth(dateKey);
        List<String> hasExistUniqueCodes = cfCrmMemberSnapshotDOS.stream()
                .map(CfCrmMemberSnapshotDO::getUniqueCode)
                .collect(Collectors.toList());
        List<CfCrmMemberSnapshotDO> filterDuplicateUniqueCode = snapshotDOList.stream()
                .filter(GrowthtoolUtil.distinctByKey(CfCrmMemberSnapshotDO::getUniqueCode))
                .filter(item -> !hasExistUniqueCodes.contains(item.getUniqueCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterDuplicateUniqueCode)) {
            log.info("过滤后快照为空");
            return;
        }
        //批量插入时限制下每次插入的个数
        Lists.partition(filterDuplicateUniqueCode, 100)
                .forEach(item -> snapshotDao.addBatch(item));
    }

    @Override
    public void updateWhenLevel(String uniqueCode, Date leaveTime) {
        if (leaveTime == null) {
            leaveTime = DateTime.now().toDate();
        }
        //不要直接取当前月应该取对应的离职时间
        String dateKey = new DateTime(leaveTime).toString("yyyy-MM");

        //更新当前周期内的uniqueCode对应的状态
        snapshotDao.updateWhenLevel(dateKey, uniqueCode, 1, leaveTime);
    }

    @Override
    public void updateSnapshotNewStaff(CrowdfundingVolunteer volunteer) {
        //如果是内部转岗且设置了入职时间才需要考虑更新是否是新员工
        //尽量精确范围
        if (volunteer.getEntryTime() != null && Objects.equals(volunteer.getEntryModel(), VolunteerEnums.EntryLeaveModel.inner_adjustment.getCode())) {
            String monthKey = DateTime.now().toString("yyyy-MM");
            CfKpiCaseTimeDO caseTimeDO = cfKpiCaseTimeService.getCfKpiCaseTimeByMonthkey(monthKey);
            if (caseTimeDO == null) {
                return;
            }
            boolean newStaff = CfCrmMemberSnapshotDO.newStaffOrNot(volunteer, caseTimeDO, true);
            log.info("volunteer:{},newStaff:{}", volunteer, newStaff);
            SnapshotRepairData snapshotRepairData = new SnapshotRepairData();
            snapshotRepairData.setUniqueCode(volunteer.getUniqueCode());
            snapshotRepairData.setNewStaff(newStaff ? 1 : 0);
            snapshotRepairData.setMonthKey(monthKey);
            snapshotDao.repairData(snapshotRepairData);
        }
    }


    @Override
    public List<CfCrmMemberSnapshotDO> listVolunteerByCityListAndLevel(List<String> cityList, int level) {
        String dateKey = DateTime.now().toString("yyyy-MM");
        return snapshotDao.selectByCityListAndLevel(dateKey, cityList, level);
    }

    @Override
    public List<String> listUniqueCodeByCityListAndLevel(List<String> cityList, int level) {
        String dateKey = DateTime.now().toString("yyyy-MM");
        return snapshotDao.selectUniqueCodeByCityListAndLevel(dateKey, cityList, level);
    }


    @Override
    public CfCrmMemberSnapshotDO getByUniqueCode(String uniqueCode) {
        String dateKey = DateTime.now().toString("yyyy-MM");
        return snapshotDao.getByUniqueCode(dateKey,uniqueCode);
    }

    @Override
    public List<CfMemberKpiCalcInfoModel> listAllMemberSnapshot(String monthKey) {
        return snapshotDao.listAllMemberSnapshot(monthKey);
    }

    @Override
    public List<CfCrmMemberSnapshotModel> getBdList(Integer level, String cityName, String monthKey) {
        if (Objects.equals(level, CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel())) {
            return snapshotDao.getBdList(level, "", monthKey);
        }
        return snapshotDao.getBdList(level, cityName, monthKey);
    }

    @Override
    public int updateRecordDay(CfCrmMemberSnapshotDO cfCrmMemberSnapshotDO) {
        return snapshotDao.updateRecordDay(cfCrmMemberSnapshotDO);
    }

    @Override
    public int updateOrgIdByUniqueCodeAndMonthKey(BdCrmOrgUserRelationDO relationDO, String monthKey) {
        if (Objects.isNull(relationDO) || StringUtils.isEmpty(relationDO.getUniqueCode()) || StringUtils.isEmpty(monthKey)){
            return 0;
        }
        log.info("updateOrgIdByUniqueCodeAndMonthKey_uniqueCode:{},monthKey:{}",relationDO.getUniqueCode(),monthKey);
        String uniqueCode = relationDO.getUniqueCode();
        //由最长路径改为取最短路径
        long result = Optional.ofNullable(memberInfoService.findShortestPathForKpi(relationDO.getUniqueCode())).map(BdCrmOrgUserRelationDO::getOrgId).orElse(0L);
        if (result > 0){
            int affectNum = snapshotDao.updateOrgIdByUniqueCodeAndMonthKey(uniqueCode, monthKey, result);
            if (affectNum > 0) {
                log.info("更新快照人员的组织id,信息为:{},monthKey:{},组织id:{}", relationDO, monthKey, result);
            }
            return affectNum;
        }else{
            return 0;
        }
    }

    @Override
    public List<CfCrmMemberSnapshotDO> listMemberByMonthKeyAndOrgId(String monthKey, List<Long> allSubOrgIncludeSelf) {
        if (CollectionUtils.isEmpty(allSubOrgIncludeSelf)){
            return Lists.newArrayList();
        }
        List<List<Long>> listList = Lists.partition(allSubOrgIncludeSelf, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> snapshotDao.listMemberByMonthKeyAndOrgIds(monthKey,list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
    }


    @Override
    public CfCrmMemberSnapshotDO getByUniqueCodeByMonthKey(String uniqueCode, String monthKey) {
        String dateKey;
        if (StringUtils.isNotEmpty(monthKey)){
            dateKey = monthKey;
        }else{
            dateKey = DateTime.now().toString("yyyy-MM");
        }
        return snapshotDao.getByUniqueCode(dateKey,uniqueCode);
    }

    @Override
    public List<CfCrmMemberSnapshotDO> listAllMemberSnapshotByMonth(String monthKey) {
        return snapshotDao.selectByDataKey(monthKey);
    }

    @Override
    public void repairData(SnapshotRepairData snapshotRepairData) {
        if (snapshotRepairData == null) {
            return;
        }
        snapshotDao.repairData(snapshotRepairData);
    }

    @Override
    public void saveOrUpdateWhenDimission(CrowdfundingVolunteerChangeEvent crowdfundingVolunteerChangeEvent) {
        CrowdfundingVolunteer crowdfundingVolunteer = crowdfundingVolunteerChangeEvent.getCrowdfundingVolunteer();
        log.info("saveOrUpdateWhenDimission:{}", JSONObject.toJSONString(crowdfundingVolunteer));
        if (Objects.isNull(crowdfundingVolunteer) || Objects.isNull(crowdfundingVolunteer.getLeaveTime()) || Objects.isNull(crowdfundingVolunteer.getUniqueCode())){
            return;
        }
        //只录入顾问
        //if (!CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel().equals(crowdfundingVolunteer.getLevel())){
        //    return;
        //}
        String curMonthKey = DateTime.now().toString("yyyy-MM");
        //延后一个月数据
        String nextMonthKey = DateTime.now().plusMonths(1).toString("yyyy-MM");
        CfKpiCaseTimeDO cfKpiCaseTimeDO = cfKpiCaseTimeService.getCfKpiCaseTimeByMonthkey(curMonthKey);
        if (Objects.isNull(cfKpiCaseTimeDO)) {
            return;
        }
        Date endTime = DateUtil.getDateFromLongString(cfKpiCaseTimeDO.getEndTime() + " 23:59:59");
        Date startTime = DateUtil.getDateFromShortString(cfKpiCaseTimeDO.getStartTime());
        //离职时间
        Date leaveTime = crowdfundingVolunteer.getLeaveTime();
        boolean needHandle = true;
        if (leaveTime.before(startTime)) {
            needHandle = false;
        }
        if (!needHandle) {
            log.info("离职时间早于周期开始时间,不处理");
            return;
        }
        createOrUpdateDimissionMember(crowdfundingVolunteer, curMonthKey, leaveTime);
        if (leaveTime.after(endTime)) {
            createOrUpdateDimissionMember(crowdfundingVolunteer, nextMonthKey, leaveTime);
        }
    }

    private void createOrUpdateDimissionMember(CrowdfundingVolunteer crowdfundingVolunteer, String monthKey, Date leaveTime) {
        CfCrmMemberSnapshotDO snapshotDO = snapshotDao.getByUniqueCode(monthKey, crowdfundingVolunteer.getUniqueCode());
        //快照数据存在
        if (Objects.nonNull(snapshotDO)) {
            //只更新离职时间
            snapshotDao.updateWhenLevel(monthKey, crowdfundingVolunteer.getUniqueCode(), 1, crowdfundingVolunteer.getLeaveTime());
        } else {
            //只关注需要设置数据的,其他员工不考虑
            int volunteerLevel = crowdfundingVolunteer.getLevel();
            CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(volunteerLevel);
            if (roleEnum == null) {
                return;
            }
            CrowdfundingVolunteerEnum.KpiMemberSnapshotStatusEnum kpiMemberSnapshotStatusEnum = roleEnum.getKpiMemberSnapshotStatusEnum();
            if (!Objects.equals(kpiMemberSnapshotStatusEnum, CrowdfundingVolunteerEnum.KpiMemberSnapshotStatusEnum.NUM0)) {
                return;
            }
            List<BdCrmOrgUserRelationDO> orgUserRelationList = crmOrganizationRelationService.listMemberOrgRelationNoMatterDelete(crowdfundingVolunteer.getUniqueCode())
                    .stream()
                    .sorted(Comparator.comparing(BdCrmOrgUserRelationDO::getId).reversed())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orgUserRelationList)) {
                return;
            }
            snapshotDO = new CfCrmMemberSnapshotDO();
            snapshotDO.setDateKey(monthKey);
            snapshotDO.setUniqueCode(crowdfundingVolunteer.getUniqueCode());
            snapshotDO.setVolunteerName(crowdfundingVolunteer.getVolunteerName());
            snapshotDO.setLevel(crowdfundingVolunteer.getLevel());
            snapshotDO.setNewStaff(0);
            snapshotDO.setWorkStatus(crowdfundingVolunteer.getWorkStatus());
            snapshotDO.setLeaveTime(leaveTime);
            //如果是存在组织,直接使用
            Optional<BdCrmOrgUserRelationDO> first = orgUserRelationList.stream()
                    .filter(item -> item.getIsDelete() == 0)
                    .findFirst();
            BdCrmOrgUserRelationDO relationDO = first.orElseGet(() -> orgUserRelationList.get(0));
            BdCrmOrganizationDO currentOrg = crmSelfBuiltOrgReadService.getCurrentOrgById(relationDO.getOrgId());
            if (currentOrg == null) {
                log.info("组织被删除,测试组织,非重要数据");
                return;
            }
            List<BdCrmOrganizationDO> organizationDOS = crmSelfBuiltOrgReadService.listParentOrgAsChain(currentOrg.getId());
            String chainName = organizationDOS.stream().map(item -> String.valueOf(item.getOrgName())).collect(Collectors.joining("-"));
            snapshotDO.setOrgId(currentOrg.getId());
            snapshotDO.setOrgPath(chainName);
            snapshotDO.setCity(currentOrg.getOrgName());
            snapshotDO.setIsDelete(0);
            memberSnapshotHandlerService.setOrgRelation(relationDO, snapshotDO, currentOrg, crowdfundingVolunteer.getLevel());
            //设置兼职信息,未以后排除兼职人员留个口子
            snapshotDO.setPartnerTag(crowdfundingVolunteer.getPartnerTag());
            snapshotDao.addBatch(Lists.newArrayList(snapshotDO));
        }
    }

    @Override
    public List<Long> getScoreOrgIds(String monthKey, Set<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)){
            return Lists.newArrayList();
        }
        return snapshotDao.getScoreOrgIds(monthKey,orgIds);
    }

    @Override
    public List<CfCrmMemberSnapshotDO> listAllMemberSnapshotNotCareDelete(String monthKey) {
        return snapshotDao.listAllMemberNotCareDelete(monthKey);
    }


}
