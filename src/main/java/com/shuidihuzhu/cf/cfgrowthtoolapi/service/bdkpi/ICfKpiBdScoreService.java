package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiBdScoreDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CustomPerformanceScoreModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.KPICustomRuleScoreModel;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
public interface ICfKpiBdScoreService {

    OpResult<List<KPICustomRuleScoreModel>> querySuperiorScoreDetail(String monthkey, String uniqueCode);

    List<CfKpiBdScoreDO> listAllKpiBdScore(String monthKey);

    List<CfKpiBdScoreDO> getKpiBdList(Integer level, String monthKey, List<String> uniqueCodeList);

    int batchSaveOrUpdate(List<CfKpiBdScoreDO> kpiBdList, String monthKey, Integer level);

    List<CustomPerformanceScoreModel> listCustomScore(CfKpiBdScoreDO cfKpiBdScoreDO);

    OpResult<List<KPICustomRuleScoreModel>> queryTeamRecScoreDetail(String monthKey, String uniqueCode);

    List<CfKpiBdScoreDO> listKpiBdByMonthAndUniqueCodes(String monthKey, List<String> uniqueCodeList);

    int batchSaveOrUpdate(List<CfKpiBdScoreDO> kpiBdList, String monthKey);

    CfKpiBdScoreDO queryBdScoreList(String monthkey, String uniqueCode);
}
