package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiVolunteerManagerCntDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiVolunteerManagerCntService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepManagerCntModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 人效-人数
 *
 * @author: fengxuan
 * @create 2023-03-20 19:25
 **/
@Slf4j
@Service
public class DonateManagerAvgCntService extends AbstractPushDataService {

    @Autowired
    private CfKpiVolunteerManagerCntService kpiVolunteerManagerCntService;


    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.donate_manager_avg_cnt;
    }


    @Override
    protected List<PepManagerCntModel> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        //找到对应的月份
        List<CfKpiVolunteerManagerCntDO> cfKpiCaseMonthDonateList = kpiVolunteerManagerCntService.listByDayKey(pushWhichDay.toString(GrowthtoolUtil.ymdfmt));
        List<PepManagerCntModel> result = Lists.newArrayList();
        Map<String, List<String>> cntListMap = Maps.newHashMap();
        for (CfKpiVolunteerManagerCntDO cfKpiCaseMonthDonateData : cfKpiCaseMonthDonateList) {
            String areaGl = cfKpiCaseMonthDonateData.getAreaGl();
            String regionGl = cfKpiCaseMonthDonateData.getRegionGl();
            if (StringUtils.isBlank(cfKpiCaseMonthDonateData.getVolunteerCnt())) {
                continue;
            }
            if (StringUtils.isNotBlank(areaGl)) {
                cntListMap.computeIfAbsent(areaGl, k -> Lists.newArrayList()).add(cfKpiCaseMonthDonateData.getVolunteerCnt());
            }
            if (StringUtils.isNotBlank(regionGl)) {
                cntListMap.computeIfAbsent(regionGl, k -> Lists.newArrayList()).add(cfKpiCaseMonthDonateData.getVolunteerCnt());
            }
        }

        for (Map.Entry<String, List<String>> item : cntListMap.entrySet()) {
            PepManagerCntModel pepManagerCntModel = new PepManagerCntModel();
            BigDecimal bigDecimal = new BigDecimal(0);
            for (String s : item.getValue()) {
                bigDecimal = bigDecimal.add(new BigDecimal(s));
            }
            pepManagerCntModel.setUserId(item.getKey());
            pepManagerCntModel.setLotId(lotInfo.getLotId());
            pepManagerCntModel.setVolunteer_cnt(Double.parseDouble(bigDecimal.toString()));
            result.add(pepManagerCntModel);
        }
        return result;
    }

}
