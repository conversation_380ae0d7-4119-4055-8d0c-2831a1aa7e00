package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.common.CommonPermissionConfigEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmStyleCustomizationColor;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmStyleCustomizationDiglossia;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmStyleCustomizationParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdCrmStyleCustomizationVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.CommonPermissionConfigDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfBdCrmStyleCustomizationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.common.CommonPermissionConfigService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.client.adminpure.feign.AdminCaseDisplayRecordFeignClient;
import com.shuidihuzhu.cf.client.apipure.feign.CfCaseDisplaySettingFeignClient;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseDisplaySettingVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/2  11:30
 */
@Service
@Slf4j
public class CfBdCrmStyleCustomizationServiceImpl implements CfBdCrmStyleCustomizationService {

    @Autowired
    private AdminCaseDisplayRecordFeignClient adminCaseDisplayRecordFeignClient;

    @Autowired
    private CfCaseDisplaySettingFeignClient cfCaseDisplaySettingFeignClient;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Autowired
    private CommonPermissionConfigService commonPermissionConfigService;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Override
    public Response<CfBdCrmStyleCustomizationVo> showStyleCustomizationInfo(String infoUuid, CrowdfundingVolunteer cfVolunteer) {
        if (StringUtils.isEmpty(infoUuid) || cfVolunteer == null) {
            return NewResponseUtil.makeFail("参数不能为空");
        }
        OperationResult<Integer> operationResult = adminCaseDisplayRecordFeignClient.countCaseDisplayRecord(infoUuid);
        int count = Optional.ofNullable(operationResult).filter(OperationResult::isSuccess).map(OperationResult::getData).orElse(0);
        CfBdCrmStyleCustomizationVo cfBdCrmStyleCustomizationVo = new CfBdCrmStyleCustomizationVo();
        //在小鲸鱼只能修改1次，不区分是改颜色还是改双语统一只能修改一次
        if (count > 0) {
            return NewResponseUtil.makeSuccess(cfBdCrmStyleCustomizationVo);
        }
        //查询配置
        Map<Integer, CommonPermissionConfigDo> commonPermissionConfigDoMap = commonPermissionConfigService.mapByConfigTypes(CommonPermissionConfigEnums.ConfigType.styleCustomization);
        if (MapUtils.isEmpty(commonPermissionConfigDoMap)) {
            return NewResponseUtil.makeSuccess(cfBdCrmStyleCustomizationVo);
        }
        //查询顾问所在组织
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOList = crmOrganizationRelationService.listMemberOrgRelationByUniqueCode(cfVolunteer.getUniqueCode());
        if (CollectionUtils.isEmpty(bdCrmOrgUserRelationDOList)) {
            return NewResponseUtil.makeSuccess(cfBdCrmStyleCustomizationVo);
        }
        //查询案例当前展示
        CaseDisplaySettingVo displaySettingVo = getCaseDisplaySettingVo(infoUuid);
        //1. 颜色：直营分区经理可对案例详情页设置颜色；渠道高级渠道经理可对案例详情页设置颜色
        supportGreenColor(cfBdCrmStyleCustomizationVo, displaySettingVo, cfVolunteer, bdCrmOrgUserRelationDOList, commonPermissionConfigDoMap);

        //    2. 双语：sea后台组织架构下，案例归属的城市对应的顾问及业务经理有对案例设置双语的能力
        //      1. 直营：顾问，业务经理
        //      2. 渠道：筹款合作组长，合作团队顾问，筹款合作团队
        supportBilingual(cfBdCrmStyleCustomizationVo, displaySettingVo, cfVolunteer, bdCrmOrgUserRelationDOList, commonPermissionConfigDoMap);
        return NewResponseUtil.makeSuccess(cfBdCrmStyleCustomizationVo);
    }

    @Override
    public Response<Void> addOrUpdateCaseDisplaySetting(CfBdCrmStyleCustomizationParam cfBdCrmStyleCustomizationParam, CrowdfundingVolunteer cfVolunteer) {
        if (cfBdCrmStyleCustomizationParam == null || StringUtils.isEmpty(cfBdCrmStyleCustomizationParam.getInfoUuid()) || cfVolunteer == null) {
            return NewResponseUtil.makeFail("参数不能为空");
        }

        CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegate.getCrowdfundingInfoByInfouuid(cfBdCrmStyleCustomizationParam.getInfoUuid());
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeFail("案例不存在");
        }

        //查询案例当前展示
        CaseDisplaySettingVo displaySettingVo = getCaseDisplaySettingVo(crowdfundingInfo.getInfoId());
        CaseDisplaySettingVo caseDisplaySettingVo = getCaseDisplaySettingVo(cfBdCrmStyleCustomizationParam, crowdfundingInfo, displaySettingVo, cfVolunteer);
        OperationResult<Void> operationResult = cfCaseDisplaySettingFeignClient.addOrUpdateCaseDisplaySetting(caseDisplaySettingVo);
        if (operationResult.isFail()) {
            return NewResponseUtil.makeFail(operationResult.getMsg());
        }
        return NewResponseUtil.makeSuccess();
    }

    @NotNull
    private static CaseDisplaySettingVo getCaseDisplaySettingVo(CfBdCrmStyleCustomizationParam cfBdCrmStyleCustomizationParam, CrowdfundingInfo crowdfundingInfo,
                                                                CaseDisplaySettingVo displaySettingVo, CrowdfundingVolunteer cfVolunteer) {
        CaseDisplaySettingVo caseDisplaySettingVo = new CaseDisplaySettingVo();
        caseDisplaySettingVo.setCaseId(crowdfundingInfo.getId());
        caseDisplaySettingVo.setInfoUuid(crowdfundingInfo.getInfoId());
        caseDisplaySettingVo.setUniqueCode(cfVolunteer.getUniqueCode());
        caseDisplaySettingVo.setUpdateChannel(CaseDisplaySettingVo.UPDATE_CHANNEL_TYPE.WHALE.getType());
        if (cfBdCrmStyleCustomizationParam.getCfBdCrmStyleCustomizationDiglossia() == null) {
            caseDisplaySettingVo.setLanguage(Optional.ofNullable(displaySettingVo).map(CaseDisplaySettingVo::getLanguage).orElse(""));
        } else {
            caseDisplaySettingVo.setLanguage(cfBdCrmStyleCustomizationParam.getCfBdCrmStyleCustomizationDiglossia().getLanguage());
        }
        if (cfBdCrmStyleCustomizationParam.getCfBdCrmStyleCustomizationColor() == null) {
            caseDisplaySettingVo.setColor(Optional.ofNullable(displaySettingVo).map(CaseDisplaySettingVo::getColor).orElse(""));
        } else {
            caseDisplaySettingVo.setColor(cfBdCrmStyleCustomizationParam.getCfBdCrmStyleCustomizationColor().getColor());
        }
        return caseDisplaySettingVo;
    }

    private void supportGreenColor(CfBdCrmStyleCustomizationVo cfBdCrmStyleCustomizationVo, CaseDisplaySettingVo displaySettingVo, CrowdfundingVolunteer cfVolunteer,
                                   List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOList, Map<Integer, CommonPermissionConfigDo> commonPermissionConfigDoMap) {
        CommonPermissionConfigDo commonPermissionConfigDo = commonPermissionConfigDoMap.get(CommonPermissionConfigEnums.ConfigType.SUPPORT_GREEN_COLOR.getCode());
        if (commonPermissionConfigDo == null || StringUtils.isEmpty(commonPermissionConfigDo.getPermissionValue())) {
            return;
        }

        List<String> orgIdList = Splitter.on(",").splitToList(commonPermissionConfigDo.getPermissionValue());
        Set<Long> orgIdSet = orgIdList.stream().map(Long::valueOf).collect(Collectors.toSet());
        Optional<Long> optional = bdCrmOrgUserRelationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).filter(orgIdSet::contains).findFirst();
        if (optional.isEmpty()) {
            return;
        }

        //    1. 颜色：直营分区经理可对案例详情页设置颜色；渠道高级渠道经理可对案例详情页设置颜色
        if (cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getLevel() ||
                cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getLevel()) {
            cfBdCrmStyleCustomizationVo.setShow(true);
            cfBdCrmStyleCustomizationVo.setColorUse(Optional.ofNullable(displaySettingVo).map(CaseDisplaySettingVo::getColor).orElse(""));
            cfBdCrmStyleCustomizationVo.setColorList(Lists.newArrayList(
                    CfBdCrmStyleCustomizationColor.getCfBdCrmStyleCustomizationColor("绿色", String.valueOf(CaseDisplaySettingVo.COLOR_TYPE.GREEN)),
                    CfBdCrmStyleCustomizationColor.getCfBdCrmStyleCustomizationColor("不额外定制颜色", "")));
        }
    }

    private void supportBilingual(CfBdCrmStyleCustomizationVo cfBdCrmStyleCustomizationVo, CaseDisplaySettingVo displaySettingVo, CrowdfundingVolunteer cfVolunteer,
                                  List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOList, Map<Integer, CommonPermissionConfigDo> commonPermissionConfigDoMap) {
        CommonPermissionConfigDo commonPermissionConfigDo = commonPermissionConfigDoMap.get(CommonPermissionConfigEnums.ConfigType.SUPPORT_BILINGUAL.getCode());
        if (commonPermissionConfigDo == null || StringUtils.isEmpty(commonPermissionConfigDo.getPermissionValue())) {
            return;
        }

        List<String> orgIdList = Splitter.on(",").splitToList(commonPermissionConfigDo.getPermissionValue());
        Set<Long> orgIdSet = orgIdList.stream().map(Long::valueOf).collect(Collectors.toSet());
        Optional<Long> optional = bdCrmOrgUserRelationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).filter(orgIdSet::contains).findFirst();
        if (optional.isEmpty()) {
            return;
        }

        //    2. 双语：sea后台组织架构下，案例归属的城市对应的顾问及业务经理有对案例设置双语的能力
        //      1. 直营：顾问，业务经理
        //      2. 渠道：筹款合作组长，合作团队顾问，筹款合作团队
        if (cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel() ||
                cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel() ||
                cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_PROVINCE.getLevel() ||
                cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_BOSS.getLevel() ||
                cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_COMMON.getLevel()) {
            cfBdCrmStyleCustomizationVo.setShow(true);
            cfBdCrmStyleCustomizationVo.setDiglossiaUse(Optional.ofNullable(displaySettingVo).map(CaseDisplaySettingVo::getLanguage).orElse(""));
            cfBdCrmStyleCustomizationVo.setDiglossiaList(Lists.newArrayList(
                    CfBdCrmStyleCustomizationDiglossia.getCfBdCrmStyleCustomizationDiglossia("维语", String.valueOf(CaseDisplaySettingVo.DOUBLE_LANGUAGE_TYPE.uyGurLanguage)),
                    CfBdCrmStyleCustomizationDiglossia.getCfBdCrmStyleCustomizationDiglossia("哈语", String.valueOf(CaseDisplaySettingVo.DOUBLE_LANGUAGE_TYPE.kazakhLanguage)),
                    CfBdCrmStyleCustomizationDiglossia.getCfBdCrmStyleCustomizationDiglossia("藏语", String.valueOf(CaseDisplaySettingVo.DOUBLE_LANGUAGE_TYPE.zangLanguage)),
                    CfBdCrmStyleCustomizationDiglossia.getCfBdCrmStyleCustomizationDiglossia("不展示双语", "")));
        }
    }

    private CaseDisplaySettingVo getCaseDisplaySettingVo(String infoUuid) {
        CaseDisplaySettingVo caseDisplaySettingVo = new CaseDisplaySettingVo();
        caseDisplaySettingVo.setInfoUuid(infoUuid);
        OperationResult<CaseDisplaySettingVo> caseDisplaySettingVoOperationResult = cfCaseDisplaySettingFeignClient.getCaseDisplaySetting(caseDisplaySettingVo);
        return Optional.ofNullable(caseDisplaySettingVoOperationResult)
                .filter(OperationResult::isSuccess)
                .map(OperationResult::getData)
                .orElse(null);
    }

}
