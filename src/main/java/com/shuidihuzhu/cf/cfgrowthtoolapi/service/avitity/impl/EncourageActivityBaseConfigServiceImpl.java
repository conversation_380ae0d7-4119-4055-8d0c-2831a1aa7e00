package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.EncourageActivityBaseConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.param.EncourageActivityQueryParam;
import com.shuidihuzhu.cf.dao.avtivity.EncourageActivityBaseConfigDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.EncourageActivityBaseConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 激励活动基本信息表(EncourageActivityBaseConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-23 15:32:55
 */
@Service("encourageActivityBaseConfigService")
public class EncourageActivityBaseConfigServiceImpl implements EncourageActivityBaseConfigService {
   
    @Resource
    private EncourageActivityBaseConfigDao encourageActivityBaseConfigDao;

    @Override
    public EncourageActivityBaseConfigDO queryById(long id) {
        return encourageActivityBaseConfigDao.queryById(id);
    }
    

    @Override
    public int insert(EncourageActivityBaseConfigDO encourageActivityBaseConfig) {
        return encourageActivityBaseConfigDao.insert(encourageActivityBaseConfig);
    }

    @Override
    public int update(EncourageActivityBaseConfigDO encourageActivityBaseConfig) {
        return encourageActivityBaseConfigDao.update(encourageActivityBaseConfig);
    }

    @Override
    public boolean deleteById(long id) {
        return encourageActivityBaseConfigDao.deleteById(id) > 0;
    }

    @Override
    public boolean updateStatusById(long id, int status) {
        return encourageActivityBaseConfigDao.updateStatusById(id, status) > 0;
    }

    @Override
    public List<EncourageActivityBaseConfigDO> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return encourageActivityBaseConfigDao.listByIds(ids);
    }

    @Override
    public List<EncourageActivityBaseConfigDO> listByCondition(EncourageActivityQueryParam query) {
        return encourageActivityBaseConfigDao.listByCondition(query);
    }

    @Override
    public int totalByCondition(EncourageActivityQueryParam query) {
        return encourageActivityBaseConfigDao.totalByCondition(query);
    }


    @Override
    public EncourageActivityBaseConfigDO getLastOneByCondition(EncourageActivityQueryParam query) {
        return encourageActivityBaseConfigDao.getLastOneByCondition(query);
    }
}
