package com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRecommendStatusModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-10
 */
public interface ICfInviteCaseInfoService {

    OpResult<Boolean> checkFilled(long userId, String infoUuid);

    OpResult<Void> fillIfo(long userId, String mobile, String verifyCode, String idCard, String name, String infoUuid, String clientIp);

    OpResult<Void> fillInfoAuto(long userId);

    OpResult<CfRecommendStatusModel> showRecommendStatus(long userId);

    void doExecute(Date startTime, Date endTime);

    Response<Boolean> isRaiserInvitor(long invitorId);

    Response<CfRaiserInvitorStatus> getRaiserInvitorStatus(long invitorId);

    Response<PageReturnModel<CfInvitedCaseInfo>> listInvitedCase(long invitorId, int current, int pageSize);

    Response<List<CfInvitorPaymentInfo>> getInvitorPaymentInfo(List<Integer> caseIds);

    Response<Boolean> changeInvitorPaymentStatus(int caseId, int targetDrawStatus);
}
