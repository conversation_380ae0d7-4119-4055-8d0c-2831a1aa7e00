package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill.CfBdPartnerBillOrgSummaryService;
import com.shuidihuzhu.cf.dao.lovepartner.CfBdPartnerBillOrgSummaryDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfBdPartnerBillOrgSummaryDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 爱心伙伴兼职-组织总账单(CfBdPartnerBillOrgSummary)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-02 19:49:46
 */
@Service
public class CfBdPartnerBillOrgSummaryServiceImpl implements CfBdPartnerBillOrgSummaryService {

    @Resource
    private CfBdPartnerBillOrgSummaryDao cfBdPartnerBillOrgSummaryDao;

    @Override
    public CfBdPartnerBillOrgSummaryDO queryById(long id) {
        return cfBdPartnerBillOrgSummaryDao.queryById(id);
    }


    @Override
    public int insertOrUpdate(CfBdPartnerBillOrgSummaryDO cfBdPartnerBillOrgSummary) {
        //如果组织账单生成了就不要重复生成
        CfBdPartnerBillOrgSummaryDO orgSummaryDO = cfBdPartnerBillOrgSummaryDao.queryOrgBillByOrg(cfBdPartnerBillOrgSummary.getCycleId(), cfBdPartnerBillOrgSummary.getOrgId());
        if (orgSummaryDO != null) {
            cfBdPartnerBillOrgSummary.setId(orgSummaryDO.getId());
            cfBdPartnerBillOrgSummaryDao.update(cfBdPartnerBillOrgSummary);
            return 0;
        }
        return cfBdPartnerBillOrgSummaryDao.insert(cfBdPartnerBillOrgSummary);
    }

    @Override
    public int update(CfBdPartnerBillOrgSummaryDO cfBdPartnerBillOrgSummary) {
        return cfBdPartnerBillOrgSummaryDao.update(cfBdPartnerBillOrgSummary);
    }

    @Override
    public int updateApproveStatus(int approveStatus, long orgId, int cycleId) {
        return cfBdPartnerBillOrgSummaryDao.updateApproveStatus(approveStatus, orgId, cycleId);
    }

    @Override
    public boolean deleteById(long id) {
        return cfBdPartnerBillOrgSummaryDao.deleteById(id) > 0;
    }

    @Override
    public List<CfBdPartnerBillOrgSummaryDO> listByOrgIdsAndCycleId(List<Long> orgIds, int cycleId) {
        return cfBdPartnerBillOrgSummaryDao.listByOrgIdsAndCycleId(orgIds, cycleId);
    }

    @Override
    public int deleteByCycleId(long cycleId) {
        return cfBdPartnerBillOrgSummaryDao.deleteByCycleId(cycleId);
    }
}