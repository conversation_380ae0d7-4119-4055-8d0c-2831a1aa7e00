package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCaseDayDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.DepartmentDayCaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.HospitalDonateModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CampaignMapTrendView;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OrgDataStatVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfCaseDayDataService;
import com.shuidihuzhu.cf.dao.CfCaseDayDataDao;
import com.shuidihuzhu.cf.repository.CfCaseDayDataRepository;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 案例每日数据(CfCaseDayData)表服务实现类
 *
 * <AUTHOR>
 * @since 2020-11-13 14:45:38
 */
@Service
@Slf4j
public class CfCaseDayDataServiceImpl implements CfCaseDayDataService {


    @Autowired
    private CfCaseDayDataRepository cfCaseDayDataRepository;

    @Autowired
    private CfCaseDayDataDao cfCaseDayDataDao;

    @Override
    public List<OrgDataStatVO> countDonateCount(String startTime, String endTime, List<Long> orgIds) {
        List<OrgDataStatVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orgIds)){
            return result;
        }
        List<List<Long>> orgIdList = Lists.partition(orgIds,100);
        List<OrgDataStatVO> listDataFromDb = orgIdList.parallelStream().map(list -> cfCaseDayDataRepository.countDonateCount(startTime, endTime, list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());

        if (CollectionUtils.isEmpty(listDataFromDb)){
            result = listDataFromDb;
        }else{
            Map<String,List<OrgDataStatVO>> dateStatMap = listDataFromDb.stream().collect(Collectors.groupingBy(OrgDataStatVO::getDateTime));
            for (String key : dateStatMap.keySet()){
                OrgDataStatVO orgDataStatVO = new OrgDataStatVO();
                orgDataStatVO.setDateTime(key);
                orgDataStatVO.setDonateCount(dateStatMap.get(key).stream().mapToInt(OrgDataStatVO::getDonateCount).sum());
                orgDataStatVO.setDonateAmount(dateStatMap.get(key).stream().mapToLong(OrgDataStatVO::getDonateAmount).sum());
                result.add(orgDataStatVO);
            }
        }
        return result;
    }

    @Override
    public OrgDataStatVO countRealTimeDonateCount(String time, List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds) || StringUtils.isBlank(time)) {
            return new OrgDataStatVO();
        }
        String finalTime = time.length() > GeneralConstant.SHORT_TIME_LENGTH ?
                time.substring(0, GeneralConstant.SHORT_TIME_LENGTH) : time;
        List<List<Long>> orgIdList = Lists.partition(orgIds,100);
        return orgIdList.parallelStream()
                .map(list -> Optional.ofNullable(cfCaseDayDataRepository.countRealTimeDonateCount(finalTime, list)).orElse(new OrgDataStatVO()))
                .reduce((o1, o2) -> {
                    OrgDataStatVO orgDataStatVO = new OrgDataStatVO();
                    orgDataStatVO.setDonateAmount(o1.getDonateAmount() + o2.getDonateAmount());
                    orgDataStatVO.setDonateCount(o1.getDonateCount() + o2.getDonateCount());
                    return orgDataStatVO;
                })
                .orElse(new OrgDataStatVO());
    }


    @Override
    public List<OrgDataStatVO> countDonateCountByOrgId(BdCrmDataStatParam crmDataStatParam, List<Long> querOrgIdList) {
        if (CollectionUtils.isEmpty(querOrgIdList)){
            return Lists.newArrayList();
        }
        DateQueryParam dateQueryParam = crmDataStatParam.getDateQueryParam();
        String startTime = dateQueryParam.getStartTime();
        Date endDate = DateUtil.addDay(DateUtil.getDateFromShortString(dateQueryParam.getEndTime()),-1);
        String endTime = DateUtil.formatDate(endDate);
        log.info("startTime:{},endTime:{},querOrgIdList:{}",startTime,endTime,querOrgIdList);
        List<List<Long>> orgIdList = Lists.partition(querOrgIdList,100);
        return orgIdList.parallelStream().map(list -> cfCaseDayDataRepository.countDonateCountByOrgId(startTime,endTime,list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
    }
    @Override
    public List<OrgDataStatVO> countDonateCountGroupByUniqueCode(BdCrmDataStatParam crmDataStatParam, List<Long> querOrgIdList) {
        if (CollectionUtils.isEmpty(querOrgIdList)){
            return Lists.newArrayList();
        }
        DateQueryParam dateQueryParam = crmDataStatParam.getDateQueryParam();
        String startTime = dateQueryParam.getStartTime();
        Date endDate = DateUtil.addDay(DateUtil.getDateFromShortString(dateQueryParam.getEndTime()),-1);
        String endTime = DateUtil.formatDate(endDate);
        log.info("startTime:{},endTime:{},querOrgIdList:{}",startTime,endTime,querOrgIdList);
        return cfCaseDayDataRepository.countDonateCountGroupByUniqueCode(startTime,endTime,querOrgIdList);
    }

    @Override
    public Long getTotalCfDonatedCountByDateTimeWithOrderIdList(List<String> dateTimes, List<Integer> allSubOrgIds) {
        return cfCaseDayDataRepository.countDonatedCountByDateTimeWithOrderIdList(dateTimes,allSubOrgIds);
    }

    @Override
    public List<DepartmentDayCaseModel> departmentGbDateKey(DepartmentCmpParam departmentCmpParam) {
        DateQueryParam dateQueryParam = departmentCmpParam.getDateQueryParam();
        return cfCaseDayDataRepository.departmentGbDateKey(departmentCmpParam.getDepartmentIds(), dateQueryParam.getStartTime(), dateQueryParam.getEndTime());
    }


    @Override
    public Map<String, Integer> viewAreaByHospital(String vvhospitalCode, List<String> vhospitalCodes, String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return Maps.newHashMap();
        }
        return cfCaseDayDataRepository.viewAreaByHospital(vvhospitalCode, vhospitalCodes, startTime, endTime)
                .stream()
                .collect(Collectors.toMap(HospitalDonateModel::getVhospitalCode, HospitalDonateModel::getDonateCnt, (before, after) -> before));
    }

    @Override
    public List<CampaignMapTrendView> hospitalGbDateKey(String vvhospitalCode, String vhospitalCode, String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return Lists.newArrayList();
        }
        return cfCaseDayDataRepository.hospitalGbDateKey(vvhospitalCode, vhospitalCode, startTime, endTime);
    }

    @Override
    public List<OrgDataStatVO> listHospDataGroupByOrg(BdCrmDataStatParam crmDataStatParam) {
        if (crmDataStatParam == null) {
            return Lists.newArrayList();
        }
        return cfCaseDayDataRepository.listHospDataGroupByOrg(crmDataStatParam);
    }

    //不要使用tidb查询
    @Override
    public List<CfCaseDayDataDO> getByCaseIdAndDayKey(List<Integer> caseIds, String dayKey) {
        if (CollectionUtils.isEmpty(caseIds) || StringUtils.isBlank(dayKey)) {
            return Lists.newArrayList();
        }
        List<CfCaseDayDataDO> result = Lists.newArrayList();
        Lists.partition(caseIds, 500)
                .forEach(item -> result.addAll(cfCaseDayDataDao.getByCaseIdAndDayKey(item, dayKey)));
        return result;
    }

    @Override
    public List<CfCaseDayDataDO> getByCaseIdAndDayKeys(Integer caseId, List<String> dayKeys) {
        if (CollectionUtils.isEmpty(dayKeys) || Objects.isNull(caseId)) {
            return Lists.newArrayList();
        }
        return cfCaseDayDataDao.getByCaseIdAndDayKeys(caseId, dayKeys);
    }
}
