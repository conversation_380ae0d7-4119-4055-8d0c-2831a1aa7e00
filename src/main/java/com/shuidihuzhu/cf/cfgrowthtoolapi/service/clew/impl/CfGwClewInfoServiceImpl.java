package com.shuidihuzhu.cf.cfgrowthtoolapi.service.clew.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.service.clew.ICfGwClewInfoService;
import com.shuidihuzhu.cf.dao.clew.CfGwClewInfoDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-09-03
 */

@Service
@Slf4j
public class CfGwClewInfoServiceImpl implements ICfGwClewInfoService {

    @Autowired
    private CfGwClewInfoDao cfGwClewInfoDao;

    @Override
    public List<Long> listGwInfoByClewIds(List<Long> clewIds) {
        return cfGwClewInfoDao.listGwInfoByClewIds(clewIds);
    }

    @Override
    public Date getClewAssignTimeByClewId(List<Long> clewIds){
        return cfGwClewInfoDao.getClewAssignTimeByClewId(clewIds);
    }
}
