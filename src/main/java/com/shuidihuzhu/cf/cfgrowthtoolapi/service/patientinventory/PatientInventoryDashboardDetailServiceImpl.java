package com.shuidihuzhu.cf.cfgrowthtoolapi.service.patientinventory;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.param.DailyEntryDetailParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.param.DailyInventoryDetailParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientinventory.param.DailyVisitDetailParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WashBaseParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.dao.patientinventory.*;
import com.shuidihuzhu.cf.dao.tdsql.CfBdFollowPatientsSumTdsql;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.util.DateUtil;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class PatientInventoryDashboardDetailServiceImpl implements PatientInventoryDashboardDetailService {

    @Resource
    private PatientInventoryDashboardDetailDao patientInventoryDashboardDetailDao;

    @Resource
    private PatientInventoryPatientFollowUpRecordDao patientInventoryPatientFollowUpRecordDao;

    @Resource
    private PatientInventoryPatientInfoDao patientInventoryPatientInfoDao;

    @Resource
    private CfBdFollowPatientsSumTdsql cfBdFollowPatientsSumTdsql;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Resource
    private PatientInventoryDepartmentsBedDao patientInventoryDepartmentsBedDao;

    @Resource
    private PatientInventoryDepartmentsDao patientInventoryDepartmentsDao;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private PatientInventoryBedPatientRecordDao patientInventoryBedPatientRecordDao;

    @Autowired
    private ApolloService apolloService;


    /**
     * 保存记录
     *
     * @param patientInventoryPatientFollowUpRecordDo 患者跟进记录
     */
    @Override
    public void saveDashboardData(PatientInventoryPatientFollowUpRecordDo patientInventoryPatientFollowUpRecordDo) {
        if (patientInventoryPatientFollowUpRecordDo == null) {
            log.error("保存患者数据失败，参数为空");
            return;
        }
        //高潜不记录
        if (patientInventoryPatientFollowUpRecordDo.getDepartmentType() == PatientInventoryDepartmentTypeEnum.HIGH_POTENTIAL_DEPARTMENT.getCode()) {
            return;
        }
        if (!apolloService.isCanGeneratePatientInventoryDetail()) {
            return;
        }

        try {
            // 获取患者信息，获取科室ID和床位ID
            PatientInventoryPatientInfoDo patientInfoDo = patientInventoryPatientInfoDao.getById(patientInventoryPatientFollowUpRecordDo.getPatientInfoId());
            if (patientInfoDo.getBedId() <= 0) {
                //出院了，查询数据
                List<PatientInventoryBedPatientRecordDo> recordDos = patientInventoryBedPatientRecordDao.listByPatientInfoIdsAndType(Lists.newArrayList(patientInfoDo.getId())
                        , PatientInventoryPatientType.discharge_from_hospital.getCode());
                if (CollectionUtils.isEmpty(recordDos)) {
                    log.info("数据异常patientInventoryPatientFollowUpRecordDo：{}", JSON.toJSONString(recordDos));
                    return;
                }
                patientInfoDo.setBedId(recordDos.get(0).getBedId());
            }
            //查询科室信息
            PatientInventoryDepartmentsDo departmentsDo = patientInventoryDepartmentsDao.getByDepartmentsId(patientInfoDo.getDepartmentsId());
            if (departmentsDo == null || StringUtils.isEmpty(departmentsDo.getBindingUniqueCode())) {
                log.error("未查询到科室绑定信息departmentsDo：{}", JSON.toJSONString(departmentsDo));
                return;
            }
            CrowdfundingVolunteer cfVolunteer = cfVolunteerService.getByUniqueCode(departmentsDo.getBindingUniqueCode());


            // 处理录入数据：首次跟进时记录新增录入信息，今日首次录入时会增加NEW_ENTRY类型的记录
            handleEntryData(patientInventoryPatientFollowUpRecordDo, cfVolunteer, patientInfoDo);

            // 处理拜访数据：根据意向处理拜访数据，设置拜访需求和完成记录
            // 1. 对于首次跟进，会根据意向类型创建需拜访记录
            // 2. 对于后续跟进，会处理上次拜访的完成状态，并根据新的意向创建需拜访记录
            handleVisitData(patientInventoryPatientFollowUpRecordDo, cfVolunteer.getUniqueCode(), patientInfoDo);

            // 处理盘点数据：根据患者标签、意向决定是否需要盘点
            // 1. 首次录入时，可能会产生需盘点数据
            // 2. 后续跟进时（盘点数据不为空），会处理之前盘点的完成状态，并可能创建新的盘点需求
            handleInventoryData(patientInventoryPatientFollowUpRecordDo, cfVolunteer.getUniqueCode(), patientInfoDo);

        } catch (Exception e) {
            log.error("保存患者数据异常", e);
        }
    }

    /**
     * 处理录入数据
     */
    private void handleEntryData(PatientInventoryPatientFollowUpRecordDo recordDo,
                                 CrowdfundingVolunteer cfVolunteer, PatientInventoryPatientInfoDo patientInfoDo) {
        List<PatientInventoryDashboardDetail> details = new ArrayList<>();
        if (PatientInventoryPatientFollowUpRecordDo.Type.FIRST_FOLLOW_UP.getCode() == recordDo.getType()) {
            //兼容历史数据清洗逻辑，正常数据createTime都是null
            String dateKey = DateUtil.formatDate(recordDo.getCreateTime() != null ? recordDo.getCreateTime() : new Date());
            PatientInventoryDashboardDetail detail = new PatientInventoryDashboardDetail();
            detail.setDateKey(dateKey);
            detail.setDataType(PatientInventoryDataType.NEW_ENTRY.getCode());
            detail.setBindingUniqueCode(cfVolunteer.getUniqueCode());
            detail.setResultType(PatientInventoryDataResultType.INIT.getCode());
            detail.setPatientInfoId(recordDo.getPatientInfoId());
            detail.setBatchUid(UUID.randomUUID().toString().replace("-", ""));
            detail.setCanOverdue(0);
            detail.setResultId(0L);

            // 设置科室ID和床位ID
            if (patientInfoDo != null) {
                detail.setDepartmentsId(Long.valueOf(patientInfoDo.getDepartmentsId()));
                detail.setBedId(patientInfoDo.getBedId());
            }

            details.add(detail);
        }
        //保存数据
        if (CollectionUtils.isNotEmpty(details)) {
            patientInventoryDashboardDetailDao.batchInsert(details);
        }
    }

    /**
     * 处理拜访数据
     */
    private void handleVisitData(PatientInventoryPatientFollowUpRecordDo recordDo,
                                 String volunteerUniqueCode, PatientInventoryPatientInfoDo patientInfoDo) {
        boolean isFirstRecord = PatientInventoryPatientFollowUpRecordDo.Type.FIRST_FOLLOW_UP.getCode() == recordDo.getType();
        Integer firstIntentionType = recordDo.getFirstIntention();
        Integer secondIntentionType = recordDo.getSecondIntention();


        if (isFirstRecord) {
            handleFirstVisitData(recordDo, volunteerUniqueCode, firstIntentionType, secondIntentionType, patientInfoDo);
        } else {
            handleFollowUpVisitData(recordDo, volunteerUniqueCode, firstIntentionType, patientInfoDo);
        }
    }

    /**
     * 处理盘点数据
     */
    private void handleInventoryData(PatientInventoryPatientFollowUpRecordDo recordDo,
                                     String volunteerUniqueCode, PatientInventoryPatientInfoDo patientInfoDo) {

        boolean isFirstRecord = PatientInventoryPatientFollowUpRecordDo.Type.FIRST_FOLLOW_UP.getCode() == recordDo.getType();
        String leaderUniqueCode = getLeaderUniqueCode(volunteerUniqueCode);

        if (isFirstRecord) {
            //首次录入时，可能会产生需盘点数据
            handleFirstInventoryData(recordDo, leaderUniqueCode, patientInfoDo);
        } else {
            //若是盘点数据，顾问uniqueCode直接取操作者，若是顾问跟进数据，取操作者上级（可能产生需盘点数据）
            handleFollowUpInventoryData(recordDo, StringUtils.isNotEmpty(recordDo.getInventoryRecord()) ? leaderUniqueCode : volunteerUniqueCode, patientInfoDo);
        }
    }

    /**
     * 根据患者标签判断是否需要盘点
     * <p>
     * 这个方法通过分析患者的标签组合，判断患者是否需要进行盘点。
     * 盘点规则主要有以下几种组合情况：
     * 1. 首次住院 + 家庭情况差
     * 2. 已入仓需移植 + 已加微信
     * 3. 特殊身份
     * 4. 未手术 + 预计花费超过5万 + 家庭情况差
     * 5. 术后继续治疗 + 预计花费超过5万 + 已加微信
     *
     * @param labels 患者标签字符串，以逗号分隔的整数标签值
     * @return 是否需要盘点，true表示需要盘点，false表示不需要盘点
     */
    @NotNull
    private static Boolean getNeedInventory(String labels) {
        Set<Integer> labelSet = new HashSet<>();
        // 解析标签字符串，将标签值添加到集合中
        if (StringUtils.isNotEmpty(labels)) {
            labelSet.addAll(Arrays.stream(labels.split(",")).map(Integer::valueOf).collect(Collectors.toList()));
        }

        // 默认不需要盘点
        Boolean needInventory = false;

        // 根据不同的标签组合判断是否需要盘点
        if (CollectionUtils.isNotEmpty(labelSet)) {
            // 规则1: 首次住院 + 家庭情况差
            if (labelSet.contains(DepartmentsLabelEnum.first_hospitalization.getCode()) &&
                    labelSet.contains(DepartmentsLabelEnum.poor_family_situation.getCode()) &&
                    labelSet.contains(DepartmentsLabelEnum.added_wechat.getCode())) {
                needInventory = true;
            }
            // 规则2: 已入仓需移植 + 已加微信
            else if (labelSet.contains(DepartmentsLabelEnum.transplant_in_warehouse.getCode()) &&
                    labelSet.contains(DepartmentsLabelEnum.added_wechat.getCode())) {
                needInventory = true;
            }
            // 规则3: 特殊身份
            else if (labelSet.contains(DepartmentsLabelEnum.special_identity.getCode())) {
                needInventory = true;
            }
            // 规则4: 未手术 + 预计花费超过5万 + 家庭情况差
            else if (labelSet.contains(DepartmentsLabelEnum.not_operated.getCode()) &&
                    labelSet.contains(DepartmentsLabelEnum.to_spend_more_than_5w.getCode()) &&
                    labelSet.contains(DepartmentsLabelEnum.poor_family_situation.getCode())
            ) {
                needInventory = true;
            }
            // 规则5: 术后继续治疗 + 预计花费超过5万 + 已加微信
            else if (labelSet.contains(DepartmentsLabelEnum.postoperative_continued_treatment.getCode()) &&
                    labelSet.contains(DepartmentsLabelEnum.to_spend_more_than_5w.getCode()) &&
                    labelSet.contains(DepartmentsLabelEnum.added_wechat.getCode())) {
                needInventory = true;
            }
        }
        return needInventory;
    }

    @Override
    public List<PatientInventoryDashboardDetail> listAllNoResultData(long id, Integer limit, String limitTime) {
        if (limitTime == null) {
            return null;
        }
        return patientInventoryDashboardDetailDao.listAllNoResultData(id, limit, limitTime);
    }

    @Override
    public Long getMaxIdOfNoResultData(String limitTime) {
        return patientInventoryDashboardDetailDao.getMaxIdOfNoResultData(limitTime);
    }

    @Override
    public Integer insert(PatientInventoryDashboardDetail detail) {
        if (detail == null) {
            return 0;
        }
        return patientInventoryDashboardDetailDao.insertSelective(detail);
    }

    @Override
    public Integer overDueDataByIds(List<Long> ids, Long resultId) {
        if (CollectionUtils.isEmpty(ids) || resultId == null) {
            return 0;
        }
        return patientInventoryDashboardDetailDao.updateResultType(ids, resultId, PatientInventoryDataResultType.OVERDUE_NOT_COMPLETED.getCode());
    }

    //获取当前组织的概览数据
    @Override
    public DepartmentDashboardOverviewVO getDepartmentDashboardOverview(Long orgId, CrowdfundingVolunteer cfVolunteer) {
        String dateKey = DateUtil.getCurrentDateStr();
        DepartmentDashboardOverviewVO vo = null;
        if (cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel()
                || cfVolunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_COMMON.getLevel()) {
            //查询本人数据
            vo = cfBdFollowPatientsSumTdsql.selectByDateKeyAndVolunteerCode(dateKey, cfVolunteer.getUniqueCode());
        } else {
            //查询一个组织下的所有数据
            List<BdCrmOrganizationDO> organizationDOList = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(orgId);
            List<Integer> orgIds = organizationDOList.stream().map(item -> Long.valueOf(item.getId()).intValue()).collect(Collectors.toList());
            vo = cfBdFollowPatientsSumTdsql.selectByDateKeyAndOrgIds(dateKey, orgIds);
        }
        return vo;
    }

    /**
     * 查询当前用户及其下级组织，是否绑定科室
     *
     * @param orgIds
     * @return
     */
    @Override
    public BindDepartmentVO getBindDepartment(String uniqueCode, List<Long> orgIds) {
        //查询一个组织下的所有数据
        BindDepartmentVO vo = new BindDepartmentVO();
        Boolean hasBind = false;
        Boolean onlyHighPotentialDepartment = null;
        List<String> uniqueCodeList = new ArrayList<>();
        uniqueCodeList.add(uniqueCode);
        if (CollectionUtils.isNotEmpty(orgIds)) {

            for (Long orgId : orgIds) {
                List<BdCrmOrganizationDO> organizationDOList = crmSelfBuiltOrgReadService.listAllSubOrgExcludeSelf(orgId);
                List<Long> subOrgIds = organizationDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
                List<BdCrmOrgUserRelationDO> relationDOS = crmOrganizationRelationService.listByOrgIds(subOrgIds);
                uniqueCodeList.addAll(relationDOS.stream().map(item -> item.getUniqueCode()).collect(Collectors.toList()));
            }
        }
        List<PatientInventoryDepartmentsDo> departmentsDos = patientInventoryDepartmentsDao.listByBindingUniqueCodes(uniqueCodeList);
        if (CollectionUtils.isNotEmpty(departmentsDos)) {
            hasBind = true;
            Long normalDepartmentCount = departmentsDos.stream().filter(item ->
                    PatientInventoryDepartmentTypeEnum.NORMAL_INVENTORY_DEPARTMENT.getCode() == item.getDepartmentType()).count();
            //有普通科室
            if (normalDepartmentCount != null && normalDepartmentCount > 0) {
                onlyHighPotentialDepartment = false;
            } else {
                onlyHighPotentialDepartment = true;
            }
        }
        vo.setHasBind(hasBind);
        vo.setOnlyHighPotentialDepartment(onlyHighPotentialDepartment);
        return vo;
    }

    /**
     * 获取录入详情
     *
     * @param param
     * @return
     */
    @Override
    public DailyEntryDetailVO getDailyEntryDetail(DailyEntryDetailParam param) {
        String methodDesc = "getDailyEntryDetail|";
        String datekey = param.getDate();
        //判断查询数据维度
        String dimensionType = null;
        CrowdfundingVolunteer volunteer = param.getCrowdfundingVolunteer();
        List<BdCrmOrganizationDO> directSubOrgByOrg = null;
        if (volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel()
                || volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_COMMON.getLevel()) {
            dimensionType = PatientInventoryDashboardDimensionEnum.DEPARTMENT.getValue();
            param.setUniqueCode(volunteer.getUniqueCode());
        } else if (volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel()
                || volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_BOSS.getLevel()
                || volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_PROVINCE.getLevel()) {
            //业务经理/筹款合作团队/合作团队组长
            if (PatientInventoryQueryTypeEnum.HOSPITAL.getCode() == param.getQueryType()) {
                dimensionType = PatientInventoryDashboardDimensionEnum.HOSPITAL.getValue();
            } else if (PatientInventoryQueryTypeEnum.CONSULTANT.getCode() == param.getQueryType()) {
                dimensionType = PatientInventoryDashboardDimensionEnum.CONSULTANT.getValue();
            }
        } else {
            //再往上管理层，需要区分是查顾问数据，还是组织数据
            if (StringUtils.isNotBlank(param.getUniqueCode()) || StringUtils.isNotBlank(param.getVhospitalCode())) {
                dimensionType = PatientInventoryDashboardDimensionEnum.DEPARTMENT.getValue();
            } else {
                directSubOrgByOrg = crmSelfBuiltOrgReadService.findDirectSubOrgByOrgId(param.getOrgId());
                if (CollectionUtils.isNotEmpty(directSubOrgByOrg)) {
                    dimensionType = PatientInventoryDashboardDimensionEnum.ORGANIZATION.getValue();
                } else {
                    if (PatientInventoryQueryTypeEnum.HOSPITAL.getCode() == param.getQueryType()) {
                        dimensionType = PatientInventoryDashboardDimensionEnum.HOSPITAL.getValue();
                    } else if (PatientInventoryQueryTypeEnum.CONSULTANT.getCode() == param.getQueryType()) {
                        dimensionType = PatientInventoryDashboardDimensionEnum.CONSULTANT.getValue();
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(param.getUniqueCode()) || StringUtils.isNotBlank(param.getVhospitalCode())) {
            //查询某个顾问数据
            dimensionType = PatientInventoryDashboardDimensionEnum.DEPARTMENT.getValue();
        }
        //根据维度处理不同数据
        if (dimensionType == null) {
            log.info(methodDesc + "未匹配到查询维度param：{}", JSON.toJSONString(param));
            return null;
        }
        DailyEntryDetailVO detailVO = new DailyEntryDetailVO();
        detailVO.setDimensionType(dimensionType);
        List<DailyEntryDetailInfo> detailItems = null;
        //查询子组织id
        List<Long> subOrgIds = new ArrayList<>();
        if (param.getOrgId() != null) {
            subOrgIds = crmSelfBuiltOrgReadService.listAllSubOrgExcludeSelf(param.getOrgId()).stream()
                    .map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subOrgIds)) {
                subOrgIds.add(param.getOrgId());
            }
        }

        if (PatientInventoryDashboardDimensionEnum.CONSULTANT.getValue().equals(dimensionType)) {
            //查询某个组织下顾问的数据
            detailItems = cfBdFollowPatientsSumTdsql.selectConsultantEntryByOrgIds(datekey, subOrgIds);
        } else if (PatientInventoryDashboardDimensionEnum.HOSPITAL.getValue().equals(dimensionType)) {
            detailItems = cfBdFollowPatientsSumTdsql.selectHospitalEntryByOrgIds(datekey, subOrgIds);
        } else if (PatientInventoryDashboardDimensionEnum.DEPARTMENT.getValue().equals(dimensionType)) {
            detailItems = cfBdFollowPatientsSumTdsql.selectDepartmentEntryByVolunteerCodeOrHospital(datekey, param.getUniqueCode(), param.getVhospitalCode());
        } else if (PatientInventoryDashboardDimensionEnum.ORGANIZATION.getValue().equals(dimensionType)) {
            //组织的聚合数据
            detailItems = new ArrayList<>();
            for (BdCrmOrganizationDO org : directSubOrgByOrg) {
                List<BdCrmOrganizationDO> organizationDOList = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(org.getId());
                List<Long> orgIds = organizationDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
                DailyEntryDetailInfo detailItem = cfBdFollowPatientsSumTdsql.selectOrgEntryByOrgIds(datekey, orgIds);
                if (detailItem == null) {
                    continue;
                }
                detailItem.setOrgName(org.getOrgName());
                detailItem.setOrgId(org.getId());
                detailItem.setLastLevel(org.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode());
                detailItems.add(detailItem);
            }
        }
        detailVO.setTotalCount(detailItems.size());
        detailVO.setDetailList(detailItems);
        return detailVO;
    }

    /**
     * 获取今日拜访数据明细
     *
     * @param param
     * @return
     */
    @Override
    public DailyVisitDetailVO getDailyVisitDetail(DailyVisitDetailParam param) {
        String methodDesc = "getDailyVisitDetail|";
        String datekey = param.getDate();
        //判断查询数据维度
        String dimensionType = null;
        CrowdfundingVolunteer volunteer = param.getCrowdfundingVolunteer();
        List<BdCrmOrganizationDO> directSubOrgByOrg = null;
        String limitTime = DateUtil.formatDate(DateUtil.addMonth(new Date(), -1));

        if (volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel()
                || volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_COMMON.getLevel()) {
            dimensionType = PatientInventoryDashboardDimensionEnum.PATIENT.getValue();
            param.setUniqueCode(volunteer.getUniqueCode());
        } else if (volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel()
                || volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_BOSS.getLevel()
                || volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_PROVINCE.getLevel()) {
            //业务经理/筹款合作团队/合作团队组长
            if (PatientInventoryQueryTypeEnum.HOSPITAL.getCode() == param.getQueryType()) {
                dimensionType = PatientInventoryDashboardDimensionEnum.HOSPITAL.getValue();
            } else if (PatientInventoryQueryTypeEnum.CONSULTANT.getCode() == param.getQueryType()) {
                dimensionType = PatientInventoryDashboardDimensionEnum.CONSULTANT.getValue();
            }
        } else {
            //再往上管理层，需要区分是查顾问数据，还是组织数据 查看详情数据
            if (StringUtils.isNotBlank(param.getUniqueCode()) || StringUtils.isNotBlank(param.getVhospitalCode())) {
                dimensionType = PatientInventoryDashboardDimensionEnum.PATIENT.getValue();
            } else {
                directSubOrgByOrg = crmSelfBuiltOrgReadService.findDirectSubOrgByOrgId(param.getOrgId());
                if (CollectionUtils.isNotEmpty(directSubOrgByOrg)) {
                    dimensionType = PatientInventoryDashboardDimensionEnum.ORGANIZATION.getValue();
                } else {
                    if (PatientInventoryQueryTypeEnum.CONSULTANT.getCode() == param.getQueryType()) {
                        dimensionType = PatientInventoryDashboardDimensionEnum.CONSULTANT.getValue();
                    } else if (PatientInventoryQueryTypeEnum.HOSPITAL.getCode() == param.getQueryType()) {
                        dimensionType = PatientInventoryDashboardDimensionEnum.HOSPITAL.getValue();

                    }
                }
            }

        }
        if (StringUtils.isNotBlank(param.getUniqueCode()) || StringUtils.isNotBlank(param.getVhospitalCode())) {
            //查询某个顾问数据
            dimensionType = PatientInventoryDashboardDimensionEnum.PATIENT.getValue();
        }
        //根据维度处理不同数据
        if (dimensionType == null) {
            log.info(methodDesc + "未匹配到查询维度param：{}", JSON.toJSONString(param));
            return null;
        }
        DailyVisitDetailVO detailVO = new DailyVisitDetailVO();
        detailVO.setDimensionType(dimensionType);
        List<DailyVisitDetailInfo> detailItems = null;
        //查询子组织id
        List<Long> subOrgIds = new ArrayList<>();
        if (param.getOrgId() != null) {
            subOrgIds = crmSelfBuiltOrgReadService.listAllSubOrgExcludeSelf(param.getOrgId()).stream()
                    .map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subOrgIds)) {
                subOrgIds.add(param.getOrgId());
            }
        }
        if (PatientInventoryDashboardDimensionEnum.PATIENT.getValue().equals(dimensionType)) {
            detailItems = new ArrayList<>();
            //获取患者明细数据
            if (PatientInventoryQueryTypeEnum.TODAY.getCode() == param.getQueryType()) {
                //查询
                if (StringUtils.isNotEmpty(param.getUniqueCode())) {
                    detailItems = getDailyVisitDetailByUniqueCode(param.getDate(), param.getUniqueCode(), PatientInventoryDataType.NEW_VISIT_REQUIRED.getCode());
                } else if (StringUtils.isNotEmpty(param.getVhospitalCode())) {
                    detailItems = getDailyVisitDetailByVhospitalCode(param.getDate(), param.getVhospitalCode(), PatientInventoryDataType.NEW_VISIT_REQUIRED.getCode());
                }
            } else if (PatientInventoryQueryTypeEnum.OVERDUE.getCode() == param.getQueryType()) {
                List<PatientInventoryDashboardDetail> detailList = new ArrayList<>();
                String limitDate = DateUtil.formatDate(DateUtil.addDay(new Date(), -15));
                if (StringUtils.isNotEmpty(param.getUniqueCode())) {
                    List<PatientInventoryDepartmentsDo> departmentsDos = patientInventoryDepartmentsDao.listByBindingUniqueCode(param.getUniqueCode());
                    if (CollectionUtils.isNotEmpty(departmentsDos)) {
                        List<Integer> departmentsIds = departmentsDos.stream().map(item -> item.getDepartmentsId()).collect(Collectors.toList());
                        detailList = patientInventoryDashboardDetailDao.selectByDepartmentsIdsAndResultType(
                                departmentsIds, PatientInventoryDataType.NEW_VISIT_REQUIRED.getCode(), PatientInventoryDataResultType.OVERDUE_NOT_COMPLETED.getCode(),limitDate);
                    }
                } else if (StringUtils.isNotEmpty(param.getVhospitalCode())) {
                    List<PatientInventoryDepartmentsBedDo> bedDoList = patientInventoryDepartmentsBedDao.listByHospitalCode(param.getVhospitalCode());
                    if (CollectionUtils.isNotEmpty(bedDoList)) {
                        List<Integer> departmentsIds = bedDoList.stream().map(item -> item.getDepartmentsId()).distinct().collect(Collectors.toList());
                        //查询患者
                        detailList = patientInventoryDashboardDetailDao.selectByDepartmentsIdsAndResultType(departmentsIds,
                                PatientInventoryDataType.NEW_VISIT_REQUIRED.getCode(), PatientInventoryDataResultType.OVERDUE_NOT_COMPLETED.getCode(),limitDate);
                    }
                }
                detailItems = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(detailList)) {
                    List<Long> patientIds = detailList.stream().map(item -> item.getPatientInfoId()).distinct().collect(Collectors.toList());
                    List<PatientInventoryPatientInfoDo> patientInfoDos = patientInventoryPatientInfoDao.listByIds(patientIds);
                    Map<Long, PatientInventoryPatientInfoDo> patientInfoDoMap = patientInfoDos.stream().collect(Collectors.toMap(PatientInventoryPatientInfoDo::getId, item -> item));
                    List<Long> bedIds = patientInfoDos.stream()
                            .map(PatientInventoryPatientInfoDo::getBedId)
                            .filter(item -> item > 0)
                            .distinct()
                            .collect(Collectors.toList());
                    //查看是否有出院的患者
                    Map<Long, Long> dischargeFromHospitalPatientBedIdMap = new HashMap<>();
                    if (bedIds.size() < patientInfoDos.size()) {
                        List<Long> patientIdList = patientInfoDos.stream().filter(item -> item.getBedId() == 0)
                                .map(item -> item.getId()).collect(Collectors.toList());
                        List<PatientInventoryBedPatientRecordDo> bedPatientRecordDos =
                                patientInventoryBedPatientRecordDao.listByPatientInfoIdsAndType(patientIdList, PatientInventoryPatientType.discharge_from_hospital.getCode());
                        bedIds.addAll(bedPatientRecordDos.stream().map(item -> item.getBedId()).distinct().collect(Collectors.toList()));
                        Map<Long, List<PatientInventoryBedPatientRecordDo>> recordMap = bedPatientRecordDos.stream().collect(Collectors.groupingBy(item -> item.getPatientInfoId()));
                        recordMap.forEach((k, v) -> {
                            dischargeFromHospitalPatientBedIdMap.put(k, v.get(0).getBedId());
                        });
                    }

                    List<PatientInventoryDepartmentsBedDo> bedDoList = patientInventoryDepartmentsBedDao.listByIds(bedIds);
                    Map<Long, PatientInventoryDepartmentsBedDo> departmentsBedDoMap = bedDoList.stream().collect(Collectors.toMap(PatientInventoryDepartmentsBedDo::getId, item -> item));
                    Map<Long, List<PatientInventoryDashboardDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(PatientInventoryDashboardDetail::getPatientInfoId));
                    for (Map.Entry<Long, List<PatientInventoryDashboardDetail>> entry : detailMap.entrySet()) {
                        List<PatientInventoryDashboardDetail> batchDetailList = entry.getValue();
                        //取患者数据
                        PatientInventoryPatientInfoDo patientInfoDo = patientInfoDoMap.get(batchDetailList.get(0).getPatientInfoId());
                        Long bedId = patientInfoDo.getBedId();
                        if (bedId == 0) {
                            bedId = dischargeFromHospitalPatientBedIdMap.get(patientInfoDo.getId());
                        }
                        PatientInventoryDepartmentsBedDo bedDo = departmentsBedDoMap.get(bedId);
                        if (bedDo == null) {
                            continue;
                        }
                        DailyVisitDetailInfo detailItem = new DailyVisitDetailInfo();
                        detailItem.setPatientName(patientInfoDo.getPatientName());
                        detailItem.setVhospitalCode(bedDo.getVhospitalCode());
                        detailItem.setVhospitalName(bedDo.getHospitalName());
                        detailItem.setDepartmentId(bedDo.getDepartmentsId());
                        detailItem.setDepartmentName(bedDo.getDepartmentsName());
                        detailItem.setBedNumber(bedDo.getBedName());
                        //多个时间使用、拼接
                        detailItem.setVisitTime(batchDetailList.stream().map(item -> item.getDateKey()).collect(Collectors.toSet()));
                        detailItems.add(detailItem);
                    }
                }
            }
        } else if (PatientInventoryDashboardDimensionEnum.HOSPITAL.getValue().equals(dimensionType)) {
            detailItems = cfBdFollowPatientsSumTdsql.selectHospitalVisitByOrgIds(datekey, subOrgIds);
        } else if (PatientInventoryDashboardDimensionEnum.ORGANIZATION.getValue().equals(dimensionType)) {
            //组织的聚合数据
            detailItems = new ArrayList<>();
            for (BdCrmOrganizationDO org : directSubOrgByOrg) {
                List<BdCrmOrganizationDO> organizationDOList = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(org.getId());
                List<Long> orgIds = organizationDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
                DailyVisitDetailInfo detailItem = cfBdFollowPatientsSumTdsql.selectOrgVisitByOrgIds(datekey, orgIds);
                if (detailItem == null) {
                    continue;
                }
                detailItem.setOrgName(org.getOrgName());
                detailItem.setOrgId(org.getId());
                detailItem.setLastLevel(org.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode());

                detailItems.add(detailItem);
            }
        } else if (PatientInventoryDashboardDimensionEnum.CONSULTANT.getValue().equals(dimensionType)) {
            detailItems = cfBdFollowPatientsSumTdsql.selectConsultantVisitByOrgIds(datekey, subOrgIds);
        }
        detailVO.setTotalCount(detailItems.size());
        detailVO.setDetailList(detailItems);
        return detailVO;
    }

    /**
     * 获取今日需拜访患者明细
     *
     * @param dateKey
     * @param uniqueCode
     * @param dataType
     * @return
     */
    private List<DailyVisitDetailInfo> getDailyVisitDetailByUniqueCode(String dateKey, String uniqueCode,
                                                                       int dataType) {
        String methodName = "getDailyVisitDetailByUniqueCode|";
        log.info(methodName + "dateKey:{},uniqueCode:{},dataType:{}", dateKey, uniqueCode, dataType);
        List<DailyVisitDetailInfo> detailItems = new ArrayList<>();
        List<PatientInventoryDepartmentsDo> departmentsDos = patientInventoryDepartmentsDao.listByBindingUniqueCode(uniqueCode);
        if (CollectionUtils.isEmpty(departmentsDos)) {
            return detailItems;
        }
        List<Integer> departmentsIds = departmentsDos.stream().map(item -> item.getDepartmentsId()).collect(Collectors.toList());
        List<PatientInventoryDashboardDetail> detailList = patientInventoryDashboardDetailDao.selectByDepartmentsIdsAndType(dateKey,
                departmentsIds, PatientInventoryDataType.NEW_VISIT_REQUIRED.getCode());
        if (CollectionUtils.isEmpty(detailList)) {
            return detailItems;
        }
        List<Long> patientIds = detailList.stream().map(item -> item.getPatientInfoId()).distinct().collect(Collectors.toList());
        List<PatientInventoryPatientInfoDo> patientInfoDos = patientInventoryPatientInfoDao.listByIds(patientIds);
        Map<Long, PatientInventoryPatientInfoDo> patientInfoDoMap = patientInfoDos.stream().collect(Collectors.toMap(PatientInventoryPatientInfoDo::getId, item -> item));
        //获取床位信息
        List<Long> bedIds = patientInfoDos.stream()
                .map(PatientInventoryPatientInfoDo::getBedId)
                .filter(item -> item > 0)
                .distinct()
                .collect(Collectors.toList());
        //查看是否有出院的患者
        Map<Long, Long> dischargeFromHospitalPatientBedIdMap = new HashMap<>();
        if (bedIds.size() < patientInfoDos.size()) {
            List<Long> patientIdList = patientInfoDos.stream().filter(item -> item.getBedId() == 0)
                    .map(item -> item.getId()).collect(Collectors.toList());
            List<PatientInventoryBedPatientRecordDo> bedPatientRecordDos =
                    patientInventoryBedPatientRecordDao.listByPatientInfoIdsAndType(patientIdList, PatientInventoryPatientType.discharge_from_hospital.getCode());
            bedIds.addAll(bedPatientRecordDos.stream().map(item -> item.getBedId()).distinct().collect(Collectors.toList()));
            Map<Long, List<PatientInventoryBedPatientRecordDo>> recordMap = bedPatientRecordDos.stream().collect(Collectors.groupingBy(item -> item.getPatientInfoId()));
            recordMap.forEach((k, v) -> {
                dischargeFromHospitalPatientBedIdMap.put(k, v.get(0).getBedId());
            });
        }

        List<PatientInventoryDepartmentsBedDo> bedDoList = patientInventoryDepartmentsBedDao.listByIds(bedIds);
        Map<Long, PatientInventoryDepartmentsBedDo> departmentsBedDoMap = bedDoList.stream().collect(Collectors.toMap(PatientInventoryDepartmentsBedDo::getId, item -> item));
        //需要根据患者-拜访时间去个重
        Set<String> resultSet = new HashSet<>();
        for (PatientInventoryDashboardDetail dashboardDetail : detailList) {
            DailyVisitDetailInfo detailItem = new DailyVisitDetailInfo();
            //组装数据
            PatientInventoryPatientInfoDo patientInfoDo = patientInfoDoMap.get(dashboardDetail.getPatientInfoId());

            Long bedId = patientInfoDo.getBedId();
            if (bedId == 0) {
                bedId = dischargeFromHospitalPatientBedIdMap.get(patientInfoDo.getId());
            }
            PatientInventoryDepartmentsBedDo bedDo = departmentsBedDoMap.get(bedId);
            if (bedDo == null) {
                continue;
            }
            String key = dashboardDetail.getPatientInfoId() + dashboardDetail.getDateKey();
            if (resultSet.contains(key)) {
                continue;
            }
            resultSet.add(key);
            detailItem.setPatientName(patientInfoDo.getPatientName());
            detailItem.setVhospitalCode(bedDo.getVhospitalCode());
            detailItem.setVhospitalName(bedDo.getHospitalName());
            detailItem.setDepartmentId(bedDo.getDepartmentsId());
            detailItem.setDepartmentName(bedDo.getDepartmentsName());
            detailItem.setBedNumber(bedDo.getBedName());
            if (PatientInventoryDataResultType.INIT.getCode() == dashboardDetail.getResultType()) {
                detailItem.setIsFollowUp(0);
            } else {
                detailItem.setIsFollowUp(1);
            }
            detailItems.add(detailItem);
        }
        detailItems.sort(Comparator.comparing(DailyVisitDetailInfo::getIsFollowUp));

        return detailItems;
    }

    private List<DailyVisitDetailInfo> getDailyVisitDetailByVhospitalCode(String dateKey, String vhospitalCode,
                                                                          int dataType) {
        String methodName = "getDailyVisitDetailByVhospitalCode|";
        log.info(methodName + "dateKey:{},vhospitalCode:{},dataType:{}", dateKey, vhospitalCode, dataType);
        List<DailyVisitDetailInfo> detailItems = new ArrayList<>();

        List<PatientInventoryDepartmentsBedDo> bedDoList = patientInventoryDepartmentsBedDao.listByHospitalCode(vhospitalCode);
        if (CollectionUtils.isEmpty(bedDoList)) {
            return detailItems;
        }
        List<Integer> departmentsIds = bedDoList.stream().map(item -> item.getDepartmentsId()).distinct().collect(Collectors.toList());

        List<PatientInventoryDashboardDetail> detailList = patientInventoryDashboardDetailDao.selectByDepartmentsIdsAndType(dateKey,
                departmentsIds, PatientInventoryDataType.NEW_VISIT_REQUIRED.getCode());
        if (CollectionUtils.isEmpty(detailList)) {
            return detailItems;
        }
        //查询患者
        List<PatientInventoryPatientInfoDo> allPatientInfoDos = patientInventoryPatientInfoDao.listByIds(detailList.stream().map(item -> item.getPatientInfoId()).distinct()
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(allPatientInfoDos)) {
            return detailItems;
        }
        List<Long> patientIds = detailList.stream().map(item -> item.getPatientInfoId()).distinct().collect(Collectors.toList());
        List<PatientInventoryPatientInfoDo> patientInfoDos = allPatientInfoDos.stream().filter(item -> patientIds.contains(item.getId())).collect(Collectors.toList());
        Map<Long, PatientInventoryPatientInfoDo> patientInfoDoMap = patientInfoDos.stream().collect(Collectors.toMap(PatientInventoryPatientInfoDo::getId, item -> item));
        Set<String> resultSet = new HashSet<>();

        Map<Long, PatientInventoryDepartmentsBedDo> departmentsBedDoMap = bedDoList.stream().collect(Collectors.toMap(PatientInventoryDepartmentsBedDo::getId, item -> item));
        for (PatientInventoryDashboardDetail dashboardDetail : detailList) {
            DailyVisitDetailInfo detailItem = new DailyVisitDetailInfo();
            //组装数据
            PatientInventoryPatientInfoDo patientInfoDo = patientInfoDoMap.get(dashboardDetail.getPatientInfoId());
            PatientInventoryDepartmentsBedDo bedDo = departmentsBedDoMap.get(dashboardDetail.getBedId());
            if (bedDo == null) {
                continue;
            }
            String key = dashboardDetail.getPatientInfoId() + dashboardDetail.getDateKey();
            if (resultSet.contains(key)) {
                continue;
            }
            resultSet.add(key);
            detailItem.setPatientName(patientInfoDo.getPatientName());
            detailItem.setVhospitalCode(bedDo.getVhospitalCode());
            detailItem.setVhospitalName(bedDo.getHospitalName());
            detailItem.setDepartmentId(bedDo.getDepartmentsId());
            detailItem.setDepartmentName(bedDo.getDepartmentsName());
            detailItem.setBedNumber(bedDo.getBedName());
            if (PatientInventoryDataResultType.INIT.getCode() == dashboardDetail.getResultType()) {
                detailItem.setIsFollowUp(0);
            } else {
                detailItem.setIsFollowUp(1);
            }
            detailItems.add(detailItem);
        }

        return detailItems;
    }

    /**
     * 获取今日盘点数据明细
     *
     * @param param
     * @return
     */
    @Override
    public DailyInventoryDetailVO getDailyInventoryDetail(DailyInventoryDetailParam param) {
        String methodDesc = "getDailyVisitDetail|";
        String datekey = param.getDate();
        //判断查询数据维度
        String dimensionType = null;
        CrowdfundingVolunteer volunteer = param.getCrowdfundingVolunteer();
        List<BdCrmOrganizationDO> directSubOrgByOrg = null;
        if (volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel()
                || volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_BOSS.getLevel()
                || volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_PROVINCE.getLevel()) {
            //业务经理/筹款合作团队/合作团队组长
            dimensionType = PatientInventoryDashboardDimensionEnum.PATIENT.getValue();
        } else {
            //再往上管理层，需要区分是查顾问数据，还是组织数据
            directSubOrgByOrg = crmSelfBuiltOrgReadService.findDirectSubOrgByOrgId(param.getOrgId());
            if (CollectionUtils.isNotEmpty(directSubOrgByOrg) && directSubOrgByOrg.get(0).getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.no_leaf.getCode()) {
                dimensionType = PatientInventoryDashboardDimensionEnum.ORGANIZATION.getValue();
            } else {
                dimensionType = PatientInventoryDashboardDimensionEnum.PATIENT.getValue();
            }
        }
        //根据维度处理不同数据
        if (dimensionType == null) {
            log.info(methodDesc + "未匹配到查询维度param：{}", JSON.toJSONString(param));
            return null;
        }
        DailyInventoryDetailVO detailVO = new DailyInventoryDetailVO();
        detailVO.setDimensionType(dimensionType);
        List<DailyInventoryDetailInfo> detailItems = null;
        if (PatientInventoryDashboardDimensionEnum.PATIENT.getValue().equals(dimensionType)) {
            detailItems = new ArrayList<>();
            //获取患者明细数据
            if (PatientInventoryQueryTypeEnum.TODAY.getCode() == param.getQueryType()) {
                detailItems = getInventoryDetail(param.getOrgId(), datekey, false);
            } else if (PatientInventoryQueryTypeEnum.OVERDUE.getCode() == param.getQueryType()) {
                detailItems = getInventoryDetail(param.getOrgId(), datekey, true);

            }
        } else if (PatientInventoryDashboardDimensionEnum.ORGANIZATION.getValue().equals(dimensionType)) {
            //组织的聚合数据
            detailItems = new ArrayList<>();
            //判断是否到达最后一层
            for (BdCrmOrganizationDO org : directSubOrgByOrg) {
                Boolean lastLevel = false;
                List<BdCrmOrganizationDO> subOrgByOrg = crmSelfBuiltOrgReadService.findDirectSubOrgByOrgId(org.getId());
                if (CollectionUtils.isEmpty(subOrgByOrg) || subOrgByOrg.get(0).getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode()) {
                    lastLevel = true;
                }
                List<BdCrmOrganizationDO> organizationDOList = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(org.getId());
                List<Long> orgIds = organizationDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
                DailyInventoryDetailInfo detailItem = cfBdFollowPatientsSumTdsql.selectOrgInventoryByOrgIds(datekey, orgIds);
                if (detailItem == null) {
                    continue;
                }
                detailItem.setOrgName(org.getOrgName());
                detailItem.setOrgId(org.getId());
                detailItem.setLastLevel(lastLevel);
                detailItems.add(detailItem);
            }
        }
        detailVO.setTotalCount(detailItems.size());
        detailVO.setDetailList(detailItems);
        return detailVO;
    }

    @Override
    public void washPatientInventoryDashboardDetail(WashBaseParam washBaseParam) {
        log.info("开始清洗dashboard数据，参数：{}", JSON.toJSONString(washBaseParam));
        if (washBaseParam == null || washBaseParam.getStartId() == null || washBaseParam.getEndId() == null || washBaseParam.getPageSize() == null) {
            log.error("清洗dashboard数据参数错误");
            return;
        }

        // 创建分页索引和大小
        Long startId = washBaseParam.getStartId();
        Long endId = washBaseParam.getEndId();
        Integer pageSize = washBaseParam.getPageSize();

        // 分页处理
        while (startId <= endId) {
            // 查询patient_inventory_patient_follow_up_record表中指定ID范围内的数据
            List<PatientInventoryPatientFollowUpRecordDo> recordList = patientInventoryPatientFollowUpRecordDao.selectByIdRange(startId, startId + pageSize - 1);
            if (CollectionUtils.isEmpty(recordList)) {
                startId += pageSize;
                continue;
            }
            log.info("查询到{}条记录，ID范围：[{}, {}]", recordList.size(), startId, startId + pageSize - 1);
            // 提取所有操作员唯一码
            Set<String> operatorUniqueCodes = recordList.stream()
                    .map(PatientInventoryPatientFollowUpRecordDo::getOperatorUniqueCode)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(operatorUniqueCodes)) {
                log.warn("没有顾问数据，跳过startId:{}", startId);
                startId += pageSize;
                continue;
            }

            // 从数据库批量查询CrowdfundingVolunteer
            List<CrowdfundingVolunteer> volunteerList = cfVolunteerService.getCfVolunteerDOByUniqueCodes(operatorUniqueCodes);

            // 创建Map存储查询结果，提高查询效率
            Map<String, CrowdfundingVolunteer> volunteerMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(volunteerList)) {
                volunteerMap = volunteerList.stream()
                        .collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, volunteer -> volunteer, (v1, v2) -> v1));
            }

            log.info("查询到{}个顾问信息", volunteerMap.size());

            // 遍历记录并计算数据
            for (PatientInventoryPatientFollowUpRecordDo record : recordList) {
                try {
                    // 获取操作员信息
                    String operatorUniqueCode = record.getOperatorUniqueCode();
                    if (StringUtils.isBlank(operatorUniqueCode)) {
                        log.warn("操作员唯一码为空，跳过处理，记录ID：{}", record.getId());
                        continue;
                    }
                    // 从Map中获取CrowdfundingVolunteer对象
                    CrowdfundingVolunteer cfVolunteer = volunteerMap.get(operatorUniqueCode);
                    if (cfVolunteer == null) {
                        log.warn("未找到对应的顾问对象，跳过处理，记录ID：{}，操作员唯一码：{}", record.getId(), operatorUniqueCode);
                        continue;
                    }
                    // 使用saveDashboardData方法计算数据
                    saveDashboardData(record);
                } catch (Exception e) {
                    log.error("处理记录异常，记录ID：{}", record.getId(), e);
                }
            }
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                log.error("线程休眠异常,startId:{}", startId, e);
                throw new RuntimeException(e);
            }
            startId += pageSize;
        }

        log.info("清洗dashboard数据完成");
    }

    /**
     * 获取盘点数据明细
     *
     * @param orgId
     * @param datekey
     * @return
     */
    private List<DailyInventoryDetailInfo> getInventoryDetail(Long orgId, String datekey, Boolean searchOverdueData) {
        String methodDesc = "getDailyVisitDetail|";
        //先查询组织下所有顾问，再查询顾问下所有患者
        List<DailyInventoryDetailInfo> detailItems = new ArrayList<>();
        String limitTime = DateUtil.formatDate(DateUtil.addMonth(new Date(), -1));
        List<BdCrmOrganizationDO> organizationDOList = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(orgId);
        List<Long> orgIds = organizationDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        //查询组织下所有顾问
        List<BdCrmOrgUserRelationDO> relationDOS = crmOrganizationRelationService.listByOrgIds(orgIds);

        if (CollectionUtils.isEmpty(relationDOS)) {
            log.info(methodDesc + "未查询到组织下顾问，orgId：{}", orgId);
            return detailItems;
        }
        //获取所有顾问的uniqueCode
        List<String> volunteerCodes = relationDOS.stream()
                .map(BdCrmOrgUserRelationDO::getUniqueCode)
                .collect(Collectors.toList());
        //通过顾问查询科室
        List<PatientInventoryDepartmentsDo> patientInventoryDepartmentsDos = patientInventoryDepartmentsDao.listByBindingUniqueCodes(volunteerCodes);
        if (CollectionUtils.isEmpty(patientInventoryDepartmentsDos)) {
            return detailItems;
        }
        List<Integer> departmentIds = patientInventoryDepartmentsDos.stream().map(item -> item.getDepartmentsId()).collect(Collectors.toList());

        List<PatientInventoryDashboardDetail> detailList = null;
        String limitDate = DateUtil.formatDate(DateUtil.addDay(new Date(), -15));

        if (searchOverdueData) {
            //查询过期数据
            detailList = patientInventoryDashboardDetailDao.selectByDepartmentsIdsAndResultType(
                    departmentIds,
                    PatientInventoryDataType.NEW_INVENTORY_REQUIRED.getCode(),
                    PatientInventoryDataResultType.OVERDUE_NOT_COMPLETED.getCode(),limitDate);

        } else {
            //查询今日需要盘点的数据
            detailList = patientInventoryDashboardDetailDao.selectByDepartmentsIdsAndType(
                    datekey,
                    departmentIds,
                    PatientInventoryDataType.NEW_INVENTORY_REQUIRED.getCode());
        }


        if (CollectionUtils.isEmpty(detailList)) {
            log.info(methodDesc + "未查询到今日需要盘点的数据，orgId：{}，datekey：{}", orgId, datekey);
            return detailItems;
        }
        //获取患者信息
        List<Long> patientIds = detailList.stream()
                .map(PatientInventoryDashboardDetail::getPatientInfoId)
                .distinct()
                .collect(Collectors.toList());
        List<PatientInventoryPatientInfoDo> patientInfoDos = patientInventoryPatientInfoDao.listByIds(patientIds);
        Map<Long, PatientInventoryPatientInfoDo> patientInfoDoMap = patientInfoDos.stream()
                .collect(Collectors.toMap(PatientInventoryPatientInfoDo::getId, item -> item));

        //获取床位信息
        List<Long> bedIds = patientInfoDos.stream()
                .map(PatientInventoryPatientInfoDo::getBedId)
                .filter(item -> item > 0)
                .distinct()
                .collect(Collectors.toList());
        //查看是否有出院的患者
        Map<Long, Long> dischargeFromHospitalPatientBedIdMap = new HashMap<>();
        if (bedIds.size() < patientInfoDos.size()) {
            List<Long> patientIdList = patientInfoDos.stream().filter(item -> item.getBedId() == 0)
                    .map(item -> item.getId()).collect(Collectors.toList());
            List<PatientInventoryBedPatientRecordDo> bedPatientRecordDos =
                    patientInventoryBedPatientRecordDao.listByPatientInfoIdsAndType(patientIdList, PatientInventoryPatientType.discharge_from_hospital.getCode());
            bedIds.addAll(bedPatientRecordDos.stream().map(item -> item.getBedId()).distinct().collect(Collectors.toList()));
            Map<Long, List<PatientInventoryBedPatientRecordDo>> recordMap = bedPatientRecordDos.stream().collect(Collectors.groupingBy(item -> item.getPatientInfoId()));
            recordMap.forEach((k, v) -> {
                dischargeFromHospitalPatientBedIdMap.put(k, v.get(0).getBedId());
            });
        }
        List<PatientInventoryDepartmentsBedDo> bedDoList = patientInventoryDepartmentsBedDao.listByIds(bedIds);
        Map<Long, PatientInventoryDepartmentsBedDo> departmentsBedDoMap = bedDoList.stream()
                .collect(Collectors.toMap(PatientInventoryDepartmentsBedDo::getId, item -> item));

        //根据科室号，获取科室责任顾问
        List<Integer> departmentsIds = bedDoList.stream().map(item -> item.getDepartmentsId()).distinct().collect(Collectors.toList());
        List<PatientInventoryDepartmentsDo> departmentsDos = patientInventoryDepartmentsDao.listByDepartmentsId(departmentsIds);
        Map<Integer, PatientInventoryDepartmentsDo> departmentsDoMap = departmentsDos.stream().collect(Collectors.toMap(PatientInventoryDepartmentsDo::getDepartmentsId, item -> item));

        //按批次号分组
        Map<Long, List<PatientInventoryDashboardDetail>> detailMap = detailList.stream()
                .collect(Collectors.groupingBy(PatientInventoryDashboardDetail::getPatientInfoId));

        //组装明细数据
        for (Map.Entry<Long, List<PatientInventoryDashboardDetail>> entry : detailMap.entrySet()) {
            List<PatientInventoryDashboardDetail> batchDetailList = entry.getValue();
            PatientInventoryPatientInfoDo patientInfoDo = patientInfoDoMap.get(batchDetailList.get(0).getPatientInfoId());
            Long bedId = patientInfoDo.getBedId();
            if (bedId == 0) {
                bedId = dischargeFromHospitalPatientBedIdMap.get(patientInfoDo.getId());
            }
            PatientInventoryDepartmentsBedDo bedDo = departmentsBedDoMap.get(bedId);
            if (bedDo == null) {
                continue;
            }
            PatientInventoryDepartmentsDo departmentsDo = departmentsDoMap.get(bedDo.getDepartmentsId());
            DailyInventoryDetailInfo detailItem = new DailyInventoryDetailInfo();
            detailItem.setPatientName(patientInfoDo.getPatientName());
            detailItem.setVhospitalCode(bedDo.getVhospitalCode());
            detailItem.setHospitalName(bedDo.getHospitalName());
            detailItem.setDepartmentId(bedDo.getDepartmentsId());
            detailItem.setDepartmentName(bedDo.getDepartmentsName());
            detailItem.setBedNumber(bedDo.getBedName());
            detailItem.setDutyUniqueCode(departmentsDo.getBindingUniqueCode());
            detailItem.setDutyVolunteerName(departmentsDo.getBindingMisName());
            if (PatientInventoryDataResultType.INIT.getCode() == batchDetailList.get(0).getResultType()) {
                detailItem.setIsInventoried(0);
            } else {
                detailItem.setIsInventoried(1);
            }
            if (searchOverdueData) {
                detailItem.setInventoryTime(batchDetailList.stream().map(item -> item.getDateKey()).collect(Collectors.toSet()));
            }
            detailItems.add(detailItem);
        }
        detailItems.sort(Comparator.comparing(DailyInventoryDetailInfo::getIsInventoried));
        return detailItems;
    }

    /**
     * 处理首次拜访数据
     */
    private void handleFirstVisitData(PatientInventoryPatientFollowUpRecordDo recordDo,
                                      String volunteerUniqueCode,
                                      Integer firstIntentionType,
                                      Integer secondIntentionType,
                                      PatientInventoryPatientInfoDo patientInfoDo) {
        if (PatientInventoryFirstIntentionType.NEED_FOLLOW_UP.getCode() != firstIntentionType) {
            return;
        }

        // 获取患者信息，获取科室ID和床位ID

        Date nextFollowUpTime = recordDo.getNextFollowUpTime();
        String batchUid = UUID.randomUUID().toString().replace("-", "");
        List<PatientInventoryDashboardDetail> details = new ArrayList<>();
        List<String> datekeys = new ArrayList<>();
        Date recordDate = recordDo.getCreateTime() != null ? recordDo.getCreateTime() : new Date();
        if (PatientInventoryFirstIntentionType.PatientInventorySecondIntentionType.NOT_CONTACTED.getCode() == secondIntentionType) {
            datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 1)));
            datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 2)));
        } else {
            if (nextFollowUpTime != null) {
                datekeys.add(DateUtil.formatDate(nextFollowUpTime));
                datekeys.add(DateUtil.formatDate(DateUtil.addDay(nextFollowUpTime, 1)));
            } else {
                //没有下次跟进时间且二级意向为其它时，不生成数据
                if (PatientInventoryFirstIntentionType.PatientInventorySecondIntentionType.NEED_FOLLOW_UP_OTHER.getCode() == secondIntentionType) {
                    return;
                }
                datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 1)));
                datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 2)));
            }
        }

        for (String datekey : datekeys) {
            PatientInventoryDashboardDetail detail = new PatientInventoryDashboardDetail();
            detail.setDateKey(datekey);
            detail.setDataType(PatientInventoryDataType.NEW_VISIT_REQUIRED.getCode());
            detail.setBindingUniqueCode(volunteerUniqueCode);
            detail.setResultType(PatientInventoryDataResultType.INIT.getCode());
            detail.setPatientInfoId(recordDo.getPatientInfoId());
            detail.setBatchUid(batchUid);
            detail.setCanOverdue(1);
            detail.setResultId(0L);

            // 设置科室ID和床位ID
            if (patientInfoDo != null) {
                detail.setDepartmentsId(Long.valueOf(patientInfoDo.getDepartmentsId()));
                detail.setBedId(patientInfoDo.getBedId());
            }

            details.add(detail);
        }
        //保存数据
        if (CollectionUtils.isNotEmpty(details)) {
            patientInventoryDashboardDetailDao.batchInsert(details);
        }
    }

    /**
     * 处理后续拜访数据
     * 主要功能：
     * 1. 处理上次需拜访任务的完成状态
     * 2. 根据当前意向和下次跟进时间创建新的拜访需求
     * <p>
     * 处理流程：
     * 1. 检查是否为盘点数据，盘点数据不处理拜访逻辑
     * 2. 获取历史跟进记录，找出最近一次的意向类型
     * 3. 处理上次拜访任务的状态（如有）
     * - 对于过期未完成的任务，标记为OVERDUE_VISIT
     * - 对于正常状态的任务，根据时间判断是提前完成还是按期完成
     * 4. 处理新的拜访需求（如需要跟进）
     * - 根据下次跟进时间创建两个拜访日期
     * - 判断是否可过期（首次需跟进时可过期）
     *
     * @param recordDo            患者跟进记录
     * @param volunteerUniqueCode 顾问唯一编码
     * @param firstIntentionType  一级意向类型
     */
    private void handleFollowUpVisitData(PatientInventoryPatientFollowUpRecordDo recordDo,
                                         String volunteerUniqueCode,
                                         Integer firstIntentionType,
                                         PatientInventoryPatientInfoDo patientInfoDo) {
        if (StringUtils.isNotEmpty(recordDo.getInventoryRecord())) {
            //是盘点数据，不能处理拜访相关操作
            return;
        }
        List<PatientInventoryDashboardDetail> details = new ArrayList<>();
        String batchUid = UUID.randomUUID().toString().replace("-", "");
        //查询上一次跟进记录
        List<PatientInventoryPatientFollowUpRecordDo> recordDos = patientInventoryPatientFollowUpRecordDao
                .selectByCorrelationIdAndId(recordDo.getCorrelationId(), recordDo.getId());
        Date nextFollowUpTime = recordDo.getNextFollowUpTime();
        //获取最新非盘点意向
        Integer lastFirstIntentionType = null;
        for (PatientInventoryPatientFollowUpRecordDo historyRecord : recordDos) {
            if (StringUtils.isNotEmpty(historyRecord.getInventoryRecord())) {
                continue;
            }
            lastFirstIntentionType = historyRecord.getFirstIntention();
            break;
        }

        //处理上一次需拜访的完成状态
        if (PatientInventoryFirstIntentionType.NEED_FOLLOW_UP.getCode() == lastFirstIntentionType) {

            String dateKey = DateUtil.formatDate(recordDo.getCreateTime() != null ? recordDo.getCreateTime() : new Date());
            PatientInventoryDashboardDetail lastVisit = patientInventoryDashboardDetailDao.selectLastByPatientInfoIdAndType(
                    recordDo.getPatientInfoId(),
                    PatientInventoryDataType.NEW_VISIT_REQUIRED.getCode()
            );

            if (lastVisit != null) {
                List<PatientInventoryDashboardDetail> detailList = patientInventoryDashboardDetailDao
                        .listByDataTypeAndBatchUid(lastVisit.getBatchUid(), lastVisit.getDataType());

                Integer dataType = null;
                Integer resultType = null;

                if (lastVisit.getResultType() == PatientInventoryDataResultType.OVERDUE_NOT_COMPLETED.getCode()) {
                    dataType = PatientInventoryDataType.OVERDUE_VISIT.getCode();
                    resultType = PatientInventoryDataResultType.OVERDUE_COMPLETED.getCode();
                } else if (lastVisit.getResultType() == PatientInventoryDataResultType.INIT.getCode()) {
                    dataType = PatientInventoryDataType.VISIT.getCode();
                    boolean preComplete = detailList.stream().allMatch(detail ->
                            DateUtil.parseDate(dateKey).before(DateUtil.parseDate(detail.getDateKey())));
                    resultType = preComplete ?
                            PatientInventoryDataResultType.PRE_COMPLETED.getCode() :
                            PatientInventoryDataResultType.EXPECTED_COMPLETED.getCode();
                }

                if (dataType != null) {
                    PatientInventoryDashboardDetail completionDetail = new PatientInventoryDashboardDetail();
                    completionDetail.setDateKey(dateKey);
                    completionDetail.setDataType(dataType);
                    completionDetail.setBindingUniqueCode(volunteerUniqueCode);
                    completionDetail.setResultType(resultType);
                    completionDetail.setPatientInfoId(recordDo.getPatientInfoId());
                    completionDetail.setBatchUid(batchUid);
                    completionDetail.setCanOverdue(0);
                    completionDetail.setResultId(0L);
                    completionDetail.setBedId(patientInfoDo.getBedId());
                    completionDetail.setDepartmentsId((long) patientInfoDo.getDepartmentsId());
                    patientInventoryDashboardDetailDao.insertSelective(completionDetail);

                    List<Long> ids = detailList.stream()
                            .map(PatientInventoryDashboardDetail::getId)
                            .collect(Collectors.toList());
                    patientInventoryDashboardDetailDao.updateResultType(ids, completionDetail.getId(), resultType);
                }
            }
        }

        //处理新的拜访记录
        if (nextFollowUpTime != null && PatientInventoryFirstIntentionType.NEED_FOLLOW_UP.getCode() == firstIntentionType) {
            List<String> datekeys = new ArrayList<>();
            datekeys.add(DateUtil.formatDate(nextFollowUpTime));
            datekeys.add(DateUtil.formatDate(DateUtil.addDay(nextFollowUpTime, 1)));

            Integer canOverdue = 0;
            if (PatientInventoryFirstIntentionType.NEED_FOLLOW_UP.getCode() != lastFirstIntentionType) {
                Long count = recordDos.stream()
                        .filter(item -> item.getFirstIntention() == PatientInventoryFirstIntentionType.NEED_FOLLOW_UP.getCode())
                        .distinct()
                        .count();
                if (count < 1) {
                    canOverdue = 1;
                }
            }

            for (String datekey : datekeys) {
                PatientInventoryDashboardDetail detail = new PatientInventoryDashboardDetail();
                detail.setDateKey(datekey);
                detail.setDataType(PatientInventoryDataType.NEW_VISIT_REQUIRED.getCode());
                detail.setBindingUniqueCode(volunteerUniqueCode);
                detail.setResultType(PatientInventoryDataResultType.INIT.getCode());
                detail.setPatientInfoId(recordDo.getPatientInfoId());
                detail.setBatchUid(batchUid);
                detail.setCanOverdue(canOverdue);
                detail.setResultId(0L);
                detail.setDepartmentsId((long) patientInfoDo.getDepartmentsId());
                detail.setBedId(patientInfoDo.getBedId());
                details.add(detail);
            }
            //保存数据
            if (CollectionUtils.isNotEmpty(details)) {
                patientInventoryDashboardDetailDao.batchInsert(details);
            }
        }
    }

    /**
     * 处理首次盘点数据
     */
    private void handleFirstInventoryData(PatientInventoryPatientFollowUpRecordDo recordDo,
                                          String volunteerUniqueCode,
                                          PatientInventoryPatientInfoDo patientInfoDo) {
        List<PatientInventoryDashboardDetail> details = new ArrayList<>();
        List<String> datekeys = new ArrayList<>();
        String batchUid = UUID.randomUUID().toString().replace("-", "");
        Date recordDate = recordDo.getCreateTime() != null ? recordDo.getCreateTime() : new Date();
        Integer firstIntentionType = recordDo.getFirstIntention();
        Integer secondIntentionType = recordDo.getSecondIntention();
        Date nextFollowUpTime = recordDo.getNextFollowUpTime();

        if (PatientInventoryFirstIntentionType.NEED_FOLLOW_UP.getCode() == firstIntentionType) {
            if (PatientInventoryFirstIntentionType.PatientInventorySecondIntentionType.NOT_CONTACTED.getCode() == secondIntentionType) {
                datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 2)));
                datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 3)));
            } else if (PatientInventoryFirstIntentionType.PatientInventorySecondIntentionType.POSSIBLE_INITIATION.getCode() == secondIntentionType) {
                if (nextFollowUpTime != null) {
                    datekeys.add(DateUtil.formatDate(nextFollowUpTime));
                    datekeys.add(DateUtil.formatDate(DateUtil.addDay(nextFollowUpTime, 1)));
                } else {
                    datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 1)));
                    datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 2)));
                }
            }
        } else if (PatientInventoryFirstIntentionType.NO_INTENTION.getCode() == firstIntentionType) {
            String labels = patientInfoDo.getLabels();
            if (getNeedInventory(labels)) {
                datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 1)));
                datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 2)));
            }
        }
        //此处需要查询上级，当前uniqueCode为顾问的
        for (String datekey : datekeys) {
            PatientInventoryDashboardDetail detail = new PatientInventoryDashboardDetail();
            detail.setDateKey(datekey);
            detail.setDataType(PatientInventoryDataType.NEW_INVENTORY_REQUIRED.getCode());
            detail.setBindingUniqueCode(volunteerUniqueCode);
            detail.setResultType(PatientInventoryDataResultType.INIT.getCode());
            detail.setPatientInfoId(recordDo.getPatientInfoId());
            detail.setBatchUid(batchUid);
            detail.setCanOverdue(1);
            detail.setResultId(0L);
            detail.setDepartmentsId((long) patientInfoDo.getDepartmentsId());
            detail.setBedId(patientInfoDo.getBedId());
            details.add(detail);
        }
        //保存数据
        if (CollectionUtils.isNotEmpty(details)) {
            patientInventoryDashboardDetailDao.batchInsert(details);
        }
    }

    /**
     * 处理后续患者盘点数据
     * <p>
     * 该方法主要处理两部分内容：
     * 1. 处理上一次盘点任务的完成状态，若当前是盘点数据则标记完成
     * 2. 根据患者标签和意向判断是否需要创建新的盘点任务
     * <p>
     * 处理流程：
     * 1. 获取今天的日期，生成新的批次ID
     * 2. 检查是否为盘点数据（盘点记录不为空）
     * 3. 查询最近一次该患者的盘点任务记录
     * 4. 如果存在盘点任务且当前是盘点数据（包含盘点记录），则处理上次盘点任务的完成状态：
     * - 对于过期未完成的任务，标记为过期盘点(OVERDUE_INVENTORY)
     * - 对于正常状态的任务，判断是提前完成还是按期完成
     * 5. 检查是否需要创建新的盘点记录：
     * - 分析患者标签，确定是否需要盘点
     * - 检查首次意向是否为无意向
     * - 确保之前没有盘点记录
     * 6. 如需创建新盘点任务，则创建接下来两天的盘点任务
     *
     * @param recordDo            患者跟进记录，包含盘点记录和意向类型等数据
     * @param volunteerUniqueCode 负责盘点的顾问唯一编码
     * @param patientInfoDo       患者信息，包含标签等属性
     */
    private void handleFollowUpInventoryData(PatientInventoryPatientFollowUpRecordDo recordDo,
                                             String volunteerUniqueCode,
                                             PatientInventoryPatientInfoDo patientInfoDo) {
        String todayDateKey = DateUtil.formatDate(recordDo.getCreateTime() != null ? recordDo.getCreateTime() : new Date());
        String batchUid = UUID.randomUUID().toString().replace("-", "");
        Boolean isInventoryData = StringUtils.isNotEmpty(recordDo.getInventoryRecord());
        //处理上一次盘点的完成状态
        PatientInventoryDashboardDetail lastInventory = patientInventoryDashboardDetailDao.selectLastByPatientInfoIdAndType(
                recordDo.getPatientInfoId(),
                PatientInventoryDataType.NEW_INVENTORY_REQUIRED.getCode()
        );
        List<PatientInventoryDashboardDetail> details = new ArrayList<>();
        PatientInventoryPatientFollowUpRecordDo firstRecord = patientInventoryPatientFollowUpRecordDao
                .selectFirstByCorrelationId(recordDo.getCorrelationId());

        //完成盘点数据
        if (lastInventory != null) {
            if (isInventoryData) {
                //只有盘点数据，才会完成上一次需盘点记录
                List<PatientInventoryDashboardDetail> detailList = patientInventoryDashboardDetailDao
                        .listByDataTypeAndBatchUid(lastInventory.getBatchUid(), lastInventory.getDataType());

                Integer dataType = null;
                Integer resultType = null;

                if (lastInventory.getResultType() == PatientInventoryDataResultType.OVERDUE_NOT_COMPLETED.getCode()) {
                    dataType = PatientInventoryDataType.OVERDUE_INVENTORY.getCode();
                    resultType = PatientInventoryDataResultType.OVERDUE_COMPLETED.getCode();
                } else if (lastInventory.getResultType() == PatientInventoryDataResultType.INIT.getCode()) {
                    dataType = PatientInventoryDataType.INVENTORY.getCode();
                    boolean preComplete = detailList.stream().allMatch(detail ->
                            DateUtil.parseDate(todayDateKey).before(DateUtil.parseDate(detail.getDateKey())));
                    resultType = preComplete ?
                            PatientInventoryDataResultType.PRE_COMPLETED.getCode() :
                            PatientInventoryDataResultType.EXPECTED_COMPLETED.getCode();
                }

                if (dataType != null) {
                    PatientInventoryDashboardDetail completionDetail = new PatientInventoryDashboardDetail();
                    completionDetail.setDateKey(todayDateKey);
                    completionDetail.setDataType(dataType);
                    completionDetail.setBindingUniqueCode(volunteerUniqueCode);
                    completionDetail.setResultType(0);
                    completionDetail.setPatientInfoId(recordDo.getPatientInfoId());
                    completionDetail.setBatchUid(batchUid);
                    completionDetail.setCanOverdue(0);
                    completionDetail.setResultId(0L);
                    completionDetail.setDepartmentsId((long) patientInfoDo.getDepartmentsId());
                    completionDetail.setBedId(patientInfoDo.getBedId());
                    patientInventoryDashboardDetailDao.insertSelective(completionDetail);

                    List<Long> ids = detailList.stream()
                            .map(PatientInventoryDashboardDetail::getId)
                            .collect(Collectors.toList());
                    patientInventoryDashboardDetailDao.updateResultType(ids, completionDetail.getId(), resultType);
                }
            } else {
                //跟进记录，查看是否需要取消本次盘点数据，跟进时间在应盘点时间之前，就取消盘点数据
                if (firstRecord.getFirstIntention() == PatientInventoryFirstIntentionType.NEED_FOLLOW_UP.getCode() &&
                        firstRecord.getSecondIntention() == PatientInventoryFirstIntentionType.PatientInventorySecondIntentionType.NOT_CONTACTED.getCode()) {
                    //查看最新的多条盘点记录
                    List<PatientInventoryDashboardDetail> detailList = patientInventoryDashboardDetailDao.selectByBatchUid(lastInventory.getBatchUid());
                    //如果该盘点时间都晚于跟进时间，则取消盘点数据
                    if (detailList.stream().allMatch(detail -> DateUtil.parseDate(detail.getDateKey()).after(new Date()))) {
                        patientInventoryDashboardDetailDao.deleteByIds(detailList.stream().map(item -> item.getId()).collect(Collectors.toList()));
                    }
                }
            }

        }
        if (isInventoryData) {
            //盘点数据，后续无操作
            return;
        }

        //检查是否需要创建新的盘点记录
        Boolean needInventory = getNeedInventory(patientInfoDo.getLabels());

        if (needInventory &&
                firstRecord.getFirstIntention() == PatientInventoryFirstIntentionType.NO_INTENTION.getCode() &&
                lastInventory == null) {
            List<String> datekeys = new ArrayList<>();
            Date recordDate = recordDo.getCreateTime() != null ? recordDo.getCreateTime() : new Date();
            datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 1)));
            datekeys.add(DateUtil.formatDate(DateUtil.addDay(recordDate, 2)));

            for (String datekey : datekeys) {
                PatientInventoryDashboardDetail detail = new PatientInventoryDashboardDetail();
                detail.setDateKey(datekey);
                detail.setDataType(PatientInventoryDataType.NEW_INVENTORY_REQUIRED.getCode());
                detail.setBindingUniqueCode(volunteerUniqueCode);
                detail.setResultType(PatientInventoryDataResultType.INIT.getCode());
                detail.setPatientInfoId(recordDo.getPatientInfoId());
                detail.setBatchUid(batchUid);
                detail.setCanOverdue(1);
                detail.setResultId(0L);
                detail.setDepartmentsId((long) patientInfoDo.getDepartmentsId());
                detail.setBedId(patientInfoDo.getBedId());
                details.add(detail);
            }
            //保存数据
            if (CollectionUtils.isNotEmpty(details)) {
                patientInventoryDashboardDetailDao.batchInsert(details);
            }
        }
    }


    private String getLeaderUniqueCode(String uniqueCode) {
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOList = crmOrganizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(bdCrmOrgUserRelationDOList)) {
            return "";
        }

        BdCrmOrganizationDO bdCrmOrganizationDO = crmSelfBuiltOrgReadService.getCurrentOrgById(bdCrmOrgUserRelationDOList.get(0).getOrgId());
        if (bdCrmOrganizationDO == null) {
            return "";
        }

        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = crmOrganizationRelationService.listRelationByOrgId(bdCrmOrganizationDO.getParentId());
        return org.apache.commons.collections4.CollectionUtils.isNotEmpty(bdCrmOrgUserRelationDOS) ? bdCrmOrgUserRelationDOS.get(0).getUniqueCode() : "";
    }

}
