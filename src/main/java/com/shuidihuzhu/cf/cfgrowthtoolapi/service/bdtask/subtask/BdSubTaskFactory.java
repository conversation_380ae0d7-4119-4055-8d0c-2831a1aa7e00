package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.subtask;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdSubTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdSubTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.ICrmBdTaskService;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-09-20 14:09
 * 状态流转
 * 进行中 -> 完成
 * 进行中 -> 超时
 * 其他状态无法流转
 **/
@Slf4j
@Service
public class BdSubTaskFactory {

    @Autowired
    private List<IBdSubTaskCheckService> bdSubTaskCheckServiceList;
    private Map<CrmBdSubTaskDO.TaskTypeEnum, IBdSubTaskCheckService> bdSubTaskCheckServiceMap;

    @Autowired
    private ICrmBdSubTaskService crmBdSubTaskService;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoServiceImpl;

    @Autowired
    private ICrmBdTaskService crmBdTaskService;


    @PostConstruct
    public void init() {
        bdSubTaskCheckServiceMap = bdSubTaskCheckServiceList.stream()
                .collect(Collectors.toMap(IBdSubTaskCheckService::getTaskType,
                        bdTaskStatusService -> bdTaskStatusService));
    }

    public void checkAndCreateTask(BdSubTaskContext bdSubTaskContext) {
        log.debug("checkAndCreateTask bdSubTaskContext:{}", bdSubTaskContext);
        boolean checkAndFillFiled = checkAndFillFiled(bdSubTaskContext);
        if (!checkAndFillFiled) {
            log.info("填充字段失败");
            return;
        }
        List<CrmBdSubTaskDO.TaskTypeEnum> taskTypeEnumList = bdSubTaskContext.getTaskTypeEnumList();
        for (CrmBdSubTaskDO.TaskTypeEnum taskTypeEnum : taskTypeEnumList) {
            bdSubTaskContext.setTaskTypeEnum(taskTypeEnum);
            checkAndCreateTaskInner(bdSubTaskContext);
        }
    }


    private boolean checkAndFillFiled(BdSubTaskContext bdSubTaskContext) {
        if (bdSubTaskContext.getCaseId() <= 0) {
            log.info("没有设置案例id");
            return false;
        }
        if (CollectionUtils.isEmpty(bdSubTaskContext.getTaskTypeEnumList())) {
            log.info("没有设置对应的任务类型");
            return false;
        }

        CfBdCaseInfoDo cfBdCaseInfoDo = bdSubTaskContext.getCfBdCaseInfoDo();
        if (cfBdCaseInfoDo == null) {
            cfBdCaseInfoDo = cfBdCaseInfoServiceImpl.getBdCaseInfoByInfoId(bdSubTaskContext.getCaseId());
            if (cfBdCaseInfoDo == null) {
                log.info("不是线下案例，无法生成任务");
                return false;
            }
            bdSubTaskContext.setCfBdCaseInfoDo(cfBdCaseInfoDo);
        }
        return true;
    }


    public void updateBdTaskWhenComplete(BdSubTaskContext bdSubTaskContext) {
        log.debug("updateBdTaskWhenComplete bdSubTaskContext:{}", bdSubTaskContext);
        boolean checkAndFillFiled = checkAndFillFiled(bdSubTaskContext);
        if (!checkAndFillFiled) {
            log.info("填充字段失败");
            return;
        }
        List<CrmBdSubTaskDO.TaskTypeEnum> taskTypeEnumList = bdSubTaskContext.getTaskTypeEnumList();
        for (CrmBdSubTaskDO.TaskTypeEnum taskTypeEnum : taskTypeEnumList) {
            bdSubTaskContext.setTaskTypeEnum(taskTypeEnum);
            updateBdTaskWhenCompleteInner(bdSubTaskContext);
        }
    }


    public void updateBdTaskWhenOverTime(BdSubTaskContext bdSubTaskContext) {
        log.debug("updateBdTaskWhenOverTime bdSubTaskContext:{}", bdSubTaskContext);
        boolean checkAndFillFiled = checkAndFillFiled(bdSubTaskContext);
        if (!checkAndFillFiled) {
            log.info("填充字段失败");
            return;
        }
        List<CrmBdSubTaskDO.TaskTypeEnum> taskTypeEnumList = bdSubTaskContext.getTaskTypeEnumList();

        List<Integer> taskTypes = taskTypeEnumList.stream().map(CrmBdSubTaskDO.TaskTypeEnum::getCode).collect(Collectors.toList());
        List<CrmBdSubTaskDO> crmBdSubTaskDOList = crmBdSubTaskService.getByTaskTypesAndCaseId(taskTypes, bdSubTaskContext.getCaseId());
        if (CollectionUtils.isEmpty(crmBdSubTaskDOList)) {
            log.info("当前任务不存在,任务类型:{}", taskTypeEnumList);
            return;
        }

        List<CrmBdSubTaskDO> crmBdSubTaskDOS = crmBdSubTaskDOList.stream().filter(v -> v.getTaskStatus() == CrmBdSubTaskDO.TaskStatusEnum.start.getCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmBdSubTaskDOS)) {
            log.info("非进行中状态无法修改为已完成,任务类型:{}", taskTypeEnumList);
            return;
        }

        //修改子任务状态
        List<Long> ids = crmBdSubTaskDOS.stream().map(CrmBdSubTaskDO::getId).collect(Collectors.toList());
        crmBdSubTaskService.batchUpdateWhenOverTime(ids);
        //修改主任务状态
        CrmBdSubTaskDO crmBdSubTaskDO = crmBdSubTaskDOS.get(0);
        crmBdTaskService.updateWhenOverTime(crmBdSubTaskDO.getParentId());
    }


    private void checkAndCreateTaskInner(BdSubTaskContext bdSubTaskContext) {
        CrmBdSubTaskDO.TaskTypeEnum taskTypeEnum = bdSubTaskContext.getTaskTypeEnum();
        IBdSubTaskCheckService bdSubTaskCheckService = getBdSubTaskCheckService(taskTypeEnum);
        if (bdSubTaskCheckService == null) {
            return;
        }
        boolean needCreateTask = bdSubTaskCheckService.checkNeedCreateTask(bdSubTaskContext);
        if (!needCreateTask) {
            log.info("无需生成任务");
            return;
        }
        //如果已经存在了就不需要再次生成
        CrmBdSubTaskDO crmBdSubTaskDOFromDB = crmBdSubTaskService.getByTaskTypeAndCaseId(taskTypeEnum.getCode(), bdSubTaskContext.getCaseId());
        if (crmBdSubTaskDOFromDB != null) {
            log.info("当前任务不存在,任务类型:{}", taskTypeEnum);
            return;
        }
        CfBdCaseInfoDo cfBdCaseInfoDo = bdSubTaskContext.getCfBdCaseInfoDo();
        CrmBdSubTaskDO crmBdSubTaskDO = new CrmBdSubTaskDO();
        crmBdSubTaskDO.setTaskType(taskTypeEnum.getCode());
        crmBdSubTaskDO.setTaskStatus(CrmBdSubTaskDO.TaskStatusEnum.start.getCode());
        crmBdSubTaskDO.setCaseId(bdSubTaskContext.getCaseId());
        crmBdSubTaskDO.setUniqueCode(cfBdCaseInfoDo.getUniqueCode());
        crmBdSubTaskDO.setOrgId(cfBdCaseInfoDo.getOrgId());
        crmBdSubTaskDO.setOrgPath(cfBdCaseInfoDo.getOrgPath());
        crmBdSubTaskDO.setParentId(bdSubTaskContext.getParentTaskId());
        crmBdSubTaskService.insert(crmBdSubTaskDO);
    }


    private void updateBdTaskWhenCompleteInner(BdSubTaskContext bdSubTaskContext) {
        CrmBdSubTaskDO.TaskTypeEnum taskTypeEnum = bdSubTaskContext.getTaskTypeEnum();
        IBdSubTaskCheckService bdTaskCheckService = getBdSubTaskCheckService(taskTypeEnum);
        if (bdTaskCheckService == null) {
            return;
        }
        //查找当前类型的任务是否存在
        CrmBdSubTaskDO crmBdSubTaskDO = crmBdSubTaskService.getByTaskTypeAndCaseId(taskTypeEnum.getCode(), bdSubTaskContext.getCaseId());
        if (crmBdSubTaskDO == null) {
            log.info("当前任务不存在,任务类型:{}", taskTypeEnum);
            return;
        }
        if (!Objects.equals(CrmBdSubTaskDO.TaskStatusEnum.start.getCode(), crmBdSubTaskDO.getTaskStatus())) {
            log.info("非进行中状态无法修改为已完成,任务类型:{}", taskTypeEnum);
            return;
        }
        boolean needCreateTask = bdTaskCheckService.checkTaskComplete(bdSubTaskContext);
        if (!needCreateTask) {
            log.info("无需更新任务");
            return;
        }
        int res = crmBdSubTaskService.updateWhenComplete(crmBdSubTaskDO.getId());
        //更新父任务
        updateBdParentTaskWhenCompleteInner(res, crmBdSubTaskDO.getParentId());
    }

    private void updateBdParentTaskWhenCompleteInner(int res, long parentId) {
        if (res <= 0) {
            return;
        }
        List<CrmBdSubTaskDO> bdSubTaskDOList = crmBdSubTaskService.queryByParentId(parentId);
        if (CollectionUtils.isEmpty(bdSubTaskDOList)) {
            return;
        }
        //如果所有的子任务都已经完成了,那么就更新父任务
        long completeCount = bdSubTaskDOList.stream().filter(v -> v.getTaskStatus() == CrmBdSubTaskDO.TaskStatusEnum.complete.getCode()).count();
        if (completeCount != bdSubTaskDOList.size()) {
            return;
        }
        crmBdTaskService.updateWhenComplete(parentId);
    }


    @Nullable
    private IBdSubTaskCheckService getBdSubTaskCheckService(CrmBdSubTaskDO.TaskTypeEnum taskTypeEnum) {
        if (taskTypeEnum == null) {
            log.info("缺少关键参数");
            return null;
        }
        IBdSubTaskCheckService bdSubTaskCheckService = bdSubTaskCheckServiceMap.get(taskTypeEnum);
        if (bdSubTaskCheckService == null) {
            log.info("当前类型不支持");
            return null;
        }
        return bdSubTaskCheckService;
    }

}
