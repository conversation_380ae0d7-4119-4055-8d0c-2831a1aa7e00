package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdcrmVolunteerOrgnizationCopyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ValidCaseEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.BdServiceInfoVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.excel.DepartmentExcelModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.SimpleHospitalCodeModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.CaseValidCommonSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IESCfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrgConvertService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.es.EsCfBdCaseInfoDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * @author: wanghui
 * @create: 2019/12/16 3:55 PM
 * @Desc
 *  使用elastic-sql 查询 between '2019-11-01' and '2019-11-05' 实际查的是 2019-11-01 00:00:00 到2019-11-05 23:59:59
 *  使用dao 查询时  between '2019-11-01' and '2019-11-05' 实际查的是 2019-11-01 00:00:00 到2019-11-05 00:00:00
 *  如果想查询2019-11-01 00:00:00 到2019-11-05 00:00:00 时间的  只能是 date_created >= '2019-11-01' and date_created < '2019-11-05'
 *  所以这里 使用< 、 > 来比较时间
 *  注意：   elastic-sql 中的 <= 2019-11-05 能查到 2019-11-05 23:59:59秒的数据
 **/
@Service
@Slf4j
@RefreshScope
public class ESCfBdCaseInfoService implements IESCfBdCaseInfoService {

    @Autowired
    private EsCfBdCaseInfoDao esCfBdCaseInfoDao;

    @Autowired
    private ICrmOrgConvertService crmOrgConvertService;

    @Autowired
    private ApolloService apolloService;


    @Override
    public OpResult<List<CfCrmVolunteerCaseCountModel>> getCfCrmVolunteerCaseCountModelGroupByFromEs(List<String> dateTimes, List<Integer> orgIdList, BdCrmSearchParam bdCrmSearchParam) {
        if (CollectionUtils.isEmpty(orgIdList)){
            return OpResult.createSucResult(Lists.newArrayList());
        }
        List<CfCrmVolunteerCaseCountModel> modelFromEs;

        String startTimeStr =  GrowthtoolUtil.convertEsTimeByDateTime(dateTimes.get(0));
        String endTimeStr = GrowthtoolUtil.convertEsTimeByDateTime(DateUtil.formatDate(DateUtils.addDays(DateUtil.getDateFromShortString(dateTimes.get(dateTimes.size() - 1)),1)));
        modelFromEs = esCfBdCaseInfoDao.getCfCrmVolunteerCaseCountModelGroupByFromEs(startTimeStr, endTimeStr, orgIdList, bdCrmSearchParam);

        if (CollectionUtils.isEmpty(modelFromEs)){
            return OpResult.createSucResult(Lists.newArrayList());
        }

        return OpResult.createSucResult(modelFromEs);
    }

    @Override
    public OpResult<CfCrmVolunteerCaseCountModel> getOrgCrmCrowdfundingModelFromEs(List<String> dateTimes, List<Integer> orgIdList, BdCrmSearchParam bdCrmSearchParam) {
        if (CollectionUtils.isEmpty(orgIdList)){
            return OpResult.createSucResult(new CfCrmVolunteerCaseCountModel());
        }
        String startTimeStr =  GrowthtoolUtil.convertEsTimeByDateTime(dateTimes.get(0));
        String endTimeStr = GrowthtoolUtil.convertEsTimeByDateTime(DateUtil.formatDate(DateUtils.addDays(DateUtil.getDateFromShortString(dateTimes.get(dateTimes.size() - 1)),1)));
        return OpResult.createSucResult(esCfBdCaseInfoDao.getOrgCrmCrowdfundingModelFromEs(startTimeStr, endTimeStr, orgIdList, bdCrmSearchParam));
    }

    @Override
    public OpResult<List<CfCrmVolunteerCaseCountSimpleModel>> getCfCrmVolunteerCaseCountSimpleModelFromEs(List<String> dateTimes, long amount, String uniqueCode, Integer firstApprovePass) {
        List<CfCrmVolunteerCaseCountSimpleModel> modelFromEs;
        String startTimeStr = GrowthtoolUtil.convertEsTimeByDateTime(dateTimes.get(0));
        String endTimeStr = GrowthtoolUtil.convertEsTimeByDateTime(dateTimes.get(dateTimes.size() - 1));
        modelFromEs = esCfBdCaseInfoDao.getCfCrmVolunteerCaseCountSimpleModelFromEs(startTimeStr, endTimeStr, amount, uniqueCode, firstApprovePass);

        List<CfBdcrmVolunteerOrgnizationCopyDO> copyDOByMisList = crmOrgConvertService.getOrgByUniqueCodes(Lists.newArrayList(uniqueCode));
        if (CollectionUtils.isEmpty(copyDOByMisList)) {
            log.error(this.getClass().getSimpleName() + " getOrgByUniqueCodes uniqueCode:{} 没有数据", uniqueCode);
            return OpResult.createFailResult(CfGrowthtoolErrorCode.USER_CAN_NOT_EXSITS);
        }
        for (CfCrmVolunteerCaseCountSimpleModel model : modelFromEs) {
            model.setVolunteerName(copyDOByMisList.get(0).getMisName() + "-" + copyDOByMisList.get(0).getOrgName());
        }
        return OpResult.createSucResult(modelFromEs);
    }

    @Override
    public List<CfCrmVolunteerCaseCountSimpleModel> aggregateByUniqueCodeAndDateTimeFromEs(List<String> dateTimes, List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(dateTimes) || CollectionUtils.isEmpty(uniqueCodes)) {
            return Lists.newArrayList();
        }
        String startTimeStr = GrowthtoolUtil.convertEsTimeByDateTime(dateTimes.get(0));
        String endTimeStr = GrowthtoolUtil.convertEsTimeByDateTime(dateTimes.get(dateTimes.size() - 1));
        return esCfBdCaseInfoDao.aggregateByUniqueCodeAndDateTime(startTimeStr, endTimeStr, uniqueCodes);
    }

    @Override
    public OpResult<List<CfCrmDateTimeCaseCountModel>> getCfCrmDateTimeCaseCountModelFromEs(List<String> dateTimes,
                                                                                            long amount,
                                                                                            List<Integer> orgIdList,
                                                                                            int notFirstApprovePass){
        if (CollectionUtils.isEmpty(orgIdList)) {
            return OpResult.createSucResult(Lists.newArrayList());
        }
        List<CfCrmDateTimeCaseCountModel> modelFromEs;
        String startTimeStr = GrowthtoolUtil.convertEsTimeByDateTime(dateTimes.get(0));
        String endTimeStr= GrowthtoolUtil.convertEsTimeByDateTime(dateTimes.get(dateTimes.size()-1));
        modelFromEs = esCfBdCaseInfoDao.getCfCrmDateTimeCaseCountModelFromEs(startTimeStr, endTimeStr, amount, orgIdList, notFirstApprovePass);

        return OpResult.createSucResult(modelFromEs);
    }

    @Override
    public OpResult<Long> getCountByUniqueCodeListFromEs(List<String> uniqueCodeList, String startTime, String endTime) {
        if (CollectionUtils.isEmpty(uniqueCodeList)){
            return OpResult.createSucResult(0L);
        }

        String startTimeStr = convertEsTimeByDateTime(startTime);
        String endTimeStr =  convertEsTimeByDateTime(endTime);
        return OpResult.createSucResult(Optional.ofNullable(esCfBdCaseInfoDao.getCountByUniqueCodeListFromEs(uniqueCodeList,startTimeStr,endTimeStr)).orElse(0L));

    }

    @Override
    public List<CfBdCaseInfoDo> listCaseIdByOrgIdListFromEs(List<Integer> orgIdList, String startTime, String endTime) {
        String startTimeStr = convertEsTimeByDateTime(startTime);
        String endTimeStr = convertEsTimeByDateTime(endTime);
        return esCfBdCaseInfoDao.listCaseIdByOrgIdListFromEs(orgIdList, startTimeStr, endTimeStr);
    }


    @Override
    public List<CfBdCaseInfoDo> listCaseIdByUniqueCodesFromEs(List<String> uniqueCodeList, String startTime, String endTime) {
        if (CollectionUtils.isEmpty(uniqueCodeList)) {
            return Lists.newArrayList();
        }

        String startTimeStr = convertEsTimeByDateTime(startTime);
        String endTimeStr = convertEsTimeByDateTime(endTime);
        return esCfBdCaseInfoDao.listCaseIdByUniqueCodesFromEs(uniqueCodeList, startTimeStr, endTimeStr);
    }


    private String convertEsTimeByDateTime(String time){
        if (StringUtils.isBlank(time)){
            return null;
        }
        return GrowthtoolUtil.convertEsTimeByDateTime(time);
    }

    @Override
    public OpResult<Long> getCountByUniqueCodeAndTimeAndCfAmountFromEs(List<String> uniqueCodeList, Integer orgId, Date startTime, Date endTime, BdCrmSearchParam bdCrmSearchParam) {
        if (CollectionUtils.isEmpty(uniqueCodeList)){
            return OpResult.createSucResult(0L);
        }

        String startTimeStr = GrowthtoolUtil.convertEsTimeByDateTime(DateUtil.formatDate(startTime));
        String endTimeStr = GrowthtoolUtil.convertEsTimeByDateTime(DateUtil.formatDate(endTime));
        return OpResult.createSucResult(Optional.ofNullable(esCfBdCaseInfoDao.getCountByUniqueCodeAndTimeAndCfAmountFromEs(uniqueCodeList,orgId,startTimeStr,endTimeStr,bdCrmSearchParam)).orElse(0L));
    }

    @Override
    public OpResult<BdServiceInfoVo> getBdServiceInfoVoByUniqueCodeFromES(String uniqueCode) {
        List<BdServiceInfoVo> modelFromEs = esCfBdCaseInfoDao.getBdServiceInfoVoByUniqueCodeFromES(uniqueCode);
        if (CollectionUtils.isEmpty(modelFromEs)){
            return OpResult.createSucResult(null);
        }
        BdServiceInfoVo bdServiceInfoVo = modelFromEs.get(0);
        bdServiceInfoVo.setRaiseAmount(bdServiceInfoVo.getRaiseAmount()/100);
        bdServiceInfoVo.setSingleCaseRaiseAmount(bdServiceInfoVo.getSingleCaseRaiseAmount()/100);
        return OpResult.createSucResult(bdServiceInfoVo);
    }

    @Override
    public OpResult<BdServiceInfoVo> getBdServiceInfoVoByUniqueCodeFromESOneYearAgo(String uniqueCode, String createTime) {
        createTime = convertEsTimeByDateTime(createTime);
        List<BdServiceInfoVo> modelFromEs = esCfBdCaseInfoDao.getBdServiceInfoVoByUniqueCodeFromESOneYearAgo(uniqueCode, createTime);
        if (CollectionUtils.isEmpty(modelFromEs)){
            return OpResult.createSucResult(null);
        }
        BdServiceInfoVo bdServiceInfoVo = modelFromEs.get(0);
        bdServiceInfoVo.setRaiseAmount(bdServiceInfoVo.getRaiseAmount()/100);
        bdServiceInfoVo.setSingleCaseRaiseAmount(bdServiceInfoVo.getSingleCaseRaiseAmount()/100);
        return OpResult.createSucResult(bdServiceInfoVo);
    }


    @Override
    public List<DepartmentExcelModel> groupByHospitalNameDepartment(BdCrmDepartmentDataParam departmentDataParam) {
        if (departmentDataParam == null) {
            return Lists.newArrayList();
        }
        return esCfBdCaseInfoDao.groupByHospitalNameDepartment(departmentDataParam);
    }

    @Override
    public List<CfCrmVolunteerCaseCountSimpleModel> getBdCaseStatisticsFromEs(String startTime, String endTime, String uniqueCode) {
        String startTimeEs = GrowthtoolUtil.convertEsTimeByDateTime(startTime);
        String endTimeEs = GrowthtoolUtil.convertEsTimeByDateTime(endTime);

        return esCfBdCaseInfoDao.getBdCaseStatisticsFromEs(startTimeEs, endTimeEs, uniqueCode);
    }

    @Override
    public List<DepartmentDayCaseModel> departmentGbCaseCreateTime(DepartmentCmpParam departmentCmpParam) {
        if (departmentCmpParam.getDateQueryParam() == null || CollectionUtils.isEmpty(departmentCmpParam.getDepartmentIds())) {
            return Lists.newArrayList();
        }
        return esCfBdCaseInfoDao.departmentGbCaseCreateTime(departmentCmpParam);
    }

    @Override
    public List<DepartmentDayCaseModel> departmentGbFirstApproveTime(DepartmentCmpParam departmentCmpParam) {
        if (departmentCmpParam.getDateQueryParam() == null || CollectionUtils.isEmpty(departmentCmpParam.getDepartmentIds())) {
            return Lists.newArrayList();
        }
        return esCfBdCaseInfoDao.departmentGbFirstApproveTime(departmentCmpParam, ValidCaseEnum.VALID.getCode());
    }

    @Override
    public List<DepartmentDayCaseModel> departmentGbHot(DepartmentCmpParam departmentCmpParam) {
        if (departmentCmpParam.getDateQueryParam() == null || CollectionUtils.isEmpty(departmentCmpParam.getDepartmentIds())) {
            return Lists.newArrayList();
        }
        return esCfBdCaseInfoDao.departmentGbHot(departmentCmpParam);
    }

    @Override
    public List<SimpleHospitalCodeModel> listHospitalForDepartment(DepartmentHospitalParam departmentHospitalParam) {
        if (departmentHospitalParam == null || departmentHospitalParam.getDateQueryParam() == null) {
            return Lists.newArrayList();
        }
        DateQueryParam dateQueryParam = departmentHospitalParam.getDateQueryParam();
        if (StringUtils.isBlank(dateQueryParam.getStartTime()) || StringUtils.isBlank(dateQueryParam.getEndTime())) {
            return Lists.newArrayList();
        }
        return esCfBdCaseInfoDao.listHospitalForDepartment(dateQueryParam.getStartTime(), dateQueryParam.getEndTime(), departmentHospitalParam.getHospitalName(), departmentHospitalParam.getOrgIds());
    }

    @Override
    public List<Integer> findImportFlagDepartment(int id) {
        return esCfBdCaseInfoDao.findImportFlagDepartment(id)
                .stream()
                .map(DepartmentDayCaseModel::getDepartmentId)
                .collect(Collectors.toList());
    }

    @Override
    public CrmCrowdfundingModel statByOrgUseEs(long orgId) {
        return esCfBdCaseInfoDao.statByOrgUseEs(orgId);
    }

    /**
     * 封装时间的查询 where 条件
     * @param startTime  Date 类型 或 String 类型的 2019-11-11 或 2011-11-11 00:00:00
     * @param endTime Date 类型 或  String 类型的 2019-11-11 或 2011-11-11 00:00:00
     * @return
     */
    private StringBuilder builderWhereSqlOfDateCreated(Object startTime, Object endTime){
        StringBuilder whereSqlStr = new StringBuilder();
        if (Date.class.isInstance(startTime)){
            if (startTime!=null){
                whereSqlStr.append(String.format(" date_created >= '%s' and ",GrowthtoolUtil.convertEsTimeByDateTime(DateUtil.formatDate((Date) startTime))));
            }
            if (endTime!=null){
                whereSqlStr.append(String.format(" date_created < '%s' and ",GrowthtoolUtil.convertEsTimeByDateTime(DateUtil.formatDate((Date)endTime))));
            }
        }else {
            // 传入的是 String 类型的时间
            String startTimeStr = (String) startTime;
            String endTimeStr = (String) endTime;
            if (StringUtils.isNotBlank(startTimeStr)){
                whereSqlStr.append(String.format(" date_created >= '%s' and ",startTimeStr.length()>GeneralConstant.SHORT_TIME_LENGTH ?
                        GrowthtoolUtil.convertEsTimeByDateTime(DateUtil.formatDate(DateUtil.getDateFromLongString(startTimeStr))):
                        GrowthtoolUtil.convertEsTimeByDateTime(startTimeStr)));
            }
            if (StringUtils.isNotBlank(endTimeStr)){
                whereSqlStr.append(String.format(" date_created < '%s' and ",endTimeStr.length()>GeneralConstant.SHORT_TIME_LENGTH ?
                        GrowthtoolUtil.convertEsTimeByDateTime(DateUtil.formatDate(DateUtil.getDateFromLongString(endTimeStr))):
                        GrowthtoolUtil.convertEsTimeByDateTime(endTimeStr)));
            }
        }
        return whereSqlStr;
    }

}
