package com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund.Impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.CrowdFundingFeignDelegateImpl;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.caserefund.CfCaseRefundTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.caserefund.CfWaitDealTypeRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfWaitDealTypeEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.caserefund.CfCaseRefundDetailModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.caserefund.CfWaitDealResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.caserefund.CfCaseRefundDetailParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund.CfCaseFinanceRefundService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund.CfCaseRefundTaskService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund.CfWaitDealTypeRecordService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceRefundFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfRefundEnums;
import com.shuidihuzhu.cf.finance.model.vo.refund.CfRefundApplyV2Vo;
import com.shuidihuzhu.cf.finance.mq.CfRefundApplyChangeMsg;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CfPartnerRefundInfoMsg;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/8/19 2:15 PM
 * 案例退款顾问相关处理
 */
@Service
@Slf4j
public class CfCaseFinanceRefundServiceImpl implements CfCaseFinanceRefundService {

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private CfWaitDealTypeRecordService cfWaitDealTypeRecordService;

    @Autowired
    private CfCaseRefundTaskService cfCaseRefundTaskService;

    @Autowired
    private CfFinanceRefundFeignClient cfFinanceRefundFeignClient;

    @Autowired
    private CrowdFundingFeignDelegateImpl crowdFundingFeignDelegate;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Autowired
    private IOperateLogService operateLogService;

    private final List<Integer> refundTaskHaveDealList =
            Lists.newArrayList(CfRefundEnums.FollowStatus.USER_CONFIRM.getCode(), CfRefundEnums.FollowStatus.USER_CANCEL.getCode());

    @Override
    public void sendCaseRefundTask(CfBdCaseInfoDo cfBdCaseInfoDo, String traceNo) {
        String uniqueCode = cfBdCaseInfoDo.getUniqueCode();
        FeignResponse<List<CfRefundApplyV2Vo>> refundApplyV2ByTraceNo = cfFinanceRefundFeignClient.getRefundApplyV2ByTraceNo(Lists.newArrayList(traceNo));
        if (refundApplyV2ByTraceNo.notOk() || CollectionUtils.isEmpty(refundApplyV2ByTraceNo.getData())) {
            log.info("获取案例退款状态失败,traceNo:{}", traceNo);
            return;
        }
        // 创建案例退款任务
        CfWaitDealTypeRecordDO cfWaitDealTypeRecordDO = new CfWaitDealTypeRecordDO();
        cfWaitDealTypeRecordDO.setOrgId(cfBdCaseInfoDo.getOrgId());
        cfWaitDealTypeRecordDO.setUniqueCode(uniqueCode);
        cfWaitDealTypeRecordDO.setDealType(CfWaitDealTypeEnums.DealTypeEnum.case_refund.getCode());
        cfWaitDealTypeRecordService.insert(cfWaitDealTypeRecordDO);
        CfCaseRefundTaskDO cfCaseRefundTaskDO = new CfCaseRefundTaskDO();
        cfCaseRefundTaskDO.setCaseId(cfBdCaseInfoDo.getCaseId());
        cfCaseRefundTaskDO.setTraceNo(traceNo);
        cfCaseRefundTaskDO.setWaitDealId(cfWaitDealTypeRecordDO.getId());
        List<CfRefundApplyV2Vo> data = refundApplyV2ByTraceNo.getData();
        CfRefundApplyV2Vo refundApplyV2Vo = data.get(0);
        String refundMsgCodes = refundApplyV2Vo.getRefundMsgCodes();
        List<Integer> codes = Lists.newArrayList();
        for (String code : refundMsgCodes.split(",")) {
            codes.add(Integer.parseInt(code));
        }
        String applyReasonMsg = CfRefundEnums.CfRefundReasonV2.getByCodes(codes)
                .stream()
                .map(CfRefundEnums.CfRefundReasonV2::getDesc)
                .collect(Collectors.joining(","));
        String applyReason = refundApplyV2Vo.getApplyReason();
        if (StringUtils.isNotBlank(applyReason)) {
            cfCaseRefundTaskDO.setRefundReason(applyReasonMsg + "," + applyReason);
        } else {
            cfCaseRefundTaskDO.setRefundReason(applyReasonMsg);
        }
        cfCaseRefundTaskService.insert(cfCaseRefundTaskDO);
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(uniqueCode);
        appPushCrmCaseMsgService.sendRefundTaskCreate(volunteer, cfBdCaseInfoDo, cfCaseRefundTaskDO);
        // 发送24小时延时消息监测顾问待办任务处理状态
        long delayTime = DateUtils.MILLIS_PER_DAY;
        if (!applicationService.isProduction()) {
            delayTime = DateUtils.MILLIS_PER_MINUTE * 30;
        }
        mqProducerService.sendUpdateTaskStatusDelayMsg(cfCaseRefundTaskDO.getId(), delayTime);
    }

    @Override
    public void cancelCaseRefundMsg(CfBdCaseInfoDo cfBdCaseInfoDo, CfRefundApplyChangeMsg applyChangeMsg) {
        String uniqueCode = cfBdCaseInfoDo.getUniqueCode();
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(uniqueCode);
        // 更新用户取消退款时间
        cfCaseRefundTaskService.updateCacleTime(applyChangeMsg.getCancelTime(), applyChangeMsg.getTraceNo());
        CfCaseRefundTaskDO caseRefundTaskInfo = cfCaseRefundTaskService.getCaseRefundTaskInfo(applyChangeMsg.getTraceNo());
        if (ObjectUtils.isEmpty(caseRefundTaskInfo)) {
            log.info("未查询到案例退款任务,traceNo:{}", applyChangeMsg.getTraceNo());
            return;
        }
        cfWaitDealTypeRecordService.updateDealStatus(caseRefundTaskInfo.getWaitDealId(), CfWaitDealTypeEnums.DealStatusEnum.HAVA_DEAL.getCode(), CfRefundEnums.FollowStatus.DEFAULT.getCode());
        appPushCrmCaseMsgService.sendRefundTaskCancle(volunteer, cfBdCaseInfoDo, caseRefundTaskInfo);
    }

    @Override
    public void changeRefundTaskStatus(String traceNo) {
        CfCaseRefundTaskDO caseRefundTaskInfo = cfCaseRefundTaskService.getCaseRefundTaskInfo(traceNo);
        if (ObjectUtils.isEmpty(caseRefundTaskInfo)) {
            return;
        }
        // 接收到资金审核完成消息，如果顾问未处理任务需要更新任务状态为已处理
        int dealResult = caseRefundTaskInfo.getDealResult();
        if (refundTaskHaveDealList.contains(dealResult)) {
            return;
        }
        cfWaitDealTypeRecordService.updateDealStatus(caseRefundTaskInfo.getWaitDealId(), CfWaitDealTypeEnums.DealStatusEnum.HAVA_DEAL.getCode(), CfRefundEnums.FollowStatus.DEFAULT.getCode());
    }

    @Override
    public OpResult<String> modifyCaseRefundTaskStatus(Integer id, Integer dealStatus) {
        // 更新顾问处理状态
        cfCaseRefundTaskService.modifyCaseRefundTaskStatus(id, dealStatus);
        // 状态更新后需发送mq给资金
        CfCaseRefundTaskDO cfCaseRefundTaskDO = cfCaseRefundTaskService.getCaseRefundTaskInfoById(id);
        cfWaitDealTypeRecordService.updateDealStatus(cfCaseRefundTaskDO.getWaitDealId(), CfWaitDealTypeEnums.DealStatusEnum.HAVA_DEAL.getCode(), dealStatus);
        if (ObjectUtils.isEmpty(cfCaseRefundTaskDO)) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_ERROR);
        }
        CfPartnerRefundInfoMsg cfPartnerRefundInfoMsg = new CfPartnerRefundInfoMsg();
        cfPartnerRefundInfoMsg.setTraceNo(cfCaseRefundTaskDO.getTraceNo());
        cfPartnerRefundInfoMsg.setCaseId(cfCaseRefundTaskDO.getCaseId());
        cfPartnerRefundInfoMsg.setRefundResult(cfCaseRefundTaskDO.getDealResult());
        mqProducerService.sendmodifyCaseRefundTaskStatus(cfPartnerRefundInfoMsg);
        return OpResult.createFailResult(CfGrowthtoolErrorCode.SUCCESS);
    }

    @Override
    public CfWaitDealResultModel getCaseRefundDetails(CfCaseRefundDetailParam cfCaseRefundDetailParam) {
        CfWaitDealResultModel cfWaitDealResultModel = new CfWaitDealResultModel();
        List<CfCaseRefundDetailModel> cfCaseRefundDetailList = Lists.newArrayList();
        // 获取不同类型的待办数
        CfWaitDealResultModel typeCountByParams = cfWaitDealTypeRecordService.getWaitDealTypeCountByParams(cfCaseRefundDetailParam);
        if (Objects.isNull(typeCountByParams)) {
            return cfWaitDealResultModel;
        }
        cfWaitDealResultModel.setFinshWaitDealCount(typeCountByParams.getFinshWaitDealCount());
        cfWaitDealResultModel.setInitWaitDealCount(typeCountByParams.getInitWaitDealCount());
        cfWaitDealResultModel.setOverWaitDealCount(typeCountByParams.getOverWaitDealCount());

        //获取待办列表
        if (Objects.nonNull(cfCaseRefundDetailParam.getRefundReason())) {
            cfCaseRefundDetailParam.setRefundReasonDesc(CfWaitDealTypeEnums.RefundReasonEnum.parseDesc(cfCaseRefundDetailParam.getRefundReason()));
        }
        List<CfWaitDealTypeRecordDO> waitDealTypeInfoByParams = cfWaitDealTypeRecordService.getWaitDealTypeByParams(cfCaseRefundDetailParam);
        if (CollectionUtils.isEmpty(waitDealTypeInfoByParams)) {
            return cfWaitDealResultModel;
        }
        List<Long> waitDealIdList = waitDealTypeInfoByParams.stream().map(CfWaitDealTypeRecordDO::getId).collect(Collectors.toList());

        //获取资金退款待办列表
        cfCaseRefundDetailParam.setCaseRefundIds(waitDealIdList);
        Integer count = cfCaseRefundTaskService.getCaseRefundCountByParams(cfCaseRefundDetailParam);
        if (count == 0) {
            return cfWaitDealResultModel;
        }
        List<CfCaseRefundTaskDO> caseRefundTaskByIdList = cfCaseRefundTaskService.getCaseRefundTaskByIds(cfCaseRefundDetailParam);

        List<String> traceNoList = caseRefundTaskByIdList.stream().map(CfCaseRefundTaskDO::getTraceNo).collect(Collectors.toList());
        List<Integer> caseIdList = caseRefundTaskByIdList.stream().map(CfCaseRefundTaskDO::getCaseId).collect(Collectors.toList());

        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = crowdFundingFeignDelegate.getCrowdfundingListById(caseIdList)
                .stream()
                .collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (before, after) -> before));

        Map<Integer, CfBdCaseInfoDo> cfBdCaseInfoDoMap = cfBdCaseInfoService.listCaseInfoByCaseIds(caseIdList)
                .stream()
                .collect(Collectors.toMap(CfBdCaseInfoDo::getCaseId, Function.identity(), (before, after) -> before));

        FeignResponse<List<CfRefundApplyV2Vo>> refundApplyV2ByTraceNo = cfFinanceRefundFeignClient.getRefundApplyV2ByTraceNo(traceNoList);

        if (refundApplyV2ByTraceNo.notOk()) {
            log.info("获取案例退款状态失败,traceNoList:{}", traceNoList);
            return cfWaitDealResultModel;
        }

        Map<Integer, CfRefundApplyV2Vo> applyV2VoMap = refundApplyV2ByTraceNo.getData()
                .stream()
                .collect(Collectors.toMap(CfRefundApplyV2Vo::getCaseId, Function.identity(), (before, after) -> before));

        for (CfCaseRefundTaskDO cfCaseRefundTaskDO : caseRefundTaskByIdList) {
            int caseId = cfCaseRefundTaskDO.getCaseId();
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoMap.get(caseId);
            CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoDoMap.get(caseId);
            CfCaseRefundDetailModel cfCaseRefundDetailModel = new CfCaseRefundDetailModel();
            cfCaseRefundDetailModel.setId(cfCaseRefundTaskDO.getId());
            cfCaseRefundDetailModel.setCaseTitle(crowdfundingInfo.getTitle());
            cfCaseRefundDetailModel.setInfoUuid(crowdfundingInfo.getInfoId());
            cfCaseRefundDetailModel.setApplyName(cfBdCaseInfoDo.getPatientName());
            cfCaseRefundDetailModel.setPhone(shuidiCipher.decrypt(cfBdCaseInfoDo.getRaiserPhone()));
            cfCaseRefundDetailModel.setCreateTime(cfCaseRefundTaskDO.getCreateTime());
            cfCaseRefundDetailModel.setDealResult(cfCaseRefundTaskDO.getDealResult());
            cfCaseRefundDetailModel.setDealTime(cfCaseRefundTaskDO.getVolunteerDealTime());
            cfCaseRefundDetailModel.setCacelTime(cfCaseRefundTaskDO.getCancelRefundTime());
            cfCaseRefundDetailModel.setUrgeStatus(cfCaseRefundTaskDO.getUrgeStatus());
            CfRefundApplyV2Vo refundApplyV2Vo = applyV2VoMap.get(caseId);
            cfCaseRefundDetailModel.setCaseRefundStatus(refundApplyV2Vo.getApplyStatus());
            cfCaseRefundDetailModel.setApplyReason(cfCaseRefundTaskDO.getRefundReason());
            cfCaseRefundDetailList.add(cfCaseRefundDetailModel);
        }
        cfWaitDealResultModel.setCfCaseRefundDetailList(cfCaseRefundDetailList);
        cfWaitDealResultModel.setCount(count);
        return cfWaitDealResultModel;
    }

    @Override
    public int getWaitDealCount(CrowdfundingVolunteer volunteer) {
        // 只有登录用户职级级别为“普通职员、小助理、筹款伙伴、合同团队顾问” 可以查看
        if (!CrowdfundingVolunteerEnum.commonRoles.contains(volunteer.getLevel())) {
            return 0;
        }
        return cfWaitDealTypeRecordService.getWaitDealCount(volunteer.getUniqueCode());
    }

    @Override
    public void sendUrgeWaitDealMsg(Integer id, CrowdfundingVolunteer volunteer) {
        CfCaseRefundTaskDO caseRefundTaskInfoById = cfCaseRefundTaskService.getCaseRefundTaskInfoById(id);
        CfWaitDealTypeRecordDO waitDealTypeById = cfWaitDealTypeRecordService.getWaitDealTypeById(caseRefundTaskInfoById.getWaitDealId());
        CrowdfundingVolunteer waitVolunteer = cfVolunteerService.getByUniqueCode(waitDealTypeById.getUniqueCode());
        FeignResponse<List<CfRefundApplyV2Vo>> refundApplyV2ByTraceNo = cfFinanceRefundFeignClient.getRefundApplyV2ByTraceNo(Lists.newArrayList(caseRefundTaskInfoById.getTraceNo()));
        if (refundApplyV2ByTraceNo.notOk() || CollectionUtils.isEmpty(refundApplyV2ByTraceNo.getData())) {
            return;
        }

        List<CfRefundApplyV2Vo> caseRefundData = refundApplyV2ByTraceNo.getData();
        CfRefundApplyV2Vo cfRefundApplyV2Vo = caseRefundData.get(0);
        CfBdCaseInfoDo bdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoId(cfRefundApplyV2Vo.getCaseId());
        appPushCrmCaseMsgService.sendUrgeWaitDealMsg(volunteer, waitVolunteer, bdCaseInfoDo);
        cfCaseRefundTaskService.updateUrgeStatus(id);
    }
}
