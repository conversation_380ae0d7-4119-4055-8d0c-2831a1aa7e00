package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2021-08-30 6:00 下午
 * cf_bd_case_info 更新字段,同步信息接口
 **/
public interface ICfBdCaseSyncService {

    int updateCountIncrement(String infoId);

    void updateFundraiserShareNum(int caseId, Integer fundraiserShareNum);

    void incrFundraiserShareDays(int caseId);

    void updateDonateNum(int id, Integer donateNum);

    void updateValidCase(int id, int validCase);

    int batchUpdateValidCase(List<Integer> ids, int validCase);

    //有效案例达标判断
    void updateReachDonateDate(int caseId);

    void updateLocalCityTag(int caseId, int localCity, int remoteCity);

    //迁移组织下的数据,案例归属修改组织
    int moveCaseData(long originOrgId, long moveOrgId, String orgPath);

    //维护案例归属爱心伙伴
    int updatePartnerUniqueCodeById(Long id, String partnerUniqueCode);

    /**
     * 更新举报工单中的举报工单处理状态,举报工单处理时间
     * @param id
     * @param reportOrderHandleTime
     * @param reportOrderHandleStatus
     * @return
     */
    int updateReportOrderHandleInfo(Integer id, Date reportOrderHandleTime, int reportOrderHandleStatus);


    void syncReportInfo(CfBdCaseInfoDo cfBdCaseInfoDo);


    int saveOrUpdateCfBdCaseInfo(CfInfoExt cfInfoExt);

    int updateBdFollowedByInfoUuidWithUniqueCode(String infoUuid, String uniqueCode, int bdFollowed);

    void repairCfBdCaseInfoAmount(String startTime);

    int updateCfBdCaseInfoAmount(List<CfBdCaseInfoDo> cfBdCaseInfoDos);

    int updateCaseField(CfBdCaseInfoDo cfBdCaseInfoDo);

    void updateCaseStatus(CfBdCaseInfoDo cfBdCaseInfoDo);

    void updateNoOrgIdMis(String uniqueCode, String mis, int orgId, String orgPath);

    /**
     * 批量更新案例状态
     * @param cfBdCaseInfoDo
     * return 需要更新的总条数
     */
    int syncCaseStatusBatch(List<CfBdCaseInfoDo> cfBdCaseInfoDo);

    void updateValidAmount(int caseId, int validAmount, int validDonate);

    /*
     * 根据顾问角色判断是否直营案例
     */
    boolean getIsBdDirtctManage(int caseId);

}
