package com.shuidihuzhu.cf.cfgrowthtoolapi.service.mdc;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.dedicated.CfShuidichouWechatFriendDO;
import com.shuidihuzhu.client.cf.growthtool.model.CfWeChatFriendGroupInfo;
import com.shuidihuzhu.client.cf.growthtool.model.CfWeChatFriendInfo;
import com.shuidihuzhu.client.cf.growthtool.model.IdRangeModel;
import com.shuidihuzhu.client.cf.growthtool.model.PatientAddGroupModel;

import java.util.Date;
import java.util.List;

/**
 * @author: guohaidong
 * @create: 2021/09/22 17:05
 */
public interface IWeChatFriendService {
    /**
     * 根据加密手机号列表获取全部企业微信友好关联信息
     * @param phoneList 电话号码列表（加密）
     * @return 企业微信友好关联信息
     */
    List<CfShuidichouWechatFriendDO> getFriendInfoByEncryptPhone(List<String> phoneList);

    /**
     * 根据id获取企业微信好友关系
     * @param unionId
     * @return
     */
    CfShuidichouWechatFriendDO getFriendInfoById(Long unionId);

    /**
     * 根据externalUserId获取企业微信好友关系数据
     * @param externalUserId
     * @return
     */
    List<CfShuidichouWechatFriendDO> getFriendInfoByExternalUserId(String externalUserId);


    List<String> getFriendInfoUnionIds(List<String> unionIds);

    List<PatientAddGroupModel> getAddGroupInfoByEncryptPhones(List<String> phoneList);

    List<CfShuidichouWechatFriendDO> getBingYouFriendInfoByUnionId(String unionId);

    List<CfShuidichouWechatFriendDO> listFriendInfo(Long minId, Long maxId);

    IdRangeModel getIdRange4ListFriendInfo(String startTime, String endTime);

    List<CfShuidichouWechatFriendDO> listFriendInfoByUnionId(String unionId);

    List<CfShuidichouWechatFriendDO> getFriendByExternalUserIdAndQyWechatUserId(String externalUserId, String qyWechatUserId);

    List<CfShuidichouWechatFriendDO> getFriendInfoByTimeAndCallbackId(Date startDay, Date endDay, Integer callBackId);

    List<CfWeChatFriendGroupInfo> trimFriendGroupInfo(List<CfWeChatFriendInfo> weChatFriendInfos);
}
