package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.BdCorpFriendDO;

import java.util.List;

/**
 * bd添加微信好友信息(BdCorpFriend)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-15 18:10:18
 */
public interface BdCorpFriendService {

    BdCorpFriendDO queryById(long id);

    int insert(BdCorpFriendDO bdCorpFriendDO);

    void batchInsert(List<BdCorpFriendDO> bdCorpFriendDOS);

    int update(BdCorpFriendDO bdCorpFriendDO);

    List<BdCorpFriendDO> listFriendByUserId(String corpId, String userId);

    void deleteByIds(List<Long> ids);

}
