package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfOrgHospitalRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmBdLocationConditionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmGpsDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmVisitingHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmBdGpsSimpleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmGpsVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmLocationConfitionVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfCrmVisitingHospitalVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCrmCampaignMapService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmBdLocationConditionDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmGpsDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmVisitingHospitalDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfOrgHospitalRelationDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.olap.client.BdCrmCampaignMapFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2020-04-23 16:17
 */
@Service
public class CfCrmCampaignMapServiceImpl implements ICfCrmCampaignMapService {

	@Autowired
	private CfCrmBdLocationConditionDao cfCrmBdLocationConditionDao;
	@Autowired
	private CfCrmGpsDao cfCrmGpsDao;
	@Autowired
	private CfCrmVisitingHospitalDao cfCrmVisitingHospitalDao;
	@Autowired
	private CfOrgHospitalRelationDao cfOrgHospitalRelationDao;
	@Autowired
	private BdCrmCampaignMapFeignClient bdCrmCampaignMapFeignClient;
	@Autowired
	private ICfVolunteerService cfVolunteerService;

	@Override
	public long getCountByDateTime(String dateTime) {
		return cfCrmBdLocationConditionDao.getCountByDateTime(dateTime);
	}

	@Override
	public int insertCfCrmBdLocationConditionDO(CfCrmBdLocationConditionDO cfCrmBdLocationConditionDO){
		return cfCrmBdLocationConditionDao.insert(cfCrmBdLocationConditionDO);
	}

	@Override
	public int batchInsert(List<CfCrmBdLocationConditionDO> modelList){
		if (CollectionUtils.isEmpty(modelList)){
			return 0;
		}
		// 查询是否已经存在DB
		List<String> uniqueCodeListInDB = getMisListByDateTimeWithMisList(modelList.stream().map(CfCrmBdLocationConditionDO::getUniqueCode).collect(Collectors.toList()), DateUtil.getCurrentDateStr());
		// 把存在DB的数据 过滤掉
		List<CfCrmBdLocationConditionDO> needBatchInsert = modelList.stream().filter(model -> !uniqueCodeListInDB.contains(model.getUniqueCode())).collect(Collectors.toList());
		return cfCrmBdLocationConditionDao.batchInsert(needBatchInsert);
	}

	@Override
	public int updateCfCrmBdLocationConditionDO(CfCrmBdLocationConditionDO model) {
		return cfCrmBdLocationConditionDao.update(model);
	}

	@Override
	public int insertCfCrmGpsDO(CfCrmGpsDO cfCrmGpsDO){
		return cfCrmGpsDao.insert(cfCrmGpsDO);
	}

	@Override
	public int updateIsShow(String dateTime,
							String volunteerUniqueCode){
		return cfCrmGpsDao.updateIsShow(dateTime, volunteerUniqueCode);
	}

	@Override
	public CfCrmBdLocationConditionDO getCfCrmBdLocationConditionDO(String dateTime, String uniqueCode){
		return cfCrmBdLocationConditionDao.getCfCrmBdLocationConditionDO(dateTime, uniqueCode);
	}

	/**
	 * 批量获取 CfCrmGpsDO 去更新其中的location
	 * @param dateTime
	 * @param currentId
	 * @return
	 */
	@Override
	public List<CfCrmGpsDO> getCfCrmGpsDO(String dateTime, long currentId){
		return cfCrmGpsDao.getCfCrmGpsDO(dateTime, currentId);
	}

	@Override
	public int batchUpdateLocation(List<CfCrmGpsDO> needUpdateList) {
		return cfCrmGpsDao.batchUpdateLocation(needUpdateList);
	}

	@Override
	public int updateCfCrmGpsStatus(String dateTime, String mis, int status) {
		return cfCrmGpsDao.updateCfCrmGpsStatus(dateTime,mis,status);
	}

	@Override
	public List<CfCrmGpsVo> getCfCrmGpsListByUniqueCodeList(List<String> uniqueCodeList, String currentDateStr) {
		if (CollectionUtils.isEmpty(uniqueCodeList)){
			return Lists.newArrayList();
		}
		return cfCrmGpsDao.getCfCrmGpsListByUniqueCodeList(uniqueCodeList,currentDateStr);
	}

	@Override
	public long getLocationConditionCount(List<String> unqiueCodeList,int type, String currentDateStr) {
		if (CollectionUtils.isEmpty(unqiueCodeList)){
			return 0L;
		}
		return cfCrmBdLocationConditionDao.getLocationConditionCount(unqiueCodeList,type,currentDateStr);
	}

	@Override
	public List<CfCrmLocationConfitionVo> getLocationConditionList(List<String> unqiueCodeList,int type, String currentDateStr, int pageNo, int pageSize) {
		if (CollectionUtils.isEmpty(unqiueCodeList)){
			return Lists.newArrayList();
		}
		int offset = (pageNo-1)*pageSize;
		return cfCrmBdLocationConditionDao.getLocationConditionList(unqiueCodeList,type,currentDateStr,offset,pageSize);
	}

	@Override
	public List<CfCrmGpsVo> getBdGps(String volunteerUniqueCode,String dateTime, boolean needCalcStayTime) {
		return cfCrmGpsDao.getBdGps(volunteerUniqueCode,dateTime, needCalcStayTime? CfCrmGpsDO.UploadWayEnum.listNeedStayTimeUploadWay() : Lists.newArrayList()).stream().sorted(Comparator.comparing(CfCrmGpsVo::getCreateTime)).collect(Collectors.toList());
	}

	@Override
	public List<CfCrmGpsVo> getBdGpsByUniqueCodeList(List<String> uniqueCodeList, String dateTime, boolean needCalcStayTime) {
		if (CollectionUtils.isEmpty(uniqueCodeList)) {
			return Lists.newArrayList();
		}
		List<Integer> uploadWaysList = needCalcStayTime ? CfCrmGpsDO.UploadWayEnum.listNeedStayTimeUploadWay() : Lists.newArrayList();
		List<CfCrmGpsVo> result = Lists.newArrayList();
		List<List<String>> partition = ListUtils.partition(uniqueCodeList, 50);
		for (List<String> list : partition) {
			List<CfCrmGpsVo> cfCrmGpsVoList = cfCrmGpsDao.getBdGpsByUniqueCodeList(list, dateTime, uploadWaysList);
			if (CollectionUtils.isNotEmpty(cfCrmGpsVoList)) {
				result.addAll(cfCrmGpsVoList);
			}
		}
		return result.stream().sorted(Comparator.comparing(CfCrmGpsVo::getCreateTime)).collect(Collectors.toList());
	}

	@Override
	public List<CfCrmBdGpsSimpleModel> listBdGpsByVHospitalCode(String hospitalName, String dateTime, String startTime, String endTime, boolean needCalcStayTime) {
		return cfCrmGpsDao.listBdGpsByVHospitalCode(hospitalName, dateTime, startTime, endTime, needCalcStayTime ? CfCrmGpsDO.UploadWayEnum.listNeedStayTimeUploadWay() : Lists.newArrayList())
				.stream()
				.sorted(Comparator.comparing(CfCrmBdGpsSimpleModel::getCreateTime))
				.collect(Collectors.toList());
	}

	@Override
	public List<CfCrmBdGpsSimpleModel> listBdGpsByVHospitalCodes(List<String> hospitalNameList, String dateTime, String startTime, String endTime, boolean needCalcStayTime) {
		List<Integer> uploadWaysList = needCalcStayTime ? CfCrmGpsDO.UploadWayEnum.listNeedStayTimeUploadWay() : Lists.newArrayList();
		List<CfCrmBdGpsSimpleModel> result = Lists.newArrayList();
		List<List<String>> partition = ListUtils.partition(hospitalNameList, 100);
		for (List<String> list : partition) {
			result.addAll(cfCrmGpsDao.listBdGpsByVHospitalCodes(list, dateTime, startTime, endTime, uploadWaysList));
		}
		return result.stream().sorted(Comparator.comparing(CfCrmBdGpsSimpleModel::getCreateTime)).collect(Collectors.toList());
	}

	@Override
	public List<CfCrmBdGpsSimpleModel> listBdGpsByVHospitalCodeWithUniqueCodeList(String hospitalName, List<String> uniqueCodeList, String dateTime, String startTime, String endTime, boolean needCalcStayTime) {
		if (CollectionUtils.isEmpty(uniqueCodeList)) {
			return Lists.newArrayList();
		}
		return cfCrmGpsDao.listBdGpsByVHospitalCodeWithUniqueCodeList(hospitalName, uniqueCodeList, dateTime, startTime, endTime, needCalcStayTime? CfCrmGpsDO.UploadWayEnum.listNeedStayTimeUploadWay() : Lists.newArrayList()).stream().sorted(Comparator.comparing(CfCrmBdGpsSimpleModel::getCreateTime)).collect(Collectors.toList());
	}

	@Override
	public List<CfCrmBdGpsSimpleModel> listBdGpsByVHospitalNameList(List<String> hospitalNameList, String dateTime, String startTime, String endTime, boolean needCalcStayTime) {
		if (CollectionUtils.isEmpty(hospitalNameList)) {
			return Lists.newArrayList();
		}
		List<Integer> uploadWaysList = needCalcStayTime ? CfCrmGpsDO.UploadWayEnum.listNeedStayTimeUploadWay() : Lists.newArrayList();
		List<CfCrmBdGpsSimpleModel> result = Lists.newArrayList();
		List<List<String>> partition = ListUtils.partition(hospitalNameList, 100);
		for (List<String> list : partition) {
			result.addAll(cfCrmGpsDao.listBdGpsByVHospitalNameList(list, dateTime, startTime, endTime, uploadWaysList));
		}
		return result.stream().sorted(Comparator.comparing(CfCrmBdGpsSimpleModel::getCreateTime)).collect(Collectors.toList());
	}

	@Override
	public List<CfCrmGpsDO> listGpsByUploadWays(String volunteerUniqueCode,
												List<String> dateTimeList,
												List<Integer> uploadWaysList){
		return cfCrmGpsDao.listGpsByUploadWays(volunteerUniqueCode, dateTimeList, uploadWaysList);
	}

	@Override
	public List<String> listDateTimeByUploadWaysWithDateTimeList(String volunteerUniqueCode,
																 List<String> dateTimeList,
																 int uploadWays){
		return cfCrmGpsDao.listDateTimeByUploadWaysWithDateTimeList(volunteerUniqueCode, dateTimeList, uploadWays);
	}
	@Override
	public int insertfCrmVisitingHospital(CfCrmVisitingHospitalDO cfCrmVisitingHospitalDO){
		return cfCrmVisitingHospitalDao.insert(cfCrmVisitingHospitalDO);
	}

	@Override
	public int updateCrmVisitingHospital(CfCrmVisitingHospitalDO cfCrmVisitingHospitalDO){
		return cfCrmVisitingHospitalDao.update(cfCrmVisitingHospitalDO);
	}

	@Override
	public CfCrmVisitingHospitalDO getLatelyModel(String dateTime,
												  String volunteerUniqueCode,
												  String vhospitalCode){
		return cfCrmVisitingHospitalDao.getLatelyModel(dateTime, volunteerUniqueCode, vhospitalCode);
	}

	@Override
	public List<CfCrmVisitingHospitalVO> visitingHospitals(String volunteerUniqueCode, String currentDateStr) {
		return cfCrmVisitingHospitalDao.visitingHospitals(volunteerUniqueCode,currentDateStr);
	}

	@Override
	public List<CfCrmVisitingHospitalVO> batchVisitingHospitals(List<String> volunteerUniqueCodes, String currentDateStr) {
		return cfCrmVisitingHospitalDao.batchVisitingHospitals(volunteerUniqueCodes, currentDateStr);
	}

	@Override
	public int saveCfOrgHospitalRelation(String vhospitalCode, long orgId){
		CfOrgHospitalRelationDO cfOrgHospitalRelation = cfOrgHospitalRelationDao.getCfOrgHospitalRelation(vhospitalCode, orgId);
		if (cfOrgHospitalRelation!=null){
			return 1;
		}
		bdCrmCampaignMapFeignClient.addOrgHospitalRelation(vhospitalCode,orgId);
		return 1;
	}

	/**
	 * 逻辑删除 符合条件的数据
	 * @param vhospitalCode
	 * @param orgIds
	 * @return
	 */
	@Override
	public int updateCfOrgHospitalRelationIsDelete(String vhospitalCode, List<Long> orgIds){
		Response<Integer> response = bdCrmCampaignMapFeignClient.OrgHospitalRelationUpdateIsDelete(vhospitalCode, orgIds);
		if(response.notOk()){
			return 0;
		}
		return response.getData();
	}

	/**
	 * 为符合条件的数据 设置 is_target
	 * @param vhospitalCode
	 * @param orgIds
	 * @param isTarget
	 * @return
	 */
	@Override
	public int updateCfHospitalInterviewCaseIsTarget(String vhospitalCode, List<Long> orgIds, int isTarget){
		Response<Integer> response =  bdCrmCampaignMapFeignClient.updateIsTarget(vhospitalCode, orgIds, isTarget);
		if(response.notOk()){
			return 0;
		}
		return response.getData();
	}

	@Override
	public List<CfCrmVisitingHospitalDO> listVisitHospitalCode(String volunteerUniqueCode, String startDate, String endDate) {
		if (StringUtils.isBlank(volunteerUniqueCode) || StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
			return Lists.newArrayList();
		}
		return cfCrmVisitingHospitalDao.listHospitalCode(volunteerUniqueCode, startDate, endDate);
	}

	@Override
	public List<CfCrmGpsVo> listBdGpsByDateTimeWithCreateTime(String uniqueCode, String dateTime, String startTime, String endTime, boolean needCalcStayTime) {
		return cfCrmGpsDao.listBdGpsByDateTimeWithCreateTime(uniqueCode, dateTime, startTime, endTime, needCalcStayTime? CfCrmGpsDO.UploadWayEnum.listNeedStayTimeUploadWay() : Lists.newArrayList());
	}

	@Override
	public List<CfCrmGpsVo> listBdGpsByUniqueCodeList(List<String> uniqueCodeList, String dateTime, String startTime, String endTime, boolean needCalcStayTime) {
		List<Integer> uploadWaysList = needCalcStayTime ? CfCrmGpsDO.UploadWayEnum.listNeedStayTimeUploadWay() : Lists.newArrayList();
		List<CfCrmGpsVo> result = Lists.newArrayList();
		List<List<String>> partition = Lists.partition(uniqueCodeList, 100);
		for (List<String> list : partition) {
			result.addAll(cfCrmGpsDao.listBdGpsByUniqueCodeList(list, dateTime, startTime, endTime, uploadWaysList));
		}
		return result;
	}


	@Override
	public List<CfCrmGpsVo> listLastGpsInfo(String startTime, List<Integer> roles, int nums, int size) {
		return cfCrmGpsDao.listLastGpsInfo(startTime, roles, nums * size, size);
	}

	private List<String> getMisListByDateTimeWithMisList(List<String> uniqueCodeList,
														String dateTime){
		return cfCrmBdLocationConditionDao.getMisListByDateTimeWithMisList(uniqueCodeList, dateTime);
	}
}
