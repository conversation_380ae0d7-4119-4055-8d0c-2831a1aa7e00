package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdQyWxRobotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywx.QywxRobotDetailModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.OrganizationFormatUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 企微机器人验证服务
 *
 * <AUTHOR>
 * @date 2024
 */
@Service
@Slf4j
public class QywxRobotValidationService {

    @Autowired
    private BdQyWxRobotService qywxRobotService;

    @Autowired
    private ICrmSelfBuiltOrgReadService selfBuiltOrgReadService;


    /**
     * 检查组织重复情况
     *
     * @param saveParam 保存参数
     * @return 如果发现重复返回错误响应，否则返回null
     */
    public Response<Void> checkOrganizationDuplication(QywxRobotDetailModel saveParam) {
        try {
            if (saveParam.getCaseSourceType() != 0 || CollectionUtils.isEmpty(saveParam.getOrgIdList())) {
                return null; // 非线下机器人或无组织信息，跳过检查
            }

            List<BdQyWxRobotDO> existingRobots = qywxRobotService.listByCaseSourceType(0)
                    .stream()
                    .filter(item -> item.getAccountStatus() == 0)
                    .collect(Collectors.toList());
            List<Long> newOrgIdList = saveParam.getOrgIdList();

            // 获取当前新增机器人的所有子组织ID（包含自身）
            Set<Long> newAllOrgIds = newOrgIdList.stream()
                    .flatMap(orgId -> selfBuiltOrgReadService.listAllSubOrgIncludeSelf(orgId).stream()
                            .map(BdCrmOrganizationDO::getId))
                    .collect(Collectors.toSet());

            // 检查已存在的机器人是否有重复的组织ID
            for (BdQyWxRobotDO existingRobot : existingRobots) {
                // 跳过当前正在编辑的机器人（更新场景）
                if (saveParam.getId() > 0 && existingRobot.getId() == saveParam.getId()) {
                    continue;
                }

                // 获取已存在机器人的所有子组织ID
                Set<Long> existingAllOrgIds = existingRobot.parseOrgIdList().stream()
                        .flatMap(orgId -> selfBuiltOrgReadService.listAllSubOrgIncludeSelf(orgId).stream()
                                .map(BdCrmOrganizationDO::getId))
                        .collect(Collectors.toSet());

                // 检查组织ID是否有重复
                Set<Long> duplicateOrgIds = new HashSet<>(newAllOrgIds);
                duplicateOrgIds.retainAll(existingAllOrgIds);

                if (!duplicateOrgIds.isEmpty()) {
                    return buildDuplicationErrorResponse(existingRobot, duplicateOrgIds, saveParam.getRobotName());
                }
            }

            log.info("组织路径检查通过: 新增机器人={}, 涉及组织数={}", saveParam.getRobotName(), newAllOrgIds.size());
            return null; // 没有重复，返回null表示检查通过

        } catch (Exception e) {
            log.error("组织重复检查异常: {}", saveParam, e);
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 检查企微账号ID是否重复
     *
     * @param externalUserId 企微账号ID
     * @param excludeId      排除的机器人ID（更新场景）
     * @return 如果重复返回错误响应，否则返回null
     */
    public Response<Void> checkExternalUserIdDuplication(String externalUserId, long excludeId) {
        if (StringUtil.isBlank(externalUserId)) {
            return null;
        }

        try {
            BdQyWxRobotDO existingRobot = qywxRobotService.queryByExternalUserId(externalUserId);
            if (existingRobot != null && !Objects.equals(existingRobot.getId(), excludeId)) {
                log.warn("账号ID已存在: {}", externalUserId);
                return NewResponseUtil.makeError(CfGrowthtoolErrorCode.EXIST);
            }
            return null;

        } catch (Exception e) {
            log.error("企微账号ID重复检查异常: {}", externalUserId, e);
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 构建组织重复错误响应
     */
    private Response<Void> buildDuplicationErrorResponse(BdQyWxRobotDO existingRobot,
                                                         Set<Long> duplicateOrgIds,
                                                         String newRobotName) {
        try {
            // 获取重复组织的完整路径
            Map<Long, String> duplicateOrgChainMap = selfBuiltOrgReadService.listChainByOrgIdsWithDefaultSplitter(new ArrayList<>(duplicateOrgIds));

            // 格式化组织名称并去重
            Set<String> formattedOrgNames = duplicateOrgChainMap.values().stream()
                    .map(OrganizationFormatUtil::formatOrgChainName)
                    .filter(StringUtil::isNotBlank)
                    .collect(Collectors.toSet());

            String orgNamesStr = String.join("、", formattedOrgNames);
            String errorMsg = String.format("编辑失败，识别到企微ID%d名称%s正在负责%s组织",
                    existingRobot.getId(), existingRobot.getExternalUserId(), orgNamesStr);

            log.warn("机器人组织重复: 当前机器人={}, 已存在机器人={}, 重复组织={}",
                    newRobotName, existingRobot.getRobotName(), formattedOrgNames);

            return NewResponseUtil.makeFail(errorMsg);

        } catch (Exception e) {
            log.error("构建重复错误响应异常", e);
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_ERROR);
        }
    }
} 