package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrFeedbackDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.GrFeedbackService;
import com.shuidihuzhu.cf.dao.gr.GrFeedbackDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2021-05-20 4:44 下午
 **/
@Service
public class GrFeedbackServiceImpl implements GrFeedbackService {

    @Autowired
    private GrFeedbackDao grFeedbackDao;

    @Override
    public int insert(GrFeedbackDO feedbackDO) {
        if (feedbackDO == null) {
            return 0;
        }
        return grFeedbackDao.insert(feedbackDO);
    }

    @Override
    public List<GrFeedbackDO> listByGrCustomerId(int customerId) {
        return grFeedbackDao.listByGrCustomerId(customerId);
    }
}
