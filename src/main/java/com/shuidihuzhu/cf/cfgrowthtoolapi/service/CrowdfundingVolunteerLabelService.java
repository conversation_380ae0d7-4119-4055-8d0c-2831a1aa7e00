package com.shuidihuzhu.cf.cfgrowthtoolapi.service;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CrowdfundingVolunteerLabelVo;
import com.shuidihuzhu.client.cf.growthtool.model.PageReturnModel;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface CrowdfundingVolunteerLabelService {
    CrowdfundingVolunteerLabelVo queryByUniqueCode(String uniqueCode);
    /**
     * 查询在职的顾问的标签
     * @param name
     * @return
     */
    List<CrowdfundingVolunteerLabelVo> queryVolunteerByNameOnWorking(String name);
    PageReturnModel<CrowdfundingVolunteerLabelVo> queryByName(String name, int pageNum, int pageSize);
    /**
     * 导入数据
     */
    Response<Void> importData(MultipartFile file);

    /**
     * 导出数据
     */
    Response<Void> exportData(long seaUserId);

    Response<Void> addLabel(CrowdfundingVolunteerLabelVo volunteerLabelVo);

    Response<Void> deleteLabel(long id);
}
