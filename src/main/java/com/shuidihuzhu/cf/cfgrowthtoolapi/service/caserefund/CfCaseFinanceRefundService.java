package com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.caserefund.CfWaitDealResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.caserefund.CfCaseRefundDetailParam;
import com.shuidihuzhu.cf.finance.mq.CfRefundApplyChangeMsg;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

/**
 * <AUTHOR>
 * @Date 2022/8/19 2:15 PM
 */
public interface CfCaseFinanceRefundService {

    /*
    * 接收资金处理退款消息给顾问发消息
    */
    void sendCaseRefundTask(CfBdCaseInfoDo cfBdCaseInfoDo, String traceNo);

    /*
    * 发送退款申请取消消息给顾问
    */
    void cancelCaseRefundMsg(CfBdCaseInfoDo cfBdCaseInfoDo, CfRefundApplyChangeMsg applyChangeMsg);

    /*
    * 资金退款状态变更后需更改任务状态
    */
    void changeRefundTaskStatus(String traceNo);

    /*
     * 案例退款待办任务状态变更
     */
    OpResult<String> modifyCaseRefundTaskStatus(Integer id, Integer dealStatus);

    CfWaitDealResultModel getCaseRefundDetails(CfCaseRefundDetailParam cfCaseRefundDetailParam);

    // 根据顾问查询待办数
    int getWaitDealCount(CrowdfundingVolunteer volunteer);

    // 管理者待办催办
    void sendUrgeWaitDealMsg(Integer id, CrowdfundingVolunteer volunteer);


}
