package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdCrmAiChangeParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdCrmAiGenerateParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.ChatRecord;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatStreamResult;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * @Description: ai生成内容
 * @Author: panghairui
 * @Date: 2025/5/7 14:49
 */
public interface BdCrmAiAssistService {

    /**
     * ai流式生成文章
     */
    Flux<ChatChunk<ChatStreamResult>> streamGenerateContentByAi(BdCrmAiGenerateParam bdCrmAiGenerateParam, String uniqueCode);

    Flux<ChatChunk<ChatStreamResult>> streamChangeContentByAi(BdCrmAiChangeParam bdCrmAiChangeParam, String uniqueCode);

    List<ChatRecord> getAiChatHistory(Integer modelType, String uniqueCode);

}
