package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgAndCityRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.BdOrgAndCityRelationModel;

import java.util.List;

public interface IBdCrmOrgAndCityRelationService {

    List<BdCrmOrgAndCityRelationDO> getByOrgId(long id);

    void batchAdd(long orgId, List<BdOrgAndCityRelationModel.BindCityModel> bindList);

    int batchDeleteByIds(long orgId, List<Integer> cityIdList);

    List<BdCrmOrgAndCityRelationDO> getAll();

    List<BdCrmOrgAndCityRelationDO> getByOrgIds(List<Long> orgIdList);

    void updateMarketShareObjective(Long orgId, String marketShareObjective);

}
