package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerCaseBillDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerCycleDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerSnapshotDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.CfPartnerCaseBillService;
import com.shuidihuzhu.cf.dao.lovepartner.CfPartnerCaseBillDao;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */

@Service
@Slf4j
public class CfPartnerCaseBillServiceImpl implements CfPartnerCaseBillService {

    @Autowired
    private CfPartnerCaseBillDao partnerCaseBillDao;

    @Override
    public int batchInsert(List<CfBdCaseInfoDo> bdCaseInfoList, CfPartnerSnapshotDo partnerSnapshotDo, CfPartnerCycleDo cfPartnerCycleDo) {
        if (CollectionUtils.isEmpty(bdCaseInfoList)){
            return 0;
        }
        return partnerCaseBillDao.batchInsert(bdCaseInfoList,partnerSnapshotDo,cfPartnerCycleDo);
    }

    @Override
    public List<CfPartnerCaseBillDo> listByCycle(int cycleId) {
        return partnerCaseBillDao.listByCycleId(cycleId);
    }

    @Override
    public int deleteByCycleId(long cycleId) {
        return partnerCaseBillDao.deleteByCycleId(cycleId);
    }
}
