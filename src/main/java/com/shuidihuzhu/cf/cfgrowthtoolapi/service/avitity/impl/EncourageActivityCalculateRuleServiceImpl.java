package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.EncourageActivityCalculateRuleDO;
import com.shuidihuzhu.cf.dao.avtivity.EncourageActivityCalculateRuleDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.EncourageActivityCalculateRuleService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 激励活动规则表(EncourageActivityCalculateRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-23 15:32:56
 */
@Service("encourageActivityCalculateRuleService")
public class EncourageActivityCalculateRuleServiceImpl implements EncourageActivityCalculateRuleService {
   
    @Resource
    private EncourageActivityCalculateRuleDao encourageActivityCalculateRuleDao;

    @Override
    public EncourageActivityCalculateRuleDO queryById(long id) {
        return encourageActivityCalculateRuleDao.queryById(id);
    }
    

    @Override
    public int insert(EncourageActivityCalculateRuleDO encourageActivityCalculateRule) {
        return encourageActivityCalculateRuleDao.insert(encourageActivityCalculateRule);
    }

    @Override
    public int update(EncourageActivityCalculateRuleDO encourageActivityCalculateRule) {
        return encourageActivityCalculateRuleDao.update(encourageActivityCalculateRule);
    }

    @Override
    public boolean deleteById(long id) {
        return encourageActivityCalculateRuleDao.deleteById(id) > 0;
    }

    @Override
    public List<EncourageActivityCalculateRuleDO> listByActivityIds(List<Long> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return List.of();
        }
        return encourageActivityCalculateRuleDao.listByActivityIds(activityIds);
    }

    @Override
    public boolean deleteByActivityId(long activityId) {
        return encourageActivityCalculateRuleDao.deleteByActivityId(activityId) > 0;
    }

    @Override
    public EncourageActivityCalculateRuleDO queryByActivityIdAndRuleId(long activityId, int ruleId) {
        return encourageActivityCalculateRuleDao.queryByActivityIdAndRuleId(activityId, ruleId);
    }

    @Override
    public List<EncourageActivityCalculateRuleDO> listByActivityId(long activityId) {
        return encourageActivityCalculateRuleDao.listByActivityId(activityId);
    }
}
