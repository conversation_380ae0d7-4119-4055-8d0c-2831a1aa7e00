package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.PunchCardVo;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;

/**
 * @author: wanghui
 * @create: 2020-04-29 17:49
 */
@Service
@RefreshScope
@Data
@Slf4j
public class ApolloService {
	@Value("${apollo.bdcrm.gps.time:8-20}")
	private String gpsTime;

	@Value("${apollo.bdcrm.gps.retentiontime:false}")
	private boolean retentiontime;

	@Value("${apollo.bdcrm.gps.cfvinfo.blacklist:wanghui}")
	private String cfvinfoblacklist;
	@Value("${apollo.fangbiandai.testlist:}")
	private String fangbiandaiTest;
	@Value("${apollo.bdcrm.virtualphone:false}")
	private boolean enableVirtualPhone;
	@Value("${apollo.fangbiandai.lowinventory:10}")
	private Integer lowinventory;
	/**
	 * 检测重复代录入小时数
	 */
	@Value("${apollo.growthtool.checkrepeat.hours:-24}")
	private Integer checkRepeatHours;
	/**
	 * 检测重复代录入分钟数
	 */
	@Value("${apollo.growthtool.checkrepeat.minutes:0}")
	private Integer checkRepeatMinutes;

	@Value("${apollo.enforce.use.redis:false}")
	private boolean useRedisQuery;
	@Value("${apollo.fangbiandai.thirdType:54}")
	private Integer fangbiandaiThirdType;


	@Value("${apollo.workstatus.noneed.alarm:}")
	private String workStatusNoNeedAlarmMis;

	//固定的城市对人的信息
	@Value("${apollo.case.city.list:}")
	private String apolloCaseCityList;

	//可修改的城市对人的信息
	@Value("${apollo.case.city.list.can.modify:济南市,西安市}")
	private String apolloCaseCityCanModify;

	@Value("${apollo.case.hospital.list.can.modify:济南市,西安市}")
	private String apolloCaseHospitalCanModify;

	@Value("${third.interface.url.host:gateway.test.isrm.going-link.com}")
	private String thirdHost;

	@Value("${third.param.applicationCode:GOINGLINK_CLOUD_TEST}")
	private String thirdParamApplicationCode;

	@Value("${third.param.applicationGroupCode:PUBLIC_CLOUD}")
	private String thirdApplicationGroupCode;

	@Value("${third.param.userName:77474470}")
	private String thirdUserName;

	@Value("${third.param.clientId:srm-interface-client}")
	private String thirdClientId;

	@Value("${third.client.secret:secret}")
	private String clientSecret;
	@Value("${apollo.param.campaignmap.endtimeafter1day:true}")
	private boolean endtimeafter1day;
	@Value("${apollo.param.objective.show.orgPathList:}")
	private List<String> showOrgPathList;


	public boolean getEndtimeafter1day() {
		return endtimeafter1day;
	}


	@Value("${apollo.case.valid.amount:300000}")
	private Integer validAmount;

	@Value("${apollo.case.valid.donate.num:100}")
	private Integer validDonateNum;

	@Value("${apollo.singel.manul.import.case.bonus:10000}")
	private Long singleManulImportCaseBonus;
	@Value("${apollo.gps.retentiontime:30}")
	private int retentiontimeInterval;

	@Value("${apollo.has.check.city:北京市,太原市}")
	private String hasCheckCity;

	@Value("${apollo.third.sso.url:http://mobile.dev.isrm.going-link.com/WxCover/?state=wdvtGoldFish-90e4163459ab489b9b5193cab33ecf70}")
	private String thirdSsoUrl;
	@Value("${apollo.bd.crm.illeagal.gps:0.0}")
	private String illeagalGps;

	@Value("${apollo.growthtool.diseasenorm.version:2}")
	private int diseaseNormVersion;


	@Value("${apollo.greenchannel.onepublic.permission:{1:'张宇坤,张月茹,郑宏宇',2:'成校婷,张晔,李瑞鑫',3:'郭庆',4:'李冬冬,乔帅杰,张宇坤',5:'魏君,张月茹,郑宏宇',6:'张宇坤,李冬冬,乔帅杰',7:'张宇坤,郭庆,张月茹,郑宏宇'}}")
	private String publicGreenChannelPermission;


	@Value("${apollo.greenchannel.doctor.permission:{1:'张宇坤,张芮齐,陈桢,郑宏宇',2:'成校婷,张晔,李瑞鑫',3:'郭庆,张宇坤,张芮齐,陈桢,郑宏宇',4:'张宇坤,张芮齐,陈桢,郑宏宇',7:'郭庆,张宇坤,张芮齐,陈桢,郑宏宇'}}")
	private String doctorGreenChannelPermission;

	@Value("${apollo.greenchannel.onepublic.robot:cd14930d-6be5-4f16-bf12-ae912a2c16c7}")
	private String publicGreenChannelRobort;

	@Value("${apollo.greenchannel.doctor.robot:cd14930d-6be5-4f16-bf12-ae912a2c16c7}")
	private String doctorGreenChannelRobot;

	@Value("${apollo.growthtool.bd.kpiAlarmUsers:fengxuan}")
	private String bdKpiAlarmUsers;

	@Value("${apollo.growthtool.bd.kpi.outerShowFlag:0}")
	private int outerShowFlag;

	@Value("${apollo.growthtool.cfmaster.fegin.switch:false}")
	private Boolean feginSwith;


	@Value("${apollo.growthtool.case.qrcode.shareTitle:救人！请大家帮助！帮转发！帮扩散！}")
	private String shareTitle;

	@Value("${apollo.growthtool.case.qrcode.shareContent:大家好，现急需费用救治，请大家帮帮忙转发证实一下！谢谢大家了。您的每一次转发和证实，对我都是天大的帮助，感激不尽！}")
	private String shareContent;

	@Value("${apollo.growthtool.qsc.data.switch:false}")
	private Boolean qscDataSwith;

	@Value("${apollo.growthtool.scan.record.scanRecordStartTime:2020-12-09 16:00:00}")
	private String scanRecordStartTime;

	//获取第几层大区,默认为第一层
	@Value("${apollo.growthtool.redress.hospital.area.level:1}")
	private int redressHospitalLevel;

	@Value("${growthtool.data.permission.switch:true}")
	private Boolean dataPermissionSwitch;

	@Value("${growthtool.city.permission.switch:true}")
	private Boolean cityIdPermissionSwitch;

	//测试环境： 41ba9aa0-9bc7-418e-9b26-16c27286bd51
	@Value("${growthtool.hospital.apply.robert:f5cd5026-abf0-45fb-9459-c35a09e31746}")
	private String hospitalApplyRobert;

	@Value("${prepose.check.orgid.list:}")
	private List<Integer> preposeCheckOrgIdList;

	@Value("${apollo.growthtool.backDoor.kpiAllowMis.list:lichengjin,zenghuan,wanghui,fengxuan}")
	private List<String> backDoorKpiAllowMisList;

	@Value("${apollo.growthtool.login.clause.version:V1.0}")
	private String clauseVersion;

	@ApiModelProperty("水平校验开关")
	@Value("${apollo.clewtrack.check.horizonAuth4BD.switch:true}")
	private boolean checkHorizonAuth4BdSwitch;

	@Value("${apollo.crm.get_token_fail_alarm:true}")
	private boolean tokenFailAlarm;

	@ApiModelProperty("小程序渠道")
	@Value("${apollo.growthtool.clue.miniapp.channel:miniapp_wx_sdc,miniapp_wx_sdc_axsy}")
	private List<String> miniAppChannelList;

	@Value("${apollo.growthtool.commit.header:true}")
	private boolean canCommitHeader;

	@ApiModelProperty("微信视频号请求次数报警阈值")
	@Value("${apollo.growthtool.wx.official.account.request.threshold:60}")
	private Integer wxRequestThreshold;
	@ApiModelProperty("数据诊断中时间分界的时间点")
	@Value("${apollo.growthtool.diagnose.time_split:8}")
	private Integer diagnoseTimeSplit;
	@ApiModelProperty("数据诊断中全局诊断阀值")
	@Value("${apollo.growthtool.diagnose.threshold:0.10}")
	private Double diagnoseThreshold;
	@ApiModelProperty("数据诊断中线下判断主要贡献的临界值")
	@Value("${apollo.growthtool.diagnose.offline_threshold:0.70}")
	private Double diagnoseOfflineThreshold;
	@ApiModelProperty("数据诊断中线下判断正向贡献的临界值")
	@Value("${apollo.growthtool.diagnose.fwd_threshold:0.20}")
	private Double diagnoseFwdThreshold;
	@ApiModelProperty("数据诊断中分析医院浮动的临界值")
	@Value("${apollo.growthtool.diagnose.hospital_threshold:0.50}")
	private Double diagnoseHospitalThreshold;
	@ApiModelProperty("数据诊断报警通知人")
	@Value("${apollo.growthtool.diagnose.alarm_mis:wanghui,liuxiaogang,panwen11,gongxiaorui,hanmei01,liuxuejing}")
	private List<String> diagnoseAlarmMis;
	@ApiModelProperty("数据诊断时 城市分级未配置 报警黑名单")
	@Value("${apollo.growthtool.diagnose.alarm.city_black:}")
	private List<String> diagnoseCityAlarmBlack;

	@ApiModelProperty("gr最上层组织")
	@Value("${growthtool.gr.orgid:0}")
	private int grOrgId;

	@ApiModelProperty("gr距离")
	@Value("${growthtool.gr.remote.miles:5000}")
	private int grRemoteMiles;

	@ApiModelProperty("申请视频号链接功能开关")
	@Value("${apollo.growthtool.apply.wx.video.url.switch:false}")
	private boolean applyWxVideoUrlSwitch;

	@ApiModelProperty("测试区最上层组织")
	@Value("${apollo.growthtool.test.orgid:571}")
	private int testOrgId;

	@ApiModelProperty("目标管理 使用es查询开关")
	@Value("${apollo.growthtool.objective.es.switch:false}")
	private boolean objectiveManageEs;

	@ApiModelProperty("3h后是否重新获取好友信息")
	@Value("${apollo.growthtool.friend.info:false}")
	private boolean dealFriendInfo;

	@ApiModelProperty("微信视频号申请链接ClassName")
	@Value("${apollo.growthtool.wx.apply.video.url.class.name:PermanentMaterialWxFacadeImpl}")
	private String wxApplyVideoClassName;

	@ApiModelProperty("患者招募组织idList")
	@Value("${apollo.growthtool.patient.recruit.orgid:1620,1622,2039}")
	private List<Long> patientRecruitOrgIds;

    @ApiModelProperty("患者招募报警群,提醒群中的指定成员列表")
    @Value("${apollo.growthtool.patient.recruit.alram.group.personArray:lichengjin,zhangyang24,niejiangnan}")
    private String[] patientRecruitGroupPersonArray;

	@ApiModelProperty("哪些组织需要展示患者招募")
	@Value("${patient.icon.orgid.list:}")
	private List<Integer> patientIconOrgIdList;

	@ApiModelProperty("签到打卡开放地区")
	@Value("${apollo.growthtool.part.time.open.city:西安市,郑州市,成都市,新疆}")
	private List<String> partTimeCitys;

	@ApiModelProperty("关键页面截图次数监控")
	@Value("${apollo.growthtool.screenshot.limit_num:5}")
	private int screenshotLimitNum;
	@ApiModelProperty("关键页面截图次数监控@的人")
	@Value("${apollo.growthtool.screenshot.alarm_mis_list:zhangyang}")
	private List<String> screenshotAlarmMisList;

	@ApiModelProperty("作战地图的数据是否走统计数据开关")
	@Value("${growthtool.campaign.data.from.statistic.switch:false}")
	private Boolean campaignDataFromStatisticSwitch;

	@ApiModelProperty("作战地图-科室模块哪些组织可见")
	@Value("${growthtool.department.can.show:}")
	private List<Integer> departmentCanShow;

	@ApiModelProperty("用户评价展示条数")
	@Value("${apollo.growthtool.homepage.user.evaluation.display.count:3}")
	private int userEvaluationDisplayCount;

	@ApiModelProperty("用户评价审核通过标识")
	@Value("${apollo.growthtool.homepage.user.evaluation.audit.pass.type:11}")
	private int userEvaluationAuditPassType;

	@ApiModelProperty("用户评价审核用户默认头像")
	@Value("${apollo.growthtool.homepage.user.default.headImg:https://cf-growthtool-imgs.shuidichou.com/xjy-gw/default1.png}")
	private String userDefaultHeadImg;


	@Value("${apollo.growthtool.delayLevelForFangBianDaiMsg:14}")
	private Integer delayLevelForFangBianDaiMsg;

	@ApiModelProperty("gps错误上报机器人")
	@Value("${gps.error.report.robot:}")
	private String gpsErrorReportRobot;


	@ApiModelProperty("陪诊服务提醒 次数（单个手机号）")
	@Value("${apollo.growthtool.accompany.patient.msg_alarm_num:1}")
	private int accompanyPatientMsgAlarmNum;


	@ApiModelProperty("需要处理的messageId,助力活动消息id")
	@Value("${apollo.growthtool.activity.messageid:0}")
	private long activityMessageId;
	@ApiModelProperty("对于“下降”状态增加一个阈值，即今日走访时长小于近7日同时段走访时长的 N% ，才展示“下降”状态。")
	@Value("${apollo.growthtool.current.interview_rate:0.8}")
	private Double interviewRate;

	@ApiModelProperty("对于“下降”状态增加一个阈值，即今日走访时长小于近7日同时段走访时长的 N% ，才展示“下降”状态。")
	@Value("${apollo.growthtool.location.switch.v2:false}")
	private boolean userLocationSwitchV2;

	@ApiModelProperty("大数据推送案例快照数据时是否需要重新设置uniqueCode")
	@Value("${apollo.growthtool.cf_kpi_case_base_data_sync.switch:false}")
	private boolean isNeedResetUniqueCode;

	@ApiModelProperty("大数据推送案例快照数据时是否需要重新设置uniqueCode")
	@Value("${apollo.growthtool.cf_kpi_case_base_data_sync.reset.uniqueCode:tgcn36611}")
	private String specialUniqueCode;

	@ApiModelProperty("通过growhtool同步首次捐单,0:是")
	@Value("${apollo.growthtool.bd.case.donate.switch:1}")
	private int bdCaseDonateSwitch;

	@ApiModelProperty("是否是测试环境,默认为false")
	@Value("${growthtool.test.env:false}")
	private boolean growthTestEnv;

	@ApiModelProperty("兼职人员-我的绩效入口开关")
	@Value("${apollo.growthtool.partner.kpi.icon.switch:false}")
	private boolean partnerKpiIconSwitch;

	@ApiModelProperty("录入爱心伙伴信息开关")
	@Value("${apollo.growthtool.create.love.partner.switch:true}")
	private boolean createLovePartner;

	@ApiModelProperty("处理参数中的空格")
	@Value("${apollo.growthtool.replace.blank:true}")
	private boolean replaceBlank;

	@ApiModelProperty("蜂鸟计划组织")
	@Value("${apollo.growthtool.partner.orgId:2039}")
	private int partnerOrgId;

	@Value("${apollo.direct.show.recruit:true}")
	private boolean directShowRecruit;

	@ApiModelProperty("潮普慈善会")
	@Value("${apollo.chaopu.fundation.city.name:汕头市}")
	private List<String> chaopuCityList;

	@ApiModelProperty("招募组织")
	@Value("${apollo.growthtool.recruit.orgId:2934}")
	private long recruitOrgId;


	@ApiModelProperty("模板以及对应说明")
	@Value("${apollo.growthtool.template.lot.map:}")
	private String templateLotMap;

	@ApiModelProperty("陪访-兼职业务数据批次")
	@Value("${apollo.partner.business.lot:0}")
	private Long partnerBusinessLot;

	@ApiModelProperty("法律援助关键词")
	@Value("${apollo.legal.aid.key.word.list:工伤,车祸,事故,骑手,工地,交通,火灾,撞到,撞飞,交通事故,保险,司机,定责,高位截瘫}")
	private List<String> legalAidKeyWordList;

	@ApiModelProperty("筹为发起线索过滤词")
	@Value("${apollo.not.initiated.key.word:车祸|骨折|粉碎}")
	private String notInitiatedKeyWord;


	@ApiModelProperty("要素对应得分项")
	@Value("${apollo.fact.score.map:}")
	private String factScoreMap;


	@ApiModelProperty("同步微信好友是否关注水滴妙医公众号至标签位置")
	@Value("${apollo.growthtool.sync_subscribe_tag:false}")
	private boolean syncSubscribeTag;


	@ApiModelProperty("不上报给大数据的uri")
	@Value("${growthtool.ignore.report.paths:}")
	private List<String> ignoreReportPaths;

	@ApiModelProperty("案例分层")
	@Value("${apollo.case.level.model:}")
	private String caseLevelModel;

	@ApiModelProperty("申诉有效时间")
	@Value("${remote.apply.delay:24}")
	private int remoteApplyDelay;


	@ApiModelProperty("mdc正在运营的企微主体，用于添加外部联系人/备注获取筹集号等")
	@Value("${growthtool.weChatFriendInfoChangeMsgToMdc_callbackIds:123}")
	private List<Integer> weChatFriendInfoChangeMsgToMdc_callbackIds;


	@ApiModelProperty("小助理使用中台月份")
	@Value(("${apollo.not.use.old.month:2024-12}"))
	private String notUseOldMonth;

	@ApiModelProperty("企业微信试验人员")
	@Value("${wechat.qr.code.users:}")
	private List<String> wechatQrCodeUsers;

	@ApiModelProperty("oneservice appkey")
	@Value("${oneservice.appkey:100073}")
	private String oneServiceAppkey;

	@ApiModelProperty("线上人员主页展示我们帮住过的患者兜底案例")
	@Value("${hompage.helpedpatient.caseids:2994934,2981212}")
	private String helpedPatientCaseIdStr;


	@ApiModelProperty("企业微信试验人员-所有人")
	@Value("${all.use.qr.wechat:false}")
	private boolean allUseQrWechat;


	@ApiModelProperty("绩效案例重新归属人员")
	@Value("${kpi.case.reattribute:}")
	private String kpiReAttributeCase;

	@ApiModelProperty("招募推送忽略哪些患者")
	@Value("${ignore.kpi.recruit:}")
	private List<Long> ignoreKpiRecruit;

	@ApiModelProperty("最大组织id")
	@Value("${begin,qw.departmentid:320}")
	private int beginQwDepartmentId;


	@ApiModelProperty("推送代理商人员批次")
	@Value("${pep.delegate.member.lot:0}")
	private long pepDelegateMemberLot;

	@ApiModelProperty("推送合作人员批次")
	@Value("${apollo.growthtool.deletegate.member.lot:0}")
	private long deletegateMemberLot;

	@ApiModelProperty("侧边栏需要入Friend库的企业微信")
	@Value("#{${cf-patient-api.sidebar.corpid.map:{'wpdiWoDQAAs5JV7oOSqIhUOVwu8LBgpA':'ww0b60bdf25d7dbd8b','wpdiWoDQAANgx1KiixHNL36A5Hw4AKlg':'wwe573ade39a68372a'}}}")
	private Map<String, String> sideBarCorpIdMap;

	@ApiModelProperty("学生案例关键词")
	@Value("${apollo.student.case.key.word.list:学生,学校,校园}")
	private List<String> studentCaseKeyWordList;

	@ApiModelProperty("线上转线下案例")
	@Value("${apollo.kpi.online.case.list:}")
	private List<Long> kpiOnlineCaseList;


	@ApiModelProperty("代录入链接失效时间")
	@Value("${link.valid.day:2}")
	private int linkValidDay;

	@ApiModelProperty("案例真实性灰度城市")
	@Value("${growthtool.case.credit.city.list:}")
	private List<String> caseCreditCityList;

	@ApiModelProperty("家庭经济拆分灰度城市")
	@Value("${growthtool.family.financial.separation.city.list:}")
	private List<String> familyFinancialSeparationCityList;

	@ApiModelProperty("标题70字案例分区")
	@Value("${growthtool.case.title.partition.list:}")
	private List<Long> caseTitlePartitionList;


	@ApiModelProperty("企业微信回调开关")
	@Value("${apollo.qywx.callback.switch:false}")
	private boolean qywxCallbackSwitch;

	@ApiModelProperty("合作基金会基本信息")
	@Value("${apollo.foundationInfo.config:}")
	private String foundationInfoConfig;

	@ApiModelProperty("线上线索转线下案例集合")
	@Value("${apollo.leads.online.to.offline.case.list:}")
	private List<Long> leadsOnlineToOfflineCaseList;

	//上线后需要去掉
	@ApiModelProperty("自发起确权超时处理时间-小时")
	@Value("${apollo.confirm.over.hour:2}")
	private int confirmOverHour;

	@ApiModelProperty("自发起发送频率-分钟")
	@Value("${apollo.confirm.msg.rate.minute:30}")
	private int confirmMsgRateMinute;

	@ApiModelProperty("自发起上级开始发送-分钟")
	@Value("${apollo.confirm.leader.start.minute:60}")
	private int confirmLeaderStartMinute;

	@ApiModelProperty("省直辖县级行政区划/自治区直辖县级行政区划集合")
	@Value("${apollo.directly.under.the.jurisdiction.list:}")
	private List<Integer> directlyUnderTheJurisdictionList;

	@ApiModelProperty("获取重复城市集合 简阳市：目前挂在省直辖跟成都市下，现属于成都市，过滤掉省直辖/" +
			"枞阳县：目前挂在安庆市跟铜陵市下，现属于铜陵市，过滤掉安庆市/" +
			"寿县：目前挂在淮南市跟六安市下，现属于淮南市，过滤掉六安市 ")
	@Value("${apollo.repetition.city.list:}")
	private List<Integer> repetitionCityList;

	@ApiModelProperty("tr key:服务费规则id value:城市名称集合")
	@Value("#{${tr.charge.city.rule.id.map:{}}}")
	private Map<String, String> chargeCityRuleIdMap;

	@ApiModelProperty("线上同步线下callbakId集合")
	@Value("${apollo.online.sync.offline.callback.id.set:}")
	private Set<Integer> syncOfflineSet;

	@ApiModelProperty("任务中心有效案例筹款金额")
	@Value("${apollo.task.center.valid.case.amount:350000}")
	private int taskCenterValidCaseAmount;

	@ApiModelProperty("任务中心有效案例捐单量")
	@Value("${apollo.task.center.valid.case.donate.count:120}")
	private int taskCenterValidCaseDonateCount;

	@ApiModelProperty("任务中心有效案例标准配置大区")
	@Value("${apollo.task.center.valid.case.org:}")
	private List<Long> taskCenterValidCaseOrg;

	@ApiModelProperty("案例顾问卡片用户不展示黑名单")
	@Value(("${volunteer.not.show.for.users:}"))
	private List<Long> volunteerNotShowForUserIds;

	@ApiModelProperty("举报工单生成时 外呼给对应的顾问的外呼模版")
	@Value("${apollo.growthtool.call.template-number:657}")
	private long callTemplateNumber;

	@ApiModelProperty("绩效-团队绩效灰度")
	@Value(("${kpi.team.grey.city.list:}"))
	private List<String> kpiTeamGreyCityList;

	@ApiModelProperty("顾问服务月url")
	@Value(("${gwfwy.url:}"))
	private String gwfwyUrl;


	@ApiModelProperty("重点服务案例即将有效案例判断")
	@Value("${apollo.growthtool.person.pep.case.pool.donate-num:50}")
	private int personPepCasePoolDonateNum;

	@ApiModelProperty("重点服务案例即将有效案例判断")
	@Value("${apollo.growthtool.person.pep.case.pool.friend-contributions:70}")
	private int personPepCasePoolFriendContributions;

	@ApiModelProperty("撞单试点城市")
	@Value("${apollo.clew.crash.city:}")
	private List<String> crashCities;

	@ApiModelProperty("ai帮写文章试点城市")
	@Value("${apollo.clew.ai-content.city:}")
	private List<String> aiContentCities;

	@ApiModelProperty("代理商不需要展示的团队")
	@Value("${delegate.kpi.exclude.teams:}")
	private List<String> delegateKpiExcludeTeams;

	@ApiModelProperty("代理商不需要展示的团队")
	@Value("${delegate.kpi.exclude.uniquecodes:}")
	private List<String> delegateKpiExcludeUniqueCodes;

	@ApiModelProperty("非自运营企微回调逻辑关闭")
	@Value("${apollo.close.other.qw.callback:false}")
	private boolean apolloCloseOtherQwCallback;

	@ApiModelProperty("可生成患者盘点看板明细")
	@Value("${apollo.can.generate.patient.inventory.detail:false}")
	private boolean canGeneratePatientInventoryDetail;

	@ApiModelProperty("企业微信taskId前缀")
	@Value("${apollo.qywx.group.prefix.header:}")
	private String qywxGroupPrefixedHeader;

	@ApiModelProperty("1v1试点人员")
	@Value("${apollo.qywx.group.online.user:}")
	private List<String> qywxGroupOnlineUser;

	@ApiModelProperty("转移到新的群主")
	@Value("${apollo.online.new.group.owner:}")
	private String onlineNewGroupOwner;



	/**
	 * 根据配置的时间 来控制签到和签退
	 * @return
	 */
	public int getPunchStatus() {
		Date now = new Date();
		String[] timeArr = gpsTime.split("-");
		Date startTime = DateUtils.addHours(DateUtil.getCurrentDate(), Integer.valueOf(timeArr[0]));
		Date endTime = DateUtils.addHours(DateUtil.getCurrentDate(), Integer.valueOf(timeArr[1]));
		// 在endTime之前 在startTime之后 表示已签到
		if (now.before(endTime) && now.after(startTime)) {
			return PunchCardVo.PushStatusEnum.SIGN_IN.getStatus();
		}
		// 在endTime之后 表示已签退
		if (now.after(endTime)) {
			return PunchCardVo.PushStatusEnum.SIGN_OUT.getStatus();
		}
		// 在startTime之前表示 未签到
		return PunchCardVo.PushStatusEnum.DEFAULT.getStatus();
	}

	public boolean isHandleRetentiontime(){
		return retentiontime;
	}

	public boolean isInBlackList(String mis){
		String[] misArr = cfvinfoblacklist.split(",");
		return Lists.newArrayList(misArr).contains(mis);
	}

	public List<String> getCfvinfoblacklist(){
		return Splitter.on(",").trimResults().omitEmptyStrings().splitToList(this.cfvinfoblacklist);
	}

	public boolean isInFangbiandaiTest(String unionId){
		if (StringUtils.isBlank(fangbiandaiTest) || StringUtils.isBlank(unionId)){
			return false;
		}
		String[] fangbiandaiTestArr = fangbiandaiTest.split(GeneralConstant.splitChar);
		if (Lists.newArrayList(fangbiandaiTestArr).contains(unionId)){
			return true;
		}
		return false;
	}

	public boolean enableVirtualPhone(){
		return enableVirtualPhone;
	}

	public Integer getLowinventory(){
		return lowinventory;
	}

	public Timestamp getCheckRepeatTime() {
		return DateUtil.addMinutes(DateUtil.addHours(DateUtil.getCurrentTimestamp(),checkRepeatHours),checkRepeatMinutes);
	}

	public int getfangbiandaiThirdType(){
		return fangbiandaiThirdType;
	}


	public List<String> listWorkStatusNoNeedAlarmMis() {
		List<String> misList = Lists.newArrayList();
		try {
			if (StringUtils.isBlank(workStatusNoNeedAlarmMis)) {
				return misList;
			}
			misList = Splitter.on(",").splitToList(workStatusNoNeedAlarmMis);
		} catch (Exception e) {
			log.error("解析数据异常", e);
		}
		return misList;
	}


	public Map<String, String> getCaseTMisMap() {
		Map<String, String> caseTMisMap = Maps.newHashMap();
		try {
			caseTMisMap = JSON.parseObject(apolloCaseCityList, new TypeReference<Map<String, String>>() {});
			log.debug("apolloCaseCityList:{}", caseTMisMap);
		} catch (Exception e) {
			log.error("apollo配置信息格式错误,apolloCaseCityList:{}", apolloCaseCityList, e);
		}
		return caseTMisMap;
	}


	public Map<Long, Integer> parseFactScoreMap() {
		Map<Long, Integer> factMap = Maps.newHashMap();
		try {
			if (StringUtils.isNotBlank(factScoreMap)) {
				factMap = JSON.parseObject(factScoreMap, new TypeReference<Map<Long, Integer>>() {});
				log.debug("factScoreMap:{}", factMap);
			}
		} catch (Exception e) {
			log.error("apollo配置信息格式错误,factScoreMap:{}", factScoreMap, e);
		}
		return factMap;
	}

	//城市-mis
	public Map<String, String> getCaseTMisMapCanModify() {
		Map<String, String> caseTMisMap = Maps.newHashMap();
		try {
			caseTMisMap = JSON.parseObject(apolloCaseCityCanModify, new TypeReference<Map<String, String>>() {});
			log.debug("apolloCaseCityCanModify:{}", caseTMisMap);
		} catch (Exception e) {
			log.error("apollo配置信息格式错误,apolloCaseCityCanModify:{}", apolloCaseCityCanModify, e);
		}
		return caseTMisMap;
	}

	//医院-mis
	public Map<String, String> getHospitalTMisMapCanModify() {
		Map<String, String> caseTMisMap = Maps.newHashMap();
		try {
			caseTMisMap = JSON.parseObject(apolloCaseHospitalCanModify, new TypeReference<Map<String, String>>() {});
			log.debug("apolloCaseHospitalCanModify:{}", caseTMisMap);
		} catch (Exception e) {
			log.error("apollo配置信息格式错误,apolloCaseHospitalCanModify:{}", apolloCaseCityCanModify, e);
		}
		return caseTMisMap;
	}

	public Long getSingleManulImportCaseBonus() {
		return singleManulImportCaseBonus;
	}

	public int getRetentiontimeInterval() {
		return retentiontimeInterval;
	}


	public List<String> listHasCheckCity() {
		List<String> hasCheckCityList = Lists.newArrayList();
		try {
			if (StringUtils.isBlank(hasCheckCity)) {
				return hasCheckCityList;
			}
			hasCheckCityList = Splitter.on(",").splitToList(hasCheckCity);
		} catch (Exception e) {
			log.error("解析数据异常", e);
		}
		return hasCheckCityList;
	}

	/**
	 * 判断gps 是否 非法
	 * @param longitude
	 * @param latitude
	 * @return
	 */
	public boolean checkGpsIlleagal(String longitude,String latitude){
		List<String> illeagalGpsList = Lists.newArrayList(illeagalGps.split(GeneralConstant.splitChar));
		if (illeagalGpsList.contains(longitude) || illeagalGpsList.contains(latitude)){
			return true;
		}
		return false;
	}


	/**
	 * 绿色通道审核权限-个转公
	 * @return
	 */
	public Map<Integer, String> getPublicGreenChannelPermission() {
		Map<Integer, String> greenChannelPermissionMap = Maps.newHashMap();
		try {
			greenChannelPermissionMap = JSON.parseObject(publicGreenChannelPermission, new TypeReference<Map<Integer, String>>() {});
			log.debug("greenChannelPermission:{}", publicGreenChannelPermission);
		} catch (Exception e) {
			log.error("apollo配置信息格式错误,publicGreenChannelPermission:{}", publicGreenChannelPermission, e);
		}
		return greenChannelPermissionMap;
	}


	/**
	 * 绿色通道审核权限-医护
	 * @return
	 */
	public Map<Integer, String> getDoctorRecommendGreenChannelPermission() {
		Map<Integer, String> greenChannelPermissionMap = Maps.newHashMap();
		try {
			greenChannelPermissionMap = JSON.parseObject(doctorGreenChannelPermission, new TypeReference<Map<Integer, String>>() {});
			log.debug("doctorGreenChannelPermission:{}", doctorGreenChannelPermission);
		} catch (Exception e) {
			log.error("apollo配置信息格式错误,doctorGreenChannelPermission:{}", doctorGreenChannelPermission, e);
		}
		return greenChannelPermissionMap;
	}


	public List<String> getBdKpiAlarmUsers() {
		return Splitter.on(",").splitToList(bdKpiAlarmUsers);
	}

    public int getOuterShowFlag() {
        return outerShowFlag;
    }


	public boolean getTokenFailAlarm(){
		return tokenFailAlarm;
	}

	public boolean checkCanShowObjective(String orgPath) {
		if (CollectionUtils.isEmpty(showOrgPathList)) return true;
		return showOrgPathList.stream().anyMatch(showOrgPath -> orgPath.contains(showOrgPath));
	}

	public Date getDiagnoseSplitDate(){
		return DateUtil.addHours(new Timestamp(DateUtil.getCurrentDate().getTime()),diagnoseTimeSplit);
	}

	public String[] getDiagnoseAlarmMisArr(){
		return diagnoseAlarmMis.toArray(new String[diagnoseAlarmMis.size()]);
	}


	public DelayLevel getDelayLevelForFangBianDaiMsg() {
		try {
			return DelayLevel.valueOfCode(delayLevelForFangBianDaiMsg);
		}catch (Exception e){
			log.error("getDelayLevelForFangBianDaiMsg err:",e);
		}
		return DelayLevel.M10;
	}


	public String replaceEncryptInfo(String encryptInfo) {
		String replace = encryptInfo;
		if (replaceBlank) {
			replace = StringUtils.replace(encryptInfo, " ", "+");
			if (!Objects.equals(replace, encryptInfo)) {
				log.info("原始info:{},replace后的info:{}", encryptInfo, replace);
			}
		}
		return replace;
	}


	public Map<Integer, String> reAttributeCase() {
		Map<Integer, String> caseTUniqueCodeMap = Maps.newHashMap();
		if (StringUtils.isBlank(kpiReAttributeCase)) {
			return caseTUniqueCodeMap;
		}
		try {
			caseTUniqueCodeMap = JSON.parseObject(kpiReAttributeCase, new TypeReference<Map<Integer, String>>() {});
			log.debug("reAttributeCase:{}", caseTUniqueCodeMap);
		} catch (Exception e) {
			log.error("apollo配置信息格式错误,reattribute:{}", apolloCaseCityList, e);
		}
		return caseTUniqueCodeMap;
	}
}
