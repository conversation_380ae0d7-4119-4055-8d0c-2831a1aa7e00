package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.subtask;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdSubTaskDO;

import java.util.List;


public interface ICrmBdSubTaskService {

    CrmBdSubTaskDO queryById(long id);

    int insert(CrmBdSubTaskDO crmBdSubTaskDO);

    int update(CrmBdSubTaskDO crmBdSubTaskDO);

    List<CrmBdSubTaskDO> queryByParentId(long parentId);

    List<CrmBdSubTaskDO> batchQueryByParentId(List<Long> parentIds);

    void batchUpdateWhenOverTime(List<Long> ids);

    int updateWhenComplete(long id);

    CrmBdSubTaskDO getByTaskTypeAndCaseId(int taskType, long caseId);

    List<CrmBdSubTaskDO> getByTaskTypesAndCaseId(List<Integer> taskTypes, long caseId);

}
