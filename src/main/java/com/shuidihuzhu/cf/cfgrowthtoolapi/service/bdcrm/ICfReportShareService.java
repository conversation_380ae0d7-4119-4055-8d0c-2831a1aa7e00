package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;


import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdReportLinkDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.ReportVerificationVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.VerifyAdvisorVO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.param.raise.RaiseCaseParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.cf.vo.initialaudit.*;
import com.shuidihuzhu.client.cf.growthtool.param.BindCaseReportModel;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-01-09
 */
public interface ICfReportShareService {

    /**
     * 生成链接  默认是线下BD
     *
     * @param uniqueCode
     * @param reportId
     * @return
     */
    OpResult<Integer> generateShareLink(String uniqueCode, Long reportId);

    /**
     * 生成链接
     *
     * @param uniqueCode
     * @param reportId
     * @param reportType 0代表线下BD  1代表 1v1
     * @return
     */
    OpResult<Integer> generateShareLink(String uniqueCode, Long reportId, int reportType);


    /**
     * 验证链接接口
     *
     * @param info
     * @param userId
     * @param clientIp
     * @return
     */
    OpResult<ReportVerificationVo> validateLink(String info, Long userId, String clientIp);

    /**
     * 案例发起后更新uuid接口
     *
     * @param info
     * @param infoId
     * @return
     */
    void bindInfoId(BindCaseReportModel bindCaseReportModel);

    /**
     * 根据报备id获取分享链接
     *
     * @param reportId
     * @return
     */
    OpResult<CfBdReportLinkDO> getLink(Long reportId);

    /**
     * 根据报备id,判断是否可以修改字段
     *
     * @param preposeMaterialId
     * @return
     */
    OpResult<Boolean> checkIsCanModFiled(Long preposeMaterialId);

    List<CfBdReportLinkDO> getCfBdReportLinkDoByReportIds(List<Long> reportIds);

    /**
     * 根据案例查询是否未被举报的前置报备发起
     *
     * @param infoUuid
     * @return
     */
    OpResult<VerifyAdvisorVO> validAdvisor(String infoUuid, Integer type);


    // 不进行校验手机号
    Response<String> verifyMobileV2(String info, String mobile, String clientIp, long userId);

    /**** 3100 版本 **/
    //校验+绑定手机号
    Response<String> verifyMobileV3(String info, String mobile, String verifyCode, String clientIp, long userId, String key);

    //获取详情
    Response<RaiseCaseParam> getReportData(List<String> pageNames, String info, String verifyToken, long userId);

    //驳回列表信息
    Response<InitialAuditInfoVO> initialInfo(String infoUuid, String info, String verifyToken);

    //驳回基础信息
    Response<CrowdfundingInfoBaseVo> getBaseInfo(String infoUuid, String info, String verifyToken);

    //驳回医疗信息
    Response<CfFirsApproveMaterialVO> getFirstApproveInfo(String infoUuid, String info, String verifyToken);

    //驳回经济状况信息
    Response<CfInsuranceLivingAuditVo> getPropertyInsuranceAuditInfo(String infoUuid, String info, String verifyToken);

    /**
     * 根据案例获取链接
     *
     * @param caseIds
     * @return
     */
    List<CfBdReportLinkDO> listByCaseIds(List<Integer> caseIds);

    Long getReportIdByCaseId(int caseId);

    OpResult<CfBdReportLinkDO> verifyInfo(String info);

}
