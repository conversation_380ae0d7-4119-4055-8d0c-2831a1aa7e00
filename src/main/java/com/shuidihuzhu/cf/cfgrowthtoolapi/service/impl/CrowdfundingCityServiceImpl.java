package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CampaignSimpleCityModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.campaignv2.CampaignProCityModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICrowdfundingCityService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.LocalCacheHelper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.dataconvert.CrowdfundingCityConvert;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.client.cf.api.client.CrowdfundingCityFeignClient;
import com.shuidihuzhu.client.cf.api.model.CrmCityModel;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2021/8/6 下午4:47
 */
@Service
public class CrowdfundingCityServiceImpl implements ICrowdfundingCityService {

    @Resource
    private CrowdfundingCityFeignClient crowdfundingCityFeignClient;

    @Override
    public Map<String, Integer> getCityNameMapCityId() {
        Response<Map<String, Integer>> response = crowdfundingCityFeignClient.getCityNameMapCityId();
        return response.notOk() ? Maps.newHashMap() : response.getData();
    }

    @Override
    public List<CrowdfundingCity> getCityByParentIds(Collection<Integer> parentIds) {
        Response<List<CrowdfundingCity>> response = crowdfundingCityFeignClient.getCityByParentIds(Lists.newArrayList(parentIds));
        return response.notOk() ? Lists.newArrayList() : response.getData();
    }


    @Override
    public List<CampaignProCityModel> getCampaignCity(List<Integer> cityIds) {
        List<CampaignProCityModel> result = Lists.newArrayList();
        List<CrowdfundingCity> provinceList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(cityIds)) {
            return result;
        }
        //根据作战地图配置设置查询数据
        List<CrowdfundingCity> crowdfundingCities = this.getCityByCityIds(cityIds);
        //先获取省份
        provinceList = crowdfundingCities.stream().filter(s -> s.getLevel() == 0).collect(Collectors.toList());
        //获取单独城市
        List<CrowdfundingCity> cityIdListFromConfig = crowdfundingCities.stream().filter(s -> s.getLevel() == 1).collect(Collectors.toList());
        //获取省份下所有城市
        List<CrowdfundingCity> subCityIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(provinceList)) {
            subCityIdList = this.getCityByParentIds(provinceList.stream().map(CrowdfundingCity::getId).collect(Collectors.toList()));
        }
        //合并 单独城市 + 获取省份下所有城市
        subCityIdList.addAll(cityIdListFromConfig);
        Map<Integer, List<CrowdfundingCity>> cityMap =
                subCityIdList.stream().collect(Collectors.groupingBy(CrowdfundingCity::getRealParentId));
        //找到省份
        List<CrowdfundingCity> provinces = this.getCityByCityIds(Lists.newArrayList(cityMap.keySet()));
        for (Map.Entry<Integer, List<CrowdfundingCity>> entry : cityMap.entrySet()) {
            CrowdfundingCity province = provinces.stream()
                    .filter(item -> Objects.equals(entry.getKey(), item.getId()))
                    .findFirst().orElse(null);
            if (province == null) {
                continue;
            }
            CampaignProCityModel campaignProCityModel = new CampaignProCityModel();
            campaignProCityModel.setProvinceId(province.getId());
            campaignProCityModel.setProvinceName(province.getName());
            List<CampaignSimpleCityModel> cityModelList = entry.getValue().stream().map(item -> {
                CampaignSimpleCityModel simpleCityModel = new CampaignSimpleCityModel();
                simpleCityModel.setCityName(item.getName());
                simpleCityModel.setCityId(item.getId());
                return simpleCityModel;
            }).collect(Collectors.toList());
            campaignProCityModel.setCityList(cityModelList);
            result.add(campaignProCityModel);
        }
        return result;
    }

    @Override
    public List<CrowdfundingCity> getCityByCityIds(List<Integer> cityIds) {
        Response<List<CrmCityModel>> response = crowdfundingCityFeignClient.listByIds(cityIds);
        List<CrmCityModel> crmCityModels = Optional.ofNullable(response)
                .map(Response::getData)
                .orElse(null);
        return convertCityDoList(crmCityModels);
    }

    private List<CrowdfundingCity> convertCityDoList(List<CrmCityModel> crmCityModels) {
        if (CollectionUtils.isEmpty(crmCityModels)) {
            return Lists.newArrayList();
        }
        return crmCityModels.stream().map(CrowdfundingCityConvert.INSTANCE::toCityDO).collect(Collectors.toList());
    }

    @Override
    public List<CrowdfundingCity> getByParentId(Integer parentId) {
        Response<List<CrmCityModel>> response = crowdfundingCityFeignClient.getCityList(parentId);
        List<CrmCityModel> crmCityModels = Optional.ofNullable(response)
                .map(Response::getData)
                .orElse(null);
        return convertCityDoList(crmCityModels);
    }

    @Override
    public CrowdfundingCity getById(Integer id) {
        Response<CrmCityModel> response = crowdfundingCityFeignClient.getById(id);
        return response.notOk() ? null : CrowdfundingCityConvert.INSTANCE.toCityDO(response.getData());
    }

    @Override
    public List<CrowdfundingCity> getProvinceByName(String province) {
        Response<List<CrmCityModel>> response = crowdfundingCityFeignClient.getProvinceList();
        List<CrmCityModel> crmCityModels = Optional.ofNullable(response)
                .map(Response::getData)
                .orElse(null);
        List<CrowdfundingCity> crowdfundingCityList = convertCityDoList(crmCityModels);
        if (CollectionUtils.isNotEmpty(crowdfundingCityList)) {
            return crowdfundingCityList
                    .stream()
                    .filter(item -> item.getName().equals(province) && item.getValid() == 1 && item.getLevel() == 0)
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<CrowdfundingCity> getByProvinceAndCityName(String city, Integer parentId) {
        List<CrowdfundingCity> crowdfundingCityList = getByParentId(parentId);
        return crowdfundingCityList
                .stream()
                .filter(item -> item.getName().equals(city) && item.getValid() == 1 && item.getLevel() == 1)
                .collect(Collectors.toList());
    }

    @Override
    public List<CrowdfundingCity> getByProvinceAndCityNameByLike(String city, Integer parentId) {
        Response<List<CrowdfundingCity>> response = crowdfundingCityFeignClient.getByProvinceAndCityNameByLike(city, parentId);
        return response.notOk() ? Lists.newArrayList() : response.getData();
    }

    @Override
    public List<CrowdfundingCity> getByCityNames(List<String> queryCities) {
        Response<List<CrowdfundingCity>> response = crowdfundingCityFeignClient.listByCityNames(queryCities);
        return response.notOk() ? Lists.newArrayList() : response.getData();
    }

    @Override
    public List<CrowdfundingCity> listByCityNameAndLevel(String cityName, List<Integer> levels, Integer limit) {
        if (StringUtils.isBlank(cityName)) {
            return Lists.newArrayList();
        }
        Response<List<CrowdfundingCity>> response = crowdfundingCityFeignClient.listByCityNameAndLevel(cityName, levels, limit);
        return response.notOk() ? Lists.newArrayList() : response.getData();
    }

}
