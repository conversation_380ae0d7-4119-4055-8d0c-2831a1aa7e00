package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr;


import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrCustomerConnectorDO;

import java.util.List;
import java.util.Map;


/**
 * gr客户联系人(GrCustomerConnector)表服务接口
 *
 * <AUTHOR>
 * @since 2021-04-09 15:32:55
 */
public interface GrCustomerConnectorService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    GrCustomerConnectorDO queryById(long id);

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    List<GrCustomerConnectorDO> queryAllByLimit(int offset, int limit);

    /**
     * 新增数据
     *
     * @param grCustomerConnector 实例对象
     * @return 实例对象
     */
    GrCustomerConnectorDO insert(GrCustomerConnectorDO grCustomerConnector);

    /**
     * 修改数据
     *
     * @param grCustomerConnector 实例对象
     */
    void update(GrCustomerConnectorDO grCustomerConnector);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);

    List<GrCustomerConnectorDO> listByCustomerIds(List<Integer> customerIds);

    Map<Integer, List<GrCustomerConnectorDO>> listGroupByCustomerId(List<Integer> customerIds);

}