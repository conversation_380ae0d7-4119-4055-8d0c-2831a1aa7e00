package com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage;


import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.payload.DelayMsgNoticePayload;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

/**
 * @author: yangliming
 * @create: 2019/11/29
 */
public interface IAppPushCrmReportMsgService {

    DelayMsgNoticePayload sendMsg2BdForPreposeMaterial(CrowdfundingVolunteer cfVolunteer, CrowdfundingInfo crowdfundingInfo, String url, String title, String buttonText);

}
