package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.subtask;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdSubTaskDO;
import com.shuidihuzhu.cf.dao.bdtask.CrmBdSubTaskDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class CrmBdSubTaskServiceImpl implements ICrmBdSubTaskService {

    @Resource
    private CrmBdSubTaskDao crmBdSubTaskDao;

    @Override
    public CrmBdSubTaskDO queryById(long id) {
        if (id <= 0) {
            return null;
        }
        return crmBdSubTaskDao.queryById(id);
    }


    @Override
    public int insert(CrmBdSubTaskDO crmBdSubTaskDO) {
        if (Objects.isNull(crmBdSubTaskDO)) {
            return 0;
        }
        return crmBdSubTaskDao.insert(crmBdSubTaskDO);
    }

    @Override
    public int update(CrmBdSubTaskDO crmBdSubTaskDO) {
        if (Objects.isNull(crmBdSubTaskDO)) {
            return 0;
        }
        return crmBdSubTaskDao.update(crmBdSubTaskDO);
    }

    @Override
    public List<CrmBdSubTaskDO> queryByParentId(long parentId) {
        if (parentId <= 0) {
            return Lists.newArrayList();
        }
        return crmBdSubTaskDao.queryByParentId(parentId);
    }

    @Override
    public List<CrmBdSubTaskDO> batchQueryByParentId(List<Long> parentIds) {
        if (CollectionUtils.isEmpty(parentIds)) {
            return Lists.newArrayList();
        }
        return crmBdSubTaskDao.batchQueryByParentId(parentIds);
    }

    @Override
    public void batchUpdateWhenOverTime(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        crmBdSubTaskDao.batchUpdateWhenOverTime(ids);
    }

    @Override
    public int updateWhenComplete(long id) {
        if (id <= 0) {
            return 0;
        }
        return crmBdSubTaskDao.updateWhenComplete(id);
    }

    @Override
    public CrmBdSubTaskDO getByTaskTypeAndCaseId(int taskType, long caseId) {
        if (taskType <= 0 || caseId <= 0) {
            return null;
        }
        return crmBdSubTaskDao.getByTaskTypeAndCaseId(taskType, caseId);
    }

    @Override
    public List<CrmBdSubTaskDO> getByTaskTypesAndCaseId(List<Integer> taskTypes, long caseId) {
        if (CollectionUtils.isEmpty(taskTypes) || caseId <= 0) {
            return Lists.newArrayList();
        }
        return crmBdSubTaskDao.getByTaskTypesAndCaseId(taskTypes, caseId);
    }
}
