package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.organization;

import com.alibaba.druid.sql.visitor.functions.If;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.OrgOptParam;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2020-05-13 15:44
 **/
@Service
@Slf4j
public class EditOrgLimitImpl extends AbstractOptOrgLimit {

    @Override
    public Response<Boolean> doCanOptCheck(OrgOptParam orgOptParam) {
        log.info("开始修改组织校验");
        BdCrmOrganizationDO currentOptOrg = orgOptParam.getCurrentOptOrg();
        Optional<BdCrmOrganizationDO> currentOrg = isExistOrgInfo(currentOptOrg.getId());
        if (!currentOrg.isPresent()) {
            return NewResponseUtil.makeFail("修改的节点不存在,请确认后再修改");
        }
        orgOptParam.setBeforeChangeOrg(currentOrg.get());
        OrganizationUserEnums.OrgNodeAttributeEnum changedAttribute = OrganizationUserEnums.paresNodeAttribute(currentOptOrg.getOrgAttribute());
        if (changedAttribute == null) {
            return NewResponseUtil.makeFail("请设置节点属性");
        }
        Response<Boolean> checkCanChangeNodeAttribute = checkCanChangeNodeAttribute(orgOptParam);
        if (checkCanChangeNodeAttribute.notOk()) {
            return checkCanChangeNodeAttribute;
        }
        Response<Boolean> checkCanChangeNodeName = checkCanChangeNodeName(orgOptParam);
        if (checkCanChangeNodeName.notOk()) {
            return checkCanChangeNodeName;
        }
        return NewResponseUtil.makeSuccess(true);
    }


    private Response<Boolean> checkCanChangeNodeName(OrgOptParam orgOptParam) {
        Response<Boolean> checkHasSameName = checkHasSameName(orgOptParam);
        //如果是叶子结点确保是不是以下几种特殊类型的城市
        boolean nodeForbidName = nodeForbidName(orgOptParam);
        if (nodeForbidName) {
            return NewResponseUtil.makeFail("叶子结点不允许设置为'市辖市'或'省直辖县级行政区划'");
        }
        if (checkHasSameName.notOk()) {
            return checkHasSameName;
        }
        return NewResponseUtil.makeSuccess(true);
    }


    private Response<Boolean> checkCanChangeNodeAttribute(OrgOptParam orgOptParam) {
        BdCrmOrganizationDO currentOptOrg = orgOptParam.getCurrentOptOrg();
        BdCrmOrganizationDO beforeChangeOrg = orgOptParam.getBeforeChangeOrg();
        if (currentOptOrg.getOrgAttribute() == leafNodeCode) {
            CrowdfundingCity crowdfundingCity = crowdfundingCityService.getById(orgOptParam.getCityId());
            if (crowdfundingCity == null || crowdfundingCity.getLevel() != 1 || crowdfundingCity.getName().compareTo(orgOptParam.getCityName()) != 0) {
                return NewResponseUtil.makeFail("叶子节点必需且只能关联城市");
            }
            //将城市自动映射成对应的真正的城市
            if (!ObjectUtils.nullSafeEquals(crowdfundingCity.getRealParentId(), crowdfundingCity.getParentId()) && crowdfundingCity.getRealParentId() > 0) {
                CrowdfundingCity realCity = crowdfundingCityService.getById(crowdfundingCity.getRealParentId());
                if (realCity != null) {
                    log.info("cityId:{}cityName:{}重新设置对应的城市为:{}", crowdfundingCity.getId(), crowdfundingCity.getName(), realCity.getName());
                    orgOptParam.setCityId(realCity.getId());
                    orgOptParam.setCityName(realCity.getName());
                }
            }
        }
        if (ObjectUtils.nullSafeEquals(currentOptOrg.getOrgAttribute(), beforeChangeOrg.getOrgAttribute())) {
            log.info("节点信息无修改");
            return NewResponseUtil.makeSuccess(true);
        }
        //如果叶子->非叶子要判断是否还有顾问
        List<BdCrmOrgUserRelationDO> relationDOList = listMemberUnderOrgByOrgId(beforeChangeOrg.getId());
        //修改了节点属性需要设置移除人员
        if (CollectionUtils.isNotEmpty(relationDOList)) {
            if (beforeChangeOrg.getOrgAttribute() == leafNodeCode) {
                return NewResponseUtil.makeFail(String.format("%s下存在非管理岗，请先将成员移除后操作", beforeChangeOrg.getOrgName()));
            } else {
                return NewResponseUtil.makeFail(String.format("%s下存在管理岗，请先将成员移除后操作", beforeChangeOrg.getOrgName()));
            }
        }
        //非叶子转化为叶子，需要确认组织下是否还有其他
        List<BdCrmOrganizationDO> subOrgs = crmOrganizationService.findDirectSubOrgByOrgId(currentOptOrg.getId());
        if (CollectionUtils.isNotEmpty(subOrgs) && currentOptOrg.getOrgAttribute() == leafNodeCode) {
            log.info("非叶子结点:{}转化为叶子结点,存在子节点", beforeChangeOrg.getOrgName());
            //如果它下面还存在子节点不能删除
            return NewResponseUtil.makeFail(String.format("%s下存在子组织，请移动后操作", beforeChangeOrg.getOrgName()));
        }

        //非叶子转化为叶子结点时
        boolean nodeForbidName = nodeForbidName(orgOptParam);
        if (nodeForbidName) {
            return NewResponseUtil.makeFail("叶子结点不允许设置为'市辖市'或'省直辖县级行政区划'");
        }
        return NewResponseUtil.makeSuccess(true);
    }


    @Override
    public List<OrganizationMemberOptEnum> getNeedCheckOptEnums() {
        return Lists.newArrayList(OrganizationMemberOptEnum.edit_node);
    }

}
