package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdQyWxBindMappingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.qywx.QywxBindingQueryParam;

import java.util.List;

/**
 * 顾问与企业微信绑定信息(BdQyWxAdvisorMapping)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-11 11:33:18
 */
public interface BdQyWxBindMappingService {


    BdQyWxBindMappingDO queryById(long id);

    int insert(BdQyWxBindMappingDO bdQyWxAdvisorMapping);

    int updateNickName(long id, String nickName, String avatarUrl);

    int update(BdQyWxBindMappingDO bdQyWxAdvisorMapping);

    List<BdQyWxBindMappingDO> listByUniqueCode(String uniqueCode);

    List<BdQyWxBindMappingDO> queryByRobotUserId(String robotUserId);

    BdQyWxBindMappingDO getByRobotUserIdAndUniqueCode(String robotUserId, String uniqueCode);

    BdQyWxBindMappingDO queryByExternalUserId(String externalUserId);

    List<BdQyWxBindMappingDO> queryByName(String name);

    /**
     * 分页查询企微绑定关系
     * @param queryParam 查询参数
     * @return 绑定关系列表
     */
    List<BdQyWxBindMappingDO> listBindingByPage(QywxBindingQueryParam queryParam);

    /**
     * 统计企微绑定关系数量
     * @param queryParam 查询参数
     * @return 总数量
     */
    int countBinding(QywxBindingQueryParam queryParam);

    /**
     * 逻辑删除企微绑定关系
     * @param id 绑定关系ID
     * @return 删除结果，大于0表示成功
     */
    int deleteById(long id);

    /**
     * 根据机器人用户ID批量删除绑定关系
     * @param robotUserId 机器人用户ID
     * @return 删除的记录数
     */
    int deleteByRobotUserId(String robotUserId);

    /**
     * 根据机器人用户ID统计绑定关系数量
     * @param robotUserId 机器人用户ID
     * @return 绑定关系数量
     */
    int countByRobotUserId(String robotUserId);
} 