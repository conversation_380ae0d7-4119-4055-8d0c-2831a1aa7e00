package com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.CfBdCaseDonateTaskConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate.IBdCaseDonateTaskConfigService;
import com.shuidihuzhu.cf.dao.donate.CfBdCaseDonateTaskConfigDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2022-06-30 15:24
 **/
@Service
public class BdCaseDonateTaskConfigServiceImpl implements IBdCaseDonateTaskConfigService {

    @Autowired
    private CfBdCaseDonateTaskConfigDao taskConfigDao;

    @Override
    public List<CfBdCaseDonateTaskConfigDO> listByCityConfigIds(List<Long> cityConfigIds) {
        if (CollectionUtils.isEmpty(cityConfigIds)) {
            return Lists.newArrayList();
        }
        return taskConfigDao.listByCityConfigIds(cityConfigIds);
    }
}
