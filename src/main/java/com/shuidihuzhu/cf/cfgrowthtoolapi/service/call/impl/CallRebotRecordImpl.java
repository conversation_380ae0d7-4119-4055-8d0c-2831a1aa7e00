package com.shuidihuzhu.cf.cfgrowthtoolapi.service.call.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.call.ICallRobotRecord;
import com.shuidihuzhu.client.baseservice.msg.callrobot.MsgCallRobotClientV2;
import com.shuidihuzhu.client.baseservice.msg.callrobot.request.CallRobotRecordSendRequest;
import com.shuidihuzhu.client.baseservice.msg.callrobot.request.TemplateInfoItem;
import com.shuidihuzhu.client.baseservice.msg.callrobot.response.CallRobotRecordSendResponse;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.msg.vo.MsgResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Yuhang
 * @Date 2022/10/24 15:13
 */
@Slf4j
@Service
public class CallRebotRecordImpl implements ICallRobotRecord {

    @Resource
    private MsgCallRobotClientV2 msgCallRobotClientV2;

    @Override
    public MsgResponse<CallRobotRecordSendResponse> callRobotRecordSend(CallRobotRecordSendRequest callRobotRecordSendRequest, CfBdCaseInfoDo cfBdCaseInfoDo, CrowdfundingVolunteer volunteer) {
        if (Objects.nonNull(callRobotRecordSendRequest) && StringUtils.isNotEmpty(volunteer.getMobile()) && StringUtils.isNotEmpty(cfBdCaseInfoDo.getPatientName())) {
            //组装被叫顾问信息
            List<TemplateInfoItem> templateInfoItemList = Lists.newArrayList();
            TemplateInfoItem templateInfoItem = new TemplateInfoItem();

            //被叫顾问加密手机号
            templateInfoItem.setMobile(volunteer.getMobile());
            //外呼内容中的患者姓名
            String patientName = cfBdCaseInfoDo.getPatientName();

            Map<String, String> customParamMap = Maps.newHashMap();
            //模版中动态参数用的{0}代替，key设为0
            customParamMap.put("0", patientName);
            List<Map<String, String>> customParam = Lists.newArrayList();
            customParam.add(customParamMap);

            templateInfoItem.setCustomParam(customParam);

            templateInfoItemList.add(templateInfoItem);

            //用户信息列表
            callRobotRecordSendRequest.setTemplateInfo(templateInfoItemList);

            log.info("callParam {}", callRobotRecordSendRequest);

            MsgResponse<CallRobotRecordSendResponse> response =  msgCallRobotClientV2.callRobotRecordSend(callRobotRecordSendRequest);
            log.info("callResponse {}", response);
            return response;
        }
        return null;
    }
}
