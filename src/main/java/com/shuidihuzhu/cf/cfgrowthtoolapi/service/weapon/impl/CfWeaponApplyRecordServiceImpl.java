package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponApplyRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.MemberUseBudgetInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponApplyRecordModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.ICfWeaponApplyRecordService;
import com.shuidihuzhu.cf.dao.weapon.CfWeaponApplyRecordDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * @author: fengxuan
 * @create 2020-10-16 2:24 下午
 **/
@Slf4j
@Service
public class CfWeaponApplyRecordServiceImpl implements ICfWeaponApplyRecordService {

    @Autowired
    private CfWeaponApplyRecordDao applyRecordDao;

    @Override
    public List<CfWeaponApplyRecordDO> todayApplyLimitRecord(int budgetId) {
        DateTime startTime = DateTime.now().withTimeAtStartOfDay();
        DateTime endTime = startTime.plusDays(1);
        return applyRecordDao.listApplySuccessByTimeRange(budgetId,
                startTime.toDate(), endTime.toDate(),
                Lists.newArrayList(WeaponEnums.BudgetApplyStatusEnum.applying.getStatus(),
                        WeaponEnums.BudgetApplyStatusEnum.leader_pass.getStatus(),
                        WeaponEnums.BudgetApplyStatusEnum.activity_pass.getStatus()));
    }

    @Override
    public List<CfWeaponApplyRecordDO> listByCaseIdAndActivityType(int caseId, List<Integer> activityTypes) {
        return applyRecordDao.listByCaseIdAndTypes(caseId, activityTypes);
    }

    @Override
    public boolean checkCaseApplyRepeat(Integer caseId, int activityType) {
        //案例是否还在中间状态
        List<CfWeaponApplyRecordDO> cfWeaponApplyRecordDOS = applyRecordDao.listByCaseIdAndActivityType(caseId, activityType, WeaponEnums.middleApplyStatus);
        return CollectionUtils.isNotEmpty(cfWeaponApplyRecordDOS);
    }

    @Override
    public CfWeaponApplyRecordDO getById(int id) {
        return applyRecordDao.getById(id);
    }

    @Override
    public int addApplyLeader(int id, String leaderUniqueCode) {
        if (StringUtils.isBlank(leaderUniqueCode) || id <= 0) {
           return 0;
        }
        return applyRecordDao.addApplyLeader(id, leaderUniqueCode);
    }

    @Override
    public List<CfWeaponApplyRecordDO> listByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return applyRecordDao.listByIds(ids);
    }


    @Override
    public List<CfWeaponApplyRecordDO> listByBudgetGroupId(int budgetGroupId, String startTime, String endTime) {
        return applyRecordDao.listByBudgetGroupId(budgetGroupId, startTime, endTime);
    }


    @Override
    public int updateApplyStatusFromLeader(int applyId, WeaponEnums.BudgetApplyStatusEnum applyStatusEnum, String comment) {
        if (applyStatusEnum == null) {
            return 0;
        }
        return applyRecordDao.updateApplyStatusFromLeader(applyId, applyStatusEnum.getStatus(), comment);
    }

    @Override
    public int updateApplyStatusFromActivity(int applyId, String activityName, WeaponEnums.BudgetApplyStatusEnum applyStatusEnum, String comment, Long callbackId) {
        if (applyStatusEnum == null) {
            return 0;
        }
        return applyRecordDao.updateApplyStatusFromActivity(applyId, applyStatusEnum.getStatus(), activityName, comment, callbackId);
    }

    @Override
    public int insertBySubmit(WeaponApplyRecordModel weaponApplyRecordModel) {
        return applyRecordDao.insert(weaponApplyRecordModel);
    }

    @Override
    public long countByUniqueCode(String uniqueCode, Date queryStartTime, Date queryEndTime, Integer weaponId) {
        if (StringUtils.isEmpty(uniqueCode)) {
            return 0;
        }
        return applyRecordDao.countByUniqueCode(uniqueCode, queryStartTime, queryEndTime, weaponId);
    }

    @Override
    public List<CfWeaponApplyRecordDO> listApplyInfoByUniqueCode(String uniqueCode, Date queryStartTime, Date queryEndTime, Integer weaponId, int offSet, int pageSize) {
        if (StringUtils.isEmpty(uniqueCode)) {
            return Lists.newArrayList();
        }
        return applyRecordDao.listApplyInfoByUniqueCode(uniqueCode, queryStartTime, queryEndTime, weaponId, offSet, pageSize);
    }

    @Override
    public int updateExtInfo(int applyId, String extInfo) {
        return applyRecordDao.updateExtInfo(applyId,extInfo);
    }

    @Override
    public List<CfWeaponApplyRecordDO> listAllApplyRecord(int offset, int limit) {
        return applyRecordDao.listAllApplyRecord(offset, limit);
    }

    @Override
    public int countMemberUseInfoGroupByUniqueCode(List<Integer> budgetIdList) {
        if (CollectionUtils.isEmpty(budgetIdList)) {
            return 0;
        }
        return applyRecordDao.countMemberUseInfoGroupByUniqueCode(budgetIdList);
    }

    @Override
    public List<MemberUseBudgetInfo> listMemberUseInfoGroupByUniqueCode(List<Integer> budgetIdList, int offset, int limit) {
        if (CollectionUtils.isEmpty(budgetIdList)) {
            return Lists.newArrayList();
        }
        return applyRecordDao.listMemberUseInfoGroupByUniqueCode(budgetIdList, offset, limit);
    }

    @Override
    public int countByWeaponId(int weaponId) {
        return applyRecordDao.countByWeaponId(weaponId);
    }

    @Override
    public long countByOrgIds(List<Integer> orgIds, Integer applyStatus, Date queryStartTime, Date queryEndTime, Integer weaponId) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return 0L;
        }
        return applyRecordDao.countByOrgIds(orgIds, applyStatus, queryStartTime, queryEndTime, weaponId);
    }

    @Override
    public List<CfWeaponApplyRecordDO> listApplyByOrgIds(List<Integer> budgetIds, Integer applyStatus, Date queryStartTime, Date queryEndTime, Integer weaponId, int offSet, int pageSize) {
        if (CollectionUtils.isEmpty(budgetIds)) {
            return Lists.newArrayList();
        }
        return applyRecordDao.listApplyByOrgIds(budgetIds, applyStatus, queryStartTime, queryEndTime, weaponId, offSet, pageSize);
    }

    @Override
    public CfWeaponApplyRecordDO getByCaseId(int caseId) {
        return applyRecordDao.getByCaseId(caseId);
    }

    @Override
    public CfWeaponApplyRecordDO getByCaseIdAndStatus(int caseId, int activityType, List<Integer> applyStatusList) {
        return applyRecordDao.getByCaseIdAndStatus(caseId, activityType, applyStatusList);
    }

    @Override
    public int updateApplyMoney(int applyId, int unUsedMoney) {
        return applyRecordDao.updateApplyMoney(applyId, unUsedMoney);
    }

    @Override
    public void deleteApplyRecord(long id) {
        applyRecordDao.deleteApplyRecord(id);
    }

    @Override
    public CfWeaponApplyRecordDO getByCaseIdAndActivityTypeAndStatus(int caseId, List<Integer> activityTypeList, List<Integer> applyStatusList) {
        return applyRecordDao.getByCaseIdAndActivityTypeAndStatus(caseId, activityTypeList, applyStatusList);
    }

    @Override
    public List<CfWeaponApplyRecordDO> listByUniqueCodeByBudgetId(String uniqueCode, int budgetId) {
        if(StringUtils.isEmpty(uniqueCode) || budgetId <= 0){
            return Lists.newArrayList();
        }
        return applyRecordDao.listByUniqueCodeByBudgetId(uniqueCode, budgetId);
    }

    @Override
    public List<CfWeaponApplyRecordDO> listByGroupIdAndApplyStatus(int budgetGroupId, List<Integer> applyStatusList) {
        if (budgetGroupId <= 0 || CollectionUtils.isEmpty(applyStatusList)) {
            return Lists.newArrayList();
        }
        return applyRecordDao.listByGroupIdAndApplyStatus(budgetGroupId, applyStatusList);
    }

    @Override
    public List<CfWeaponApplyRecordDO> listByGroupIdsAndApplyStatus(List<Integer> budgetGroupIds, List<Integer> applyStatusList) {
        if (CollectionUtils.isEmpty(budgetGroupIds) || CollectionUtils.isEmpty(applyStatusList)) {
            return Lists.newArrayList();
        }
        return applyRecordDao.listByGroupIdsAndApplyStatus(budgetGroupIds, applyStatusList);
    }

    @Override
    public List<CfWeaponApplyRecordDO> listByBudgetIdAndUniqueCodesAndApplyStatus(int budgetId, Set<String> uniqueCodes, List<Integer> applyStatusList) {
        if (budgetId <= 0 || CollectionUtils.isEmpty(uniqueCodes) || CollectionUtils.isEmpty(applyStatusList)) {
            return Lists.newArrayList();
        }
        return applyRecordDao.listByBudgetIdAndUniqueCodesAndApplyStatus(budgetId, uniqueCodes, applyStatusList);
    }
}
