package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IQywxSdkDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.channel.IDedicatedServiceFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.DotTagContentBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.QyWechatCallBackPayload;
import com.shuidihuzhu.cf.cfgrowthtoolapi.outfeignclient.IGrowthtoolOfflineClient;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfDotService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolAsyncPoolConstants;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.qywechat.AesException;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.qywechat.WXBizMsgCrypt;
import com.shuidihuzhu.cf.controller.callback.QyWechatCallback;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewQyWxCorpDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.ExecutorService;

/**
 * @author: lichengjin
 * @create 2023-11-13 15:46
 **/
@Slf4j
@Service
public class QyWxCallbackDecryptEventService {
    @Autowired
    private Environment environment;
    @Autowired
    private ApolloService apolloService;
    @Autowired
    private IQywxSdkDelegate qywxSdkDelegate;
    @Autowired
    private IDedicatedServiceFacade dedicatedServiceFacade;
    @Autowired
    private IGrowthtoolOfflineClient growthtoolOfflineClient;
    @Autowired
    private ICfDotService cfDotService;
    @Resource(name = GrowthtoolAsyncPoolConstants.SMALL_USE_EXECUTOR_SERVICE)
    private ExecutorService smallUseExecutorService;

    public ImmutablePair<CfClewQyWxCorpDO,WXBizMsgCrypt> getWxBizMsgCryptByCallbackId(int callbackId) {
        //根据callbackId 获取CfClewWxCorpMsgDO
        CfClewQyWxCorpDO cfClewQyWxCorpDO = qywxSdkDelegate.getQyWxCorpSelfAppByCallbackId(callbackId);
        if (cfClewQyWxCorpDO == null){
            return null;
        }
        WXBizMsgCrypt wxcpt = null;
        try {
            wxcpt = new WXBizMsgCrypt(cfClewQyWxCorpDO.getToken(), cfClewQyWxCorpDO.getAesKey(), cfClewQyWxCorpDO.getCorpId());
        } catch (AesException e) {
            log.error(this.getClass().getSimpleName()+" getWxBizMsgCryptByCallbackId new WXBizMsgCrypt err:",e);
        }
        if (Objects.isNull(wxcpt)){
            return null;
        }
        return ImmutablePair.of(cfClewQyWxCorpDO,wxcpt);
    }

    public String verifyURL(WXBizMsgCrypt wxcpt, String echostr, String msg_signature, String timestamp, String nonce, CfClewQyWxCorpDO qyWxCorpDO){
        String sEchoStr = null;
        try {
            sEchoStr = wxcpt.VerifyURL(msg_signature, timestamp,
                    nonce, echostr);
            log.info("verifyurl echostr: {}", sEchoStr);
            // 验证URL成功，将sEchoStr返回
            // HttpUtils.SetResponse(sEchoStr);
        } catch (Exception e) {
            //验证URL失败，错误原因请查看异常
            log.error(this.getClass().getSimpleName()+" commonFallback err:",e);
            DotTagContentBuilder dotTagContentBuilder = DotTagContentBuilder.create().subject(GeneralConstant.CF_PATIENT_QYWX_EVENT_CALLBACK_DECRYPT)
                    .payload("action","verifyUrl")
                    .payload("corp_id",qyWxCorpDO.getCorpId())
                    .payload("callback_id",qyWxCorpDO.getCallbackId())
                    .payload("result","fail")
                    .payload("message", e.getMessage());
            cfDotService.report(dotTagContentBuilder);
        }
        return sEchoStr;
    }

    public String decryptMsg(WXBizMsgCrypt wxcpt, String msgSig, String timestamp, String nonce, String reqData, CfClewQyWxCorpDO qyWxCorpDO) {
        String decryptMsg = null;
        try {
            decryptMsg = wxcpt.DecryptMsg(msgSig, timestamp, nonce, reqData);
            log.info("{} decryptMsg after decrypt msg {}", this.getClass().getSimpleName(), decryptMsg);
        } catch (Exception e) {
            // 解密失败，失败原因请查看异常
            log.warn(this.getClass().getSimpleName() + " decryptMsg err:", e);
            DotTagContentBuilder dotTagContentBuilder = DotTagContentBuilder.create().subject(GeneralConstant.CF_PATIENT_QYWX_EVENT_CALLBACK_DECRYPT)
                    .payload("action", "decryptMsg")
                    .payload("corpId", qyWxCorpDO.getCorpId())
                    .payload("callbackId", qyWxCorpDO.getCallbackId())
                    .payload("result", "fail")
                    .payload("message", e.getMessage());
            cfDotService.report(dotTagContentBuilder);
        }
        return decryptMsg;
    }

    public String handleByMq(QyWechatCallBackPayload qyWechatCallBackPayload) {
        String postData = qyWechatCallBackPayload.getPostData();
        int callbackId = qyWechatCallBackPayload.getCallbackId();
        String echostr = qyWechatCallBackPayload.getEchostr();
        String nonce = qyWechatCallBackPayload.getNonce();
        String msgSig = qyWechatCallBackPayload.getMsgSig();
        String timestamp = qyWechatCallBackPayload.getTimestamp();

        boolean isProduction = environment.acceptsProfiles(Profiles.of("production"));
        log.info("{} selfAppCallback commonFallback isProduction {}", this.getClass().getSimpleName(), isProduction);
        if(isProduction && apolloService.getSyncOfflineSet().contains(callbackId)) {
            //将线下请求转发到线下
            smallUseExecutorService.submit(() -> {
                try {
                    QyWechatCallback.CallBackData callBackData = new QyWechatCallback.CallBackData();
                    callBackData.setEchostr(echostr);
                    callBackData.setMsgSig(msgSig);
                    callBackData.setTimestamp(timestamp);
                    callBackData.setNonce(nonce);
                    callBackData.setPostData(postData);
                    String result = growthtoolOfflineClient.transferOnline2Offline(callbackId,callBackData);
                    log.info(this.getClass().getSimpleName()+" selfAppCallback commonFallback transfer result:{}", result);
                } catch (Exception e) {
                    log.warn(this.getClass().getSimpleName()+" selfAppCallback commonFallback transfer Exception", e);
                }
            });
        }
        String decryptMsg = qyWechatCallBackPayload.getDecryptMsg();
        CfClewQyWxCorpDO qyWxCorpDO = qyWechatCallBackPayload.getQyWxCorpDO();
        if (StringUtils.isNotBlank(decryptMsg) && Objects.nonNull(qyWxCorpDO)) {
            return this.doHandelQyWxEventFromDecryptMsg(decryptMsg, qyWxCorpDO);
        } else {
            // 异常
            log.warn("{} handleByMq request {}", this.getClass().getSimpleName(), JSONObject.toJSON(qyWechatCallBackPayload));
            return "";
        }
    }

    private String doHandelQyWxEventFromDecryptMsg(String decryptMsg, CfClewQyWxCorpDO cfClewQyWxCorpDO) {
        log.info("{} doHandelQyWxEventFromDecryptMsg decryptMsg {} qyWxCorpDO {} ", this.getClass().getSimpleName(), decryptMsg, JSONObject.toJSON(cfClewQyWxCorpDO));
        if (apolloService.getSideBarCorpIdMap().containsKey(cfClewQyWxCorpDO.getCorpId())) {
            try {
                dedicatedServiceFacade.doHandelQyWxEventFromDecryptMsg(decryptMsg, cfClewQyWxCorpDO);
            } catch (Exception e) {
                log.warn(this.getClass().getSimpleName() + " doHandelQyWxEventFromDecryptMsg msg {} cfClewWxCorpMsgDO {} error", decryptMsg, JSONObject.toJSON(cfClewQyWxCorpDO), e);
            }
        }
        return "success";
    }
}
