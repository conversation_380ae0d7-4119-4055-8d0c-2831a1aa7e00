package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WeaponSearchParam;

import java.util.*;

/**
 * @author: fengxuan
 * @create 2020-10-14 9:36 下午
 **/
public interface ICfWeaponService {

    int insert(CfWeaponDO cfWeaponDO);


    int editCfWeapon(CfWeaponDO cfWeaponDO);


    List<CfWeaponDO> listAllWeapon();

    /**
     * 查询所有武器 不包含下架武器
     * @param tab
     * @return
     */
    List<CfWeaponDO> listAllWeapon(Integer tab);

    /**
     * 查询武器信息 不包含下架武器
     * @param tab
     * @param ids
     * @return
     */
    List<CfWeaponDO> listWeaponByTabAndIds(Integer tab, Set<Integer> ids);

    CfWeaponDO getById(int weaponId);

    CfWeaponDO getByActivityId(int activityId);

    int handleWeapon(int weaponId, Integer useStatus, int isDelete, Date useTime);

    int countByParam(WeaponSearchParam queryParam);

    List<CfWeaponDO> queryCfWeaponDoListByParam(WeaponSearchParam queryParam);

    int getWeaponIdByActivityId(int activityId);

    CfWeaponDO getByActivityTypeAndBudgetManagementId(int activityType, int budgetManagementId);

    List<CfWeaponDO> listByActivityTypeAndBudgetManagementId(int activityType, List<Integer> budgetManagementIds);
}
