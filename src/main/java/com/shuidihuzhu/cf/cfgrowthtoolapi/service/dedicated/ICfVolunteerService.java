package com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmUserExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfVolunteerMis;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfVolunteerProvinceAndCity;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.VolunteerAutoChangeModel;
import com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO;
import com.shuidihuzhu.cf.model.dedicated.VolunteerIdCardModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.*;

import java.util.*;

/**
 /**
 * @author: wanghui
 * @create: 2019/1/14 8:07 PM
 */
public interface ICfVolunteerService {
    CfVolunteerModelDto fullCfVolunteerModelByCfVolunteer(CrowdfundingVolunteer crowdfundingVolunteer, UserInfoModel userInfoModel);

    int updateEntryLeaveTime(VolunteerAutoChangeModel changeModel);

    List<CrowdfundingVolunteer> getCrowdfundingVolunteersByVolunteerType(int volunteerType, int offset, int limit);

    CrowdfundingVolunteer getByUniqueCode(String uniqueCode);

    CfCrmUserExtDO buildCfCrmUserExtBaseInfo(String volunteerName, String mis, String uniqueCode);

    CfUserVolunteerRelationDO getAccountUserAndVolunteerRelation(String openId);

    CfUserVolunteerRelationDO getAccountUserAndVolunteerRelationByPhone(String phone);

    int addUserVolunteerRelationDO(CfUserVolunteerRelationDO volunteerRelationDO);

    CfVolunteerMaterialDO getVolunteerMateri(String uniqueCode);

    int addCfVolunteerMaterial(CfVolunteerMaterialDO materialDO);

    int updateCfVolunteerMaterial(CfVolunteerMaterialDO materialDO);

    int updateZhanzhangVerifyPictures(String uniqueCode, String s, String s1);

    int updateYihuVerifyPictures(String uniqueCode, String s,String s1);

    CrowdfundingVolunteer getVolunteerByUniqueCode(String uniqueCode);

    CrowdfundingVolunteer getVolunteerByMis(String mis);

    CrowdfundingVolunteer getOnWorkVolunteerByMis(String mis);

    CrowdfundingVolunteer getOnWorkVolunteerByJobNum(String jobNum);

    List<CrowdfundingVolunteer> getOnWorkCfVolunteerDOByLimit(Long currentId, int size);

    List<CrowdfundingVolunteer> getCfVolunteerDOByUniqueCodes(Collection<String> uniqueCodes);

    int addVolunteer(CrowdfundingVolunteer crowdfundingVolunteer);

    PageReturnModel<CrowdfundingVolunteer> getVolunteer(VolunteerSearchModel volunteerSearchModel, boolean needPage, Integer pageNo, Integer pageSize);

    CrowdfundingVolunteer getCrowdfundingVolunteerById(Long id);

    void updateVolunteerInfoById(CrowdfundingVolunteer crowdfundingVolunteer);

    void updateQrCode(String qrCode, Long id);

    boolean checkIsExistByTypeOrIdentity(Integer volunteerType, String idCardNumber, Long vid);

    List<CrowdfundingVolunteer> listByTypeIdentity(Integer volunteerType, String idCardNumber, Long vid);

    boolean checkIsExistByTypeOrEmail(Integer volunteerType, String email, Long vid);

    void updateApplyStatusById(Long id, Integer applyStatus, String operatorName, Long operatorUserId, String angelUrl, String refuseReasons, String qrCode);

    List<String> getVolunteerUniqueByMobileAndWorkStatus(int workStatus, String mobile);

    List<String> getUniqueCodeListByVolunteerType(Integer volunteerType);

    List<CrowdfundingVolunteer> getVolunteerListByMisList(List<String> misList);

    List<CrowdfundingVolunteer> getXianXiaVolunteerListByRole(Integer level);

    CrowdfundingVolunteer getCrowdfundingVolunteerByUniqueCode(String uniqueCode);

    CrowdfundingVolunteer getByPhone(String mobile);

    CrowdfundingVolunteer getByPhoneAndWorkStatus(int workStatus, String mobile);

    List<CrowdfundingVolunteer> getVolunteerByPhone(String mobile);

    CrowdfundingVolunteer getByIdCardNumber(String idCardNumber);

    List<String> getUniqueCodeByIdCardNumber(String idCardNumber);

    CrowdfundingVolunteer getVolunteerByEmail(String email);

    CrowdfundingVolunteer getOnWorkVolunteerByUniqueCode(String uniqueCode);

    int addNewVolunteer(CrowdfundingVolunteer cfVolunteer);

    int updateBaseInfoApplyStatus(String uniqueCode, Integer i);

    int updateVerifyInfoApplyStatus(String uniqueCode, Integer verifyInfoStatus, Integer applyStatus);

    int updateVolunteerInfo(String uniqueCode, VolunteerIdCardModel idCardModel);

    List<CrowdfundingVolunteer> getCrowdfundingVolunteerByMobile(List<String> mobile);

    int updateEntryTime(long id, Date entryTime, String system);

    List<Integer> getProvinceByMis(Set<String> misName);

    List<CfVolunteerProvinceAndCity> getProvinceAndCityByMis(Set<String> misName);

    List<CfVolunteerMis> getOnWorkMis(long currentId, List<Integer> levels);

	List<CrowdfundingVolunteer> getOnWorkGwDOByLimit(Long currentId, int size);

	void updateVEncryptMobiles(long id, String vEncryptMobile);

	List<CrowdfundingVolunteer> getOnWorkWithNoVEncryptMobileGwByLimit(long currentId);

    List<CrowdfundingVolunteer> fuzzyQueryByMis(String mis);

    List<CrowdfundingVolunteer> fuzzyQueryByVolunteerName(String volunteerName);

    List<CrowdfundingVolunteer> exactQueryByVolunteerName(String volunteerName);

	String getEncryptVPhone(String mis);

	String getEncryptVPhone(CrowdfundingVolunteer crowdfundingVolunteer, String mis);

    int updateMisBlankById(Long id);

    List<CrowdfundingVolunteer> listAllOnWorkVolunteer();

    List<CrowdfundingVolunteer> listAllOnWorkVolunteerByRoleList(List<String> roleList);

    List<CrowdfundingVolunteer> getGwInfoByIdCardNumberList(List<String> idCardList);

    List<CrowdfundingVolunteer> listAllOnWorkVolunteerByLevels(List<Integer> levels);

    List<CrowdfundingVolunteer> getXianXiaVolunteerListByRoles(List<Integer> roleCodes);

    CrowdfundingVolunteer getByJobNum(String jobNum);

    List<CrowdfundingVolunteer> listByJobNums(List<String> jobNums);

    List<CrowdfundingVolunteer> getXianXiaVolunteerListByRoleWithCityCodeFromCache(Integer cityCode);

    List<CrowdfundingVolunteer> listByIds(List<Long> ids);

    void updateGrTag(List<String> uniqueCodeList, int grTag);

    OpResult<Void> checkIsShowForC(CrowdfundingVolunteer crowdfundingVolunteer);

    List<CrowdfundingVolunteer> getPartnerInfoByIdCardNumberList(List<String> idCardList);

    List<CrowdfundingVolunteer> getPartnerInfoByUniqueCode(List<String> uniqueCodes);

    List<CrowdfundingVolunteer> listAllVolunteer();

    List<CrowdfundingVolunteer> fuzzyAllQueryByVolunteerName(String name);

    List<CrowdfundingVolunteer> listVolunteerByIdCardListOfIgnoreWorkStatus(List<String> encryptIdCardList);

    void getSubscribeMiaoyiToTag(Long minId);

    void getSubscribeMiaoyiToTagById(Long id);

}
