package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.shorturl.ShortUrlDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.export.BudgetDetailExportModelForBase;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.growthtool.enums.WeaponActivityTypeEnum;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-12-09 10:23 下午
 **/
@Slf4j
public abstract class AbstractWeaponExcelService implements IWeaponExcelService {

    @Autowired
    private ICfWeaponApplyRecordService applyRecordService;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Autowired
    private ICrmSelfBuiltOrgReadService orgReadService;

    @Autowired
    private ShortUrlDelegate shortUrlDelegate;

    @Autowired
    private ICfWeaponBudgetGroupService budgetGroupService;

    @Autowired
    private ICfWeaponService weaponService;


    protected abstract void doExportExcel(HttpServletResponse response, List<BudgetDetailExportModelForBase> baseList, long adminLongUserId) throws IOException;


    public List<BudgetDetailExportModelForBase> listExcelBase(ExcelNeedModel excelNeedModel) {
        List<CfWeaponApplyRecordDO> applyRecordDOList = excelNeedModel.getApplyRecordDOList();
        List<Integer> caseIds = applyRecordDOList.stream().map(CfWeaponApplyRecordDO::getCaseId)
                .filter(item -> item > 0)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, CrowdfundingInfo> caseIdTModel = Maps.newHashMap();
        //查找对应的案例详情
        if (CollectionUtils.isNotEmpty(caseIds)) {
            List<CrowdfundingInfo> crowdfundingInfoList = Lists.newArrayList();
            Lists.partition(caseIds, 100)
                    .forEach(item -> crowdfundingInfoList.addAll(crowdFundingFeignDelegate.getCrowdfundingListById(item)));
            caseIdTModel = crowdfundingInfoList
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (before, after) -> before));
        }
        //组织-路径
        Map<Long, String> orgIdTChain = orgReadService.listChainByOrgIdsWithDefaultSplitter(applyRecordDOList.stream()
                .map(item -> (long) item.getApplyOrgId())
                .collect(Collectors.toList()));

        List<BudgetDetailExportModelForBase> modelList = Lists.newArrayList();
        for (CfWeaponApplyRecordDO cfWeaponApplyRecordDO : applyRecordDOList) {
            CrowdfundingInfo crowdfundingInfo = caseIdTModel.get(cfWeaponApplyRecordDO.getCaseId());
            String orgPath = orgIdTChain.getOrDefault((long) cfWeaponApplyRecordDO.getApplyOrgId(), "");
            BudgetDetailExportModelForBase modelForC = BudgetDetailExportModelForBase.build(cfWeaponApplyRecordDO, orgPath);
            if (crowdfundingInfo != null) {
                //案例链接
                String linkUrl = shortUrlDelegate.process(GeneralConstant.caseBaseUrl + crowdfundingInfo.getInfoId());
                modelForC.setCaseLink(linkUrl);
                modelForC.setCaseTitle(crowdfundingInfo.getTitle());
            }
            modelList.add(modelForC);
        }
        return modelList;
    }


    public ExcelNeedModel createNeedModel(CfWeaponBudgetGroupDO budgetGroupDO, CfWeaponDO weaponDO, List<CfWeaponApplyRecordDO> applyRecordDOList) {
        ExcelNeedModel excelNeedModel = new ExcelNeedModel();
        excelNeedModel.applyRecordDOList = applyRecordDOList;
        excelNeedModel.cfWeaponDO = weaponDO;
        excelNeedModel.budgetGroupDO = budgetGroupDO;
        return excelNeedModel;
    }


    @Override
    public void exportExcel(HttpServletResponse response, int budgetGroupId, String startTime, String endTime, long adminLongUserId) {
        CfWeaponBudgetGroupDO budgetGroupDO = budgetGroupService.getById(budgetGroupId);
        if (budgetGroupDO == null) {
            log.info("没有对应的预算组");
            return;
        }
        CfWeaponDO weaponDO = weaponService.getById(budgetGroupDO.getWeaponId());
        if (weaponDO == null) {
            log.info("没有对应的武器");
            return;
        }
        WeaponActivityTypeEnum activityTypeEnum = WeaponActivityTypeEnum.findByCode(weaponDO.getActivityType());
        if (activityTypeEnum == null) {
            log.info("武器类型暂不支持");
            return;
        }
        //判读当前类型是否支持导出
        boolean supportActivityType = supportActivityType(weaponDO.getActivityType());
        if (!supportActivityType) {
            return;
        }
        log.info("当前武器类型为:{},使用:{}导出", activityTypeEnum, this.getClass().getSimpleName());
        List<CfWeaponApplyRecordDO> applyRecordDOList = applyRecordService.listByBudgetGroupId(budgetGroupDO.getId(), startTime, endTime);
        ExcelNeedModel needModel = createNeedModel(budgetGroupDO, weaponDO, applyRecordDOList);
        List<BudgetDetailExportModelForBase> modelForBaseList = listExcelBase(needModel);
        List<BudgetDetailExportModelForBase> hasHandledBaseModelList = postProcess(modelForBaseList, needModel);
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        try {
            doExportExcel(response, hasHandledBaseModelList, adminLongUserId);
        } catch (IOException e) {
            log.error("预算使用明细导出失败", e);
        }

    }
}
