package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfTianRunVirtualPhonePoolDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfTianRunVirtualPhonePoolService;
import com.shuidihuzhu.cf.dao.bdcrm.CfTianRunVirtualPhonePoolDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2020-05-27 19:55
 */
@Service
public class CfTianRunVirtualPhonePoolService implements ICfTianRunVirtualPhonePoolService {

	@Autowired
	private CfTianRunVirtualPhonePoolDao cfTianRunVirtualPhonePoolDao;

	@Override
	public int saveCfTianRunVirtualPhonePoolDO(CfTianRunVirtualPhonePoolDO cfTianRunVirtualPhonePoolDO) {
		return cfTianRunVirtualPhonePoolDao.saveCfTianRunVirtualPhonePoolDO(cfTianRunVirtualPhonePoolDO);
	}

	@Override
	public int updateBindStatus(int bindStatus, String vEncryptMobile) {
		return cfTianRunVirtualPhonePoolDao.updateBindStatus(bindStatus, vEncryptMobile);
	}

	@Override
	public CfTianRunVirtualPhonePoolDO getCanAssignDataByCity(String city) {
		return cfTianRunVirtualPhonePoolDao.getCanAssignDataByCity(city);
	}

	@Override
	public CfTianRunVirtualPhonePoolDO getCfTianRunVirtualPhonePoolDOByVEncryptMobile(String vEncryptMobile) {
		return cfTianRunVirtualPhonePoolDao.getCfTianRunVirtualPhonePoolDOByVEncryptMobile(vEncryptMobile);
	}
}
