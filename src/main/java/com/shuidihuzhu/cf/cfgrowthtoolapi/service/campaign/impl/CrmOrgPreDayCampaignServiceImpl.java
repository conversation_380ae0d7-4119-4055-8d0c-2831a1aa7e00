package com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.CampCommonSearchParam;
import com.shuidihuzhu.cf.dao.campaign.CrmOrgPreDayCampaignDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.campaignv2.CrmOrgPreDayCampaignDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign.CrmOrgPreDayCampaignService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 每日-每个组织当天累计数据(CrmOrgPreDayCampaign)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-10 17:24:47
 */
@Slf4j
@Service
public class CrmOrgPreDayCampaignServiceImpl implements CrmOrgPreDayCampaignService {

    @Resource
    private CrmOrgPreDayCampaignDao crmOrgPreDayCampaignDao;

    @Override
    public void batchInsertOrUpdate(List<CrmOrgPreDayCampaignDO> crmOrgPreDayCampaignList, String dateKey) {
        if (CollectionUtils.isEmpty(crmOrgPreDayCampaignList)) {
            return;
        }
        Lists.partition(crmOrgPreDayCampaignList, 200)
                .forEach(item -> {
                    List<Long> orgIds = item.stream().map(CrmOrgPreDayCampaignDO::getOrgId).collect(Collectors.toList());
                    Map<Long, CrmOrgPreDayCampaignDO> orgTocampaignMap = crmOrgPreDayCampaignDao.listByDateKey(dateKey, orgIds)
                            .stream()
                            .collect(Collectors.toMap(CrmOrgPreDayCampaignDO::getOrgId, Function.identity(), (before, after) -> before));
                    List<CrmOrgPreDayCampaignDO> needAddData = item.stream()
                            .filter(campaign -> !orgTocampaignMap.containsKey(campaign.getOrgId()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(needAddData)) {
                        crmOrgPreDayCampaignDao.batchInsert(needAddData);
                    }
                    item.stream()
                            .filter(campaign -> orgTocampaignMap.containsKey(campaign.getOrgId()))
                            .forEach(campaign -> {
                                CrmOrgPreDayCampaignDO crmOrgPreDayCampaign = orgTocampaignMap.get(campaign.getOrgId());
                                campaign.setId(crmOrgPreDayCampaign.getId());
                                update(campaign);
                            });
                });
    }

    @Override
    public int update(CrmOrgPreDayCampaignDO crmOrgPreDayCampaign) {
        return crmOrgPreDayCampaignDao.update(crmOrgPreDayCampaign);
    }

    @Override
    public boolean deleteById(long id) {
        return crmOrgPreDayCampaignDao.deleteById(id) > 0;
    }

    @Override
    public CrmOrgPreDayCampaignDO sumByCampCommonSearchParam(CampCommonSearchParam searchParam) {
        if (searchParam == null || CollectionUtils.isEmpty(searchParam.getDays())) {
            return new CrmOrgPreDayCampaignDO();
        }
        CrmOrgPreDayCampaignDO crmOrgPreDayCampaign = crmOrgPreDayCampaignDao.sumByCampCommonSearchParam(searchParam);
        return Optional.ofNullable(crmOrgPreDayCampaign).orElse(new CrmOrgPreDayCampaignDO());
    }

    @Override
    public CrmOrgPreDayCampaignDO highDateByCampCommonSearchParam(CampCommonSearchParam searchParam) {
        if (searchParam == null || CollectionUtils.isEmpty(searchParam.getDays())) {
            return new CrmOrgPreDayCampaignDO();
        }
        CrmOrgPreDayCampaignDO crmOrgPreDayCampaign = crmOrgPreDayCampaignDao.highDateByCampCommonSearchParam(searchParam);
        return Optional.ofNullable(crmOrgPreDayCampaign).orElse(new CrmOrgPreDayCampaignDO());
    }

    @Override
    public CrmOrgPreDayCampaignDO avgDateByCampCommonSearchParam(CampCommonSearchParam searchParam) {
        if (searchParam == null || CollectionUtils.isEmpty(searchParam.getDays())) {
            return new CrmOrgPreDayCampaignDO();
        }
        if (searchParam.getDivider() <= 0) {
            log.info("除数小于0,需要check是否正常");
            return new CrmOrgPreDayCampaignDO();
        }
        CrmOrgPreDayCampaignDO crmOrgPreDayCampaign = crmOrgPreDayCampaignDao.avgDateByCampCommonSearchParam(searchParam);
        return Optional.ofNullable(crmOrgPreDayCampaign).orElse(new CrmOrgPreDayCampaignDO());
    }

    @Override
    public List<CrmOrgPreDayCampaignDO> listByDaysAndOrgId(long orgId, List<String> days) {
        if (CollectionUtils.isEmpty(days)) {
            return Lists.newArrayList();
        }
        return crmOrgPreDayCampaignDao.listByDaysAndOrgId(orgId, days);
    }
}