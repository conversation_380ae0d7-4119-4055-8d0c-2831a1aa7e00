package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfLogicHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.GdMapHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.ICrmNewHospitalFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.YiYuanModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.YuanQuModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.HospitalAreaModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.HospitalEditModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICrmNewHospitalService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICrowdfundingCityService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalService;
import com.shuidihuzhu.cf.dao.CfLogicHospitalDao;
import com.shuidihuzhu.cf.dao.hospital.CfHospitalApplyDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HospitalServiceImpl implements HospitalService {

    @Autowired
    private ICrowdfundingCityService crowdfundingCityService;

    @Resource
    private CfHospitalApplyDao cfHospitalApplyDao;

    @Resource
    private CfLogicHospitalDao cfLogicHospitalDao;

    @Autowired
    private ICrmNewHospitalFacade crmNewHospitalFacade;

    @Autowired
    private ICrmNewHospitalService crmNewHospitalService;

    @Override
    public Response<String> syncAreaHospitalAlias(String cityName, String logicName, String hospitalName, String alias) {
        if (StringUtils.isBlank(alias)) {
            return NewResponseUtil.makeSuccess(null);
        }
        crmNewHospitalFacade.syncAreaHospitalAlias(cityName, logicName, hospitalName, alias);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public void deleteHospital(HospitalEditModel hospitalEditModel) {
        HospitalAreaModel afterEditModel = hospitalEditModel.getAfterEditModel();
        String city = afterEditModel.getCity();
        List<HospitalAreaModel.HospitalAreaDetail> areaEditModelList = afterEditModel.getAreaEditModelList();
        List<String> hospitalNames = areaEditModelList.stream().map(HospitalAreaModel.HospitalAreaDetail::getAreaMainName).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<GdMapHospitalDO> hospitalByNamesAndCityList = crmNewHospitalService.getHospitalByNamesAndCity(hospitalNames, city);
        List<Long> ids = hospitalByNamesAndCityList.stream().map(GdMapHospitalDO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ids)) {
            log.info("删除的医院id:{}", JSON.toJSONString(ids));
            ids.forEach(id -> crmNewHospitalService.deleteById(id, 1));
        }
    }

    @Override
    public Response<String> syncManualHospitalByCity(String cityName) {
        crmNewHospitalFacade.syncManualHospitalByCity(cityName);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<String> addLogicToAreaHospitalByCity(String cityName) {
        crmNewHospitalFacade.addLogicToAreaHospitalByCity(cityName);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<String> addAreaHospital(String logicName, String hospitalName, String cityName, String provinceName, String addrees) {
        crmNewHospitalFacade.addAreaHospital(logicName, hospitalName, cityName, provinceName, addrees);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<String> addLogicHospital(String name, String province, String cityName, Integer areaCount, Integer independenceFlag, Integer materialFlag, String alias) {
        if (StringUtils.isBlank(name) || StringUtils.isBlank(cityName)) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        String city = cityName;
        if (city.endsWith("市")) {
            city = cityName.substring(0, cityName.length() - 1);
        }
        List<CfLogicHospitalDO> cfLogicHospitalDOS = cfLogicHospitalDao.getByHospitalNameAndCityName(name, city);
        if (CollectionUtils.isNotEmpty(cfLogicHospitalDOS)) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.HOSPITAL_EXSIT);
        }
        CfLogicHospitalDO cfLogicHospitalDO = new CfLogicHospitalDO();
        cfLogicHospitalDO.setHospitalName(name);
        cfLogicHospitalDO.setAliasNames(alias == null ? "" : alias);
        cfLogicHospitalDO.setCityName(cityName);
        cfLogicHospitalDO.setProvinceName(province);
        cfLogicHospitalDO.setVvhospitalCode(UUID.randomUUID().toString());
        cfLogicHospitalDO.setAreaCount(areaCount);
        cfLogicHospitalDO.setIndependenceFlag(independenceFlag);
        cfLogicHospitalDO.setMaterialFlag(materialFlag);
        cfLogicHospitalDao.insert(cfLogicHospitalDO);
        return NewResponseUtil.makeSuccess(cfLogicHospitalDO.getVvhospitalCode());
    }

    @Override
    public Response<String> addWaitHospital(String city, boolean isSync) {
        List<CrowdfundingCity> cities = crowdfundingCityService.getByCityNames(Lists.newArrayList(city));
        cities = cities.stream().filter(s -> s.getLevel() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cities)) {
            return NewResponseUtil.makeSuccess("city is empty");
        }
        if (cities.size() > 1) {
            return NewResponseUtil.makeSuccess("too many cites");
        }
        CrowdfundingCity province = crowdfundingCityService.getById(cities.get(0).getParentId());
        if (province == null || province.getLevel() != 0) {
            return NewResponseUtil.makeSuccess("province is empty");
        }

        //获取城市下待技术整理的医院
        List<YiYuanModel> yiYuanModels = cfHospitalApplyDao.selectYiYuanByCity(city);
        if (CollectionUtils.isEmpty(yiYuanModels)) {
            return NewResponseUtil.makeSuccess("no yiyuan");
        }
        for (YiYuanModel yiYuanModel : yiYuanModels) {
            yiYuanModel.setHospitalAlias(yiYuanModel.getHospitalAlias().replace(",", "|").replace("未知", ""));
        }
        List<YuanQuModel> yuanQuModels = cfHospitalApplyDao.selectYuanQuByCity(city);
        if (CollectionUtils.isEmpty(yuanQuModels)) {
            return NewResponseUtil.makeSuccess("not yuanqu");
        }
        for (YuanQuModel yuanQuModel : yuanQuModels) {
            yuanQuModel.setAreaAlias(yuanQuModel.getAreaAlias().replace(",", "|").replace("未知", ""));

        }
        //获取城市下待技术整理的院区
        log.info("yiyuan:{}", JSON.toJSONString(yiYuanModels));
        log.info("yuanqu:{}", JSON.toJSONString(yuanQuModels));
        if (!isSync) {
            return NewResponseUtil.makeSuccess(province.getName() + "," + city + ",check");
        }
        Response<String> response = null;
        for (YiYuanModel yiYuanModel : yiYuanModels) {
            response = addLogicHospital(yiYuanModel.getHospitalMainName(), province.getName(), city, yiYuanModel.getAreaCount(),
                    yiYuanModel.getSealIndependent(), yiYuanModel.getMaterialIndependent(), yiYuanModel.getHospitalAlias());
            log.info("addLogicHospital response:{}", JSON.toJSONString(response));
        }
        for (YuanQuModel yuanQuModel : yuanQuModels) {
            response = this.addAreaHospital(yuanQuModel.getHospitalMainName(), yuanQuModel.getAreaMainName(), city, province.getName(), yuanQuModel.getAreaAddress());
            log.info("addLogicHospital response:{}", JSON.toJSONString(response));
        }
        response = this.addLogicToAreaHospitalByCity(city);
        log.info("addLogicHospital response:{}", JSON.toJSONString(response));
        response = this.syncManualHospitalByCity(city);
        log.info("addLogicHospital response:{}", JSON.toJSONString(response));
        for (YuanQuModel yuanQuModel : yuanQuModels) {
            response = this.syncAreaHospitalAlias(city, yuanQuModel.getHospitalMainName(), yuanQuModel.getAreaMainName(), yuanQuModel.getAreaAlias());
            log.info("addLogicHospital response:{}", JSON.toJSONString(response));
        }
        return NewResponseUtil.makeSuccess(province.getName() + "," + city + ",sync");
    }
}
