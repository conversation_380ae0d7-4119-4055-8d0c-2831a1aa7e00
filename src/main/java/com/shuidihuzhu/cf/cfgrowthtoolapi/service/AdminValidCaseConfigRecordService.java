package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigRecordVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.AdminValidCaseConfigRecordParam;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdminValidCaseConfigRecordService {

    Response<Integer> save(AdminValidCaseConfigRecordParam param);

    Response<List<AdminValidCaseConfigRecordVO>> getRecordList(long configId);

}