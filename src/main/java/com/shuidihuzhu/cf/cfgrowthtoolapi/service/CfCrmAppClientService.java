package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmAppClientDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCrmAppClientService;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmAppClientDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: wanghui
 * @create: 2019/12/5 6:10 PM
 */
@Service
public class CfCrmAppClientService implements ICfCrmAppClientService {
    @Autowired
    private CfCrmAppClientDao cfCrmAppClientDao;
    @Override
    public int saveCfCrmAppClient(CfCrmAppClientDO cfCrmAppClientDO) {
        return cfCrmAppClientDao.saveCfCrmAppClient(cfCrmAppClientDO);
    }
}
