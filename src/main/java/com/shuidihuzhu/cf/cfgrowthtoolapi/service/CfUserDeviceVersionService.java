package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfUserDeviceVersionDO;

public interface CfUserDeviceVersionService {

    /**
     * 保存用户设备版本信息
     * @param vo
     * @return
     */
    int saveCfUserDeviceVersion(CfUserDeviceVersionDO vo);

    /**
     * 根据设备唯一ID  和系统平台查询  设备信息
     * @param deviceId
     * @param version
     * @return
     */
    CfUserDeviceVersionDO queryCfUserDeviceByDeviceAndVersion(String deviceId,String version);
}
