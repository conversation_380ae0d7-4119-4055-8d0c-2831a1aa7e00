package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IpepDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.impl.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.partner.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.team.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2023-03-21 11:36
 **/
@Slf4j
@Service
public class PepDataPushFactory {

    @Autowired
    private CaseBusinessDataPushService caseBusinessDataPushService;

    @Autowired
    private VisitDataPushService visitDataPushService;

    @Autowired
    private DonateTargetDataService donateTargetDataService;

    @Autowired
    private DonateCompleteDataService donateCompleteDataService;

    @Autowired
    private RecruitRandomizedDataPushService recruitRandomizedDataPushService;

    @Autowired
    private RecruitSubmitDataPushService recruitSubmitDataPushService;

    @Autowired
    private DonateManagerAvgCntService donateManagerAvgCntService;

    @Autowired
    private SeniorPerformanceDataPushService seniorPerformanceDataPushService;

    @Autowired
    private PartnerCaseDetailPushService partnerCaseDetailPushService;

    @Autowired
    private PartnerCityDetailPushService partnerCityDetailPushService;

    @Autowired
    private DelegateMemberPushService delegateMemberPushService;

    @Autowired
    private PerformanceFactorDataPushService performanceFactorDataPushService;

    @Autowired
    private PushPepMemberDataService pushDataService;

    @Autowired
    private PepLotObtainService pepLotObtainService;

    @Autowired
    private ManagerHcDataPushService managerHcDataPushService;

    @Autowired
    private DelegateManagerHcDataPushService delegateManagerHcDataPushService;

    @Autowired
    private TeamDonatePushService teamDonatePushService;

    @Autowired
    private TeamFirstCasePushService teamFirstCasePushService;

    @Autowired
    private IpepDelegate pepDelegate;

    //直营数据推送
    public void pushPepData() {
        //人员数据
        pushDataService.syncAllMember(pepLotObtainService.listKpiLotDO(PepPushEnum.direct_member));
        caseBusinessDataPushService.pushData();
        visitDataPushService.pushData();
        donateTargetDataService.pushData();
        donateCompleteDataService.pushData();
        donateManagerAvgCntService.pushData();
        recruitRandomizedDataPushService.pushData();
        recruitSubmitDataPushService.pushData();
        performanceFactorDataPushService.pushData();

        //高级渠道经理
        seniorPerformanceDataPushService.pushData();
        managerHcDataPushService.pushData();
        teamDonatePushService.pushData();
        teamFirstCasePushService.pushData();

        //最后推送分数相关数据
        pepDelegate.syncScoreDataByLot();
    }



    //代理渠道数据推送
    public void pushDelegateData() {
        partnerCaseDetailPushService.pushData();
        partnerCityDetailPushService.pushData();
        delegateManagerHcDataPushService.pushData();
        //推送人员
        delegateMemberPushService.syncDelegateMember(pepLotObtainService.getLotIds(PepPushEnum.partner_member));
    }


}
