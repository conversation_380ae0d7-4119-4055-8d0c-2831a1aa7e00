package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdCropOrgMappingDO;

import java.util.List;

/**
 * 主体departmentId映射(BdCropOrgMapping)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-28 11:33:18
 */
public interface BdCropOrgMappingService {

    BdCropOrgMappingDO queryById(long id);

    int insert(BdCropOrgMappingDO bdCropOrgMapping);

    BdCropOrgMappingDO queryByOrgId(String orgId, int cropId);

    List<BdCropOrgMappingDO> listCropId(int cropId);

    void deleteRelation(List<Long> ids);

    String maxDepartmentId(int cropId);

}
