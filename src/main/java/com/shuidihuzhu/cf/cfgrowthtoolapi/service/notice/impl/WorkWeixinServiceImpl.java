package com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IWorkWeiXinDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.notice.workwx.WorkWeiXinService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.SendQyWechatMsgService;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-05-15
 */
@Service
@RefreshScope
public class WorkWeixinServiceImpl implements WorkWeiXinService {

    @Resource
    private IWorkWeiXinDelegate delegate;

    @Value("${apollo.send.fumi.gw:false}")
    private boolean sendFumiToGw;

    @Autowired
    private SendQyWechatMsgService sendQyWechatMsgService;

    @Override
    public OpResult sendByUser(List<String> operatorNameList, String content) {
        if (!sendFumiToGw) {
            return OpResult.createSucResult();
        }
        return delegate.sendByUser(operatorNameList, content);
    }

    @Override
    public OpResult sendByVolunteers(List<CrowdfundingVolunteer> volunteers, String content) {
        if (!sendFumiToGw) {
            return OpResult.createSucResult();
        }
        //发送到企业微信不同主体下
        sendQyWechatMsgService.sendMsgToQyWechat(volunteers, content);
        List<String> misList = volunteers.stream()
                .filter(volunteer -> volunteer.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.ON_THE_JOB.getValue()
                        && StringUtils.isNotBlank(volunteer.getMis()))
                .map(CrowdfundingVolunteer::getMis)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(misList)) {
            return OpResult.createSucResult();
        }
        return delegate.sendByUser(misList, content);
    }

}
