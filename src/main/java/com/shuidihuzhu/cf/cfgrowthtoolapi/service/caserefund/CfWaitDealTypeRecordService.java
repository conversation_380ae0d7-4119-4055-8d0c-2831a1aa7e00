package com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.caserefund.CfWaitDealTypeRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.caserefund.CfWaitDealResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.caserefund.CfWaitTaskDealModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.caserefund.CfCaseRefundDetailParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/22 11:27 AM
 */
public interface CfWaitDealTypeRecordService {

    int insert(CfWaitDealTypeRecordDO cfWaitDealTypeRecordDO);

    void updateDealStatus(Long id, Integer dealStatus, Integer dealResult);

    CfWaitDealTypeRecordDO getWaitDealTypeById(Long id);

    List<CfWaitDealTypeRecordDO> getWaitDealTypeByParams(CfCaseRefundDetailParam cfCaseRefundDetailParam);

    Integer getWaitDealTypeByParamsCount(CfCaseRefundDetailParam cfCaseRefundDetailParam);

    CfWaitDealResultModel getWaitDealTypeCountByParams(CfCaseRefundDetailParam param);

    int getWaitDealCount(String uniqueCode);

    CfWaitTaskDealModel getWaitDealTaskByOrgIds(String startTime, String endTime, Integer dealResult, List<Long> orgIdList);

    List<CfWaitTaskDealModel> groupByUniqueCode(long orgId, Integer dealResult, String startTime, String endTime);

    CfWaitTaskDealModel getTaskModelByUniqueCode(String startTime, String endTime, String uniqueCode);

    CfWaitTaskDealModel getTaskModelByOrgIds(String startTime, String endTime, List<Long> allSubOrgIds);
}
