package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfPartnerOrgMemberSnapshotDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfBdCrmOrgModel;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */
public interface CfPartnerOrgMemberSnapshotService {


    /**
     * 批量插入组织数据
     * @param crmOrgModelList
     * @param cycleId
     */
    void batchInsertOrgSnapshot(List<CfBdCrmOrgModel> crmOrgModelList, Long cycleId);


    /**
     * 批量插入组织人员信息
     * @param orgMemberSnapshotList
     * @param cycleId
     */
    void batchInsertMemberSnapshot(List<CfPartnerOrgMemberSnapshotDo> orgMemberSnapshotList, Long cycleId);

    List<CfPartnerOrgMemberSnapshotDo> listByCycleId(int cycleId);

    int deleteByCycleId(long cycleId);

}
