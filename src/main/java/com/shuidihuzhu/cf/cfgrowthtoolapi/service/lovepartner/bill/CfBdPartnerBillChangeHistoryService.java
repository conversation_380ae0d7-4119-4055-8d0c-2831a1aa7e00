package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfBdPartnerBillChangeHistoryDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/2/20 11:14
 * 爱心伙伴账单修改增加相应记录和备注(cf_partner_opt_log)表服务接口
 */
public interface CfBdPartnerBillChangeHistoryService {

    int insert(CfBdPartnerBillChangeHistoryDO cfBdPartnerBillChangeHistoryDo);

    List<CfBdPartnerBillChangeHistoryDO> getCfBdPartnerBillChangeHistoryInfo(String partnerUniqueCode, int cycleId);
}
