package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMasterForGrowthtoolDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.dao.CfTouFangSignDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfTouFangSignInterface;
import com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Auther: zgq
 * @Date: 2019-05-10 16:14
 * @Description:
 */
@Slf4j
@Service
public class CfTouFangSignImpl implements CfTouFangSignInterface {

    @Autowired
    CfTouFangSignDao cfTouFangSignDao;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private ICfMasterForGrowthtoolDelegate cfMasterForGrowthtoolDelegate;

    @Override
    public int insertList(List<CfTouFangSign> cfTouFangSigns) {
        return cfMasterForGrowthtoolDelegate.insertCfTouFangSignList(cfTouFangSigns);
    }

    @Override
    public CfTouFangSign selectByCryptoMobile(String mobile) {
        if(StringUtils.isEmpty(ShuidiCipherUtils.encrypt(mobile))){
            return null;
        }

        CfTouFangSign cfTouFangSign = cfTouFangSignDao.selectByCryptoMobile(ShuidiCipherUtils.encrypt(mobile));
        if(Objects.nonNull(cfTouFangSign)){
            cfTouFangSign.setMobile(shuidiCipher.decrypt(cfTouFangSign.getCryptoMobile()));
        }

        return cfTouFangSign;
    }
}
