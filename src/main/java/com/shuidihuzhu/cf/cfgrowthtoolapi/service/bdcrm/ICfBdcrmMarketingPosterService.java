package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdcrmMarketingPosterDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.MarketingPosterSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.MarketingPosterVO;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/1/8 下午5:56
 */
public interface ICfBdcrmMarketingPosterService {



    int insertSelective(CfBdcrmMarketingPosterDO record);

    CfBdcrmMarketingPosterDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CfBdcrmMarketingPosterDO record);

    long getListCountForSea(MarketingPosterSearchModel marketingPosterSearchModel);

    List<CfBdcrmMarketingPosterDO> getListForSea(MarketingPosterSearchModel marketingPosterSearchModel);

    long getUpCountForSea(MarketingPosterSearchModel marketingPosterSearchModel);

    void upOrDown(Long id, int publishStatus);

    void updateIsDelete(Long id, int isDelete);

    long getListCountForXiaoJingYu(MarketingPosterSearchModel marketingPosterSearchModel, CrowdfundingVolunteer cfVolunteer);

    List<MarketingPosterVO> getListForXiaoJingYu(MarketingPosterSearchModel marketingPosterSearchModel, CrowdfundingVolunteer cfVolunteer);

}
