package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.CrowdFundingFeignDelegateImpl;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.HasOfflineBreakWaveTaskEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OfflineBreakWaveTaskStatusEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OfflineBreakWaveTaskTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfOfflineBreakWaveTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfOfflineBreakWaveTaskService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.dao.CfOfflineBreakWaveTaskDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 任务服务实现
 */
@Service
@Slf4j
public class CfOfflineBreakWaveTaskServiceImpl implements ICfOfflineBreakWaveTaskService {

    @Autowired
    private CfOfflineBreakWaveTaskDao cfOfflineBreakWaveTaskDao;

    @Autowired
    private CrowdFundingFeignDelegateImpl crowdFundingFeignDelegateImpl;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;



    @Override
    public int insertTask(CfOfflineBreakWaveTaskDO record, String infoUuid) {
        if (record == null) {
            log.warn("插入任务参数为空");
            return 0;
        }
        CfOfflineBreakWaveTaskDO taskByCaseIdAndType = cfOfflineBreakWaveTaskDao.getTaskByCaseIdAndType(
                record.getCaseId(), record.getTaskType(), record.getDayKey(),
                OfflineBreakWaveTaskStatusEnum.INIT.getCode());
        if (taskByCaseIdAndType != null) {
            return 0;
        }
        int result = cfOfflineBreakWaveTaskDao.insertSelective(record);
        // 更新任务中有代办标识
        if (result > 0) {
            cfBdCaseInfoService.updateHasOfflineBreakWaveTask(infoUuid,
                    HasOfflineBreakWaveTaskEnum.HAS_UNFINISHED_TASK.getCode());
        }
        return result;
    }

    @Override
    public List<CfOfflineBreakWaveTaskDO> queryTaskByCaseIdAndDayKey(Integer caseId, String dayKey) {
        if (caseId == null || dayKey == null) {
            log.warn("查询任务参数不完整, caseId={}, dayKey={}", caseId, dayKey);
            return Collections.emptyList();
        }
        return cfOfflineBreakWaveTaskDao.selectByCaseIdAndDayKey(caseId, dayKey);
    }

    @Override
    public int batchUpdateTaskStatus(List<Long> idList, Integer taskStatus, String infoUuid, String reason) {
        if (CollectionUtils.isEmpty(idList) || taskStatus == null) {
            log.warn("批量更新任务状态参数不完整, idList={}, taskStatus={}", idList, taskStatus);
            return 0;
        }

        int result = cfOfflineBreakWaveTaskDao.batchUpdateStatus(idList, taskStatus, reason);
        updateHasOfflineBreakWaveTask(infoUuid);
        return result;
    }

    private void updateHasOfflineBreakWaveTask(String infoUuid) {
        try {
            CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegateImpl.getCrowdfundingInfoByInfouuid(infoUuid);
            if (crowdfundingInfo == null) {
                return;
            }
            // 查看案例是否还有未完成任务,通过caseId和状态查询，不要dayKey
            List<CfOfflineBreakWaveTaskDO> unfinishedTasks = cfOfflineBreakWaveTaskDao.selectByCaseIdAndStatus(
                    crowdfundingInfo.getId(), OfflineBreakWaveTaskStatusEnum.INIT.getCode());
            // 使用枚举代替魔法值
            int hasOfflineBreakWaveTask = CollectionUtils.isNotEmpty(unfinishedTasks)
                    ? HasOfflineBreakWaveTaskEnum.HAS_UNFINISHED_TASK.getCode()
                    : HasOfflineBreakWaveTaskEnum.NO_UNFINISHED_TASK.getCode();
            if (CollectionUtils.isEmpty(unfinishedTasks)) {
                // 更新状态
                cfBdCaseInfoService.updateHasOfflineBreakWaveTask(infoUuid, hasOfflineBreakWaveTask);
            }
        } catch (Exception e) {
            log.error("updateHasOfflineBreakWaveTask err", e);
        }
    }

    @Override
    public int updateTaskStatus(Integer caseId, Integer taskStatus, String infoUuid, Integer taskType) {
        if (caseId == null || taskType == null) {
            return 0;
        }
        int i = cfOfflineBreakWaveTaskDao.updateTaskStatus(caseId, taskStatus, taskType);
        updateHasOfflineBreakWaveTask(infoUuid);
        return i;
    }

    @Override
    public Integer getTaskCaseCount(String uniqueCode, Integer taskStatus) {
        if (StringUtils.isBlank(uniqueCode) || taskStatus == null) {
            log.warn("获取顾问待办任务数量参数为空, uniqueCode={}", uniqueCode);
            return 0;
        }
        // 只查6天前的
        Date limitTime = DateUtil.getLastMsOfThisDay(DateUtil.addDay(new Date(), -6));
        return cfBdCaseInfoService.countTaskCaseCount(uniqueCode, HasOfflineBreakWaveTaskEnum.HAS_UNFINISHED_TASK.getCode(), limitTime);
    }

    @Override
    public List<CfOfflineBreakWaveTaskDO> selectByStatusAndCaseIds(Integer taskStatus, List<Integer> caseIds) {
        if (caseIds == null || caseIds.isEmpty()) {
            return Collections.emptyList();
        }
        return cfOfflineBreakWaveTaskDao.selectByStatusAndCaseIds(taskStatus, caseIds);
    }

    @Override
    public Boolean hasNoNeedCompleteTask(Integer caseId) {
        if (caseId == null) {
            return false;
        }
        Integer taskCount = cfOfflineBreakWaveTaskDao.countTaskByCaseIdAndStatus(caseId,
                OfflineBreakWaveTaskStatusEnum.NO_NEED.getCode());
        if (taskCount == null || taskCount == 0) {
            return false;
        }
        return true;
    }

    /**
     * 结束
     *
     * @param caseId
     * @return
     */
    @Override
    public int endTask(Integer caseId, Integer taskStatus) {
        if (caseId == null || taskStatus == null) {
            return 0;
        }
        CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegateImpl.getCaseInfoById(caseId);
        if (crowdfundingInfo == null) {
            return 0;
        }
        List<Integer> taskTypeList = new ArrayList<>();
        if (OfflineBreakWaveTaskStatusEnum.CASE_END.getCode() == taskStatus) {
            taskTypeList.add(OfflineBreakWaveTaskTypeEnum.NO_FORWARD_VERIFIER.getCode());
            taskTypeList.add(OfflineBreakWaveTaskTypeEnum.NO_FORWARD_REMINDER.getCode());
            taskTypeList.add(OfflineBreakWaveTaskTypeEnum.TOP_FRIENDS_REMINDER.getCode());
        } else if (OfflineBreakWaveTaskStatusEnum.EXPIRED.getCode() == taskStatus) {
            //证实类型的任务不过期
            taskTypeList.add(OfflineBreakWaveTaskTypeEnum.NO_FORWARD_REMINDER.getCode());
            taskTypeList.add(OfflineBreakWaveTaskTypeEnum.TOP_FRIENDS_REMINDER.getCode());
        }
        if (CollectionUtils.isEmpty(taskTypeList)) {
            return 0;
        }
        int num = cfOfflineBreakWaveTaskDao.updateTaskStatusByCaseIdAndTypes(caseId, taskStatus, taskTypeList);
        log.info("endTask caseId={}, taskStatus={},num:{}", caseId, taskStatus, num);
        updateHasOfflineBreakWaveTask(crowdfundingInfo.getInfoId());

        return 0;
    }

    @Override
    public Long getMaxIdByStatusAndTypesAndDayKey(Integer taskStatus, List<Integer> taskTypes, String dayKey) {
        if (taskStatus == null || CollectionUtils.isEmpty(taskTypes) || StringUtils.isBlank(dayKey)) {
            log.warn("获取任务最大ID参数不完整, taskStatus={}, taskTypes={}, dayKey={}", taskStatus, taskTypes, dayKey);
            return null;
        }
        return cfOfflineBreakWaveTaskDao.getMaxIdByStatusAndTypesAndDayKey(taskStatus, taskTypes, dayKey);
    }

    @Override
    public Long getMinIdByStatusAndTypesAndDayKey(Integer taskStatus, List<Integer> taskTypes, String dayKey) {
        if (taskStatus == null || CollectionUtils.isEmpty(taskTypes) || StringUtils.isBlank(dayKey)) {
            log.warn("获取任务最小ID参数不完整, taskStatus={}, taskTypes={}, dayKey={}", taskStatus, taskTypes, dayKey);
            return null;
        }
        return cfOfflineBreakWaveTaskDao.getMinIdByStatusAndTypesAndDayKey(taskStatus, taskTypes, dayKey);
    }

    @Override
    public List<CfOfflineBreakWaveTaskDO> selectByStatusAndTypesAndDayKeyAndIdRange(
            Integer taskStatus,
            List<Integer> taskTypes,
            String dayKey,
            Long minId,
            Long maxId,
            Integer limit) {
        if (taskStatus == null || CollectionUtils.isEmpty(taskTypes)
                || StringUtils.isBlank(dayKey) || minId == null || maxId == null || limit == null) {
            log.warn("查询任务范围参数不完整, taskStatus={}, taskTypes={}, dayKey={}, minId={}, maxId={}, limit={}",
                    taskStatus, taskTypes, dayKey, minId, maxId, limit);
            return Collections.emptyList();
        }
        return cfOfflineBreakWaveTaskDao.selectByStatusAndTypesAndDayKeyAndIdRange(
                taskStatus, taskTypes, dayKey, minId, maxId, limit);
    }
}