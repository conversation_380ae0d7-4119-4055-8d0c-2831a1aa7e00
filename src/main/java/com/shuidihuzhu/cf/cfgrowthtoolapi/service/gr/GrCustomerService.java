package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr;


import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrCustomerDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.GrCustomerPageParam;

import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * gr客户信息(GrCustomer)表服务接口
 *
 * <AUTHOR>
 * @since 2021-04-09 15:32:49
 */
public interface GrCustomerService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    GrCustomerDO queryById(long id);

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    List<GrCustomerDO> queryAllByLimit(int offset, int limit);

    /**
     * 新增数据
     *
     * @param grCustomer 实例对象
     * @return 实例对象
     */
    GrCustomerDO insert(GrCustomerDO grCustomer);

    /**
     * 修改数据
     *
     * @param grCustomer 实例对象
     */
    void update(GrCustomerDO grCustomer);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);

    List<GrCustomerDO> queryAll(GrCustomerDO grCustomer);

    List<GrCustomerDO> queryByName(String customerName, String city);


    int countCustomer(GrCustomerPageParam customerPageParam);


    List<GrCustomerDO> pageCustomer(GrCustomerPageParam customerPageParam);


    List<GrCustomerDO> listAllImportCustomer();

    void updateStatusChangeTime(int id, Date date);

    void updateWhenAddRecord(int id, Date date, String purpose, String connectResult);

    void updateAddFeedbackTime(int id, Date date, Integer modifyConnectStatus);

    void updateBySea(int id, Integer priorityLevel, String uniqueCode, Integer connectStatus, String mis);

    GrCustomerDO getCustomerByCityAndHospital(String cityName, String hospital);

    //重新导入恢复原来导入的数据
    void updateByDelete(GrCustomerDO grCustomer);

    List<GrCustomerDO> getCustomerByIds(Set<Integer> customerIds);
}