package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrCustomerConnectorDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.GrCustomerConnectorService;
import com.shuidihuzhu.cf.dao.gr.GrCustomerConnectorDao;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * gr客户联系人(GrCustomerConnector)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-09 15:32:56
 */
@Service
public class GrCustomerConnectorServiceImpl implements GrCustomerConnectorService {

    @Resource
    private GrCustomerConnectorDao grCustomerConnectorDao;

    @Autowired
    private ShuidiCipher shuidiCipher;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public GrCustomerConnectorDO queryById(long id) {
        return grCustomerConnectorDao.queryById(id);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    @Override
    public List<GrCustomerConnectorDO> queryAllByLimit(int offset, int limit) {
        return grCustomerConnectorDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param grCustomerConnector 实例对象
     * @return 实例对象
     */
    @Override
    public GrCustomerConnectorDO insert(GrCustomerConnectorDO grCustomerConnector) {
        grCustomerConnectorDao.insert(grCustomerConnector);
        return grCustomerConnector;
    }

    /**
     * 修改数据
     *
     * @param grCustomerConnector 实例对象
     * @return 实例对象
     */
    @Override
    public void update(GrCustomerConnectorDO grCustomerConnector) {
        grCustomerConnectorDao.update(grCustomerConnector);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return grCustomerConnectorDao.deleteById(id) > 0;
    }

    @Override
    public List<GrCustomerConnectorDO> listByCustomerIds(List<Integer> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Lists.newArrayList();
        }
        List<GrCustomerConnectorDO> connectorDOList = grCustomerConnectorDao.listByCustomerIds(customerIds);
        connectorDOList.forEach(item -> {
            if (StringUtils.isNotBlank(item.getEncryptPhone())) {
                item.setOriginPhone(shuidiCipher.decrypt(item.getEncryptPhone()));
                item.setEncryptPhone("");
            }
        });
        return connectorDOList;
    }

    @Override
    public Map<Integer, List<GrCustomerConnectorDO>> listGroupByCustomerId(List<Integer> customerIds) {
        List<GrCustomerConnectorDO> grCustomerConnectorDOS = listByCustomerIds(customerIds);
        return grCustomerConnectorDOS.stream().collect(Collectors.groupingBy(GrCustomerConnectorDO::getCustomerId));
    }
}