package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.BdCorpFriendDO;
import com.shuidihuzhu.cf.dao.qywx.BdCorpFriendDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.BdCorpFriendService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * bd添加微信好友信息(BdCorpFriend)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-15 18:10:18
 */
@Service("bdCorpFriendService")
public class BdCorpFriendServiceImpl implements BdCorpFriendService {
   
    @Resource
    private BdCorpFriendDao bdCorpFriendDao;

    @Override
    public BdCorpFriendDO queryById(long id) {
        return bdCorpFriendDao.queryById(id);
    }
    

    @Override
    public int insert(BdCorpFriendDO bdCorpFriendDO) {
        return bdCorpFriendDao.insert(bdCorpFriendDO);
    }

    @Override
    public void batchInsert(List<BdCorpFriendDO> bdCorpFriendDOS) {
        if (CollectionUtils.isEmpty(bdCorpFriendDOS)) {
            return;
        }
        bdCorpFriendDao.batchInsert(bdCorpFriendDOS);
    }

    @Override
    public int update(BdCorpFriendDO bdCorpFriendDO) {
        return bdCorpFriendDao.update(bdCorpFriendDO);
    }

    @Override
    public List<BdCorpFriendDO> listFriendByUserId(String corpId, String userId) {
        return bdCorpFriendDao.listFriendByUserId(corpId, userId);
    }


    @Override
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        bdCorpFriendDao.deleteByIds(ids);
    }
}
