package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdDelegateCaseTagRecordDO;

import java.util.List;

/**
 * 代理商发起案例标签(BdDelegateCaseTagRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-13 10:54:08
 */
public interface BdDelegateCaseTagRecordService {

    BdDelegateCaseTagRecordDO queryById(long id);

    int insert(BdDelegateCaseTagRecordDO bdDelegateCaseTagRecordDO);

    int updateByApproveInfo(long approveId, int approveStatus, String rejectReason, String operator);

    List<BdDelegateCaseTagRecordDO> listByCaseIdAndSource(int caseId, int applySource);

    List<BdDelegateCaseTagRecordDO> listByCaseId(int caseId);

    List<BdDelegateCaseTagRecordDO> listByCaseIds(List<Integer> caseIds);


}
