package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.client.material.model.authenticity.enumModel.*;
import com.shuidihuzhu.cf.dao.bdcrm.BdCrmAiAssistDao;
import com.shuidihuzhu.client.cf.admin.client.CfAdminAiGenerateFeignClient;
import com.shuidihuzhu.client.cf.admin.service.ChatService;
import com.shuidihuzhu.client.model.ChatChunk;
import com.shuidihuzhu.client.model.ChatParam;
import com.shuidihuzhu.client.model.ChatStreamResult;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2025/5/7 14:49
 */
@Slf4j
@Service
public class BdCrmAiAssistServiceImpl implements BdCrmAiAssistService {

    @Resource
    private ChatService chatService;

    @Resource
    private BdCrmAiAssistDao bdCrmAiAssistDao;

    @Resource
    private CfAdminAiGenerateFeignClient cfAdminAiGenerateFeignClient;

    @Override
    public Flux<ChatChunk<ChatStreamResult>> streamGenerateContentByAi(BdCrmAiGenerateParam bdCrmAiGenerateParam, String uniqueCode) {

        // 根据已有信息拼装提示词
        ChatParam chatParam = new ChatParam();
        chatParam.setPrompt(constructGeneratePrompt(bdCrmAiGenerateParam));
        chatParam.setModelNum(bdCrmAiGenerateParam.getModelType());

        // 保存请求
        BdCrmAiChatRecord userAiChatRecord = BdCrmAiChatRecord.builder()
                .role("user")
                .prompt(chatParam.getPrompt())
                .modelType(chatParam.getModelNum())
                .content(bdCrmAiGenerateParam.getBdQuestion())
                .uniqueCode(uniqueCode)
                .build();
        bdCrmAiAssistDao.insert(userAiChatRecord);

        // 存储大模型回复的内容
        StringBuilder replyContentFull = new StringBuilder();

        return chatService.stream(chatParam).mapNotNull(
                chatResponse -> {

                    ChatStreamResult chatStreamResult = chatResponse.getData();
                    if (Objects.nonNull(chatStreamResult) && !chatResponse.isEnd()) {
                        replyContentFull.append(chatStreamResult.getReplyContent());
                    }

                    // 如果流式结束，保存结果到聊天记录
                    if (chatResponse.isEnd()) {
                        BdCrmAiChatRecord assistantAiChatRecord = BdCrmAiChatRecord.builder()
                                .role("assistant")
                                .prompt("")
                                .modelType(chatParam.getModelNum())
                                .content(replyContentFull.toString())
                                .uniqueCode(uniqueCode)
                                .build();
                        bdCrmAiAssistDao.insert(assistantAiChatRecord);
                    }

                    return chatResponse;
                }
        );
    }

    @Override
    public Flux<ChatChunk<ChatStreamResult>> streamChangeContentByAi(BdCrmAiChangeParam bdCrmAiChangeParam, String uniqueCode) {

        // 根据已有信息拼装提示词
        ChatParam chatParam = new ChatParam();
        chatParam.setPrompt(constructChangePrompt(bdCrmAiChangeParam));
        chatParam.setModelNum(bdCrmAiChangeParam.getModelType());

        // 保存请求
        BdCrmAiChatRecord userAiChatRecord = BdCrmAiChatRecord.builder()
                .role("user")
                .prompt(chatParam.getPrompt())
                .modelType(chatParam.getModelNum())
                .content(bdCrmAiChangeParam.getBdQuestion())
                .uniqueCode(uniqueCode)
                .build();
        bdCrmAiAssistDao.insert(userAiChatRecord);

        // 存储大模型回复的内容
        StringBuilder replyContentFull = new StringBuilder();

        return chatService.stream(chatParam).mapNotNull(
                chatResponse -> {

                    ChatStreamResult chatStreamResult = chatResponse.getData();
                    if (Objects.nonNull(chatStreamResult) && !chatResponse.isEnd()) {
                        replyContentFull.append(chatStreamResult.getReplyContent());
                    }

                    // 如果流式结束，保存结果到聊天记录
                    if (chatResponse.isEnd()) {
                        BdCrmAiChatRecord assistantAiChatRecord = BdCrmAiChatRecord.builder()
                                .role("assistant")
                                .prompt("")
                                .modelType(chatParam.getModelNum())
                                .content(replyContentFull.toString())
                                .uniqueCode(uniqueCode)
                                .build();
                        bdCrmAiAssistDao.insert(assistantAiChatRecord);
                    }

                    return chatResponse;
                }
        );
    }

    @Override
    public List<ChatRecord> getAiChatHistory(Integer modelType, String uniqueCode) {

        if (Objects.isNull(modelType) || StringUtils.isEmpty(uniqueCode)) {
            return Lists.newArrayList();
        }

        // 获取聊天记录
        List<BdCrmAiChatRecord> bdCrmAiChatRecords = bdCrmAiAssistDao.selectByModelTypeAndUniqueCode(modelType, uniqueCode);
        if (CollectionUtils.isEmpty(bdCrmAiChatRecords)) {
            return Lists.newArrayList();
        }

        // 转换
        return bdCrmAiChatRecords.stream()
                .map(record -> {
                    return ChatRecord.builder()
                            .role(record.getRole())
                            .content(record.getContent())
                            .build();
                })
                .collect(Collectors.collectingAndThen(
                        Collectors.toList(),
                        list -> {
                            Collections.reverse(list);
                            return list;
                        }
                ));

    }

    private String constructChangePrompt(BdCrmAiChangeParam bdCrmAiChangeParam) {
        return new StringBuilder()
                .append("## 请根据以下要求，重新对筹款文章进行调整:\n")
                .append(bdCrmAiChangeParam.getChangeGuideline()).append("\n")
                .append("## 原始文章:\n").append(bdCrmAiChangeParam.getOriginalMaterial())
                .toString();
    }

    private String constructGeneratePrompt(BdCrmAiGenerateParam bdCrmAiGenerateParam) {
        // 1. 查询当前提示词
        String originalPrompt = queryPromptTemplate(bdCrmAiGenerateParam);
        if (StringUtils.isEmpty(originalPrompt)) {
            return "";
        }

        // 2. 替换主参数中的变量
        originalPrompt = replaceFieldVariables(originalPrompt, bdCrmAiGenerateParam);

        // 3. 替换扩展字段中的变量
        AiGenerateExtField extField = bdCrmAiGenerateParam.getAiGenerateExtField();
        originalPrompt = replaceExtFieldVariables(originalPrompt, extField);

        return originalPrompt;
    }

    /**
     * 查询提示词模板
     */
    private String queryPromptTemplate(BdCrmAiGenerateParam param) {
        Response<String> response = cfAdminAiGenerateFeignClient.queryPromptConfigByAdmin(
                param.getGenerateType(),
                param.getModelType(),
                param.getBizType());

        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse("");
    }

    /**
     * 替换基本参数中的变量
     */
    private String replaceFieldVariables(String prompt, Object object) {
        if (prompt == null || object == null) {
            return prompt;
        }

        try {
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.getType() == String.class) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    String fieldValue = (String) field.get(object);

                    if (fieldValue != null && !fieldValue.isEmpty()) {
                        String placeholder = "${" + fieldName + "}";
                        prompt = prompt.replace(placeholder, fieldValue);
                    } else {
                        String placeholder = "${" + fieldName + "}";
                        prompt = prompt.replace(placeholder, "未提供");
                    }
                }
            }
        } catch (IllegalAccessException e) {
            log.error("Failed to replace variables in {}", object.getClass().getSimpleName(), e);
        }

        return prompt;
    }

    /**
     * 替换扩展字段中的变量，包括特殊处理逻辑
     */
    private String replaceExtFieldVariables(String prompt, AiGenerateExtField extField) {
        if (prompt == null) {

            return prompt;
        }

        if (extField == null) {
            // 查找并删除【##患者信息】及其后面的内容
            int patientInfoIndex = prompt.indexOf("##患者信息");
            if (patientInfoIndex != -1) {
                return prompt.substring(0, patientInfoIndex).trim();
            }
            return prompt;
        }

        try {
            Field[] fields = AiGenerateExtField.class.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                String fieldValue = String.valueOf(field.get(extField));
                String placeholder = "${" + fieldName + "}";

                // 字段值为空时的处理
                if (fieldValue == null || fieldValue.isEmpty()) {
                    prompt = prompt.replace(placeholder, "未提供");
                    continue;
                }

                // 特殊字段处理
                switch (fieldName) {
                    case "selfCryptoIdcard":
                    case "patientIdCard":
                        // 解析身份证号码获取年龄
                        int age = GrowthtoolUtil.idNOToAge(fieldValue);
                        if (age != 0) {
                            prompt = prompt.replace("${" + fieldName + "Age}", String.valueOf(age));
                        }
                        prompt = prompt.replace(placeholder, fieldValue);
                        break;

                    case "raisePatientRelation":
                        PreposeMaterialModel.RaiserPatientRelation relType = PreposeMaterialModel.RaiserPatientRelation.valueOfCode(Integer.valueOf(fieldValue));
                        prompt = prompt.replace(placeholder,
                                Objects.nonNull(relType) ? relType.getDesc() : "未提供");
                        break;

                    case "patientIdentity":
                        PreposeMaterialModel.PatientIdentity identity =
                                PreposeMaterialModel.PatientIdentity.valueOfCode(Integer.valueOf(fieldValue));
                        prompt = prompt.replace(placeholder,
                                Objects.nonNull(identity) ? identity.getDesc() : "未提供");
                        break;

                    case "patientMaritalStatus":
                        PatientMarriedEnum maritalStatus =
                                PatientMarriedEnum.descOfCode(Integer.valueOf(fieldValue));
                        prompt = prompt.replace(placeholder,
                                Objects.nonNull(maritalStatus) ? maritalStatus.getDesc() : "未提供");
                        break;

                    case "accidentType":
                        PreposeMaterialModel.AccidentType accidentType =
                                PreposeMaterialModel.AccidentType.valueOfCode(Integer.valueOf(fieldValue));
                        prompt = prompt.replace(placeholder,
                                Objects.nonNull(accidentType) ? accidentType.getDesc() : "未提供");
                        break;

                    case "targetAmount":
                        String targetAmountStr = fieldValue + "元";
                        prompt = prompt.replace(placeholder, targetAmountStr);
                        break;

                    case "selfHouseNum":
                        PreposeMaterialModel.HouseNumEnum houseNumEnum =
                                PreposeMaterialModel.HouseNumEnum.valueOfCode(Integer.valueOf(fieldValue));
                        prompt = prompt.replace(placeholder,
                                Objects.nonNull(houseNumEnum) ? houseNumEnum.getDesc() : "未提供");
                        break;

                    case "selfHouseAmountArea":
                        SelfHouseRangeEnum selfHouseRangeEnum =
                                SelfHouseRangeEnum.valueOfCode(Integer.valueOf(fieldValue));

                        Field selfHouseValueField = AiGenerateExtField.class.getDeclaredField("selfHouseValue");
                        selfHouseValueField.setAccessible(true);
                        Integer selfHouseValue = (Integer) selfHouseValueField.get(extField);

                        if (Objects.nonNull(selfHouseValue)) {
                            String selfHouseValueStr = selfHouseValue + "元";
                            prompt = prompt.replace(placeholder, selfHouseValueStr);
                        } else {
                            prompt = prompt.replace(placeholder,
                                    Objects.nonNull(selfHouseRangeEnum) ? selfHouseRangeEnum.getDesc() : "未提供");
                        }
                        break;

                    case "selfHouseSellingCount":
                        String selfHouseSellingCountStr = fieldValue + "套";
                        prompt = prompt.replace(placeholder, selfHouseSellingCountStr);
                        break;

                    case "selfHouseSellingAmountArea":
                        PreposeMaterialModel.HouseSellingAmountAreaEnum selfHouseSellingAmountAreaEnum =
                                PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(Integer.valueOf(fieldValue));

                        Field selfHouseSellingAmountField = AiGenerateExtField.class.getDeclaredField("selfHouseSellingAmount");
                        selfHouseSellingAmountField.setAccessible(true);
                        Integer selfHouseSellingAmount = (Integer) selfHouseSellingAmountField.get(extField);

                        if (Objects.nonNull(selfHouseSellingAmount)) {
                            String selfHouseValueStr = selfHouseSellingAmount + "元";
                            prompt = prompt.replace(placeholder, selfHouseValueStr);
                        } else {
                            prompt = prompt.replace(placeholder,
                                    Objects.nonNull(selfHouseSellingAmountAreaEnum) ? selfHouseSellingAmountAreaEnum.getDesc() : "未提供");
                        }
                        break;

                    case "houseNum":
                        PreposeMaterialModel.HouseNumEnum otherHouseNumEnum =
                                PreposeMaterialModel.HouseNumEnum.valueOfCode(Integer.valueOf(fieldValue));
                        prompt = prompt.replace(placeholder,
                                Objects.nonNull(otherHouseNumEnum) ? otherHouseNumEnum.getDesc() : "未提供");
                        break;

                    case "houseNetWorthArea":
                        CfPropertyInsuranceInfoModel.HouseValueRange houseValueRange =
                                CfPropertyInsuranceInfoModel.HouseValueRange.valueOfCode(Integer.valueOf(fieldValue));

                        Field houseNetWorthValueField = AiGenerateExtField.class.getDeclaredField("houseNetWorthValue");
                        houseNetWorthValueField.setAccessible(true);
                        Integer houseNetWorthValue = (Integer) houseNetWorthValueField.get(extField);

                        if (Objects.nonNull(houseNetWorthValue)) {
                            String houseNetWorthValueStr = houseNetWorthValue + "元";
                            prompt = prompt.replace(placeholder, houseNetWorthValueStr);
                        } else {
                            prompt = prompt.replace(placeholder,
                                    Objects.nonNull(houseValueRange) ? houseValueRange.getDesc() : "未提供");
                        }
                        break;

                    case "houseSellingCount":
                        String houseSellingCountStr = fieldValue + "套";
                        prompt = prompt.replace(placeholder, houseSellingCountStr);
                        break;

                    case "houseSellingAmountArea":
                        PreposeMaterialModel.HouseSellingAmountAreaEnum houseSellingAmountAreaEnum =
                                PreposeMaterialModel.HouseSellingAmountAreaEnum.valueOfCode(Integer.valueOf(fieldValue));

                        Field houseSellingAmountField = AiGenerateExtField.class.getDeclaredField("houseSellingAmount");
                        houseSellingAmountField.setAccessible(true);
                        Integer houseSellingAmount = (Integer) houseSellingAmountField.get(extField);

                        if (Objects.nonNull(houseSellingAmount)) {
                            String houseValueStr = houseSellingAmount + "元";
                            prompt = prompt.replace(placeholder, houseValueStr);
                        } else {
                            prompt = prompt.replace(placeholder,
                                    Objects.nonNull(houseSellingAmountAreaEnum) ? houseSellingAmountAreaEnum.getDesc() : "未提供");
                        }
                        break;

                    case "carNum":
                        PreposeMaterialModel.CarNumEnum carNumEnum =
                                PreposeMaterialModel.CarNumEnum.valueOfCode(Integer.valueOf(fieldValue));
                        prompt = prompt.replace(placeholder,
                                Objects.nonNull(carNumEnum) ? carNumEnum.getDesc() : "未提供");
                        break;

                    case "carAmountArea":
                        CarRangeEnum carRangeEnum =
                                CarRangeEnum.valueOfCode(Integer.valueOf(fieldValue));

                        Field carValueField = AiGenerateExtField.class.getDeclaredField("carValue");
                        carValueField.setAccessible(true);
                        Integer carValue = (Integer) carValueField.get(extField);

                        if (Objects.nonNull(carValue)) {
                            String carValueStr = carValue + "元";
                            prompt = prompt.replace(placeholder, carValueStr);
                        } else {
                            prompt = prompt.replace(placeholder,
                                    Objects.nonNull(carRangeEnum) ? carRangeEnum.getDesc() : "未提供");
                        }
                        break;

                    case "carSellingCount":
                        String carSellingCountStr = fieldValue + "套";
                        prompt = prompt.replace(placeholder, carSellingCountStr);
                        break;

                    case "carSellingAmountArea":
                        PreposeMaterialModel.CarSellingAmountAreaEnum carSellingAmountAreaEnum =
                                PreposeMaterialModel.CarSellingAmountAreaEnum.valueOfCode(Integer.valueOf(fieldValue));

                        Field carSellingAmountField = AiGenerateExtField.class.getDeclaredField("carSellingAmount");
                        carSellingAmountField.setAccessible(true);
                        Integer carSellingAmount = (Integer) carSellingAmountField.get(extField);

                        if (Objects.nonNull(carSellingAmount)) {
                            String carSellingAmountStr = carSellingAmount + "元";
                            prompt = prompt.replace(placeholder, carSellingAmountStr);
                        } else {
                            prompt = prompt.replace(placeholder,
                                    Objects.nonNull(carSellingAmountAreaEnum) ? carSellingAmountAreaEnum.getDesc() : "未提供");
                        }
                        break;

                    case "homeIncomeArea":
                        PreposeMaterialModel.HomeIncomeAreaEnum homeIncomeAreaEnum =
                                PreposeMaterialModel.HomeIncomeAreaEnum.valueOfCode(Integer.valueOf(fieldValue));

                        Field homeIncomeField = AiGenerateExtField.class.getDeclaredField("homeIncome");
                        homeIncomeField.setAccessible(true);
                        Integer homeIncome = (Integer) homeIncomeField.get(extField);

                        if (Objects.nonNull(homeIncome)) {
                            String homeIncomeStr = homeIncome + "元";
                            prompt = prompt.replace(placeholder, homeIncomeStr);
                        } else {
                            prompt = prompt.replace(placeholder,
                                    Objects.nonNull(homeIncomeAreaEnum) ? homeIncomeAreaEnum.getDesc() : "未提供");
                        }
                        break;

                    case "financialAssetsAmountArea":
                        PreposeMaterialModel.FinancialAssetsAmountAreaEnum financialAssetsAmountAreaEnum =
                                PreposeMaterialModel.FinancialAssetsAmountAreaEnum.valueOfCode(Integer.valueOf(fieldValue));

                        Field financialAssetsAmountField = AiGenerateExtField.class.getDeclaredField("financialAssetsAmount");
                        financialAssetsAmountField.setAccessible(true);
                        Integer financialAssetsAmount = (Integer) financialAssetsAmountField.get(extField);

                        if (Objects.nonNull(financialAssetsAmount)) {
                            String financialAssetsAmountStr = financialAssetsAmount + "元";
                            prompt = prompt.replace(placeholder, financialAssetsAmountStr);
                        } else {
                            prompt = prompt.replace(placeholder,
                                    Objects.nonNull(financialAssetsAmountAreaEnum) ? financialAssetsAmountAreaEnum.getDesc() : "未提供");
                        }
                        break;

                    case "homeOwningAmountArea":
                        HouseDebtEnum houseDebtEnum =
                                HouseDebtEnum.valueOfCode(Integer.valueOf(fieldValue));

                        Field homeOwningAmountField = AiGenerateExtField.class.getDeclaredField("homeOwningAmount");
                        homeOwningAmountField.setAccessible(true);
                        Integer homeOwningAmount = (Integer) homeOwningAmountField.get(extField);

                        if (Objects.nonNull(homeOwningAmount)) {
                            String homeOwningAmountStr = homeOwningAmount + "元";
                            prompt = prompt.replace(placeholder, homeOwningAmountStr);
                        } else {
                            prompt = prompt.replace(placeholder,
                                    Objects.nonNull(houseDebtEnum) ? houseDebtEnum.getDesc() : "未提供");
                        }
                        break;

                    case "hasDebt":
                    case "hasFinancialAssets":
                    case "livingAllowance":
                    case "medicalInsurance":
                    case "hasPersonalInsurance":
                    case "hasCarInsurance":
                        Integer tag = Integer.valueOf(fieldValue);
                        prompt = prompt.replace(placeholder,
                                tag == 0 ? "无" : "有");
                        break;

                    default:
                        prompt = prompt.replace(placeholder, fieldValue);
                }
            }
        } catch (IllegalAccessException e) {
            log.error("Failed to replace AiGenerateExtField variables", e);
        } catch (NumberFormatException e) {
            log.error("Failed to parse numeric field in AiGenerateExtField", e);
        } catch (NoSuchFieldException e) {
            log.error("Failed to parse NoSuchFieldException in AiGenerateExtField", e);
        }

        return prompt;
    }


}
