package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponBudgetGroupDO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @author: fengxuan
 * @create 2020-10-14 9:36 下午
 **/
public interface ICfWeaponBudgetGroupService {

    /**
     * 通过武器id查到对应的预算组
     */
    List<CfWeaponBudgetGroupDO> listByWeaponId(int weaponId);

    /**
     * 查询在使用中的预算
     */
    List<CfWeaponBudgetGroupDO> listInUseBudgetGroup(int weaponId);

    List<CfWeaponBudgetGroupDO> listInUseBudgetGroup(List<Integer> weaponIds);

    /**
     * 点查
     */
    CfWeaponBudgetGroupDO getById(int budgetGroupId);

    /**
     * 新增预算
     */
    int insert(CfWeaponBudgetGroupDO budgetGroupDO);

    /**
     * 作废预算
     */
    int invalidBudgetGroup(int budgetGroupId);

    /**
     * 编辑预算
     */
    int editBudgetGroup(CfWeaponBudgetGroupDO budgetGroupDO);

    /**
     * 查询预算表获取时间 （按照endTime正序排序）设置武器有效期
     * 按照武器id分组，按照endTime正序排序
     * @param allWeaponIdList
     * @return
     */
    List<CfWeaponBudgetGroupDO> listGroupByWeaponSortByEndTime(List<Integer> allWeaponIdList);

    /**
     * 根据id查询预算组表
     * @param idSet
     * @return
     */
    List<CfWeaponBudgetGroupDO> listByIds(Set<Integer> idSet);

    /**
     * 查询endTime在当前时间之后的预算
     * @param weaponId
     * @return
     */
    List<CfWeaponBudgetGroupDO> listInUsingBudgetGroup(int weaponId);

    /**
     * 修改已使用名额，和冻结名额，addUseResource和addApplyingResource允许为负数 爱心首页主题活动武器由于存在提前扣减 冻结名额还有冻结金额传0
     * @param id
     * @param addUseResource       增加已使用名额
     * @param addApplyingResource  增加冻结名额,当归还时 为负数
     * @param addUseMoney          增加已使用名额
     * @param addApplyingMoney     增加冻结名额,当归还时 为负数
     * @return
     */
    int modifyGroupResource(int id, int addUseResource, int addApplyingResource, int addUseMoney, int addApplyingMoney);

    /**
     * 预算组冻结数量+1
     * @param id
     * @return
     */
    int addApplyingResource(int id,int addApplyingResource);

    int addApplyingMoney(int id,int addApplyingMoney);

    int minusUsedMoney(int id, int unUsedMoney);

    int repairResource(int id, int usedResource, int applyingResource);

    int getApplyingResource(int weaponId, Date time);

    int updateAmountByWeaponId(int weaponId, long amount);
}
