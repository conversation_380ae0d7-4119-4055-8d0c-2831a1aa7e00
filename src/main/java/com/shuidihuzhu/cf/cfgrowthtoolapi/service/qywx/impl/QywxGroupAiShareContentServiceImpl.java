package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.QywxGroupAiShareContentDO;
import com.shuidihuzhu.cf.dao.qywx.QywxGroupAiShareContentDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.QywxGroupAiShareContentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * ai生成企微转发语信息(QywxGroupAiShareContent)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-13 11:52:41
 */
@Service("qywxGroupAiShareContentService")
public class QywxGroupAiShareContentServiceImpl implements QywxGroupAiShareContentService {
   
    @Resource
    private QywxGroupAiShareContentDao qywxGroupAiShareContentDao;

    @Override
    public QywxGroupAiShareContentDO queryById(long id) {
        return qywxGroupAiShareContentDao.queryById(id);
    }

    @Override
    public List<QywxGroupAiShareContentDO> queryByMsgId(long msgId) {
        return qywxGroupAiShareContentDao.queryByMsgId(msgId);
    }


    @Override
    public int insert(QywxGroupAiShareContentDO qywxGroupAiShareContent) {
        return qywxGroupAiShareContentDao.insert(qywxGroupAiShareContent);
    }


    @Override
    public boolean deleteById(long id) {
        return qywxGroupAiShareContentDao.deleteById(id) > 0;
    }

    @Override
    public void bindMsgId(long id, long msgId) {
        qywxGroupAiShareContentDao.bindMsgId(id, msgId);
    }

    @Override
    public void fillShareContent(QywxGroupAiShareContentDO aiShareContent) {
        qywxGroupAiShareContentDao.fillShareContent(aiShareContent);
    }
}
