package com.shuidihuzhu.cf.cfgrowthtoolapi.service.common.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.CommonPermissionConfigDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.common.CommonPermissionConfigService;
import com.shuidihuzhu.cf.dao.common.CommonPermissionConfigDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommonPermissionConfigServiceImpl implements CommonPermissionConfigService {

    @Autowired
    private CommonPermissionConfigDao commonPermissionConfigDao;

    @Override
    public int insert(CommonPermissionConfigDo commonPermissionConfigDo) {
        if (commonPermissionConfigDo == null) {
            return 0;
        }
        return commonPermissionConfigDao.insert(commonPermissionConfigDo);
    }

    @Override
    public int update(CommonPermissionConfigDo commonPermissionConfigDo) {
        if (commonPermissionConfigDo == null) {
            return 0;
        }
        return commonPermissionConfigDao.update(commonPermissionConfigDo);
    }

    @Override
    public CommonPermissionConfigDo getByConfigType(int configType) {
        if (configType == 0) {
            return null;
        }
        return commonPermissionConfigDao.getByConfigType(configType);
    }

    @Override
    public List<CommonPermissionConfigDo> listByConfigTypes(List<Integer> configTypes) {
        if (CollectionUtils.isEmpty(configTypes)) {
            return Lists.newArrayList();
        }
        return commonPermissionConfigDao.listByConfigTypes(configTypes);
    }

    @Override
    public Map<Integer, CommonPermissionConfigDo> mapByConfigTypes(List<Integer> configTypes) {
        if (CollectionUtils.isEmpty(configTypes)) {
            return Maps.newHashMap();
        }
        List<CommonPermissionConfigDo> commonPermissionConfigDoList = commonPermissionConfigDao.listByConfigTypes(configTypes);
        if (CollectionUtils.isEmpty(commonPermissionConfigDoList)) {
            return Maps.newHashMap();
        }
        return commonPermissionConfigDoList.stream().collect(Collectors.toMap(CommonPermissionConfigDo::getConfigType, Function.identity()));
    }
}
