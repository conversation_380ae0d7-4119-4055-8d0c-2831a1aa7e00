package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponAssignRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;

import java.util.List;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2020-10-14 9:36 下午
 **/
public interface ICfWeaponAssignRecordService {

    void batchAdd(List<CfWeaponAssignRecordDO> weaponAssignRecordDOs);

    /**
     * 查询有分配权限的分配记录
     * @param budgetId
     * @return
     */
    List<CfWeaponAssignRecordDO> listCanAssignByBudgetId(int budgetId);

    List<CfWeaponAssignRecordDO> listCanAssignByBudgetIds(List<Integer> budgetIds);

    /**
     * 更新可分配字段
     */
    int updateAssignStatus(List<Integer> budgetIds, WeaponEnums.BudgetAssignStatusEnum assignStatusEnum);

    /**
     * 根据uniqueCode查询分配记录
     * @param uniqueCode
     * @return
     */
    List<CfWeaponAssignRecordDO> listByUniqueCode(String uniqueCode);

    /**
     * 根据uniqueCode + budgetId 查询分配记录
     * @param uniqueCode
     * @param budgetId
     * @return
     */
    CfWeaponAssignRecordDO getByUniqueCodeAndBudgetId(String uniqueCode, int budgetId);

    /**
     * 插入浏览状态
     * @param assignRecord
     * @return
     */
    int insert4ViewWeapon(CfWeaponAssignRecordDO assignRecord);

    /**
     * 根据budgetIds + uniqueCode 查询有分配权限的分配记录
     * @param budgetIds
     * @return
     */
    List<CfWeaponAssignRecordDO> listCanAssignByBudgetIdsAndUniqueCode(List<Integer> budgetIds, String uniqueCode);

    Map<Integer, Long> mapByWeaponIdAndBudgetsAndAssignSource(int weaponId, List<Integer> budgetIds, WeaponEnums.BudgetAssignStatusEnum can_assign, int assignSource);

    List<CfWeaponAssignRecordDO> listByBudgetIdAndAssignSource(int budgetId, int assignSource);

    List<CfWeaponAssignRecordDO> listByBudgetIdsAndAssignSource(List<Integer> budgetIds, int assignSource);

    void batchCancelAssign(List<Integer> delIdList, WeaponEnums.BudgetAssignStatusEnum assignStatusEnum, String assignMis);

    /**
     * 根据uniqueCode + weaponId 查询有效分配记录
     * @param uniqueCode
     * @param weaponId
     * @return
     */
    List<CfWeaponAssignRecordDO> listByUniqueCodeAndWeaponId(String uniqueCode, int weaponId);

    /**
     * 更新浏览状态
     * @param ids
     * @param viewWeaponStatus
     */
    int batchUpdateViewWeaponStatus(List<Integer> ids, boolean viewWeaponStatus);

    int batchAddOrUpdate(List<CfWeaponAssignRecordDO> newWeaponAssignList, List<CfWeaponAssignRecordDO> weaponListFromDb, String assignMis);

    int updateOrgId(int id, int orgId);

    //点查
    CfWeaponAssignRecordDO findById(int id);


    List<CfWeaponAssignRecordDO> listAssignByBudgetGroupId(int budgetGroupId);

    int updateAssignStatusByUniqueCode(String uniqueCode, WeaponEnums.BudgetAssignStatusEnum assignStatusEnum);

    int updateAvailableAmount(int id, double availableAmount);

    List<CfWeaponAssignRecordDO> listByBudgetIdAndAssignStatus(int budgetId, int assignStatus);
}
