package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfHospitalApplyDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.ApplyStatusDataModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.HospitalOrgInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.HospitalApplySearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.CfHospitalApplyService;
import com.shuidihuzhu.cf.dao.hospital.CfHospitalApplyDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 医院纠错申报表(CfHospitalApply)表服务实现类
 *
 * <AUTHOR>
 * @since 2020-11-09 15:44:56
 */
@Service("cfHospitalApplyService")
public class CfHospitalApplyServiceImpl implements CfHospitalApplyService {
    @Resource
    private CfHospitalApplyDao cfHospitalApplyDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public CfHospitalApplyDo queryById(long id) {
        return this.cfHospitalApplyDao.queryById(id);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    @Override
    public List<CfHospitalApplyDo> queryAllByLimit(int offset, int limit) {
        return this.cfHospitalApplyDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param cfHospitalApplyDo 实例对象
     * @return 实例对象
     */
    @Override
    public CfHospitalApplyDo insert(CfHospitalApplyDo cfHospitalApplyDo) {
        this.cfHospitalApplyDao.insert(cfHospitalApplyDo);
        return cfHospitalApplyDo;
    }

    @Override
    public List<HospitalOrgInfo> groupByOrgId() {
        return cfHospitalApplyDao.groupByOrgId();
    }

    @Override
    public List<ApplyStatusDataModel> listGroupByStatus(HospitalApplySearchParam searchParam) {
        if (searchParam == null) {
            return Lists.newArrayList();
        }
        return cfHospitalApplyDao.listGroupByStatus(searchParam);
    }

    @Override
    public List<CfHospitalApplyDo> listBySearchParam(HospitalApplySearchParam searchParam) {
        if (searchParam == null) {
            return Lists.newArrayList();
        }
        return cfHospitalApplyDao.listBySearchParam(searchParam);
    }

    @Override
    public int countBySearchParam(HospitalApplySearchParam searchParam) {
        if (searchParam == null) {
            return 0;
        }
        return cfHospitalApplyDao.countBySearchParam(searchParam);
    }



    @Override
    public int updateApplyStatus(int applyId, int applyStatus) {
        return cfHospitalApplyDao.updateApplyStatus(applyId, applyStatus);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return this.cfHospitalApplyDao.deleteById(id) > 0;
    }

    @Override
    public List<CfHospitalApplyDo> listByHospitalNameAndCity(String hospitalName, String city) {
        if (StringUtils.isBlank(hospitalName) || StringUtils.isBlank(city)) {
            return Lists.newArrayList();
        }
        return cfHospitalApplyDao.listByHospitalNameAndCity(hospitalName, city);
    }

    @Override
    public List<CfHospitalApplyDo> listByOrgIdAndHospitalName(int orgId, String hospitalName) {
        return cfHospitalApplyDao.listByOrgIdAndHospitalName(orgId, hospitalName);
    }
}