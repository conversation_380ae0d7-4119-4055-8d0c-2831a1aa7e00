package com.shuidihuzhu.cf.cfgrowthtoolapi.service.kuaishou;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfKuaiShouApplyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.kuaishou.CfKuaiShouApplyRecordModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.kuaishou.CfKuaiShouApplyResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.CfKuaiShouApplySearchParams;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/25 7:46 PM
 */
public interface CfKuaiShouApplyService {

    OpResult<String> addKuaiShouApplyRecord(CfKuaiShouApplyRecordModel kuaiShouApplyRecordModel, CrowdfundingVolunteer volunteer);

    OpResult<CfKuaiShouApplyDO> getKuaiShouApplyDetail(String infoId);

    CommonPageModel<CfKuaiShouApplyResultModel> getApplyRecordList(CfKuaiShouApplySearchParams params);

    void modifyStatus(Long id, Integer dealResult, List<Integer> dealReasons, String name, String otherReason);

    void exportApplyRecord(CfKuaiShouApplySearchParams params, HttpServletResponse response, long adminLongUserId);

    boolean getIsApplyRecord(String infoId);
}
