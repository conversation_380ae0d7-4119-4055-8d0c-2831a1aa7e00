package com.shuidihuzhu.cf.cfgrowthtoolapi.service.greenchannel;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.greenchannel.CfClewGreenChannelApplyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGreenChannelEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.GreenChannelApplyParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.GreenChannelApplyService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolCrusorQuery;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.greenchannel.CfClewGreenChannelApplyDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class GreenChannelApplyServiceImpl implements GreenChannelApplyService {

    @Autowired
    CfClewGreenChannelApplyDao channelApplyDao;


    @Override
    public List<CfClewGreenChannelApplyDO> queryApplyList(GreenChannelApplyParam queryParam) {
        if (queryParam == null){
            return null;
        }
        List<CfClewGreenChannelApplyDO> applyList = new ArrayList<>();
        if (queryParam.getPageSize() == null || queryParam.getPageIndex() == null){
             applyList = channelApplyDao.selectApplyListByParam(queryParam, null);
        }else {
            int offSet = (queryParam.getPageIndex() - 1) * queryParam.getPageSize();
            applyList = channelApplyDao.selectApplyListByParam(queryParam, offSet);
        }
        return applyList;
    }

    @Override
    public int queryApplyCount(GreenChannelApplyParam queryParam) {
        int count = 0 ;
        if (queryParam == null){
            return count;
        }
        if (queryParam.getPageSize() == null || queryParam.getPageIndex() == null) {
            count = channelApplyDao.selectApplyCountByParam(queryParam, null);
        }else {
            int offSet = (queryParam.getPageIndex() -1) * queryParam.getPageSize();
            count = channelApplyDao.selectApplyCountByParam(queryParam, offSet);
        }
        return count;
    }

    @Override
    public CfClewGreenChannelApplyDO queryApplyById(Long id) {
        if (id == null){
            return null;
        }
        return channelApplyDao.selectByPrimaryKey(id);
    }

    @Override
    public List<CfClewGreenChannelApplyDO> queryApplyByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return Lists.newArrayList();
        }
        return channelApplyDao.selectByIdList(idList);
    }

    @Override
    public List<CfClewGreenChannelApplyDO> queryApplyByCaseId(Integer caseId) {
        if (caseId != null){
           return channelApplyDao.selectApplyByCaseId(caseId);
        }
        return null;
    }

    @Override
    public List<JSONObject> queryApplyNum(GreenChannelApplyParam queryParam) {
        HashMap<Integer, HashMap<Integer, Long>> applyNumMap = channelApplyDao.selectApplyStatusNumGroupStatus(queryParam);
        List<JSONObject> data = new ArrayList<>();
        Map<Integer, Integer> keyMap = new HashMap<>();
        if (applyNumMap != null && applyNumMap.size() > 0) {
            Iterator<Map.Entry<Integer, HashMap<Integer, Long>>> iterator = applyNumMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Integer, HashMap<Integer, Long>> next = iterator.next();
                HashMap<Integer, Long> map = next.getValue();
                Integer key = Integer.valueOf(map.get("statusKey") + "");
                Integer value = Integer.valueOf(map.get("statusNum") + "");
                keyMap.put(key, value);
            }
        }
        for (CfGreenChannelEnums.ApplyStatusEnum applyStatusEnum : CfGreenChannelEnums.ApplyStatusEnum.values()) {
            if (queryParam.getApplyResourceType() == CfGreenChannelEnums.ApplyResourceTypeEnum.ONE_PUBLIC.getCode() ){
                switch (applyStatusEnum) {
                    case FIRST_PROJECT:
                    case SECOND_RISK:
                    case THIRD_CONTENT:
                    case FOURTH_FOUNDATION:
                    case SIX_FOUNDATION:
                    case FIVE_COMSUMER:
                    case SEVEN_FOUNDATION:
                        JSONObject object = new JSONObject();
                        object.put("label", applyStatusEnum.getDesc());
                        object.put("value", applyStatusEnum.getCode());
                        object.put("count", keyMap.get(applyStatusEnum.getCode()) == null ? 0 : keyMap.get(applyStatusEnum.getCode()));
                        data.add(object);
                        break;
                    default:
                }
            }else {
                switch (applyStatusEnum) {
                    case FIRST_PROJECT_DOCTOR:
                    case SECOND_RISK_DOCTOR:
                    case THIRD_CONTENT_DOCTOR:
                    case FOURTH_PROJECT_DOCTOR:
                        JSONObject object = new JSONObject();
                        object.put("label", applyStatusEnum.getDesc());
                        object.put("value", applyStatusEnum.getCode());
                        object.put("count", keyMap.get(applyStatusEnum.getCode()) == null ? 0 : keyMap.get(applyStatusEnum.getCode()));
                        data.add(object);
                        break;
                    default:
                }
            }
        }
        return data;
    }

    @Override
    public void saveApplyRecord(CfClewGreenChannelApplyDO applyDO) {
        if (applyDO == null){
            return;
        }
        channelApplyDao.insertSelective(applyDO);
    }

    @Override
    public void modifyApplyRecord(CfClewGreenChannelApplyDO applyDO) {
        if (applyDO == null || applyDO.getId() == null){
            return;
        }
        channelApplyDao.updateByPrimaryKeySelective(applyDO);
    }

    @Override
    public void modifyApplyStatusById(Long id, CfGreenChannelEnums.ApplyStatusEnum status, List<CfGreenChannelEnums.PushChannelEnum> channeList) {
        if (id == null || (status ==null && CollectionUtils.isEmpty(channeList))){
            return;
        }
        int applyStatus = status.getCode();
        String channel = CollectionUtils.isEmpty(channeList)? null: StringUtils.join(channeList.stream().map(CfGreenChannelEnums.PushChannelEnum::getCode).
                collect(Collectors.toList()), ",");
        channelApplyDao.modifyApplyStatusById(id, applyStatus, channel);
    }

    @Override
    public int modifyPayProgressById(Long id, Integer progressStatus) {
        if (id == null){
            return 0;
        }
        return channelApplyDao.modifyPayStatusById(id,progressStatus);
    }

    @Override
    public int modifyRejectReasonById(Long id, String reason, CfGreenChannelEnums.ApplyStatusEnum statusEnum) {
        if (id == null){
            return 0;
        }
        return channelApplyDao.modifyRejectReasonById(id,reason,statusEnum.getCode());
    }

    @Override
    public int modifyReviewOpinionById(Long id, String viewPinoin, CfGreenChannelEnums.ApplyStatusEnum applyStatusEnum) {
        if (id == null){
            return 0;
        }
        CfClewGreenChannelApplyDO record = new CfClewGreenChannelApplyDO();
        record.setId(id);
        record.setReviewOpinion(StringUtils.isEmpty(viewPinoin)?"":viewPinoin);
        record.setApprovalStatus(applyStatusEnum !=null ?applyStatusEnum.getCode():null);
        return channelApplyDao.updateByPrimaryKeySelective(record);
    }

    @Override
    public int modifyPublicCaseId(Long id, String publicCaseId) {
        if (id == null) {
            return 0;
        }
        CfClewGreenChannelApplyDO record = new CfClewGreenChannelApplyDO();
        record.setId(id);
        record.setPublicCaseId(publicCaseId);
        return channelApplyDao.updateByPrimaryKeySelective(record);
    }


    @Override
    public List<CfClewGreenChannelApplyDO> listByPublicCaseIds(List<String> publicCaseIds) {
        return GrowthtoolCrusorQuery.queryPartition(publicCaseIds, (item) -> channelApplyDao.listByPublicCaseIds(item));
    }


}
