package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponApplyRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WeaponEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.MemberUseBudgetInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponApplyRecordModel;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @author: fengxuan
 * @create 2020-10-14 9:36 下午
 **/
public interface ICfWeaponApplyRecordService {

    /**
     * 根据子预算id查询当日申请通过数据
     * @param budgetId
     * @return
     */
    List<CfWeaponApplyRecordDO> todayApplyLimitRecord(int budgetId);


    List<CfWeaponApplyRecordDO> listByCaseIdAndActivityType(int caseId, List<Integer> activityTypes);

    /**
     * 根据caseId和活动类型 进行重复申请判断 重复校验范围 武器申请状态=待审核或上级审核通过
     * @param caseId
     * @param activityType
     * @return
     */
    boolean checkCaseApplyRepeat(Integer caseId, int activityType);

    CfWeaponApplyRecordDO getById(int id);

    int addApplyLeader(int id, String leaderUniqueCode);

    List<CfWeaponApplyRecordDO> listByIds(List<Integer> ids);

    /**
     * 根据预算查看申请记录
     * @param budgetGroupId
     * @param startTime
     * @param endTime
     * @return
     */
    List<CfWeaponApplyRecordDO> listByBudgetGroupId(int budgetGroupId, String startTime, String endTime);

    int updateApplyStatusFromLeader(int applyId, WeaponEnums.BudgetApplyStatusEnum applyStatusEnum, String comment);

    /**
     * 回调修改申请状态
     */
    int updateApplyStatusFromActivity(int applyId,  String activityName, WeaponEnums.BudgetApplyStatusEnum applyStatusEnum, String comment, Long callbackId);

    /**
     * 插入数据 插入后需要填入主键id
     * @param weaponApplyRecordModel
     * @return
     */
    int insertBySubmit(WeaponApplyRecordModel weaponApplyRecordModel);

    /**
     * 根据uniqueCode查询总数
     * @param uniqueCode
     * @param queryStartTime
     * @param queryEndTime
     * @param weaponId
     * @return
     */
    long countByUniqueCode(String uniqueCode, Date queryStartTime, Date queryEndTime, Integer weaponId);

    /**
     * 根据uniqueCode查询，倒序排序
     * @param uniqueCode
     * @param queryStartTime
     * @param queryEndTime
     * @param weaponId
     * @param offSet
     * @param pageSize
     * @return
     */
    List<CfWeaponApplyRecordDO> listApplyInfoByUniqueCode(String uniqueCode, Date queryStartTime, Date queryEndTime, Integer weaponId, int offSet, int pageSize);

    int updateExtInfo(int applyId, String extInfo);

    /**
     * 获取所有的申请记录
     * @return
     */
    List<CfWeaponApplyRecordDO> listAllApplyRecord(int offset, int limit);


    int countMemberUseInfoGroupByUniqueCode(List<Integer> budgetIdList);

    /**
     * 分页查询排名
     */
    List<MemberUseBudgetInfo> listMemberUseInfoGroupByUniqueCode(List<Integer> budgetIdList, int offset, int limit);


    int countByWeaponId(int weaponId);


    long countByOrgIds(List<Integer> orgIds, Integer applyStatus, Date queryStartTime, Date queryEndTime, Integer weaponId);

    List<CfWeaponApplyRecordDO> listApplyByOrgIds(List<Integer> budgetIds, Integer applyStatus, Date queryStartTime, Date queryEndTime, Integer weaponId, int offSet, int pageSize);

    CfWeaponApplyRecordDO getByCaseId(int caseId);

    CfWeaponApplyRecordDO getByCaseIdAndStatus(int caseId, int activityType, List<Integer> applyStatusList);

    int updateApplyMoney(int applyId, int unUsedMoney);

    void deleteApplyRecord(long id);

    CfWeaponApplyRecordDO getByCaseIdAndActivityTypeAndStatus(int caseId, List<Integer> activityTypeList, List<Integer> applyStatusList);

    List<CfWeaponApplyRecordDO> listByUniqueCodeByBudgetId(String uniqueCode, int budgetId);

    List<CfWeaponApplyRecordDO> listByGroupIdAndApplyStatus(int budgetGroupId, List<Integer> applyStatusList);

    List<CfWeaponApplyRecordDO> listByGroupIdsAndApplyStatus(List<Integer> budgetGroupIds, List<Integer> applyStatusList);

    List<CfWeaponApplyRecordDO> listByBudgetIdAndUniqueCodesAndApplyStatus(int budgetId, Set<String> uniqueCode, List<Integer> applyStatusList);
}
