package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseDonateCommissionDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiCaseCommisionVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCaseDonateCommissionService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiCaseDonateCommissionDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-08-04
 */

@Service
@Slf4j
public class CfKpiCaseDonateCommissionServiceImpl implements ICfKpiCaseDonateCommissionService {

    @Autowired
    private CfKpiCaseDonateCommissionDao cfKpiCaseDonateCommissionDao;

    @Override
    public long queryCaseDonateCommissionCount(String monthKey, String uniqueCode, long validAmount, int validDonateNum) {
        return cfKpiCaseDonateCommissionDao.queryCaseDonateCommissionCount(monthKey, uniqueCode, validAmount, validDonateNum);
    }

    @Override
    public List<CfKpiCaseCommisionVO> queryCaseDonateCommissionDetail(String monthKey, String uniqueCode, long validAmount, int validDonateNum, int pageNo, int pageSize) {
        int offSet = (pageNo - 1) * pageSize;
        return cfKpiCaseDonateCommissionDao.queryCaseDonateCommissionDetail(monthKey, uniqueCode, validAmount, validDonateNum, offSet, pageSize);
    }

    @Override
    public OpResult<List<CfKpiCaseCommisionVO>> queryCaseCommissionDetail(String monthkey, String uniqueCode) {
        if (StringUtils.isEmpty(monthkey)){
            monthkey = DateUtil.getYYYYMMStr(DateUtil.getCurrentDate());
        }
        List<CfKpiCaseCommisionVO> list = cfKpiCaseDonateCommissionDao.queryCaseCommissionDetail(monthkey,uniqueCode);
        return OpResult.createSucResult(list);
    }

    @Override
    public int batchSaveOrUpdate(List<CfKpiCaseDonateCommissionDo> kpiCaseDonateCommissionDoList, String uniqueCode, String monthKey) {
        List<CfKpiCaseDonateCommissionDo> cfKpiCaseCommisionFromDb = cfKpiCaseDonateCommissionDao.getCaseCommission(uniqueCode,monthKey);
        Map<Long,CfKpiCaseDonateCommissionDo> statDataMap = cfKpiCaseCommisionFromDb.stream().collect(Collectors.toMap(CfKpiCaseDonateCommissionDo::getCaseId, Function.identity()));
        Map<Long,CfKpiCaseDonateCommissionDo> inputCaseCommisionMap = kpiCaseDonateCommissionDoList.stream().collect(Collectors.toMap(CfKpiCaseDonateCommissionDo::getCaseId, Function.identity()));
        List<CfKpiCaseDonateCommissionDo> insertList = Lists.newArrayList();
        List<CfKpiCaseDonateCommissionDo> updateList = Lists.newArrayList();
        List<CfKpiCaseDonateCommissionDo> delList = Lists.newArrayList();
        for (CfKpiCaseDonateCommissionDo cfKpiCommisionDO : kpiCaseDonateCommissionDoList){
            CfKpiCaseDonateCommissionDo singleDataFromDb = statDataMap.get(cfKpiCommisionDO.getCaseId());
            if (Objects.isNull(singleDataFromDb)){
                insertList.add(cfKpiCommisionDO);
            }else{
                cfKpiCommisionDO.setId(singleDataFromDb.getId());
                updateList.add(cfKpiCommisionDO);
            }
        }

        for (CfKpiCaseDonateCommissionDo cfKpiCommisionDO : cfKpiCaseCommisionFromDb){
            //被顶替掉的数据
            if (Objects.isNull(inputCaseCommisionMap.get(cfKpiCommisionDO.getCaseId()))){
                delList.add(cfKpiCommisionDO);
            }
        }

        if (CollectionUtils.isNotEmpty(insertList)){
            List<List<CfKpiCaseDonateCommissionDo>> listList = Lists.partition(insertList, GeneralConstant.MAX_PAGE_SIZE);
            listList.forEach(list -> cfKpiCaseDonateCommissionDao.batchInsert(list));
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            List<List<CfKpiCaseDonateCommissionDo>> listList = Lists.partition(updateList, GeneralConstant.MAX_PAGE_SIZE);
            listList.forEach(list -> cfKpiCaseDonateCommissionDao.batchUpdate(list));
        }
        if (CollectionUtils.isNotEmpty(delList)){
            List<List<CfKpiCaseDonateCommissionDo>> listList = Lists.partition(delList, GeneralConstant.MAX_PAGE_SIZE);
            listList.forEach(list -> cfKpiCaseDonateCommissionDao.batchUpdateDelete(list));
        }
        return 0;
    }

    @Override
    public List<CfKpiCaseDonateCommissionDo> listCaseDonateCommission(String monthKey, List<String> uniqueCodeList) {
        if (CollectionUtils.isEmpty(uniqueCodeList)){
            return Lists.newArrayList();
        }
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> cfKpiCaseDonateCommissionDao.listCaseCommission(monthKey, list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
    }
}
