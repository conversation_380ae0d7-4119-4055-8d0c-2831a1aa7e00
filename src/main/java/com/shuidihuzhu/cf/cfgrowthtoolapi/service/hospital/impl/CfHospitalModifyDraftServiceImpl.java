package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfHospitalModifyInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.CfHospitalModifyDraftService;
import com.shuidihuzhu.cf.dao.hospital.CfHospitalModifyDraftDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 医院审核填写的医院信息和审核表是1:n的关系(CfHospitalModifyInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2020-11-09 15:46:08
 */
@Service
public class CfHospitalModifyDraftServiceImpl implements CfHospitalModifyDraftService {

    @Resource
    private CfHospitalModifyDraftDao draftDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public CfHospitalModifyInfoDo queryById(long id) {
        return draftDao.queryById(id);
    }




    @Override
    public List<CfHospitalModifyInfoDo> listByApproveId(int approveId) {
        return draftDao.listByApproveId(approveId);
    }

    /**
     * 新增数据
     *
     * @param modifyList 实例对象
     */
    @Override
    public void insertBatch(List<CfHospitalModifyInfoDo> modifyList) {
        if (CollectionUtils.isEmpty(modifyList)) {
            return;
        }
        draftDao.insertBatch(modifyList);
    }

    /**
     * 修改数据
     *
     * @param cfHospitalModifyInfoDo 实例对象
     * @return 实例对象
     */
    @Override
    public CfHospitalModifyInfoDo update(CfHospitalModifyInfoDo cfHospitalModifyInfoDo) {
        this.draftDao.update(cfHospitalModifyInfoDo);
        return this.queryById(cfHospitalModifyInfoDo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return this.draftDao.deleteById(id) > 0;
    }

    @Override
    public boolean isExistByAreaAddress(String areaAddress) {
        if (StringUtils.isBlank(areaAddress)) {
            return false;
        }
        Integer result = draftDao.isExistByAreaAddress(areaAddress);
        return Objects.nonNull(result) && result > 0;
    }
}