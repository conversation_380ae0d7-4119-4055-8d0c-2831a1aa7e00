package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiVisitBaseDataDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiVisitBaseDataService;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.shuidihuzhu.common.util.redisson.RedissonHandler.ONE_DAY;

/**
 * 陪访记录(CfKpiVisitBaseData)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-14 10:43:13
 */
@Slf4j
@Service
public class CfKpiVisitBaseDataServiceImpl implements ICfKpiVisitBaseDataService {

    @Resource
    private CfKpiVisitBaseDataDao cfKpiVisitBaseDataDao;

    @Autowired
    private AlarmClient alarmClient;

    @Autowired
    private ApplicationService applicationService;


    @Override
    public int insert(CfKpiVisitBaseDataDO cfKpiVisitBaseData) {
        //插入前重置下对应的validVisit
        //找到这个人这天的数据
        //陪访日志优化：限定同一天有效陪访次数最多统计2次，且同一个陪访（同一个被陪访人，相同时间）多次重复录入只认定为1次有效陪访
        List<CfKpiVisitBaseDataDO> visitBaseDataDOS = cfKpiVisitBaseDataDao.listByDayKeyAndUniqueCode(cfKpiVisitBaseData.getDayKey(), cfKpiVisitBaseData.getUniqueCode());
        Optional<CfKpiVisitBaseDataDO> first = visitBaseDataDOS.stream()
                .filter(item -> item.getVisitId() == cfKpiVisitBaseData.getVisitId())
                .findFirst();
        if (first.isPresent()) {
            return 0;
        }
        //有效陪访
        if (cfKpiVisitBaseData.getValidVisit() == 1) {
            boolean maybeDuplicateDate = visitBaseDataDOS.stream()
                    .filter(item -> item.getValidVisit() == 1)
                    .anyMatch(item -> Objects.equals(cfKpiVisitBaseData.getVisitStartTime(), item.getVisitStartTime())
                            && Objects.equals(cfKpiVisitBaseData.getBeVisitUniqueCode(), item.getBeVisitUniqueCode()));
            long hasValidCount = visitBaseDataDOS.stream()
                    .filter(item -> item.getValidVisit() == 1)
                    .count();
            if (maybeDuplicateDate && hasValidCount >= 2) {
                log.info("可能是重复数据或者是超过两次有效数据");
                cfKpiVisitBaseData.setValidVisit(0);
            }
        }
        return cfKpiVisitBaseDataDao.insert(cfKpiVisitBaseData);
    }


    @Override
    public Map<String, List<CfKpiVisitBaseDataDO>> listValidByTime(String monthKey, String dayKey) {
        DateTime dateTime = DateTime.parse(monthKey, GrowthtoolUtil.ymfmt);
        String startTime = dateTime.withDayOfMonth(1).toString(GrowthtoolUtil.ymdfmt);
        String endTime = dateTime.plusMonths(1).withDayOfMonth(1).toString(GrowthtoolUtil.ymdfmt);
        Map<String, List<CfKpiVisitBaseDataDO>> result = cfKpiVisitBaseDataDao.listByTime(startTime, endTime, dayKey)
                .stream()
                .collect(Collectors.groupingBy(CfKpiVisitBaseDataDO::getUniqueCode));
        if (MapUtils.isEmpty(result)) {
            String content = "绩效计算-陪访数据为空,请核查是否正常";
            alarmClient.sendByUser(Lists.newArrayList("fengxuan"), content);
        }
        return result;
    }

}