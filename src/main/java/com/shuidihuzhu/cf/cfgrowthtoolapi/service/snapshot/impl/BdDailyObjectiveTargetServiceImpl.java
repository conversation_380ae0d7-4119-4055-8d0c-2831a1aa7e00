package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdDailyObjectiveTargetDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot.BdDailyObjectiveTargetService;
import com.shuidihuzhu.cf.dao.snapshot.BdDailyObjectiveTargetDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 组织的目标项和目标值(BdDailyObjectiveTarget)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-02 15:00:08
 */
@Service("bdDailyObjectiveTargetService")
public class BdDailyObjectiveTargetServiceImpl implements BdDailyObjectiveTargetService {
   
    @Resource
    private BdDailyObjectiveTargetDao bdDailyObjectiveTargetDao;

    @Override
    public BdDailyObjectiveTargetDO queryById(long id) {
        return bdDailyObjectiveTargetDao.queryById(id);
    }


    @Override
    public int batchInsert(List<BdDailyObjectiveTargetDO> targetDOList) {
        if (CollectionUtils.isEmpty(targetDOList)) {
            return 0;
        }
        return bdDailyObjectiveTargetDao.batchInsert(targetDOList);
    }

    @Override
    public int batchUpdate(List<BdDailyObjectiveTargetDO> targetDOList) {
        if (CollectionUtils.isEmpty(targetDOList)) {
            return 0;
        }
        return bdDailyObjectiveTargetDao.batchUpdate(targetDOList);
    }

    @Override
    public boolean deleteById(long id) {
        return bdDailyObjectiveTargetDao.deleteById(id) > 0;
    }

    @Override
    public List<BdDailyObjectiveTargetDO> listByOrgIds(String dateKey, List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Collections.emptyList();
        }
        return bdDailyObjectiveTargetDao.listByOrgIds(dateKey, orgIds);
    }
}
