package com.shuidihuzhu.cf.cfgrowthtoolapi.service.caserefund;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.caserefund.CfCaseRefundTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.caserefund.CfCaseRefundDetailParam;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/22 11:26 AM
 */
public interface CfCaseRefundTaskService {

    int insert(CfCaseRefundTaskDO cfCaseRefundTaskDO);

    int updateCacleTime(Date cacleTime, String traceNo);

    CfCaseRefundTaskDO getCaseRefundTaskInfo(String traceNo);

    CfCaseRefundTaskDO getCaseRefundTaskInfoById(Integer id);

    int modifyCaseRefundTaskStatus(Integer id, Integer dealStatus);

    List<CfCaseRefundTaskDO> getCaseRefundTaskByIds(CfCaseRefundDetailParam cfCaseRefundDetailParam);

    void updateUrgeStatus(int id);

    Integer getCaseRefundCountByParams(CfCaseRefundDetailParam cfCaseRefundDetailParam);

    List<CfCaseRefundTaskDO> getCaseRefundTaskByTime(CfCaseRefundDetailParam params);

}
