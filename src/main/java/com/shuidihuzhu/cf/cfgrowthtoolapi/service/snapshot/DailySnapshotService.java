package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.objective.DailyTargetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2024-12-02 15:19
 **/
@Slf4j
@Service
public class DailySnapshotService {

    @Autowired
    private BdCrmDailyCycleService bdCrmDailyCycleService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private BdCrmOrganizationSnapshotService orgSnapshotService;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private BdCrmOrgUserRelationSnapshotService relationSnapshotService;

    @Autowired
    private BdDailyObjectiveReachService objectiveReachService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private BdDailyObjectiveReachDetailService bdDailyObjectiveReachDetailService;


    public void createDailySnapshot() {
        String dateKey = DateTime.now().toString(GrowthtoolUtil.ymdfmt);
        createByDateKey(dateKey);
    }

    public void createByDateKey(String dateKey) {
        //查看下是否已经生成过快照
        List<BdCrmDailyCycleDO> bdCrmDailyCycleDOS = bdCrmDailyCycleService.listByDateKey(Lists.newArrayList(dateKey));
        if (CollectionUtils.isEmpty(bdCrmDailyCycleDOS)) {
            log.info("没有对应的周期");
            return;
        }
        BdCrmDailyCycleDO bdCrmDailyCycleDO = bdCrmDailyCycleDOS.get(0);
        int createSnapshot = bdCrmDailyCycleDO.getCreateSnapshot();
        if (createSnapshot == 1) {
            log.info("已经生成过快照");
            return;
        }
        //获取所有的组织
        List<BdCrmOrganizationDO> allOrg = crmSelfBuiltOrgReadService.listNotTestOrg();
        //去除蜂鸟组织
        Set<Long> partnerOrgIdSet = crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(apolloService.getPartnerOrgId()).stream()
                .map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toSet());
        allOrg = allOrg.stream().filter(item -> !partnerOrgIdSet.contains(item.getId())).collect(Collectors.toList());
        //一个组织一个组织去同步数据
        List<BdCrmOrgUserRelationDO> allOrgRelationList = Lists.newArrayList();
        for (BdCrmOrganizationDO bdCrmOrganizationDO : allOrg) {
            allOrgRelationList.addAll(crmOrganizationRelationService.listRelationByOrgId(bdCrmOrganizationDO.getId()));
        }
        List<BdCrmOrganizationSnapshotDO> orgSnapshotList = allOrg.stream()
                .map(item -> BdCrmOrganizationSnapshotDO.create(item, dateKey))
                .collect(Collectors.toList());
        List<BdCrmOrgUserRelationSnapshot> relationSnapshotList = allOrgRelationList.stream()
                .map(item -> BdCrmOrgUserRelationSnapshot.create(item, dateKey))
                .collect(Collectors.toList());
        orgSnapshotService.batchInsert(orgSnapshotList);
        relationSnapshotService.batchInsert(relationSnapshotList);
        bdCrmDailyCycleService.updateCreateSnapshot(dateKey);
    }


    //修改完成情况
    public void addTarget(DailyTargetUtil.DailyTargetEnum dailyTargetEnum, int addValue, String uniqueCode, Long caseId) {
        log.info("addTarget dailyTargetEnum:{},uniqueCode:{}", dailyTargetEnum, uniqueCode);
        String dateKey = DateTime.now().toString(GrowthtoolUtil.ymdfmt);
        //获取需要修改的目标
        BdCrmOrgUserRelationSnapshot bdCrmOrgUserRelationSnapshot = relationSnapshotService.listByUniqueCode(dateKey, uniqueCode)
                .stream().findFirst()
                .orElse(null);
        long orgId = Optional.ofNullable(bdCrmOrgUserRelationSnapshot).map(BdCrmOrgUserRelationSnapshot::getOrgId).orElse(0L);
        String misName = Optional.ofNullable(bdCrmOrgUserRelationSnapshot).map(BdCrmOrgUserRelationSnapshot::getMisName).orElse("");
        //调用objectiveReachService,如果存在记录则更新,如果不存在则插入
        BdDailyObjectiveReachDO userDailyReach = objectiveReachService.getByTargetTypeAndUniqueCode(dateKey, uniqueCode, dailyTargetEnum.getCode());
        //查询是否有记录
        BdDailyObjectiveReachDetail dailyObjectiveReachDetail = bdDailyObjectiveReachDetailService.selectByTargetTypeAndCaseId(dailyTargetEnum.getCode(),caseId);
        if (dailyObjectiveReachDetail != null) {
            log.info("caseId:{} 已经处理过", caseId);
            return;
        }
        if (userDailyReach != null) {
            userDailyReach.setReachValue(userDailyReach.getReachValue() + addValue);
        } else {
            userDailyReach = new BdDailyObjectiveReachDO()
                    .setTargetId(dailyTargetEnum.getCode())
                    .setReachValue(addValue)
                    .setDateKey(dateKey)
                    .setOrgId(orgId)
                    .setReachType(DailyTargetUtil.ReachTypeEnum.user.getCode())
                    .setMisName(misName)
                    .setUniqueCode(uniqueCode);
        }

        objectiveReachService.addOrUpdate(userDailyReach);
        dailyObjectiveReachDetail = new BdDailyObjectiveReachDetail();
        dailyObjectiveReachDetail.setReachId(userDailyReach.getId());
        dailyObjectiveReachDetail.setCaseId(caseId);
        dailyObjectiveReachDetail.setTargetType(dailyTargetEnum.getCode());
        int result = bdDailyObjectiveReachDetailService.inset(dailyObjectiveReachDetail);
        log.info("插入明细表result:{}", result);
        BdDailyObjectiveReachDO orgDailyReach = objectiveReachService.getByTargetTypeAndOrgId(dateKey, orgId, dailyTargetEnum.getCode());
        if (orgDailyReach != null) {
            orgDailyReach.setReachValue(orgDailyReach.getReachValue() + addValue);
        } else {
            orgDailyReach = new BdDailyObjectiveReachDO()
                    .setTargetId(dailyTargetEnum.getCode())
                    .setReachValue(addValue)
                    .setDateKey(dateKey)
                    .setReachType(DailyTargetUtil.ReachTypeEnum.org.getCode())
                    .setOrgId(orgId);
        }
        objectiveReachService.addOrUpdate(orgDailyReach);

    }
}
