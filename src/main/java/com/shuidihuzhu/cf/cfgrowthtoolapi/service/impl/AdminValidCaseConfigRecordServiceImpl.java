package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigRecordVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.AdminValidCaseConfigRecordParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.AdminValidCaseConfigRecordService;
import com.shuidihuzhu.cf.dao.AdminValidCaseConfigRecordDao;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/11  14:50
 */
@Service
public class AdminValidCaseConfigRecordServiceImpl implements AdminValidCaseConfigRecordService {

    @Autowired
    private AdminValidCaseConfigRecordDao adminValidCaseConfigRecordDao;

    @Autowired
    private UserFeignClient userFeignClient;

    @Override
    public Response<Integer> save(AdminValidCaseConfigRecordParam param) {
        int res = adminValidCaseConfigRecordDao.save(param);
        return NewResponseUtil.makeSuccess(res);
    }

    @Override
    public Response<List<AdminValidCaseConfigRecordVO>> getRecordList(long configId) {
        List<AdminValidCaseConfigRecordDO> adminValidCaseConfigRecordDOList = adminValidCaseConfigRecordDao.getRecordList(configId);
        if (CollectionUtils.isEmpty(adminValidCaseConfigRecordDOList)) {
            return NewResponseUtil.makeSuccess();
        }
        //获取操作人姓名
        List<Long> operatorUserIdList = adminValidCaseConfigRecordDOList.stream().map(AdminValidCaseConfigRecordDO::getOperatorUserId).collect(Collectors.toList());
        Response<List<AuthUserDto>> response = userFeignClient.getAuthUserByIds(operatorUserIdList);
        Map<Long, String> userMap = Optional.ofNullable(response).map(Response::getData).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(AuthUserDto::getUserId, AuthUserDto::getUserName, (before, after) -> before));

        List<AdminValidCaseConfigRecordVO> adminValidCaseConfigRecordVOList = adminValidCaseConfigRecordDOList.stream().map(v -> {
            AdminValidCaseConfigRecordVO adminValidCaseConfigRecordVO = new AdminValidCaseConfigRecordVO();
            adminValidCaseConfigRecordVO.setOperatorContent(v.getOperatorContent());
            adminValidCaseConfigRecordVO.setOperatorTime(DateUtil.formatDateTime(v.getCreateTime()));
            adminValidCaseConfigRecordVO.setOperatorUserName(userMap.get(v.getOperatorUserId()));
            return adminValidCaseConfigRecordVO;
        }).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(adminValidCaseConfigRecordVOList);
    }
}
