package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

/**
 * <AUTHOR>
 * @date 2019-09-01
 */
public interface ICommonJudgment {

    /**
     * 是否关注水指定公众号
     * @param userId
     * @return
     */
    boolean checkSubscribeByUserIdAndThridType(long userId,int userThirdType);

    /**
     * 关注是否来自特殊eventKey
     * @param userId
     * @return
     */
    boolean checkIsExceptSubscribe(long userId);

    /**
     * 判断是否有在筹案例
     */
    boolean checkIsHaveNoEndCase(long userId);

    /**
     * 是否填写过任何一项草稿信息
     * true 表示有填写过内容 false表示没有填写过内容
     * @param userId
     * @param startTime 时间格式:yyyy-MM-dd HH:mm:ss
     * @param endTime   时间格式:yyyy-MM-dd HH:mm:ss
     * @return
     */
    boolean checkIsFilled(long userId,String startTime,String endTime);

    /**
     *  24小时内是否登记过
     *  true 表示有登记 false表是没有登记
     * @param userId
     * @return
     */
    boolean checkIsWithin24HoursRepetition(long userId);

    /**
     * 是否有前置草稿
     * true 表示有前置草稿
     * @param userId
     * @return
     */
    boolean checkIsHaveDraft(long userId);
}
