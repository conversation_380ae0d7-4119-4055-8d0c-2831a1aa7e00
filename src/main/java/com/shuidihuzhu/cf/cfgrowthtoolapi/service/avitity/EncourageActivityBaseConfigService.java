package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.EncourageActivityBaseConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.param.EncourageActivityQueryParam;

import java.util.List;

/**
 * 激励活动基本信息表(EncourageActivityBaseConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-23 15:32:55
 */
public interface EncourageActivityBaseConfigService {

    EncourageActivityBaseConfigDO queryById(long id);

    int insert(EncourageActivityBaseConfigDO encourageActivityBaseConfig);

    int update(EncourageActivityBaseConfigDO encourageActivityBaseConfig);

    boolean deleteById(long id);

    boolean updateStatusById(long id, int status);

    List<EncourageActivityBaseConfigDO> listByIds(List<Long> ids);

    List<EncourageActivityBaseConfigDO> listByCondition(EncourageActivityQueryParam query);

    int totalByCondition(EncourageActivityQueryParam query);

    EncourageActivityBaseConfigDO getLastOneByCondition(EncourageActivityQueryParam query);
}
