package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmMsgDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmMsgVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.MsgCategory;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.MsgCategoryGroup;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCrmMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.MsgCategoryUtil;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmMsgDao;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/12/5 5:19 PM
 */
@Service
@AllArgsConstructor
@NoArgsConstructor
public class CfBdCrmMsgService implements ICfBdCrmMsgService {

    @Autowired
    private CfBdCrmMsgDao cfBdCrmMsgDao;


    @Override
    public int saveCfBdCrmMsg(String title, String subTitle, String content, String mis, String uniqueCode) {
        CfBdCrmMsgDO cfBdCrmMsgDO = new CfBdCrmMsgDO(title, subTitle, content, mis, uniqueCode);
        //拦截下查看对应的类型
        MsgCategory msgCategory = MsgCategoryUtil.getMsgCategory(title);
        cfBdCrmMsgDO.setMsgCategory(msgCategory.getCategoryId());
        return cfBdCrmMsgDao.saveCfBdCrmMsg(cfBdCrmMsgDO);
    }


    @Override
    public int updateReadStatusByUniqueCode(long id, String uniqueCode, Integer categoryId) {
        return cfBdCrmMsgDao.updateReadStatusById(id, uniqueCode, categoryId);
    }

    @Override
    public int updateReadStatusByUniqueCode(String uniqueCode, Date before1Month, Integer categoryId) {
        return cfBdCrmMsgDao.updateReadStatusByUniqueCode(uniqueCode, before1Month, categoryId);
    }

    @Override
    public List<CfBdCrmMsgVo> listByUniqueCode(String uniqueCode, int pageNo, int pageSize, Integer categoryId, Date startDate, Date endDate) {
        int offset = (pageNo - 1) * pageSize;
        return cfBdCrmMsgDao.listByUniqueCode(uniqueCode, offset, pageSize, categoryId, startDate, endDate);
    }


    @Override
    public long countNoReadMsgByUniqueCode(String uniqueCode, Date createTime) {
        return cfBdCrmMsgDao.countNoReadMsgByUniqueCode(uniqueCode, createTime);
    }

    @Override
    public CfBdCrmMsgDO getCfBdCrmMsgDOById(long id) {
        return cfBdCrmMsgDao.getCfBdCrmMsgDOById(id);
    }


    @Override
    public List<CfBdCrmMsgDO> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return cfBdCrmMsgDao.listByIds(ids);
    }

    @Override
    public List<MsgCategoryGroup> groupByCategory(String uniqueCode, Date createTime) {
        return cfBdCrmMsgDao.groupByCategory(uniqueCode, createTime);
    }
}
