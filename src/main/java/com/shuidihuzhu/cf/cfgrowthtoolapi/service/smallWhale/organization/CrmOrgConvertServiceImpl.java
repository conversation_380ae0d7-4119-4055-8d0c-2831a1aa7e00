package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.*;
import com.shuidihuzhu.client.auth.saas.model.dto.SimpleGroupVo;
import com.shuidihuzhu.client.auth.saas.model.dto.SimpleUserVo;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.client.cf.growthtool.model.OrgInfoModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-06-15 21:03
 * 自建组织转化为copy类
 **/
@Service
@Slf4j
public class CrmOrgConvertServiceImpl implements ICrmOrgConvertService {

    @Autowired
    private ICrmSelfBuiltOrgReadService organizationService;

    @Autowired
    private ICrmOrganizationRelationService relationService;


    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> listByOrgId(long orgId) {
        List<BdCrmVolunteerOrgnizationSimpleModel> simpleModels = Lists.newArrayList();
        Map<Long, BdCrmOrganizationDO> orgIdTBdOrg = organizationService.listAllSubOrgIncludeSelf(orgId)
                .stream().collect(Collectors.toMap(BdCrmOrganizationDO::getId, Function.identity(), (before, after) -> before));
        List<BdCrmOrgUserRelationDO> relationDOList = relationService.listByOrgIdsFromDB(Lists.newArrayList(orgIdTBdOrg.keySet()));
        //返回全路径
        Map<Long, String> orgIdTPath = organizationService.listChainByOrgIdsWithDefaultSplitter(relationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList()));
        for (BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO : relationDOList) {
            BdCrmVolunteerOrgnizationSimpleModel simpleModel = OrgConvertUtil.convertTSimpleModel(bdCrmOrgUserRelationDO,
                    orgIdTBdOrg.get(bdCrmOrgUserRelationDO.getOrgId()),
                    orgIdTPath.get(bdCrmOrgUserRelationDO.getOrgId()));
            simpleModels.add(simpleModel);
        }
        return simpleModels;
    }

    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> listByOrgIds(List<Long> orgIds) {
        List<BdCrmVolunteerOrgnizationSimpleModel> simpleModels = Lists.newArrayList();
        Map<Long, BdCrmOrganizationDO> orgIdTBdOrg = organizationService.getOrgInfoList(orgIds)
                .stream().collect(Collectors.toMap(BdCrmOrganizationDO::getId, Function.identity(), (before, after) -> before));
        List<BdCrmOrgUserRelationDO> relationDOList = relationService.listByOrgIdsFromDB(Lists.newArrayList(orgIdTBdOrg.keySet()));
        //返回全路径
        Map<Long, String> orgIdTPath = organizationService.listChainByOrgIdsWithDefaultSplitter(relationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList()));
        for (BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO : relationDOList) {
            BdCrmVolunteerOrgnizationSimpleModel simpleModel = OrgConvertUtil.convertTSimpleModel(bdCrmOrgUserRelationDO,
                    orgIdTBdOrg.get(bdCrmOrgUserRelationDO.getOrgId()),
                    orgIdTPath.get(bdCrmOrgUserRelationDO.getOrgId()));
            simpleModels.add(simpleModel);
        }
        return simpleModels;
    }


    @Override
    public AdminOrganization getUserOrganization(String mis) {
        //查看人员在哪些组织
        List<BdCrmOrgUserRelationDO> relationDOList = relationService.listMemberOrgRelationByMis(mis);
        if (CollectionUtils.isEmpty(relationDOList)) {
            log.info("当前人员不存在组织信息");
            return null;
        }
        return getAdminOrganization(relationDOList);

    }

    private AdminOrganization getAdminOrganization(List<BdCrmOrgUserRelationDO> relationDOList) {
        AdminOrganization adminOrganization = new AdminOrganization();
        BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO = relationDOList.get(0);
        adminOrganization.setId((int) bdCrmOrgUserRelationDO.getOrgId());
        //查找对应的组织
        BdCrmOrganizationDO currentOrg = organizationService.getCurrentOrgById(bdCrmOrgUserRelationDO.getOrgId());
        if (currentOrg == null) {
            log.info("currentOrgById is null, orgId:{}", bdCrmOrgUserRelationDO.getOrgId());
            return null;
        }
        adminOrganization.setName(currentOrg.getOrgName());
        adminOrganization.setParentOrgId((int) currentOrg.getParentId());
        return adminOrganization;
    }

    @Override
    public AdminOrganization getUserOrganizationByUniqueCode(String uniqueCode) {
        //查看人员在哪些组织
        List<BdCrmOrgUserRelationDO> relationDOList = relationService.listMemberOrgRelationByUniqueCode(uniqueCode);
        if (CollectionUtils.isEmpty(relationDOList)) {
            log.info("当前人员不存在组织信息");
            return null;
        }
        return getAdminOrganization(relationDOList);
    }

    @Override
    public OrgMembersResult getOrgMembers(int parentOrgId) {
        BdCrmOrganizationDO currentOrgById = organizationService.getCurrentOrgById(parentOrgId);
        if (currentOrgById == null) {
            return null;
        }
        OrgMembersResult orgMembersResult = new OrgMembersResult();
        orgMembersResult.setOrgId(parentOrgId);
        orgMembersResult.setOrgName(currentOrgById.getOrgName());

        List<SimpleGroupVo> simpleOrgVoList = Lists.newArrayList();
        List<BdCrmOrganizationDO> directSubOrgByOrgId = organizationService.findDirectSubOrgByOrgId(parentOrgId);
        for (BdCrmOrganizationDO bdCrmOrganizationDO : directSubOrgByOrgId) {
            simpleOrgVoList.add(OrgConvertUtil.convertTSimpleOrgVo(bdCrmOrganizationDO));
        }
        orgMembersResult.setSubOrgs(simpleOrgVoList);

        //查询对应的组织人员信息
        List<SimpleUserVo> simpleUserVoList = Lists.newArrayList();
        List<GrowthSimpleUserVo> growthSimpleUserList = Lists.newArrayList();
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = relationService.listRelationByOrgId(parentOrgId);
        for (BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO : bdCrmOrgUserRelationDOS) {
            simpleUserVoList.add(OrgConvertUtil.convertTSimpleUserVo(bdCrmOrgUserRelationDO));
            growthSimpleUserList.add(OrgConvertUtil.convertTGrowthSimpleUserVo(bdCrmOrgUserRelationDO));
        }
        orgMembersResult.setMembers(simpleUserVoList);
        orgMembersResult.setMemberList(growthSimpleUserList);
        orgMembersResult.setMemberCount(simpleUserVoList.size());
        return orgMembersResult;
    }

    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> getAllUserByOrgId(int orgId) {
        return this.listByOrgId(orgId);
    }

    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> getAllUserByOrgIds(List<Long> orgIds) {
        return this.listByOrgIds(orgIds);
    }

    @Override
    public List<OrgInfoModel> getSubOrgByOrgId(int orgId) {
        List<BdCrmOrganizationDO> directSubOrgByOrgs = organizationService.findDirectSubOrgByOrgId(orgId);
        List<OrgInfoModel> orgInfoModels = Lists.newArrayList();
        for (BdCrmOrganizationDO directSubOrgByOrg : directSubOrgByOrgs) {
            orgInfoModels.add(OrgConvertUtil.convertTOrgInfoModel(directSubOrgByOrg));
        }
        return orgInfoModels;
    }



    @Override
    public List<OrgInfoModel> getOrgListByOrgId(List<Integer> orgIdList) {
        List<OrgInfoModel> orgInfoModels = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orgIdList)) {
            return orgInfoModels;
        }
        List<Long> orgIds = orgIdList.stream().map(Long::valueOf).collect(Collectors.toList());
        Map<Long, BdCrmOrganizationDO> listOrgInfo = organizationService.listOrgInfo(orgIds);
        for (BdCrmOrganizationDO organizationDO : listOrgInfo.values()) {
            OrgInfoModel orgInfoModel = OrgConvertUtil.convertTOrgInfoModel(organizationDO);
            orgInfoModels.add(orgInfoModel);
        }
        return orgInfoModels;
    }

    @Override
    public OrgInfoModel getParentOrgByOrgId(int orgId) {
        BdCrmOrganizationDO parentOrg = organizationService.getParentOrg(orgId);
        if (parentOrg == null) {
            return null;
        }
        return OrgConvertUtil.convertTOrgInfoModel(parentOrg);
    }


    @Override
    public List<OrgInfoModel> getAllOrg() {
        return organizationService.getAllOrg()
                .stream()
                .map(OrgConvertUtil::convertTOrgInfoModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<CfBdcrmVolunteerOrgnizationCopyDO> getOrgByUniqueCodes(List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Lists.newArrayList();
        }
        List<BdCrmOrgUserRelationDO> relationDOS = relationService.listByUniqueCodes(uniqueCodes);
        List<Long> orgIds = relationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList());
        Map<Long, BdCrmOrganizationDO> bdCrmOrganizationDOMap = organizationService.listOrgInfo(orgIds);
        return relationDOS.stream()
                .map(item -> OrgConvertUtil.convertTCopyDo(item, bdCrmOrganizationDOMap.get(item.getOrgId())))
                .collect(Collectors.toList());
    }

    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> getBdCrmVolunteerOrgnizationSimpleModelByMisList(Collection<String> misList) {
        List<BdCrmOrgUserRelationDO> relationDOS = relationService.listByMisList(misList);
        return getBdCrmVolunteerOrgnizationSimpleModels(relationDOS);
    }

    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> getByUniqueCodeList(Collection<String> uniqueCodeList) {
        List<BdCrmOrgUserRelationDO> relationDOList = relationService.listByUniqueCodes(uniqueCodeList);
        return getBdCrmVolunteerOrgnizationSimpleModels(relationDOList);
    }

    @NotNull
    private List<BdCrmVolunteerOrgnizationSimpleModel> getBdCrmVolunteerOrgnizationSimpleModels(List<BdCrmOrgUserRelationDO> relationDOS) {
        if (CollectionUtils.isEmpty(relationDOS)) {
            return Lists.newArrayList();
        }
        List<Long> orgIds = relationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList());
        Map<Long, BdCrmOrganizationDO> bdCrmOrganizationDOMap = organizationService.listOrgInfo(orgIds);
        //返回全路径
        Map<Long, String> orgIdTPath = organizationService.listChainByOrgIdsWithDefaultSplitter(orgIds);
        return relationDOS.stream()
                .map(item -> {
                            String path = orgIdTPath.get(item.getOrgId());
                            return OrgConvertUtil.convertTSimpleModel(item, bdCrmOrganizationDOMap.get(item.getOrgId()), path);
                        }
                ).collect(Collectors.toList());
    }

    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> getSimpleModelByMisNameList(List<String> misNameList) {

        List<BdCrmOrgUserRelationDO> relationDOS = relationService.listByMisNameList(misNameList);
        return getBdCrmVolunteerOrgnizationSimpleModels(relationDOS);
    }
}
