package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfBdCrmOrgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CrmOrgUserVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.dao.es.EsCfBdCrmObjectiveOrgMemberSnapshotDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmObjectiveOrgMemberSnapshotMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveOrgMemberSnapshot;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfBdCrmObjectiveOrgMemberSnapshotService;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2021/2/23 上午10:39
 */
@Service
public class CfBdCrmObjectiveOrgMemberSnapshotServiceImpl implements CfBdCrmObjectiveOrgMemberSnapshotService {

    @Resource
    private CfBdCrmObjectiveOrgMemberSnapshotMapper cfBdCrmObjectiveOrgMemberSnapshotMapper;
    @Resource
    private EsCfBdCrmObjectiveOrgMemberSnapshotDao esCfBdCrmObjectiveOrgMemberSnapshotDao;
    @Autowired
    private ApolloService apolloService;

    @Override
    public CfBdCrmObjectiveOrgMemberSnapshot getByCycleIdWithOrgId(Long cycleId, Long orgId) {
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.getByCycleIdWithOrgId(cycleId, orgId);
    }

    @Override
    public List<String> listAllSubOrgUniqueCode(Long cycleId, Long orgId) {
        CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = cfBdCrmObjectiveOrgMemberSnapshotMapper.getByCycleIdWithOrgId(cycleId, orgId);
        if (orgMemberSnapshot == null) return Lists.newArrayList();
        if (apolloService.isObjectiveManageEs()) {
            return esCfBdCrmObjectiveOrgMemberSnapshotDao.listUniqueCodeByCycleIdWithLikeOrgPathExcludeSelf(cycleId, String.join("-",orgMemberSnapshot.getOrgPath(),"%"));
        }
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listUniqueCodeByCycleIdWithLikeOrgPathExcludeSelf(cycleId, orgMemberSnapshot.getOrgPath());
    }

    @Override
    public List<CfBdCrmObjectiveOrgMemberSnapshot> listAllSubByCycleIdWithOrgId(Long cycleId, Long orgId) {
        CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = cfBdCrmObjectiveOrgMemberSnapshotMapper.getByCycleIdWithOrgId(cycleId, orgId);
        if (orgMemberSnapshot == null)  return Lists.newArrayList();
        if (apolloService.isObjectiveManageEs()) {
            return esCfBdCrmObjectiveOrgMemberSnapshotDao.listByCycleIdWithLikeOrgPathExcludeSelf(cycleId, String.join("-",orgMemberSnapshot.getOrgPath(),"%"));
        }
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listByCycleIdWithLikeOrgPathExcludeSelf(cycleId, orgMemberSnapshot.getOrgPath());
    }

    @Override
    public List<Long> listSubOrgIdByCycleIdWithOrgId(Long cycleId, Long orgId) {
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listSubOrgIdByCycleIdWithOrgId(cycleId, orgId);
    }

    @Override
    public List<Long> listOrgIdByUniqueCodeWithCycleIdList(List<Long> cycleIdList, String uniqueCode) {
        if (CollectionUtils.isEmpty(cycleIdList))  return Lists.newArrayList();
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listOrgByCycleIdWithUniqueCode(cycleIdList,Lists.newArrayList(uniqueCode)).stream().map(CrmOrgUserVo::getUniqueKey).map(Long::valueOf).distinct().collect(Collectors.toList());
    }

    @Override
    public List<CrmOrgUserVo> listObjectiveCycleOrgByCycleIdWithUniqueCode(List<Long> cycleIdList, List<String> uniqueCodeList) {
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listOrgByCycleIdWithUniqueCode(cycleIdList, uniqueCodeList).stream().sorted(Comparator.comparing(CrmOrgUserVo::depth)).collect(Collectors.toList());
    }

    @Override
    public List<CfBdCrmObjectiveOrgMemberSnapshot> listSubOrgByCycleIdWithOrgId(Long cycleId, Long orgId) {
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listSubOrgByCycleIdWithOrgId(cycleId, orgId);
    }

    @Override
    public void batchInsertOrgSnapshot(List<CfBdCrmOrgModel> crmOrgModelList, List<Long> cycleIdList) {
        for (Long cycleId : cycleIdList) {
            Lists.partition(crmOrgModelList, 100).stream()
                    .forEach(list -> {
                        List<Long> orgIdInDB = cfBdCrmObjectiveOrgMemberSnapshotMapper.listCfBdCrmOrgModelByCycleIdWithOrgIdList(cycleId,
                                list.stream().map(CfBdCrmOrgModel::getOrgId).collect(Collectors.toList())).stream().map(CfBdCrmOrgModel::getOrgId).collect(Collectors.toList());
                        list = list.stream().filter(item -> !orgIdInDB.contains(item.getOrgId())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(list)) {
                            return;
                        }
                        cfBdCrmObjectiveOrgMemberSnapshotMapper.batchInsertOrgSnapshot(list, cycleId);
                    });
        }
    }

    @Override
    public void batchInsertMemberSnapshot(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, List<Long> cycleIdList) {
        for (Long cycleId : cycleIdList) {
            Lists.partition(orgMemberSnapshotList, 100).stream()
                    .forEach(list -> {
                        List<String> memberUniqueKeyList = cfBdCrmObjectiveOrgMemberSnapshotMapper.listByCycleIdWithUniqueCodeList(cycleId,
                                list.stream().map(CfBdCrmObjectiveOrgMemberSnapshot::getUniqueCode).collect(Collectors.toList()))
                                .stream().map(CfBdCrmObjectiveOrgMemberSnapshot::showUniqueKey).collect(Collectors.toList());
                        list = list.stream().filter(item -> !memberUniqueKeyList.contains(item.showUniqueKey())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(list)) {
                            return;
                        }
                        cfBdCrmObjectiveOrgMemberSnapshotMapper.batchInsertMemberSnapshot(list, cycleId);
                    });
        }
    }

    @Override
    public void updateOrgNameWithOrgPathByCycleIdList(List<Long> cycleIdList, BdCrmOrganizationDO currentOrganization, String oldOrgPath, String oldOrgName) {
        if (CollectionUtils.isEmpty(cycleIdList)) {
            return;
        }
        List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList = cfBdCrmObjectiveOrgMemberSnapshotMapper.listByCycleIdListWithOrgPath(cycleIdList, oldOrgPath);
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) {
            return;
        }
        orgMemberSnapshotList = orgMemberSnapshotList.stream().map(orgMemberSnapshot -> {
            if (orgMemberSnapshot.getOrgId().equals(currentOrganization.getId())) {
                orgMemberSnapshot.setOrgName(currentOrganization.getOrgName());
                orgMemberSnapshot.setOrgPath(orgMemberSnapshot.getOrgPath().substring(0, orgMemberSnapshot.getOrgPath().lastIndexOf("-")+1) + currentOrganization.getOrgName());
            }else {
                orgMemberSnapshot.setOrgPath(orgMemberSnapshot.getOrgPath().replace(String.format("-%s-",oldOrgName),String.format("-%s-", currentOrganization.getOrgName())));
            }
            return orgMemberSnapshot;
        }).collect(Collectors.toList());
        Lists.partition(orgMemberSnapshotList, 100).parallelStream().forEach(list -> cfBdCrmObjectiveOrgMemberSnapshotMapper.batchUpdateOrgNameWithOrgPath(list));
    }

    @Override
    public List<CfBdCrmOrgModel> listCfBdCrmOrgModelByCycleId(Long cycleId){
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listCfBdCrmOrgModelByCycleId(cycleId);
    }

    @Override
    public void updateParentOrgId(long orgId, long newParentOrgId, String oldOrgPath, String newOrgPath, List<Long> cycleIdList) {
        cfBdCrmObjectiveOrgMemberSnapshotMapper.updateParentOrgIdByCycleIdListWithOrgId(cycleIdList, orgId, newParentOrgId);
        List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList = cfBdCrmObjectiveOrgMemberSnapshotMapper.listByCycleIdListWithOrgPath(cycleIdList, oldOrgPath);
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) {
            return;
        }
        orgMemberSnapshotList = orgMemberSnapshotList.stream().map(orgMemberSnapshot -> {
            if (orgMemberSnapshot.getOrgId().equals(orgId)) {
                orgMemberSnapshot.setOrgPath(newOrgPath);
            }else {
                orgMemberSnapshot.setOrgPath(orgMemberSnapshot.getOrgPath().replace(String.format("%s-",oldOrgPath),String.format("%s-", newOrgPath)));
            }
            return orgMemberSnapshot;
        }).collect(Collectors.toList());
        Lists.partition(orgMemberSnapshotList, 100).parallelStream().forEach(list -> cfBdCrmObjectiveOrgMemberSnapshotMapper.batchUpdateOrgNameWithOrgPath(list));
    }

    @Override
    public List<Long> listSubOrgIdFromAllCycleByOrgIdList(List<Long> orgIdList) {
        if (CollectionUtils.isEmpty(orgIdList)) {
            return Lists.newArrayList();
        }
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listSubOrgIdFromAllCycleByOrgIdList(orgIdList);
    }
    @Override
    public List<CfBdCrmObjectiveOrgMemberSnapshot> listSubOrgFromAllCycleByOrgIdList(List<Long> orgIdList) {
        if (CollectionUtils.isEmpty(orgIdList)) {
            return Lists.newArrayList();
        }
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listSubOrgFromAllCycleByOrgIdList(orgIdList);
    }

    @Override
    public List<CfBdCrmOrgModel> listParentOrgByOrgIdList(List<Long> orgIdList, Long cycleId) {
        if (CollectionUtils.isEmpty(orgIdList)) {
            return Lists.newArrayList();
        }
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listParentOrgByOrgIdList(orgIdList, cycleId);
    }

    @Override
    public List<String> listParentOrgNameByOrgIdList(List<Long> orgIdList, Long cycleId) {
        if (CollectionUtils.isEmpty(orgIdList)) {
            return Lists.newArrayList();
        }
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listParentOrgNameByOrgIdList(orgIdList, cycleId);
    }

    @Override
    public List<String> listLeaderByOrgIdList(List<Long> orgIdList, Long cycleId) {
        if (CollectionUtils.isEmpty(orgIdList)) {
            return Lists.newArrayList();
        }
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listLeaderUniqueCodeByOrgIdList(orgIdList, cycleId);
    }

    @Override
    public List<String> listLeaderUniqueCodeByOrgIdListWithRoleList(List<Long> orgIdList, Long cycleId, List<Integer> roleList) {
        if (CollectionUtils.isEmpty(orgIdList) || CollectionUtils.isEmpty(roleList)) {
            return Lists.newArrayList();
        }
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listLeaderUniqueCodeByOrgIdListWithRoleList(orgIdList, cycleId, roleList);
    }

    @Override
    public void updateIsDelete(List<Long> objectiveCycleIdList, String uniqueCode, Long orgId, int isDelete) {
        if (StringUtils.isBlank(uniqueCode)) return;
        cfBdCrmObjectiveOrgMemberSnapshotMapper.updateIsDelete(objectiveCycleIdList, uniqueCode, orgId, isDelete);
    }

    @Override
    public List<CfBdCrmObjectiveOrgMemberSnapshot> listByCycleIdWithUniqueCode(Long cycleId, String uniqueCode) {
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listByCycleIdWithUniqueCode(cycleId, uniqueCode);
    }

    @Override
    public void updateIsDelete(String newOrgPath, int isDelete) {
        if (StringUtils.isBlank(newOrgPath)) return;
        cfBdCrmObjectiveOrgMemberSnapshotMapper.updateIsDeleteByOrgPath(newOrgPath, isDelete);
    }

    @Override
    public List<CfBdCrmObjectiveOrgMemberSnapshot> listAllMemberByCycleIdWithOrgIdList(Long cycleId, List<Long> subOrgIdList) {
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listAllMemberByCycleIdWithOrgIdList(cycleId, subOrgIdList);
    }

    @Override
    public List<CfBdCrmObjectiveOrgMemberSnapshot> listByCycleIdListWithOrgId(Collection<Long> cycleIdList, Long orgId) {
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listByCycleIdListWithOrgId(cycleIdList, orgId);
    }

    @Override
    public List<BdCrmOrganizationDO> getAllOrgByCycleId(Long cycleId) {
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.getAllOrgByCycleId(cycleId);
    }
    @Override
    public List<Long> listLeafParentOrgIdByCycleId(Long cycleId) {
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listLeafParentOrgIdByCycleId(cycleId);
    }

    @Override
    public void updateOrgAttribute(List<Long> objectiveCycleIdList, long orgId, int orgAttribute) {
        cfBdCrmObjectiveOrgMemberSnapshotMapper.updateOrgAttribute(objectiveCycleIdList, orgId, orgAttribute);
    }

    @Override
    public List<Long> listCycleIdByCycleIdListWithOrgId(List<Long> cycleIdList, Long orgId) {
        if (CollectionUtils.isEmpty(cycleIdList)) return Lists.newArrayList();
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listCycleIdByCycleIdListWithOrgId(cycleIdList, orgId);
    }

    @Override
    public List<CfBdCrmObjectiveOrgMemberSnapshot> listOrgByCycleIdListWithOrgIdList(List<Long> cycleIdList, List<Long> orgIdList) {
        if (CollectionUtils.isEmpty(cycleIdList) || CollectionUtils.isEmpty(orgIdList)) return Lists.newArrayList();
        return cfBdCrmObjectiveOrgMemberSnapshotMapper.listOrgByCycleIdListWithOrgIdList(cycleIdList, orgIdList);
    }
}
