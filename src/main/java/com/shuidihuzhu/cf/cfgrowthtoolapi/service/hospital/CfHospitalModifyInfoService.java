package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfHospitalModifyInfoDo;

import java.util.List;

/**
 * 医院审核填写的医院信息和审核表是1:n的关系(CfHospitalModifyInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2020-11-09 15:46:07
 */
public interface CfHospitalModifyInfoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CfHospitalModifyInfoDo queryById(long id);

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    List<CfHospitalModifyInfoDo> queryAllByLimit(int offset, int limit);

    /**
     * 根据审核查询填写信息
     * @param approveId
     * @return
     */
    List<CfHospitalModifyInfoDo> listByApproveId(int approveId);

    /**
     * 新增数据
     *
     * @param modifyList 实例对象
     */
    void insertBatch(List<CfHospitalModifyInfoDo> modifyList);

    /**
     * 修改数据
     *
     * @param cfHospitalModifyInfoDo 实例对象
     * @return 实例对象
     */
    CfHospitalModifyInfoDo update(CfHospitalModifyInfoDo cfHospitalModifyInfoDo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);

}