package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.configuration.EsDataSoureConfigure;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfHospitalInterviewCaseDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.GdMapHospitalDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICrmNewHospitalService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICrmHospitalService;
import com.shuidihuzhu.cf.dao.bdcrm.CfHospitalInterviewCaseDao;
import com.shuidihuzhu.cf.dao.bdcrm.CrmNewHospitalDao;
import com.shuidihuzhu.esdk.EsClient;
import com.shuidihuzhu.esdk.utils.ClientTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.geo.GeoPoint;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.GeoDistanceQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class CrmNewHospitalServiceImpl implements ICrmNewHospitalService {
    @Autowired
    private CrmNewHospitalDao crmNewHospitalDao;

    @Autowired
    ICrmHospitalService crmHospitalService;

    @Resource(name = EsDataSoureConfigure.ES_BEAN_NAME)
    private EsClient esClientNewHospital;

    private static final String index = "shuidi_cf_clewtrack_cf_crm_new_hospital_alias";

    @Autowired
    private CfHospitalInterviewCaseDao cfHospitalInterviewCaseDao;

    @Override
    public String getHospitalVPoiCodeByNameAndCity(String hospitalName, String cityName) {
        if(StringUtils.isBlank(hospitalName)){
            return "";
        }
        String ret =  crmNewHospitalDao.getHospitalVPoiCodeByNameAndCity(hospitalName,cityName);
        if(StringUtils.isNotBlank(ret)){
            return ret;
        }
        return "";
    }

    @Override
    public GdMapHospitalDO getGdMapHospitalDOByVHospitalcode(String vhospitalCode){
        if (StringUtils.isBlank(vhospitalCode)) {
            return null;
        }
        return crmNewHospitalDao.getGdMapHospitalDOByVHospitalcode(vhospitalCode);
    }

    @Override
    public String getHospitalVPoiCodeByGdCode(String vpoiId) {
        if(StringUtils.isBlank(vpoiId)){
            return "";
        }
        return crmNewHospitalDao.getHospitalVPoiCodeByGdCode(vpoiId);
    }

    @Override
    public int insert(GdMapHospitalDO gdMapHospitalDO) {
        int ret = crmNewHospitalDao.insert(gdMapHospitalDO);
        return ret;
    }
    @Override
    public GdMapHospitalDO getHospitalByGdPoiId(String gdPoiId){
        return crmNewHospitalDao.getHospitalByGdPoiId(gdPoiId);
    }

    @Override
    public void fixHospitalByGps() {
        List<GdMapHospitalDO> hospitalDOList = crmNewHospitalDao.getAllHosiptal();
        if(CollectionUtils.isEmpty(hospitalDOList)){
            return;
        }
        Map<String,List<GdMapHospitalDO>> gpsHospitalMaps = hospitalDOList.stream().collect(Collectors.groupingBy(GdMapHospitalDO::getGdLocation));

        Set<String> gpsKeyset = gpsHospitalMaps.keySet();
        Map<String,Set<String>> ysMap = Maps.newHashMap();
        for(String gps : gpsKeyset){
            String[] gpsList = gps.split(",");
            List<Map<String, Object>> maps = getHospitalMapsFromEs(gps, gpsList);
            if(CollectionUtils.isEmpty(maps)){
                log.info("pullByQueryBuilders es empty request:{}",gps);
                continue;
            }

            for(Map<String,Object> map : maps){
                String esgps = String.valueOf(map.get("gd_location"));
                if(esgps.compareToIgnoreCase(gps)==0){
                    continue;
                }
                if(ysMap.containsKey(gps)){
                    ysMap.get(gps).add(String.valueOf(map.get("vhospital_code")));
                }else{
                    ysMap.put(gps, Sets.newTreeSet(Lists.newArrayList(String.valueOf(map.get("vhospital_code")))));
                    List<GdMapHospitalDO> list = gpsHospitalMaps.get(gps);
                    ysMap.get(gps).addAll(list.stream().map(s->s.getVhospitalCode()).collect(Collectors.toList()));
                }
            }
        }

        log.info("fixHospitalByGps gps :{}",ysMap);
        Map<String,Set<String>> resultMap = Maps.newHashMap();
        for(Map.Entry<String,Set<String>> entry : ysMap.entrySet()){
            Set<String> value = entry.getValue();
            if(resultMap.containsKey(value.toString())){
                continue;
            }
            resultMap.put(value.toString(),value);
        }

        log.info("result:{}",resultMap);

        for(Map.Entry<String,Set<String>> entry : resultMap.entrySet()){
            String targetVcode = null;
            for(String item : entry.getValue()){
                targetVcode = item;
                break;
            }
            crmNewHospitalDao.updateNewVhospitalCodeByVcode(entry.getValue(),targetVcode);
        }
    }

    @Override
    public List<Map<String, Object>> getHospitalMapsFromEs(String gps, String[] gpsList) {
        GeoPoint geoPoint = new GeoPoint(Double.valueOf(gpsList[1]), Double.valueOf(gpsList[0]));
        GeoDistanceQueryBuilder geoDistanceQueryBuilder = QueryBuilders.geoDistanceQuery("gd_location_ext")
                .distance(100, DistanceUnit.METERS)
                .point(geoPoint);
        List<Map<String, Object>> maps = null;
        try {
            maps = ClientTools.searchResponse2Map(esClientNewHospital.pullByQueryBuilders(EsDataSoureConfigure.CLUSTER_NAME, index, geoDistanceQueryBuilder, 0, 100));
        } catch (Exception e) {
            log.error(this.getClass().getName()+" pullByQueryBuilders err",e);
        }
        log.info("pullByQueryBuilders es request:{},result:{}",gps,maps.size());
        return maps;
    }

    @Override
    public GdMapHospitalDO getHospitalByGdPoiIdV2(String gdPoiId) {
        return crmNewHospitalDao.getHospitalByGdPoiIdV2(gdPoiId);
    }

    @Override
    public GdMapHospitalDO getGdMapHospitalDOByInputNameAndCity(String hospitalName, String cityName) {
        return crmNewHospitalDao.getGdMapHospitalDOByInputNameAndCity(hospitalName,cityName);
    }

    @Override
    public List<GdMapHospitalDO> standardHospitalSearch(String provinceName, String cityName, String hospitalName, String vhospitalCode, Integer pageNo, Integer pageSize,Integer acceptToPublic) {
        return crmNewHospitalDao.standardHospitalSearch(provinceName,cityName,hospitalName,vhospitalCode,pageNo,pageSize,acceptToPublic);
    }

    @Override
    public List<GdMapHospitalDO> standardHospitalSearchNoPage(String provinceName, String cityName, String hospitalName, String vhospitalCode, Integer acceptToPublic) {
        return crmNewHospitalDao.standardHospitalSearchNoPage(provinceName, cityName, hospitalName, vhospitalCode, acceptToPublic);
    }

    @Override
    public int standardHospitalSearchCount(String provinceName, String cityName, String hospitalName, String vhospitalCode,Integer acceptToPublic) {
        return crmNewHospitalDao.standardHospitalSearchCount(provinceName,cityName,hospitalName,vhospitalCode,acceptToPublic);
    }

    @Override
    public List<GdMapHospitalDO> getGdMapHospitalDOByVHospitalcodes(Set<String> vHospitalCodes) {
        if (CollectionUtils.isEmpty(vHospitalCodes)) {
            return Lists.newArrayList();
        }
        return crmNewHospitalDao.getGdMapHospitalDOByVHospitalcodes(vHospitalCodes);
    }

    @Override
    public List<GdMapHospitalDO> getGdMapHospitalDOByVHospitalcodesV2(Set<String> vHospitalCodes) {
        List<GdMapHospitalDO> retList = crmNewHospitalDao.getGdMapHospitalDOByVHospitalcodesV2(vHospitalCodes);
        return retList;
    }

    @Override
    public void deleteById(Long id, int isDelete) {
        crmNewHospitalDao.deleteById(id,isDelete);
    }

    @Override
    public void updateById(Long id, String vvhospitalCode, Integer isDelete, Integer isMain, Integer checkStatus) {
        crmNewHospitalDao.updateById(id,vvhospitalCode,isDelete,isMain,checkStatus);
    }

    @Override
    public GdMapHospitalDO getGdMapHospitalDOByVVHospitalcode(String vvhospitalCode) {
        if(StringUtils.isBlank(vvhospitalCode)){
            return null;
        }
        return crmNewHospitalDao.getGdMapHospitalDOByVVHospitalcode(vvhospitalCode);
    }

    @Override
    public List<GdMapHospitalDO> getGdMapHospitalDOByVVHospitalcodeV2(String vvhospitalCode) {
        if(StringUtils.isBlank(vvhospitalCode)){
            return Lists.newArrayList();
        }
        return crmNewHospitalDao.getGdMapHospitalDOByVVHospitalcodeV2(vvhospitalCode);
    }

    @Override
    public GdMapHospitalDO getHasCheckedHospital(String inputCity, String inputName) {
        if (StringUtils.isBlank(inputName)) {
            return null;
        }
        return crmNewHospitalDao.getHasCheckedHospital(inputCity, inputName);
    }

    @Override
    public List<GdMapHospitalDO> pageMainHospitalByCity(String city, String hospitalName, Integer isMain, int limit, int offset) {
        return crmNewHospitalDao.pageMainHospitalByCity(city, hospitalName, isMain, limit, offset);
    }

    @Override
    public List<GdMapHospitalDO> pageMainHospitalByCityIncludeAlias(String city, String hospitalName, Integer checkStatus, int limit, int offset) {
        if (StringUtils.isBlank(city)) {
            return Lists.newArrayList();
        }
        return crmNewHospitalDao.pageMainHospitalByCityIncludeAlias(city, hospitalName, checkStatus, limit, offset);
    }

    @Override
    public long countMainHospitalByCity(String city, String hospitalName, Integer isMain) {
        return crmNewHospitalDao.countMainHospitalByCity(city, hospitalName, isMain);
    }

    @Override
    public long countMainHospitalByCityIncludeAlias(String city, String hospitalName, Integer checkStatus) {
        if (StringUtils.isBlank(city)) {
            return 0L;
        }
        return crmNewHospitalDao.countMainHospitalByCityIncludeAlias(city, hospitalName, checkStatus);
    }

    @Override
    public CfHospitalInterviewCaseDO findByCaseId(int caseId) {
        if (caseId < 0) {
            return null;
        }
        return cfHospitalInterviewCaseDao.findByCaseId(caseId);
    }

    @Override
    public void fixLogicAliasById(Long id, String origin, String target) {
        if(StringUtils.isBlank(origin) || StringUtils.isBlank(target)){
            return ;
        }
        crmNewHospitalDao.fixLogicAliasById(id,origin,target);
    }

    @Override
    public List<GdMapHospitalDO> listByVvhospitalCode(List<String> vvHospitalCodes) {
        if (CollectionUtils.isEmpty(vvHospitalCodes)) {
            return Lists.newArrayList();
        }
        return crmNewHospitalDao.listByVvhospitalCode(vvHospitalCodes);
    }

    /**
     * cityName可以为空
     */
    @Override
    public List<GdMapHospitalDO> listHospitalByNameHasCheck(String inputName, String cityName) {
        if (StringUtils.isBlank(inputName) && StringUtils.isBlank(cityName)) {
            return Lists.newArrayList();
        }
        return crmNewHospitalDao.listHospitalByNameHasCheck(inputName, cityName);
    }

    @Override
    public List<GdMapHospitalDO> listByPcodeAndHospitalName(String pcode, String hospitalName) {
        if (StringUtils.isBlank(pcode)) {
            return Lists.newArrayList();
        }
        return crmNewHospitalDao.listByPcodeAndHospitalName(pcode, hospitalName, 100);
    }

    @Override
    public boolean isExistByGdAddress(String gdAddress) {
        if (StringUtils.isBlank(gdAddress)) {
            return false;
        }
        Integer result = crmNewHospitalDao.isExistByGdAddress(gdAddress);
        return Objects.nonNull(result) && result > 0;
    }

    @Override
    public int saveOrUpdateLocation(long id, String longitude, String latitude) {
        GdMapHospitalDO gdMapHospitalDO = new GdMapHospitalDO();
        gdMapHospitalDO.setId(id);
        gdMapHospitalDO.setGdLongitude(longitude);
        gdMapHospitalDO.setGdLatitude(latitude);
        gdMapHospitalDO.setGdLocation(longitude + "," + latitude);
        gdMapHospitalDO.setGdLocationExt(latitude + "," + longitude);
        return crmNewHospitalDao.saveOrUpdateLocation(gdMapHospitalDO);
    }

    @Override
    public GdMapHospitalDO selectSpiderCityByHospital(String hospitalName) {
        return crmNewHospitalDao.selectSpiderCityByHospital(hospitalName);
    }

    @Override
    public List<GdMapHospitalDO> getHospitalVPoiCodeByNameAndCityWithLike(String hospitalName, String cityName) {
        List<GdMapHospitalDO> retList = crmNewHospitalDao.getHospitalVPoiCodeByNameAndCityWithLike(hospitalName,cityName);
        return retList;
    }

    @Override
    public List<String> getHospitalVPoiCodeByNameAndCityV2(String hospitalName, String city) {
        return crmNewHospitalDao.getHospitalVPoiCodeByNameAndCityV2(hospitalName,city);
    }

    @Override
    public void updateAcceptPublicByVhospitalCode(String vhospitalCode, int acceptToPublic) {
        if(StringUtils.isBlank(vhospitalCode)){
            return ;
        }
        crmNewHospitalDao.updateAcceptPublicByVhospitalCode(vhospitalCode,acceptToPublic);
    }

    @Override
    public List<GdMapHospitalDO> getHospitalVPoiCodeByNameAndCityV3(String hospitalName, String cityName) {
        String queryCity = cityName;
        if(queryCity.endsWith("市")){
            queryCity = queryCity.substring(0,queryCity.length()-1);
        }
        return crmNewHospitalDao.getHospitalVPoiCodeByNameAndCityV3(hospitalName,queryCity);
    }

    @Override
    public List<GdMapHospitalDO> getHospitalByNamesAndCity(List<String> hospitalNames, String cityName) {
        if (CollectionUtils.isEmpty(hospitalNames) || StringUtils.isBlank(cityName)) {
            return Lists.newArrayList();
        }
        String queryCity = cityName;
        if(queryCity.endsWith("市")){
            queryCity = queryCity.substring(0,queryCity.length()-1);
        }
        return crmNewHospitalDao.getHospitalByNamesAndCity(hospitalNames, queryCity);
    }

    @Override
    public GdMapHospitalDO getGdMapHospitalDOById(Integer id) {
        return crmNewHospitalDao.getGdMapHospitalDOById(id);
    }

    @Override
    public GdMapHospitalDO getGdMapHospitalDOPoiIdAndInputName(String gdPoiId, String inputName) {
        return crmNewHospitalDao.getGdMapHospitalDOPoiIdAndInputName(gdPoiId,inputName);
    }
}
