package com.shuidihuzhu.cf.cfgrowthtoolapi.service.app;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.app.CfAppUpgradeBiz;
import com.shuidihuzhu.cf.dao.app.AppUpgradeDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfAppUpgrade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfAppUpgradeLog;
import com.shuidihuzhu.cf.model.crowdfunding.app.AppUpgradeInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by lixurui on 18/12/02
 */

@Service
public class CfAppUpgradeImpl implements CfAppUpgradeBiz {

    @Autowired
    private AppUpgradeDao appUpgradeDao;

    @Override
    public AppUpgradeInfo queryVersionByPlatform(String appPlatform) {

        return appUpgradeDao.queryVersionByPlatform(appPlatform);
    }

    @Override
    public List<AppUpgradeInfo> listByPlatform(String appPlatform) {
        if (StringUtils.isBlank(appPlatform)) {
            return Lists.newArrayList();
        }
        return appUpgradeDao.listByPlatform(appPlatform);
    }

    @Override
    public List<String> queryLogById(int upgradeId) {
        return appUpgradeDao.queryLogByid(upgradeId);
    }

    @Override
    public void cfAppUpgradeInsert(CfAppUpgrade cfAppUpgrade) {
        this.appUpgradeDao.cfAppUpgradeInsert(cfAppUpgrade);

    }

    @Override
    public void cfAppUpgradeDelete(int id) {
        this.appUpgradeDao.cfAppUpgradeDelete(id);

    }

    @Override
    public void cfAppUpgradeUpdate(CfAppUpgrade cfAppUpgrade) {
        this.appUpgradeDao.cfAppUpgradeUpdate(cfAppUpgrade);

    }

    @Override
    public void cfAppUpgradeLogInsert(CfAppUpgradeLog cfAppUpgradeLog) {
        this.appUpgradeDao.cfAppUpgradeLogInsert(cfAppUpgradeLog);
    }

    @Override
    public Integer cfAppUpgradeIdByVersion(String platform, String version) {
        return this.appUpgradeDao.cfAppUpgradeIdByVersion(platform, version);
    }

    @Override
    public void cfAppUpgradeLogDelete(int id) {
        this.appUpgradeDao.cfAppUpgradeLogDelete(id);

    }

    @Override
    public void cfAppUpgradeLogUpdate(CfAppUpgradeLog cfAppUpgradeLog) {
        this.appUpgradeDao.cfAppUpgradeLogUpdate(cfAppUpgradeLog);
    }

    @Override
    public Integer cfAppUpgradeSelectVersion(String version) {
        return this.appUpgradeDao.cfAppUpgradeSelectVersion(version);

    }

    @Override
    public List<CfAppUpgrade> cfAppUpgradeSelect() {
        return this.appUpgradeDao.cfAppUpgradeSelect();
    }

    @Override
    public List<CfAppUpgradeLog> cfApplogSelect(int id) {
        return this.appUpgradeDao.cfApplogSelect(id);
    }

    @Override
    public void cfApplogDelete(int id) {
        this.appUpgradeDao.cfApplogDelete(id);
    }

}
