package com.shuidihuzhu.cf.cfgrowthtoolapi.service.delegate;

import com.shuidihuzhu.cf.model.crowdfunding.CfHospital;
import com.shuidihuzhu.cf.model.dedicated.VolunteerIdCardModel;
import com.shuidihuzhu.cf.response.OpResult;

public interface IOutFeignServiceDelegate {
    /**
     * 根据医院名称获取
     * @param hosiptalName
     * @return
     * CfHospitalBiz.selectHospitalByName
     */
    CfHospital selectHospitalByName(String hosiptalName);

    /**
     * 根据身份证正面照获取身份证信息
     * @param photoUrl
     * @return
     */
    OpResult<VolunteerIdCardModel> getVolunteerIdCardByPhoto(String photoUrl);

    /**
     * 验证身份证背面照片
     * @param photoUrl
     * @return
     */
    OpResult checkVolunteerIdCardBackPhoto(String photoUrl);
}
