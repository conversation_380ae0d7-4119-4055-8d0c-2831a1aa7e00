package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICommonJudgment;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IFuwuMenuExperiment;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019-09-06
 */
@Slf4j
@Service
@RefreshScope
public class FuwuMenuExperimentImpl implements IFuwuMenuExperiment {

    @Autowired
    private ICommonJudgment commonJudgmentImpl;

    @Override
    public OpResult<Integer> query(long userId) {
        // 线下扫码关注的用户 不进入实验
        if (commonJudgmentImpl.checkIsExceptSubscribe(userId)){
            return OpResult.createSucResult(1);
        }
        // 有在筹案例的用户 不进入实验
        if (commonJudgmentImpl.checkIsHaveNoEndCase(userId)){
            return OpResult.createSucResult(1);
        }
        return OpResult.createSucResult(2);
    }
}
