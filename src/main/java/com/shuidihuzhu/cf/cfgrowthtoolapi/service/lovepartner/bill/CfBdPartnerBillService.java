package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfBdPartnerBillDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfBdPartnerBillSummaryDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerAwardDetail;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.bill.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdLovePartnerBillParam;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * 爱心伙伴兼职账单(CfBdPartnerBill)表服务接口
 *
 * <AUTHOR>
 * @since 2021-09-02 19:49:34
 */
public interface CfBdPartnerBillService {

    CfBdPartnerBillDO queryById(long id);

    void insertBatch(List<CfBdPartnerBillDO> cfBdPartnerBillDOS);

    int updateSalaryByPartnerUniqueCode(CfBdPartnerBillDO cfBdPartnerBill);

    int updateApproveStatus(int approveStatus, int partnerBillId);

    /**
     * 顾问下所有的兼职绩效
     */
    List<CfBdPartnerBillDO> listByUniqueCode(String uniqueCode, int cycleId);

    List<CfBdPartnerBillDO> listByOrgIds(List<Integer> orgIds, int cycleId);

    boolean deleteById(long id);

    List<CfBdPartnerBillDO> listByCycleId(int cycleId);

    CfBdPartnerBillDO getPartnerBill(String partnerUniqueCode, int cycleId);

    List<CfBdPartnerBillDO> listByUniqueCodes(List<String> uniqueCodeList, int cycleId);

    List<CfBdPartnerBillDO> listByPartnerUniqueCodes(List<String> partnerUniqueCodeList, int cycleId);


    /**
     * 查询负责的兼职伙伴总账单
     * @return
     */
    List<BillBaseModel> listMyPartnerSumBill(CrowdfundingVolunteer volunteer);

    /**
     * 查询负责的组织总账单
     * @return
     */
    List<BillBaseModel> listMyOrgSumBill(List<BdCrmOrganizationDO> bdOrgModelList, CrowdfundingVolunteer volunteer);

    /**
     * 查询子账单
     * @param cycleId
     * @param unique
     * @param uniqueType
     * @param volunteerOrgIds
     * @return
     */
    BillOrgSubBillVO listOrgSubBill(long cycleId, String unique, int uniqueType, CrowdfundingVolunteer cfVolunteer, List<Long> volunteerOrgIds);

    /**
     * 查询兼职账单
     * @param cycleId 周期id
     * @param partnerUniqueCode 兼职人员uniqueCode
     * @return
     */
    PartnerBillDetail getPartnerBill(int cycleId, String partnerUniqueCode);

    /**
     * 查询兼职账单案例出勤明细
     * @param cycleId
     * @param partnerUniqueCode
     * @return
     */
    PartnerAwardDetail getPartnerBillDetail(int cycleId, String partnerUniqueCode);

    /**
     * 调整账单
     * @param partnerAwardDetail
     */
    void modifyPartnerBill(PartnerAwardDetail partnerAwardDetail);

    /**
     * 查询账单的数量
     * @param partnerBillParam
     * @return
     */
    int countPartnerBill(BdLovePartnerBillParam partnerBillParam);

    /**
     * 查询兼职账单列表
     * @param partnerBillParam
     * @return
     */
    List<PartnerBillAdmin> listPartnerBill(BdLovePartnerBillParam partnerBillParam);

    List<PartnerApproveRecordModel> getApproveRecord(String partnerUniqueCode, int cycleId);

    //判断当前人员是否能修改账单
    Response<Void> checkCanModifyPartnerBill(List<Long> volunteerOrgIds, long cycleId, List<CfBdPartnerBillDO> partnerBillDOS);

    int deleteByCycleId(long cycleId);

}