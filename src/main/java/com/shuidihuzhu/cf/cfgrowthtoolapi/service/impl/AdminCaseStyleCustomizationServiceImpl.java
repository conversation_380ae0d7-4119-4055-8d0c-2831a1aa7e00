package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ISeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.common.CommonPermissionConfigEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OperateLogSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OperatorLogVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.CommonPermissionConfigDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.CommonPermissionConfigParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.CommonPermissionConfigVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.AdminCaseStyleCustomizationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.common.CommonPermissionConfigService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3  15:56
 */
@Service
public class AdminCaseStyleCustomizationServiceImpl implements AdminCaseStyleCustomizationService {

    @Autowired
    private CommonPermissionConfigService commonPermissionConfigService;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Autowired
    private ISeaAccountServiceDelegate seaAccountServiceDelegate;

    @Autowired
    private IOperateLogService operateLogService;


    @Override
    public Response<Void> saveOrUpdate(CommonPermissionConfigParam commonPermissionConfigParam, long authSaasUserId) {
        if (commonPermissionConfigParam == null) {
            return NewResponseUtil.makeFail("参数不能为空");
        }

        if (commonPermissionConfigParam.getId() > 0) {
            // update
            CommonPermissionConfigDo commonPermissionConfigDo = CommonPermissionConfigDo.of(commonPermissionConfigParam);
            int res = commonPermissionConfigService.update(commonPermissionConfigDo);
            if (res > 0) {
                saveRecord(commonPermissionConfigDo, authSaasUserId);
            }
        } else {
            // insert
            CommonPermissionConfigDo commonPermissionConfigDo = CommonPermissionConfigDo.of(commonPermissionConfigParam);
            int res = commonPermissionConfigService.insert(commonPermissionConfigDo);
            if (res > 0) {
                saveRecord(commonPermissionConfigDo, authSaasUserId);
            }
        }
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<CommonPermissionConfigVo> get(int configType) {
        CommonPermissionConfigVo commonPermissionConfigVo = new CommonPermissionConfigVo();
        CommonPermissionConfigDo commonPermissionConfigDo = commonPermissionConfigService.getByConfigType(configType);
        if (commonPermissionConfigDo == null) {
            commonPermissionConfigVo.setConfigType(configType);
            commonPermissionConfigVo.setPermissionType(CommonPermissionConfigEnums.PermissionType.ORG_ID.getCode());
        } else {
            commonPermissionConfigVo.setId(commonPermissionConfigDo.getId());
            commonPermissionConfigVo.setConfigType(commonPermissionConfigDo.getConfigType());
            commonPermissionConfigVo.setPermissionType(commonPermissionConfigDo.getPermissionType());
        }

        List<String> permissionValueList = Lists.newArrayList();
        if (commonPermissionConfigDo != null && StringUtils.isNotBlank(commonPermissionConfigDo.getPermissionValue())) {
            permissionValueList = Splitter.on(",").splitToList(commonPermissionConfigDo.getPermissionValue());
        }
        commonPermissionConfigVo.setPermissionValueList(permissionValueList);
        return NewResponseUtil.makeSuccess(commonPermissionConfigVo);
    }

    @Override
    public Response<CommonResultModel<OperatorLogVO>> getOperateLog(OperateLogSearchModel searchModel) {
        CommonResultModel<OperatorLogVO> commonResultModel = operateLogService.getOperateLog(searchModel, OperateTypeEnum.getEnumListByAttriButeType(OperateTypeEnum.COMMON_PERMISSION_CONFIG_OPERATING_RECORD.getAttributeType()));
        return NewResponseUtil.makeSuccess(commonResultModel);
    }

    private void saveRecord(CommonPermissionConfigDo commonPermissionConfigDo, long authSaasUserId) {
        String content = "";
        if (StringUtils.isNotEmpty(commonPermissionConfigDo.getPermissionValue())) {
            content = commonPermissionConfigDo.getPermissionValue();
        }

        String name = "";
        AdminUserAccountModel adminUserAccountModel = seaAccountServiceDelegate.getValidUserAccountById(authSaasUserId);
        if (adminUserAccountModel != null && StringUtils.isNotEmpty(adminUserAccountModel.getName())) {
            name = adminUserAccountModel.getName();
        }

        customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(commonPermissionConfigDo.getId()),
                OperateTypeEnum.COMMON_PERMISSION_CONFIG_OPERATING_RECORD.getDesc(), OperateTypeEnum.COMMON_PERMISSION_CONFIG_OPERATING_RECORD,
                content, authSaasUserId, name));
    }
}
