package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.calresult;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.calresult.EncourageActivityUserResultDO;

import java.util.List;

/**
 * 个人计算结果(EncourageActivityUserResult)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-23 15:32:57
 */
public interface EncourageActivityUserResultService {

    List<EncourageActivityUserResultDO> listByActivityIdAndUniqueCodeAndRuleIds(long activityId, String uniqueCode, List<Long> ruleIds);

    List<EncourageActivityUserResultDO> listByUniqueCodeAndTime(String uniqueCode, String startTime, String startFinishTime, String endTime, String finishTime);

    List<EncourageActivityUserResultDO> listByActivityIdAndRuleId(long activityId, long ruleId);

    List<EncourageActivityUserResultDO> listByActivityIdAndRuleIds(long activityId, List<Long> ruleIds);

    List<Long> listAllActivityId(List<String> uniqueCodes, String startTime, String startFinishTime, String endTime, String finishTime);

}
