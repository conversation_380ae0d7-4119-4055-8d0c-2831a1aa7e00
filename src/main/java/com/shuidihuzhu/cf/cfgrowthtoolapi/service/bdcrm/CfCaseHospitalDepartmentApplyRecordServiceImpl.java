package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGrowthtoolApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfCaseHospitalDepartmentApplyRecordDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalAreaBuildingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalBuildingDepartmentDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.bdcrm.IHospitalDepartmentFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfGrowthtoolApproveContentVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.DeleteDepartmentModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.HospitalAreaBuildingParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.HospitalBuildingDepartmentParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfGrowthtoolApproveService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalAreaBuildingService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalBuildingDepartmentService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolWeaponUtil;
import com.shuidihuzhu.cf.dao.bdcrm.CfCaseHospitalDepartmentApplyRecordDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/27  14:23
 */
@Service
@Slf4j
public class CfCaseHospitalDepartmentApplyRecordServiceImpl implements CfCaseHospitalDepartmentApplyRecordService {

    @Autowired
    private CfCaseHospitalDepartmentApplyRecordDao cfCaseHospitalDepartmentApplyRecordDao;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Autowired
    private ICfGrowthtoolApproveService cfGrowthtoolApproveService;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    @Autowired
    private IHospitalDepartmentFacade hospitalDepartmentFacade;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private HospitalAreaBuildingService hospitalAreaBuildingService;

    @Autowired
    private HospitalBuildingDepartmentService hospitalBuildingDepartmentService;

    @Override
    public CfCaseHospitalDepartmentApplyRecordDo get(long id) {
        if (id <= 0) {
            return null;
        }
        return cfCaseHospitalDepartmentApplyRecordDao.get(id);
    }

    @Override
    public Response<Void> updateBuildingPicAudit(CrowdfundingVolunteer volunteer, HospitalAreaBuildingParam areaBuilding) {
        //查找申请人上级
        CrowdfundingVolunteer leaderVolunteer = findApplicantLeader(volunteer);
        if (leaderVolunteer == null) {
            return NewResponseUtil.makeFail("申请人上级为空");
        }

        CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo = new CfCaseHospitalDepartmentApplyRecordDo();
        cfCaseHospitalDepartmentApplyRecordDo.setUniqueCode(volunteer.getUniqueCode());
        cfCaseHospitalDepartmentApplyRecordDo.setMis(volunteer.getMis());
        cfCaseHospitalDepartmentApplyRecordDo.setMisName(volunteer.getVolunteerName());
        cfCaseHospitalDepartmentApplyRecordDo.setLeaderUniqueCode(leaderVolunteer.getUniqueCode());
        cfCaseHospitalDepartmentApplyRecordDo.setType(CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.hospital_building_pic.getStatus());
        cfCaseHospitalDepartmentApplyRecordDo.setReason(areaBuilding.getAdjustmentReason());
        cfCaseHospitalDepartmentApplyRecordDo.setStatus(CfCaseHospitalDepartmentApplyRecordDo.ApplyStatusEnum.applying.getStatus());
        areaBuilding.setUniqueCode(volunteer.getUniqueCode());
        //清除无用数据
        areaBuilding.clear();
        cfCaseHospitalDepartmentApplyRecordDo.setDataContent(JSON.toJSONString(areaBuilding));
        int res = cfCaseHospitalDepartmentApplyRecordDao.insert(cfCaseHospitalDepartmentApplyRecordDo);
        if (res > 0) {
            createApproveAndSendMessage(cfCaseHospitalDepartmentApplyRecordDo, volunteer, leaderVolunteer);
        }
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<Void> deleteDepartmentAudit(CrowdfundingVolunteer volunteer, int departmentId, String adjustmentReason) {
        //查找申请人上级
        CrowdfundingVolunteer leaderVolunteer = findApplicantLeader(volunteer);
        if (leaderVolunteer == null) {
            return NewResponseUtil.makeFail("申请人上级为空");
        }
        CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo = new CfCaseHospitalDepartmentApplyRecordDo();
        cfCaseHospitalDepartmentApplyRecordDo.setUniqueCode(volunteer.getUniqueCode());
        cfCaseHospitalDepartmentApplyRecordDo.setMis(volunteer.getMis());
        cfCaseHospitalDepartmentApplyRecordDo.setMisName(volunteer.getVolunteerName());
        cfCaseHospitalDepartmentApplyRecordDo.setLeaderUniqueCode(leaderVolunteer.getUniqueCode());
        cfCaseHospitalDepartmentApplyRecordDo.setType(CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.delete_department.getStatus());
        cfCaseHospitalDepartmentApplyRecordDo.setReason(adjustmentReason);
        cfCaseHospitalDepartmentApplyRecordDo.setStatus(CfCaseHospitalDepartmentApplyRecordDo.ApplyStatusEnum.applying.getStatus());

        DeleteDepartmentModel deleteDepartmentModel = new DeleteDepartmentModel();
        deleteDepartmentModel.setDepartmentId(departmentId);
        deleteDepartmentModel.setUniqueCode(volunteer.getUniqueCode());
        cfCaseHospitalDepartmentApplyRecordDo.setDataContent(JSON.toJSONString(deleteDepartmentModel));
        int res = cfCaseHospitalDepartmentApplyRecordDao.insert(cfCaseHospitalDepartmentApplyRecordDo);
        if (res > 0) {
            createApproveAndSendMessage(cfCaseHospitalDepartmentApplyRecordDo, volunteer, leaderVolunteer);
        }
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public Response<Void> editDepartmentAudit(CrowdfundingVolunteer volunteer, HospitalBuildingDepartmentParam departmentDO) {
        //查找申请人上级
        CrowdfundingVolunteer leaderVolunteer = findApplicantLeader(volunteer);
        if (leaderVolunteer == null) {
            return NewResponseUtil.makeFail("申请人上级为空");
        }

        CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo = new CfCaseHospitalDepartmentApplyRecordDo();
        cfCaseHospitalDepartmentApplyRecordDo.setUniqueCode(volunteer.getUniqueCode());
        cfCaseHospitalDepartmentApplyRecordDo.setMis(volunteer.getMis());
        cfCaseHospitalDepartmentApplyRecordDo.setMisName(volunteer.getVolunteerName());
        cfCaseHospitalDepartmentApplyRecordDo.setLeaderUniqueCode(leaderVolunteer.getUniqueCode());
        int type = CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.add_department.getStatus();
        if (departmentDO.getId() > 0) {
            type = CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.edit_department.getStatus();
        }
        cfCaseHospitalDepartmentApplyRecordDo.setType(type);
        cfCaseHospitalDepartmentApplyRecordDo.setReason(departmentDO.getAdjustmentReason());
        cfCaseHospitalDepartmentApplyRecordDo.setStatus(CfCaseHospitalDepartmentApplyRecordDo.ApplyStatusEnum.applying.getStatus());
        departmentDO.setUniqueCode(volunteer.getUniqueCode());
        //清除无用数据
        departmentDO.clear();
        cfCaseHospitalDepartmentApplyRecordDo.setDataContent(JSON.toJSONString(departmentDO));
        int res = cfCaseHospitalDepartmentApplyRecordDao.insert(cfCaseHospitalDepartmentApplyRecordDo);
        if (res > 0) {
            createApproveAndSendMessage(cfCaseHospitalDepartmentApplyRecordDo, volunteer, leaderVolunteer);
        }
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public void handleByLeader(CfGrowthtoolApproveDO cfGrowthtoolApproveDO, boolean pass) {
        String approveContent = cfGrowthtoolApproveDO.getApproveContent();
        long id = Long.parseLong(approveContent);
        if (pass) {
            int res = cfCaseHospitalDepartmentApplyRecordDao.update(id, CfCaseHospitalDepartmentApplyRecordDo.ApplyStatusEnum.leader_pass.getStatus(), "");
            if (res > 0) {
                CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo = cfCaseHospitalDepartmentApplyRecordDao.get(id);
                if (cfCaseHospitalDepartmentApplyRecordDo == null) {
                    log.warn("{}审批通过，但是没有找到对应的申请记录", this.getClass().getSimpleName());
                    return;
                }

                if (cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.hospital_building_pic.getStatus()) {
                    HospitalAreaBuildingParam hospitalAreaBuildingParam = JSONObject.parseObject(cfCaseHospitalDepartmentApplyRecordDo.getDataContent(), HospitalAreaBuildingParam.class);
                    if (hospitalAreaBuildingParam == null) {
                        log.warn("{}审批通过，但是没有找到对应的楼宇修改信息", this.getClass().getSimpleName());
                        return;
                    }
                    CrowdfundingVolunteer crowdfundingVolunteer = cfVolunteerService.getByUniqueCode(hospitalAreaBuildingParam.getUniqueCode());
                    if (Objects.isNull(crowdfundingVolunteer)) {
                        log.warn("{}审批通过，但是没有找到对应的志愿者信息", this.getClass().getSimpleName());
                        return;
                    }
                    hospitalDepartmentFacade.editBuilding(crowdfundingVolunteer, hospitalAreaBuildingParam);
                } else if (cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.delete_department.getStatus()) {
                    DeleteDepartmentModel deleteDepartmentModel = JSONObject.parseObject(cfCaseHospitalDepartmentApplyRecordDo.getDataContent(), DeleteDepartmentModel.class);
                    if (deleteDepartmentModel == null) {
                        log.warn("{}审批通过，但是没有找到对应的删除科室信息", this.getClass().getSimpleName());
                        return;
                    }
                    CrowdfundingVolunteer crowdfundingVolunteer = cfVolunteerService.getByUniqueCode(deleteDepartmentModel.getUniqueCode());
                    if (Objects.isNull(crowdfundingVolunteer)) {
                        log.warn("{}审批通过，但是没有找到对应的志愿者信息", this.getClass().getSimpleName());
                        return;
                    }
                    hospitalDepartmentFacade.deleteDepartment(crowdfundingVolunteer, deleteDepartmentModel.getDepartmentId(), false, "");
                } else if (cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.add_department.getStatus() ||
                        cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.edit_department.getStatus()) {
                    HospitalBuildingDepartmentParam hospitalBuildingDepartmentParam = JSONObject.parseObject(cfCaseHospitalDepartmentApplyRecordDo.getDataContent(), HospitalBuildingDepartmentParam.class);
                    if (hospitalBuildingDepartmentParam == null) {
                        log.warn("{}审批通过，但是没有找到对应的科室修改信息", this.getClass().getSimpleName());
                        return;
                    }
                    CrowdfundingVolunteer crowdfundingVolunteer = cfVolunteerService.getByUniqueCode(hospitalBuildingDepartmentParam.getUniqueCode());
                    if (Objects.isNull(crowdfundingVolunteer)) {
                        log.warn("{}审批通过，但是没有找到对应的志愿者信息", this.getClass().getSimpleName());
                        return;
                    }
                    hospitalDepartmentFacade.editDepartment(crowdfundingVolunteer, hospitalBuildingDepartmentParam);
                }
                //发送成功消息
                appPushCrmCaseMsgService.applyCaseHospitalDepartmentLeaderPass(cfCaseHospitalDepartmentApplyRecordDo);
            }
        } else {
            int res = cfCaseHospitalDepartmentApplyRecordDao.update(id, CfCaseHospitalDepartmentApplyRecordDo.ApplyStatusEnum.leader_reject.getStatus(), cfGrowthtoolApproveDO.getRemark());
            if (res > 0) {
                CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo = cfCaseHospitalDepartmentApplyRecordDao.get(id);
                if (cfCaseHospitalDepartmentApplyRecordDo == null) {
                    log.warn("{}审批驳回，但是没有找到对应的申请记录", this.getClass().getSimpleName());
                    return;
                }
                //发送失败消息
                appPushCrmCaseMsgService.applyCaseHospitalDepartmentLeaderReject(cfCaseHospitalDepartmentApplyRecordDo);
            }
        }
    }

    @Override
    public CfGrowthtoolApproveContentVo.CfCaseHospitalDepartmentApplyRecordContent buildCfCaseHospitalDepartmentApplyRecordContent(String approveContent) {
        long id = Long.parseLong(approveContent);
        CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo = cfCaseHospitalDepartmentApplyRecordDao.get(id);
        if (cfCaseHospitalDepartmentApplyRecordDo == null) {
            return null;
        }
        CfGrowthtoolApproveContentVo.CfCaseHospitalDepartmentApplyRecordContent caseHospitalDepartmentApplyRecordContent = new CfGrowthtoolApproveContentVo.CfCaseHospitalDepartmentApplyRecordContent();
        caseHospitalDepartmentApplyRecordContent.setType(cfCaseHospitalDepartmentApplyRecordDo.getType());
        caseHospitalDepartmentApplyRecordContent.setReason(cfCaseHospitalDepartmentApplyRecordDo.getReason());
        if (cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.hospital_building_pic.getStatus()) {
            CfGrowthtoolApproveContentVo.HospitalBuildingPic hospitalBuildingPic = buildHospitalBuildingPic(cfCaseHospitalDepartmentApplyRecordDo.getDataContent());
            caseHospitalDepartmentApplyRecordContent.setHospitalBuildingPic(hospitalBuildingPic);
        } else if (cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.edit_department.getStatus()) {
            CfGrowthtoolApproveContentVo.EditDepartment editDepartment = buildEditDepartment(cfCaseHospitalDepartmentApplyRecordDo.getDataContent());
            caseHospitalDepartmentApplyRecordContent.setEditDepartment(editDepartment);
        } else if (cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.delete_department.getStatus()) {
            CfGrowthtoolApproveContentVo.DeleteDepartment deleteDepartment = buildDeleteDepartment(cfCaseHospitalDepartmentApplyRecordDo.getDataContent());
            caseHospitalDepartmentApplyRecordContent.setDeleteDepartment(deleteDepartment);
        } else if (cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.add_department.getStatus()) {
            CfGrowthtoolApproveContentVo.AddDepartment addDepartment = buildAddDepartment(cfCaseHospitalDepartmentApplyRecordDo.getDataContent());
            caseHospitalDepartmentApplyRecordContent.setAddDepartment(addDepartment);
        }
        return caseHospitalDepartmentApplyRecordContent;
    }

    @Override
    public OpResult<String> checkRepeatability(CfGrowthtoolApproveDO cfGrowthtoolApproveDO) {
        if (cfGrowthtoolApproveDO == null) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR, "审批信息为空");
        }
        String approveContent = cfGrowthtoolApproveDO.getApproveContent();
        long id = Long.parseLong(approveContent);
        CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo = cfCaseHospitalDepartmentApplyRecordDao.get(id);
        if (cfCaseHospitalDepartmentApplyRecordDo == null) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR, "申请信息为空");
        }

        if (cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.hospital_building_pic.getStatus()) {
            HospitalAreaBuildingParam hospitalAreaBuildingParam = JSONObject.parseObject(cfCaseHospitalDepartmentApplyRecordDo.getDataContent(), HospitalAreaBuildingParam.class);
            if (hospitalAreaBuildingParam == null) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
            }

            List<HospitalAreaBuildingDO> buildingDOList = hospitalAreaBuildingService.listByHospitalCodeAndBuildingName(hospitalAreaBuildingParam.getVhospitalCode(), hospitalAreaBuildingParam.getBuildingName());
            if (CollectionUtils.isNotEmpty(buildingDOList)) {
                //当前名称和buildingDOList一样,且id相同可以忽略,当id不一样时需要提示修改
                boolean matchDuplicate = buildingDOList
                        .stream()
                        .anyMatch(item -> !Objects.equals(item.getId(), hospitalAreaBuildingParam.getId()) && Objects.equals(item.getBuildingName(), hospitalAreaBuildingParam.getBuildingName()));
                if (matchDuplicate) {
                    return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR, "楼名称存在和其它楼重复，请操作驳回");
                }
            }
        } else if (cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.add_department.getStatus() ||
                cfCaseHospitalDepartmentApplyRecordDo.getType() == CfCaseHospitalDepartmentApplyRecordDo.TypeStatusEnum.edit_department.getStatus()) {
            HospitalBuildingDepartmentParam hospitalBuildingDepartmentParam = JSONObject.parseObject(cfCaseHospitalDepartmentApplyRecordDo.getDataContent(), HospitalBuildingDepartmentParam.class);
            if (hospitalBuildingDepartmentParam == null) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
            }

            String buildingFloor = hospitalBuildingDepartmentParam.getBuildingFloor();
            if (StringUtils.isNotBlank(buildingFloor)
                    && StringUtils.isAsciiPrintable(buildingFloor)
                    && !buildingFloor.endsWith("F")
                    && !buildingFloor.endsWith("f")) {
                hospitalBuildingDepartmentParam.setBuildingFloor(buildingFloor + "层");
            }

            //去重校验
            List<HospitalBuildingDepartmentDO> departmentDOList = hospitalBuildingDepartmentService.listByBuildingIds(Lists.newArrayList(hospitalBuildingDepartmentParam.getBuildingId()));
            boolean duplicateDepartment = departmentDOList.stream().anyMatch(item -> (Objects.equals(item.getBuildingFloor(), hospitalBuildingDepartmentParam.getBuildingFloor())
                    && Objects.equals(item.getBuildingFloorArea(), hospitalBuildingDepartmentParam.getBuildingFloorArea())
                    && Objects.equals(item.getBuildingDepartment(), hospitalBuildingDepartmentParam.getBuildingDepartment()))
                    && item.getId() != hospitalBuildingDepartmentParam.getId());
            if (duplicateDepartment) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR, "楼层备注+楼层区域+科室名称存在和其它科室重复，请操作驳回");
            }
        }
        return OpResult.createSucResult();
    }

    private CrowdfundingVolunteer findApplicantLeader(CrowdfundingVolunteer volunteer) {
        return memberInfoService.listApplyLeaderWithDefaultExplicit(volunteer.getUniqueCode(), GrowthtoolWeaponUtil.getContinueApproveRoleEnum(volunteer.getLevel()));
    }

    private void createApproveAndSendMessage(CfCaseHospitalDepartmentApplyRecordDo cfCaseHospitalDepartmentApplyRecordDo, CrowdfundingVolunteer volunteer, CrowdfundingVolunteer leaderVolunteer) {
        //插入对应的申请记录
        cfGrowthtoolApproveService.saveApproveContent(String.valueOf(cfCaseHospitalDepartmentApplyRecordDo.getId()), CfGrowthtoolApproveDO.ApproveTypeEnum.CASE_HOSPITAL_DEPARTMENT.getType(), volunteer, leaderVolunteer);
        //给上级发送消息
        appPushCrmCaseMsgService.applyCaseHospitalDepartmentLeader(cfCaseHospitalDepartmentApplyRecordDo, leaderVolunteer);
    }

    private CfGrowthtoolApproveContentVo.HospitalBuildingPic buildHospitalBuildingPic(String dataContent) {
        if (StringUtils.isBlank(dataContent)) {
            return null;
        }
        HospitalAreaBuildingParam hospitalAreaBuildingParam = JSONObject.parseObject(dataContent, HospitalAreaBuildingParam.class);
        if (hospitalAreaBuildingParam == null) {
            return null;
        }
        CfGrowthtoolApproveContentVo.HospitalBuildingPic hospitalBuildingPic = new CfGrowthtoolApproveContentVo.HospitalBuildingPic();

        HospitalAreaBuildingDO hospitalAreaBuildingDO = hospitalAreaBuildingService.queryById(hospitalAreaBuildingParam.getId());
        if (hospitalAreaBuildingDO != null) {
            hospitalBuildingPic.setHospitalName(hospitalAreaBuildingDO.getHospitalName());
            hospitalBuildingPic.setNowHospitalBuildingPic(hospitalAreaBuildingDO.getBuildingPic());
            hospitalBuildingPic.setDiffFloorArea(StringUtils.isNotBlank(hospitalAreaBuildingDO.getBuildingFloorArea()));
            hospitalBuildingPic.setBuildingFloorArea(hospitalAreaBuildingDO.getBuildingFloorArea());
        }
        hospitalBuildingPic.setUpdateHospitalBuildingPic(hospitalAreaBuildingParam.getBuildingPic());
        return hospitalBuildingPic;
    }

    private CfGrowthtoolApproveContentVo.EditDepartment buildEditDepartment(String dataContent) {
        if (StringUtils.isBlank(dataContent)) {
            return null;
        }
        HospitalBuildingDepartmentParam hospitalBuildingDepartmentParam = JSONObject.parseObject(dataContent, HospitalBuildingDepartmentParam.class);
        if (hospitalBuildingDepartmentParam == null) {
            return null;
        }
        CfGrowthtoolApproveContentVo.EditDepartment editDepartment = new CfGrowthtoolApproveContentVo.EditDepartment();

        HospitalAreaBuildingDO hospitalAreaBuildingDO = hospitalAreaBuildingService.queryById(hospitalBuildingDepartmentParam.getBuildingId());
        if (hospitalAreaBuildingDO != null) {
            editDepartment.setBuildingName(hospitalAreaBuildingDO.getBuildingName());
        }
        HospitalBuildingDepartmentDO hospitalBuildingDepartmentDO = hospitalBuildingDepartmentService.queryById(hospitalBuildingDepartmentParam.getId());
        if (hospitalBuildingDepartmentDO != null) {
            editDepartment.setHospitalName(hospitalBuildingDepartmentDO.getHospitalName());
            editDepartment.setNowDepartmentName(hospitalBuildingDepartmentDO.getBuildingDepartment());
            editDepartment.setNowFloorRemark(hospitalBuildingDepartmentDO.getBuildingFloor());
            editDepartment.setNowBuildingFloorArea(hospitalBuildingDepartmentDO.getBuildingFloorArea());
            HospitalAreaBuildingDO areaBuildingDO = hospitalAreaBuildingService.queryById(hospitalBuildingDepartmentDO.getBuildingId());
            if (areaBuildingDO != null) {
                editDepartment.setNowBuildingName(areaBuildingDO.getBuildingName());
            }
        }
        editDepartment.setUpdateDepartmentName(hospitalBuildingDepartmentParam.getBuildingDepartment());
        editDepartment.setFloorRemark(hospitalBuildingDepartmentParam.getBuildingFloor());
        editDepartment.setBuildingFloorArea(hospitalBuildingDepartmentParam.getBuildingFloorArea());
        return editDepartment;
    }

    private CfGrowthtoolApproveContentVo.DeleteDepartment buildDeleteDepartment(String dataContent) {
        if (StringUtils.isBlank(dataContent)) {
            return null;
        }
        DeleteDepartmentModel deleteDepartmentModel = JSONObject.parseObject(dataContent, DeleteDepartmentModel.class);
        if (deleteDepartmentModel == null) {
            return null;
        }
        CfGrowthtoolApproveContentVo.DeleteDepartment deleteDepartment = new CfGrowthtoolApproveContentVo.DeleteDepartment();

        HospitalBuildingDepartmentDO hospitalBuildingDepartmentDO = hospitalBuildingDepartmentService.getById(deleteDepartmentModel.getDepartmentId());
        if (hospitalBuildingDepartmentDO != null) {
            deleteDepartment.setHospitalName(hospitalBuildingDepartmentDO.getHospitalName());
            deleteDepartment.setDepartmentName(hospitalBuildingDepartmentDO.getBuildingDepartment());
            deleteDepartment.setFloorRemark(hospitalBuildingDepartmentDO.getBuildingFloor());
            deleteDepartment.setBuildingFloorArea(hospitalBuildingDepartmentDO.getBuildingFloorArea());
            HospitalAreaBuildingDO hospitalAreaBuildingDO = hospitalAreaBuildingService.getById(hospitalBuildingDepartmentDO.getBuildingId());
            if (hospitalAreaBuildingDO != null) {
                deleteDepartment.setBuildingName(hospitalAreaBuildingDO.getBuildingName());
            }
        }
        return deleteDepartment;
    }

    private CfGrowthtoolApproveContentVo.AddDepartment buildAddDepartment(String dataContent) {
        if (StringUtils.isBlank(dataContent)) {
            return null;
        }
        HospitalBuildingDepartmentParam hospitalBuildingDepartmentParam = JSONObject.parseObject(dataContent, HospitalBuildingDepartmentParam.class);
        if (hospitalBuildingDepartmentParam == null) {
            return null;
        }
        CfGrowthtoolApproveContentVo.AddDepartment addDepartment = new CfGrowthtoolApproveContentVo.AddDepartment();

        HospitalAreaBuildingDO hospitalAreaBuildingDO = hospitalAreaBuildingService.queryById(hospitalBuildingDepartmentParam.getBuildingId());
        if (hospitalAreaBuildingDO != null) {
            addDepartment.setHospitalName(hospitalAreaBuildingDO.getHospitalName());
            addDepartment.setBuildingName(hospitalAreaBuildingDO.getBuildingName());
        }
        addDepartment.setDepartmentName(hospitalBuildingDepartmentParam.getBuildingDepartment());
        addDepartment.setFloorRemark(hospitalBuildingDepartmentParam.getBuildingFloor());
        addDepartment.setBuildingFloorArea(hospitalBuildingDepartmentParam.getBuildingFloorArea());
        addDepartment.setBedNum(hospitalBuildingDepartmentParam.getBednum());
        return addDepartment;
    }

}
