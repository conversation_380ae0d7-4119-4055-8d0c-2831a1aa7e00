package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill.CfBdPartnerBillSummaryService;
import com.shuidihuzhu.cf.dao.lovepartner.CfBdPartnerBillSummaryDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfBdPartnerBillSummaryDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 爱心伙伴兼职-兼职人员总账单(CfBdPartnerBillSummary)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-02 19:49:51
 */
@Service
public class CfBdPartnerBillSummaryServiceImpl implements CfBdPartnerBillSummaryService {

    @Resource
    private CfBdPartnerBillSummaryDao cfBdPartnerBillSummaryDao;


    @Override
    public int insertOrUpdate(CfBdPartnerBillSummaryDO cfBdPartnerBillSummary) {
        //如果顾问账单已经生成了就不要重复生成
        CfBdPartnerBillSummaryDO summaryDO = cfBdPartnerBillSummaryDao.queryPartnerSumBill(cfBdPartnerBillSummary.getCycleId(), cfBdPartnerBillSummary.getUniqueCode());
        if (summaryDO != null) {
            cfBdPartnerBillSummary.setId(summaryDO.getId());
            return cfBdPartnerBillSummaryDao.update(cfBdPartnerBillSummary);
        }
        return cfBdPartnerBillSummaryDao.insert(cfBdPartnerBillSummary);
    }

    @Override
    public int updateApproveStatus(List<String> uniqueCodeList, long cycleId, int approveStatus) {
        if (CollectionUtils.isEmpty(uniqueCodeList)) {
            return 0;
        }
        return cfBdPartnerBillSummaryDao.updateApproveStatus(uniqueCodeList, cycleId, approveStatus);
    }

    @Override
    public List<CfBdPartnerBillSummaryDO> listByUniqueCodes(List<String> uniqueCodes, long cycleId) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Lists.newArrayList();
        }
        return cfBdPartnerBillSummaryDao.listByUniqueCodes(uniqueCodes, cycleId);
    }

    @Override
    public int deleteByCycleId(long cycleId) {
        return cfBdPartnerBillSummaryDao.deleteByCycleId(cycleId);
    }

}