package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiManulDeductionCaseDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfKpiManulDeductionVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiManulDeductionService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiManulDeductionCaseDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
@Service
@Slf4j
public class CfKpiManulDeductionServiceImpl implements ICfKpiManulDeductionService {

    @Autowired
    private CfKpiManulDeductionCaseDao cfKpiManulDeductionCaseDao;

    @Override
    public void addBatch(List<CfKpiManulDeductionCaseDO> cfKpiManulDeductionCaseDOList) {
        if (CollectionUtils.isEmpty(cfKpiManulDeductionCaseDOList)) {
            log.info("插入集合为空");
            return;
        }
        cfKpiManulDeductionCaseDOList = cfKpiManulDeductionCaseDOList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        Lists.partition(cfKpiManulDeductionCaseDOList, 100)
                .forEach(item -> cfKpiManulDeductionCaseDao.addBatch(item));
    }

    @Override
    public OpResult<List<CfKpiManulDeductionVO>> queryCaseDeductionAmountDetail(String monthkey, Long caseId) {
        if (StringUtils.isEmpty(monthkey)){
            monthkey = DateUtil.getYYYYMMStr(DateUtil.getCurrentDate());
        }
        List<CfKpiManulDeductionVO> list = cfKpiManulDeductionCaseDao.queryCaseDeductionAmountDetail(monthkey,caseId);
        return OpResult.createSucResult(list);
    }

    @Override
    public List<CfKpiManulDeductionCaseDO> listDeductCaseId(String monthKey) {
        return cfKpiManulDeductionCaseDao.listDeductCaseId(monthKey);
    }

    @Override
    public int countOperateDetail(Date startTime) {
        if (startTime == null) {
            return 0;
        }
        return cfKpiManulDeductionCaseDao.countOperateDetail(startTime);
    }

    @Override
    public List<CfKpiManulDeductionCaseDO> pageOperateDetail(Date startTime, int offset, int limit) {
        if (startTime == null) {
            return Lists.newArrayList();
        }
        return cfKpiManulDeductionCaseDao.pageOperateDetail(startTime, offset, limit);
    }
}
