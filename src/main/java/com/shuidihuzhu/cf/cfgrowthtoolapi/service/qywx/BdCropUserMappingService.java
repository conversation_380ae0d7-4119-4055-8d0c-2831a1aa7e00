package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdCropOrgMappingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdCropUserMappingDO;

import java.util.List;

/**
 * 主体userId映射(BdCropUserMapping)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-28 11:33:18
 */
public interface BdCropUserMappingService {

    BdCropUserMappingDO queryById(long id);

    int insert(BdCropUserMappingDO bdCropUserMapping);

    boolean deleteById(long id);

    BdCropUserMappingDO queryByUniqueCode(String uniqueCode, int cropId);

    List<BdCropUserMappingDO> listByUniqueCodeList(List<String> uniqueCodes, int cropId);

}
