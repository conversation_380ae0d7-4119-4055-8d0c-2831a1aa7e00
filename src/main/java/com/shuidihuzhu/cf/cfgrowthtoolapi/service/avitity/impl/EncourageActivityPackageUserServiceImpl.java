package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.EncourageActivityPackageUserDO;
import com.shuidihuzhu.cf.dao.avtivity.EncourageActivityPackageUserDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.EncourageActivityPackageUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 激励活动-预圈选人群包(EncourageActivityPackageUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-23 15:32:57
 */
@Slf4j
@Service("encourageActivityPackageUserService")
public class EncourageActivityPackageUserServiceImpl implements EncourageActivityPackageUserService {
   
    @Resource
    private EncourageActivityPackageUserDao encourageActivityPackageUserDao;

    @Override
    public void batchHandle(long activityId, List<EncourageActivityPackageUserDO> packageUserDOList) {
        List<EncourageActivityPackageUserDO> dataFromDBList = encourageActivityPackageUserDao.listByActivityId(activityId);
        Map<String, EncourageActivityPackageUserDO> packageUserDOMap = packageUserDOList.stream().collect(Collectors.toMap(EncourageActivityPackageUserDO::getUniqueCode, item -> item, (k1, k2) -> k1));

        Map<String, EncourageActivityPackageUserDO> dbMap = dataFromDBList.stream().collect(Collectors.toMap(EncourageActivityPackageUserDO::getUniqueCode, item -> item, (k1, k2) -> k1));

        List<EncourageActivityPackageUserDO> addDataList = Lists.newArrayList();
        List<Long> deleteIds = Lists.newArrayList();

        Sets.difference(dbMap.keySet(), packageUserDOMap.keySet()).forEach(item -> {
            EncourageActivityPackageUserDO packageUserDO = dbMap.get(item);
            deleteIds.add(packageUserDO.getId());
        });

        Sets.difference(packageUserDOMap.keySet(), dbMap.keySet()).forEach(item -> {
            EncourageActivityPackageUserDO packageUserDO = packageUserDOMap.get(item);
            addDataList.add(packageUserDO);
        });
        log.info("batchHandle addDataList:{},deleteList:{}", addDataList.size(), deleteIds.size());
        if (!deleteIds.isEmpty()) {
            encourageActivityPackageUserDao.deleteByIds(deleteIds);
        }
        if (!addDataList.isEmpty()) {
            encourageActivityPackageUserDao.batchInsert(addDataList);
        }
    }

    @Override
    public int update(EncourageActivityPackageUserDO encourageActivityPackageUser) {
        return encourageActivityPackageUserDao.update(encourageActivityPackageUser);
    }

}
