package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfCrmVisitingHospitalVO;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-04-23 16:17
 */
public interface ICfCrmCampaignMapService {
	long getCountByDateTime(String dateTime);

	int insertCfCrmBdLocationConditionDO(CfCrmBdLocationConditionDO cfCrmBdLocationConditionDO);

	int batchInsert(List<CfCrmBdLocationConditionDO> modelList);

	int updateCfCrmBdLocationConditionDO(CfCrmBdLocationConditionDO model);

	int insertCfCrmGpsDO(CfCrmGpsDO cfCrmGpsDO);

	int updateIsShow(String dateTime,
					 String volunteerUniqueCode);

	CfCrmBdLocationConditionDO getCfCrmBdLocationConditionDO(String dateTime, String uniqueCode);

	List<CfCrmGpsDO> getCfCrmGpsDO(String dateTime, long currentId);

	int batchUpdateLocation(List<CfCrmGpsDO> needUpdateList);

	int updateCfCrmGpsStatus(String dateTime, String mis, int status);

	List<CfCrmGpsVo> getCfCrmGpsListByUniqueCodeList(List<String> uniqueCodeList, String currentDateStr);

	long getLocationConditionCount(List<String> uniqueCodeList,int type, String currentDateStr);

	List<CfCrmLocationConfitionVo> getLocationConditionList(List<String> uniqueCodeList,int type, String currentDateStr, int pageNo, int pageSize);

	List<CfCrmGpsVo> getBdGps(String volunteerUniqueCode,String dateTime, boolean needCalcStayTime);

	List<CfCrmGpsVo> getBdGpsByUniqueCodeList(List<String> uniqueCodeList,String dateTime, boolean needCalcStayTime);

	List<CfCrmBdGpsSimpleModel> listBdGpsByVHospitalCode(String hospitalName, String dateTime, String startTime, String endTime, boolean needCalcStayTime);

	List<CfCrmBdGpsSimpleModel> listBdGpsByVHospitalCodes(List<String> hospitalNameList, String dateTime, String startTime, String endTime, boolean needCalcStayTime);

	List<CfCrmBdGpsSimpleModel> listBdGpsByVHospitalCodeWithUniqueCodeList(String hospitalName, List<String> uniqueCodeList,String dateTime, String startTime, String endTime, boolean needCalcStayTime);

	List<CfCrmBdGpsSimpleModel> listBdGpsByVHospitalNameList(List<String> hospitalNameList, String dateTime, String startTime, String endTime, boolean needCalcStayTime);

	List<CfCrmGpsDO> listGpsByUploadWays(String volunteerUniqueCode,
										 List<String> dateTimeList,
										 List<Integer> uploadWaysList);

	List<String> listDateTimeByUploadWaysWithDateTimeList(String volunteerUniqueCode,
										   				  List<String> dateTimeList,
														  int uploadWays);

	int insertfCrmVisitingHospital(CfCrmVisitingHospitalDO cfCrmVisitingHospitalDO);

	int updateCrmVisitingHospital(CfCrmVisitingHospitalDO cfCrmVisitingHospitalDO);

	CfCrmVisitingHospitalDO getLatelyModel(String dateTime,
										   String volunteerUniqueCode,
										   String vhospitalCode);

	List<CfCrmVisitingHospitalVO> visitingHospitals(String volunteerUniqueCode, String currentDateStr);

	List<CfCrmVisitingHospitalVO> batchVisitingHospitals(List<String> volunteerUniqueCodes, String currentDateStr);

    int saveCfOrgHospitalRelation(String vhospitalCode, long orgId);

	int updateCfOrgHospitalRelationIsDelete(String vhospitalCode, List<Long> orgIds);

	int updateCfHospitalInterviewCaseIsTarget(String vhospitalCode,
											  List<Long> orgIds,
											  int isTarget);

	/**
	 * 获取时间范围内走访医院的hospitalCode,根据名称去重
	 * @param volunteerUniqueCode
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	List<CfCrmVisitingHospitalDO> listVisitHospitalCode(String volunteerUniqueCode, String startDate, String endDate);

	List<CfCrmGpsVo> listBdGpsByDateTimeWithCreateTime(String uniqueCode, String dateTime, String startTime, String endTime, boolean needCalcStayTime);

	List<CfCrmGpsVo> listBdGpsByUniqueCodeList(List<String> uniqueCodeList, String dateTime, String startTime, String endTime, boolean needCalcStayTime);

	List<CfCrmGpsVo> listLastGpsInfo(String startTime, List<Integer> roles, int nums, int size);

}
