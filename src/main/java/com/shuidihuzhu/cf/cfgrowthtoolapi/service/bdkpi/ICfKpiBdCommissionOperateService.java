package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiBdCommissionOperateDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfBdKpiEnums;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
public interface ICfKpiBdCommissionOperateService {

    /**
     * 批量插入
     */
    void addBatch(List<CfKpiBdCommissionOperateDO> kpiBdCommissionOperateDOList);

    OpResult<List<CfKpiBdCommissionOperateDO>> queryBdCommissionDetail(String monthkey, String uniqueCode, CfBdKpiEnums.SpecialTypeEnum specialTypeEnum);

    List<CfKpiBdCommissionOperateDO> listAllSpecialCommission(String monthKey);

    int countOperateDetail(Date startTime, int bdSpecialType);

    List<CfKpiBdCommissionOperateDO> pageOperateDetail(Date startTime, int bdSpecialType, int offset, int limit);
}
