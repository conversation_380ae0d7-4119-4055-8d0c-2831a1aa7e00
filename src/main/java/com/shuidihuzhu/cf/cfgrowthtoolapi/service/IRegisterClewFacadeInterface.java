package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.model.clewtrack.ClewReceiveModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.msg.model.SmsRecord;

/**
 * 统一处理与筹款登记相关交互的面板
 */
public interface IRegisterClewFacadeInterface {
    /**
     * 登记成功后，延时30分钟发送短信提醒
     * @param smsRecord
     */
    void sendRegisterSuccessDelayMessage(SmsRecord smsRecord);

    /**
     * 登记成功之后，发送登记线索消息给线索系统(cf-clewtrack-api)
     * @return
     */
    OpResult<ClewReceiveModel> sendNewClewMsg(ClewReceiveModel clewReceiveModel);
}
