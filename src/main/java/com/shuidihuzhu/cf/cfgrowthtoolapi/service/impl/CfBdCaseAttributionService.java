package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfClewtrackDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrowdfundingVolunteerCreateCaseRecordExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCaseAttributionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseAttributionService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCaseAttributionDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.client.cf.growthtool.model.CfBdCaseAttributionModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-04-02 17:56
 */
@Service
@Slf4j
public class CfBdCaseAttributionService implements ICfBdCaseAttributionService {
	@Autowired
	private CfBdCaseAttributionDao cfBdCaseArributionDao;
	@Autowired
	private CrowdfundingVolunteerCreateCaseRecordBizImpl crowdfundingVolunteerCreateCaseRecordBiz;
	@Autowired
	private ICfClewtrackDelegate cfClewtrackDelegateImpl;
	@Autowired
	private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;
	@Autowired
	private IMqProducerService mqProducerServiceImpl;

	@Override
	public void saveCfBdCaseAttribution(CfInfoExt cfInfoExt, ImmutablePair<String, ClewCrowdfundingReportRelation> bindPreposeMaterialResult, long userId) {
		if (cfInfoExt == null || StringUtils.isBlank(cfInfoExt.getVolunteerUniqueCode())) {
			return;
		}
		CfBdCaseAttributionDO cfBdCaseAttributionDO = new CfBdCaseAttributionDO();
		UserInfoModel userInfoModel = crowdFundingFeignDelegate.getUserInfoByUserId(userId);
		if (userInfoModel != null) {
			cfBdCaseAttributionDO.setCaseEncryptPhone(userInfoModel.getCryptoMobile());
		}
		CrowdfundingVolunteerCreateCaseRecordExtDO caseRecordExtDO = crowdfundingVolunteerCreateCaseRecordBiz.getByUserIdWithVolunteerUnique(userId,
				cfInfoExt.getVolunteerUniqueCode());
		if (caseRecordExtDO == null && userInfoModel != null && StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
			caseRecordExtDO = crowdfundingVolunteerCreateCaseRecordBiz.getByEncryptPhoneWithVolunteerUnique(userInfoModel.getCryptoMobile(), cfInfoExt.getVolunteerUniqueCode());
		}
		cfBdCaseAttributionDO.setCaseCreateTime(cfInfoExt.getDateCreated());
		cfBdCaseAttributionDO.setCaseId(Long.valueOf(cfInfoExt.getCaseId()));
		cfBdCaseAttributionDO.setInfoUuid(cfInfoExt.getInfoUuid());
		cfBdCaseAttributionDO.setUniqueCode(cfInfoExt.getVolunteerUniqueCode());
		if (caseRecordExtDO != null) {
			cfBdCaseAttributionDO.setAttributionType(caseRecordExtDO.getAttributionType());
		}
		if (bindPreposeMaterialResult != null) {
			cfBdCaseAttributionDO.setClewId(bindPreposeMaterialResult.getRight().getClewId());
			cfBdCaseAttributionDO.setPreposeMaterialId(bindPreposeMaterialResult.right.getPreposeMaterialId());
			cfBdCaseAttributionDO.setEncryptPhone(ShuidiCipherUtils.encrypt(bindPreposeMaterialResult.getLeft()));

			//报备手机号和当前发起的手机号不一致  认为是换号发起
			if (userInfoModel!=null && !bindPreposeMaterialResult.getLeft().equals(ShuidiCipherUtils.decrypt(userInfoModel.getCryptoMobile()))){
				cfBdCaseAttributionDO.setAttributionType(CfBdCaseAttributionDO.AttributionTypeEnum.HUANHAO_FAQI.getType());
				//发送换号发起mq
				CfBdCaseAttributionModel bdCaseAttributionModel = JSONObject.parseObject(JSONObject.toJSONString(cfBdCaseAttributionDO),CfBdCaseAttributionModel.class);
				mqProducerServiceImpl.sendOfflineHuanhaoFaqiNoticeClewMsg(bdCaseAttributionModel);
			}
		}else {
			CfClewBaseInfoDO cfClewBaseInfoDO = cfClewtrackDelegateImpl.getCfClewBaseInfoDOByUniqueCodeWithInfoUuid(cfInfoExt.getVolunteerUniqueCode(), Long.valueOf(cfInfoExt.getCaseId()));
			if (cfClewBaseInfoDO!=null){
				cfBdCaseAttributionDO.setClewId(cfClewBaseInfoDO.getId());
				cfBdCaseAttributionDO.setEncryptPhone(cfClewBaseInfoDO.getEncryptPhone());
			}
		}
		// 插入前判断是否已有值
		CfBdCaseAttributionDO cfBdCaseAttributionDOByCaseId = this.getCfBdCaseAttributionDOByCaseId(Long.valueOf(cfInfoExt.getCaseId()));
		if (cfBdCaseAttributionDOByCaseId!=null){
			log.info(this.getClass().getName()+" saveCfBdCaseAttribution 案例已处理caseId:{}",cfInfoExt.getCaseId());
			return;
		}
		cfBdCaseArributionDao.insert(cfBdCaseAttributionDO);
	}
	@Override
	@Async
	public void saveCfBdCaseAttribution(ImmutablePair<String, ClewCrowdfundingReportRelation> bindPreposeMaterialResult){
		try {
			ClewCrowdfundingReportRelation reportRelation = bindPreposeMaterialResult.getRight();
			if (reportRelation.getInfoId() == null || reportRelation.getInfoId() == 0L) {
				return;
			}
			CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegate.getCaseInfoById(reportRelation.getInfoId());
			if (crowdfundingInfo == null) {
				log.warn(this.getClass().getName() + " getCaseInfoById result is null  param:{} ", reportRelation.getInfoId());
				return;
			}
			CfInfoExt cfInfoExt = crowdFundingFeignDelegate.getCfInfoExtByuuid(crowdfundingInfo.getInfoId());
			this.saveCfBdCaseAttribution(cfInfoExt, bindPreposeMaterialResult, crowdfundingInfo.getUserId());
		} catch (Exception e) {
			log.error(this.getClass().getName() + " saveCfBdCaseAttribution() param:{} err:", JSON.toJSONString(bindPreposeMaterialResult), e);
		}

	}
	@Override
	public CfBdCaseAttributionDO getCfBdCaseAttributionDOByCaseId(long caseId){
		return cfBdCaseArributionDao.getCfBdCaseAttributionDOByCaseId(caseId);
	}

	@Override
	public List<CfBdCaseAttributionDO> listByCaseIds(List<Long> caseIds) {
		if (CollectionUtils.isEmpty(caseIds)) {
			return Lists.newArrayList();
		}
		return cfBdCaseArributionDao.listByCaseIds(caseIds);
	}
}
