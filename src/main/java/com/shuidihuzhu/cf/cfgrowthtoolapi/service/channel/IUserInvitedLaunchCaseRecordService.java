package com.shuidihuzhu.cf.cfgrowthtoolapi.service.channel;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfUserInvitedLaunchCaseRecordDO;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;

import java.util.List;

/**
 * 以案例id、userid 为维度入库 发起渠道详细信息
 * @author: wanghui
 * @create: 2019/4/29 12:01 PM
 */
public interface IUserInvitedLaunchCaseRecordService {
    /**
     * 保存用户发起案例时 是否被邀请的信息
     * @param cfUserInvitedLaunchCaseRecordDO
     */
    Integer saveUserInvitedLaunchCaseRecord(CfUserInvitedLaunchCaseRecordDO cfUserInvitedLaunchCaseRecordDO);

    Integer updateChannelByInfoIdWithUserId(CfUserInvitedLaunchCaseRecordDO cfUserInvitedLaunchCaseRecordDO);

    /**
     * 根据案例id  获得 用户邀请发起的记录
     * @param infoIds
     * @return
     */
    List<CfUserInvitedLaunchCaseRecordModel> getCfUserInvitedLaunchCaseRecordByInfoIds(List<Long> infoIds);

    List<CfUserInvitedLaunchCaseRecordDO> getCfUserInvitedLaunchCaseRecordDOByInfoIds(List<Long> infoIds);


    CfUserInvitedLaunchCaseRecordModel getCfUserInvitedLaunchCaseRecordModel(Long infoId, Long userId);
}
