package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfGwReplaceInputAppealApproveLogDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfGwReplaceInputAppealDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdQCAppealSimpleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfLeaderQCAppealSimpleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfQCAppealListSearch;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfGwReplaceInputAppealService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrgConvertService;
import com.shuidihuzhu.cf.dao.bdcrm.CfGwReplaceInputAppealApproveLogDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfGwReplaceInputAppealDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestFeedbackModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2020/11/12 8:00 下午
 */
@Service
@Slf4j
public class CfGwReplaceInputAppealService implements ICfGwReplaceInputAppealService {

    @Autowired
    private CfGwReplaceInputAppealApproveLogDao appealApproveLogDao;
    @Autowired
    private CfGwReplaceInputAppealDao appealDao;

    @Autowired
    private ICrmOrgConvertService crmOrgConvertService;

    @Override
    public int saveAppealApproveLog(Long appealId,String operatorName,String operateDesc,String reason){
        CfGwReplaceInputAppealApproveLogDO appealApproveLogDO = new CfGwReplaceInputAppealApproveLogDO(appealId,
                operatorName,
                operateDesc,
                reason,
                new Date());
        return appealApproveLogDao.insert(appealApproveLogDO);
    }
    @Override
    public int saveAppealApproveLog(Long appealId,String operatorName,String operateDesc,String reason,Date operateTime){
        CfGwReplaceInputAppealApproveLogDO appealApproveLogDO = new CfGwReplaceInputAppealApproveLogDO(appealId,
                operatorName,
                operateDesc,
                reason,
                operateTime);
        return appealApproveLogDao.insert(appealApproveLogDO);
    }

    @Override
    public OpResult saveAppeal(CfGwReplaceInputAppealDO appealDO){
        CfGwReplaceInputAppealDO latelyAppealByWorkOrderId = appealDao.getAppealByWorkOrderId(appealDO.getWorkOrderId());
        if (latelyAppealByWorkOrderId!=null){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.EXIST);
        }
        appealDao.insert(appealDO);
        return OpResult.createSucResult();
    }

    @Override
    public CfGwReplaceInputAppealDO getLatelyAppealByReportId(Long reportId) {
        return appealDao.getLatelyAppealByReportId(reportId);
    }

    @Override
    public List<CfGwReplaceInputQualityTestFeedbackModel> getCfGwReplaceInputAppealResult(List<Long> workOrderIds) {
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return Lists.newArrayList();
        }
        return workOrderIds.parallelStream().map(workOrderId -> {
            CfGwReplaceInputAppealDO appealDO = appealDao.getAppealByWorkOrderId(workOrderId);
            if (appealDO != null) {
                List<BdCrmVolunteerOrgnizationSimpleModel> gwOrgList = crmOrgConvertService.getByUniqueCodeList(Lists.newArrayList(appealDO.getUniqueCode()));
                appealDO.setGwName(CollectionUtils.isNotEmpty(gwOrgList) ? gwOrgList.get(0).getOrgName() + " " + appealDO.getGwName() : appealDO.getGwName());
                if (StringUtils.isNotBlank(appealDO.getLeaderUniqueCode())) {
                    List<BdCrmVolunteerOrgnizationSimpleModel> leaderOrgList = crmOrgConvertService.getByUniqueCodeList(Lists.newArrayList(appealDO.getLeaderUniqueCode()));
                    appealDO.setLeaderName(CollectionUtils.isNotEmpty(leaderOrgList) ? leaderOrgList.get(0).getOrgName() + " " + appealDO.getLeaderName() : appealDO.getLeaderName());
                }
            }
            return Optional.ofNullable(appealDO).map(CfGwReplaceInputAppealDO::convert).orElse(null);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public int updateAppealStatus(Long id, int appealStatus) {
        return appealDao.updateAppealStatus(id,appealStatus);
    }

    @Override
    public CfGwReplaceInputAppealDO getAppealById(Long id) {
        return appealDao.getAppealById(id);
    }

    @Override
    public long getAppealNum(String uniqueCode, Date startTime) {
        return appealDao.getAppealNumForBd(uniqueCode, startTime);
    }

    @Override
    public long getAppealNum(List<String> uniqueCodeList, Date startTime) {
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> appealDao.getAppealNumForLeader(list, startTime)).reduce((total, t) -> total + t)
                .get();
    }

    @Override
    public List<CfBdQCAppealSimpleModel> bdAppealList(String uniqueCode, CfQCAppealListSearch cfQCAppealListSearch, Date startTime) {
        int offset = (cfQCAppealListSearch.getPageNo() - 1) * cfQCAppealListSearch.getPageSize();
        return appealDao.bdAppealList(uniqueCode, cfQCAppealListSearch, startTime, offset);
    }

    @Override
    public CfGwReplaceInputAppealDO getBdGwReplaceInputAppealByIdWithUniqueCode(Long id, String uniqueCode) {
        return appealDao.getBdGwReplaceInputAppealByIdWithUniqueCode(id, uniqueCode);
    }
    @Override
    public CfGwReplaceInputAppealDO getBdGwReplaceInputAppealById(Long id) {
        return appealDao.getBdGwReplaceInputAppealById(id);
    }

    @Override
    public List<CfGwReplaceInputAppealApproveLogDO> appealApproveList(Long appealId) {
        return appealApproveLogDao.appealApproveList(appealId);
    }

    @Override
    public List<CfLeaderQCAppealSimpleModel> leaderAppealList(CfQCAppealListSearch cfQCAppealListSearch, Date startTime, List<String> uniqueCodeList) {
        int offset = (cfQCAppealListSearch.getPageNo() - 1) * cfQCAppealListSearch.getPageSize();
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        List<CfLeaderQCAppealSimpleModel> simpleModels = listList.parallelStream().map(list -> appealDao.leaderAppealList(cfQCAppealListSearch, startTime, list, offset)).reduce((total, item) -> {
            total.addAll(item);
            return total;
        }).get();
        simpleModels = simpleModels.stream().sorted(Comparator.comparing(CfLeaderQCAppealSimpleModel::getCreateTime).reversed()).collect(Collectors.toList());
        return simpleModels.size() >= cfQCAppealListSearch.getPageSize() ? simpleModels.subList(0, cfQCAppealListSearch.getPageSize()) : simpleModels;
    }

    @Override
    public void saveAppealInfoJson(Long id, String appealInfoJson) {
        appealDao.saveAppealInfoJson(id,appealInfoJson);
    }

    @Override
    public CfGwReplaceInputAppealDO getBdGwReplaceInputAppealByIdWithLeaderUniqueCode(Long id, String leaderUniqueCode) {
        return appealDao.getBdGwReplaceInputAppealByIdWithLeaderUniqueCode(id, leaderUniqueCode);
    }

    @Override
    public void updateLeaderInfo(Long id, CrowdfundingVolunteer leaderVolunteer) {
        appealDao.updateLeaderInfo(id,leaderVolunteer);
    }

    @Override
    public long bdAppealListCount(String uniqueCode, CfQCAppealListSearch cfQCAppealListSearch, Date startTime) {
        return appealDao.bdAppealListCount(uniqueCode, cfQCAppealListSearch, startTime);
    }

    @Override
    public long leaderAppealListCount(CfQCAppealListSearch cfQCAppealListSearch, Date startTime, List<String> uniqueCodeList) {
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> appealDao.leaderAppealListCount(cfQCAppealListSearch, startTime, list)).reduce((total, item) -> total += item).get();
    }

}
