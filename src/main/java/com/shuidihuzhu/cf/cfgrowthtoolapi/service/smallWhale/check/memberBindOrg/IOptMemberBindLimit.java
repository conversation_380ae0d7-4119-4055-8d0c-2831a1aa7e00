package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.memberBindOrg;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.MemberOrgRelationParam;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-05-12 16:22
 * 人员-组织关系操作限制
 **/
public interface IOptMemberBindLimit {

    Response<Boolean> checkCanOptMemberBind(MemberOrgRelationParam memberOrgRelationParam);

    List<OrganizationMemberOptEnum> getNeedCheckOptEnums();

    BdCrmOrgUserRelationDO getRelationDO(MemberOrgRelationParam memberOrgRelationParam);

}
