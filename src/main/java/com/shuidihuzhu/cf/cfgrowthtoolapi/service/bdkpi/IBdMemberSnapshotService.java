package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CrowdfundingVolunteerChangeEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfCrmMemberSnapshotModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfMemberKpiCalcInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.xrxs.SnapshotRepairData;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @author: fengxuan
 * @create 2020-08-02 2:58 下午
 **/
public interface IBdMemberSnapshotService {

    /**
     * 月初插入或者是后面有新入职的员工, 新入职的定义是没有找到之前的绑定记录
     * 一个人只会存在一条记录
     * @param snapshotDOList
     */
    void addBatch(List<CfCrmMemberSnapshotDO> snapshotDOList);

    /**
     * 更新离职员工
     * @param uniqueCode
     * @param leaveTime
     *
     */
    void updateWhenLevel(String uniqueCode, Date leaveTime);

    /**
     * 更新员工是新老员工
     */
    void updateSnapshotNewStaff(CrowdfundingVolunteer volunteer);

    List<CfCrmMemberSnapshotDO> listVolunteerByCityListAndLevel(List<String> cityList, int level);

    List<String> listUniqueCodeByCityListAndLevel(List<String> cityList, int level);

    /**
     * 根据员工查询对应的快照信息
     * @param uniqueCode
     * @return
     */
    CfCrmMemberSnapshotDO getByUniqueCode(String uniqueCode);


    List<CfMemberKpiCalcInfoModel> listAllMemberSnapshot(String monthKey);

    List<CfCrmMemberSnapshotModel> getBdList(Integer level, String cityName, String monthKey);

    int updateRecordDay(CfCrmMemberSnapshotDO cfCrmMemberSnapshotDO);

    int updateOrgIdByUniqueCodeAndMonthKey(BdCrmOrgUserRelationDO relationDO, String monthKey);

    List<CfCrmMemberSnapshotDO> listMemberByMonthKeyAndOrgId(String monthKey, List<Long> allSubOrgIncludeSelf);

    CfCrmMemberSnapshotDO getByUniqueCodeByMonthKey(String uniqueCode, String monthKey);

    List<CfCrmMemberSnapshotDO> listAllMemberSnapshotByMonth(String monthKey);

    /**
     * 修复数据
     */
    void repairData(SnapshotRepairData snapshotRepairData);

    void saveOrUpdateWhenDimission(CrowdfundingVolunteerChangeEvent crowdfundingVolunteerChangeEvent);

    /**
     * 根据月份获取实际需要考核的组织
     * @param monthKey
     * @param newHashSet
     * @return
     */
    List<Long> getScoreOrgIds(String monthKey, Set<Long> newHashSet);

    List<CfCrmMemberSnapshotDO> listAllMemberSnapshotNotCareDelete(String monthKey);

}
