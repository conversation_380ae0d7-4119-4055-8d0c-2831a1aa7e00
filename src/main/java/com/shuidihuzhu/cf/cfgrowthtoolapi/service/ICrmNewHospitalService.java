package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfHospitalInterviewCaseDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.GdMapHospitalDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ICrmNewHospitalService {
    String getHospitalVPoiCodeByNameAndCity(String hospitalName, String cityName);

    GdMapHospitalDO getGdMapHospitalDOByVHospitalcode(String vhospitalCode);

    String getHospitalVPoiCodeByGdCode(String id);

    int insert(GdMapHospitalDO gdMapHospitalDO);

    GdMapHospitalDO getHospitalByGdPoiId(String gdPoiId);

    GdMapHospitalDO getGdMapHospitalDOById(Integer id);

    GdMapHospitalDO getGdMapHospitalDOPoiIdAndInputName(String gdPoiId, String inputName);

    void fixHospitalByGps();

    List<Map<String, Object>> getHospitalMapsFromEs(String gps, String[] gpsList);

    GdMapHospitalDO getHospitalByGdPoiIdV2(String gdPoiId);

    GdMapHospitalDO getGdMapHospitalDOByInputNameAndCity(String hospitalName, String cityName);

    List<GdMapHospitalDO> standardHospitalSearch(String provinceName, String cityName, String hospitalName, String vhospitalCode, Integer pageNo, Integer pageSize,Integer acceptToPublic);

    List<GdMapHospitalDO> standardHospitalSearchNoPage(String provinceName, String cityName, String hospitalName, String vhospitalCode, Integer acceptToPublic);

    int standardHospitalSearchCount(String provinceName, String cityName, String hospitalName, String vhospitalCode,Integer acceptToPublic);

    List<GdMapHospitalDO> getGdMapHospitalDOByVHospitalcodes(Set<String> vHospitalCodes);


    List<GdMapHospitalDO> getHospitalVPoiCodeByNameAndCityWithLike(String hospitalName, String cityName);

    List<String> getHospitalVPoiCodeByNameAndCityV2(String hospitalName, String city);

    void updateAcceptPublicByVhospitalCode(String vhospitalCode, int acceptToPublic);

    List<GdMapHospitalDO> getHospitalVPoiCodeByNameAndCityV3(String hospitalName, String cityName);

    List<GdMapHospitalDO> getHospitalByNamesAndCity(List<String> hospitalNames, String cityName);

    List<GdMapHospitalDO> getGdMapHospitalDOByVHospitalcodesV2(Set<String> vhospitalCodes);

    void deleteById(Long id, int isDelete);

    void updateById(Long id, String vvhospitalCode, Integer isDelete, Integer isMain, Integer checkStatus);

    GdMapHospitalDO getGdMapHospitalDOByVVHospitalcode(String vvhospitalCode);

    List<GdMapHospitalDO> getGdMapHospitalDOByVVHospitalcodeV2(String vvhospitalCode);

    GdMapHospitalDO getHasCheckedHospital(String inputCity, String inputName);

    /**
     * 分页查询城市对应的医院
     * @return
     */
    List<GdMapHospitalDO> pageMainHospitalByCity(String city, String hospitalName, Integer isMain, int limit, int offset);

    List<GdMapHospitalDO> pageMainHospitalByCityIncludeAlias(String city, String hospitalName, Integer checkStatus, int limit, int offset);

    long countMainHospitalByCity(String city, String hospitalName, Integer isMain);

    long countMainHospitalByCityIncludeAlias(String city, String hospitalName, Integer checkStatus);

    /**
     * 获取案例对应的医院
     * @param caseId
     * @return
     */
    CfHospitalInterviewCaseDO findByCaseId(int caseId);

    void fixLogicAliasById(Long id, String origin, String target);

    List<GdMapHospitalDO> listByVvhospitalCode(List<String> vvHospitalCodes);

    List<GdMapHospitalDO> listHospitalByNameHasCheck(String inputName, String cityName);

    List<GdMapHospitalDO> listByPcodeAndHospitalName(String pcode, String hospitalName);

    boolean isExistByGdAddress(String gdAddress);

    int saveOrUpdateLocation(long id, String longitude, String latitude);

    GdMapHospitalDO selectSpiderCityByHospital(String hospitalName);

}
