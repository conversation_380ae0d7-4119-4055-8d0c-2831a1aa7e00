package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgAndCityRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseBaseDataModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseForCalcModel;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2021/6/9 下午8:55
 */
public abstract class AbstractBdCrmDiagnoseService {
    protected CfBdCrmDiagnoseForCalcModel calcModel;

    public CfBdCrmDiagnoseBaseDataModel aggregateData(String dateTime, List<BdCrmOrgAndCityRelationDO> chinaCityList){
        Map<Long,CfBdCrmDiagnoseBaseDataModel> provinceData = calcModel.getProvinceData(dateTime);
        Map<Long,CfBdCrmDiagnoseBaseDataModel> cityData = calcModel.getCityData(dateTime);
        List<CfBdCrmDiagnoseBaseDataModel> allDiagnoseBaseDataList = Lists.newArrayList();
        for (BdCrmOrgAndCityRelationDO cityRelationDO : chinaCityList){
            //province
            if (cityRelationDO.getBindType() == 0){
                allDiagnoseBaseDataList.add(provinceData.get((long) cityRelationDO.getCityId()));
            }
            //city
            if (cityRelationDO.getBindType() == 1){
                allDiagnoseBaseDataList.add(cityData.get((long) cityRelationDO.getCityId()));
            }
        }
        return CfBdCrmDiagnoseBaseDataModel.aggregateData(allDiagnoseBaseDataList);
    }

    protected String getKeyForOrgIdWithCityIdMapPreData(long orgId, long cityId){
        return String.join(GeneralConstant.CONCAT_CHAR, String.valueOf(orgId),String.valueOf(cityId));
    }
}
