package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfGwReplaceInputAppealApproveLogDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfGwReplaceInputAppealDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdQCAppealSimpleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfLeaderQCAppealSimpleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfQCAppealListSearch;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.model.CfGwReplaceInputQualityTestFeedbackModel;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @create: 2020/11/12 8:00 下午
 */
public interface ICfGwReplaceInputAppealService {

    int saveAppealApproveLog(Long appealId, String operatorName, String operateDesc, String reason);

    int saveAppealApproveLog(Long appealId, String operatorName, String operateDesc, String reason, Date operateTime);

    OpResult saveAppeal(CfGwReplaceInputAppealDO appealDO);

    CfGwReplaceInputAppealDO getLatelyAppealByReportId(Long reportId);

    List<CfGwReplaceInputQualityTestFeedbackModel> getCfGwReplaceInputAppealResult(List<Long> workOrderIds);

    int updateAppealStatus(Long id, int status);

    CfGwReplaceInputAppealDO getAppealById(Long id);

    /**
     * 查询 个人申述个数
     * @param uniqueCode
     * @param startTime
     * @return
     */
    long getAppealNum(String uniqueCode, Date startTime);

    /**
     * 查询 团队申述个数
     * @param uniqueCodeList
     * @param startTime
     * @return
     */
    long getAppealNum(List<String> uniqueCodeList, Date startTime);

    /**
     * 查询个人申述 列表
     *
     * @param uniqueCode
     * @param cfQCAppealListSearch
     * @param startTime
     * @return
     */
    List<CfBdQCAppealSimpleModel> bdAppealList(String uniqueCode, CfQCAppealListSearch cfQCAppealListSearch, Date startTime);

    long bdAppealListCount(String uniqueCode, CfQCAppealListSearch cfQCAppealListSearch, Date startTime);

    /**
     * 根据 id以及顾问uniqueCode 查询
     * @param id
     * @param uniqueCode
     * @return
     */
    CfGwReplaceInputAppealDO getBdGwReplaceInputAppealByIdWithUniqueCode(Long id, String uniqueCode);

    CfGwReplaceInputAppealDO getBdGwReplaceInputAppealById(Long id);

    /**
     * 根据appealId 查询审批记录
     * @param appealId
     * @return
     */
    List<CfGwReplaceInputAppealApproveLogDO> appealApproveList(Long appealId);

    /**
     * 团队申述记录 列表
     * @param cfQCAppealListSearch
     * @param startTime
     * @return
     */
    List<CfLeaderQCAppealSimpleModel> leaderAppealList(CfQCAppealListSearch cfQCAppealListSearch, Date startTime, List<String> uniqueCodeList);

    /**
     * 修改appeal_info_json
     * @param id
     * @param appealInfoJson
     */
    void saveAppealInfoJson(Long id, String appealInfoJson);

    /**
     * 根据id以及 上级的uniqueCode查询 申述记录
     * @param id
     * @param leaderUniqueCode
     * @return
     */
    CfGwReplaceInputAppealDO getBdGwReplaceInputAppealByIdWithLeaderUniqueCode(Long id, String leaderUniqueCode);

    /**
     * 修改上级 的uniqueCode和 name
     * @param id
     * @param leaderVolunteer
     */
    void updateLeaderInfo(Long id, CrowdfundingVolunteer leaderVolunteer);

    long leaderAppealListCount(CfQCAppealListSearch cfQCAppealListSearch, Date startTime, List<String> uniqueCodeList);
}
