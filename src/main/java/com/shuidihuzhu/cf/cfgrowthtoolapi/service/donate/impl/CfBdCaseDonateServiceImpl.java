package com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.CrowdFundingFeignDelegateImpl;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfOperatingRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.donate.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.OperateLogEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.donate.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.donate.BdCaseDonateTaskSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.donate.CfBdCaseDonateService;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCaseInfoDao;
import com.shuidihuzhu.cf.dao.donate.*;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/27 5:45 PM
 */
@Service
@Slf4j
public class CfBdCaseDonateServiceImpl implements CfBdCaseDonateService {

    @Autowired
    private CfBdCaseDonateCityConfigDao cfBdCaseDonateCityConfigDao;

    @Autowired
    private CfBdCaseDonateLevelConfigDao cfBdCaseDonateLevelConfigDao;

    @Autowired
    private CfBdCaseDonateTaskConfigDao cfBdCaseDonateTaskConfigDao;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Autowired
    private IOperateLogService operateLogService;

    @Autowired
    private BdCaseDonateTaskDao bdCaseDonateTaskDao;

    @Autowired
    private CfBdCaseInfoDao cfBdCaseInfoDao;

    @Autowired
    private CrowdFundingFeignDelegateImpl crowdFundingFeignDelegateImpl;

    @Override
    public OpResult<String> updateCityDonateUseStatus(Integer useStatus, Long id, AdminUserAccountModel adminUserAccountModel) {
        // 修改状态前需查询正常运营配置中是否存在已经启用的重复城市
        CfBdCaseDonateCityConfigDO cfBdCaseDonateCityConfigById = cfBdCaseDonateCityConfigDao.getCfBdCaseDonateCityConfigById(id);
        if (Objects.isNull(cfBdCaseDonateCityConfigById)) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        // 只有当启用配置时候进行重复城市判断
        if (useStatus == CaseDonateEnums.CityUseStatusEnum.open.getCode()) {
            String donateOrgName = cfBdCaseDonateCityConfigById.getOrgName();
            String[] orgNameList = donateOrgName.split("、");
            List<String> sameDonateOrgName = Lists.newArrayList();
            for (String orgName : orgNameList) {
                List<CfBdCaseDonateCityConfigDO> sameDonateCityConfig = cfBdCaseDonateCityConfigDao.getSameDonateCityConfig(orgName);
                if (CollectionUtils.isNotEmpty(sameDonateCityConfig)) {
                    // 找出重复的城市
                    sameDonateOrgName.add(orgName);
                }
            }
            // 如果查询到正常运营配置的重复城市返回重复的城市
            if (CollectionUtils.isNotEmpty(sameDonateOrgName)) {
                String msg = String.format(CfGrowthtoolErrorCode.DONATE_CITY_HAVE.getMsg(), String.join("、", sameDonateOrgName));
                return OpResult.createFailResult(CfGrowthtoolErrorCode.DONATE_CITY_HAVE, msg);
            }
        }
        cfBdCaseDonateCityConfigDao.updateUseStatus(useStatus, id);
        // 添加操作记录
        int code = CaseDonateEnums.CityUseStatusEnum.open.getCode();
        if (useStatus == code) {
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(id), OperateTypeEnum.OPEN_CASE_DONATE.getDesc(), OperateTypeEnum.OPEN_CASE_DONATE,
                    "启用配置", adminUserAccountModel.getId(), adminUserAccountModel.getName()));
        } else {
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(id), OperateTypeEnum.CLOSE_CASE_DONATE.getDesc(), OperateTypeEnum.CLOSE_CASE_DONATE,
                    "禁用配置", adminUserAccountModel.getId(), adminUserAccountModel.getName()));
        }
        return OpResult.createSucResult(CfGrowthtoolErrorCode.SUCCESS.getMsg());
    }

    @Override
    public OpResult<String> addOrUpdateCaseDonate(CfBdCaseDonateModel cfBdCaseDonateModel, AdminUserAccountModel adminUserAccountModel) {
        long operateUserId = 0;
        String operateUserName = "";
        if (Objects.nonNull(adminUserAccountModel)) {
            operateUserId = adminUserAccountModel.getId();
            operateUserName = adminUserAccountModel.getName();
        }
        CfBdCaseDonateCityConfigDO cfBdCaseDonateCityConfigDO = cfBdCaseDonateModel.getCfBdCaseDonateCityConfigDO();
        List<CfBdCaseDonateLevelConfigModel> cfBdCaseDonateLevelConfigDOList = cfBdCaseDonateModel.getCfBdCaseDonateLevelConfigDOList();
        List<CfBdCaseDonateTaskConfigDO> cfBdCaseDonateTaskConfigDOList = cfBdCaseDonateModel.getCfBdCaseDonateTaskConfigDOList();
        if (CollectionUtils.isEmpty(cfBdCaseDonateLevelConfigDOList) || CollectionUtils.isEmpty(cfBdCaseDonateTaskConfigDOList)) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        long caseDonateCityConfigId = cfBdCaseDonateCityConfigDO.getId();
        // 限制一下案例运营城市范围数量
        String orgName = cfBdCaseDonateCityConfigDO.getOrgName();
        String[] orgNameList = orgName.split(",");
        if (orgNameList.length > 50) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.OUT_ORG_NAME_SIZE);
        }
        cfBdCaseDonateCityConfigDO.setOrgName(String.join("、", orgNameList));

        // 判断不应该存在重复的分层类别
        long caseLevelCount = cfBdCaseDonateLevelConfigDOList.stream().map(CfBdCaseDonateLevelConfigModel::getCaseLevel).distinct().count();
        if (caseLevelCount < cfBdCaseDonateLevelConfigDOList.size()) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CASE_LEVEL_HAVE);
        }

        // 判断任务生成时间是否重复
        long cornDescCount = cfBdCaseDonateTaskConfigDOList.stream().map(CfBdCaseDonateTaskConfigDO::getCornDesc).distinct().count();
        long conditionTypeCount = cfBdCaseDonateTaskConfigDOList.stream().map(CfBdCaseDonateTaskConfigDO::getConditionType).distinct().count();
        if (cornDescCount < cfBdCaseDonateTaskConfigDOList.size() && conditionTypeCount < cfBdCaseDonateTaskConfigDOList.size()) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.TASK_CONFIG_HAVE);
        }

        // 判断所添加的配置的时间格式是否正确
        List<String> timeList = cfBdCaseDonateTaskConfigDOList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getCornDesc()))
                .map(CfBdCaseDonateTaskConfigDO::getCornDesc)
                .collect(Collectors.toList());
        if (StringUtils.isNotBlank(cfBdCaseDonateCityConfigDO.getMonitorTime())) {
            timeList.add(cfBdCaseDonateCityConfigDO.getMonitorTime());
        }
        boolean timeFormat = isTimeFormat(timeList);
        if (!timeFormat) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.TIME_FORMAT_ERROR);
        }
        if (caseDonateCityConfigId == 0) {
            cfBdCaseDonateCityConfigDO.setUseStatus(CaseDonateEnums.CityUseStatusEnum.close.getCode());
            cfBdCaseDonateCityConfigDao.insert(cfBdCaseDonateCityConfigDO);
            long afterInsertCityConfigId = cfBdCaseDonateCityConfigDO.getId();
            List<CfBdCaseDonateLevelConfigDO> cfBdCaseDonateLevelConfigDOS = Lists.newArrayList();
            // 设置捐转城市配置id
            cfBdCaseDonateLevelConfigDOList.forEach(item -> {
                item.setCityConfigId(afterInsertCityConfigId);
                String donateDailyConfig = Optional.of(JSON.toJSONString(item.getDonateDailyConfigList())).orElse("");
                // 前端需要通过list进行表单回显需转换存入数据库
                CfBdCaseDonateLevelConfigDO cfBdCaseDonateLevelConfigDO = new CfBdCaseDonateLevelConfigDO();
                cfBdCaseDonateLevelConfigDO.setDayConfig(donateDailyConfig);
                BeanUtils.copyProperties(item, cfBdCaseDonateLevelConfigDO);
                cfBdCaseDonateLevelConfigDOS.add(cfBdCaseDonateLevelConfigDO);
            });
            cfBdCaseDonateTaskConfigDOList = cfBdCaseDonateTaskConfigDOList.stream()
                    .peek(item -> item.setCityConfigId(afterInsertCityConfigId)).collect(Collectors.toList());
            cfBdCaseDonateLevelConfigDao.insertCaseDonateLevelConfig(cfBdCaseDonateLevelConfigDOS);
            cfBdCaseDonateTaskConfigDao.insertDonateTaskConfigList(cfBdCaseDonateTaskConfigDOList);
            // 增加操作记录
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(afterInsertCityConfigId), OperateTypeEnum.ADD_CASE_DONATE.getDesc(), OperateTypeEnum.ADD_CASE_DONATE,
                    "", operateUserId, operateUserName));
        } else {
            // 获取字段更新内容
            List<String> operateContent = this.getOperateContent(caseDonateCityConfigId, cfBdCaseDonateModel);
            // 操作记录
            customEventPublisher.publish(new OperateLogEvent(this, String.valueOf(caseDonateCityConfigId), OperateTypeEnum.EDIT_CASE_DONATE.getDesc(), OperateTypeEnum.EDIT_CASE_DONATE,
                    operateContent.toString(), operateUserId, operateUserName));
            cfBdCaseDonateCityConfigDO.setUpdateTime(new Date());
            cfBdCaseDonateCityConfigDao.update(cfBdCaseDonateCityConfigDO);
            // 判断是否有删除的运营目标标准配置
            List<CfBdCaseDonateLevelConfigDO> donateLevelConfigByCityIdFromDb = cfBdCaseDonateLevelConfigDao.getDonateLevelConfigByCityId(caseDonateCityConfigId);
            if (CollectionUtils.isNotEmpty(donateLevelConfigByCityIdFromDb)) {
                List<Long> donateLevelIdList = cfBdCaseDonateLevelConfigDOList.stream().map(CfBdCaseDonateLevelConfigModel::getId).collect(Collectors.toList());
                for (CfBdCaseDonateLevelConfigDO cfBdCaseDonateLevelConfigDO : donateLevelConfigByCityIdFromDb) {
                    // 如果前端传过来的运营目标标准配置不包含数据库中查询到的就删除掉这条配置
                    if (!donateLevelIdList.contains(cfBdCaseDonateLevelConfigDO.getId())) {
                        cfBdCaseDonateLevelConfigDao.updateDelete(cfBdCaseDonateLevelConfigDO.getId());
                    }
                }
            }
            // 判断是否有删除的任务生成规则配置
            List<CfBdCaseDonateTaskConfigDO> donateTaskConfigByCityIdFromDb = cfBdCaseDonateTaskConfigDao.getDonateTaskConfigByCityId(caseDonateCityConfigId);
            if (CollectionUtils.isNotEmpty(donateTaskConfigByCityIdFromDb)) {
                List<Long> donateTaskIdList = cfBdCaseDonateTaskConfigDOList.stream().map(CfBdCaseDonateTaskConfigDO::getId).collect(Collectors.toList());
                for (CfBdCaseDonateTaskConfigDO cfBdCaseDonateTaskConfigDO : donateTaskConfigByCityIdFromDb) {
                    // 如果前端传过来的任务生成规则配置不包含数据库中查询到的就删除掉这条配置
                    if (!donateTaskIdList.contains(cfBdCaseDonateTaskConfigDO.getId())) {
                        cfBdCaseDonateTaskConfigDao.updateDelete(cfBdCaseDonateTaskConfigDO.getId());
                    }
                }
            }
            for (CfBdCaseDonateLevelConfigModel cfBdCaseDonateLevelConfigModel : cfBdCaseDonateLevelConfigDOList) {
                CfBdCaseDonateLevelConfigDO cfBdCaseDonateLevelConfig = new CfBdCaseDonateLevelConfigDO();
                List<DonateDailyConfig> donateDailyConfigList = cfBdCaseDonateLevelConfigModel.getDonateDailyConfigList();
                BeanUtils.copyProperties(cfBdCaseDonateLevelConfigModel, cfBdCaseDonateLevelConfig);
                cfBdCaseDonateLevelConfig.setDayConfig(JSON.toJSONString(donateDailyConfigList));
                if (cfBdCaseDonateLevelConfig.getId() == 0) {
                    // 如果是新增配置cityConfigId
                    cfBdCaseDonateLevelConfig.setCityConfigId(caseDonateCityConfigId);
                    cfBdCaseDonateLevelConfigDao.insert(cfBdCaseDonateLevelConfig);
                } else {
                    cfBdCaseDonateLevelConfigDao.update(cfBdCaseDonateLevelConfig);
                }
            }
            for (CfBdCaseDonateTaskConfigDO cfBdCaseDonateTaskConfigDO : cfBdCaseDonateTaskConfigDOList) {
                if (cfBdCaseDonateTaskConfigDO.getId() == 0) {
                    // 如果是新增配置cityConfigId
                    cfBdCaseDonateTaskConfigDO.setCityConfigId(caseDonateCityConfigId);
                    cfBdCaseDonateTaskConfigDao.insert(cfBdCaseDonateTaskConfigDO);
                } else {
                    cfBdCaseDonateTaskConfigDao.update(cfBdCaseDonateTaskConfigDO);
                }
            }
        }
        return OpResult.createSucResult(CfGrowthtoolErrorCode.SUCCESS.getMsg());
    }

    @Override
    public CommonPageModel<CfBdCaseDonateModel> getBdCaseDonateConfigInfo(String cityName, Integer useStatus, Integer pageNo, Integer pageSize) {
        List<CfBdCaseDonateCityConfigDO> caseDonateCityConfigDOS = cfBdCaseDonateCityConfigDao.getDonateByCityOrStatus(cityName, useStatus, (pageNo - 1) * pageSize, pageSize);
        Integer caseDonateCityConfigCount = cfBdCaseDonateCityConfigDao.getDonateByCityOrStatusCount(cityName, useStatus);
        CommonPageModel<CfBdCaseDonateModel> donateModelCommonPageModel = new CommonPageModel<>();
        donateModelCommonPageModel.setCount(caseDonateCityConfigCount);
        if (CollectionUtils.isNotEmpty(caseDonateCityConfigDOS)) {
            List<CfBdCaseDonateModel> cfBdCaseDonateModelList = Lists.newArrayList();
            List<Long> cityConfigIdList = caseDonateCityConfigDOS.stream().map(CfBdCaseDonateCityConfigDO::getId).collect(Collectors.toList());
            List<CfBdCaseDonateLevelConfigDO> donateLevelConfigByCityIds = cfBdCaseDonateLevelConfigDao.getDonateLevelConfigByCityIds(cityConfigIdList);
            List<CfBdCaseDonateTaskConfigDO> donateTaskConfigByCityIds = cfBdCaseDonateTaskConfigDao.getDonateTaskConfigByCityIds(cityConfigIdList);
            Map<Long, List<CfBdCaseDonateLevelConfigDO>> cfBdCaseDonateLevelConfigDOMap = donateLevelConfigByCityIds.stream()
                    .collect(Collectors.groupingBy(CfBdCaseDonateLevelConfigDO::getCityConfigId));
            Map<Long, List<CfBdCaseDonateTaskConfigDO>> cfBdCaseDonateTaskConfigDOMap = donateTaskConfigByCityIds.stream()
                    .collect(Collectors.groupingBy(CfBdCaseDonateTaskConfigDO::getCityConfigId));
            for (CfBdCaseDonateCityConfigDO caseDonateCityConfigDO : caseDonateCityConfigDOS) {
                CfBdCaseDonateModel cfBdCaseDonateModel = new CfBdCaseDonateModel();
                Long cityConfigId = caseDonateCityConfigDO.getId();
                List<CfBdCaseDonateLevelConfigDO> cfBdCaseDonateLevelConfigDOList = cfBdCaseDonateLevelConfigDOMap.get(cityConfigId);
                List<CfBdCaseDonateTaskConfigDO> cfBdCaseDonateTaskConfigDOList = cfBdCaseDonateTaskConfigDOMap.get(cityConfigId);
                cfBdCaseDonateModel.setCfBdCaseDonateCityConfigDO(caseDonateCityConfigDO);
                // 对数据库中的转发目标标准转换为list
                List<CfBdCaseDonateLevelConfigModel> cfBdCaseDonateLevelConfigModelList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(cfBdCaseDonateLevelConfigDOList)) {
                    for (CfBdCaseDonateLevelConfigDO cfBdCaseDonateLevelConfigDO : cfBdCaseDonateLevelConfigDOList) {
                        CfBdCaseDonateLevelConfigModel cfBdCaseDonateLevelConfigModel = new CfBdCaseDonateLevelConfigModel();
                        List<DonateDailyConfig> donateDailyConfigList = cfBdCaseDonateLevelConfigDO.parseDayConfig();
                        BeanUtils.copyProperties(cfBdCaseDonateLevelConfigDO, cfBdCaseDonateLevelConfigModel);
                        cfBdCaseDonateLevelConfigModel.setDonateDailyConfigList(donateDailyConfigList);
                        cfBdCaseDonateLevelConfigModelList.add(cfBdCaseDonateLevelConfigModel);
                    }
                }
                cfBdCaseDonateModel.setCfBdCaseDonateLevelConfigDOList(cfBdCaseDonateLevelConfigModelList);
                cfBdCaseDonateModel.setCfBdCaseDonateTaskConfigDOList(cfBdCaseDonateTaskConfigDOList);
                // 查询最新操作人和操作时间
                List<CfOperatingRecordDO> cfOperatingRecordDOList = operateLogService.listOptLogRecordByKey(String.valueOf(cityConfigId),
                        Lists.newArrayList(OperateTypeEnum.CLOSE_CASE_DONATE, OperateTypeEnum.OPEN_CASE_DONATE, OperateTypeEnum.ADD_CASE_DONATE, OperateTypeEnum.EDIT_CASE_DONATE));
                CfOperatingRecordDO cfOperatingRecordDO = cfOperatingRecordDOList
                        .stream()
                        .max(Ordering.natural().onResultOf(CfOperatingRecordDO::getCreateTime))
                        .orElse(null);
                if (Objects.nonNull(cfOperatingRecordDO)) {
                    cfBdCaseDonateModel.setModifyName(cfOperatingRecordDO.getOperateName());
                    cfBdCaseDonateModel.setModifyTime(cfOperatingRecordDO.getCreateTime());
                }
                cfBdCaseDonateModelList.add(cfBdCaseDonateModel);
            }
            cfBdCaseDonateModelList = cfBdCaseDonateModelList.stream().sorted(Ordering.natural().reverse().nullsLast().onResultOf(CfBdCaseDonateModel::getModifyTime)).collect(Collectors.toList());
            donateModelCommonPageModel.setList(cfBdCaseDonateModelList);
            return donateModelCommonPageModel;
        }
        return donateModelCommonPageModel;
    }

    @Override
    public CfBdCaseDonateModel getDetailDonate(Long id) {
        CfBdCaseDonateCityConfigDO cfBdCaseDonateCityConfigById = cfBdCaseDonateCityConfigDao.getCfBdCaseDonateCityConfigById(id);
        CfBdCaseDonateModel cfBdCaseDonateModel = new CfBdCaseDonateModel();
        if (Objects.isNull(cfBdCaseDonateCityConfigById)) {
            return null;
        }
        String orgName = cfBdCaseDonateCityConfigById.getOrgName();
        String[] orgNameList = orgName.split("、");
        cfBdCaseDonateCityConfigById.setOrgName(String.join(",", orgNameList));
        List<CfBdCaseDonateLevelConfigDO> donateLevelConfigByCityIdList = cfBdCaseDonateLevelConfigDao.getDonateLevelConfigByCityId(id);
        List<CfBdCaseDonateLevelConfigModel> cfBdCaseDonateLevelConfigModelList = Lists.newArrayList();
        for (CfBdCaseDonateLevelConfigDO cfBdCaseDonateLevelConfigDO : donateLevelConfigByCityIdList) {
            CfBdCaseDonateLevelConfigModel cfBdCaseDonateLevelConfigModel = new CfBdCaseDonateLevelConfigModel();
            List<DonateDailyConfig> donateDailyConfigList = cfBdCaseDonateLevelConfigDO.parseDayConfig();
            BeanUtils.copyProperties(cfBdCaseDonateLevelConfigDO, cfBdCaseDonateLevelConfigModel);
            cfBdCaseDonateLevelConfigModel.setDonateDailyConfigList(donateDailyConfigList);
            cfBdCaseDonateLevelConfigModelList.add(cfBdCaseDonateLevelConfigModel);
        }
        List<CfBdCaseDonateTaskConfigDO> donateTaskConfigByCityIdList = cfBdCaseDonateTaskConfigDao.getDonateTaskConfigByCityId(id);
        cfBdCaseDonateModel.setCfBdCaseDonateCityConfigDO(cfBdCaseDonateCityConfigById);
        cfBdCaseDonateModel.setCfBdCaseDonateTaskConfigDOList(donateTaskConfigByCityIdList);
        cfBdCaseDonateModel.setCfBdCaseDonateLevelConfigDOList(cfBdCaseDonateLevelConfigModelList);
        return cfBdCaseDonateModel;
    }

    @Override
    public List<CfOperatingRecordDO> getOperatingRecord(Long recordKey) {
        return operateLogService.listOptLogRecordByKey(String.valueOf(recordKey),
                Lists.newArrayList(OperateTypeEnum.CLOSE_CASE_DONATE, OperateTypeEnum.OPEN_CASE_DONATE, OperateTypeEnum.ADD_CASE_DONATE, OperateTypeEnum.EDIT_CASE_DONATE));
    }

    @Override
    public CommonPageModel<CfBdCaseDonateTaskResultModel> getBdCaseDonateTask(BdCaseDonateTaskSearchParam bdCaseDonateTaskSearchParam) {
        CommonPageModel<CfBdCaseDonateTaskResultModel> commonPageModel = new CommonPageModel<>();
        // 处理下时间
        Date taskEndTime = bdCaseDonateTaskSearchParam.getTaskEndTime();
        if (Objects.nonNull(taskEndTime)) {
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(taskEndTime);
            calendar.add(Calendar.DATE, 1);
            bdCaseDonateTaskSearchParam.setTaskEndTime(calendar.getTime());
        }
        Long caseDonateTaskCount = bdCaseDonateTaskDao.countByQueryParams(bdCaseDonateTaskSearchParam);
        if (caseDonateTaskCount == 0) {
            return commonPageModel;
        }
        List<CfBdCaseDonateTaskResultModel> cfBdCaseDonateTaskResultList = bdCaseDonateTaskDao.listByQueryParams(bdCaseDonateTaskSearchParam);
        if (CollectionUtils.isNotEmpty(cfBdCaseDonateTaskResultList)) {
            List<Integer> caseIdList = cfBdCaseDonateTaskResultList.stream().map(CfBdCaseDonateTaskResultModel::getCaseId).collect(Collectors.toList());
            //获取配置信息
            List<Long> cityConfigIdList = cfBdCaseDonateTaskResultList.stream().map(CfBdCaseDonateTaskResultModel::getCityConfigId).collect(Collectors.toList());
            Map<Long, CfBdCaseDonateLevelConfigDO> levelConfigDOMap = cfBdCaseDonateLevelConfigDao.getDonateLevelConfigByCityIds(cityConfigIdList)
                    .stream()
                    .collect(Collectors.toMap(CfBdCaseDonateLevelConfigDO::getCityConfigId, Function.identity(), (before, after) -> before));

            Map<Long, CfBdCaseDonateTaskConfigDO> taskConfigDOMap = cfBdCaseDonateTaskConfigDao.getDonateTaskConfigByCityIds(cityConfigIdList)
                    .stream()
                    .collect(Collectors.toMap(CfBdCaseDonateTaskConfigDO::getCityConfigId, Function.identity(), (before, after) -> before));

            // 案例维度统计任务完成情况
            Map<Integer, List<BdCaseDonateTaskDO>> caseDonateTaskMap = bdCaseDonateTaskDao.getFinishTaskByCaseId(caseIdList)
                    .stream()
                    .collect(Collectors.groupingBy(BdCaseDonateTaskDO::getCaseId));

            List<CfBdCaseInfoDo> cfBdCaseInfoDoList = cfBdCaseInfoDao.listCaseInfoByCaseIds(caseIdList);
            // 查询案例详情
            Map<Integer, CfBdCaseInfoDo> cfBdCaseInfoDoMap = cfBdCaseInfoDoList.stream().collect(Collectors.toMap(CfBdCaseInfoDo::getCaseId, Function.identity()));
            Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = crowdFundingFeignDelegateImpl.getCrowdfundingListById(caseIdList).stream()
                    .collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (before, after) -> before));
            for (CfBdCaseDonateTaskResultModel cfBdCaseDonateTaskResultModel : cfBdCaseDonateTaskResultList) {
                CfBdCaseDonateTaskConfigDO cfBdCaseDonateTaskConfigDO = taskConfigDOMap.get(cfBdCaseDonateTaskResultModel.getCityConfigId());
                if (cfBdCaseDonateTaskConfigDO == null) {
                    continue;
                }
                cfBdCaseDonateTaskResultModel.setConditionType(cfBdCaseDonateTaskConfigDO.getConditionType());
                int caseId = cfBdCaseDonateTaskResultModel.getCaseId();
                CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoDoMap.get(caseId);
                CrowdfundingInfo crowdfundingInfo = crowdfundingInfoMap.get(caseId);
                cfBdCaseDonateTaskResultModel.setCaseAmount(cfBdCaseInfoDo.getAmount());
                cfBdCaseDonateTaskResultModel.setDonteCount(cfBdCaseInfoDo.getDonateNum());
                cfBdCaseDonateTaskResultModel.setHavaForwardCount(cfBdCaseInfoDo.getFundraiserShareNum());
                cfBdCaseDonateTaskResultModel.setCaseTitle(crowdfundingInfo.getTitle());
                cfBdCaseDonateTaskResultModel.setInfoId(crowdfundingInfo.getInfoId());
                CfBdCaseDonateLevelConfigDO levelConfigDO = levelConfigDOMap.get(cfBdCaseDonateTaskResultModel.getCityConfigId());
                if (Objects.nonNull(levelConfigDO)) {
                    cfBdCaseDonateTaskResultModel.setRequireDays(levelConfigDO.getRequireDays());
                }
                List<BdCaseDonateTaskDO> finishTaskList = caseDonateTaskMap.get(caseId);
                if (CollectionUtils.isNotEmpty(finishTaskList)) {
                    cfBdCaseDonateTaskResultModel.setFinishDays(finishTaskList.size());
                }
            }
        }

        commonPageModel.setList(cfBdCaseDonateTaskResultList);
        commonPageModel.setCount(Optional.ofNullable(caseDonateTaskCount).orElse(0L).intValue());
        return commonPageModel;
    }

    @Override
    public Long getBdCaseDonateTaskCount(String uniqueCode) {
        return bdCaseDonateTaskDao.countByTaskStatus(uniqueCode);
    }

    private List<String> getOperateContent(Long id, CfBdCaseDonateModel cfBdCaseDonateModel) {
        List<String> contents = new ArrayList<>();
        if (id == null || id == 0) {
            return contents;
        }
        CfBdCaseDonateCityConfigDO newCfBdCaseDonateCityConfigDo = cfBdCaseDonateCityConfigDao.getCfBdCaseDonateCityConfigById(id);
        CfBdCaseDonateCityConfigDO oldCfBdCaseDonateCityConfigDo = cfBdCaseDonateModel.getCfBdCaseDonateCityConfigDO();
        String newOrgName = newCfBdCaseDonateCityConfigDo.getOrgName();
        String newComment = newCfBdCaseDonateCityConfigDo.getComment();
        int newMonitorStatus = newCfBdCaseDonateCityConfigDo.getMonitorStatus();
        String newMonitorTime = newCfBdCaseDonateCityConfigDo.getMonitorTime();
        int newMonitorCondition = newCfBdCaseDonateCityConfigDo.getMonitorCondition();
        String oldOrgName = oldCfBdCaseDonateCityConfigDo.getOrgName();
        String oldComment = oldCfBdCaseDonateCityConfigDo.getComment();
        int oldMonitorStatus = oldCfBdCaseDonateCityConfigDo.getMonitorStatus();
        String oldMonitorTime = oldCfBdCaseDonateCityConfigDo.getMonitorTime();
        int oldMonitorCondition = oldCfBdCaseDonateCityConfigDo.getMonitorCondition();
        if (!newOrgName.equals(oldOrgName)) {
            contents.add(getOrgName(newOrgName, oldOrgName));
        }
        if (!newComment.equals(oldComment)) {
            contents.add(getComment(newComment, oldComment));
        }
        if (!Objects.equals(newMonitorStatus, oldMonitorStatus)) {
            contents.add(getMonitorStatus(newMonitorStatus, oldMonitorStatus));
        }
        if (!newMonitorTime.equals(oldMonitorTime)) {
            contents.add(getMonitorTime(newMonitorTime, oldMonitorTime));
        }
        if (!Objects.equals(newMonitorCondition, oldMonitorCondition)) {
            contents.add(getMonitorCondition(newMonitorCondition, oldMonitorCondition));
        }
        List<CfBdCaseDonateLevelConfigDO> oldCfBdCaseDonateLevelConfigDOList = cfBdCaseDonateLevelConfigDao.getDonateLevelConfigByCityId(id);
        List<CfBdCaseDonateLevelConfigModel> newCfBdCaseDonateLevelConfigDOList = cfBdCaseDonateModel.getCfBdCaseDonateLevelConfigDOList();
        Map<Long, CfBdCaseDonateLevelConfigDO> oldCfBdCaseDonateLevelConfigDOMap = oldCfBdCaseDonateLevelConfigDOList.stream()
                .collect(Collectors.toMap(CfBdCaseDonateLevelConfigDO::getId, Function.identity(), (before, after) -> before));
        // 判断运营目标标准配置是否有更改
        for (CfBdCaseDonateLevelConfigModel newCfBdCaseDonateLevelConfigDO : newCfBdCaseDonateLevelConfigDOList) {
            Long donateLevelConfigId = newCfBdCaseDonateLevelConfigDO.getId();
            CfBdCaseDonateLevelConfigDO oldCfBdCaseDonateLevelConfigDO = oldCfBdCaseDonateLevelConfigDOMap.get(donateLevelConfigId);
            // 如果数据库中的运营目标标准没有获取到说明是新增
            if (Objects.isNull(oldCfBdCaseDonateLevelConfigDO)) {
                contents.add(getAddDonateLevelRecord(newCfBdCaseDonateLevelConfigDO));
                continue;
            }
            int newCaseLevel = newCfBdCaseDonateLevelConfigDO.getCaseLevel();
            int newTaskDays = newCfBdCaseDonateLevelConfigDO.getTaskDays();
            List<DonateDailyConfig> newDonateDailyConfigList = newCfBdCaseDonateLevelConfigDO.getDonateDailyConfigList();

            int oldCaseLevel = oldCfBdCaseDonateLevelConfigDO.getCaseLevel();
            int oldTaskDays = oldCfBdCaseDonateLevelConfigDO.getTaskDays();
            List<DonateDailyConfig> oldDonateDailyConfigList = oldCfBdCaseDonateLevelConfigDO.parseDayConfig();
            if (!Objects.equals(newCaseLevel, oldCaseLevel)) {
                contents.add(getCaseLevel(newCaseLevel, oldCaseLevel));
            }
            if (!Objects.equals(newTaskDays, oldTaskDays)) {
                contents.add(getTaskDay(newTaskDays, oldTaskDays));
            }
            if (!Objects.equals(newDonateDailyConfigList, oldDonateDailyConfigList)) {
                contents.add(getDayConfig(newDonateDailyConfigList, oldDonateDailyConfigList));
            }
        }

        List<CfBdCaseDonateTaskConfigDO> oldCfBdCaseDonateTaskConfigDOList = cfBdCaseDonateTaskConfigDao.getDonateTaskConfigByCityId(id);
        List<CfBdCaseDonateTaskConfigDO> newCfBdCaseDonateTaskConfigDOList = cfBdCaseDonateModel.getCfBdCaseDonateTaskConfigDOList();
        Map<Long, CfBdCaseDonateTaskConfigDO> oldCfBdCaseDonateTaskConfigDOMap = oldCfBdCaseDonateTaskConfigDOList.stream()
                .collect(Collectors.toMap(CfBdCaseDonateTaskConfigDO::getId, Function.identity(), (before, after) -> before));
        for (CfBdCaseDonateTaskConfigDO newCfBdCaseDonateTaskConfigDO : newCfBdCaseDonateTaskConfigDOList) {
            Long donateTaskConfigDOId = newCfBdCaseDonateTaskConfigDO.getId();
            CfBdCaseDonateTaskConfigDO oldCfBdCaseDonateTaskConfigDO = oldCfBdCaseDonateTaskConfigDOMap.get(donateTaskConfigDOId);
            // 如果数据库中的运营目标标准没有获取到说明是新增
            if (Objects.isNull(oldCfBdCaseDonateTaskConfigDO)) {
                contents.add(getAddDonateTaskRecord(newCfBdCaseDonateTaskConfigDO));
                continue;
            }
            String newCornDesc = newCfBdCaseDonateTaskConfigDO.getCornDesc();
            int newConditionType = newCfBdCaseDonateTaskConfigDO.getConditionType();
            int newConditionTarget = newCfBdCaseDonateTaskConfigDO.getConditionTarget();
            int newFinishType = newCfBdCaseDonateTaskConfigDO.getFinishType();
            String oldCornDesc = oldCfBdCaseDonateTaskConfigDO.getCornDesc();
            int oldConditionType = oldCfBdCaseDonateTaskConfigDO.getConditionType();
            int oldConditionTarget = oldCfBdCaseDonateTaskConfigDO.getConditionTarget();
            int oldFinishType = oldCfBdCaseDonateTaskConfigDO.getFinishType();
            if (!Objects.equals(newCornDesc, oldCornDesc)) {
                contents.add(getCornDesc(newCornDesc, oldCornDesc));
            }
            if (!Objects.equals(newConditionType, oldConditionType)) {
                contents.add(getConditionType(newConditionType, oldConditionType));
            }
            if (!Objects.equals(newConditionTarget, oldConditionTarget)) {
                contents.add(getConditionTarget(newConditionTarget, oldConditionTarget));
            }
            if (!Objects.equals(newFinishType, oldFinishType)) {
                contents.add(getFinishType(newFinishType, oldFinishType));
            }
        }
        return contents;
    }

    // 更改任务生成规则记录
    private String getCornDesc(String newCornDesc, String oldCornDesc) {
        return String.format("任务生成时间由%s更改为%s", oldCornDesc, newCornDesc);
    }

    private String getConditionType(Integer newConditionType, Integer oldConditionType) {
        String newConditionTypeDesc = CaseDonateEnums.ConditionTypeEnum.parseDesc(newConditionType);
        String oldConditionTypeDesc = CaseDonateEnums.ConditionTypeEnum.parseDesc(oldConditionType);
        return String.format("任务生成条件由%s更改为%s", oldConditionTypeDesc, newConditionTypeDesc);
    }

    private String getConditionTarget(Integer newConditionTarget, Integer oldConditionTarget) {
        return String.format("转发目标完成度为由%s更改为%s", oldConditionTarget, newConditionTarget);
    }

    private String getFinishType(Integer newFinishType, Integer oldFinishType) {
        String newFinishDesc = CaseDonateEnums.FinishTypeEnum.parseDesc(newFinishType);
        String oldFinishDesc = CaseDonateEnums.FinishTypeEnum.parseDesc(oldFinishType);
        return String.format("任务完成目标由%s更改为%s", oldFinishDesc, newFinishDesc);
    }

    // 新增任务生成规则记录
    private String getAddDonateTaskRecord(CfBdCaseDonateTaskConfigDO newCfBdCaseDonateTaskConfigDO) {
        String cornDesc = newCfBdCaseDonateTaskConfigDO.getCornDesc();
        int conditionType = newCfBdCaseDonateTaskConfigDO.getConditionType();
        String conditionTypeDesc = CaseDonateEnums.ConditionTypeEnum.parseDesc(conditionType);
        int conditionTarget = newCfBdCaseDonateTaskConfigDO.getConditionTarget();
        int finishType = newCfBdCaseDonateTaskConfigDO.getFinishType();
        String finishDesc = CaseDonateEnums.FinishTypeEnum.parseDesc(finishType);
        return String.format("新增任务生成规则记录：任务生成时间为：%s,任务生成条件为：%s,转发目标完成度为：%s,任务完成目标为:%s", cornDesc, conditionTypeDesc, conditionTarget, finishDesc);
    }

    // 更改运营目标标准操作记录
    private String getCaseLevel(Integer newCaseLevel, Integer oldCaseLevel) {
        String newCaseLevelDesc = CaseDonateEnums.CaseLevelEnum.parseDesc(newCaseLevel);
        String oldCaseLevelDesc = CaseDonateEnums.CaseLevelEnum.parseDesc(oldCaseLevel);
        return String.format("案例分层类别由%s更改为%s", oldCaseLevelDesc, newCaseLevelDesc);
    }

    private String getTaskDay(Integer newTaskDays, Integer oldTaskDays) {
        return String.format("运营天数由%s天更改为%s天", oldTaskDays, newTaskDays);
    }

    private String getDayConfig(List<DonateDailyConfig> newDayConfig, List<DonateDailyConfig> oldDayConfig) {
        return String.format("每天转发运营标准由%s更改为%s", oldDayConfig.toString(), newDayConfig.toString());
    }

    // 新增运营目标标准操作记录
    private String getAddDonateLevelRecord(CfBdCaseDonateLevelConfigModel cfBdCaseDonateLevelConfigDO) {
        int caseLevel = cfBdCaseDonateLevelConfigDO.getCaseLevel();
        String caseLevelDesc = CaseDonateEnums.CaseLevelEnum.parseDesc(caseLevel);
        int taskDays = cfBdCaseDonateLevelConfigDO.getTaskDays();
        String dayConfig = cfBdCaseDonateLevelConfigDO.getDonateDailyConfigList().toString();
        return String.format("新增运营目标标准记录：案例分层类别为：%s,运营天数为：%s,每天转发运营标准为：%s", caseLevelDesc, taskDays, dayConfig);
    }

    // 更改运营城市等相关配置操作记录
    private String getMonitorCondition(Integer newMonitorCondition, Integer oldMonitorCondition) {
        return String.format("任务目标完成度为达到由%s调整为%s", oldMonitorCondition, newMonitorCondition);
    }

    private String getMonitorTime(String newMonitorTime, String oldMonitorTime) {
        return String.format("每日监控时间由%s调整为%s", oldMonitorTime, newMonitorTime);
    }

    private String getMonitorStatus(Integer newMonitorStatus, Integer oldMonitorStatus) {
        String newMonitorDesc = CaseDonateEnums.MonitorStatusEnum.parseDesc(newMonitorStatus);
        String oldMonitorDesc = CaseDonateEnums.MonitorStatusEnum.parseDesc(oldMonitorStatus);
        return String.format("是否开启监测由%s调整为%s", oldMonitorDesc, newMonitorDesc);
    }

    private String getComment(String newComment, String oldComment) {
        return String.format("备注信息从%s调整为%s", oldComment, newComment);
    }

    private String getOrgName(String newOrgName, String oldOrgName) {
        return String.format("案例运营城市从%s调整为%s", oldOrgName, newOrgName);
    }

    // 判断是否是HH:mm格式的时间
    private boolean isTimeFormat(List<String> timeList) {
        if (CollectionUtils.isEmpty(timeList)) {
            return false;
        }
        for (String time : timeList) {
            if (!time.matches("(0\\d{1}|1\\d{1}|2[0-3]):([0-5]\\d{1})")) {
                return false;
            }
        }
        return true;
    }
}
