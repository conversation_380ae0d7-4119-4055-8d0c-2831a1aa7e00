package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfGrowthtoolKpiLotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.*;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2022-02-15 15:54
 **/
@Slf4j
@Service
public class PushPepMemberDataService {

    @Autowired
    private IBdMemberSnapshotService bdMemberSnapshotService;

    @Autowired
    private PepClient pepClient;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ICrmSelfBuiltOrgReadService selfBuiltOrgReadService;

    @Autowired
    private ICrmOrganizationRelationService orgRelationService;

    @Autowired
    private IBdMemberSnapshotJobService bdMemberSnapshotJobService;

    @Autowired
    private ICfGrowthtoolKpiLotService cfGrowthtoolKpiLotService;

    public static final String testOrgName = "测试";

    private void pushMemberData(PepUserModel pepUserModel) {
        mqProducerService.pushMemberData(pepUserModel);
    }


    /**
     * 所有需要计算绩效的人员
     */
    public void syncAllMember(List<CfGrowthtoolKpiLotDO> memberLots) {
        if (CollectionUtils.isEmpty(memberLots)) {
            return;
        }
        for (CfGrowthtoolKpiLotDO memberLot : memberLots) {
            pushMemberData(memberLot);
        }
    }


    private void pushMemberData(CfGrowthtoolKpiLotDO kpiLotDO) {
        //一个程序中只有一个批次
        List<Long> lotIds = kpiLotDO.parseLotIds();
        if (CollectionUtils.isEmpty(lotIds)) {
            return;
        }
        long memberLot = lotIds.get(0);
        Response<LotInfo> lotResponse = pepClient.getLotInfoById(memberLot);
        log.info("获取人员批次,id:{},response:{}", memberLot, lotResponse);
        if (lotResponse.notOk() || lotResponse.getData() == null) {
            return;
        }
        LotInfo lotInfo = lotResponse.getData();
        //判断取哪一部分数据
        Date lotStartTime = lotInfo.getLotStartTime();
        if (lotStartTime == null) {
            return;
        }
        String monthKey = new DateTime(lotStartTime).toString(GrowthtoolUtil.ymfmt);
        //23号
        DateTime lastEntryTime = DateTime.parse(monthKey, GrowthtoolUtil.ymfmt)
                .withDayOfMonth(23)
                .withTimeAtStartOfDay();
        //如果当前小于批次开始的23号,需要直接推送当前组织上的人员信息
        if (lastEntryTime.isAfter(DateTime.now())) {
            pushRealTimeMember(memberLot, lastEntryTime);
        } else {
            //查看是否有推送过
            if (kpiLotDO.getPushStatus() == 0) {
                //需要先删除数据,后续再将状态修改为已经推送
                pepClient.cleanUserInfo(memberLot);
                cfGrowthtoolKpiLotService.updatePushStatus(kpiLotDO.getId(), 1);
            }
            pushSnapshotMember(memberLot, monthKey, lastEntryTime);
        }
    }


    private void pushRealTimeMember(long memberLot, DateTime lastEntryTime) {
        //获取当前直营组织人员
        List<CfCrmMemberSnapshotDO> allMemberSnapshotInfo = bdMemberSnapshotJobService.getAllMemberSnapshotInfo();
        pushMemberInfo(memberLot, allMemberSnapshotInfo, lastEntryTime);
    }

    private void pushSnapshotMember(long memberLot, String monthKey, DateTime lastEntryTime) {
        List<CfCrmMemberSnapshotDO> cfMemberKpiCalcInfoModels = bdMemberSnapshotService.listAllMemberSnapshotNotCareDelete(monthKey);
        pushMemberInfo(memberLot, cfMemberKpiCalcInfoModels, lastEntryTime);
        Map<String, CrowdfundingVolunteer> leaderVolunteerMap =
                cfVolunteerService.getCfVolunteerDOByUniqueCodes(
                                cfMemberKpiCalcInfoModels.stream()
                                        .map(CfCrmMemberSnapshotDO::getLeaderUniqueCode)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toList())
                        )
                        .stream()
                        .collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity()));
        //当前人员和所有上级
        List<PepUserRelationModel> relationModelList = Lists.newArrayList();
        for (CfCrmMemberSnapshotDO cfMemberKpiCalcInfoModel : cfMemberKpiCalcInfoModels) {
            List<String> allLeader = Lists.newArrayList();
            getAllLeader(cfMemberKpiCalcInfoModels, allLeader, cfMemberKpiCalcInfoModel.getUniqueCode());
            for (String leader : allLeader) {
                PepUserRelationModel pepUserRelationModel = new PepUserRelationModel();
                pepUserRelationModel.setUserId(cfMemberKpiCalcInfoModel.getUniqueCode());
                pepUserRelationModel.setUserName(cfMemberKpiCalcInfoModel.getVolunteerName());
                pepUserRelationModel.setLeaderUserId(leader);
                pepUserRelationModel.setLeaderUserName(Optional.ofNullable(leaderVolunteerMap.get(leader))
                        .map(CrowdfundingVolunteer::getVolunteerName).orElse(""));
                pepUserRelationModel.setLotId(memberLot);
                relationModelList.add(pepUserRelationModel);
            }
        }

        for (PepUserRelationModel pepUserRelationModel : relationModelList) {
            mqProducerService.pushMemberRelationData(pepUserRelationModel);
        }
    }


    private void pushMemberInfo(long memberLot, List<CfCrmMemberSnapshotDO> cfMemberKpiCalcInfoModels, DateTime lastEntryTime) {
        Map<String, CrowdfundingVolunteer> volunteerMap = cfVolunteerService.getCfVolunteerDOByUniqueCodes(cfMemberKpiCalcInfoModels.stream().map(CfCrmMemberSnapshotDO::getUniqueCode).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity()));
        List<PepUserModel> pepUserModels = cfMemberKpiCalcInfoModels.stream()
                .filter(item -> item.getLevel() < 300)
                .map(item -> {
                    PepUserModel pepUserModel = new PepUserModel();
                    pepUserModel.setLotId(memberLot);
                    pepUserModel.setUserId(item.getUniqueCode());
                    pepUserModel.setUserName(item.getVolunteerName());
                    pepUserModel.setCity(item.getCity());
                    pepUserModel.setProvince(item.getProvince());
                    pepUserModel.setMemberLevel(item.getLevel());
                    pepUserModel.setWorkStatus(item.getWorkStatus());
                    pepUserModel.setNewStaff(item.getNewStaff());
                    pepUserModel.setLeaveTime(item.getLeaveTime());
                    pepUserModel.setOrgPath(item.getOrgPath());
                    CrowdfundingVolunteer crowdfundingVolunteer = volunteerMap.get(item.getUniqueCode());
                    if (crowdfundingVolunteer != null) {
                        pepUserModel.setMis(crowdfundingVolunteer.getMis());
                        pepUserModel.setEntryTime(crowdfundingVolunteer.getEntryTime());
                        pepUserModel.setJobNum(crowdfundingVolunteer.getJobNum());
                        pepUserModel.setEncryptIdCard(crowdfundingVolunteer.getIdCardNumber());
                        pepUserModel.setEncryptMobile(crowdfundingVolunteer.getMobile());
                    }
                    //试岗员工不推送
                    boolean testJobMember = crowdfundingVolunteer != null && crowdfundingVolunteer.getEntryTime() != null
                            && new DateTime(crowdfundingVolunteer.getEntryTime()).isAfter(lastEntryTime);
                    if (testJobMember) {
                        return null;
                    }
                    return pepUserModel;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        int testOrgId = apolloService.getTestOrgId();
        for (PepUserModel pepUserModel : pepUserModels) {
            //过滤下，当前人员如果只再测试区域不需要推送
            if (pepUserModel.getOrgPath() != null && pepUserModel.getOrgPath().contains(testOrgName)) {
                List<BdCrmOrgUserRelationDO> relationDOList = orgRelationService.listMemberOrgRelationByUniqueCode(pepUserModel.getUserId());
                boolean allInTest = relationDOList
                        .stream()
                        .allMatch(item -> selfBuiltOrgReadService.listParentOrgAsChain(item.getOrgId())
                                .stream()
                                .map(BdCrmOrganizationDO::getId)
                                .collect(Collectors.toList())
                                .contains((long) testOrgId));
                if (allInTest) {
                    continue;
                }
            }
            //发送人员给绩效平台
            pushMemberData(pepUserModel);
        }
    }


    public void getAllLeader(List<CfCrmMemberSnapshotDO> cfMemberKpiCalcInfoModels, List<String> allLeader, String userId) {
        List<String> newLeaders = cfMemberKpiCalcInfoModels.stream()
                .filter(item -> StringUtils.isNotBlank(item.getLeaderUniqueCode()))
                //防止互为上级导致死循环
                .filter(item -> !allLeader.contains(item.getLeaderUniqueCode()))
                .filter(item -> StringUtils.isNotBlank(userId) && Objects.equals(userId, item.getUniqueCode()))
                .map(CfCrmMemberSnapshotDO::getLeaderUniqueCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        allLeader.addAll(newLeaders);
        for (String newLeader : newLeaders) {
            getAllLeader(cfMemberKpiCalcInfoModels, allLeader, newLeader);
        }
    }


}
