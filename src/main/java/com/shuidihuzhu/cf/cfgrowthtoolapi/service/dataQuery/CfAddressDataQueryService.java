package com.shuidihuzhu.cf.cfgrowthtoolapi.service.dataQuery;

import com.shuidihuzhu.cf.model.datautilapi.DataAddressByIdCard;

/**
 * <AUTHOR>
 * @Date 2022/7/21 8:49 PM
 * @Describe 地域信息查询接口
 */
public interface CfAddressDataQueryService {

    /*
    * 根据身份证号查询地址
    */
    DataAddressByIdCard queryAddressByIdCard(String idCard);

    /*
    * 根据ip查询地址
    */
    DataAddressByIdCard queryAddressByIp(String ip);

    /*
     * 根据手机号查询地址
     */
    DataAddressByIdCard queryAddressByMobile(String mobile);

    /*
     * 根据城市code查询地址
     */
    DataAddressByIdCard queryAddressByCityCode(String code);

    /*
     * 根据城市名称查询地址
     */
    DataAddressByIdCard queryAddressByCityName(String provinceName, String cityName);
}
