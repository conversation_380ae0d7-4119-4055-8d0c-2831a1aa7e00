package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.partner;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPartnerDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PartnerEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.partner.KpiPartnerCaseDetail;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.partner.KpiPartnerCityTarget;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepPartnerCaseDetailModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-05-22 20:30
 **/
@Slf4j
@Service
public class PartnerCaseDetailPushService extends AbstractPushPartnerDataService {


    List<Long> caseTags = Lists.newArrayList(1L, 2L);

    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.partner_case_detail;
    }

    @Override
    protected List<PepPartnerCaseDetailModel> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        //5号后不需要在推送数据
        if (DateTime.now().minusDays(5).isAfter(new DateTime(lotInfo.getLotEndTime()))) {
            return Lists.newArrayList();
        }
        List<CfKpiPartnerDataDO> cityPartnerList = cfKpiPartnerDataService.listByDateKeyAndPartnerType(pushWhichDay.toString(GrowthtoolUtil.ymdfmt), PartnerEnums.KpiPartnerTypeEnum.city_detail.getType());
        List<CfKpiPartnerDataDO> cfKpiPartnerDataDOS = cfKpiPartnerDataService.listByDateKeyAndPartnerType(pushWhichDay.toString(GrowthtoolUtil.ymdfmt), PartnerEnums.KpiPartnerTypeEnum.case_detail.getType());
        if (CollectionUtils.isEmpty(cfKpiPartnerDataDOS)) {
            return Lists.newArrayList();
        }
        List<KpiPartnerCityTarget> cityTargetList = cityPartnerList.stream()
                .map(item -> {
                    String content = item.getContent();
                    if (StringUtils.isBlank(content)) {
                        return null;
                    }
                    return JSON.parseObject(content, KpiPartnerCityTarget.class);
                })
                .filter(Objects::nonNull).collect(Collectors.toList());

        //需要对案例数据做聚合
        Map<Long, List<KpiPartnerCaseDetail>> caseIdGroup = cfKpiPartnerDataDOS.stream()
                .map(item -> {
                            String content = item.getContent();
                            if (StringUtils.isBlank(content)) {
                                return null;
                            }
                            //限制下时间范围
                            KpiPartnerCaseDetail kpiPartnerCaseDetail = JSON.parseObject(content, KpiPartnerCaseDetail.class);
                            if (kpiPartnerCaseDetail == null || !isInMonth(kpiPartnerCaseDetail.getStatDt(), lotInfo)) {
                                return null;
                            }
                            return kpiPartnerCaseDetail;
                        }
                )
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(KpiPartnerCaseDetail::getInfoId));
        List<PepPartnerCaseDetailModel> result = Lists.newArrayList();
        for (Long infoId : caseIdGroup.keySet()) {
            List<KpiPartnerCaseDetail> kpiPartnerCaseDetails = caseIdGroup.get(infoId);
            KpiPartnerCaseDetail kpiPartnerCaseDetail = kpiPartnerCaseDetails.get(0);
            PepPartnerCaseDetailModel pepPartnerCaseDetailModel = new PepPartnerCaseDetailModel();
            pepPartnerCaseDetailModel.setUserId(kpiPartnerCaseDetail.getBdGroup());
            pepPartnerCaseDetailModel.setCase_id(kpiPartnerCaseDetail.getInfoId());
            pepPartnerCaseDetailModel.setUniqueCode(kpiPartnerCaseDetail.getVolunteerCode());
            if (StringUtils.isNumeric(kpiPartnerCaseDetail.getCaseType())) {
                pepPartnerCaseDetailModel.setCase_tag(Integer.parseInt(kpiPartnerCaseDetail.getCaseType()));
            }
            int donateCnt = kpiPartnerCaseDetails.stream().mapToInt(KpiPartnerCaseDetail::getRealDonateCnt).sum();
            pepPartnerCaseDetailModel.setOrigin_donate_cnt(donateCnt);
            int donateCntV1 = kpiPartnerCaseDetails.stream().mapToInt(KpiPartnerCaseDetail::getDonateCnt).sum();
            if (donateCntV1 <= 0) {
                log.info("本月捐单次数为0,不推送数据");
                continue;
            }
            int donateAmt = kpiPartnerCaseDetails.stream().mapToInt(KpiPartnerCaseDetail::getRealDonateAmt).sum();
            donateAmt = Math.max(donateAmt, 0);
            donateAmt = Math.min(donateAmt, 133333);
            int donateCntExclude12 = donateCnt - kpiPartnerCaseDetail.getDonateCnt12();
            pepPartnerCaseDetailModel.setDonate_amount(donateAmt);
            pepPartnerCaseDetailModel.setDonate_cnt(Math.max(donateCntExclude12, 0));
            pepPartnerCaseDetailModel.setCase_city(kpiPartnerCaseDetail.getCaseCity());
            pepPartnerCaseDetailModel.setCity_level(kpiPartnerCaseDetail.getCityLevel());
            pepPartnerCaseDetailModel.setLotId(lotInfo.getLotId());
            pepPartnerCaseDetailModel.setCity_tag_v1(pepPartnerCaseDetailModel.getCity_level());
            boolean wrongCityLevel = Objects.equals(pepPartnerCaseDetailModel.getCase_tag(), 0L)
                    && Objects.equals(pepPartnerCaseDetailModel.getCity_level(), 4L);
            //案例标签(加工)
            if (caseTags.contains(pepPartnerCaseDetailModel.getCase_tag()) || wrongCityLevel) {
                Optional<String> cityOptional = cityTargetList.stream()
                        .filter(item -> Objects.equals(item.getBdGroup(), pepPartnerCaseDetailModel.getUserId()))
                        .min(Ordering.natural().onResultOf(KpiPartnerCityTarget::getCityLevel))
                        .map(KpiPartnerCityTarget::getCityLevel);
                if (cityOptional.isPresent() && StringUtils.isNumeric(cityOptional.get())) {
                    pepPartnerCaseDetailModel.setCity_tag_v1(Integer.parseInt(cityOptional.get()));
                }
            }
            result.add(pepPartnerCaseDetailModel);
        }
        return result;
    }


    private boolean isInMonth(Date date, LotInfo lotInfo) {
        if (date == null) {
            return false;
        }
        DateTime dateTime = new DateTime(date);
        Date lotEndTime = lotInfo.getLotEndTime();
        Date lotStartTime = lotInfo.getLotStartTime();
        //DateTime lotDateTime = new DateTime(lotEndTime);
        //统计时间between lotStartTime 和 lotEndTime
        return !date.before(lotStartTime) && !date.after(lotEndTime);
    }
}
