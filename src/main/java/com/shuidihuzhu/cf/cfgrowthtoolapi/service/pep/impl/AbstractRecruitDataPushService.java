package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiRecruitClewBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.PepRealTimeLeaderInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepBaseModel;
import com.shuidihuzhu.cf.performance.data.meta.PepGrowthRecruitModel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-03-20 17:27
 *
 * 直营中的招募绩效部分
 **/
public abstract class AbstractRecruitDataPushService extends AbstractPushDataService {



    abstract List<CfKpiRecruitClewBaseDataDO> listRecruitData(String dayKey, String startTime, String endTime);


    @Override
    protected List<PepGrowthRecruitModel> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        List<PepGrowthRecruitModel> result = Lists.newArrayList();
        List<Long> ignoreKpiRecruitList = apolloService.getIgnoreKpiRecruit();
        String startTime = new DateTime(lotInfo.getLotStartTime()).toString(GrowthtoolUtil.ymdhmsfmt);
        String endTime = new DateTime(lotInfo.getLotEndTime()).toString(GrowthtoolUtil.ymdhmsfmt);
        List<CfKpiRecruitClewBaseDataDO> cfKpiRecruitClewBaseDataDOS = listRecruitData(pushWhichDay.toString(GrowthtoolUtil.ymdfmt), startTime, endTime);
        for (CfKpiRecruitClewBaseDataDO cfKpiRecruitClewBaseDataDO : cfKpiRecruitClewBaseDataDOS) {
            if (CollectionUtils.isNotEmpty(ignoreKpiRecruitList) && ignoreKpiRecruitList.contains(cfKpiRecruitClewBaseDataDO.getPatientId())) {
                continue;
            }

            PepGrowthRecruitModel pepGrowthRecruitModel = new PepGrowthRecruitModel();
            pepGrowthRecruitModel.setUserId(cfKpiRecruitClewBaseDataDO.getUniqueCode());
            pepGrowthRecruitModel.setPatientId(cfKpiRecruitClewBaseDataDO.getPatientId());
            pepGrowthRecruitModel.setPatient_name(cfKpiRecruitClewBaseDataDO.getPatientName());
            Date firstSubmitThroughTime = cfKpiRecruitClewBaseDataDO.getFirstSubmitThroughTime();
            if (firstSubmitThroughTime != null) {
                pepGrowthRecruitModel.setFirstSubmitThroughTime(new DateTime(firstSubmitThroughTime).toString(GrowthtoolUtil.ymdhmsfmt));
            }
            Date firstRandomizedTime = cfKpiRecruitClewBaseDataDO.getFirstRandomizedTime();
            if (firstRandomizedTime != null) {
                pepGrowthRecruitModel.setFirstRandomizedTime(new DateTime(firstRandomizedTime).toString(GrowthtoolUtil.ymdhmsfmt));
            }
            pepGrowthRecruitModel.setNowUserName(cfKpiRecruitClewBaseDataDO.getNowUserName());
            pepGrowthRecruitModel.setLotId(lotInfo.getLotId());
            String leaderInfo = cfKpiRecruitClewBaseDataDO.getLeaderInfo();
            if (StringUtils.isNotBlank(leaderInfo)) {
                List<PepRealTimeLeaderInfo> leaderInfoInDBs = JSON.parseArray(leaderInfo, PepRealTimeLeaderInfo.class);
                List<PepBaseModel> leaderInfoList = leaderInfoInDBs.stream()
                        .map(item -> {
                            PepBaseModel pepBaseModel = new PepBaseModel();
                            pepBaseModel.setLeaderUserId(item.getLeaderUserId());
                            pepBaseModel.setLeaderRoleLevel(item.getLeaderRoleLevel());
                            pepBaseModel.setUserName(item.getUserName());
                            return pepBaseModel;
                        })
                        .collect(Collectors.toList());
                pepGrowthRecruitModel.setLeaderInfo(leaderInfoList);
            }
            result.add(pepGrowthRecruitModel);
        }
        return result;
    }
}
