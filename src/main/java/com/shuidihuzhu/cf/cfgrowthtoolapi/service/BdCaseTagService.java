package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCaseTagDO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * 案例发起时刻的标签信息(BdCaseTag)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-12 15:47:57
 */
public interface BdCaseTagService {

    BdCaseTagDO queryById(long id);

    BdCaseTagDO queryByCaseId(int caseId);

    List<BdCaseTagDO> listByCaseIds(List<Integer> caseIds);

    int insert(BdCaseTagDO bdCaseTag);

    int update(BdCaseTagDO bdCaseTag);

    List<BdCaseTagDO> listByTimeRange(String startTime);

    boolean deleteById(long id);

    //修改下跟进状态
    void updateStopOperate(int caseId, int stopOperate);

    void updateCaseLevel(long id, String caseScore, int caseLevel, int caseScoreTime);

    List<BdCaseTagDO> listByAppointTime(String appointTime);

    void updateExcludeMinDonateCnt(int caseId, int excludeMinDonateCnt);

    Response<Integer> caseHospitalDepartmentCheck(String infoUuid, int hospitalDepartmentCheck);

    boolean showCancelCheck(String infoUuid);

}