package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.partner;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPartnerDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PartnerEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.partner.KpiPartnerCityTarget;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepPartnerCityTargetModel;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-05-22 20:30
 **/
@Service
public class PartnerCityDetailPushService extends AbstractPushPartnerDataService {

    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.partner_city_target;
    }

    @Override
    protected List<PepPartnerCityTargetModel> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        //5号后不需要在推送数据
        if (DateTime.now().minusDays(5).isAfter(new DateTime(lotInfo.getLotEndTime()))) {
            return Lists.newArrayList();
        }
        List<CfKpiPartnerDataDO> cfKpiPartnerDataDOS = cfKpiPartnerDataService.listByDateKeyAndPartnerType(pushWhichDay.toString(GrowthtoolUtil.ymdfmt), PartnerEnums.KpiPartnerTypeEnum.city_detail.getType());
        if (CollectionUtils.isEmpty(cfKpiPartnerDataDOS)) {
            return Lists.newArrayList();
        }
        return cfKpiPartnerDataDOS.stream()
                .map(item -> {
                    String content = item.getContent();
                    if (StringUtils.isBlank(content)) {
                        return null;
                    }
                    PepPartnerCityTargetModel pepPartnerCityTargetModel = new PepPartnerCityTargetModel();
                    KpiPartnerCityTarget kpiPartnerCityTarget = JSON.parseObject(content, KpiPartnerCityTarget.class);
                    pepPartnerCityTargetModel.setUserId(kpiPartnerCityTarget.getBdGroup());
                    pepPartnerCityTargetModel.setCase_city(kpiPartnerCityTarget.getBdCity());
                    if (StringUtils.isNumeric(kpiPartnerCityTarget.getCityLevel())) {
                        pepPartnerCityTargetModel.setCity_level(Integer.parseInt(kpiPartnerCityTarget.getCityLevel()));
                    }
                    if (StringUtils.isNumeric(kpiPartnerCityTarget.getDonateGoal())) {
                        pepPartnerCityTargetModel.setCity_donate_target(Integer.parseInt(kpiPartnerCityTarget.getDonateGoal()));
                    }
                    pepPartnerCityTargetModel.setDay_num(getDayNum(kpiPartnerCityTarget, lotInfo));
                    pepPartnerCityTargetModel.setLotId(lotInfo.getLotId());
                    return pepPartnerCityTargetModel;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }



    /**
     * 计算开城天数
     * @param syncModel
     * @return
     */
    public static int getDayNum(KpiPartnerCityTarget syncModel, LotInfo lotInfo) {
        int dayNum = 0;
        //计算下开城天数
        DateTime currentEndDate = new DateTime(lotInfo.getLotEndTime()).plusMonths(1).withDayOfMonth(1).minusDays(1);
        if (StringUtils.isNotBlank(syncModel.getOpenTime()) && currentEndDate.isAfter(DateTime.parse(syncModel.getOpenTime(), GrowthtoolUtil.ymdfm))) {
            int dayOfMonth = currentEndDate.getDayOfMonth();
            DateTime openDate = DateTime.parse(syncModel.getOpenTime(), GrowthtoolUtil.ymdfm);
            int betweenTime = DateUtil.getIntervalDays(openDate.toDate(), currentEndDate.toDate());
            dayNum = Math.min(dayOfMonth, betweenTime + 1);
        }
        return dayNum;
    }
}
