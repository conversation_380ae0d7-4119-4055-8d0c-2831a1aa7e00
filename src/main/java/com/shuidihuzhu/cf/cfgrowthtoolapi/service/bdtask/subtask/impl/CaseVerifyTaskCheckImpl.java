package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.subtask.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdSubTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdSubTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.subtask.IBdSubTaskCheckService;
import com.shuidihuzhu.client.cf.api.chaifenbeta.ugc.CfUgcServiceFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2023-09-20 15:59
 **/
@Service
public class CaseVerifyTaskCheckImpl implements IBdSubTaskCheckService {

    @Autowired
    private CfUgcServiceFeignClient cfUgcServiceFeignClient;

    @Override
    public CrmBdSubTaskDO.TaskTypeEnum getTaskType() {
        return CrmBdSubTaskDO.TaskTypeEnum.case_verify_three_people;
    }

    @Override
    public boolean checkNeedCreateTask(BdSubTaskContext bdSubTaskContext) {
        return true;
    }

    @Override
    public boolean checkTaskComplete(BdSubTaskContext bdSubTaskContext) {
        Response<List<String>> response = cfUgcServiceFeignClient.queryAllCrowdFundingVerificationByInfoUuid(bdSubTaskContext.getInfoUuid());
        long size = Optional.ofNullable(response).filter(Response::ok).map(Response::getData).orElse(Lists.newArrayList()).size();
        return size >= 3;
    }

}
