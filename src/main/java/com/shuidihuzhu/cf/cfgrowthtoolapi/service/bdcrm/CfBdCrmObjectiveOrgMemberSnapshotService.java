package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveOrgMemberSnapshot;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfBdCrmOrgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.SimpleUserInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CrmOrgUserVo;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * @author: wanghui
 * @create: 2021/2/23 上午10:39
 */
public interface CfBdCrmObjectiveOrgMemberSnapshotService{

    CfBdCrmObjectiveOrgMemberSnapshot getByCycleIdWithOrgId(Long cycleId, Long orgId);

    List<String> listAllSubOrgUniqueCode(Long cycleId, Long orgId);

    List<CfBdCrmObjectiveOrgMemberSnapshot> listAllSubByCycleIdWithOrgId(Long cycleId, Long orgId);

    List<Long> listSubOrgIdByCycleIdWithOrgId(Long cycleId, Long orgId);

    List<Long> listOrgIdByUniqueCodeWithCycleIdList(List<Long> cycleIdList, String uniqueCode);

    List<CrmOrgUserVo> listObjectiveCycleOrgByCycleIdWithUniqueCode(List<Long> cycleIdList, List<String> uniqueCodeList);

    List<CfBdCrmObjectiveOrgMemberSnapshot> listSubOrgByCycleIdWithOrgId(Long cycleId, Long orgId);

    void batchInsertOrgSnapshot(List<CfBdCrmOrgModel> crmOrgModelList, List<Long> cycleIdList);

    void batchInsertMemberSnapshot(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, List<Long> cycleIdList);

    void updateOrgNameWithOrgPathByCycleIdList(List<Long> cycleIdList, BdCrmOrganizationDO currentOrganization, String oldOrgPath, String oldOrgName);

    List<CfBdCrmOrgModel> listCfBdCrmOrgModelByCycleId(Long cycleId);

    /**
     * 移动组织
     * @param orgId
     * @param newParentOrgId
     * @param oldOrgPath
     * @param newOrgPath
     * @param cycleIdList
     * @return
     */
    void updateParentOrgId(long orgId, long newParentOrgId, String oldOrgPath, String newOrgPath, List<Long> cycleIdList);

    /**
     * 在每个周期中 取出一个子组织
     * @param orgIdList
     * @return
     */
    List<Long> listSubOrgIdFromAllCycleByOrgIdList(List<Long> orgIdList);

    List<CfBdCrmObjectiveOrgMemberSnapshot> listSubOrgFromAllCycleByOrgIdList(List<Long> orgIdList);

    List<CfBdCrmOrgModel> listParentOrgByOrgIdList(List<Long> orgIdList, Long cycleId);

    List<String> listParentOrgNameByOrgIdList(List<Long> orgIdList, Long cycleId);

    /**
     * 查询每个组织上的管理者
     * @param orgIdList
     * @return
     */
    List<String> listLeaderByOrgIdList(List<Long> orgIdList, Long cycleId);

    List<String> listLeaderUniqueCodeByOrgIdListWithRoleList(List<Long> orgIdList, Long cycleId, List<Integer> roleList);

    void updateIsDelete(List<Long> objectiveCycleIdList, String uniqueCode, Long orgId, int isDelete);

    List<CfBdCrmObjectiveOrgMemberSnapshot> listByCycleIdWithUniqueCode(Long cycleId, String uniqueCode);

    void updateIsDelete(String newOrgPath, int isDelete);

    List<CfBdCrmObjectiveOrgMemberSnapshot> listAllMemberByCycleIdWithOrgIdList(Long cycleId, List<Long> subOrgIdList);

    List<CfBdCrmObjectiveOrgMemberSnapshot> listByCycleIdListWithOrgId(Collection<Long> cycleIdList, Long orgId);

    List<BdCrmOrganizationDO> getAllOrgByCycleId(Long cycleId);

    List<Long> listLeafParentOrgIdByCycleId(Long cycleId);

    void updateOrgAttribute(List<Long> objectiveCycleIdList, long orgId, int orgAttribute);

    List<Long> listCycleIdByCycleIdListWithOrgId(List<Long> cycleIdList, Long orgId);

    List<CfBdCrmObjectiveOrgMemberSnapshot> listOrgByCycleIdListWithOrgIdList(List<Long> cycleIdList, List<Long> orgIdList);

}
