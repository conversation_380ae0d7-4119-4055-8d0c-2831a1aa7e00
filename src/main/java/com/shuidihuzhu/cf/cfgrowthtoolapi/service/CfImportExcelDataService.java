package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfImportExcelDataDO;
import com.shuidihuzhu.cf.dao.CfImportExcelDataDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020/10/26 3:33 下午
 */
@Service
public class CfImportExcelDataService implements ICfImportExcelDataService {
    @Autowired
    private CfImportExcelDataDao cfImportExcelDataDao;

    @Override
    public int batchInsert(List<CfImportExcelDataDO> cfImportExcelDataDOList) {
        return cfImportExcelDataDao.batchInsert(cfImportExcelDataDOList);
    }

    @Override
    public Long getMaxId(){
        return cfImportExcelDataDao.getMaxId();
    }

    @Override
    public List<CfImportExcelDataDO> listFailData(long minId, long maxId, int type) {
        return cfImportExcelDataDao.listFailData(minId, maxId, type);
    }
}
