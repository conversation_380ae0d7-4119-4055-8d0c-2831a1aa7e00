package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.partner;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPartnerDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PartnerEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.partner.KpiPartnerCityTarget;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiPartnerDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.*;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-05-26 14:49
 **/
@Slf4j
@Service
public class DelegateMemberPushService {


    @Autowired
    private PepClient pepClient;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    protected CfKpiPartnerDataService cfKpiPartnerDataService;


    private void pushMemberData(PepUserModel pepUserModel) {
        mqProducerService.pushMemberData(pepUserModel);
    }


    /**
     * 所有需要计算绩效的人员
     * @return
     */
    public void syncDelegateMember(List<Long> memberLots) {
        if (CollectionUtils.isEmpty(memberLots)) {
            return;
        }
        Long memberLot = memberLots.get(0);
        Response<LotInfo> lotResponse = pepClient.getLotInfoById(memberLot);
        log.info("获取人员批次,id:{},response:{}", memberLot, lotResponse);
        if (lotResponse.notOk() || lotResponse.getData() == null) {
            return;
        }
        LotInfo lotInfo = lotResponse.getData();
        //判断取哪一部分数据
        Date lotStartTime = lotInfo.getLotStartTime();
        if (lotStartTime == null) {
            return;
        }
        //5号后不需要在推送数据
        if (DateTime.now().minusDays(5).isAfter(new DateTime(lotInfo.getLotEndTime()))) {
            return;
        }
        List<CfKpiPartnerDataDO> cfKpiPartnerDataDOS = cfKpiPartnerDataService.listByDateKeyAndPartnerType(getWhichDayToPush(lotInfo).toString(GrowthtoolUtil.ymdfmt), PartnerEnums.KpiPartnerTypeEnum.city_detail.getType());

        List<PepUserModel> pepUserModelList = cfKpiPartnerDataDOS.stream()
                .map(item -> {
                            String content = item.getContent();
                            if (StringUtils.isBlank(content)) {
                                return null;
                            }
                    return JSON.parseObject(content, KpiPartnerCityTarget.class);
                })
                .filter(Objects::nonNull)
                .map(item -> {
                    PepUserModel pepUserModel = new PepUserModel();
                    pepUserModel.setLotId(memberLot);
                    pepUserModel.setUserId(item.getBdGroup());
                    pepUserModel.setUserName(item.getBdGroup());
                    return pepUserModel;
                })
                .collect(Collectors.toList());
        for (PepUserModel pepUserModel : pepUserModelList) {
            //发送人员给绩效平台
            pushMemberData(pepUserModel);
        }
    }


    DateTime getWhichDayToPush(LotInfo lotInfo) {
        return getDefaultPushDate(lotInfo);
    }


    public DateTime getDefaultPushDate(LotInfo lotInfo) {
        //找到对应的业务数据
        DateTime endDateTime = new DateTime(lotInfo.getLotEndTime());
        DateTime dateTime = endDateTime;
        //如果当时间小于dayKey直接使用当前时间
        if (DateTime.now().minusDays(1).isBefore(endDateTime)) {
            dateTime = DateTime.now().minusDays(1);
        }
        //5号后不需要在推送数据
        if (DateTime.now().minusDays(5).isAfter(dateTime)) {
            return null;
        }
        return dateTime;
    }


}
