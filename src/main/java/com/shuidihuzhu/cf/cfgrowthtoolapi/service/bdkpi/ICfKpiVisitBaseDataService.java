package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCaseTimeDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiVisitBaseDataDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 陪访记录(CfKpiVisitBaseData)表服务接口
 *
 * <AUTHOR>
 * @since 2021-09-14 10:43:11
 */
public interface ICfKpiVisitBaseDataService {

    int insert(CfKpiVisitBaseDataDO cfKpiVisitBaseData);

    Map<String, List<CfKpiVisitBaseDataDO>> listValidByTime(String monthKey, String dayKey);

}