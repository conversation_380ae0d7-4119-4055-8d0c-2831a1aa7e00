package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfBdPerformanceFactorDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.VolunteerCityTarget;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PerformanceFactorDataPushService extends AbstractPushDataService {

    @Autowired
    private ICfKpiPerformanceFactorService cfKpiPerformanceFactorService;

    @Autowired
    protected IBdMemberSnapshotService bdMemberSnapshotService;

    @Autowired
    private IBdMemberSnapshotJobService bdMemberSnapshotJobService;

    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.performance_factor_data;
    }

    @Override
    public DateTime getWhichDayToPush(LotInfo lotInfo) {
        return new DateTime(lotInfo.getLotStartTime());
    }

    @Override
    protected List<VolunteerCityTarget> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        String monthKey = pushWhichDay.toString(GrowthtoolUtil.ymfmt);
        List<CfBdPerformanceFactorDO> performanceFactorDOS = cfKpiPerformanceFactorService.listByMonthKey(Lists.newArrayList(monthKey));
        if (CollectionUtils.isEmpty(performanceFactorDOS)) {
            return Lists.newArrayList();
        }
        //23号
        DateTime lastEntryTime = DateTime.parse(monthKey, GrowthtoolUtil.ymfmt)
                .withDayOfMonth(23)
                .withTimeAtStartOfDay();
        //如果当前小于批次开始的23号,需要直接推送当前组织上的人员信息
        List<CfCrmMemberSnapshotDO> cfCrmMemberSnapshotDOS = Lists.newArrayList();
        if (lastEntryTime.isAfter(DateTime.now())) {
            cfCrmMemberSnapshotDOS = bdMemberSnapshotJobService.getAllMemberSnapshotInfo();
        } else {
            cfCrmMemberSnapshotDOS = bdMemberSnapshotService.listAllMemberSnapshotByMonth(monthKey);
        }
        if (CollectionUtils.isEmpty(cfCrmMemberSnapshotDOS)) {
            return Lists.newArrayList();
        }
        List<VolunteerCityTarget> volunteerCityTargets = Lists.newArrayList();
        List<String> specialUserIds = Lists.newArrayList();
        for (CfBdPerformanceFactorDO performanceFactorDO : performanceFactorDOS) {
            String city = performanceFactorDO.getCity();
            String userId = performanceFactorDO.getUserId();
            Optional<VolunteerCityTarget> firstVolunteerTarget = cfCrmMemberSnapshotDOS
                    .stream()
                    .filter(item -> Objects.equals(item.getUniqueCode(), userId) && Objects.equals(item.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel()))
                    .map(item -> {
                        VolunteerCityTarget volunteerCityTarget = new VolunteerCityTarget();
                        volunteerCityTarget.setLotId(lotInfo.getLotId());
                        volunteerCityTarget.setUserId(item.getUniqueCode());
                        performanceFactorDO.convertToCityTarget(volunteerCityTarget);
                        return volunteerCityTarget;
                    })
                    .findFirst();
            if (firstVolunteerTarget.isPresent() && StringUtils.isNotBlank(userId)) {
                specialUserIds.add(userId);
                volunteerCityTargets.add(firstVolunteerTarget.get());
                continue;
            }
            List<VolunteerCityTarget> cityTargetList = cfCrmMemberSnapshotDOS
                    .stream()
                    .filter(item -> !specialUserIds.contains(item.getUniqueCode()))
                    .filter(item -> Objects.equals(item.getCity(), city) && Objects.equals(item.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel()))
                    .map(item -> {
                        VolunteerCityTarget volunteerCityTarget = new VolunteerCityTarget();
                        volunteerCityTarget.setLotId(lotInfo.getLotId());
                        volunteerCityTarget.setUserId(item.getUniqueCode());
                        performanceFactorDO.convertToCityTarget(volunteerCityTarget);
                        return volunteerCityTarget;
                    })
                    .collect(Collectors.toList());
            volunteerCityTargets.addAll(cityTargetList);
        }
        return volunteerCityTargets;
    }
}
