package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.calresult;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.calresult.EncourageActivityCaseDetailDO;

import java.util.List;

/**
 * 计算结果-案例明细(EncourageActivityCaseDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-23 15:32:57
 */
public interface EncourageActivityCaseDetailService {

    List<EncourageActivityCaseDetailDO> listByActivityIdAndUniqueCodeAndRuleIds(long activityId, String uniqueCode, List<Long> ruleIds);

    List<EncourageActivityCaseDetailDO> listByActivityIdAndRuleId(long activityId, long ruleId);

}
