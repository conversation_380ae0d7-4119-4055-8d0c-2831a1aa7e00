package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.enums.DiseaseTypeEnum;
import com.shuidihuzhu.client.cf.growthtool.model.CfCalcCostModel;
import com.shuidihuzhu.client.cf.growthtool.model.ClewPreposeMaterialSaveOrUpdateModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseVO;
import com.shuidihuzhu.client.cf.growthtool.model.vo.InfoReasonableAmountResultVO;
import com.shuidihuzhu.client.cf.growthtool.param.DiseaseParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-27
 */
public interface ICfCalcDiseaseCostService {
    /**
     * 更新操作日志
     * @param diseaseParam
     * @param cfVolunteer
     * @param infoVo
     * @return
     */
    Integer updateOperatorLog(DiseaseParam diseaseParam, CrowdfundingVolunteer cfVolunteer, InfoReasonableAmountResultVO infoVo);

    /**
     * 保存操作日志
     * @param diseaseParam
     * @param cfVolunteer
     * @param diseaseVO
     * @return
     */
    Long saveOperatorLog(DiseaseParam diseaseParam, CrowdfundingVolunteer cfVolunteer, DiseaseVO diseaseVO);

    /**
     * 将报备id与计算花费日志进行绑定
     * @param reportId
     * @param calcCostIds
     * @return
     */
    int recordPreposeId(Long reportId, List<Long> calcCostIds);

    /**
     * 获取计算花费日志
     * @param diseaseParam
     * @return
     */
    OpResult<List<CfCalcCostModel>> queryCalcCostLog(DiseaseParam diseaseParam);

    /**
     * 设置C端展示疾病
     * @param materialInfoVo
     */
    void setDiseaseName(PreposeMaterialModel.MaterialInfoVo materialInfoVo);

    /**
     * 设置C端展示疾病
     * @param clewPreposeMaterialSaveOrUpdateModel
     */
    void setDiseaseName(ClewPreposeMaterialSaveOrUpdateModel clewPreposeMaterialSaveOrUpdateModel);

}
