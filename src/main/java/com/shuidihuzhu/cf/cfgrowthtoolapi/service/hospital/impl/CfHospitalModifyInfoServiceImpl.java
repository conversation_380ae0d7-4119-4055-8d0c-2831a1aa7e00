package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.impl;

import com.shuidihuzhu.cf.dao.hospital.CfHospitalModifyInfoDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfHospitalModifyInfoDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.CfHospitalModifyInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 医院审核填写的医院信息和审核表是1:n的关系(CfHospitalModifyInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2020-11-09 15:46:08
 */
@Service("cfHospitalModifyInfoService")
public class CfHospitalModifyInfoServiceImpl implements CfHospitalModifyInfoService {
    @Resource
    private CfHospitalModifyInfoDao cfHospitalModifyInfoDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public CfHospitalModifyInfoDo queryById(long id) {
        return this.cfHospitalModifyInfoDao.queryById(id);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    @Override
    public List<CfHospitalModifyInfoDo> queryAllByLimit(int offset, int limit) {
        return this.cfHospitalModifyInfoDao.queryAllByLimit(offset, limit);
    }

    @Override
    public List<CfHospitalModifyInfoDo> listByApproveId(int approveId) {
        return cfHospitalModifyInfoDao.listByApproveId(approveId);
    }

    /**
     * 新增数据
     *
     * @return 实例对象
     */
    @Override
    public void insertBatch(List<CfHospitalModifyInfoDo> modifyList) {
        if (CollectionUtils.isEmpty(modifyList)) {
            return;
        }
        cfHospitalModifyInfoDao.insertBatch(modifyList);
    }

    /**
     * 修改数据
     *
     * @param cfHospitalModifyInfoDo 实例对象
     * @return 实例对象
     */
    @Override
    public CfHospitalModifyInfoDo update(CfHospitalModifyInfoDo cfHospitalModifyInfoDo) {
        this.cfHospitalModifyInfoDao.update(cfHospitalModifyInfoDo);
        return this.queryById(cfHospitalModifyInfoDo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return this.cfHospitalModifyInfoDao.deleteById(id) > 0;
    }
}