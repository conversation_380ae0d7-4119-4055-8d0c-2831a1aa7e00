package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.google.common.cache.*;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.BdCrmOrganizationModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.BdCrmOrganizationPlateModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.dao.bdcrm.BdCrmOrganizationDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-07-14 12:02 下午
 * 有两个实现
 * {@link CfCrmSelfBuiltOrgFromCacheImpl}
 * {@link CfCrmSelfBuiltOrgForSeaImpl}
 **/
@Slf4j
@RefreshScope
public abstract class AbstractCrmSelfBuiltOrgService implements ICrmSelfBuiltOrgReadService {

    @Autowired
    BdCrmOrganizationDao bdCrmOrganizationDao;

    @Autowired
    ICrmOptLogService crmOptLogService;

    @Autowired
    ApolloService apolloService;

    final static int limit = 200;

    public final static String default_splitter = "-";

    LoadingCache<Integer, List<BdCrmOrganizationDO>> allOrgList = CacheBuilder
            .newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, List<BdCrmOrganizationDO>>() {
                @Override
                public List<BdCrmOrganizationDO> load(Integer param) {
                    log.debug("设置本地缓存组织");
                    return getAllOrgByDB();
                }
            });


    /**
     * 获取组织所有的入口
     */
    @Override
    public abstract List<BdCrmOrganizationDO> getAllOrg();


    @Override
    public List<BdCrmOrganizationPlateModel> getAllOrgWithLevel() {
        BdCrmOrganizationModel treeModel = getAllOrgAsTree();
        return BdCrmOrganizationModel.plateView(treeModel);
    }


    @Override
    public List<BdCrmOrganizationDO> getAllOrgByDB() {
        List<BdCrmOrganizationDO> all = Lists.newArrayList();
        long beginId = 0L;
        boolean hasMore = true;
        while (hasMore) {
            List<BdCrmOrganizationDO> bdCrmOrganizationDOS = bdCrmOrganizationDao.listAllCursor(beginId, limit);
            all.addAll(bdCrmOrganizationDOS);
            if (CollectionUtils.isNotEmpty(bdCrmOrganizationDOS)) {
                beginId = bdCrmOrganizationDOS.stream().min(Ordering.natural().reverse().onResultOf(BdCrmOrganizationDO::getId)).get().getId();
            }
            hasMore = bdCrmOrganizationDOS.size() == limit;
        }
        return all;
    }


    @Override
    public BdCrmOrganizationModel getAllOrgAsTree() {
        List<BdCrmOrganizationDO> allOrg = getAllOrg();
        return new BdCrmOrganizationModel()
                .buildByDOList(allOrg);
    }

    @Override
    public BdCrmOrganizationModel getAllOrgAsTree(boolean noShowTest) {
        List<BdCrmOrganizationDO> allOrg = Lists.newArrayList();
        if (noShowTest) {
            allOrg = listNotTestOrg();
        } else {
            allOrg = getAllOrg();
        }
        return new BdCrmOrganizationModel()
                .buildByDOList(allOrg);
    }

    @Override
    public List<BdCrmOrganizationDO> findDirectSubOrgByOrgId(long orgId) {
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = getAllOrg().stream().filter(item -> item.getParentId() == orgId)
                .collect(Collectors.toList());
        List<Long> directOrgIds = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
        log.debug("组织id:{}获取直接下级组织:{}", orgId, directOrgIds);
        return bdCrmOrganizationDOList;
    }
    @Override
    public List<BdCrmOrganizationDO> findDirectSubOrgByOrgIdList(List<Long> orgIdList) {
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = getAllOrg().stream().filter(item -> orgIdList.contains(item.getParentId()))
                .collect(Collectors.toList());
        List<Long> directOrgIds = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
        log.debug("组织idList:{}获取直接下级组织:{}", orgIdList, directOrgIds);
        return bdCrmOrganizationDOList;
    }

    /**
     * 包含orgId对应的BdCrmOrganizationDO 根据orgId查询父节点链路,返回结果按照 首层->第二层->...->orgId对应的组织
     * 从顶层节点到查找节点的路径,如果需要获取名称只需要对这个做转化
     *
     * @param orgId
     * @return
     */
    @Override
    public List<BdCrmOrganizationDO> listParentOrgAsChain(long orgId) {
        List<BdCrmOrganizationDO> allOrg = getAllOrg();
        List<BdCrmOrganizationDO> result = Lists.newArrayList();
        createChain(result, allOrg, orgId);
        return Lists.reverse(result);
    }

    @Override
    public List<BdCrmOrganizationDO> listParentOrgAsChainOrder(long orgId) {
        List<BdCrmOrganizationDO> allOrg = getAllOrg();
        List<BdCrmOrganizationDO> result = Lists.newArrayList();
        createChain(result, allOrg, orgId);
        return result;
    }

    @Override
    public Map<Long, String> listChainByOrgIds(List<Long> orgIds, String splitter) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Maps.newHashMap();
        }
        Map<Long, String> result = Maps.newHashMap();
        List<BdCrmOrganizationDO> allOrg = getAllOrg();
        for (Long orgId : Sets.newHashSet(orgIds)) {
            List<BdCrmOrganizationDO> bdCrmOrganizationDOS = listParentOrgAsChain(orgId, allOrg);
            String chain = bdCrmOrganizationDOS.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.joining(splitter));
            result.put(orgId, chain);
        }
        return result;
    }

    private List<BdCrmOrganizationDO> listParentOrgAsChain(long orgId, List<BdCrmOrganizationDO> allOrg) {
        List<BdCrmOrganizationDO> result = Lists.newArrayList();
        createChain(result, allOrg, orgId);
        return Lists.reverse(result);
    }

    @Override
    public Map<Long, String> listChainByOrgIdsWithDefaultSplitter(List<Long> orgIds) {
        return listChainByOrgIds(orgIds, default_splitter);
    }


    private void createChain(List<BdCrmOrganizationDO> result, List<BdCrmOrganizationDO> allOrg, long orgId) {
        if (orgId == 0) {
            return;
        }
        BdCrmOrganizationDO bdCrmOrganizationDO = null;
        for (BdCrmOrganizationDO item : allOrg) {
            if (Objects.equals(item.getId(), orgId)) {
                bdCrmOrganizationDO = item;
                break;
            }
        }
        if (bdCrmOrganizationDO == null) {
            return;
        }
        result.add(bdCrmOrganizationDO);
        long partnerId = bdCrmOrganizationDO.getParentId();
        createChain(result, allOrg, partnerId);
    }


    @Override
    public List<BdCrmOrganizationDO> listAllSubOrgIncludeSelf(long orgId) {
        BdCrmOrganizationDO currentOrgById = getCurrentOrgById(orgId);
        if (currentOrgById == null) {
            return Lists.newArrayList();
        }
        List<BdCrmOrganizationDO> result = Lists.newArrayList(currentOrgById);
        listSubOrg(result, getAllOrg(), orgId);
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<BdCrmOrganizationDO> listAllSubOrgExcludeSelf(long orgId) {
        List<BdCrmOrganizationDO> result = Lists.newArrayList();
        listSubOrg(result, getAllOrg(), orgId);
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }


    private void listSubOrg(List<BdCrmOrganizationDO> result, List<BdCrmOrganizationDO> allOrgs, long orgId) {
        List<BdCrmOrganizationDO> subOrgs = allOrgs
                .stream()
                .filter(item -> item.getParentId() == orgId)
                .collect(Collectors.toList());
        result.addAll(subOrgs);
        for (BdCrmOrganizationDO subOrg : subOrgs) {
            listSubOrg(result, allOrgs, subOrg.getId());
        }
    }


    @Override
    public BdCrmOrganizationDO getCurrentOrgById(long orgId) {
        return getAllOrg().stream().filter(item -> item.getId() == orgId).findFirst().orElse(null);
    }

    @Override
    public List<BdCrmOrganizationDO> listOrgByName(String orgName) {
        List<Long> testOrgIdList = listAllSubOrgIncludeSelf(GeneralConstant.testAreaOrgId)
                .stream().map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toList());
        if (StringUtils.isBlank(orgName)) {
            return null;
        }
        if (orgName.endsWith("市")) {
            orgName = orgName.substring(0, orgName.length() - 1);
        }
        return bdCrmOrganizationDao.listOrgByName(orgName)
                .stream()
                .filter(item -> !testOrgIdList.contains(item.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, BdCrmOrganizationDO> listOrgInfo(List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Maps.newHashMap();
        }
        return getAllOrg().stream().filter(item -> orgIds.contains(item.getId()))
                .collect(Collectors.toMap(BdCrmOrganizationDO::getId, Function.identity()));
    }

    @Override
    public BdCrmOrganizationDO getParentOrg(long orgId) {
        //查找当前组织
        BdCrmOrganizationDO currentOrgById = getCurrentOrgById(orgId);
        if (currentOrgById == null) {
            log.warn("找不到当前组织:{},无对应的父组织", orgId);
            return null;
        }
        return getAllOrg().stream().filter(item -> item.getId() == currentOrgById.getParentId()).findFirst().orElse(null);
    }

    @Override
    public boolean needShowOrg(List<Integer> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return false;
        }
        if (orgIds.size() > 1) {
            return true;
        }
        long orgId = (long) orgIds.get(0);
        //所有的叶子结点对应的父节点,如果父节点大于1个,则需要展示
        Set<Long> leafParentOrgIds = listAllSubOrgIncludeSelf(orgId)
                .stream()
                .filter(item -> item.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode())
                .map(BdCrmOrganizationDO::getParentId)
                .collect(Collectors.toSet());

        return leafParentOrgIds.size() > 1;
    }


    @Override
    public List<Long> queryOrgIdListByCities(List<String> queryCities) {
        if (CollectionUtils.isEmpty(queryCities)) {
            return Lists.newArrayList();
        }
        return getAllOrg()
                .stream()
                .filter(item -> queryCities.contains(item.getOrgName()))
                .map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toList());
    }

    @Override
    public List<BdCrmOrganizationDO> getOrgInfoList(List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Lists.newArrayList();
        }
        return getAllOrg().stream().filter(item -> orgIds.contains(item.getId()))
                .collect(Collectors.toList());
    }


    @Override
    public boolean subOrgsContainsEachOther(List<Integer> orgIds) {
        log.info("校验组织:{}是否有重复", orgIds);
        if (CollectionUtils.isEmpty(orgIds)) {
            return false;
        }
        Set<Integer> distinctOrgIds = Sets.newHashSet(orgIds);
        if (distinctOrgIds.size() < orgIds.size()) {
            log.info("存在重复的组织id:{}", orgIds);
            return true;
        }
        for (Integer orgId : orgIds) {
            List<Integer> subOrgIds = listAllSubOrgIncludeSelf(orgId)
                    .stream()
                    .map(item -> (int) item.getId())
                    .collect(Collectors.toList());
            List<Integer> hasImportOrgIds = orgIds.stream()
                    .filter(item -> !ObjectUtils.nullSafeEquals(orgId, item))
                    .filter(subOrgIds::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hasImportOrgIds)) {
                log.info("组织id:{}下级组织:{}已经导入预算", orgId, hasImportOrgIds);
                return true;
            }
        }
        return false;
    }

    @Override
    public List<BdCrmOrganizationDO> getOrgByLikeOrgName(String likeValue) {
        return bdCrmOrganizationDao.getOrgByLikeOrgName(likeValue);
    }


    @Override
    public List<Integer> listOrgIdsForQueryParam(List<BdCrmOrganizationDO> contextOrgList) {
        List<Integer> allSubOrgIds = Lists.newArrayList();
        List<Long> orgIds = contextOrgList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
        //57的组织是查全量的组织id这个查询条件可以去掉了
        if (!orgIds.contains((long) GeneralConstant.CRM_ORG_ID)) {
            allSubOrgIds = orgIds.stream()
                    .flatMap(item -> this.listAllSubOrgIncludeSelf(item).stream().map(organizationDO -> (int) organizationDO.getId()))
                    .distinct()
                    .collect(Collectors.toList());
        }
        return allSubOrgIds;
    }

    @Override
    public List<Long> listAllTestOrg() {
        return listAllSubOrgIncludeSelf(apolloService.getTestOrgId())
                .stream()
                .map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toList());
    }

    @Override
    public List<BdCrmOrganizationDO> listNotTestOrg() {
        List<Long> testOrgIds = listAllTestOrg();
        return getAllOrg().stream().filter(item -> !testOrgIds.contains(item.getId())).collect(Collectors.toList());
    }

    @Override
    public List<Long> listAllPartnerOrg() {
        return getAllOrg()
                .stream()
                .filter(item -> item.getOrgName().contains("蜂鸟计划"))
                .map(BdCrmOrganizationDO::getId)
                .map(this::listAllSubOrgIncludeSelf)
                .flatMap(item -> item.stream().map(BdCrmOrganizationDO::getId))
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> listAllOwnOrg(){
        return getAllOrg()
                .stream()
                .filter(item -> item.getOrgName().contains("直营城市"))
                .map(BdCrmOrganizationDO::getId)
                .map(this::listAllSubOrgIncludeSelf)
                .flatMap(item -> item.stream().map(BdCrmOrganizationDO::getId))
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> listAllConsultantEcologicalOperationOrg() {
        return getAllOrg()
                .stream()
                .filter(item -> item.getOrgName().contains("顾问生态运营"))
                .map(BdCrmOrganizationDO::getId)
                .map(this::listAllSubOrgIncludeSelf)
                .flatMap(item -> item.stream().map(BdCrmOrganizationDO::getId))
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> listAllTest() {
        return getAllOrg()
                .stream()
                .filter(item -> item.getOrgName().contains("水滴测试大区"))
                .map(BdCrmOrganizationDO::getId)
                .map(this::listAllSubOrgIncludeSelf)
                .flatMap(item -> item.stream().map(BdCrmOrganizationDO::getId))
                .collect(Collectors.toList());
    }

}
