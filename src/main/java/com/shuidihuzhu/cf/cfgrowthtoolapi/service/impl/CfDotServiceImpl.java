package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfServiceStaffTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.DotTagContentBuilder;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICfDotService;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInviteCaseRelationDO;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInvitorVisitDO;
import com.shuidihuzhu.cf.model.INameValuePair;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-11
 */
@Service
@Slf4j
public class CfDotServiceImpl implements ICfDotService {

    @Autowired
    private MeterRegistry meterRegistry;

    @Override
    public void report(DotTagContentBuilder dotTagContentBuilder) {
        try{
            String table = dotTagContentBuilder.getSubject();
            List<INameValuePair> valuePairList = dotTagContentBuilder.getPayloadList();
            Counter.Builder counter = Counter.builder(table);
            for (INameValuePair nameValuePair : valuePairList){
                counter.tag(nameValuePair.getName(),nameValuePair.getValue());
            }
            counter.register(meterRegistry)
                    .increment();
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+"report error",e);
        }
    }

    @Override
    public void reportCfToufangInvitorVisitDO(CfToufangInvitorVisitDO cfToufangInvitorVisitDO) {
        DotTagContentBuilder dotTagContentBuilder = DotTagContentBuilder.create().subject(GeneralConstant.INVITATIONSTATISTICS)
                .payload("invite",cfToufangInvitorVisitDO.getSourceUserId())
                .payload("invitees",cfToufangInvitorVisitDO.getInvitorUserId())
                .payload("action","invite")
                .payload("invitetype", "community");
        this.report(dotTagContentBuilder);
    }

    @Override
    public void reportCfToufangInviteCaseRelationDO(CfToufangInviteCaseRelationDO caseRelationDO, UserInfoModel userInfoModel) {
        DotTagContentBuilder dotTagContentBuilder = DotTagContentBuilder.create().subject(GeneralConstant.INVITATIONSTATISTICS)
                .payload("invite",caseRelationDO.getInvitorUniqueCode())
                .payload("invitees",userInfoModel.getUserId())
                .payload("action","firstcase")
                .payload("invitetype", "community");
        this.report(dotTagContentBuilder);
    }
}
