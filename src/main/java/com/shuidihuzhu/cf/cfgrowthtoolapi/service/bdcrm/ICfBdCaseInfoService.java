package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.BdServiceInfoVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.HighRiskTipModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.VolunteerCaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmVolunteerCaseCountSimpleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CrmCrowdfundingModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCfSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmSearchParam;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistLunchCaseDto;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.ClewPreposeMaterialSaveOrUpdateModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerInfoUuidModel;
import org.springframework.scheduling.annotation.Async;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Auther: zgq
 * @Date: 2019-05-29 15:13
 * @Description:BD案例扩展操作接口
 */
public interface ICfBdCaseInfoService extends ICfBdCaseSyncService {

    CfBdCaseInfoDo getBdCaseInfoByInfoUuid(String uuid);

    CfBdCaseInfoDo getBdCaseInfoByInfoId(int infoId);

    CfBdCaseInfoDo getLatelyDateCreatedBdCaseInfoByInfoId(int infoId);

    List<CfBdCaseInfoDo> getBdCaseInfoByInfoUuids(List<String> uuids);

    int insert(CfBdCaseInfoDo cfBdCaseInfoDo, CrowdfundingVolunteer volunteer, CrowdfundingInfo crowdfundingInfo);

    List<CfBdCaseInfoDo> getBdCaseInfoByUniqueCodeWithFlag(String uniqueCode, int flag, Date startTime, Date endTime);

    /**
     * 管理员查看自己名下组织的未发起案例数据
     */
    List<CfBdCaseInfoDo> getCfBdCaseInfoByOrgListWithFlag(List<Integer> orgIdList, int flag, String startTime, String endTime, Integer pageNo, Integer pageSize);

    List<CfBdCaseInfoDo> getBdCaseInfoByUniqueCode(String uniqueCode, Date startTime, Date endTime, Integer pageNo, Integer pageSize);

    List<VolunteerInfoUuidModel> getVolunteerInfoUuidModelbyUniqueCodeListWithTime(String startTime,
                                                                                   String endTime,
                                                                                   List<String> volunteerUniqueCodes);

    List<VolunteerCaseModel> listCaseModelByUniqueCodesWithTime(String startTime,
                                                                String endTime,
                                                                List<String> volunteerUniqueCodes);


    List<VolunteerInfoUuidModel> getVolunteerInfoUuidModelbyVolunteerTypeWithTime(String startTime,
                                                                                  String endTime,
                                                                                  int volunteerType);

    long getCountByUniqueCodeList(List<String> uniqueCodeList, String startTime, String endTime);

    List<CfBdCaseInfoDo> listCaseIdByOrgIdList(List<Integer> orgIdList, String startTime, String endTime);

    List<CfBdCaseInfoDo> listCaseIdByUniqueCodeList(List<String> uniqueCodeList, String startTime, String endTime);

    List<CfBdCaseInfoDo> getBdCaseInfoByUniqueCodeAndTimeAndCfAmount(List<String> uniqueCodeList, Integer orgId, Date startTime, Date endTime, BdCrmSearchParam bdCrmSearchParam, Integer pageNo, Integer pageSize);

    long getCountByUniqueCodeAndTimeAndCfAmount(List<String> uniqueCodeList, Integer orgId, Date startTime, Date endTime, BdCrmSearchParam bdCrmSearchParam);

    long countCfByCfSearchParam(BdCfSearchParam bdCfSearchParam);

    List<CfBdCaseInfoDo> listCfByBdCfSearchParam(BdCfSearchParam bdCfSearchParam);

    @Async
    void repairCfBdCaseInfo(List<String> infoUuids);

    long getMinId(String caseStartTime, String caseEndTime);

    List<CfBdCaseInfoDo> needSyncRaiseInfoBdCase(long id, int limit);

    OpResult<BdServiceInfoVo> getBdServiceInfoVoByUniqueCode(String uniqueCode);

    OpResult<BdServiceInfoVo> getBdServiceInfoVoOnrYearAgoByUniqueCode(String uniqueCode, String createTime);

    List<Integer> getMaxAmountCaseIdList(String uniqueCode, long amount, String dateCreated);

    Map<String, List<Long>> getUniqueCodeMapInfoIdsForOneDay(String startTimeStr, List<String> uniqueCodeList);

    List<CfBdCaseInfoDo> listWithTimeRange(Date startTime, Date endTime, int offset, int limit);

    int getCaseCountByCaseTime(String caseStartTime, String caseEndTime);

    long countCfByPhoneVolunteer(String phone, Date queryStartTime, Date queryEndTime, List<Integer> chushenPassList, List<CrowdfundingVolunteer> queryVolunteerList);

    List<CfBdCaseInfoDo> listCfByPhoneVolunteer(String phone, Date queryStartTime, Date queryEndTime, List<Integer> chushenPassList, List<CrowdfundingVolunteer> queryVolunteerList, int pageNo, int pageSize);

    List<CfBdCaseInfoDo> listCaseInfoByCaseIds(List<Integer> caseIds);

    //从案例信息中获取实时案例状态,CfBdCaseInfoDo只有和案例状态相关的信息
    Map<Integer, CfBdCaseInfoDo> obtainCaseStatus(List<Integer> caseIds);

    List<CfBdCaseInfoDo> listNotEndCaseInfoForQrCode(String uniqueCode, int offset, int pageSize);

    List<CfBdCaseInfoDo> listCaseInfoByUniqueCode(String uniqueCode, int size);

    void updateShareTitleAndContent(Integer id, String title, String content);

    List<Integer> getHomePageCaseByOrgIdList(List<Integer> orgIdList, int size, String dateCreated);

    List<String> getCaseNumGreaterThan500UniqueCode();

    /**
     * 查询举报案例总数
     *
     * @param uniqueCode
     * @param startTime
     * @param endTime
     * @return
     */
    long countReportCaseInfo(String uniqueCode, Date startTime, Date endTime);

    /**
     * 获取举报案例数据
     *
     * @param uniqueCode
     * @param startTime
     * @param endTime
     * @return
     */
    List<CfBdCaseInfoDo> listCfReportCaseInfo(String uniqueCode, Date startTime, Date endTime);

    List<CfCrmVolunteerCaseCountSimpleModel> getBdCaseStatistics(String startTime, String endTime, String uniqueCode);

    List<String> listHospitalNameOfLatestMonth(String uniqueCode, Date monthDate, List<Long> caseIdList);

    List<CfBdCaseInfoDo> getBdCaseInfoByUniqueCodeWithPhoneAndTime(List<String> uniqueCodeList, String phone, Date startTime, Date endTime);

    List<CfBdCaseInfoDo> listCaseInfoByCaseIdsAndAmountAndDonate(List<Integer> caseIdList, Integer validAmount, Integer donateNum);

    OpResult<CrmCrowdfundingModel> statByOrg(long orgId);

    /*
     * 小鲸鱼新增代录入和线索判断是否黑名单
     */
    boolean blackVerify(BlacklistLunchCaseDto blacklistLunchCaseDto);

    OpResult<HighRiskTipModel> getRiskTip(ClewPreposeMaterialSaveOrUpdateModel.ClewPreposeMaterialModel clewPreposeMaterialModel);

    List<CfBdCaseInfoDo> listByValidCase(String startTime, String endTime);

    int updateHospitalDepartmentInfo(int caseId, String vhospitalCode, int departmentId, String hospitalName, String departmentName);

    List<CfBdCaseInfoDo> listByUniqueCodeDateCreated(String uniqueCode, String startTime, String endTime);

    int updateHasOfflineBreakWaveTask(String infoUuid, Integer hasOfflineBreakWaveTask);

    /**
     * 判断案例创建时间是否在6天内
     * @param cfBdCaseInfoDo 案例信息对象
     * @return true表示在6天内，false表示超过6天
     */
    boolean isCreatedWithinSixDays(CfBdCaseInfoDo cfBdCaseInfoDo);

    Integer countTaskCaseCount(String uniqueCode, Integer hasOfflineBreakWaveTask, Date limitTime);
}
