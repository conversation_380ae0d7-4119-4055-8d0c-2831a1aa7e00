package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmActivityHomepagePopupDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.OperateLogSearchModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdCrmActivityHomepagePopupVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OperatorLogVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.CfBdCrmActivityHomepagePopupParam;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;

import java.util.Date;
import java.util.Map;

public interface ICfBdCrmActivityHomepagePopupService {


    Response<String> saveOrUpdate(CfBdCrmActivityHomepagePopupParam cfBdCrmActivityHomepagePopupParam, long authSaasUserId);

    Response<String> upOrDown(long id, int publishStatus, long authSaasUserId);

    Response<String> del(long id, long authSaasUserId);

    Response<CfBdCrmActivityHomepagePopupDO> get(long id);

    Response<CommonResultModel<OperatorLogVO>> getOperateLog(OperateLogSearchModel searchModel);

    Response<Map<String, Object>> getList(String activityName, Integer publishStatus, String startTime, String endTime, int pageNum, int pageSize,Integer popupType);

    Response<CfBdCrmActivityHomepagePopupVo> get(CrowdfundingVolunteer volunteer,Integer  popupType);
}
