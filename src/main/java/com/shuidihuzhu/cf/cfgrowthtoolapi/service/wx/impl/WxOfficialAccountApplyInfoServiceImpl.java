package com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.wx.CfWxOfficialAccountApplyInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WxOfficialAccountEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx.IWxOfficialAccountApplyInfoService;
import com.shuidihuzhu.cf.dao.wx.CfWxOfficialAccountApplyInfoDao;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-03-29
 */
@Slf4j
@Service
public class WxOfficialAccountApplyInfoServiceImpl implements IWxOfficialAccountApplyInfoService {

    @Autowired
    private CfWxOfficialAccountApplyInfoDao cfWxOfficialAccountApplyInfoDao;

    @Override
    public int insertOrUpdate(CfWxOfficialAccountApplyInfoDO cfWxOfficialAccountApplyInfoDO) {
        CfWxOfficialAccountApplyInfoDO accountApplyInfoDO = getWxApplyInfoByInfoUuid(cfWxOfficialAccountApplyInfoDO.getInfoUuid());
        if (Objects.isNull(accountApplyInfoDO)){
            return cfWxOfficialAccountApplyInfoDao.insert(cfWxOfficialAccountApplyInfoDO);
        }else{
            cfWxOfficialAccountApplyInfoDO.setId(accountApplyInfoDO.getId());
            return cfWxOfficialAccountApplyInfoDao.updateApplyInfoSecondInput(cfWxOfficialAccountApplyInfoDO);
        }
    }

    @Override
    public int updateApplyStatusAndAriticalUrlById(WxOfficialAccountEnums.ApplyStatusEnum applyStatusEnum, String articleUrl, Long id) {
        return cfWxOfficialAccountApplyInfoDao.updateApplyStatusAndAriticalUrlById(applyStatusEnum.getStatus(),articleUrl,id);
    }

    @Override
    public int updateApplyInfoSucc(CfWxOfficialAccountApplyInfoDO cfWxOfficialAccountApplyInfoDO) {
        return cfWxOfficialAccountApplyInfoDao.updateApplyInfoSucc(cfWxOfficialAccountApplyInfoDO);
    }

    @Override
    public int updateApplyFailById(WxOfficialAccountEnums.ApplyStatusEnum applyStatusEnum, WxOfficialAccountEnums.ApplyFailReasonEnum failReasonEnum, Long id) {
        if (Objects.isNull(id)){
            return 0;
        }
        return cfWxOfficialAccountApplyInfoDao.updateApplyFailById(applyStatusEnum.getStatus(),failReasonEnum.getDesc(),id);
    }

    @Override
    public List<CfWxOfficialAccountApplyInfoDO> listWxOfficialAccountApplyByMsgIdAndThirdType(Long msgId, int thirdType) {
        return cfWxOfficialAccountApplyInfoDao.listWxOfficialAccountApplyByMsgIdAndThirdType(msgId,thirdType);
    }

    @Override
    public CfWxOfficialAccountApplyInfoDO getWxApplyInfoByInfoUuid(String infoUuid) {
        return cfWxOfficialAccountApplyInfoDao.getWxApplyInfoByInfoUuid(infoUuid);
    }

    @Override
    public OpResult<Void> checkIsCanApplyWxUrl(String infoUuid) {
        CfWxOfficialAccountApplyInfoDO wxOfficialAccountApplyInfoDO = getWxApplyInfoByInfoUuid(infoUuid);
        //已经申请过,排除生成失败
        if (Objects.isNull(wxOfficialAccountApplyInfoDO)){
            return OpResult.createSucResult();
        }
        WxOfficialAccountEnums.ApplyStatusEnum applyStatusEnum = WxOfficialAccountEnums.parseApplyStatusEnum(wxOfficialAccountApplyInfoDO.getApplyStatus());
        if (Objects.isNull(applyStatusEnum)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.ILLEGALLY);
        }
        switch (applyStatusEnum) {
            case fail:
            case willapply:
                return OpResult.createSucResult();
            case applying:
                return OpResult.createFailResult(CfGrowthtoolErrorCode.WX_APPLY_URL_APPLYING);
            case succ:
                return OpResult.createFailResult(CfGrowthtoolErrorCode.WX_APPLY_URL_EXISTED);
            default:
                return OpResult.createFailResult(CfGrowthtoolErrorCode.ILLEGALLY);
        }
    }

    @Override
    public int updateMaterialStatusDelete(WxOfficialAccountEnums.MaterialDeleteStatusEnum materialDeleteStatusEnum, Long id) {
        if (Objects.isNull(id)){
            return 0;
        }
        return cfWxOfficialAccountApplyInfoDao.updateMaterialStatusDelete(materialDeleteStatusEnum.getStatus(),id);
    }

    @Override
    public List<CfWxOfficialAccountApplyInfoDO> listWxOfficialByMaterialAndTime(Integer materialDeleteStatus, Date startTime, Date endTime) {
        return cfWxOfficialAccountApplyInfoDao.listWxOfficialByMaterialAndTime(materialDeleteStatus,startTime,endTime);
    }

}
