package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrCustomerConnectRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr.GrCustomerConnectRecordService;
import com.shuidihuzhu.cf.dao.gr.GrCustomerConnectRecordDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


/**
 * gr拜访记录(GrCustomerConnectRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-09 15:32:54
 */
@Service
public class GrCustomerConnectRecordServiceImpl implements GrCustomerConnectRecordService {
    
    @Resource
    private GrCustomerConnectRecordDao grCustomerConnectRecordDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public GrCustomerConnectRecordDO queryById(long id) {
        return grCustomerConnectRecordDao.queryById(id);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    @Override
    public List<GrCustomerConnectRecordDO> queryAllByLimit(int offset, int limit) {
        return grCustomerConnectRecordDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param grCustomerConnectRecord 实例对象
     * @return 实例对象
     */
    @Override
    public GrCustomerConnectRecordDO insert(GrCustomerConnectRecordDO grCustomerConnectRecord) {
        grCustomerConnectRecordDao.insert(grCustomerConnectRecord);
        return grCustomerConnectRecord;
    }

    /**
     * 修改数据
     *
     * @param grCustomerConnectRecord 实例对象
     * @return 实例对象
     */
    @Override
    public GrCustomerConnectRecordDO update(GrCustomerConnectRecordDO grCustomerConnectRecord) {
        grCustomerConnectRecordDao.update(grCustomerConnectRecord);
        return queryById(grCustomerConnectRecord.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return grCustomerConnectRecordDao.deleteById(id) > 0;
    }

    @Override
    public int countRecord(int customerId, String purpose) {
        if (customerId <= 0) {
            return 0;
        }
        return grCustomerConnectRecordDao.countRecord(customerId, purpose);
    }

    @Override
    public List<GrCustomerConnectRecordDO> pageRecord(int customerId, String purpose, int offset, int pageSize) {
        if (customerId <= 0) {
            return Lists.newArrayList();
        }
        return grCustomerConnectRecordDao.pageRecord(customerId, purpose, offset, pageSize);
    }

    //找到这段时间的拜访
    @Override
    public List<GrCustomerConnectRecordDO> listByCustomerIds(List<Integer> customerIds, Date startTime, Date endTime) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Lists.newArrayList();
        }
        return grCustomerConnectRecordDao.listByCustomerIds(customerIds, startTime, endTime);
    }

    @Override
    public int countAddRecordCnt(List<Integer> customerIds, Date startTime, Date endTime) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return 0;
        }
        return Optional.ofNullable(grCustomerConnectRecordDao.countAddRecordCnt(customerIds, startTime, endTime)).orElse(0);
    }
}