package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.dao.bdcrm.BdCaseTagDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCaseTagDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.BdCaseTagService;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 案例发起时刻的标签信息(BdCaseTag)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-12 15:47:59
 */
@Service
public class BdCaseTagServiceImpl implements BdCaseTagService {

    @Resource
    private BdCaseTagDao bdCaseTagDao;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    private final static String REDIS_KEY = "case-show-cancel-check-";

    @Override
    public BdCaseTagDO queryById(long id) {
        return bdCaseTagDao.queryById(id);
    }

    @Override
    public BdCaseTagDO queryByCaseId(int caseId) {
        return bdCaseTagDao.queryByCaseId(caseId);
    }

    @Override
    public List<BdCaseTagDO> listByCaseIds(List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }
        return bdCaseTagDao.listByCaseIds(caseIds);
    }


    @Override
    public int insert(BdCaseTagDO bdCaseTag) {
        return bdCaseTagDao.insert(bdCaseTag);
    }

    @Override
    public int update(BdCaseTagDO bdCaseTag) {
        return bdCaseTagDao.update(bdCaseTag);
    }

    @Override
    public List<BdCaseTagDO> listByTimeRange(String startTime) {
        if (StringUtils.isBlank(startTime)) {
            return Lists.newArrayList();
        }
        return bdCaseTagDao.listByTimeRange(startTime);
    }

    @Override
    public boolean deleteById(long id) {
        return bdCaseTagDao.deleteById(id) > 0;
    }

    @Override
    public void updateStopOperate(int caseId, int stopOperate) {
         bdCaseTagDao.updateStopOperate(caseId, stopOperate);
    }

    @Override
    public void updateCaseLevel(long id, String caseScore, int caseLevel, int caseScoreTime) {
        bdCaseTagDao.updateCaseLevel(id, caseScore, caseLevel, caseScoreTime);
    }

    @Override
    public List<BdCaseTagDO> listByAppointTime(String appointTime) {
        if (StringUtils.isBlank(appointTime)) {
            return Lists.newArrayList();
        }
        return bdCaseTagDao.listByAppointTime(appointTime);
    }

    @Override
    public void updateExcludeMinDonateCnt(int caseId, int excludeMinDonateCnt) {
        if (excludeMinDonateCnt <= 0 || caseId <= 0) {
            return;
        }
        bdCaseTagDao.updateExcludeMinDonateCnt(caseId, excludeMinDonateCnt);
    }

    @Override
    public Response<Integer> caseHospitalDepartmentCheck(String infoUuid, int hospitalDepartmentCheck) {
        if (StringUtils.isEmpty(infoUuid) || hospitalDepartmentCheck < 0) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoUuid(infoUuid);
        if (cfBdCaseInfoDo == null) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        int res = bdCaseTagDao.updateCaseHospitalDepartmentCheck(cfBdCaseInfoDo.getCaseId(), hospitalDepartmentCheck);
        if (res > 0) {
            if (hospitalDepartmentCheck == BdCaseTagDO.HospitalDepartmentCheckEnum.CHECK_COMPLETED.getValue()) {
                redissonHandler.setEX(REDIS_KEY + cfBdCaseInfoDo.getInfoUuid(), true, RedissonHandler.ONE_DAY * 7);
            } else {
                redissonHandler.del(REDIS_KEY + cfBdCaseInfoDo.getInfoUuid());
            }
        }
        return NewResponseUtil.makeSuccess(res);
    }

    @Override
    public boolean showCancelCheck(String infoUuid) {
        Boolean value = redissonHandler.get(REDIS_KEY + infoUuid, Boolean.class);
        return value != null && value;
    }

}