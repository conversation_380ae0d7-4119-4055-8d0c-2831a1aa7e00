package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.subtask;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdSubTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdSubTaskContext;

/**
 * <AUTHOR>
 * @date 2024/1/3  14:41
 */
public interface IBdSubTaskCheckService {

    CrmBdSubTaskDO.TaskTypeEnum getTaskType();

    boolean checkNeedCreateTask(BdSubTaskContext bdSubTaskContext);

    boolean checkTaskComplete(BdSubTaskContext bdSubTaskContext);

}
