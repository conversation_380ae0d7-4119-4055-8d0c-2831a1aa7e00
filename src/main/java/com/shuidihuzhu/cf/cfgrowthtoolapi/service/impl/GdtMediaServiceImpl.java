package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.GdtTokenEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.gdt.client.GdtRequestFeignClient;
import com.shuidihuzhu.cf.cfgrowthtoolapi.gdt.client.SMRequestFeignClient;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.gdt.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.MediaService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.MD5Util;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.common.web.util.http.HttpResponseModel;
import com.shuidihuzhu.common.web.util.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2019-08-14
 */
@Slf4j
@Service
@RefreshScope
public class GdtMediaServiceImpl implements MediaService {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Autowired
    private GdtRequestFeignClient gdtRequestFeignClient;
    @Autowired
    private SMRequestFeignClient smRequestFeignClient;


    private static final String AUTHORIZATIONCODEURI = "https://developers.e.qq.com/oauth/authorize?client_id=${client_id}&redirect_uri=${redirect_uri}";
    private static final String ACCESSTOKENURIUSEAUTHORIZATIONCODE = "https://api.e.qq.com/oauth/token?client_id=${client_id}&client_secret=${client_secret}&grant_type=authorization_code&authorization_code=${authorization_code}&redirect_uri=${redirect_uri}";
    private static final String ACCESSTOKENURIUSEREFRESHTOKEN = "https://api.e.qq.com/oauth/token?client_id=${client_id}&client_secret=${client_secret}&grant_type=refresh_token&refresh_token=${refresh_token}";

    private static final long ACCESSTOKENEXPIRES = 86395000L;
    private static final long REFRESHTOKENEXPIRES = 2591995000L;

    private static final int GDTSUCCESSCODE = 0;

    public static final Map<String,String> accountMap = Maps.newConcurrentMap();
    static {
        accountMap.put("水滴互助-1","Sdc*ms06102020");
        accountMap.put("水滴互助-2","Sdc*ms06102020");
        accountMap.put("蓝鲸理财课-品众SM","UCPZ2020");
        accountMap.put("蓝鲸搜索1","Ab123456");
    }


    @Override
    public Response<String> report(String mobile) {
        return reportGdtH5Info(mobile);
    }

    @Override
    public Response<String> reportSM(SMRequestModel smRequestModel) {
        log.info("reportSM request:{}",smRequestModel);
        if(smRequestModel.getHeader() == null){
            return NewResponseUtil.makeSuccess(null);
        }
        if(StringUtils.isBlank(smRequestModel.getHeader().getUsername())){
            return NewResponseUtil.makeSuccess(null);
        }
        String password = accountMap.get(smRequestModel.getHeader().getUsername());
        if(StringUtils.isBlank(password)){
            return NewResponseUtil.makeSuccess(null);
        }
        smRequestModel.getHeader().setPassword(password);
        try{
            String response = smRequestFeignClient.report(smRequestModel);
            log.info("smRequestFeignClient.report request:{},response:{}",smRequestModel,response);
        }catch (Exception e){
            log.warn("reportSM request:{},Exception",smRequestModel,e);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    /**
     * 上报数据
     */
    private Response<String> reportGdtH5Info(String mobile) {
        String accessToken = getAccessToken();
        log.info("get_GDT_accessToken:{}",accessToken);
        if(StringUtils.isNotBlank(accessToken)){
            GdtRequestModel jsonParam = buildGdtActivateH5(mobile);
            log.info("request_jsonParam:{}",jsonParam);
            try{
                String result = gdtRequestFeignClient.report(accessToken,String.valueOf(Instant.now().getEpochSecond()),
                        String.valueOf(UUID.randomUUID()).replaceAll("-",""),jsonParam);
                JSONObject resultJson = JSONObject.parseObject(result);
                String repsoneMsg = resultJson.getString("message");
                if (resultJson == null || GDTSUCCESSCODE != resultJson.getIntValue("code")){
                    return NewResponseUtil.makeFail(StringUtils.isNotEmpty(repsoneMsg) ? repsoneMsg : "");
                }
                return NewResponseUtil.makeSuccess(StringUtils.isNotEmpty(repsoneMsg) ? repsoneMsg : "");
            }catch (Exception e){
                return NewResponseUtil.makeError(CfGrowthtoolErrorCode.GDT_REPORT_ERROR);
            }
        }
        return NewResponseUtil.makeError(CfGrowthtoolErrorCode.GDT_NOT_ACCESSTOKEN);
    }

    /**
     * 构造上报请求体
     * @param mobile
     * @return
     */
    private GdtRequestModel buildGdtActivateH5(String mobile) {
        GdtActionUserId userId = new GdtActionUserId();
        userId.setHash_phone(MD5Util.getMD5HashValue(mobile));
        GdtAction gdtAction = new GdtAction();
        gdtAction.setAction_time(DateUtil.nowDate().getTime()/1000);
        gdtAction.setAction_type(GdtTokenEnum.GDT_ACTION_TYPE.getWord());
        gdtAction.setUser_id(userId);
        GdtRequestModel gdtRequestModel = new GdtRequestModel();
        gdtRequestModel.setAccount_id(GdtTokenEnum.GDT_ACCOUNT_ID_SHUIDICHOU.getWord());
        gdtRequestModel.setUser_action_set_id(GdtTokenEnum.GDT_USER_ACTION_SET_ID_SHUIDICHOU.getWord());
        gdtRequestModel.setActions(Lists.newArrayList(gdtAction));
        return gdtRequestModel;
    }

    /**
     * 获取access_token
     * @return access_token
     */
    @Override
    public String getAccessToken() {
        String accessToken = cfRedissonHandler.get(GdtTokenEnum.GDT_H5_ACCESS_TOKEN_KEYNAME.getWord(), String.class);
        String refreshToken = cfRedissonHandler.get(GdtTokenEnum.GDT_H5_REFRESH_TOKEN_KEYNAME.getWord(),String.class);
        if (StringUtils.isEmpty(accessToken) && StringUtils.isNotEmpty(refreshToken)){
            String accessTokenUriUseRefreshToken = ACCESSTOKENURIUSEREFRESHTOKEN.replace("${client_id}",GdtTokenEnum.GDT_CLIENT_ID_SHUIDICHOU.getWord()).
                    replace("${client_secret}",GdtTokenEnum.GDT_CLIENT_SECRET_SHUIDICHOU.getWord()).
                    replace("${refresh_token}",refreshToken);
            log.info("request_GDT_accessTokenUri:{}",accessTokenUriUseRefreshToken);
            HttpResponseModel model = HttpUtil.httpGet(accessTokenUriUseRefreshToken);
            accessToken = setAccessTokenAndRefreshToken(model);
        }else if(StringUtils.isEmpty(accessToken) && StringUtils.isEmpty(refreshToken)){
            String authorizationCode = getAuthorizationCode();
            log.info("get_GDT_authorizationCode:{}",authorizationCode);
            if (StringUtils.isNotBlank(authorizationCode)){
                String accessTokenUri = ACCESSTOKENURIUSEAUTHORIZATIONCODE.replace("${client_id}",GdtTokenEnum.GDT_CLIENT_ID_SHUIDICHOU.getWord()).
                        replace("${client_secret}",GdtTokenEnum.GDT_CLIENT_SECRET_SHUIDICHOU.getWord()).
                        replace("${authorization_code}",authorizationCode).
                        replace("${redirect_uri}",GdtTokenEnum.GDT_H5_REDIRECT_URI.getWord());
                log.info("request_GDT_accessTokenUri:{}",accessTokenUri);
                HttpResponseModel model = HttpUtil.httpGet(accessTokenUri);
                accessToken = setAccessTokenAndRefreshToken(model);
            }
        }
        return accessToken;
    }

    /**
     * 获取access_token 需要先获取AuthorizationCode
     * @return Authorization_code
     */
    private String getAuthorizationCode() {
        String authorizationCode = cfRedissonHandler.get(GdtTokenEnum.GDT_H5_AUTHORIZATION_CODE_KEYNAME.getWord(), String.class);
        if (StringUtils.isEmpty(authorizationCode) ){
            String authorizationCodeUri = AUTHORIZATIONCODEURI.replace("${client_id}",GdtTokenEnum.GDT_CLIENT_ID_SHUIDICHOU.getWord())
                    .replace("${redirect_uri}",GdtTokenEnum.GDT_H5_REDIRECT_URI.getWord());
            log.info("request_GDT_authorizationCodeUri:{}",authorizationCodeUri);
            HttpUtil.httpGet(authorizationCodeUri);
            authorizationCode = cfRedissonHandler.get(GdtTokenEnum.GDT_H5_AUTHORIZATION_CODE_KEYNAME.getWord(), String.class);
        }
        log.info("request_GDT_authorizationCode:{}",authorizationCode);
        return authorizationCode;
    }

    /**
     * 设置token
     * @param model
     * @return
     */
    private String setAccessTokenAndRefreshToken(HttpResponseModel model) {
        String accessToken;
        String refreshToken;
        GdtResponseModel gdtResponseModel = JSON.parseObject(model.getBodyString(), GdtResponseModel.class);
        log.info("getGdtAccessToken_gdtResponseModel:{}",JSONObject.toJSONString(gdtResponseModel));
        if (gdtResponseModel == null || GDTSUCCESSCODE != gdtResponseModel.getCode()){
            return null;
        }
        if (gdtResponseModel.getData() == null){
            return null;
        }
        accessToken = gdtResponseModel.getData().getAccess_token();
        Long accessTokenExpires = gdtResponseModel.getData().getAccess_token_expires_in() * 1000L;
        refreshToken = gdtResponseModel.getData().getRefresh_token();
        Long refreshTokenExpires = null;
        if (gdtResponseModel.getData().getRefresh_token_expires_in() != null){
            refreshTokenExpires = gdtResponseModel.getData().getRefresh_token_expires_in() * 1000L;
        }
        cfRedissonHandler.setEX(GdtTokenEnum.GDT_H5_ACCESS_TOKEN_KEYNAME.getWord(),accessToken,accessTokenExpires!=null?accessTokenExpires-5:ACCESSTOKENEXPIRES);
        cfRedissonHandler.setEX(GdtTokenEnum.GDT_H5_REFRESH_TOKEN_KEYNAME.getWord(),refreshToken,refreshTokenExpires!=null?refreshTokenExpires-5:REFRESHTOKENEXPIRES);
        return accessToken;
    }

}
