package com.shuidihuzhu.cf.cfgrowthtoolapi.service.channel;


import com.shuidihuzhu.cf.domain.dedicated.CrowdfundingVolunteerInviteUserRecordDO;

/**
 * @author: wanghui
 * @create: 2019/4/30 11:03 AM
 */
public interface IVolunteerInviteRecordService {
    int saveCrowdfundingVolunteerInviteUserRecord(CrowdfundingVolunteerInviteUserRecordDO crowdfundingVolunteerInviteUserRecordDO);

    String getlatelyVolunteerUniqueCodeByUserId(Long userId);

    String getlatelyBdVolunteerUniqueCodeByUserId(long userId);
}
