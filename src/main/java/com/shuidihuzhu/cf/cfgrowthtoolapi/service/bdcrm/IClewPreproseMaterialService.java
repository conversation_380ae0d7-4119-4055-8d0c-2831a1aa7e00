package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.context.CaseRaiseContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportApproveRecord;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ClewPreproseMaterialEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.ClewCrowdfundingReportBaseVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.PreposeLeaderSearchParam;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.growthtool.model.*;

import java.util.*;

/**
 * @author: wanghui
 * @create: 2019/9/4 11:01 AM
 */
public interface IClewPreproseMaterialService {
    void saveOrUpdateClewCrowdfundingReportRelation(ClewCrowdfundingReportRelation clewCrowdfundingReportRelation, boolean needConfirm);

    ClewCrowdfundingReportRelation getByPreposeMaterialId(long preposeMaterialId);

    void saveClewCrowdfundingReportApproveRecord(ClewCrowdfundingReportApproveRecord clewCrowdfundingReportApproveRecord);

    List<ClewCasePreproseMaterialRelationModel> getClewCasePreproseMaterialRelationModelByInfoIds(List<Long> infoIds, String uniqueCode);

    List<ClewCrowdfundingReportRelation> getByPreposeMaterialIds(List<Long> preposeMaterialIds);

    List<ClewCasePreproseMaterialRelationModel> getClewCasePreproseMaterialRelationModelByClewIds(List<Long> clewIds);

    List<ClewCasePreproseMaterialRelationModel> getClewCasePreproseMaterialRelationModelByMisAndTime(String mis, String startTime, String endTime);

    List<Long> getPreposeMaterialIdByCreateTimeAndUniqueCodeAndApproveStatusList(Date createTime,
                                                                                 String uniqueCode,
                                                                                 List<Integer> approveStatusList);

    List<Long> getPreposeMaterialIdByCreateTimeAndUniqueCodeAndApproveStatus(String createTime,
                                                                             String uniqueCode,
                                                                             Integer approveStatus);

    /**
     * 查询案例报备草稿  列表   approveStatus=0
     * @author: wanghui
     * @time: 2019/9/4 3:19 PM
     * @description: getCaseReportDraftByUniqueCode
     * @param: [createTime, uniqueCode]
     * @return: java.util.List<com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.ClewCrowdfundingReportBaseVo>
     */
    List<ClewCrowdfundingReportBaseVo> getCaseReportDraftByUniqueCode(Date createTime,
                                                                      String uniqueCode, int pageSize, int pageNo);

    int updateIsDeleteByProposeMaterialId(long proposeMaterialId);

    List<Long> getApproveRecordIdList(String uniqueCode, Date createTime,int roleCode);

    long getApproveRecordCount( List<Integer> approveStatusList, List<Long> approveRecordIdList);

    ClewCrowdfundingReportApproveRecord getLatestApproveRecordByMisAndPreposeMaterialId(String uniqueCode,
                                                                                        long preposeMaterialId,
                                                                                        int approveStatus);

    List<ClewPreproseMaterialResult> getApproveLog(List<Long> preposeMaterialIds);

    ClewCrowdfundingReportApproveRecord getLastApproveTime(Long preposeMaterialId);

    List<Long> getReportedInfoIdsByInfoIds(String volunteerUniqueCode, List<Long> infoIds, Date before7Day, Date after7Day);

    void createApproveRecordForCommonLeader(String mis, String name, String uniqueCode, long preposeMaterialId);

    Long getReposeMaterialIdForFuWuByClewId(Long clewId);

    List<ClewPreproseMaterialResultForClew> getApproveLogByClewIds(List<Long> clewIds);

    ClewCrowdfundingReportRelation fullInfoId(ClewCrowdfundingReportRelation reportRelation, String mobile);

    ClewCrowdfundingReportRelation fullInfoId(ClewCrowdfundingReportRelation reportRelation, PreposeMaterialModel.MaterialInfoVo materialInfoVo);


    int updateInfoIdByPreposeMaterialId(Long preposeMaterialId,
                                        Long infoId);

    int updateInfoIdByPreposeMaterialIdByType( Long preposeMaterialId,
                                               Long infoId,
                                               int type);

    PreposeMaterialStatusModel getPreposeMaterialIsCommitAndApproved(Long preposeMaterialId);

    ClewCrowdfundingReportRelation getLatelyReportedRelationByInfoId(int caseId);

    List<ClewCrowdfundingReportRelation> getReportedRelationsByInfoId(int caseId);

    List<ClewCrowdfundingReportRelation> getMaterialIdByCaseIds(List<Integer> caseIds);

    CommonResultModel<ClewCrowdfundingReportRelation> getPreposeMaterialList(List<Long> preposeMaterialIds, List<String> uniqueCodeList, Integer caseStatus, Integer remoteRaise, Integer communicationWay, String startTime, String endTime, int launchType, int pageNo, int pageSize);

    long getBdReportRelationByConfirmStatusCount(String uniqueCode, int confirmStatus, Date beforeDay);

    List<ClewCrowdfundingReportRelation> getBdReportRelationByConfirmStatusList(String uniqueCode, int confirmStatus, Date beforeDay, int pageSize, int pageNo);

	long getLeaderReportRelationByConfirmStatusCount(List<String> uniqueCodeList,
                                                     PreposeLeaderSearchParam searchParam,
                                                     Date beforeDay);

	List<ClewCrowdfundingReportRelation> getLeaderReportRelationByConfirmStatusList(List<String> uniqueCodeList,
                                                                                    PreposeLeaderSearchParam searchParam,
                                                                                    Date beforeDay,
                                                                                    int pageSize,
                                                                                    int pageNo);

    List<ClewCrowdfundingReportApproveRecord> getApproveRecordByPreposeMaterialIds(String uniqueCode,
                                                                                   List<Long> preposeMaterialIds,
                                                                                   int roleCode);

    void updateReportStatus(int infoId, int reportStatus);

    List<ClewCrowdfundingReportRelation> getNeedRepareReportStatusList(long id);

	List<ClewCrowdfundingReportRelation> getSecondNeedRepareReportStatusList(long id);

	int batchUpdateReportStatus(List<ClewCrowdfundingReportRelation> reportRelationList);

	ClewCrowdfundingReportRelation getReportedRelationsByInfoIdWithUniqueCode(int infoId, String uniqueCode);

	void syncRaiseInfoTBDCaseInfo(int caseId, long preposeMaterialId);

    CaseRaiseContext bindPreposeMaterial(CrowdfundingInfo crowdfundingInfo);

    long countThisMonthByUniqueCode(String uniqueCode);

    List<ClewCrowdfundingReportRelation> listByCursor(long beginId, int limit);

    void updateAreaOrgIdAndSpecialReport(long id, int areaOrgId, int specialReport);

    List<ClewCrowdfundingReportRelation> listByTimeRange(Date startTime, Date endTime, int offset, int limit);

    /**
     * key:id value:待录入状态
     * 获取当前的待录入状态
     */
    Map<Long, ClewPreproseMaterialEnums.ReportStatusEnum> obtainRealReportStatus(List<ClewCrowdfundingReportRelation> reportRelationList);

    List<ClewCrowdfundingReportRelation> getReportedRelationsByInfoIds(List<Integer> caseIds);

    /**
     * 获取代录入手机号
     * @param caseId
     * @return
     */
    String getPreposePhoneByCaseId(int caseId);

    List<Long> listCaseIdByUniqueCodeWithCreateTimeWithRemoteRaise(String uniqueCode, Date monthDate, int remoteRaise);

    void updateTaskId(long clewId, long taskId);

    void updateReportStatusById(long id, int reportStatus);
}
