package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfOperatingRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiBdScoreDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum.RoleEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKpiBdScoreSubmitModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiBdScoreTempService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiStatDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
@Service
@Slf4j
public class CfKpiStatDataServiceImpl implements ICfKpiStatDataService {


    @Autowired
    private ICfVolunteerService cfVolunteerService;


    @Autowired
    private IOperateLogService operateLogService;

    @Autowired
    private ICrmOrganizationRelationService orgRelationService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private ICfKpiBdScoreTempService cfKpiBdScoreTempService;

    @Autowired
    private ApplicationService applicationService;

    public static final String ALARM_ORG_NAME_PREFIX = "线下服务-直营城市-";

    public static final String FEISHU_ALARM_GROUP = "139561f2-7970-48ff-9706-bac022fe49c1";


    @Override
    public OpResult<List<CfKpiBdScoreSubmitModel>> queryKpiScoreStatus(String monthKey, String name, int commitStatus, int level) {
        List<CfKpiBdScoreSubmitModel> result = this.buildAllBdScoreModel(monthKey);
        if (CollectionUtils.isEmpty(result)) {
            return OpResult.createSucResult(result);
        }
        if (StringUtils.isNotEmpty(name)) {
            result = result.stream().filter(item -> item.getScoreName().contains(name)).collect(Collectors.toList());
        }
        if (commitStatus > 0) {
            result = result.stream().filter(item -> commitStatus == item.getCommitStatus()).collect(Collectors.toList());
        }
        if (level > 0) {
            result = result.stream().filter(item -> level == item.getLevel()).collect(Collectors.toList());
        }

        result.sort(Comparator.comparing(CfKpiBdScoreSubmitModel::getOrgChain));
        return OpResult.createSucResult(result);
    }

    //添加一个报警信息，大致格式为: 筹线下打分人员未提交, 路径：线下组织-XXX,人员：XX(),XX()
    @Override
    public void addAlarmInfo(String monthKey) {
        log.info("开始检查筹线下打分人员未提交");
        //同上,也是查询所有人员,然后根据提交状态进行过滤
        //过滤空组织和测试组织
        List<CfKpiBdScoreSubmitModel> result = this.buildAllBdScoreModel(monthKey)
                .stream()
                .filter(item -> item.getCommitStatus() == 1)
                .filter(item -> StringUtils.isNotEmpty(item.getOrgChain()))
                .filter(item-> item.getOrgChain().contains(ALARM_ORG_NAME_PREFIX))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        //再次检查下是否真没有提交,如果自己都已经被打分了,大概率是提交了但是没有记录
        List<String> unSubmitUniqueCode = result.stream().map(CfKpiBdScoreSubmitModel::getUniqueCode).collect(Collectors.toList());
        //去cf_kpi_bd_score_temp中查询
        List<String> hasScoreUniqueCodeList = cfKpiBdScoreTempService.listKpiBdByMonthAndUniqueCodes(monthKey, unSubmitUniqueCode)
                .stream()
                .map(CfKpiBdScoreDO::getUniqueCode)
                .collect(Collectors.toList());
        //链路越长的越在前面
        List<CfKpiBdScoreSubmitModel> hasSortResult = result.stream()
                .filter(item -> !hasScoreUniqueCodeList.contains(item.getUniqueCode()))
                .sorted((item1, item2) -> item2.getOrgChain().split("-").length - (item1.getOrgChain().split("-").length))
                .collect(Collectors.toList());

        //还有一种情况是一个组有多个人员,这种也需要排除掉
        //按照组织进行分组
        Map<String, List<String>> chainTNameList = Maps.newHashMap();
        for (CfKpiBdScoreSubmitModel cfKpiBdScoreSubmitModel : hasSortResult) {
            List<String> keys = chainTNameList.keySet().stream()
                    .filter(item -> item.contains(cfKpiBdScoreSubmitModel.getOrgChain()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(keys)) {
                keys.forEach(item -> chainTNameList.get(item).add(cfKpiBdScoreSubmitModel.getScoreName()));
            } else {
                chainTNameList.computeIfAbsent(cfKpiBdScoreSubmitModel.getOrgChain(), k -> Lists.newArrayList()).add(cfKpiBdScoreSubmitModel.getScoreName());
            }
        }
        if (MapUtils.isNotEmpty(chainTNameList)) {
            StringBuilder alarmInfo = new StringBuilder(monthKey + "筹线下打分人员未提交");
            for (Map.Entry<String, List<String>> entry : chainTNameList.entrySet()) {
                String orgChain = entry.getKey();
                List<String> nameList = entry.getValue();
                alarmInfo.append("路径：").append(orgChain).append(";人员：").append(String.join(",", nameList)).append("\n");
            }
            //生产环境才推送消息
            log.warn(alarmInfo.toString());
            if (applicationService.isProduction()) {
                AlarmBotService.sentText(FEISHU_ALARM_GROUP, alarmInfo.toString(), null, null);
            }
        }
    }



    private List<CfKpiBdScoreSubmitModel> buildAllBdScoreModel(String monthKey) {
        List<CfKpiBdScoreSubmitModel> result = Lists.newArrayList();
        List<Integer> levels = RoleEnum.needStatKpiScoreLevel();
        //全部人员
        List<CrowdfundingVolunteer> volunteerList = cfVolunteerService.listAllOnWorkVolunteerByLevels(levels);
        Map<String, CrowdfundingVolunteer> volunteerMap = volunteerList.stream().collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity(), (oldVal, newVal) -> newVal));
        List<BdCrmOrgUserRelationDO> relationDOList = orgRelationService.listByUniqueCodes(volunteerList.stream().map(CrowdfundingVolunteer::getUniqueCode).distinct().collect(Collectors.toList()));
        Map<String, List<BdCrmOrgUserRelationDO>> relationMap = relationDOList.stream().collect(Collectors.groupingBy(BdCrmOrgUserRelationDO::getUniqueCode));
        Map<Long, String> orgMap = crmSelfBuiltOrgReadService.listChainByOrgIdsWithDefaultSplitter(relationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).distinct().collect(Collectors.toList()));

        //已提交人员
        List<CfOperatingRecordDO> operateRecordList = operateLogService.listOptLogByOptKeyAndOptType(monthKey, OperateTypeEnum.JIXIAO_SUBMIT);
        Map<Long, List<CfOperatingRecordDO>> recordMap = operateRecordList.stream().collect(Collectors.groupingBy(CfOperatingRecordDO::getOperateUserId));
        //Set<Long> leafOrgId = Sets.newHashSet();
        for (String key : volunteerMap.keySet()) {
            CfKpiBdScoreSubmitModel cfKpiBdScoreSubmitModel = new CfKpiBdScoreSubmitModel();
            CrowdfundingVolunteer volunteer = volunteerMap.get(key);
            cfKpiBdScoreSubmitModel.setMonthKey(monthKey);
            cfKpiBdScoreSubmitModel.setUniqueCode(volunteer.getUniqueCode());
            cfKpiBdScoreSubmitModel.setLevel(volunteer.getLevel());
            cfKpiBdScoreSubmitModel.setScoreName(volunteer.getVolunteerName());
            List<BdCrmOrgUserRelationDO> orgUserRelations = relationMap.get(key);
            if (CollectionUtils.isNotEmpty(orgUserRelations)) {
                //leafOrgId = findAllLeafOrg(orgUserRelations);
                BdCrmOrgUserRelationDO userRelation = findShortestPath(orgMap, orgUserRelations);
                if (Objects.nonNull(userRelation)) {
                    cfKpiBdScoreSubmitModel.setOrgId(userRelation.getOrgId());
                    cfKpiBdScoreSubmitModel.setOrgChain(orgMap.get(userRelation.getOrgId()));
                } else {
                    cfKpiBdScoreSubmitModel.setOrgChain("");
                }
            } else {
                cfKpiBdScoreSubmitModel.setOrgChain("");
            }

            List<CfOperatingRecordDO> cfOperatingRecordList = recordMap.get(volunteer.getId());
            if (CollectionUtils.isNotEmpty(cfOperatingRecordList)) {
                Date commitTime = cfOperatingRecordList.stream().max(Comparator.comparing(CfOperatingRecordDO::getId)).get().getCreateTime();
                cfKpiBdScoreSubmitModel.setCommitStatus(2);
                cfKpiBdScoreSubmitModel.setCommitTime(commitTime);
            } else {
                cfKpiBdScoreSubmitModel.setCommitStatus(1);
            }
            result.add(cfKpiBdScoreSubmitModel);
        }
        return result;
    }

    private BdCrmOrgUserRelationDO findShortestPath(Map<Long, String> orgMap, List<BdCrmOrgUserRelationDO> orgUserRelations) {

        if (orgUserRelations.size() == 1) {
            return orgUserRelations.get(0);
        }
        return orgUserRelations.stream().min(Comparator.comparing(item -> Optional.ofNullable(orgMap.get(item.getOrgId()))
                .map(value -> value.split("-").length).orElse(Integer.MAX_VALUE))).orElse(null);
    }


}
