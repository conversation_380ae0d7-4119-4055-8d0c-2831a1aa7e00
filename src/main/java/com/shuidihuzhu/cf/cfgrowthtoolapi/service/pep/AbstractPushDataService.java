package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.IBdMemberSnapshotService;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.client.performance.PepClient;
import com.shuidihuzhu.common.web.model.Response;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: fengxuan
 * @create 2023-03-20 11:12
 **/
@Slf4j
public abstract class AbstractPushDataService implements IPushDataBaseService {

    @Autowired
    PepClient pepClient;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    protected IBdMemberSnapshotService bdMemberSnapshotService;

    @Autowired
    protected ApolloService apolloService;

    @Autowired
    private PepLotObtainService pepLotObtainService;

    @Override
    public void pushData() {
        List<Long> lotIds = pepLotObtainService.getLotIds(getPushEnum());
        lotIds = lotIds.stream().filter(item -> item > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lotIds)) {
            log.info(this.getClass().getSimpleName() + ":批次id默认为空");
            return;
        }
        log.info(this.getClass().getSimpleName() + ":推送批次数据:{}", lotIds);
        for (Long lotId : lotIds) {
            Response<LotInfo> lotResponse = pepClient.getLotInfoById(lotId);
            log.info(this.getClass().getSimpleName() + ":获取批次id:{},response:{}", lotId, lotResponse);
            if (lotResponse.notOk() || lotResponse.getData() == null) {
                return;
            }
            LotInfo data = lotResponse.getData();
            //如果不确定使用哪天的数据不要推送
            DateTime whichDayToPush = getWhichDayToPush(data);
            if (whichDayToPush == null) {
                log.info(this.getClass().getSimpleName() + ":批次id:{},不确定使用哪天的数据不要推送", lotId);
                return;
            }
            List<Object> businessData = listBusinessData(whichDayToPush, data);
            for (Object t : businessData) {
                pushBusinessData((JSONObject) JSONObject.toJSON(t));
            }
        }
    }



    protected abstract PepPushEnum getPushEnum();

    //如果不确定使用哪天的数据不要推送
    public DateTime getWhichDayToPush(LotInfo lotInfo) {
        //统一按照批次结束时间来
        DateTime dateTime = new DateTime(lotInfo.getUseDataTime());
        if (dateTime.isAfter(DateTime.now().minusDays(1))) {
            dateTime = DateTime.now().minusDays(1);
        }
        return dateTime;
    }

    //获取业务数据
    protected abstract <T> List<T> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo);


    private void pushBusinessData(JSONObject jsonObject) {
        mqProducerService.pushBusinessData(jsonObject);
    }

    protected long convertToLong(Integer num) {
        return num == null ? 0L : num;
    }

}
