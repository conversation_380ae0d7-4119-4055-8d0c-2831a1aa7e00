package com.shuidihuzhu.cf.cfgrowthtoolapi.service.experiment;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdReportLinkDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl.ClewPreproseMaterialService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolEncryptUtil;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCaseInfoDao;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdReportLinkDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.model.ExperimentHitResult;
import com.shuidihuzhu.client.model.ExperimentParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 通用场景实现
 * @Author: panghairui
 * @Date: 2024/8/5 10:58 AM
 */
@Slf4j
@Service
public class BaseSceneStrategyFunction {

    @Resource
    private CfBdCaseInfoDao cfBdCaseInfoDao;
    @Resource
    private CfBdReportLinkDao cfBdReportLinkDao;
    @Autowired
    private ICrmMemberInfoService crmMemberInfoServiceImpl;
    @Resource
    private ClewPreproseMaterialService clewPreproseMaterialService;
    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegateImpl;

    /**
     * 根据参数查询顾问的UniqueCode
     * @param experimentParam 实验需要的参数
     * @return 顾问的UniqueCode
     */
    public String getUniqueCodeByParam(ExperimentParam experimentParam) {

        if (StringUtils.isNotBlank(experimentParam.getUniqueCode())) {
            return experimentParam.getUniqueCode();
        }

        if (Objects.nonNull(experimentParam.getCaseId())) {
            return getUniqueCodeByCaseId(experimentParam.getCaseId());
        }

        if (StringUtils.isNotBlank(experimentParam.getInfoUuid())) {
            return getUniqueCodeByInfoId(experimentParam.getInfoUuid());
        }

        if (StringUtils.isNotBlank(experimentParam.getEncryptInfo())) {
            return getUniqueCodeByEncryptInfo(experimentParam.getEncryptInfo());
        }

        return "";
    }

    /**
     * 根据城市判断实验是否命中
     * @param uniqueCode 顾问的uniqueCode
     * @param experimentCity apollo所配置的实验城市
     * @return 实验命中结果
     */
    public ExperimentHitResult judgeByUniqueCodeToCity(String uniqueCode, List<String> experimentCity) {
        boolean isHit = crmMemberInfoServiceImpl.isGreyCity(experimentCity, uniqueCode);
        return ExperimentHitResult.builder()
                .isHit(isHit)
                .build();
    }

    /**
     * 根据分区判断实验是否命中
     * @param uniqueCode 顾问的uniqueCode
     * @param experimentPartition apollo所配置的实验分区
     * @return 实验命中结果
     */
    public ExperimentHitResult judgeByUniqueCodeToPartition(String uniqueCode, List<Long> experimentPartition) {
        boolean isHit = crmMemberInfoServiceImpl.isGreyPartition(experimentPartition, uniqueCode);
        return ExperimentHitResult.builder()
                .isHit(isHit)
                .build();
    }


    private String getUniqueCodeByEncryptInfo(String encryptInfo) {

        // 校验正确性
        Long reportId = GrowthtoolEncryptUtil.getReportIdByEncryptInfo(encryptInfo);
        if (reportId == null){
            return "";
        }

        // 查绑定BD
        CfBdReportLinkDO cfBdReportLinkDO = cfBdReportLinkDao.getCfBdReportLinkDoByEncryptInfo(encryptInfo);
        if (Objects.isNull(cfBdReportLinkDO)) {
            return "";
        }

        return cfBdReportLinkDO.getUniqueCode();

    }

    private String getUniqueCodeByInfoId(String infoUuid) {

        CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegateImpl.getCrowdfundingInfoByInfouuid(infoUuid);
        if (Objects.isNull(crowdfundingInfo)) {
            return "";
        }

        return getUniqueCodeByCaseId(crowdfundingInfo.getId());
    }

    private String getUniqueCodeByCaseId(Integer caseId) {

        // 查绑定BD
        CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoDao.getBdCaseInfoByInfoId(caseId);
        if (Objects.isNull(cfBdCaseInfoDo)) {
            return "";
        }

        // 必须得绑定顾问
        ClewCrowdfundingReportRelation crowdfundingReportRelation = clewPreproseMaterialService.getReportedRelationsByInfoIdWithUniqueCode(caseId, cfBdCaseInfoDo.getUniqueCode());
        if (Objects.isNull(crowdfundingReportRelation)) {
            return "";
        }

        return cfBdCaseInfoDo.getUniqueCode();
    }

}
