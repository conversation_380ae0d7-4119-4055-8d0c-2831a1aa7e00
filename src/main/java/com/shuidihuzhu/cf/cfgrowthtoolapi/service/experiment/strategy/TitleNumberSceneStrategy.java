package com.shuidihuzhu.cf.cfgrowthtoolapi.service.experiment.strategy;

import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.experiment.BaseSceneStrategyFunction;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.experiment.SceneStrategy;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.client.model.ExperimentHitResult;
import com.shuidihuzhu.client.model.ExperimentParam;
import com.shuidihuzhu.client.model.enums.ExperimentSceneEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 根据城市实验
 * @Author: panghairui
 * @Date: 2024/7/31 2:53 PM
 */
@Slf4j
@Service
public class TitleNumberSceneStrategy extends BaseSceneStrategyFunction implements SceneStrategy {

    @Resource
    private ApolloService apolloService;

    @Override
    public ExperimentHitResult isHit(ExperimentParam experimentParam) {

        List<Long> experimentPartition = apolloService.getCaseTitlePartitionList();
        if (CollectionUtils.isEmpty(experimentPartition)) {
            backstopResult();
        }

        // 获取绑定顾问
        String uniqueCode = getUniqueCodeByParam(experimentParam);
        log.info("TitleNumberSceneStrategy uniqueCode {}", uniqueCode);
        if (StringUtils.isBlank(uniqueCode)) {
            return backstopResult();
        }

        // 判断命中情况
        return judgeByUniqueCodeToPartition(uniqueCode, experimentPartition);
    }

    @Override
    public Integer getSceneId() {
        return ExperimentSceneEnum.TITLE_NUMBER_EXPERIMENT.getKey();
    }

    /**
     * 兜底
     */
    private ExperimentHitResult backstopResult() {
        return ExperimentHitResult.builder().isHit(false).build();
    }

}
