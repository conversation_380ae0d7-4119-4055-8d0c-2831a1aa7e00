package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.KpiImportDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfBdKpiEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.excel.KpiDonateTargetModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.IKpiImportDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.DonateTargetModel;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2023-03-20 20:46
 **/
@Service
public class DonateTargetDataService extends AbstractPushDataService {


    @Autowired
    private IKpiImportDataService kpiImportDataService;


    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.donate_target_data;
    }

    @Override
    public DateTime getWhichDayToPush(LotInfo lotInfo) {
        return new DateTime(lotInfo.getLotStartTime());
    }

    @Override
    protected List<DonateTargetModel> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        List<KpiImportDataDO> kpiImportDataDOS = kpiImportDataService.listByMonthKey(pushWhichDay.toString(GrowthtoolUtil.ymfmt), CfBdKpiEnums.KpiImportTypeEnum.donate_target.getCode());
        List<DonateTargetModel> result = Lists.newArrayList();
        for (KpiImportDataDO kpiImportDataDO : kpiImportDataDOS) {
            DonateTargetModel donateTargetModel = new DonateTargetModel();
            donateTargetModel.setUserId(kpiImportDataDO.getUniqueKey());
            donateTargetModel.setLotId(lotInfo.getLotId());
            KpiDonateTargetModel kpiDonateTargetModel = JSONObject.parseObject(kpiImportDataDO.getData(), KpiDonateTargetModel.class);
            donateTargetModel.setDoante_target(kpiDonateTargetModel.getDonateTarget());
            result.add(donateTargetModel);
        }
        return result;
    }
}
