package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerAttendInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */
public interface CfPartnerAttendBillService {

    int batchInsert(List<PartnerAttendInfoDTO> list, CfPartnerSnapshotDo cfPartnerSnapshotDo, CfPartnerCycleDo cfPartnerCycleDo);

    List<CfPartnerAttendBillDo> listByCycle(int cycle);

    int deleteByCycleId(long cycleId);

}
