package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.AccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrowdfundingVolunteerCreateCaseRecordExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCaseAttributionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdIntroducerDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.facade.IRegisterBdCrmClewFacade;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CrowdfundingVolunteerCreateCaseRecordBiz;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IBdCrmPageRedirect;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IBdIntroducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackBdCrmFeignClient;
import com.shuidihuzhu.client.cf.growthtool.enums.VolunteerEnums;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-07-10
 */
@Slf4j
@Service
@RefreshScope
public class BdCrmPageRedirectImpl implements IBdCrmPageRedirect {

    @Autowired
    private ICfVolunteerService cfVolunteerServiceImpl;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Resource
    private CfClewtrackBdCrmFeignClient clewtrackBdCrmFeignClient;
    @Resource
    private AccountServiceDelegate accountServiceDelegate;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private IRegisterBdCrmClewFacade registerClewFacadeImpl;
    @Autowired
    private IBdIntroducerService bdIntroducerService;

    @Autowired
    private CrowdfundingVolunteerCreateCaseRecordBiz createCaseRecordBiz;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    /**
     * 用户绑定手机号
     */
    @Override
    public OpResult<Integer> queryPage(long userId, String channel, String clientIp) {
        int redirect = 1;
        CrowdfundingVolunteerCreateCaseRecordExtDO caseRecordExtDO = createCaseRecordBiz.getRecordByUserId(userId);

        if (Objects.isNull(caseRecordExtDO)) {
            //非扫码关注事件
            return OpResult.createSucResult(redirect);
        }
        String uniqueCode = caseRecordExtDO.getVolunteerUnique();
        if (StringUtils.isEmpty(uniqueCode)) {
            //非扫码关注事件
            return OpResult.createSucResult(redirect);
        }
        //没有流转给bd
        UserInfoModel userInfo = accountServiceDelegate.getUserInfoModelByUserId(userId);
        if (userInfo == null) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        //如果没有手机号，需要补全手机号
        if (StringUtils.isNotBlank(userInfo.getCryptoMobile()) && StringUtils.isBlank(caseRecordExtDO.getEncryptPhone())) {
            createCaseRecordBiz.fillPhone(caseRecordExtDO.getId(), userInfo.getCryptoMobile(), CfBdCaseAttributionDO.FillPhoneScenEnum.bind_phone.getCode());
        }
        CrowdfundingVolunteer cfVolunteer = cfVolunteerServiceImpl.getByUniqueCode(uniqueCode);
        if (!isOfflineTeam(cfVolunteer)) {
            //不是线下团队-BD
            return OpResult.createSucResult(redirect);
        }

        boolean isNeedUpdate = caseRecordExtDO.getClewId() <= 0;
        //是线下团队-BD
        if (isHaveNoEndCase(userId)) {
            //有筹款中案例
            if (isNeedUpdate) {
                createCaseRecordBiz.updateNoClewCreateDesc(caseRecordExtDO.getId(), VolunteerEnums.ScanVolunteerCodeNoClew.HAVECASE.getDesc());
            }
            return OpResult.createSucResult(redirect);
        }
        //没有在筹的案例
        if (isHaveClews(userId, uniqueCode)) {
            //线索近30天已经流转给任意bd
            if (isNeedUpdate) {
                createCaseRecordBiz.updateNoClewCreateDesc(caseRecordExtDO.getId(), VolunteerEnums.ScanVolunteerCodeNoClew.EXISTCLEW.getDesc());
            }
            return OpResult.createSucResult(redirect);
        }
        if (StringUtils.isNotEmpty(userInfo.getCryptoMobile())) {
            String scanTime = DateUtil.formatDateTime(caseRecordExtDO.getCreateTime());
            //如果之前扫码时没有绑定手机号需要更新下保护期
            String key = GeneralConstant.VOLUNTEER_KEY + userId;
            //https://wiki.shuiditech.com/pages/viewpage.action?pageId=951977229
            long ttl = redissonHandler.getTTL(key);
            if (StringUtils.isBlank(caseRecordExtDO.getEncryptPhone()) && ttl > 0) {
                redissonHandler.setEX(key, caseRecordExtDO, GrowthtoolUtil.TIMEMILLS);
                scanTime = DateUtil.formatDateTime(new Date());
                log.info("重置下有效期,key:{}原有效期:{}", key, ttl);
            }
            //用户当前账号已经绑定手机号
            registerClewFacadeImpl.sendMq2CfClewtrackUseChannelAndPhone(userId, uniqueCode, ShuidiCipherUtils.decrypt(userInfo.getCryptoMobile()), channel, clientIp, null, String.valueOf(caseRecordExtDO.getId()), scanTime);
            return OpResult.createSucResult(redirect);
        } else {
            //用户没有绑定手机号
            redirect = 2;
            if (isNeedUpdate) {
                createCaseRecordBiz.updateNoClewCreateDesc(caseRecordExtDO.getId(), VolunteerEnums.ScanVolunteerCodeNoClew.NOBINDPHONE.getDesc());
            }
            return OpResult.createSucResult(redirect);
        }
    }

    @Override
    public OpResult registerPhone(long userId, String phone, String clientIp) {
        if (userId<=0){
            OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        //        String uniqueCode = getVolunteerUniqueCodeByUserId(userId);
        CrowdfundingVolunteerCreateCaseRecordExtDO caseRecordExtDO = createCaseRecordBiz.getRecordByUserId(userId);
        if (Objects.isNull(caseRecordExtDO)){
            //非扫码关注事件
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        String uniqueCode = caseRecordExtDO.getVolunteerUnique();
        if (StringUtils.isEmpty(uniqueCode)){
            //非扫码关注事件
            return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        String scanTime = DateUtil.formatDateTime(caseRecordExtDO.getCreateTime());
        registerClewFacadeImpl.sendMq2CfClewtrackUseChannelAndPhone(userId,uniqueCode,phone,"BD_sale_patient_register",clientIp,null,String.valueOf(caseRecordExtDO.getId()),scanTime);
        return OpResult.createSucResult("");
    }

    @Override
    public OpResult registerPhone(long userId, String phone, String clientIp, String introducerEncryMobile) {
        if (StringUtils.isEmpty(introducerEncryMobile)){
            return registerPhone(userId,phone,clientIp);
        }
        CfBdIntroducerDO cfBdIntroducerDO = bdIntroducerService.getCfBdIntroducerDOByEncryMobile(introducerEncryMobile);
        if (cfBdIntroducerDO==null){
            log.info(this.getClass().getSimpleName()+" getUniqueCodeByEncryMobile param:{}  result is empty",introducerEncryMobile);
            return registerPhone(userId,phone,clientIp);
        }
        Map<String,String> map = Maps.newHashMap();
        map.put("introducerName",cfBdIntroducerDO.getIntroducerName());
        map.put("introducerEncryMobile",cfBdIntroducerDO.getIntroducerEncryMobile());
        registerClewFacadeImpl.sendMq2CfClewtrackUseChannelAndPhone(userId,cfBdIntroducerDO.getUniqueCode(),phone,"BD_sale_user_introduction",clientIp,map,null,null);
        return OpResult.createSucResult("");
    }

    /**
     * 判断线索是否近30天已经流转给任意bd
     * @param userId
     * @param uniqueCode
     * @return
     */
    private boolean isHaveClews(long userId, String uniqueCode) {
        //没有在筹案例，判断线索是否近30天已经流转给任意bd
        com.shuidihuzhu.common.web.model.Response<Integer> clewInfos = clewtrackBdCrmFeignClient.getClewInfosbyUserId(userId,uniqueCode);
        //调用失败
        if (clewInfos == null || clewInfos.getCode() != 0 || clewInfos.getData() == null) {
            return true;
        }
        if (clewInfos.getData().equals(0)) {
            //线索近30天没有流转给任意bd
            return false;
        }
        return true;
    }

    /**
     * 是否是线下筹款顾问
     * @param cfVolunteer
     * @return
     */
    private boolean isOfflineTeam(CrowdfundingVolunteer cfVolunteer) {
        if (cfVolunteer != null && cfVolunteer.getVolunteerType() == 1){
            //是线下团队-BD
            return true;
        }
        return false;
    }

    /**
     * 判断是有在筹案例
     */
    private boolean isHaveNoEndCase(long userId){
        FeignResponse<CrowdfundingInfo> feignResponse = crowdfundingFeignClient.getLastByUserId(userId);
        if (feignResponse == null || feignResponse.getCode() !=0 ){
            return true;
        }
        CrowdfundingInfo crowdfundingInfo = feignResponse.getData();
        if (crowdfundingInfo == null){
            //没有案例
            return false;
        }else{
            if (crowdfundingInfo.getEndTime().after(DateUtil.getCurrentTimestamp())){
                //案例结束时间在当前时间之后，说明案例未结束,有在筹案例
                return true;
            }else{
                return false;
            }
        }
    }
}
