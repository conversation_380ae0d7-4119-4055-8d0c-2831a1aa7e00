package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;


import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.WorkStatusEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdLovePartnerParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.LovePartnerInfoParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfRefundPartnerInfoMode;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfWhalePartnerInfoModel;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-30
 */
public interface CfPartnerInfoService {

    /**
     * 根据uniqueCode获取人员信息
     *
     * @param uniqueCode
     * @return
     */
    CfPartnerInfoDo getPartnerInfoByUniqueCode(String uniqueCode);

    /**
     * 根据userId获取人员总数
     *
     * @return
     */
    PartnerCountModel searchPartnerCount(List<BdCrmOrganizationDO> bdOrgModelList, CrowdfundingVolunteer cfVolunteer);

    /**
     * 新增人员录入
     *
     * @param lovePartnerInfoParam
     * @return
     */
    OpResult<Integer> insertPartnerInfo(LovePartnerInfoParam lovePartnerInfoParam, CrowdfundingVolunteer cfVolunteer, long orgId);

    OpResult<Integer> updatePartnerInfo(LovePartnerInfoParam lovePartnerInfoParam, CrowdfundingVolunteer cfVolunteer, long orgId);

    /**
     * 更新审批状态
     *
     * @param approveState
     * @param remark
     * @param approveId
     * @return
     */
    int updateApproveOrPartner(int approveState, String remark, long approveId);

    /**
     * 获取我的爱心伙伴列表
     *
     * @param leaderUniqueCode 负责人标识
     * @param workStatusEnum
     * @return
     */
    List<PartnerInfoListModel> searchMyPartnerInfoList(String leaderUniqueCode, WorkStatusEnum workStatusEnum);

    PartnerInfoModel searchMypartnerInfo(long partnerId);

    List<PartnerInfoListModel> searchApplyPartnerInfo(int approveStatus, String leaderUniqueCode);

    /**
     * 获取团队爱心伙伴组织列表
     *
     * @param orgId 组织标识
     * @return
     */
    List<TeamPartnerModel> searTeamPartnerList(List<Long> orgIds, int level, long orgId);

    CfPartnerInfoDo getPartnerInfoByIdCard(String encryptIdCard);

    /**
     * 获取所有有效的顾问 (审核通过)
     *
     * @return
     */
    List<CfPartnerInfoDo> listAllValidPartner();

    /**
     * 根据获uniqueCodes批量获取兼职信息
     *
     * @param uniqueCodes
     * @return
     */
    List<CfWhalePartnerInfoModel> listWhalePartnerInfoByUniqueCodes(List<String> uniqueCodes);

    /**
     * sea后台爱心伙伴列表总条数
     *
     * @param bdLovePartnerParam
     * @return
     */
    int countLovePartnerInfo(BdLovePartnerParam bdLovePartnerParam);

    /**
     * sea后台爱心伙伴列表数据
     *
     * @param bdLovePartnerParam
     * @return
     */
    List<CfPartnerInfoModel> listLovePartnerInfo(BdLovePartnerParam bdLovePartnerParam);

    /**
     * 根据手机号查询爱心伙伴信息
     *
     * @param encryptPhone
     * @return
     */
    CfPartnerInfoDo getPartnerInfoByEncryptPhone(String encryptPhone);

    /**
     * 发送审批信息
     *
     * @param lovePartnerInfoParam
     * @param approveVolunteer
     */
    void sendApprovePartnerInfoMsg(LovePartnerInfoParam lovePartnerInfoParam, CrowdfundingVolunteer approveVolunteer);

    /**
     * 根据id查爱心伙伴信息
     *
     * @param id
     * @return
     */
    CfPartnerInfoDo getPartnerInfoById(Long id);

    /**
     * 调整爱心伙伴负责人信息
     *
     * @param id
     * @param leaderUniqueCode
     * @param orgId
     * @param orgChainName
     * @param leaderName
     * @return
     */
    int adjustLeaderInfo(Long id, String leaderUniqueCode, long orgId, String orgChainName, String leaderName);

    /**
     * 调整爱心伙伴审批人信息
     *
     * @param id
     * @param approveVolunteer
     * @param approveOrgName
     * @return
     */
    int adjustPartnerApproveName(Long id, CrowdfundingVolunteer approveVolunteer,String approveOrgName);

    /**
     * 批量获取兼职信息
     *
     * @param uniqueCodes
     * @return
     */
    List<CfPartnerInfoDo> listPartnerInfoByUniqueCodes(List<String> uniqueCodes);

    /**
     * 取消合作
     * @param leaderUniqueCode
     * @param uniqueCode
     * @return
     */
    OpResult<Void> cancelCooperation(String leaderUniqueCode, String uniqueCode);

    /**
    * 爱心伙伴历史数据经费类型
     * @param file
     * @return
     */
    OpResult<String> importLovePartnreInfo(MultipartFile file);

    OpResult<CfRefundPartnerInfoMode> getPartnerInfoByTraceNo(String traceNo);
}
