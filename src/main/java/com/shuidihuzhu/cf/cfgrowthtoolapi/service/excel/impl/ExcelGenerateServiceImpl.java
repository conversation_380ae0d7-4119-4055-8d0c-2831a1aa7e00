package com.shuidihuzhu.cf.cfgrowthtoolapi.service.excel.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.service.excel.ExcelGenerateService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.CosUploadUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.excel.ExcelUtil;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.model.ExcelGenerateParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/9/14 3:53 下午
 */
@Slf4j
@Service
public class ExcelGenerateServiceImpl implements ExcelGenerateService {

    @Resource
    private AlarmClient alarmClient;

    @Override
    public void excelGenerate(ExcelGenerateParam excelGenerateParam) {

        if (Objects.isNull(excelGenerateParam)) {
            log.info("ExcelGenerateServiceImpl excelGenerate param is null");
            return;
        }

        if (CollectionUtils.isEmpty(excelGenerateParam.getAlarmUser())
                || excelGenerateParam.getHeaders().length == 0
                || CollectionUtils.isEmpty(excelGenerateParam.getCollections())
                || StringUtils.isBlank(excelGenerateParam.getFileName())) {
            log.info("ExcelGenerateServiceImpl excelGenerate param is not ok");
            return;
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        log.info("ExcelGenerateServiceImpl excelGenerate collections:{}", excelGenerateParam.getCollections());

        try {
            ExcelUtil.exportExcel(excelGenerateParam.getHeaders(), excelGenerateParam.getCollections(), out);
            String url = CosUploadUtil.uploadCosFile(excelGenerateParam.getFileName(), out);
            if (StringUtils.isBlank(url)) {
                log.info("CosUploadUtil.uploadCosFile url is null");
                return;
            }
            log.info("CosUploadUtil.uploadCosFile url:{}", url);
            AlarmBotService.sentText(excelGenerateParam.getAlarmUser().get(0), "您的excel链接已导出完毕! " + url, null, null);
            alarmClient.sendByUser(List.of("panghairui"), "您的excel链接已导出完毕! " + url);
        } catch (Exception e) {
            log.error("ExcelGenerateServiceImpl excelGenerate error {}", e.getMessage());
            AlarmBotService.sentText(excelGenerateParam.getAlarmUser().get(0), "您的excel生成执行有错误! " + e.getMessage(), null, null);
            alarmClient.sendByUser(List.of("panghairui"), "您的excel生成执行有错误! " + e.getMessage());
        }

    }
}
