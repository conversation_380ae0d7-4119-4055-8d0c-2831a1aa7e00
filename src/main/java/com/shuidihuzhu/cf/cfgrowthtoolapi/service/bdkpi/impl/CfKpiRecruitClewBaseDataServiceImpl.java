package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiRecruitClewBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiRecruitClewBaseDataService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiRecruitClewBaseDataDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 绩效招募信息表(CfKpiRecruitClewBaseData)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-31 11:19:43
 */
@Service("cfKpiRecruitClewBaseDataService")
public class CfKpiRecruitClewBaseDataServiceImpl implements ICfKpiRecruitClewBaseDataService {
   
    @Resource
    private CfKpiRecruitClewBaseDataDao cfKpiRecruitClewBaseDataDao;

    @Override
    public CfKpiRecruitClewBaseDataDO queryById(long id) {
        return cfKpiRecruitClewBaseDataDao.queryById(id);
    }
    

    @Override
    public int insert(CfKpiRecruitClewBaseDataDO cfKpiRecruitClewBaseData) {
        return cfKpiRecruitClewBaseDataDao.insert(cfKpiRecruitClewBaseData);
    }

    @Override
    public List<CfKpiRecruitClewBaseDataDO> listRandomizedByDateKey(String dayKey, String startTime, String endTime) {
        if (StringUtils.isBlank(dayKey) || StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return Lists.newArrayList();
        }
        return cfKpiRecruitClewBaseDataDao.listRandomizedByDateKey(dayKey, startTime, endTime);
    }

    @Override
    public List<CfKpiRecruitClewBaseDataDO> listRecruitByDateKey(String dayKey, String startTime, String endTime) {
        if (StringUtils.isBlank(dayKey) || StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return Lists.newArrayList();
        }
        return cfKpiRecruitClewBaseDataDao.listRecruitByDateKey(dayKey, startTime, endTime);
    }

    @Override
    public List<CfKpiRecruitClewBaseDataDO> listByPatientIds(String dateTime, List<Long> patientIds) {
        if (CollectionUtils.isEmpty(patientIds)) {
            return Lists.newArrayList();
        }
        return cfKpiRecruitClewBaseDataDao.listByPatientIds(dateTime, patientIds);
    }

    @Override
    public int updateLeaderInfo(String dayKey, long patientId, String leaderInfo) {
        return cfKpiRecruitClewBaseDataDao.updateLeaderInfo(dayKey, patientId, leaderInfo);
    }

}
