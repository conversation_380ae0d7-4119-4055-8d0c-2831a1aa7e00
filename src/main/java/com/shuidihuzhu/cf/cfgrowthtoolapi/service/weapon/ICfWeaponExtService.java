package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.weapon.CfWeaponExtDO;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-10-14 9:36 下午
 **/
public interface ICfWeaponExtService {

    int insertBatch(List<CfWeaponExtDO> weaponExtDOList);

    int update(CfWeaponExtDO cfWeaponExtDO);

    int batchDelete(int weaponId);

    List<CfWeaponExtDO> listByWeaponId(int weaponId);

}
