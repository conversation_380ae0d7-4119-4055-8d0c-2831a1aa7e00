package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.BdCrmOrganizationModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.BdCrmOrganizationPlateModel;

import java.util.List;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2020-05-11 16:54
 **/
public interface ICrmSelfBuiltOrgReadService {


    //获取所有组织,扁平接口
    List<BdCrmOrganizationDO> getAllOrg();


    /**
     * 获取带有层级的组织
     *
     * @return
     */
    List<BdCrmOrganizationPlateModel> getAllOrgWithLevel();

    /**
     * 从db获取组织信息
     *
     * @return
     */
    List<BdCrmOrganizationDO> getAllOrgByDB();

    /**
     * 树状结构获取所有组织,只给sea后台使用
     * {@link BdCrmOrganizationModel#buildByDOList(java.util.List)}
     *
     * @return
     */
    BdCrmOrganizationModel getAllOrgAsTree();


    BdCrmOrganizationModel getAllOrgAsTree(boolean noShowTest);


    /**
     * 找到直接下级节点
     *
     * @param orgId
     * @return
     */
    List<BdCrmOrganizationDO> findDirectSubOrgByOrgId(long orgId);


    List<BdCrmOrganizationDO> findDirectSubOrgByOrgIdList(List<Long> orgIdList);

    /**
     * 包含orgId对应的BdCrmOrganizationDO
     * 根据orgId查询父节点链路,返回结果按照 首层->第二层->...->orgId对应的组织
     * 从顶层节点到查找节点的路径,如果需要获取名称只需要对这个做转化
     *
     * @return
     */
    List<BdCrmOrganizationDO> listParentOrgAsChain(long orgId);

    List<BdCrmOrganizationDO> listParentOrgAsChainOrder(long orgId);


    Map<Long, String> listChainByOrgIds(List<Long> orgIds, String splitter);


    /**
     * splitter 为“-”的listChainByOrgIds方法
     */
    Map<Long, String> listChainByOrgIdsWithDefaultSplitter(List<Long> orgIds);


    /**
     * 查找节点下的所有子节点,包含下级节点的下级节点,包含它本身
     *
     * @param orgId
     * @return
     */
    List<BdCrmOrganizationDO> listAllSubOrgIncludeSelf(long orgId);

    /**
     * 查找节点下的所有子节点,包含下级节点的下级节点,不包含它本身
     *
     * @param orgId
     * @return
     */
    List<BdCrmOrganizationDO> listAllSubOrgExcludeSelf(long orgId);

    /**
     * 获取当前组织信息，主要是用来校验当前组织是否存在
     *
     * @param orgId
     * @return
     */
    BdCrmOrganizationDO getCurrentOrgById(long orgId);

    /**
     * 排除测试区域
     *
     * @param orgName
     * @return
     */
    List<BdCrmOrganizationDO> listOrgByName(String orgName);


    Map<Long, BdCrmOrganizationDO> listOrgInfo(List<Long> orgIds);


    List<BdCrmOrganizationDO> getOrgInfoList(List<Long> orgIds);

    BdCrmOrganizationDO getParentOrg(long orgId);

    /**
     * 是否是展示组织还是人员信息,true展示组织,fasle展示人员
     *
     * @param orgIds
     * @return
     */
    boolean needShowOrg(List<Integer> orgIds);


    List<Long> queryOrgIdListByCities(List<String> queryCities);

    /**
     * orgIds的所有下级组织其他orgId
     *
     * @param orgIds
     * @return false 不包含, true 包含, message 中返回第一个包含的orgId
     */
    boolean subOrgsContainsEachOther(List<Integer> orgIds);

    List<BdCrmOrganizationDO> getOrgByLikeOrgName(String likeValue);

    /**
     * 返回所有需要查询的组织id, 当contextOrgList包含57时,返回空列表
     *
     * @param contextOrgList：一般为BdCrmContextUtil.getBdOrgList()
     * @return
     */
    List<Integer> listOrgIdsForQueryParam(List<BdCrmOrganizationDO> contextOrgList);

    List<Long> listAllTestOrg();

    //展示非测试组
    List<BdCrmOrganizationDO> listNotTestOrg();

    //获取所有蜂鸟计划组织以及下级组织
    List<Long> listAllPartnerOrg();

    //获取所有自营组织及以下组织
    List<Long> listAllOwnOrg();

    //获取所有顾问生态运营组织及以下组织
    List<Long> listAllConsultantEcologicalOperationOrg();

    //获取所有水滴测试大区组织及以下组织
    List<Long> listAllTest();


}
