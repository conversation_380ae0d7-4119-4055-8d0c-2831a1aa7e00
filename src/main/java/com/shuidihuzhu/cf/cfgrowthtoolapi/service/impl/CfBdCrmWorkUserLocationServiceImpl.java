package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.param.WorkUserLocationParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCrmWorkUserLocationService;
import com.shuidihuzhu.cf.dao.bdcrm.CfWorkUserLocationDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: yangliming
 * @create: 2019/12/25
 */
@Service
public class CfBdCrmWorkUserLocationServiceImpl implements ICfBdCrmWorkUserLocationService {

    @Autowired
    private CfWorkUserLocationDao cfWorkUserLocationDao;


    @Override
    public int addWorkUserLocation(WorkUserLocationParam param) {
        return cfWorkUserLocationDao.addWorkUserLocation(param);
    }


}
