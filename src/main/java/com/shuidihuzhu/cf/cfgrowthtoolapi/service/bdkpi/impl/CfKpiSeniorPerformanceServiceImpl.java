package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiSeniorPerformanceDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiSeniorPerformanceService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiSeniorPerformanceDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * kpi-老员工人效数据(CfKpiSeniorPerformance)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-18 11:39:10
 */
@Service("cfKpiSeniorPerformanceService")
public class CfKpiSeniorPerformanceServiceImpl implements ICfKpiSeniorPerformanceService {
   
    @Resource
    private CfKpiSeniorPerformanceDao cfKpiSeniorPerformanceDao;

    @Override
    public CfKpiSeniorPerformanceDO queryById(long id) {
        return cfKpiSeniorPerformanceDao.queryById(id);
    }
    

    @Override
    public int insert(CfKpiSeniorPerformanceDO cfKpiSeniorPerformanceDO) {
        //查询当天+uniqueCode的数据是否有重复
        CfKpiSeniorPerformanceDO dbData = cfKpiSeniorPerformanceDao.queryByDayKeyAndUniqueCode(cfKpiSeniorPerformanceDO.getDayKey(), cfKpiSeniorPerformanceDO.getUniqueCode());
        if (dbData != null) {
            cfKpiSeniorPerformanceDO.setId(dbData.getId());
            return update(cfKpiSeniorPerformanceDO);
        }
        return cfKpiSeniorPerformanceDao.insert(cfKpiSeniorPerformanceDO);
    }

    @Override
    public int update(CfKpiSeniorPerformanceDO cfKpiSeniorPerformanceDO) {
        return cfKpiSeniorPerformanceDao.update(cfKpiSeniorPerformanceDO);
    }

    @Override
    public List<CfKpiSeniorPerformanceDO> listByDayKey(String dayKey) {
        return cfKpiSeniorPerformanceDao.listByDayKey(dayKey);
    }

}
