package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.context.CaseRaiseContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.CfMaterialPreProcessDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfClewtrackDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IPreposeMaterialDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportApproveRecord;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.ClewCrowdfundingReportRelation;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalBuildingDepartmentDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ClewPreproseMaterialEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.CustomEventPublisher;
import com.shuidihuzhu.cf.cfgrowthtoolapi.events.PreposeBindCaseEvent;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.PreposeLeaderSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCrmVolunteerPreposeMaterialCountService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.IClewPreproseMaterialService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalBuildingDepartmentService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.prepose.BdCaseInfoFillService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.BdCrmContextUtil;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.dao.bdcrm.ClewCrowdfundingReportApproveRecordDao;
import com.shuidihuzhu.cf.dao.bdcrm.ClewCrowdfundingReportRelationDao;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.vo.initialaudit.InitialAuditInfoVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/9/4 11:02 AM
 */
@Service
@Slf4j
public class ClewPreproseMaterialService implements IClewPreproseMaterialService {

    @Autowired
    private ClewCrowdfundingReportRelationDao clewCrowdfundingReportRelationDao;
    @Autowired
    private ClewCrowdfundingReportApproveRecordDao clewCrowdfundingReportApproveRecordDao;
    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegateImpl;
    @Autowired
    private IPreposeMaterialDelegate preposeMaterialDelegate;
    @Autowired
    private CfMaterialPreProcessDelegate cfMaterialPreProcessDelegate;
    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private ICfClewtrackDelegate cfClewtrackDelegate;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CustomEventPublisher customEventPublisher;

    @Autowired
    private BdCaseInfoFillService bdCaseInfoFillService;

    @Override
    public void saveOrUpdateClewCrowdfundingReportRelation(ClewCrowdfundingReportRelation clewCrowdfundingReportRelation, boolean needConfirm) {
        ClewCrowdfundingReportRelation reportRelationInDB = this.getByPreposeMaterialId(clewCrowdfundingReportRelation.getPreposeMaterialId());
        if (reportRelationInDB == null) {
            if (clewCrowdfundingReportRelation.getInfoId() != null && clewCrowdfundingReportRelation.getInfoId().intValue() != 0) {
                //   查询当前案例初审状态
                InitialAuditInfoVO initialAuditInfoVO = cfMaterialPreProcessDelegate.selectInitialStatus(clewCrowdfundingReportRelation.getInfoId().intValue());
                if (initialAuditInfoVO == null) {
                    log.warn(this.getClass().getName() + " selectInitialStatus result is null 需要修复数据:caseId:{} ", clewCrowdfundingReportRelation.getInfoId());
                } else {
                    clewCrowdfundingReportRelation.setReportStatus(ClewPreproseMaterialEnums.ReportStatusEnum.parse(initialAuditInfoVO.getInitialAuditStatus()).getStatus());
                }
                // 如果 是新发起报备则 属于发起后录入
                clewCrowdfundingReportRelation.setLaunchType(ClewCrowdfundingReportRelation.LaunchTypeEnum.AFTER.getType());
            } else {
                //如果需要确权先流转为信息待确认
                int reportStatus = needConfirm ? ClewPreproseMaterialEnums.ReportStatusEnum.INFO_WAIT_CONFIRM.getStatus()
                        : ClewPreproseMaterialEnums.ReportStatusEnum.COMMIT_WAIT_CONFIRM.getStatus();
                clewCrowdfundingReportRelation.setReportStatus(reportStatus);
            }
            clewCrowdfundingReportRelationDao.insert(clewCrowdfundingReportRelation);
            customEventPublisher.publish(new PreposeBindCaseEvent(this, clewCrowdfundingReportRelation.getPreposeMaterialId()));
        } else {
            clewCrowdfundingReportRelationDao.update(clewCrowdfundingReportRelation);
        }
        if (clewCrowdfundingReportRelation.getPreposeMaterialId() != null && clewCrowdfundingReportRelation.getInfoId() != null) {
            syncRaiseInfoTBDCaseInfo(clewCrowdfundingReportRelation.getInfoId().intValue(), clewCrowdfundingReportRelation.getPreposeMaterialId());
        }
    }


    @Override
    public ClewCrowdfundingReportRelation getByPreposeMaterialId(long preposeMaterialId){
        return clewCrowdfundingReportRelationDao.getByPreposeMaterialId(preposeMaterialId);
    }

    @Override
    public void saveClewCrowdfundingReportApproveRecord(ClewCrowdfundingReportApproveRecord clewCrowdfundingReportApproveRecord){
        clewCrowdfundingReportApproveRecordDao.insert(clewCrowdfundingReportApproveRecord);
    }

    @Override
    public List<ClewCasePreproseMaterialRelationModel> getClewCasePreproseMaterialRelationModelByInfoIds(List<Long> infoIds,String uniqueCode) {
        if (CollectionUtils.isEmpty(infoIds)){
            return Lists.newArrayList();
        }
        return clewCrowdfundingReportRelationDao.getClewCasePreproseMaterialRelationModelByInfoIds(infoIds,uniqueCode);
    }

    @Override
    public List<ClewCrowdfundingReportRelation> getByPreposeMaterialIds(List<Long> preposeMaterialIds) {
        if (CollectionUtils.isEmpty(preposeMaterialIds)){
            return Lists.newArrayList();
        }
        return clewCrowdfundingReportRelationDao.getByPreposeMaterialIds(preposeMaterialIds);
    }

    @Override
    public List<ClewCasePreproseMaterialRelationModel> getClewCasePreproseMaterialRelationModelByClewIds(List<Long> clewIds) {
        if (CollectionUtils.isEmpty(clewIds)){
            return Lists.newArrayList();
        }
        return clewCrowdfundingReportRelationDao.getClewCasePreproseMaterialRelationModelByClewIds(clewIds);
    }

    @Override
    public List<ClewCasePreproseMaterialRelationModel> getClewCasePreproseMaterialRelationModelByMisAndTime(String mis, String startTime, String endTime) {
        if (StringUtils.isEmpty(mis)) {
            return Lists.newArrayList();
        }
        return clewCrowdfundingReportRelationDao.getClewCasePreproseMaterialRelationModelByMisAndTime(mis, startTime, endTime);
    }


    @Override
    public List<Long> getPreposeMaterialIdByCreateTimeAndUniqueCodeAndApproveStatusList(Date createTime, String uniqueCode, List<Integer> approveStatusList) {
        return clewCrowdfundingReportRelationDao.getPreposeMaterialIdByCreateTimeAndUniqueCodeAndApproveStatusList(createTime, uniqueCode, approveStatusList);
    }

    @Override
    public List<Long> getPreposeMaterialIdByCreateTimeAndUniqueCodeAndApproveStatus(String createTime, String uniqueCode, Integer approveStatus) {
        return clewCrowdfundingReportRelationDao.getPreposeMaterialIdByCreateTimeAndUniqueCodeAndApproveStatus(createTime, uniqueCode, approveStatus);
    }


    @Override
    public List<ClewCrowdfundingReportBaseVo> getCaseReportDraftByUniqueCode(Date createTime, String uniqueCode,int pageSize, int pageNo) {
        long limit = (pageNo - 1) * pageSize;
        return clewCrowdfundingReportRelationDao.getCaseReportDraftByUniqueCode(createTime, uniqueCode, limit, pageSize);
    }

    @Override
    public int updateIsDeleteByProposeMaterialId(long proposeMaterialId){
        clewCrowdfundingReportApproveRecordDao.updateIsDeleteByPreposeMaterialIdWithRoleCode(proposeMaterialId,BdCrmContextUtil.getRoleCode());
        return clewCrowdfundingReportRelationDao.updateIsDeleteByProposeMaterialId(proposeMaterialId);
    }

    @Override
    public List<Long> getApproveRecordIdList(String uniqueCode, Date createTime, int roleCode) {
        return clewCrowdfundingReportApproveRecordDao.getApproveRecordIdList(uniqueCode, createTime, roleCode);
    }

    @Override
    public long getApproveRecordCount(List<Integer> approveStatusList, List<Long> approveRecordIdList){
        List<List<Long>> listList = Lists.partition(approveRecordIdList, GeneralConstant.MAX_PAGE_SIZE);
        long result = listList.parallelStream()
                .map(list -> clewCrowdfundingReportApproveRecordDao.getApproveRecordCount(approveStatusList, list))
                .reduce((total,item) -> total +=item)
                .get();
        return result;
    }
    @Override
    public ClewCrowdfundingReportApproveRecord getLatestApproveRecordByMisAndPreposeMaterialId(String uniqueCode,
                                                                                               long preposeMaterialId,
                                                                                               int approveStatus){
        return clewCrowdfundingReportApproveRecordDao.getLatestApproveRecordByMisAndPreposeMaterialId(uniqueCode, preposeMaterialId, approveStatus);
    }
    @Override
    public ClewCrowdfundingReportApproveRecord getLastApproveTime(Long preposeMaterialId){
        return clewCrowdfundingReportApproveRecordDao.getLastApproveTime(preposeMaterialId);
    }

    @Override
    public List<Long> getReportedInfoIdsByInfoIds(String volunteerUniqueCode, List<Long> infoIds, Date before7Day, Date after7Day) {
        return clewCrowdfundingReportRelationDao.getReportedInfoIdsByInfoIds(volunteerUniqueCode,infoIds,before7Day,after7Day);
    }
    /**
     * @author: wanghui
     * @time: 2019/12/3 3:23 PM
     * @description: getApproveLog  获得每个报备对应的审核记录
     * @param: [preposeMaterialIds]
     * @return: java.util.List<com.shuidihuzhu.client.cf.growthtool.model.ClewPreproseMaterialResult>
     */
    @Override
    public List<ClewPreproseMaterialResult> getApproveLog(List<Long> preposeMaterialIds) {
        List<ClewPreproseMaterialResult> result = Lists.newArrayList();
        List<ClewCrowdfundingReportApproveRecord> approveRecords = clewCrowdfundingReportApproveRecordDao.getApproveLog(preposeMaterialIds);
        if (CollectionUtils.isEmpty(approveRecords)){
            return Lists.newArrayList();
        }
        Map<Long, List<ClewCrowdfundingReportApproveRecord>> preposeMaterialIdMap = approveRecords.stream()
                .collect(Collectors.groupingBy(ClewCrowdfundingReportApproveRecord::getPreposeMaterialId, Collectors.toList()));
        for (Map.Entry<Long, List<ClewCrowdfundingReportApproveRecord>> map:preposeMaterialIdMap.entrySet()){
            List<ApproveRecordModel> approveRecordModels = ClewCrowdfundingReportApproveRecord.fullApproveRecordModel(map.getValue());
            ClewPreproseMaterialResult clewPreproseMaterialResult = new ClewPreproseMaterialResult(map.getKey(),approveRecordModels);
            result.add(clewPreproseMaterialResult);
        }
        return result;
    }
    /**
     * 为普通线下筹款顾问 创建一个审核记录
     * @author: wanghui
     * @time: 2019/9/4 10:09 PM
     * @description: createApproveRecordForCommonLeader
     * @param: [commonVolunteer, preposeMaterialId]
     * @return: void
     */
    @Override
    public void createApproveRecordForCommonLeader(String mis, String name, String uniqueCode, long preposeMaterialId){
        ClewCrowdfundingReportApproveRecord latestApproveRecordByMisAndPreposeMaterialId = this.getLatestApproveRecordByMisAndPreposeMaterialId(uniqueCode, preposeMaterialId, ClewPreproseMaterialEnums.ApproveStatusEnum.APPROVE_ING.getStatus());
        if (latestApproveRecordByMisAndPreposeMaterialId != null) {
            return;
        }
        ClewCrowdfundingReportApproveRecord clewCrowdfundingReportApproveRecord = new ClewCrowdfundingReportApproveRecord();
        clewCrowdfundingReportApproveRecord.setPreposeMaterialId(preposeMaterialId);
        clewCrowdfundingReportApproveRecord.setApproveStatus(ClewPreproseMaterialEnums.ApproveStatusEnum.PASS.getStatus());
        clewCrowdfundingReportApproveRecord.setMis(mis);
        clewCrowdfundingReportApproveRecord.setUniqueCode(uniqueCode);
        clewCrowdfundingReportApproveRecord.setName(name);
        clewCrowdfundingReportApproveRecord.setRoleCode(CrowdfundingVolunteerEnum.RoleEnum.COMMON_LEADER.getLevel());
        clewCrowdfundingReportApproveRecord.setRegectReason("系统默认通过");
        this.saveClewCrowdfundingReportApproveRecord(clewCrowdfundingReportApproveRecord);
    }


    @Override
    public Long getReposeMaterialIdForFuWuByClewId(Long clewId) {
        return clewCrowdfundingReportRelationDao.getReposeMaterialIdForFuWuByClewId(clewId);
    }

    @Override
    public List<ClewPreproseMaterialResultForClew> getApproveLogByClewIds(List<Long> clewIds) {
        List<ClewPreproseMaterialResultForClew> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(clewIds)){
            return result;
        }
        List<ClewCrowdfundingReportRelation> reportRelations = clewCrowdfundingReportRelationDao.getByClewIds(clewIds);
        if (CollectionUtils.isEmpty(reportRelations)){
            return result;
        }
        List<ClewCrowdfundingReportApproveRecord> approveRecords = clewCrowdfundingReportApproveRecordDao.getApproveLog(reportRelations
                .stream().map(ClewCrowdfundingReportRelation::getPreposeMaterialId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(approveRecords)){
            return Lists.newArrayList();
        }
        Map<Long, List<ClewCrowdfundingReportRelation>> preposeMaterialIdMapRelation = reportRelations.stream()
                .collect(Collectors.groupingBy(ClewCrowdfundingReportRelation::getPreposeMaterialId));
        Map<Long, List<ClewCrowdfundingReportApproveRecord>> preposeMaterialIdMapRecord = approveRecords.stream()
                .collect(Collectors.groupingBy(ClewCrowdfundingReportApproveRecord::getPreposeMaterialId, Collectors.toList()));
        for (Map.Entry<Long, List<ClewCrowdfundingReportApproveRecord>> map:preposeMaterialIdMapRecord.entrySet()){
            List<ClewCrowdfundingReportRelation> reportRelationList = preposeMaterialIdMapRelation.get(map.getKey());
            if (CollectionUtils.isEmpty(reportRelationList)){
                continue;
            }
            List<ApproveRecordModelForClew> approveRecordModels = ClewCrowdfundingReportApproveRecord.fullApproveRecordModelForClew(map.getValue(),
                    reportRelationList.get(0).getClewId());
            ClewPreproseMaterialResultForClew clewPreproseMaterialResultForClew = new ClewPreproseMaterialResultForClew(reportRelationList.get(0).getClewId(),
                    approveRecordModels);
            result.add(clewPreproseMaterialResultForClew);
        }
        return result;
    }
    @Override
    public ClewCrowdfundingReportRelation fullInfoId(ClewCrowdfundingReportRelation reportRelation, String mobile){
        if (StringUtils.isBlank(mobile)){
            return reportRelation;
        }
        OpResult<CrowdfundingInfo> opResult = crowdFundingFeignDelegateImpl.getUnEndCaseInfoByRaiseMobile(mobile);
        if (opResult.isSuccessWithNonNullData()){
            //   20200326 与pm沟通为解决 案例是用户自发起还是顾问协助发起   ；顾问协助发起 才与报备关联
            CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoUuid(opResult.getData().getInfoId());
            if (cfBdCaseInfoDo!=null){
                reportRelation.setInfoId(Long.valueOf(opResult.getData().getId()));
            }
        }
        return reportRelation;
    }



    @Override
    public ClewCrowdfundingReportRelation fullInfoId(ClewCrowdfundingReportRelation reportRelation, PreposeMaterialModel.MaterialInfoVo materialInfoVo){
        OpResult<List<Integer>> opResult = preposeMaterialDelegate.selectCaseIds(materialInfoVo);
        if (!opResult.isSuccess()){
            return this.fullInfoId(reportRelation,materialInfoVo.getRaiseMobile());
        }
        List<Integer> caseIds = opResult.getData();
        if (CollectionUtils.isEmpty(caseIds)){
            return reportRelation;
        }
        OpResult<CrowdfundingInfo> unEndCaseByCaseIds = crowdFundingFeignDelegateImpl.getUnEndCaseByCaseIds(caseIds);
        if (unEndCaseByCaseIds.isSuccessWithNonNullData()){
            //   20200326 与pm沟通为解决 案例是用户自发起还是顾问协助发起   ；顾问协助发起 才与报备关联
            CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoUuid(unEndCaseByCaseIds.getData().getInfoId());
            if (cfBdCaseInfoDo!=null){
                reportRelation.setInfoId(Long.valueOf(unEndCaseByCaseIds.getData().getId()));
            }
        }
        return reportRelation;
    }

    @Override
    public int updateInfoIdByPreposeMaterialId(Long preposeMaterialId,
                                               Long infoId){
        int affectRows = updateInfoIdByPreposeMaterialIdByType(preposeMaterialId, infoId, ClewCrowdfundingReportRelation.TypeEnum.XIAN_XIA.getType());
        //绑定了案例id
        customEventPublisher.publish(new PreposeBindCaseEvent(this, preposeMaterialId));
        return affectRows;
    }


    @Override
    public int updateInfoIdByPreposeMaterialIdByType(Long preposeMaterialId, Long infoId, int type) {
        syncRaiseInfoTBDCaseInfo(infoId.intValue(), preposeMaterialId);
        return clewCrowdfundingReportRelationDao.updateInfoIdByPreposeMaterialIdByType(preposeMaterialId, infoId, type);
    }

    @Override
    public PreposeMaterialStatusModel getPreposeMaterialIsCommitAndApproved(Long preposeMaterialId) {
        PreposeMaterialStatusModel preposeMaterialStatusModel = new PreposeMaterialStatusModel(preposeMaterialId,false);
        ClewCrowdfundingReportApproveRecord lasteApprove = clewCrowdfundingReportApproveRecordDao.getLastApproveTime(preposeMaterialId);
        if (lasteApprove!=null
                && lasteApprove.getApproveStatus() == ApproveRecordModelForClew.ApproveStatusEnum.PASS.getStatus() ){
            preposeMaterialStatusModel.setCommitAndApproved(true);
        }
        return preposeMaterialStatusModel;
    }

    @Override
    public ClewCrowdfundingReportRelation getLatelyReportedRelationByInfoId(int caseId) {
        return clewCrowdfundingReportRelationDao.getLatelyReportedRelationByInfoId(caseId);
    }

    @Override
    public List<ClewCrowdfundingReportRelation> getReportedRelationsByInfoId(int caseId) {
        return clewCrowdfundingReportRelationDao.getReportedRelationsByInfoId(caseId);
    }

    @Override
    public List<ClewCrowdfundingReportRelation> getMaterialIdByCaseIds(List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }
        return clewCrowdfundingReportRelationDao.getMaterialIdByCaseIds(caseIds);
    }

    @Override
    public CommonResultModel<ClewCrowdfundingReportRelation> getPreposeMaterialList(List<Long> preposeMaterialIds, List<String> uniqueCodeList,
                                                                                    Integer caseStatus, Integer remoteRaise, Integer communicationWay, String startTime, String endTime,
                                                                                    int launchType, int pageNo, int pageSize) {
        CommonResultModel<ClewCrowdfundingReportRelation> commonResultModel = new CommonResultModel<>();
        int offset = (pageNo-1)*pageSize;
        long total = 0;
        if (CollectionUtils.isNotEmpty(uniqueCodeList)){
            List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
            total = listList.parallelStream().map(list -> clewCrowdfundingReportRelationDao.getCountClewCrowdfundingReportRelationList(preposeMaterialIds,
                    list,caseStatus,remoteRaise,communicationWay,startTime,endTime,launchType)).reduce((sum,item) -> sum += item).get();
        }
        List<ClewCrowdfundingReportRelation> reportRelations = clewCrowdfundingReportRelationDao.getClewCrowdfundingReportRelationList(preposeMaterialIds,
                uniqueCodeList,caseStatus,remoteRaise,communicationWay,startTime,endTime,launchType,offset,pageSize);
        commonResultModel.setTotal(total);
        commonResultModel.setModelList(reportRelations);
        return commonResultModel;
    }

    @Override
    public long getBdReportRelationByConfirmStatusCount(String uniqueCode, int confirmStatus, Date beforeDay) {
        return clewCrowdfundingReportRelationDao.getBdReportRelationByConfirmStatusCount(uniqueCode, confirmStatus, beforeDay);
    }

    @Override
    public List<ClewCrowdfundingReportRelation> getBdReportRelationByConfirmStatusList(String uniqueCode, int confirmStatus, Date beforeDay, int pageSize, int pageNo) {
        int offset = (pageNo-1)*pageSize;
        List<ClewCrowdfundingReportRelation> reportBaseVos = clewCrowdfundingReportRelationDao.getBdReportRelationByConfirmStatusList(uniqueCode, confirmStatus, beforeDay, offset, pageSize);
        return reportBaseVos;
    }
    @Override
    public long getLeaderReportRelationByConfirmStatusCount(List<String> uniqueCodeList,
                                                            PreposeLeaderSearchParam searchParam,
                                                            Date beforeDay){
        long count = clewCrowdfundingReportRelationDao.getLeaderReportRelationByConfirmStatusCount(uniqueCodeList, searchParam.getConfirmStatus(), searchParam.getVolunteerName(),
                searchParam.getPatientName(), searchParam.getPropseMateiralId(), beforeDay);
        return count;
    }
    @Override
    public List<ClewCrowdfundingReportRelation> getLeaderReportRelationByConfirmStatusList(List<String> uniqueCodeList,
                                                                                           PreposeLeaderSearchParam searchParam,
                                                                                           Date beforeDay,
                                                                                           int pageSize,
                                                                                           int pageNo){
        int offset = (pageNo-1)*pageSize;
        List<ClewCrowdfundingReportRelation> reportBaseVos = clewCrowdfundingReportRelationDao.getLeaderReportRelationByConfirmStatusList(uniqueCodeList, searchParam.getConfirmStatus(), searchParam.getVolunteerName(),
                searchParam.getPatientName(), searchParam.getPropseMateiralId(), beforeDay, offset, pageSize);
        return reportBaseVos;
    }

    @Override
    public List<ClewCrowdfundingReportApproveRecord> getApproveRecordByPreposeMaterialIds(String uniqueCode, List<Long> preposeMaterialIds, int roleCode) {
        List<Long> approveRecordIdList = clewCrowdfundingReportApproveRecordDao.getApproveRecordIdListByPreposeMaterialIds(uniqueCode, preposeMaterialIds, roleCode);
        if (CollectionUtils.isEmpty(approveRecordIdList)){
            return Lists.newArrayList();
        }
        return clewCrowdfundingReportApproveRecordDao.getApproveRecordByIds(approveRecordIdList);
    }
    @Override
    public void updateReportStatus(int infoId, int reportStatus) {
        if (infoId == 0) {
            return;
        }
        log.info(this.getClass().getName() + " updateReportStatus infoId:{} reportStatus:{}", infoId, reportStatus);
        clewCrowdfundingReportRelationDao.updateReportStatus(infoId, reportStatus);
    }

    @Override
    public List<ClewCrowdfundingReportRelation> getNeedRepareReportStatusList(long id){
        return clewCrowdfundingReportRelationDao.getNeedRepareReportStatusList(id);
    }
    @Override
    public List<ClewCrowdfundingReportRelation> getSecondNeedRepareReportStatusList(long id){
        return clewCrowdfundingReportRelationDao.getSecondNeedRepareReportStatusList(id);
    }
    @Override
    public int batchUpdateReportStatus(List<ClewCrowdfundingReportRelation> reportRelationList){
        return clewCrowdfundingReportRelationDao.batchUpdateReportStatus(reportRelationList);
    }

    @Override
    public ClewCrowdfundingReportRelation getReportedRelationsByInfoIdWithUniqueCode(int infoId, String uniqueCode) {
        return clewCrowdfundingReportRelationDao.getReportedRelationsByInfoIdWithUniqueCode(infoId,uniqueCode);
    }


    public void syncRaiseInfoTBDCaseInfo(int caseId, long preposeMaterialId) {
        CfBdCaseInfoDo bdCaseInfoDo = new CfBdCaseInfoDo();
        bdCaseInfoDo.setCaseId(caseId);
        bdCaseInfoFillService.fillBdInfoByMaterial(bdCaseInfoDo, preposeMaterialId);
        cfBdCaseInfoService.syncReportInfo(bdCaseInfoDo);
    }


    @Override
    public CaseRaiseContext bindPreposeMaterial(CrowdfundingInfo crowdfundingInfo) {
        CaseRaiseContext caseRaiseContext = CaseRaiseContext.builder()
                .caseId(crowdfundingInfo.getId())
                .crowdfundingInfo(crowdfundingInfo)
                .build();
        CfFirsApproveMaterial authorInfoByInfoId = crowdFundingFeignDelegateImpl.getAuthorInfoByInfoId(crowdfundingInfo.getId());
        if (authorInfoByInfoId == null) {
            log.info(this.getClass().getSimpleName() + " getAuthorInfoByInfoId param:{}  result is null", crowdfundingInfo.getId());
            return caseRaiseContext;
        }
        PreposeMaterialModel.PatientIdCardTypeEnum patientIdCardType = org.apache.commons.lang3.StringUtils.isNotBlank(authorInfoByInfoId.getPatientCryptoIdcard()) ?
                PreposeMaterialModel.PatientIdCardTypeEnum.IDENTITY_CARD :
                PreposeMaterialModel.PatientIdCardTypeEnum.BIRTH_CERTIFICATE;
        String idCard = patientIdCardType == PreposeMaterialModel.PatientIdCardTypeEnum.IDENTITY_CARD ?
                shuidiCipher.decrypt(authorInfoByInfoId.getPatientCryptoIdcard()) :
                authorInfoByInfoId.getPatientBornCard();
        PreposeMaterialModel.MaterialInfoVo materialInfoVo = preposeMaterialDelegate.selectPreposeByIdCard(authorInfoByInfoId.getPatientRealName(),
                idCard, patientIdCardType.getCode());
        if (materialInfoVo == null) {
            return caseRaiseContext;
        }
        caseRaiseContext.setApproveMaterial(authorInfoByInfoId);
        caseRaiseContext.setMaterialInfoVo(materialInfoVo);

        this.updateInfoIdByPreposeMaterialId(materialInfoVo.getId(), (long) crowdfundingInfo.getId());
        this.updateReportStatus(crowdfundingInfo.getId(), ClewPreproseMaterialEnums.ReportStatusEnum.APPROVE_ING.getStatus());
        preposeMaterialDelegate.saveCaseId(materialInfoVo.getId(), (long) crowdfundingInfo.getId());
        ClewCrowdfundingReportRelation reportRelation = this.getByPreposeMaterialId(materialInfoVo.getId());
        if (reportRelation == null) {
            return caseRaiseContext;
        }
        cfClewtrackDelegate.updateClewStatusWithAttachInfo(reportRelation.getClewId());
        caseRaiseContext.setRaisePhoneRelation(new ImmutablePair<String, ClewCrowdfundingReportRelation>(materialInfoVo.getRaiseMobile(), reportRelation));
        return caseRaiseContext;
    }

    @Override
    public long countThisMonthByUniqueCode(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return 0;
        }
        DateTime dateTime = DateTime.now().dayOfMonth().withMinimumValue();
        return clewCrowdfundingReportRelationDao.countThisMonthByUniqueCode(uniqueCode, dateTime.toDate());
    }

    @Override
    public List<ClewCrowdfundingReportRelation> listByCursor(long beginId, int limit) {
        return clewCrowdfundingReportRelationDao.listByCursor(beginId, limit);
    }

    @Override
    public void updateAreaOrgIdAndSpecialReport(long id, int areaOrgId, int specialReport) {
        if (areaOrgId == 0 && specialReport == 0) {
            log.info("id:{}无需更新数据", id);
            return;
        }
        clewCrowdfundingReportRelationDao.updateAreaOrgIdAndSpecialReport(id, areaOrgId, specialReport);
    }

    @Override
    public List<ClewCrowdfundingReportRelation> listByTimeRange(Date startTime, Date endTime, int offset, int limit) {
        return clewCrowdfundingReportRelationDao.listByTimeRange(startTime, endTime, offset, limit);
    }

    @Override
    public Map<Long, ClewPreproseMaterialEnums.ReportStatusEnum> obtainRealReportStatus(List<ClewCrowdfundingReportRelation> reportRelationList) {
        Set<Integer> caseIdSet = reportRelationList.stream()
                .map(item ->  item.getInfoId().intValue())
                .filter(item -> item > 0)
                .collect(Collectors.toSet());
        Map<Integer, CrowdfundingInfo> caseIdTInfoMap = crowdFundingFeignDelegateImpl.getCrowdfundingListById(Lists.newArrayList(caseIdSet))
                .stream()
                .collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (before, after) -> before));
        Map<Long, ClewPreproseMaterialEnums.ReportStatusEnum> result = Maps.newHashMap();
        for (ClewCrowdfundingReportRelation reportRelation : reportRelationList) {
            ClewPreproseMaterialEnums.ReportStatusEnum primitiveStatus = ClewPreproseMaterialEnums.ReportStatusEnum.parse(reportRelation.getReportStatus());
            ClewPreproseMaterialEnums.ReportStatusEnum reportStatusEnum = Optional
                    .ofNullable(primitiveStatus)
                    .orElse(ClewPreproseMaterialEnums.ReportStatusEnum.NO_COMMIT);
            //查看下案例是否存在,如果不存在返回默认的
            if (reportRelation.getInfoId() <= 0) {
                log.debug("relation:{}还未生成案例", reportRelation.getId());
                continue;
            }
            //判断案例是否结束
            CrowdfundingInfo crowdfundingInfo = caseIdTInfoMap.get(reportRelation.getInfoId().intValue());
            if (crowdfundingInfo == null) {
                log.info("relation:{},找不到对应的案例详情", reportRelation.getId());
                continue;
            }
            CfInfoExt cfInfoExt = crowdFundingFeignDelegateImpl.getCfInfoExtById(crowdfundingInfo.getId());
            if (cfInfoExt != null) {
                FirstApproveStatusEnum firstApproveStatus = FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus());
                reportStatusEnum = ClewPreproseMaterialEnums.ReportStatusEnum.parse(firstApproveStatus);
            }
            //确保这块这最下面
            Date endTime = crowdfundingInfo.getEndTime();
            if (endTime != null && endTime.before(new Date())) {
                reportStatusEnum = ClewPreproseMaterialEnums.ReportStatusEnum.END;
            }
            if (!ObjectUtils.nullSafeEquals(primitiveStatus, reportStatusEnum)) {
                log.info("relation caseId:{}更新前的待录入状态为:{},更新后的状态为:{}", reportRelation.getInfoId(), primitiveStatus.getDesc(), reportStatusEnum.getDesc());
                result.put(reportRelation.getInfoId(), reportStatusEnum);
            }
        }
        return result;
    }

    @Override
    public List<ClewCrowdfundingReportRelation> getReportedRelationsByInfoIds(List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }
        return clewCrowdfundingReportRelationDao.getMaterialIdByCaseIds(caseIds);
    }

    @Override
    public String getPreposePhoneByCaseId(int caseId){
        ClewCrowdfundingReportRelation reportRelation = this.getLatelyReportedRelationByInfoId(caseId);
        if (reportRelation==null){
            return null;
        }

        RpcResult<PreposeMaterialModel.MaterialInfoVo> rpcResult = preposeMaterialDelegate.selectMaterialsById(reportRelation.getPreposeMaterialId());
        if (!rpcResult.isSuccess() || rpcResult.getData() == null){
            log.error(this.getClass().getSimpleName()+"selectMaterialsById caseId:{} error",caseId);
            return null;
        }
        return rpcResult.getData().getRaiseMobile();
    }

    @Override
    public List<Long> listCaseIdByUniqueCodeWithCreateTimeWithRemoteRaise(String uniqueCode, Date monthDate, int remoteRaise) {
        return clewCrowdfundingReportRelationDao.listCaseIdByUniqueCodeWithCreateTimeWithRemoteRaise(uniqueCode, monthDate, remoteRaise);
    }

    @Override
    public void updateTaskId(long clewId, long taskId) {
        if (clewId <= 0 || taskId <= 0) {
            return;
        }
        clewCrowdfundingReportRelationDao.updateTaskId(clewId, taskId);
    }

    @Override
    public void updateReportStatusById(long id, int reportStatus) {
        if (id <= 0 || reportStatus <= 0) {
            return;
        }
        clewCrowdfundingReportRelationDao.updateReportStatusById(id, reportStatus);
    }

}
