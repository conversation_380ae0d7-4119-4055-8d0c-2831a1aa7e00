package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bigdata.CfCaseFeaturesDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bigdata.CfCaseFriendShareDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bigdata.CfCaseSampleTypeDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfStatusEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ForwardingGuidelineEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.ValidCaseEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.common.CommonPermissionConfigEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mapper.HomeCaseRemindModelMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.AdminValidCaseConfigDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.HomeCaseRemindModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.HomeCaseRemindVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.ForwardingGuidelineOneModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.ForwardingGuidelineTwoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.ForwardingGuidelineVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.CommonPermissionConfigDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CacheAdminValidCaseConfigService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.common.CommonPermissionConfigService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmOrganizationRelationService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.client.performance.PepTaskClient;
import com.shuidihuzhu.cf.client.performance.enums.FactBizTypeEnum;
import com.shuidihuzhu.cf.client.performance.model.GrowthtoolCasePoolModel;
import com.shuidihuzhu.cf.client.performance.model.GrowthtoolPepCaseScoreModel;
import com.shuidihuzhu.cf.dao.bigdata.CaseFeaturesDao;
import com.shuidihuzhu.cf.dao.bigdata.CaseFriendShareDao;
import com.shuidihuzhu.cf.dao.bigdata.CaseSampleTypeDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/16  14:24
 */
@Service
public class BdCrmForwardingRecipesService {

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegate;

    @Autowired
    private CaseSampleTypeDao cfCaseSampleTypeDao;

    @Autowired
    private CaseFeaturesDao cfCaseFeaturesDao;

    @Autowired
    private CaseFriendShareDao cfCaseFriendShareDao;

    @Autowired
    private IAccountServiceDelegate accountServiceDelegate;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private PepTaskClient pepTaskClient;

    @Autowired
    private CacheAdminValidCaseConfigService cacheAdminValidCaseConfigService;

    @Autowired
    private CommonPermissionConfigService commonPermissionConfigService;

    @Autowired
    private ICrmOrganizationRelationService crmOrganizationRelationService;

    @Autowired
    private ICrmSelfBuiltOrgReadService orgReadService;

    @Autowired
    private HomeCaseRemindModelMapper homeCaseRemindModelMapper;

    //CAR-T治疗>ECMO治疗>移植>手术>NICU>ICU>放化疗>靶向治疗>免疫治疗
    private static final ImmutableMap<String, Integer> TREATMENT_PRIORITY = ImmutableMap.<String, Integer>builder()
            .put("CAR-T治疗", 1)
            .put("ECMO治疗", 2)
            .put("手术", 3)
            .put("ICU", 4)
            .put("放化疗", 5)
            .put("靶向治疗", 6)
            .put("免疫治疗", 7)
            .build();

    //急性白血病/慢性粒细胞白血病/慢性淋巴细胞白血病/淋巴瘤/骨髓瘤/骨髓纤维化/烧伤/骨髓增生异常综合征/烧烫伤后修复/尿毒症/脑出血/恶性肿瘤
    private static final ImmutableMap<String, Integer> DISEASE_PRIORITY = ImmutableMap.<String, Integer>builder()
            .put("急性白血病", 1)
            .put("慢性粒细胞白血病", 2)
            .put("慢性淋巴细胞白血病", 3)
            .put("淋巴瘤", 4)
            .put("骨髓瘤", 5)
            .put("骨髓纤维化", 6)
            .put("烧伤", 7)
            .put("骨髓增生异常综合征", 8)
            .put("烧烫伤后修复", 9)
            .put("尿毒症", 10)
            .put("脑出血", 11)
            .put("恶性肿瘤", 12)
            .build();

    private static final Ordering<String> TREATMENT_ORDERING = Ordering.natural()
            .onResultOf(treatment -> TREATMENT_PRIORITY.getOrDefault(treatment, Integer.MAX_VALUE));

    private static final Ordering<String> DISEASE_ORDERING = Ordering.natural()
            .onResultOf(disease -> DISEASE_PRIORITY.getOrDefault(disease, Integer.MAX_VALUE));

    private static final Ordering<CfCaseFeaturesDo> MODEL_TREATMENT_ORDERING = Ordering.natural()
            .onResultOf(v -> TREATMENT_PRIORITY.getOrDefault(v.getPatientTreatmentMethods(), Integer.MAX_VALUE));

    private static final Ordering<CfCaseFeaturesDo> MODEL_DISEASE_ORDERING = Ordering.natural()
            .onResultOf(v -> DISEASE_PRIORITY.getOrDefault(v.getNormalizedDisease(), Integer.MAX_VALUE));


    public Response<ForwardingGuidelineVo> forwardingGuideline(String infoUuid, String userId) {
        if (StringUtils.isEmpty(infoUuid)) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        //试点
        boolean permission = permission(userId);
        if (!permission) {
            return NewResponseUtil.makeSuccess();
        }
        CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegate.getCrowdfundingInfoByInfouuid(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.CF_INFO_NOT_VALID);
        }
        CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoUuid(infoUuid);
        //  2. 第二行：使用以下攻略，尽快达到下一档 / 尽快达到有效 / 更快帮筹款人获捐
        if (cfBdCaseInfoDo == null) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.CF_INFO_NOT_VALID);
        }
        Long caseId = (long) crowdfundingInfo.getId();
        ForwardingGuidelineVo result = new ForwardingGuidelineVo();
        List<GrowthtoolPepCaseScoreModel> homeCaseRemind = getHomeCaseRemind(cfBdCaseInfoDo.getUniqueCode());
        //尽快达到下一档
        String useGuidelines = "使用以下攻略，更快帮筹款人获捐";
        if (CollectionUtils.isNotEmpty(homeCaseRemind)) {
            List<GrowthtoolPepCaseScoreModel> optionalList = homeCaseRemind.stream().filter(v -> v.getInfoUuid().equals(infoUuid)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(optionalList)) {
                result.setUseGuidelines("使用以下攻略，尽快达到下一档");
            }
        }
        //尽快达到有效
        if (cfBdCaseInfoDo.getValidCase() == ValidCaseEnum.NON_VALID.getCode() &&
                cfBdCaseInfoDo.getDonateNum() >= apolloService.getPersonPepCasePoolDonateNum()
                && cfBdCaseInfoDo.getCaseEndStatus() == CfStatusEnums.CaseEndStatusEnum.not_end.getCode()) {
            useGuidelines = "使用以下攻略，尽快达到有效";
        }
        result.setUseGuidelines(useGuidelines);

        String dt = DateUtil.getCurrentDateStr();

        //转发策略一
        ForwardingGuidelineOneModel forwardingGuidelineOneModel = new ForwardingGuidelineOneModel();
        forwardingGuidelineOneModel.setForwardingGuideline(ForwardingGuidelineEnum.FORWARDING_GUIDELINE_ONE.getCode());
        CfCaseFeaturesDo cfCaseFeaturesDo = cfCaseFeaturesDao.getByInfoId(caseId, dt);
        if (cfCaseFeaturesDo != null) {
            String featureDiagnosisInfo = StringUtils.EMPTY;
            //还未扩散开：“该案例约x%捐单来自亲朋好友，还未扩散开”
            if (cfCaseFeaturesDo.getFriendContributions() != null && cfCaseFeaturesDo.getFriendContributions() > apolloService.getPersonPepCasePoolFriendContributions()) {
                featureDiagnosisInfo = "该案例约" + cfCaseFeaturesDo.getFriendContributions() + "%捐单来自亲朋好友，还未扩散开";
            }
            //爆款案例：“该案例支持人数多，获捐空间大”
            List<GrowthtoolPepCaseScoreModel> growthtoolPepCaseScoreModelList = getCaseScoreList(cfBdCaseInfoDo.getUniqueCode(), FactBizTypeEnum.commission_hot_case.getCode());
            if (CollectionUtils.isNotEmpty(growthtoolPepCaseScoreModelList)) {
                Optional<GrowthtoolPepCaseScoreModel> first = growthtoolPepCaseScoreModelList.stream().filter(v -> v.getInfoUuid().equals(infoUuid)).findFirst();
                if (first.isPresent()) {
                    featureDiagnosisInfo = "该案例支持人数多，获捐空间大";
                }
            }
            //治疗方式：“该案例患者治疗方式为{icu}，花费大”
            if (StringUtils.isEmpty(featureDiagnosisInfo) && StringUtils.isNotBlank(cfCaseFeaturesDo.getPatientTreatmentMethods())) {
                List<String> list = Splitter.on(",").splitToList(cfCaseFeaturesDo.getPatientTreatmentMethods());
                List<String> sortedTreatments = TREATMENT_ORDERING.sortedCopy(list);
                featureDiagnosisInfo = "该案例患者治疗方式为" + sortedTreatments.get(0) + "，花费大";
            }
            result.setFeatureDiagnosisInfo(featureDiagnosisInfo);

            ForwardingGuidelineOneModel.ForwardingGuidelineOneInfo build = ForwardingGuidelineOneModel.ForwardingGuidelineOneInfo.builder()
                    .infoUuid(crowdfundingInfo.getInfoId())
                    .caseImg(crowdfundingInfo.getTitleImg())
                    .caseTitle(limitTitle(crowdfundingInfo.getTitle()))
                    .ckrShareCnt(cfCaseFeaturesDo.getCkrShareCnt())
                    .donateAmt(Optional.ofNullable(cfCaseFeaturesDo.getDonateAmt()).map(BigDecimal::doubleValue).orElse(0D))
                    .build();
            if (cfCaseFeaturesDo.getCkrShareCnt() != null && cfCaseFeaturesDo.getCkrShareCnt() < 35) {
                build.setLabel("转发不足");
            }
            forwardingGuidelineOneModel.setSelfInfo(build);
        }
        List<CfCaseSampleTypeDo> cfCaseSampleTypeDoList = cfCaseSampleTypeDao.listByInfoId(caseId, LocalDate.now().minusDays(1).toString());
        if (CollectionUtils.isNotEmpty(cfCaseSampleTypeDoList)) {
            List<Integer> sampleTypeInfoIdList = cfCaseSampleTypeDoList.stream().map(CfCaseSampleTypeDo::getSampleTypeInfoId).map(Long::intValue).collect(Collectors.toList());
            List<CrowdfundingInfo> crowdfundingListById = crowdFundingFeignDelegate.getCrowdfundingListById(sampleTypeInfoIdList);
            Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(crowdfundingListById)) {
                crowdfundingInfoMap = crowdfundingListById.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));
            }
            List<ForwardingGuidelineOneModel.ForwardingGuidelineOneInfo> list = Lists.newArrayList();
            for (CfCaseSampleTypeDo cfCaseSampleTypeDo : cfCaseSampleTypeDoList) {
                int infoId = cfCaseSampleTypeDo.getSampleTypeInfoId().intValue();
                CrowdfundingInfo cfInfo = crowdfundingInfoMap.get(infoId);
                if (cfInfo == null) {
                    continue;
                }
                list.add(ForwardingGuidelineOneModel.ForwardingGuidelineOneInfo.builder()
                        .infoUuid(cfInfo.getInfoId())
                        .caseImg(cfInfo.getTitleImg())
                        .caseTitle(limitTitle(cfInfo.getTitle()))
                        .ckrShareCnt(cfCaseSampleTypeDo.getCkrShareCnt())
                        .donateAmt(Optional.ofNullable(cfCaseSampleTypeDo.getDonateAmt()).map(BigDecimal::doubleValue).orElse(0D))
                        .build());
            }
            forwardingGuidelineOneModel.setForwardingGuidelineOneInfos(list);
        }
        result.setForwardingGuidelineOneModel(forwardingGuidelineOneModel);
        //转发策略二
        ForwardingGuidelineTwoModel forwardingGuidelineTwoModel = new ForwardingGuidelineTwoModel();
        forwardingGuidelineTwoModel.setForwardingGuideline(ForwardingGuidelineEnum.FORWARDING_GUIDELINE_TWO.getCode());
        List<CfCaseFriendShareDo> cfCaseFriendShareDoList = cfCaseFriendShareDao.listByInfoId(caseId, dt);
        if (CollectionUtils.isNotEmpty(cfCaseFriendShareDoList)) {
            Set<Long> longs = forwardingGuidelineTwoModelPermission(caseId);
            List<CfCaseFriendShareDo> cfCaseFriendShareDos = cfCaseFriendShareDoList.stream().filter(v ->
                    Optional.ofNullable(v.getShareDonateAmt()).map(BigDecimal::doubleValue).orElse(0D) > 50 && !longs.contains(v.getFriendUserId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cfCaseFriendShareDos) && cfCaseFriendShareDos.size() >= 3) {
                List<Long> friendUserIdList = cfCaseFriendShareDos.stream().map(CfCaseFriendShareDo::getFriendUserId).collect(Collectors.toList());
                //获取用户信息
                List<UserInfoModel> userInfoByUserIdBatch = accountServiceDelegate.getUserInfoByUserIdBatch(friendUserIdList);
                Map<Long, UserInfoModel> userInfoModelMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(userInfoByUserIdBatch)) {
                    userInfoModelMap = userInfoByUserIdBatch.stream().collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity()));
                }
                Ordering<CfCaseFriendShareDo> ordering = Ordering.natural().reverse().onResultOf(CfCaseFriendShareDo::getShareDonateAmt)
                        .compound(Ordering.natural().onResultOf(CfCaseFriendShareDo::getFriendUserId));
                List<CfCaseFriendShareDo> top5 = cfCaseFriendShareDos.stream().sorted(ordering).limit(5).collect(Collectors.toList());
                List<ForwardingGuidelineTwoModel.ForwardingGuidelineTwoInfo> list = Lists.newArrayList();
                for (CfCaseFriendShareDo cfCaseFriendShareDo : top5) {
                    //获取用户信息
                    UserInfoModel userInfoModel = userInfoModelMap.get(cfCaseFriendShareDo.getFriendUserId());
                    list.add(ForwardingGuidelineTwoModel.ForwardingGuidelineTwoInfo.builder()
                            .avatar(Optional.ofNullable(userInfoModel).map(UserInfoModel::getHeadImgUrl).orElse(""))
                            .nickname(limitNickname(Optional.ofNullable(userInfoModel).map(UserInfoModel::getNickname).orElse("")))
                            .forwardingAmount(Optional.ofNullable(cfCaseFriendShareDo.getShareDonateAmt()).map(BigDecimal::doubleValue).orElse(0D))
                            .build());
                }
                forwardingGuidelineTwoModel.setForwardingGuidelineTwoInfos(list);
            }
        }
        result.setForwardingGuidelineTwoModel(forwardingGuidelineTwoModel);
        return NewResponseUtil.makeSuccess(result);
    }


    public HomeCaseRemindVo homeCaseRemindV1(String userId, int limit) {
        HomeCaseRemindVo result = new HomeCaseRemindVo();
        result.setGrey(true);
        //获取绩效案例
        List<GrowthtoolPepCaseScoreModel> homeCaseRemind = getHomeCaseRemind(userId);
        List<HomeCaseRemindModel> homeCaseRemindModels = Lists.newArrayList();
        List<HomeCaseRemindModel> homeCaseRemindList = homeCaseRemindModelMapper.toList(homeCaseRemind);
        if (CollectionUtils.isNotEmpty(homeCaseRemindList)) {
            homeCaseRemindList = setCaseTypeEnum(homeCaseRemindList, HomeCaseRemindModel.CaseTypeEnum.NEXT_CASE);
            homeCaseRemindModels.addAll(homeCaseRemindList);
        }
        //获取运营案例
        String dt = DateUtil.getCurrentDateStr();
        if (homeCaseRemindModels.size() < limit) {
            //【即将有效案例】
            List<HomeCaseRemindModel> willValidList = willValidList(userId);
            if (CollectionUtils.isNotEmpty(willValidList)) {
                //即将有效案例排序
                List<HomeCaseRemindModel> willValidSortList = willValidAndDonateSpaceBigSort(willValidList, dt);
                homeCaseRemindModels.addAll(willValidSortList);
            }
            //【获捐空间大案例】
            if (homeCaseRemindModels.size() < limit) {
                List<CfCaseFeaturesDo> bigSpaceDonateCaseList = cfCaseFeaturesDao.listBigSpaceDonateByVolunteerCode(userId, dt);
                List<HomeCaseRemindModel> donateSpaceBigList = buildHomeCaseRemindModelByRptCfCaseFeaturesDo(bigSpaceDonateCaseList, userId, HomeCaseRemindModel.CaseTypeEnum.BIG_DONATE_CASE.getCode());
                if (CollectionUtils.isNotEmpty(donateSpaceBigList)) {
                    //获捐空间大案例排序
                    List<HomeCaseRemindModel> donateSpaceBigSortList = willValidAndDonateSpaceBigSort(donateSpaceBigList, dt);
                    homeCaseRemindModels.addAll(donateSpaceBigSortList);
                }
            }
        }
        //是否有下一页
        if (homeCaseRemindModels.size() > limit) {
            result.setNext(true);
        }
        //获取limit条数据
        List<HomeCaseRemindModel> homeCaseRemindModelList = homeCaseRemindModels.stream().limit(limit).collect(Collectors.toList());
        //特征
        getCaseFeature(dt, homeCaseRemindModelList, getCaseScoreList(userId, FactBizTypeEnum.commission_hot_case.getCode()));
        //诊断信息&诊断标签
        getDiagnosis(homeCaseRemindModelList);
        result.setHomeCaseRemindModels(homeCaseRemindModelList);
        return result;
    }

    public List<HomeCaseRemindModel> willValidList(String userId) {
        ImmutablePair<String, String> startAndEnd = getStartAndEnd();
        List<CfBdCaseInfoDo> cfBdCaseInfoDoList = cfBdCaseInfoService.listByUniqueCodeDateCreated(userId, startAndEnd.getLeft(), startAndEnd.getRight());
        if (CollectionUtils.isEmpty(cfBdCaseInfoDoList)) {
            return null;
        }
        //在筹案例，取未达到有效案例标准、且捐单大于等于50单、发起日期在当前“固定周期”的案例，定义为【即将有效案例】
        cfBdCaseInfoDoList = cfBdCaseInfoDoList.stream().filter(v -> v.getCaseEndStatus() == CfStatusEnums.CaseEndStatusEnum.not_end.getCode() &&
                v.getValidCase() == ValidCaseEnum.NON_VALID.getCode() &&
                v.getDonateNum() >= apolloService.getPersonPepCasePoolDonateNum()).collect(Collectors.toList());
        return buildHomeCaseRemindModelByCfBdCaseInfoDo(cfBdCaseInfoDoList, userId, HomeCaseRemindModel.CaseTypeEnum.NEXT_VALID_CASE.getCode());
    }

    public List<HomeCaseRemindModel> willValidAndDonateSpaceBigSort(List<HomeCaseRemindModel> homeCaseRemindModels, String dt) {
        if (CollectionUtils.isEmpty(homeCaseRemindModels)) {
            return homeCaseRemindModels;
        }
        List<Long> caseIdList = homeCaseRemindModels.stream().map(HomeCaseRemindModel::getCaseId).collect(Collectors.toList());
        List<CfCaseFeaturesDo> cfCaseFeaturesDos = cfCaseFeaturesDao.listByInfoIds(caseIdList, dt);
        if (CollectionUtils.isEmpty(cfCaseFeaturesDos)) {
            return homeCaseRemindModels;
        }
        List<HomeCaseRemindModel> result = Lists.newArrayList();
        //用于去重
        Set<Long> caseIdSet = Sets.newHashSet();
        Map<Long, HomeCaseRemindModel> homeCaseRemindModelMap = homeCaseRemindModels.stream().collect(Collectors.toMap(HomeCaseRemindModel::getCaseId, v -> v));
        //2.1还未扩散开案例：0步&1步好友贡献占比超过70%，x%由大到小排序
        List<CfCaseFeaturesDo> rptCfCaseFeaturesDoList = cfCaseFeaturesDos.stream()
                .filter(v -> v.getFriendContributions() != null && v.getFriendContributions() > apolloService.getPersonPepCasePoolFriendContributions())
                .sorted(Ordering.natural().reverse().onResultOf(CfCaseFeaturesDo::getFriendContributions))
                .collect(Collectors.toList());
        for (CfCaseFeaturesDo cfCaseFeaturesDo : rptCfCaseFeaturesDoList) {
            if (cfCaseFeaturesDo == null || cfCaseFeaturesDo.getInfoId() == null) {
                continue;
            }
            HomeCaseRemindModel homeCaseRemindModel = homeCaseRemindModelMap.get(cfCaseFeaturesDo.getInfoId());
            if (homeCaseRemindModel == null) {
                continue;
            }
            result.add(homeCaseRemindModel);
            //用来去重
            caseIdSet.add(cfCaseFeaturesDo.getInfoId());
        }
        List<CfCaseFeaturesDo> cfCaseFeaturesDoList = cfCaseFeaturesDos;
        //按照CAR-T治疗>ECMO治疗>手术>ICU>放化疗>靶向治疗>免疫治疗，多个符合则用顺序第一的来参与排序
        cfCaseFeaturesDoList = cfCaseFeaturesDoList.stream()
                .filter(v -> v.getFriendContributions() != null && v.getFriendContributions() <= apolloService.getPersonPepCasePoolFriendContributions())
                .peek(v -> {
                    if (StringUtils.isNotEmpty(v.getPatientTreatmentMethods())) {
                        List<String> list = Splitter.on(",").splitToList(v.getPatientTreatmentMethods());
                        List<String> sortedTreatments = TREATMENT_ORDERING.sortedCopy(list);
                        v.setPatientTreatmentMethods(sortedTreatments.get(0));
                    }
                }).sorted(MODEL_TREATMENT_ORDERING).collect(Collectors.toList());
        for (CfCaseFeaturesDo cfCaseFeaturesDo : cfCaseFeaturesDoList) {
            if (cfCaseFeaturesDo == null || StringUtils.isEmpty(cfCaseFeaturesDo.getPatientTreatmentMethods())) {
                continue;
            }
            HomeCaseRemindModel homeCaseRemindModel = homeCaseRemindModelMap.get(cfCaseFeaturesDo.getInfoId());
            if (homeCaseRemindModel == null) {
                continue;
            }
            result.add(homeCaseRemindModel);
            //用来去重
            caseIdSet.add(cfCaseFeaturesDo.getInfoId());
        }
        //按照急性白血病/慢性粒细胞白血病/慢性淋巴细胞白血病/淋巴瘤/骨髓瘤/骨髓纤维化/烧伤/骨髓增生异常综合征/烧烫伤后修复/尿毒症/脑出血/恶性肿瘤顺序展示，多个疾病符合则用顺序第一的疾病来参与排序
        cfCaseFeaturesDoList = cfCaseFeaturesDoList.stream()
                .filter(v -> v.getFriendContributions() != null && v.getFriendContributions() <= apolloService.getPersonPepCasePoolFriendContributions())
                .peek(v -> {
                    if (StringUtils.isNotEmpty(v.getNormalizedDisease())) {
                        List<String> list = Splitter.on(",").splitToList(v.getNormalizedDisease());
                        List<String> sortedDisease = DISEASE_ORDERING.sortedCopy(list);
                        v.setNormalizedDisease(sortedDisease.get(0));
                    }
                }).sorted(MODEL_DISEASE_ORDERING).collect(Collectors.toList());
        for (CfCaseFeaturesDo cfCaseFeaturesDo : cfCaseFeaturesDoList) {
            if (cfCaseFeaturesDo == null || StringUtils.isEmpty(cfCaseFeaturesDo.getNormalizedDisease()) || caseIdSet.contains(cfCaseFeaturesDo.getInfoId())) {
                continue;
            }
            HomeCaseRemindModel homeCaseRemindModel = homeCaseRemindModelMap.get(cfCaseFeaturesDo.getInfoId());
            if (homeCaseRemindModel == null) {
                continue;
            }
            result.add(homeCaseRemindModel);
            //用来去重
            caseIdSet.add(cfCaseFeaturesDo.getInfoId());
        }
        result.addAll(homeCaseRemindModels.stream().filter(v -> !caseIdSet.contains(v.getCaseId())).collect(Collectors.toList()));
        return result;
    }

    public void getCaseFeature(String dt, List<HomeCaseRemindModel> homeCaseRemindModels, List<GrowthtoolPepCaseScoreModel> growthtoolPepCaseScoreModelList) {
        if (CollectionUtils.isEmpty(homeCaseRemindModels)) {
            return;
        }
        List<Long> caseIdList = homeCaseRemindModels.stream().map(HomeCaseRemindModel::getCaseId).collect(Collectors.toList());
        List<CfCaseFeaturesDo> cfCaseFeaturesDoList = cfCaseFeaturesDao.listByInfoIds(caseIdList, dt);
        Map<Long, CfCaseFeaturesDo> caseFeaturesDoHashMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(cfCaseFeaturesDoList)) {
            caseFeaturesDoHashMap = cfCaseFeaturesDoList.stream().collect(Collectors.toMap(CfCaseFeaturesDo::getInfoId, v -> v));
        }
        for (HomeCaseRemindModel homeCaseRemindModel : homeCaseRemindModels) {
            List<String> featureSort = Lists.newArrayList();
            if (homeCaseRemindModel.getCaseType() == HomeCaseRemindModel.CaseTypeEnum.NEXT_CASE.getCode()) {
                //即将下一档案例】判断是否符合原来“超预期帮扶”案例，符合则打「超预期帮扶」标签
                Optional<GrowthtoolPepCaseScoreModel> first = growthtoolPepCaseScoreModelList.stream().filter(v -> v.getCaseId().equals(homeCaseRemindModel.getCaseId())).findFirst();
                if (first.isPresent()) {
                    featureSort.add("超预期帮扶");
                }
            }
            CfCaseFeaturesDo cfCaseFeaturesDo = caseFeaturesDoHashMap.get(homeCaseRemindModel.getCaseId());
            if (cfCaseFeaturesDo != null) {
                //识别出多个则按优先级展示第一个：CAR-T治疗>ECMO治疗>手术>ICU>放化疗>靶向治疗>免疫治疗
                if (StringUtils.isNotEmpty(cfCaseFeaturesDo.getPatientTreatmentMethods())) {
                    List<String> treatments = Splitter.on(",").splitToList(cfCaseFeaturesDo.getPatientTreatmentMethods());
                    List<String> sortedTreatments = TREATMENT_ORDERING.sortedCopy(treatments);
                    //取第一个
                    featureSort.add(sortedTreatments.get(0));
                }
                //x需要向上取5的倍数（风险处理），如实际12%，则展示为15%；实际48%，展示50%；实际99%，展示100%；x为5的倍数时无需处理
                if (cfCaseFeaturesDo.getFriendContributions() != null && cfCaseFeaturesDo.getFriendContributions() > apolloService.getPersonPepCasePoolFriendContributions()) {
                    homeCaseRemindModel.setFriendContributions((int) Math.ceil(cfCaseFeaturesDo.getFriendContributions() / 5.0) * 5);
                }
            }
            homeCaseRemindModel.setFeatureSort(featureSort);
        }
    }

    public void getDiagnosis(List<HomeCaseRemindModel> homeCaseRemindModels) {
        for (HomeCaseRemindModel homeCaseRemindModel : homeCaseRemindModels) {
            if (homeCaseRemindModel.getCaseType() == HomeCaseRemindModel.CaseTypeEnum.NEXT_CASE.getCode()) {
                homeCaseRemindModel.setDiagnosisLabel(HomeCaseRemindModel.CaseTypeEnum.NEXT_CASE.getShowDesc());
            } else if (homeCaseRemindModel.getCaseType() == HomeCaseRemindModel.CaseTypeEnum.NEXT_VALID_CASE.getCode()) {
                homeCaseRemindModel.setDiagnosisLabel(HomeCaseRemindModel.CaseTypeEnum.NEXT_VALID_CASE.getShowDesc());
                AdminValidCaseConfigDO adminValidCaseConfigDO = cacheAdminValidCaseConfigService.getAdminValidCaseConfigDO(homeCaseRemindModel.getOrgId());
                // //诊断信息取该案例距离有效案例的捐单、捐单金额差值，展示为“再服务患者筹xx单，再筹xx元”，
                if (adminValidCaseConfigDO != null) {
                    int diffAmount = adminValidCaseConfigDO.getValidAmount() - homeCaseRemindModel.getCaseAmount().intValue();
                    int diffDonateNum = adminValidCaseConfigDO.getValidDonateNum() - homeCaseRemindModel.getDonateNum().intValue();
                    //如果为负数，取0
                    diffAmount = Math.max(diffAmount, 0);
                    diffDonateNum = Math.max(diffDonateNum, 0);
                    homeCaseRemindModel.setDiagnosisMessage("再服务患者筹" + diffDonateNum + "单、再筹" + diffAmount / 100 + "元");
                }
            }
        }
    }

    public boolean permission(String uniqueCode) {
        if (StringUtils.isEmpty(uniqueCode)) {
            return false;
        }
        CommonPermissionConfigDo byConfigType = commonPermissionConfigService.getByConfigType(CommonPermissionConfigEnums.ConfigType.FORWARDING_DIAGNOSTIC_TOOL.getCode());
        if (byConfigType == null || StringUtils.isBlank(byConfigType.getPermissionValue())) {
            return false;
        }
        //查询顾问所在组织
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOList = crmOrganizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode);
        if (CollectionUtils.isEmpty(bdCrmOrgUserRelationDOList)) {
            return false;
        }
        List<String> orgIdList = Splitter.on(",").splitToList(byConfigType.getPermissionValue());
        for (String orgId : orgIdList) {
            List<BdCrmOrganizationDO> bdCrmOrganizationDOList = orgReadService.listAllSubOrgIncludeSelf(Long.parseLong(orgId));
            Set<Long> orgIdSet = bdCrmOrganizationDOList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toSet());
            boolean pass = bdCrmOrgUserRelationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).anyMatch(orgIdSet::contains);
            if (pass) {
                return true;
            }
        }
        return false;
    }

    public Set<Long> forwardingGuidelineTwoModelPermission(long caseId) {
        CommonPermissionConfigDo byConfigType = commonPermissionConfigService.getByConfigType(CommonPermissionConfigEnums.ConfigType.FORWARDING_GUIDELINE_TWO.getCode());
        if (byConfigType == null || StringUtils.isBlank(byConfigType.getPermissionValue())) {
            return Sets.newHashSet();
        }
        Map<Long, Set<Long>> map = JSON.parseObject(byConfigType.getPermissionValue(), new TypeReference<Map<Long, Set<Long>>>() {
        });
        return map.getOrDefault(caseId, Sets.newHashSet());
    }

    /**
     * 固定周期：指每个月23号0点:00分:00秒-次月22号23点:59分:59秒
     *
     * @return
     */
    private ImmutablePair<String, String> getStartAndEnd() {
        LocalDate now = LocalDate.now();
        LocalDate start = null;
        LocalDate end = null;
        if (now.getDayOfMonth() > 22) {
            start = now.withDayOfMonth(23);
            end = now.withDayOfMonth(22).plusMonths(1);
        } else {
            start = now.withDayOfMonth(23).minusMonths(1);
            end = now.withDayOfMonth(22);
        }
        return ImmutablePair.of(start + " 00:00:00", end + " 23:59:59");
    }

    private List<HomeCaseRemindModel> setCaseTypeEnum(List<HomeCaseRemindModel> homeCaseRemindModels, HomeCaseRemindModel.CaseTypeEnum caseTypeEnum) {
        return homeCaseRemindModels.stream().peek(v -> {
            v.setCaseType(caseTypeEnum.getCode());
        }).collect(Collectors.toList());
    }

    private List<HomeCaseRemindModel> buildHomeCaseRemindModelByCfBdCaseInfoDo(List<CfBdCaseInfoDo> cfBdCaseInfoDoList, String userId, int caseType) {
        if (CollectionUtils.isEmpty(cfBdCaseInfoDoList)) {
            return null;
        }
        List<Integer> caseIdList = cfBdCaseInfoDoList.stream().map(CfBdCaseInfoDo::getCaseId).collect(Collectors.toList());
        List<CrowdfundingInfo> crowdfundingInfos = crowdFundingFeignDelegate.getCrowdfundingListById(caseIdList);
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(crowdfundingInfos)) {
            crowdfundingInfoMap = crowdfundingInfos.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, v -> v));
        }
        List<HomeCaseRemindModel> result = Lists.newArrayList();
        for (CfBdCaseInfoDo cfBdCaseInfoDo : cfBdCaseInfoDoList) {
            HomeCaseRemindModel homeCaseRemindModel = new HomeCaseRemindModel();
            homeCaseRemindModel.setUserId(userId);
            homeCaseRemindModel.setCaseType(caseType);
            homeCaseRemindModel.setCaseId((long) cfBdCaseInfoDo.getCaseId());
            homeCaseRemindModel.setInfoUuid(cfBdCaseInfoDo.getInfoUuid());
            homeCaseRemindModel.setCaseAmount((long) cfBdCaseInfoDo.getAmount());
            homeCaseRemindModel.setDonateNum((long) cfBdCaseInfoDo.getDonateNum());
            homeCaseRemindModel.setOrgId(cfBdCaseInfoDo.getOrgId());
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoMap.get(cfBdCaseInfoDo.getCaseId());
            if (crowdfundingInfo != null) {
                homeCaseRemindModel.setTitle(crowdfundingInfo.getTitle());
            }
            result.add(homeCaseRemindModel);
        }
        return result;
    }

    private List<HomeCaseRemindModel> buildHomeCaseRemindModelByRptCfCaseFeaturesDo(List<CfCaseFeaturesDo> bigSpaceDonateCaseList, String userId, int caseType) {
        if (CollectionUtils.isEmpty(bigSpaceDonateCaseList)) {
            return null;
        }
        List<Integer> caseIdList = bigSpaceDonateCaseList.stream().map(CfCaseFeaturesDo::getInfoId).map(Long::intValue).collect(Collectors.toList());
        List<CfBdCaseInfoDo> cfBdCaseInfoDoList = cfBdCaseInfoService.listCaseInfoByCaseIds(caseIdList);
        Map<Integer, CfBdCaseInfoDo> cfBdCaseInfoDoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(cfBdCaseInfoDoList)) {
            cfBdCaseInfoDoMap = cfBdCaseInfoDoList.stream().collect(Collectors.toMap(CfBdCaseInfoDo::getCaseId, Function.identity()));
        }
        List<CrowdfundingInfo> crowdfundingInfos = crowdFundingFeignDelegate.getCrowdfundingListById(caseIdList);
        Map<Integer, CrowdfundingInfo> crowdfundingInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(crowdfundingInfos)) {
            crowdfundingInfoMap = crowdfundingInfos.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, v -> v));
        }
        List<HomeCaseRemindModel> result = Lists.newArrayList();
        for (CfCaseFeaturesDo rptCfCaseFeaturesDo : bigSpaceDonateCaseList) {
            HomeCaseRemindModel homeCaseRemindModel = new HomeCaseRemindModel();
            CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoDoMap.get(rptCfCaseFeaturesDo.getInfoId().intValue());
            if (cfBdCaseInfoDo == null) {
                continue;
            }
            homeCaseRemindModel.setCaseId((long) cfBdCaseInfoDo.getCaseId());
            homeCaseRemindModel.setInfoUuid(cfBdCaseInfoDo.getInfoUuid());
            homeCaseRemindModel.setCaseAmount((long) cfBdCaseInfoDo.getAmount());
            homeCaseRemindModel.setDonateNum((long) cfBdCaseInfoDo.getDonateNum());
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoMap.get(rptCfCaseFeaturesDo.getInfoId().intValue());
            if (crowdfundingInfo != null) {
                homeCaseRemindModel.setTitle(crowdfundingInfo.getTitle());
            }
            homeCaseRemindModel.setUserId(userId);
            homeCaseRemindModel.setCaseType(caseType);
            result.add(homeCaseRemindModel);
        }
        return result;
    }

    /**
     * 限制标题长度 10个字
     *
     * @param title
     * @return
     */
    private String limitTitle(String title) {
        if (StringUtils.isEmpty(title)) {
            return "";
        }
        if (title.length() > 10) {
            return title.substring(0, 10) + "...";
        }
        return title;
    }

    /**
     * 限制昵称长度 7个字
     *
     * @param nickname
     * @return
     */
    private String limitNickname(String nickname) {
        if (StringUtils.isEmpty(nickname)) {
            return "";
        }
        if (nickname.length() > 7) {
            return nickname.substring(0, 7) + "...";
        }
        return nickname;
    }

    private List<GrowthtoolPepCaseScoreModel> getHomeCaseRemind(String userId) {
        Response<List<GrowthtoolPepCaseScoreModel>> homeCaseRemindResponse = pepTaskClient.homeCaseRemind(userId);
        return Optional.ofNullable(homeCaseRemindResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }

    private List<GrowthtoolPepCaseScoreModel> getCaseScoreList(String userId, int factBizType) {
        Response<GrowthtoolCasePoolModel> growthtoolCasePoolModelResponse = pepTaskClient.personPepCasePool(userId, factBizType);
        return Optional.ofNullable(growthtoolCasePoolModelResponse)
                .filter(Response::ok)
                .map(Response::getData)
                .map(GrowthtoolCasePoolModel::getCaseScoreList)
                .orElse(Lists.newArrayList());
    }

}
