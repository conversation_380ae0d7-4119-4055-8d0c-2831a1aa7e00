package com.shuidihuzhu.cf.cfgrowthtoolapi.service.caseinfo;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdCooperationCaseInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdDelegateCaseTagRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfGrowthtoolApproveDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.RemoteCaseTag;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.RemoteCaseTagModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.mq.RemoteCaseRemindMsg;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.BdCooperationCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmMemberInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.ConfirmHospitalParam;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdDelegateCaseTagRecordDO.*;

/**
 * @author: fengxuan
 * @create 2022-05-12 20:19
 **/
@Slf4j
@Service
public class RemoteMarkService {

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ICrmMemberInfoService memberInfoService;

    @Autowired
    private BdDelegateCaseTagRecordService caseTagRecordService;

    @Autowired
    private ICfGrowthtoolApproveService approveService;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegateImpl;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    @Autowired
    private IMqProducerService mqProducerService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private BdCooperationCaseInfoService cooperationCaseInfoService;

    @Autowired
    private ICrmSelfBuiltOrgReadService organizationService;


    private static final List<String> directCityList = Lists.newArrayList(
            "长沙市", "乌鲁木齐市", "成都市", "南宁市", "昆明市", "湛江市", "郑州市", "太原市", "西安市", "天津市", "上海市", "南昌市", "北京市", "兰州市", "重庆市", "茂名市", "广州市", "武汉市",
            "海口市", "济南市", "阳江市", "三亚市", "哈尔滨市", "常德市", "苏州市", "汕尾市", "汕头市", "杭州市", "赣州市", "长春市", "济宁市", "柳州市", "蚌埠市", "百色市", "肇庆市", "江门市", "西宁市",
            "福州市", "玉林市", "温州市", "南充市", "南京市", "贵阳市", "临沂市", "佛山市", "清远市", "银川市", "云浮市", "宁德市", "潍坊市", "合肥市", "石家庄市", "遵义市", "东莞市", "桂林市", "郴州市", "邯郸市",
            "梅州市", "邵阳市", "怀化市", "徐州市", "沈阳市", "泸州市", "洛阳市", "深圳市", "南阳市", "恩施土家族苗族自治州", "韶关市", "中山市", "潮州市", "青岛市", "衡阳市", "盐城市", "唐山市", "梧州市", "龙岩市",
            "绵阳市", "保定市", "呼和浩特市", "惠州市", "漳州市", "运城市", "泉州市", "宁波市", "大连市", "厦门市", "揭阳市", "河源市", "珠海市", "无锡市", "儋州市"
    );


    /**
     * 后台工单标记
     */
    public void markByAdmin(ConfirmHospitalParam param, String cityName) {
        int caseId = param.getCaseId();
        CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoId(caseId);
        if (cfBdCaseInfoDo == null) {
            return;
        }
        String uniqueCode = cfBdCaseInfoDo.getUniqueCode();
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(uniqueCode);
        if (volunteer == null) {
            log.info("找不到对应的人员,caseId:{}", caseId);
            return;
        }
        boolean delegateRole = CrowdfundingVolunteerEnum.delegateRoles.contains(volunteer.getLevel());
        if (!delegateRole) {
            return;
        }
        RemoteCaseTag remoteCaseTag = judgeRemoteCaseTag(cityName, volunteer);

        //记录城市信息
        BdDelegateCaseTagRecordDO caseTagRecordDO = new BdDelegateCaseTagRecordDO();
        caseTagRecordDO.setCaseId(caseId);
        caseTagRecordDO.setUniqueCode(uniqueCode);
        caseTagRecordDO.setCityName(cityName);
        caseTagRecordDO.setHospitalName(param.getHospitalName());
        caseTagRecordDO.setJudgeSource(JudgeSource.admin_work.getCode());
        caseTagRecordDO.setLocalCity(remoteCaseTag.getOriginalLocalCity());
        caseTagRecordDO.setApproveStatus(TagApproveStatusEnum.PASS.getCode());
        caseTagRecordDO.setDirectCity(remoteCaseTag.getDirectCity());
        caseTagRecordDO.setVolunteerName("后台工单");
        caseTagRecordService.insert(caseTagRecordDO);
        updateBdCaseInfoTag(caseId, false);
    }


    /**
     * 判断应该使用什么标签
     *
     * @param cityName
     * @param volunteer
     * @return
     */
    public RemoteCaseTag judgeRemoteCaseTag(String cityName, CrowdfundingVolunteer volunteer) {
        String uniqueCode = volunteer.getUniqueCode();
        RemoteCaseTag remoteCaseTag = new RemoteCaseTag();
        if (StringUtils.isBlank(cityName)) {
            return remoteCaseTag;
        }
        CrowdfundingVolunteer crowdfundingVolunteer = null;
        // 查找顾问上级判断是否关联了筹款合作团队
        CrowdfundingVolunteer volunteerDelegateBoss = memberInfoService.listApplyLeaderWithDefaultExplicit(uniqueCode, Lists.newArrayList(CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_BOSS.getLevel()));
        if (Objects.nonNull(volunteerDelegateBoss)) {
            crowdfundingVolunteer = volunteerDelegateBoss;
        } else {
            // 查找顾问上级判断是否关联了筹款合作组长
            crowdfundingVolunteer = memberInfoService.listApplyLeaderWithDefaultExplicit(uniqueCode, Lists.newArrayList(CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_PROVINCE.getLevel()));
        }
        boolean volunteerRelation = Objects.nonNull(crowdfundingVolunteer);
        String whetherLeaderUniqueCode = volunteerRelation ? crowdfundingVolunteer.getUniqueCode() : uniqueCode;

        // 本地标签：优先通过是否关联筹款合作团队->筹款合作组长->顾问所在城市进行判定
        boolean localCityTag = memberInfoService.listAllOrgModelByUniqueCode(whetherLeaderUniqueCode)
                .stream()
                .filter(item -> Objects.equals(item.getOrgAttribute(), OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode()))
                .anyMatch(item -> Objects.equals(item.getCityName(), cityName));

        if (localCityTag) {
            remoteCaseTag.setLocalCity(LocalCityEnum.local.getCode());
            remoteCaseTag.setOriginalLocalCity(LocalCityEnum.local.getCode());
            return remoteCaseTag;
        }

        // 异地标签细分：异地同区域、异地非同区域（代理商组织下城市）
        List<String> uniqueCodeSubAreaOrgIds = memberInfoService.getUniqueCodeSubAreaOrgIds(volunteer.getUniqueCode());
        if (CrowdfundingVolunteerEnum.delegateRoles.contains(volunteer.getLevel()) && uniqueCodeSubAreaOrgIds.size() > 1) {

            // 获取顾问当前区域下的组织
            List<BdCrmOrganizationDO> currentVolunteerRelationList = organizationService.listAllSubOrgIncludeSelf(Integer.parseInt(uniqueCodeSubAreaOrgIds.get(2)));
            List<Long> currentOrgIds = currentVolunteerRelationList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());

            // 获取代理商（蜂鸟计划下所有组织）
            List<BdCrmOrganizationDO> organizationDOS = organizationService.listAllSubOrgIncludeSelf(Integer.parseInt(uniqueCodeSubAreaOrgIds.get(1)));

            organizationDOS.forEach(item -> {
                // 同区域下判定为异地同区域
                if (currentOrgIds.contains(item.getId()) && Objects.equals(item.getCityName(), cityName) &&
                        Objects.equals(item.getOrgAttribute(), OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode())) {
                    remoteCaseTag.setLocalCity(LocalCityEnum.offsite_same_area.getCode());
                    remoteCaseTag.setOriginalLocalCity(LocalCityEnum.offsite_same_area.getCode());
                    return;
                }
                // 不同区域下判定为异地非同区域
                if (Objects.equals(item.getOrgAttribute(), OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode()) &&
                        Objects.equals(item.getCityName(), cityName)) {
                    remoteCaseTag.setLocalCity(LocalCityEnum.offsite_not_same_area.getCode());
                    remoteCaseTag.setOriginalLocalCity(LocalCityEnum.offsite_not_same_area.getCode());
                }
            });

            if (remoteCaseTag.getLocalCity() != -1) {
                return remoteCaseTag;
            }

        }
        boolean directCityTag = directCityList.contains(cityName);
        // 非直营判定为异地公海
        if (!directCityTag) {
            remoteCaseTag.setLocalCity(LocalCityEnum.offsite_common_sea.getCode());
            remoteCaseTag.setOriginalLocalCity(LocalCityEnum.offsite_common_sea.getCode());
        } else {
            remoteCaseTag.setLocalCity(LocalCityEnum.offsite_direct.getCode());
            remoteCaseTag.setOriginalLocalCity(LocalCityEnum.offsite_direct.getCode());
        }
        return remoteCaseTag;
    }


    //判断是否能申请
    public Response<Void> checkCanApply(int caseId) {
        List<BdDelegateCaseTagRecordDO> recordDOList = caseTagRecordService.listByCaseId(caseId);
        CfBdCaseInfoDo bdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoId(caseId);
        Date firstApproveTime = bdCaseInfoDo.getFirstApproveTime();
        int firstApplyHours = apolloService.getRemoteApplyDelay();

        // 案例无标签且初审通过超过相应时间后不能进行申诉
        if (CollectionUtils.isEmpty(recordDOList) && Objects.nonNull(firstApproveTime) && DateTime.now().isAfter(new DateTime(firstApproveTime).plusHours(firstApplyHours))) {
            return NewResponseUtil.makeFail("案例标签申诉时间已过，不能进行案例标签申诉");
        }

        // 案例无标签可进行申诉
        if (CollectionUtils.isEmpty(recordDOList)) {
            return NewResponseUtil.makeSuccess(null);
        }

        List<BdDelegateCaseTagRecordDO> adminRecordList = recordDOList.stream()
                .filter(item -> item.getJudgeSource() == JudgeSource.admin_work.getCode())
                .sorted(Comparator.comparing(BdDelegateCaseTagRecordDO::getId).reversed())
                .collect(Collectors.toList());

        BdDelegateCaseTagRecordDO approveTagRecord = recordDOList.stream()
                .filter(item -> (item.getJudgeSource() == JudgeSource.volunteer_apply.getCode()
                        || item.getJudgeSource() == JudgeSource.sea_modify.getCode())
                        && item.getApproveStatus() == TagApproveStatusEnum.PASS.getCode())
                .min(Comparator.comparing(BdDelegateCaseTagRecordDO::getId))
                .orElse(null);

        if (approveTagRecord != null) {
            return NewResponseUtil.makeFail("案例标签已被修改成功，不能进行案例标签申诉操作");
        }


        List<BdDelegateCaseTagRecordDO> applyRecordList = recordDOList.stream()
                .filter(item -> item.getJudgeSource() == JudgeSource.volunteer_apply.getCode())
                .collect(Collectors.toList());

        BdDelegateCaseTagRecordDO applyingTagRecord = recordDOList.stream()
                .filter(item -> item.getJudgeSource() == JudgeSource.volunteer_apply.getCode()
                        && item.getApproveStatus() == TagApproveStatusEnum.APPLYING.getCode())
                .min(Comparator.comparing(BdDelegateCaseTagRecordDO::getId))
                .orElse(null);


        if (applyingTagRecord != null) {
            return NewResponseUtil.makeFail("您还有有效的案例标签申诉任务，不能重复操作");
        }


        //首次申诉
        if (CollectionUtils.isEmpty(applyRecordList)  && CollectionUtils.isNotEmpty(adminRecordList)) {
            BdDelegateCaseTagRecordDO lastAdminTagRecord = adminRecordList.get(0);
            if (DateTime.now().isAfter(new DateTime(lastAdminTagRecord.getCreateTime()).plusHours(firstApplyHours))) {
                return NewResponseUtil.makeFail("案例标签申诉时间已过，不能进行案例标签申诉");
            }
        }


        //已经驳回过三次
        List<BdDelegateCaseTagRecordDO> rejectTagRecordList = recordDOList.stream()
                .filter(item -> item.getJudgeSource() == JudgeSource.volunteer_apply.getCode()
                        && item.getApproveStatus() == TagApproveStatusEnum.REJECT.getCode())
                .sorted(Comparator.comparing(BdDelegateCaseTagRecordDO::getId).reversed())
                .collect(Collectors.toList());
        if (rejectTagRecordList.size() >= 3) {
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.REMOTE_APPLY_OVER_TIMES);
        }

        if (CollectionUtils.isNotEmpty(rejectTagRecordList)) {
            BdDelegateCaseTagRecordDO rejectTagRecord = rejectTagRecordList.get(0);
            if (DateTime.now().isAfter(new DateTime(rejectTagRecord.getApproveTime()).plusHours(firstApplyHours))) {
                return NewResponseUtil.makeFail("当前时间已超过驳回后重新申诉的时间，不能再进行案例标签申诉");
            }
        }


        List<BdDelegateCaseTagRecordDO> adminRejectTagRecordList = recordDOList.stream()
                .filter(item -> item.getJudgeSource() == JudgeSource.volunteer_apply.getCode()
                        && item.getApproveStatus() == TagApproveStatusEnum.ADMIN_REJECT.getCode())
                .sorted(Ordering.natural().onResultOf(BdDelegateCaseTagRecordDO::getApproveTime).reversed())
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(adminRejectTagRecordList)) {
            if (DateTime.now().isAfter(new DateTime(adminRejectTagRecordList.get(0).getApproveTime()).plusHours(firstApplyHours))) {
                return NewResponseUtil.makeFail("案例标签申诉时间已过，不能进行案例标签申诉");
            }
        }

        if (adminRejectTagRecordList.size() > 1) {
            return NewResponseUtil.makeFail("案例标签申诉质检驳回次数已经超过1次，不能再进行案例申诉");
        }

        List<BdDelegateCaseTagRecordDO> adminPassRecordList = recordDOList.stream()
                .filter(item -> item.getJudgeSource() == JudgeSource.volunteer_apply.getCode()
                        && item.getApproveStatus() == TagApproveStatusEnum.ADMIN_PASS.getCode())
                .sorted(Ordering.natural().onResultOf(BdDelegateCaseTagRecordDO::getApproveTime).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(adminPassRecordList)) {
            return NewResponseUtil.makeFail("案例标签申诉运营审核已通过，不能再进行案例申诉");
        }
        return NewResponseUtil.makeSuccess(null);
    }


    public Response<Void> applyRemoteCaseTag(RemoteCaseTagModel remoteCaseTagModel, CrowdfundingVolunteer volunteer) {
        Response<Void> checkCanApply = checkCanApply(remoteCaseTagModel.getCaseId());
        if (checkCanApply.notOk()) {
            return checkCanApply;
        }
        recordByApply(remoteCaseTagModel, volunteer);
        return NewResponseUtil.makeSuccess(null);
    }


    //申请信息
    public void recordByApply(RemoteCaseTagModel remoteCaseTagModel, CrowdfundingVolunteer volunteer) {
        CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegateImpl.getCaseInfoById(remoteCaseTagModel.getCaseId());
        if (crowdfundingInfo == null) {
            return;
        }
        RemoteCaseTag remoteCaseTag = judgeRemoteCaseTag(remoteCaseTagModel.getCityName(), volunteer);

        // 若识别城市对应案例标签为“异地直营”，则案例标签覆盖为异地案例转介绍
        if (Objects.equals(remoteCaseTag.getOriginalLocalCity(), LocalCityEnum.offsite_direct.getCode()) && Objects.equals(remoteCaseTagModel.getOffsiteCaseIntroduce(), 1)) {
            remoteCaseTag.setLocalCity(LocalCityEnum.special_remote.getCode());
            remoteCaseTag.setOriginalLocalCity(LocalCityEnum.special_remote.getCode());
        }

        //查找对应的审批人
        CrowdfundingVolunteer superiorVolunteer = memberInfoService.listApplyLeaderWithDefaultExplicit(volunteer.getUniqueCode(),
                Lists.newArrayList(CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_PROVINCE.getLevel(),
                        CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AREA_LEADER.getLevel()));
        if (superiorVolunteer == null) {
            log.info("无审批人,remoteCaseTagModel:{}", remoteCaseTagModel);
        }
        String city = memberInfoService.listAllOrgModelByUniqueCode(volunteer.getUniqueCode())
                .stream()
                .filter(item -> Objects.equals(item.getOrgAttribute(), OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode()))
                .map(BdCrmOrganizationDO::getCityName)
                .findFirst()
                .orElse("");
        BdDelegateCaseTagRecordDO caseTagRecordDO = new BdDelegateCaseTagRecordDO();
        caseTagRecordDO.setCaseId(remoteCaseTagModel.getCaseId());
        caseTagRecordDO.setUniqueCode(volunteer.getUniqueCode());
        caseTagRecordDO.setJudgeSource(JudgeSource.volunteer_apply.getCode());
        caseTagRecordDO.setApproveStatus(TagApproveStatusEnum.APPLYING.getCode());
        caseTagRecordDO.setApplyReason(remoteCaseTagModel.getApplyReason());
        caseTagRecordDO.setApplyPic(remoteCaseTagModel.getApplyPic());
        caseTagRecordDO.setCityName(remoteCaseTagModel.getCityName());
        caseTagRecordDO.setHospitalName(remoteCaseTagModel.getHospitalName());
        caseTagRecordDO.setLocalCity(remoteCaseTag.getLocalCity());
        caseTagRecordDO.setDirectCity(remoteCaseTag.getDirectCity());
        caseTagRecordDO.setVolunteerName(city + volunteer.getVolunteerName());
        caseTagRecordService.insert(caseTagRecordDO);

        String link = GeneralConstant.caseBaseUrl + crowdfundingInfo.getInfoId();
        remoteCaseTagModel.setCaseUrl(link);
        remoteCaseTagModel.setCaseTitle(crowdfundingInfo.getTitle());
        remoteCaseTagModel.setVolunteerName(volunteer.getVolunteerName());
        remoteCaseTagModel.setApplyId(caseTagRecordDO.getId());
        remoteCaseTagModel.setCity(city);
        String caseTag = getCaseTag(remoteCaseTag);

        remoteCaseTagModel.setCaseTag(caseTag);
        CfGrowthtoolApproveDO cfGrowthtoolApproveDO = approveService.saveApproveContent(JSON.toJSONString(remoteCaseTagModel), CfGrowthtoolApproveDO.ApproveTypeEnum.REMOTE_CASE_TAG.getType(),
                volunteer, superiorVolunteer);
        RemoteCaseRemindMsg remoteCaseRemindMsg = RemoteCaseRemindMsg.builder()
                .applyTime(DateTime.now().toString(GrowthtoolUtil.ymdhmsfmt))
                .cfGrowthtoolApproveDO(cfGrowthtoolApproveDO)
                .approveVolunteer(superiorVolunteer)
                .remoteCaseTagModel(remoteCaseTagModel)
                .build();
        //发送延时消息
        mqProducerService.sendRemoteCaseRemind(remoteCaseRemindMsg, System.currentTimeMillis() + TimeUnit.HOURS.toMillis(2));
        //发送消息
        CrowdfundingVolunteer delegateLeader = getDelegateLeader(volunteer.getUniqueCode());
        CfBdCaseInfoDo bdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoId(crowdfundingInfo.getId());
        boolean needShowOffsite = false;
        if (bdCaseInfoDo != null) {
            // 当标签为异地直营或者无标签时，需要展示异地直营
            needShowOffsite = bdCaseInfoDo.getLocalCity() == LocalCityEnum.no_tag.getCode() || bdCaseInfoDo.getLocalCity() == LocalCityEnum.offsite_direct.getCode();
        }
        appPushCrmCaseMsgService.remoteCaseApply(superiorVolunteer, delegateLeader, remoteCaseTagModel, needShowOffsite);
    }


    private String getCaseTag(RemoteCaseTag remoteCaseTag) {
        int localCity = remoteCaseTag.getLocalCity();
        return Optional.ofNullable(BdDelegateCaseTagRecordDO.parseLocalCityEnum(localCity))
                        .map(LocalCityEnum::getDesc)
                        .orElse("");
    }


    //申请-审核通过/驳回
    public void updateByApprove(String approveContent, int approveStatus, String rejectReason, String approveName) {
        try {
            RemoteCaseTagModel remoteCaseTagModel = JSON.parseObject(approveContent, RemoteCaseTagModel.class);
            long applyId = remoteCaseTagModel.getApplyId();
            BdDelegateCaseTagRecordDO caseTagRecordDO = caseTagRecordService.queryById(remoteCaseTagModel.getApplyId());
            if (caseTagRecordDO == null) {
                return;
            }
            String uniqueCode = caseTagRecordDO.getUniqueCode();
            CrowdfundingVolunteer volunteer = cfVolunteerService.getVolunteerByUniqueCode(uniqueCode);
            //修改对应的审核
            caseTagRecordService.updateByApproveInfo(applyId, approveStatus, rejectReason, approveName);
            int caseId = remoteCaseTagModel.getCaseId();
            // 更新审核时间
            cooperationCaseInfoService.updateApproveTime(caseId, new Date());
            // 更新复检状态
            if (approveStatus == TagApproveStatusEnum.PASS.getCode()) {
                cooperationCaseInfoService.updateAdminApproveStatus(caseId, BdCooperationCaseInfoDO.AdminApproveStatusEnum.DEFAULT.getCode());
            }
            updateBdCaseInfoTag(caseId, false);
            //审批信息
            CrowdfundingVolunteer delegateLeader = getDelegateLeader(volunteer.getUniqueCode());
            appPushCrmCaseMsgService.remoteCaseApprove(approveStatus, rejectReason, volunteer, delegateLeader, remoteCaseTagModel);
        } catch (Exception e) {
            log.error("审批后处理异地案例异常,msg:{}", approveContent, e);
        }
    }


    //后台修改标签
    public void markBySeaModify(RemoteCaseTagModel remoteCaseTagModel, String mis) {
        CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoId(remoteCaseTagModel.getCaseId());
        if (cfBdCaseInfoDo == null) {
            log.info("查询不到案例信息");
            return;
        }
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(cfBdCaseInfoDo.getUniqueCode());
        if (volunteer == null) {
            log.info("找不到对应的人员,caseId:{}", cfBdCaseInfoDo.getCaseId());
            return;
        }
        RemoteCaseTag remoteCaseTag = judgeRemoteCaseTag(remoteCaseTagModel.getCityName(), volunteer);
        // 如果标签识别为异地直营且为异地转介绍标签覆盖为异地转介绍
        if (Objects.equals(remoteCaseTag.getLocalCity(), LocalCityEnum.offsite_direct.getCode()) && Objects.equals(remoteCaseTagModel.getOffsiteCaseIntroduce(), 1)) {
            remoteCaseTag.setLocalCity(LocalCityEnum.special_remote.getCode());
            remoteCaseTag.setOriginalLocalCity(LocalCityEnum.special_remote.getCode());
        }
        BdDelegateCaseTagRecordDO caseTagRecordDO = new BdDelegateCaseTagRecordDO();
        caseTagRecordDO.setCaseId(remoteCaseTagModel.getCaseId());
        caseTagRecordDO.setUniqueCode(cfBdCaseInfoDo.getUniqueCode());
        caseTagRecordDO.setJudgeSource(JudgeSource.sea_modify.getCode());
        caseTagRecordDO.setApproveStatus(TagApproveStatusEnum.PASS.getCode());
        caseTagRecordDO.setApplyReason(remoteCaseTagModel.getApplyReason());
        caseTagRecordDO.setApplyPic(remoteCaseTagModel.getApplyPic());
        caseTagRecordDO.setCityName(remoteCaseTagModel.getCityName());
        caseTagRecordDO.setHospitalName(remoteCaseTagModel.getHospitalName());
        caseTagRecordDO.setLocalCity(remoteCaseTag.getLocalCity());
        caseTagRecordDO.setDirectCity(remoteCaseTag.getDirectCity());
        caseTagRecordDO.setOperateName(mis);
        caseTagRecordService.insert(caseTagRecordDO);
        updateBdCaseInfoTag(remoteCaseTagModel.getCaseId(), false);
    }


    //质检驳回
    public void rejectBySea(int caseId, int tagRecordId, String commit, String operator, Integer qualityResult) {
        CrowdfundingInfo crowdfundingInfo = crowdFundingFeignDelegateImpl.getCaseInfoById(caseId);
        // 根据案例标签变更记录id查询
        BdDelegateCaseTagRecordDO caseTagRecordDO = caseTagRecordService.queryById(tagRecordId);
        if (Objects.equals(qualityResult, TagApproveStatusEnum.ADMIN_PASS.getCode()) && caseTagRecordDO != null) {
            caseTagRecordService.updateByApproveInfo(caseTagRecordDO.getId(), TagApproveStatusEnum.ADMIN_PASS.getCode(), commit, operator);
            cooperationCaseInfoService.updateAdminApproveStatus(caseId, BdCooperationCaseInfoDO.AdminApproveStatusEnum.APPLYING.getCode());
        }
        if (Objects.equals(qualityResult, TagApproveStatusEnum.ADMIN_REJECT.getCode()) && caseTagRecordDO != null) {
            caseTagRecordService.updateByApproveInfo(caseTagRecordDO.getId(), TagApproveStatusEnum.ADMIN_REJECT.getCode(), commit, operator);
            cooperationCaseInfoService.updateAdminApproveStatus(caseId, BdCooperationCaseInfoDO.AdminApproveStatusEnum.REJECT.getCode());
            updateBdCaseInfoTag(caseId, true);
            CrowdfundingVolunteer delegateLeader = getDelegateLeader(caseTagRecordDO.getUniqueCode());
            RemoteCaseTagModel remoteCaseTagModel = new RemoteCaseTagModel();
            remoteCaseTagModel.setApplyReason(caseTagRecordDO.getApplyReason());
            remoteCaseTagModel.setCaseId(caseId);
            remoteCaseTagModel.setHospitalName(caseTagRecordDO.getHospitalName());
            remoteCaseTagModel.setCityName(caseTagRecordDO.getCityName());
            if (crowdfundingInfo != null) {
                remoteCaseTagModel.setCaseTitle(crowdfundingInfo.getTitle());
            }
            remoteCaseTagModel.setCaseTag(getCaseTag(RemoteCaseTag.builder()
                    .directCity(caseTagRecordDO.getDirectCity())
                    .localCity(caseTagRecordDO.getLocalCity())
                    .build()));
            CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(caseTagRecordDO.getUniqueCode());
            appPushCrmCaseMsgService.rejectBySea(commit, delegateLeader, volunteer, remoteCaseTagModel);
        }
    }


    private CrowdfundingVolunteer getDelegateLeader(String uniqueCode) {
        return memberInfoService.listApplyLeaderWithDefaultExplicit(uniqueCode,
                Lists.newArrayList(CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_BOSS.getLevel(),
                        CrowdfundingVolunteerEnum.RoleEnum.DELEGATE_PROVINCE.getLevel()));

    }


    //案例异地转介绍标签修改
    private void updateBdCaseInfoTag(int caseId, boolean isRejectBySea) {
        List<BdDelegateCaseTagRecordDO> recordDOList = caseTagRecordService.listByCaseId(caseId);

        BdDelegateCaseTagRecordDO priorityCaseTag = getPriorityCaseTag(recordDOList);

        // 如果运营审核驳回且未找到修改的标签,则需要将标签修改为"无标签"
        if (isRejectBySea && priorityCaseTag == null) {
            cfBdCaseInfoService.updateLocalCityTag(caseId, LocalCityEnum.no_tag.getCode(), DirectCityEnum.no_tag.getCode());
        }

        if (priorityCaseTag != null) {
            cfBdCaseInfoService.updateLocalCityTag(caseId, priorityCaseTag.getLocalCity(), priorityCaseTag.getDirectCity());
            // 案例被打异地标签给顾问发消息
            if (Objects.equals(LocalCityEnum.remote.getCode(), priorityCaseTag.getLocalCity()) && Objects.equals(priorityCaseTag.getJudgeSource(), JudgeSource.admin_work.getCode())) {
                CfBdCaseInfoDo bdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoId(caseId);
                appPushCrmCaseMsgService.sendLocalCityTagMsg(priorityCaseTag, bdCaseInfoDo);
            }
        }
    }

    /**
     * 标签优先级判定 本地标签->后台修改标签->申诉通过后的标签->工单修改医院标签
     * https://wdh.feishu.cn/wiki/Sht4wB9hTiyXOAkiMQ3cuyn8n9g
     */
    private BdDelegateCaseTagRecordDO getPriorityCaseTag(List<BdDelegateCaseTagRecordDO> recordDOList) {
        TagApproveStatusEnum.approveStatusList();
        BdDelegateCaseTagRecordDO localCityCaseTag = recordDOList
                .stream()
                .filter(item -> TagApproveStatusEnum.approveStatusList().contains(item.getApproveStatus()) && item.getLocalCity() == LocalCityEnum.local.getCode())
                .findFirst()
                .orElse(null);
        if (localCityCaseTag != null) {
            return localCityCaseTag;
        }
        // 根据来源排序优先级
        return recordDOList
                .stream()
                .filter(item -> TagApproveStatusEnum.approveStatusList().contains(item.getApproveStatus()))
                .max(Ordering.natural().onResultOf(BdDelegateCaseTagRecordDO::getJudgeSource))
                .orElse(null);
    }

    public List<BdDelegateCaseTagRecordDO> listRemoteApplyRecord(int caseId) {
        return caseTagRecordService.listByCaseIdAndSource(caseId, BdDelegateCaseTagRecordDO.JudgeSource.volunteer_apply.getCode());
    }


    //动态展示修改后的案例标签
    public RemoteCaseTag realTimeCalCaseTag(String cityName, int caseId, JudgeSource judgeSource) {
        BdDelegateCaseTagRecordDO caseTagRecordDO = caseTagRecordService.listByCaseId(caseId)
                .stream()
                .filter(item -> item.getApproveStatus() == TagApproveStatusEnum.PASS.getCode())
                .max(Ordering.natural().onResultOf(BdDelegateCaseTagRecordDO::getJudgeSource))
                .orElse(null);
        //查到对应案例
        CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoId(caseId);
        if (cfBdCaseInfoDo == null) {
            return new RemoteCaseTag();
        }
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(cfBdCaseInfoDo.getUniqueCode());
        if (volunteer == null) {
            log.info("找不到对应的人员,caseId:{}", caseId);
            return new RemoteCaseTag();
        }
        RemoteCaseTag remoteCaseTag = judgeRemoteCaseTag(cityName, volunteer);
        if (judgeSource == JudgeSource.sea_modify) {
            return remoteCaseTag;
        }
        if (caseTagRecordDO != null && caseTagRecordDO.getJudgeSource() > judgeSource.getCode()) {
            RemoteCaseTag result = new RemoteCaseTag();
            result.setLocalCity(caseTagRecordDO.getLocalCity());
            result.setDirectCity(caseTagRecordDO.getDirectCity());
            return result;
        }
        return remoteCaseTag;
    }


}
