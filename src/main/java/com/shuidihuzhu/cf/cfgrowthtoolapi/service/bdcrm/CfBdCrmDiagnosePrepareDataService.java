package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgAndCityRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmDiagnoseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.SdCityHierarchyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseBaseDataExtModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseBaseDataModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.math.RoundingMode.HALF_UP;

/**
 * @author: wanghui
 * @create: 2021/4/16 下午2:31
 */
@Service
@Slf4j
public class CfBdCrmDiagnosePrepareDataService extends AbstractBdCrmDiagnosePrepareDataService {

    private final Integer ONLINE = 0;
    private final Integer OFFLINE = 1;

    @Override
    protected List<CfBdCrmDiagnoseDataDO> calcSdUpDownContribution(List<CfBdCrmDiagnoseDataDO> list, BdCrmOrganizationDO organizationDO) {
        // 假设 数据
        Map<Long, CfBdCrmDiagnoseBaseDataExtModel> supposeMap = calcSupposeDataForOrg(organizationDO);
        List<Long> directProvinceOrCityId = Optional.ofNullable(calcModel.getOrgIdMapOrgAndCityRelationList().get(organizationDO.getId())).orElse(Lists.newArrayList()).stream().map(BdCrmOrgAndCityRelationDO::getCityId).map(Long::valueOf).collect(Collectors.toList());
        Double totalMarketInfluence = supposeMap.values().stream().filter(item -> directProvinceOrCityId.contains(item.getCityId())).map(CfBdCrmDiagnoseBaseDataExtModel::getMarketInfluence).reduce((total, item) -> total += item).orElse(0d);
        if (totalMarketInfluence==0d) return list;
        // 省份下 各个城市的贡献度
        Map<Long, Double> cityIdMapContribution = calcContributionForProvince(directProvinceOrCityId);
        for (CfBdCrmDiagnoseDataDO diagnoseDataDO : list) {
            Map<Integer, Double> cityOfflineAndOnlineContribution;
            if (diagnoseDataDO.getCityId()!=null && diagnoseDataDO.getCityId()>0) {
                cityOfflineAndOnlineContribution = getCityOfflineAndOnlineContribution(diagnoseDataDO.getCityId(), diagnoseDataDO.getCityType());
            }else {
                cityOfflineAndOnlineContribution = getOrgOfflineAndOnlineContribution(diagnoseDataDO.getOrgId());
            }
            diagnoseDataDO.setSdOnlineUpDownContribution(cityOfflineAndOnlineContribution.get(ONLINE));
            diagnoseDataDO.setSdOfflineUpDownContribution(cityOfflineAndOnlineContribution.get(OFFLINE));
            // 计算 直接权限的 省份或城市的 贡献度
            if (directProvinceOrCityId.contains(diagnoseDataDO.getCityId())) {
                CfBdCrmDiagnoseBaseDataExtModel baseDataExtModel = supposeMap.get(diagnoseDataDO.getCityId());
                if (baseDataExtModel==null) continue;
                diagnoseDataDO.setSdUpDownContribution(new BigDecimal(baseDataExtModel.getMarketInfluence())
                        .divide(new BigDecimal(totalMarketInfluence), 4, HALF_UP).doubleValue());
            }else { // 计算 省份下 各城市的贡献度
                try {
                    diagnoseDataDO.setSdUpDownContribution(cityIdMapContribution.get(diagnoseDataDO.getCityId()));
                } catch (Exception e) {
                    log.error("calcSdUpDownContribution err", e);
                }
            }
        }
        return list;
    }

    private Map<Long, Double> calcContributionForProvince(List<Long> directProvinceOrCityId) {
        if (provinceIdMapContribution==null) provinceIdMapContribution = Maps.newHashMap();
        Map<Long, Double> result = Maps.newHashMap();
        for (long cityId : directProvinceOrCityId) {
            List<SdCityHierarchyDO> cityListByProvinceId = calcModel.getCityListByProvinceId(cityId);
            if (CollectionUtils.isEmpty(cityListByProvinceId)) continue;
            // 判断是否之前计算过 该省份下 各个城市的贡献度
            if (provinceIdMapContribution.get(cityId)!=null){
                result.putAll(provinceIdMapContribution.get(cityId));
                continue;
            }
            Map<Long, Double> cityIdMapContribution = Maps.newHashMap();
            // 计算 假设当前组织数据未变的数据
            Map<Long, CfBdCrmDiagnoseBaseDataExtModel> supposeDataMap = calcSupposeDataByProvinceId(cityListByProvinceId);
            Double totalMarketInfluence = supposeDataMap.values().stream().map(CfBdCrmDiagnoseBaseDataExtModel::getMarketInfluence).reduce((total, item) -> total += item).get();
            if (totalMarketInfluence==0d) continue;
            for (SdCityHierarchyDO sdCityHierarchyDO : cityListByProvinceId) {
                CfBdCrmDiagnoseBaseDataExtModel extModel = supposeDataMap.get(sdCityHierarchyDO.getCityId());
                if (extModel==null) continue;
                double contribution = new BigDecimal(extModel.getMarketInfluence())
                        .divide(new BigDecimal(totalMarketInfluence), 4, HALF_UP).doubleValue();
                result.put(sdCityHierarchyDO.getCityId(), contribution);
                cityIdMapContribution.put(sdCityHierarchyDO.getCityId(), contribution);
            }
            provinceIdMapContribution.put(cityId, cityIdMapContribution);
        }
        return result;
    }

    private Map<Long,CfBdCrmDiagnoseBaseDataExtModel> calcSupposeDataByProvinceId(List<SdCityHierarchyDO> cityListByProvinceId) {
        Map<Long, CfBdCrmDiagnoseBaseDataExtModel> supposeMap = Maps.newHashMap();
        // 昨日数据  cityId 映射 CfBdCrmDiagnoseBaseDataModel
        Map<Long,CfBdCrmDiagnoseBaseDataModel> curDateTimeMap = cityListByProvinceId.stream()
                .map(item -> calcModel.getCurCityData().get(item.getCityId()))
                .filter(item -> item!=null)
                .collect(Collectors.toMap(CfBdCrmDiagnoseBaseDataModel::getCityId, Function.identity()));
        // 昨日同比上周数据  cityId 映射 CfBdCrmDiagnoseBaseDataModel
        Map<Long,CfBdCrmDiagnoseBaseDataModel> preDateTimeMap = cityListByProvinceId.stream()
                .map(item -> calcModel.getPreCityData().get(item.getCityId()))
                .filter(item -> item!=null)
                .collect(Collectors.toMap(CfBdCrmDiagnoseBaseDataModel::getCityId, Function.identity()));
        for (SdCityHierarchyDO sdCityHierarchyDO : cityListByProvinceId) {
            List<CfBdCrmDiagnoseBaseDataModel> dataTmp = Lists.newArrayList();
            dataTmp.add(preDateTimeMap.get(sdCityHierarchyDO.getCityId())); // 取当前城市 上周的数据
            for (Map.Entry<Long,CfBdCrmDiagnoseBaseDataModel> entry : curDateTimeMap.entrySet()) {
                if (!entry.getKey().equals(sdCityHierarchyDO.getCityId())) dataTmp.add(entry.getValue()); // 取其他城市 昨日的数据
            }
            // 计算 假设当前城市数据未变的数据
            CfBdCrmDiagnoseBaseDataExtModel cfBdCrmDiagnoseBaseDataExtModel = calcExtModel(dataTmp, curDateTimeMap, preDateTimeMap);
            cfBdCrmDiagnoseBaseDataExtModel.setCityId(sdCityHierarchyDO.getCityId());
            supposeMap.put(sdCityHierarchyDO.getCityId(), cfBdCrmDiagnoseBaseDataExtModel);
        }
        return supposeMap;
    }
    // 计算
    private Map<Long, CfBdCrmDiagnoseBaseDataExtModel> calcSupposeDataForOrg(BdCrmOrganizationDO organizationDO) {
        List<BdCrmOrgAndCityRelationDO> relationList = Optional.ofNullable(calcModel.getOrgIdMapOrgAndCityRelationList().get(organizationDO.getId())).orElse(Lists.newArrayList());
        Map<Long, CfBdCrmDiagnoseBaseDataExtModel> supposeMap = Maps.newHashMap();
        // 昨日数据  cityId 映射 CfBdCrmDiagnoseBaseDataModel
        Map<Long,CfBdCrmDiagnoseBaseDataModel> curDateTimeMap = relationList.stream()
                .map(item -> item.getBindType() == 0? calcModel.getCurProvinceData().get((long) item.getCityId()) : calcModel.getCurCityData().get((long) item.getCityId()))
                .filter(item -> item!=null)
                .collect(Collectors.toMap(CfBdCrmDiagnoseBaseDataModel::getCityId, Function.identity()));
        // 昨日同比上周数据  cityId 映射 CfBdCrmDiagnoseBaseDataModel
        Map<Long,CfBdCrmDiagnoseBaseDataModel> preDateTimeMap = relationList.stream()
                .map(item -> item.getBindType() == 0? calcModel.getPreProvinceData().get((long) item.getCityId()) : calcModel.getPreCityData().get((long) item.getCityId()))
                .filter(item -> item!=null)
                .collect(Collectors.toMap(CfBdCrmDiagnoseBaseDataModel::getCityId, Function.identity()));
        for (BdCrmOrgAndCityRelationDO relationDO : relationList) {
            List<CfBdCrmDiagnoseBaseDataModel> dataTmp = Lists.newArrayList();
            dataTmp.add(preDateTimeMap.get((long) relationDO.getCityId())); // 取当前城市或省份 上周的数据
            for (Map.Entry<Long,CfBdCrmDiagnoseBaseDataModel> entry : curDateTimeMap.entrySet()) {
                if (entry.getKey().intValue()!=relationDO.getCityId()) dataTmp.add(entry.getValue()); // 取其他城市或省份 昨日的数据
            }
            // 计算 假设当前组织数据未变的数据
            CfBdCrmDiagnoseBaseDataExtModel cfBdCrmDiagnoseBaseDataExtModel = calcExtModel(dataTmp, curDateTimeMap, preDateTimeMap);
            cfBdCrmDiagnoseBaseDataExtModel.setCityId((long)relationDO.getCityId());
            supposeMap.put((long)relationDO.getCityId(), cfBdCrmDiagnoseBaseDataExtModel);
        }
        return supposeMap;
    }

    @Override
    protected List<CfBdCrmDiagnoseDataDO> calcSdUpDownContributionForQuanGuo(List<CfBdCrmDiagnoseDataDO> list) {
        // 计算 每个区域的 上周以及本周 数据
        Map<String,Map<Long,CfBdCrmDiagnoseBaseDataModel>> dateMapData = Maps.newHashMap(); // 时间 映射
        dateMapData.put(calcModel.getPreDateTime(), calcOrgDiagnoseBaseDataForQuanGuo(calcModel.getPreDateTime()));
        dateMapData.put(calcModel.getCurDateTime(), calcOrgDiagnoseBaseDataForQuanGuo(calcModel.getCurDateTime()));

        // 假设 数据
        Map<Long, CfBdCrmDiagnoseBaseDataExtModel> supposeMap = calcSupposeDataForQuanGuo(dateMapData);
        log.debug("calcSdUpDownContributionForQuanGuo supposeMap: {}", JSON.toJSONString(supposeMap));
        Double totalMarketInfluence = supposeMap.values().stream().map(CfBdCrmDiagnoseBaseDataExtModel::getMarketInfluence).reduce((total, item) -> total += item).orElse(0d);
        if (totalMarketInfluence==0d) return list;
        for (CfBdCrmDiagnoseDataDO diagnoseDataDO:list) {
            Map<Integer, Double> orgOfflineAndOnlineContribution = getOrgOfflineAndOnlineContribution(diagnoseDataDO.getOrgId());
            diagnoseDataDO.setSdOnlineUpDownContribution(orgOfflineAndOnlineContribution.get(ONLINE));
            diagnoseDataDO.setSdOfflineUpDownContribution(orgOfflineAndOnlineContribution.get(OFFLINE));
            CfBdCrmDiagnoseBaseDataExtModel baseDataExtModel = supposeMap.get(diagnoseDataDO.getOrgId());
            if (baseDataExtModel==null) continue;
            diagnoseDataDO.setSdUpDownContribution(new BigDecimal(baseDataExtModel.getMarketInfluence())
                    .divide(new BigDecimal(totalMarketInfluence), 4, HALF_UP).doubleValue());
        }
        return list;
    }



    // 计算 每个区域的 上周以及本周 数据
    private Map<Long,CfBdCrmDiagnoseBaseDataModel> calcOrgDiagnoseBaseDataForQuanGuo(String dateTime) {
        Map<Long,CfBdCrmDiagnoseBaseDataModel> dateTimeMap = Maps.newHashMap();
        for (BdCrmOrganizationDO organizationDO : calcModel.getAllAreaOrgList()) {
            if (organizationDO.getId()==GeneralConstant.CRM_ORG_ID) continue;
            List<BdCrmOrgAndCityRelationDO> bdCrmOrgAndCityRelationDOS = calcModel.getOrgIdMapOrgAndCityRelationList().get(organizationDO.getId());
            if (CollectionUtils.isEmpty(bdCrmOrgAndCityRelationDOS)) continue;
            dateTimeMap.put(organizationDO.getId(),this.aggregateData(dateTime , bdCrmOrgAndCityRelationDOS));
        }
        return dateTimeMap;
    }

    // 计算假设数据
    private Map<Long, CfBdCrmDiagnoseBaseDataExtModel> calcSupposeDataForQuanGuo(Map<String,Map<Long,CfBdCrmDiagnoseBaseDataModel>> dateMapData) {
        Map<Long,CfBdCrmDiagnoseBaseDataModel> curDateTimeMap = dateMapData.get(calcModel.getCurDateTime());  // 昨日数据  组织id 映射 CfBdCrmDiagnoseBaseDataModel
        Map<Long,CfBdCrmDiagnoseBaseDataModel> preDateTimeMap = dateMapData.get(calcModel.getPreDateTime());  // 昨日同比上周数据  组织id 映射 CfBdCrmDiagnoseBaseDataModel
        Map<Long, CfBdCrmDiagnoseBaseDataExtModel> supposeMap = Maps.newHashMap();
        for (BdCrmOrganizationDO organizationDO : calcModel.getAllAreaOrgList()) {
            if (organizationDO.getId()==GeneralConstant.CRM_ORG_ID) continue;
            List<CfBdCrmDiagnoseBaseDataModel> dataTmp = Lists.newArrayList();
            dataTmp.add(preDateTimeMap.get(organizationDO.getId())); // 取当前组织 上周的数据
            for (Map.Entry<Long,CfBdCrmDiagnoseBaseDataModel> entry:curDateTimeMap.entrySet()) {
                if (entry.getKey().longValue()!=organizationDO.getId()) dataTmp.add(entry.getValue()); // 取其他组织 昨日的数据
            }
            // 计算 假设当前组织数据未变的数据
            supposeMap.put(organizationDO.getId(), calcExtModel(dataTmp, curDateTimeMap, preDateTimeMap));
        }
        return supposeMap;
    }

    private Map<Integer, Double> getCityOfflineAndOnlineContribution(Long cityId, Integer cityType) {
        if (cityId==null || cityId<=0 ||cityType==null) return Maps.newHashMap();
        if (cityIdMapOnlineWithOfflineContribution==null) cityIdMapOnlineWithOfflineContribution = Maps.newHashMap();
        Map<Integer, Double> cacheMap = cityIdMapOnlineWithOfflineContribution.get(cityId);
        if (cacheMap!=null && cacheMap.size()>0) return cacheMap;
        // 昨日数据  CfBdCrmDiagnoseBaseDataModel
        // 昨日同比上周数据  CfBdCrmDiagnoseBaseDataModel
        CfBdCrmDiagnoseBaseDataModel curDateTimeData;
        CfBdCrmDiagnoseBaseDataModel preDateTimeData;
        if (cityType==2) { // 说明是省份
            curDateTimeData = calcModel.getCurProvinceData().get(cityId);
            preDateTimeData = calcModel.getPreProvinceData().get(cityId);
        }else {
            curDateTimeData = calcModel.getCurCityData().get(cityId);
            preDateTimeData = calcModel.getPreCityData().get(cityId);
        }
        Map<Integer, Double> offlineAndOnlineContribution = getOfflineAndOnlineContribution(curDateTimeData, preDateTimeData);
        cityIdMapOnlineWithOfflineContribution.put(cityId, offlineAndOnlineContribution);
        return offlineAndOnlineContribution;
    }

    private Map<Integer, Double> getOrgOfflineAndOnlineContribution(long orgId){
        if (orgIdMapOnlineWithOfflineContribution==null) orgIdMapOnlineWithOfflineContribution = Maps.newHashMap();
        Map<Integer, Double> cacheMap = orgIdMapOnlineWithOfflineContribution.get(orgId);
        if (cacheMap!=null && cacheMap.size()>0) return cacheMap;
        List<BdCrmOrgAndCityRelationDO> relationList = Optional.ofNullable(calcModel.getOrgIdMapOrgAndCityRelationList().get(orgId)).orElse(Lists.newArrayList());
        // 昨日数据  CfBdCrmDiagnoseBaseDataModel
        CfBdCrmDiagnoseBaseDataModel curDateTimeData = CfBdCrmDiagnoseBaseDataModel.aggregateData(relationList.stream()
                .map(item -> item.getBindType() == 0? calcModel.getCurProvinceData().get((long) item.getCityId()) : calcModel.getCurCityData().get((long) item.getCityId()))
                .filter(item -> item!=null)
                .collect(Collectors.toList()));
        // 昨日同比上周数据  CfBdCrmDiagnoseBaseDataModel
        CfBdCrmDiagnoseBaseDataModel preDateTimeData = CfBdCrmDiagnoseBaseDataModel.aggregateData(relationList.stream()
                .map(item -> item.getBindType() == 0? calcModel.getPreProvinceData().get((long) item.getCityId()) : calcModel.getPreCityData().get((long) item.getCityId()))
                .filter(item -> item!=null)
                .collect(Collectors.toList()));
        Map<Integer, Double> offlineAndOnlineContribution = getOfflineAndOnlineContribution(curDateTimeData, preDateTimeData);
        orgIdMapOnlineWithOfflineContribution.put(orgId, offlineAndOnlineContribution);
        return offlineAndOnlineContribution;
    }

    public Map<Integer, Double> getOfflineAndOnlineContribution(CfBdCrmDiagnoseBaseDataModel curDateTimeData, CfBdCrmDiagnoseBaseDataModel preDateTimeData){
        if (curDateTimeData==null || preDateTimeData==null) return Maps.newHashMap();
        double marketInfluenceForSupposeOnline = getMarketInfluenceForOnlineOrOffline(curDateTimeData, preDateTimeData,preDateTimeData.getSdOnlineCaseAmount() + curDateTimeData.getSdOfflineCaseAmount());
        double marketInfluenceForSupposeOffline = getMarketInfluenceForOnlineOrOffline(curDateTimeData, preDateTimeData, preDateTimeData.getSdOfflineCaseAmount() + curDateTimeData.getSdOnlineCaseAmount());
        double totalMarketInfluence = marketInfluenceForSupposeOnline + marketInfluenceForSupposeOffline;
        Map<Integer, Double> result = Maps.newHashMap();
        result.put(OFFLINE, totalMarketInfluence!=0 ? new BigDecimal(marketInfluenceForSupposeOffline)
                .divide(new BigDecimal(totalMarketInfluence), 4, HALF_UP).doubleValue():0L);
        result.put(ONLINE, totalMarketInfluence!=0 ? new BigDecimal(marketInfluenceForSupposeOnline)
                .divide(new BigDecimal(totalMarketInfluence), 4, HALF_UP).doubleValue():0L);
        return result;
    }
    // 计算 线下、线上 市占影响值
    private double getMarketInfluenceForOnlineOrOffline(CfBdCrmDiagnoseBaseDataModel curDateTimeData, CfBdCrmDiagnoseBaseDataModel preDateTimeData, int supposeCaseAmount) {
        double supposeTotalCaseAmount = GrowthtoolUtil.precisionDouble(curDateTimeData.getQsTotalCaseAmount() + supposeCaseAmount,4);
        double supposeShareChange = 0D;
        if (supposeTotalCaseAmount!=0){
            supposeShareChange = GrowthtoolUtil.precisionDouble(new BigDecimal(supposeCaseAmount)
                    .divide(new BigDecimal(supposeTotalCaseAmount), 4, HALF_UP).doubleValue()-preDateTimeData.getSdMarketShare(),4);
        }
        return GrowthtoolUtil.precisionDouble(curDateTimeData.getSdMarketShare() - preDateTimeData.getSdMarketShare() - supposeShareChange,4);
    }

    private CfBdCrmDiagnoseBaseDataExtModel calcExtModel(List<CfBdCrmDiagnoseBaseDataModel> dataTmp,
                                                         Map<Long,CfBdCrmDiagnoseBaseDataModel> curDateTimeMap,
                                                         Map<Long,CfBdCrmDiagnoseBaseDataModel> preDateTimeMap) {
        // 上周的市占
        double preDateSdMarketShare = CfBdCrmDiagnoseBaseDataModel.aggregateData(preDateTimeMap.values()).getSdMarketShare();
        // 同步增长市占
        double marketShareChange = CfBdCrmDiagnoseBaseDataModel.aggregateData(curDateTimeMap.values()).getSdMarketShare() - preDateSdMarketShare;
        // 计算 假设当前组织数据未变的数据
        CfBdCrmDiagnoseBaseDataModel supposeDataModel = CfBdCrmDiagnoseBaseDataModel.aggregateData(dataTmp);
        // 假设市占变化
        double supposeMarketChange = GrowthtoolUtil.precisionDouble(supposeDataModel.getSdMarketShare() - preDateSdMarketShare,4);
        // 对市占影响值
        double marketInfluence = GrowthtoolUtil.precisionDouble(marketShareChange-supposeMarketChange,4);
        return new CfBdCrmDiagnoseBaseDataExtModel(supposeDataModel,supposeMarketChange,marketInfluence);
    }
}
