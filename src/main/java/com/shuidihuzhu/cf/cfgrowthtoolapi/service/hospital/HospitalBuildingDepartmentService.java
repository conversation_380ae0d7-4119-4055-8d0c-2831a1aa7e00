package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital;


import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalBuildingDepartmentDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.department.BuildingFloorAreaInfoCheckVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.DepartmentForCModel;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;


/**
 * 楼宇-科室信息(HospitalBuildingDepartment)表服务接口
 *
 * <AUTHOR>
 * @since 2021-01-04 17:25:57
 */
public interface HospitalBuildingDepartmentService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    HospitalBuildingDepartmentDO queryById(long id);

    List<HospitalBuildingDepartmentDO> listByIds(List<Integer> ids);


    List<HospitalBuildingDepartmentDO> listByBuildingIds(List<Integer> buildingIds);


    List<HospitalBuildingDepartmentDO> listByBuildingIdAndName(int buildingId, String departmentName);

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    List<HospitalBuildingDepartmentDO> queryAllByLimit(int offset, int limit);

    /**
     * 新增数据
     *
     * @param hospitalBuildingDepartment 实例对象
     */
    int insert(HospitalBuildingDepartmentDO hospitalBuildingDepartment);

    /**
     * 修改数据
     *
     * @param hospitalBuildingDepartment 实例对象
     */
    int update(HospitalBuildingDepartmentDO hospitalBuildingDepartment);

    /**
     * 保存科室归一结果
     */
    void updateClassBuildingDepartment(long id, String classifyBuildingDepartment);

    int updateBuildingFloorArea(List<Integer> ids, String buildingFloorArea);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);

    /**
     * 通过buildingId删除数据
     *
     * @param buildingId 主键
     * @return 是否成功
     */
    boolean deleteByBuildingId(int buildingId);


    List<HospitalBuildingDepartmentDO> listByHospitalCode(String vhospitalCode);

    List<HospitalBuildingDepartmentDO> listByVhospitalCodes(List<String> hospitalCodeList);

    //精准匹配  vhospitalCode 非空   departmentName  非空
    HospitalBuildingDepartmentDO queryByHospitalCodeAndName(String vhospitalCode, String departmentName);

    //模糊查询
    List<DepartmentForCModel> listDepartmentV2(String vhospitalCode, String departmentName);

    void changeImportantFlag(List<Integer> departmentIds, int importantFlag);

    List<HospitalBuildingDepartmentDO> listByHospitalName(String hospitalName, String hospitalCity);

    void syncNoClassifyDepartment();

    BuildingFloorAreaInfoCheckVo isDepartmentBelongsThisFloorArea(int buildingId, String buildingFloorArea);

    Response<Void> deleteBuildingFloorArea(int buildingId, String buildingFloorArea);

    HospitalBuildingDepartmentDO getById(long id);
}