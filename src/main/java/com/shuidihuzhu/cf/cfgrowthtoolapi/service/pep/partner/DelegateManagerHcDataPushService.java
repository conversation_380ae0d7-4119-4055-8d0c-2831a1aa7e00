package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.partner;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.KpiManagerDataTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.PepPushEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.team.TeamHcPerformanceModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiManagerDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import com.shuidihuzhu.cf.performance.data.meta.PepHcModel;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队 hc 数据推送
 *
 * @author: fengxuan
 * @create 2023-03-20 19:25
 **/
@Slf4j
@Service("delegateManagerHcDataPushService")
public class DelegateManagerHcDataPushService extends AbstractPushDataService {

    @Autowired
    private CfKpiManagerDataService kpiManagerDataService;

    @Override
    protected PepPushEnum getPushEnum() {
        return PepPushEnum.delegate_hc_performance;
    }

    @Override
    protected List<PepHcModel> listBusinessData(DateTime pushWhichDay, LotInfo lotInfo) {
        //找到对应的月份
        KpiManagerDataTypeEnum dataTypeEnum = KpiManagerDataTypeEnum.hc_performance;
        //找到对应的月份
        List<TeamHcPerformanceModel> dataList = kpiManagerDataService.listByDayKeyAndType(pushWhichDay.toString(GrowthtoolUtil.ymdfmt), dataTypeEnum.getCode())
                .stream()
                .map(item -> JSON.parseObject(item.getContent(), TeamHcPerformanceModel.class))
                .filter(item -> item.getUnique_code_type() == 1)
                .collect(Collectors.toList());
        List<PepHcModel> result = Lists.newArrayList();
        for (TeamHcPerformanceModel data : dataList) {
            PepHcModel personalEfficiencyModel = new PepHcModel();
            personalEfficiencyModel.setUserId(data.getUnique_code());
            personalEfficiencyModel.setBelong_unique_code(data.getBelong_unique_code());
            personalEfficiencyModel.setBelong_unique_name(data.getBelong_unique_name());
            personalEfficiencyModel.setHc_target(data.getHc_target());
            personalEfficiencyModel.setService_case_num(data.getService_case_num());
            personalEfficiencyModel.setMonth_key(data.getMonth_key());
            personalEfficiencyModel.setValid_hc_flag(data.getService_case_num() >= data.getHc_target() ? 1 : 0);
            personalEfficiencyModel.setLotId(lotInfo.getLotId());
            result.add(personalEfficiencyModel);
        }
        return result;
    }

}
