package com.shuidihuzhu.cf.cfgrowthtoolapi.service.homepage;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmUserExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.BdHomePageV2VO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.BdHomePageVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdAnnualReportDataVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCrmUserExtService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: fengxuan
 * @create 2023-04-10 14:09
 **/
@Service
public class HomePageService {
    @Autowired
    private ICfCrmUserExtService cfCrmUserExtService;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private ApolloService apolloService;

    public BdHomePageVo getBaseInfo(CrowdfundingVolunteer volunteer) {
        String uniqueCode = volunteer.getUniqueCode();
        //获取ext信息
        CfCrmUserExtDO cfCrmUserExtDO = cfCrmUserExtService.getCfCrmUserExtDOByUniqueCode(uniqueCode);
        BdHomePageVo baseInfo = null;
        if (cfCrmUserExtDO == null) {
            baseInfo = new BdHomePageVo(GeneralConstant.defaultHeadUrl,
                    volunteer.getVolunteerName(), shuidiCipher.decrypt(volunteer.getMobile()),
                    "", "", "", volunteer.getUniqueCode(), Lists.newArrayList(CrowdfundingVolunteerEnum.delegateRoles.contains(volunteer.getLevel()) ? GeneralConstant.defaultLabelForDELEGATE : GeneralConstant.defaultLabelForGW), "",
                    Lists.newArrayList(), Lists.newArrayList(), volunteer.getLevel());
        } else {
            cfCrmUserExtDO.setEncryptPhoneC(shuidiCipher.decrypt(cfCrmUserExtDO.getEncryptPhoneC()));
            cfCrmUserExtDO.setLevel(volunteer.getLevel());
            cfCrmUserExtDO.setName(volunteer.getVolunteerName());
            baseInfo = BdHomePageVo.convertBdHomePageVo(cfCrmUserExtDO);
        }
        return baseInfo;
    }

    public CfBdAnnualReportDataVo getCfBdAnnualReportDataVo(CrowdfundingVolunteer volunteer) {
        //获取ext信息
        CfCrmUserExtDO cfCrmUserExtDO = cfCrmUserExtService.getCfCrmUserExtDOByUniqueCode(volunteer.getUniqueCode());
        BdHomePageVo baseInfo = null;
        if (cfCrmUserExtDO == null) {
            baseInfo = new BdHomePageVo(GeneralConstant.defaultHeadUrl,
                    volunteer.getVolunteerName(), "",
                    "", "", "", volunteer.getUniqueCode(),
                    Lists.newArrayList(CrowdfundingVolunteerEnum.delegateRoles.contains(volunteer.getLevel()) ?
                            GeneralConstant.defaultLabelForDELEGATE : GeneralConstant.defaultLabelForGW), "",
                    Lists.newArrayList(), Lists.newArrayList(), volunteer.getLevel());
        } else {
            baseInfo = BdHomePageVo.convertBdHomePageVo(cfCrmUserExtDO);
        }
        return CfBdAnnualReportDataVo.build(baseInfo.getHeadUrl(), baseInfo.getLabelList());
    }



    public List<BdHomePageVo> getBaseInfos(List<String> uniqueCodes, Map<String, CrowdfundingVolunteer> volunteerMap) {
        List<CfCrmUserExtDO> cfCrmUserExtDOS = cfCrmUserExtService.getCrmUserExtModelByUniqueCodes(uniqueCodes);
        if (CollectionUtils.isEmpty(cfCrmUserExtDOS)) {
            return Lists.newArrayList();
        }

        cfCrmUserExtDOS.forEach(cfCrmUserExtDO -> {
            cfCrmUserExtDO.setEncryptPhoneC("");
            cfCrmUserExtDO.setWeixinXinCode("");
            CrowdfundingVolunteer volunteer = volunteerMap.getOrDefault(cfCrmUserExtDO.getUniqueCode(), null);
            if (volunteer != null) {
                cfCrmUserExtDO.setLevel(volunteer.getLevel());
                cfCrmUserExtDO.setName(volunteer.getVolunteerName());
            }
        });

        return cfCrmUserExtDOS.stream().map(BdHomePageVo::convertBdHomePageVo).collect(java.util.stream.Collectors.toList());
    }
}
