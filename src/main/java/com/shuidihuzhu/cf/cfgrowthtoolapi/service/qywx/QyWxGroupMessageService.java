package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.*;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.client.cf.admin.model.AiGenerateForwardParam;
import com.shuidihuzhu.client.cf.growthtool.model.BdQywxGroupMessageModel;

/**
 * 企业微信消息服务接口
 *
 * <AUTHOR>
 * @since 2023-04-11 11:33:18
 */
public interface QyWxGroupMessageService {


    //提醒添加人员
    void remindAddMember(BdQyWxCaseGroupMappingDO groupMapping);

    //欢迎消息
    void welcomeMessage(BdQyWxCaseGroupMappingDO groupMapping);

    //捐单成功消息
    void donateSuccess(CrowdfundingInfo crowdfundingInfo, CrowdfundingOrder crowdfundingOrder, long donateUserId);

    //筹款日常促转消息
    void sendMorningPromotionMessage();

    //筹款日结消息
    void sendEveningFundraisingSummary();

    //筹款周报消息
    void sendFundraisingWeeklyReport();

    //通用消息
    void sendCommonMessage(BdQywxGroupMessageModel messageModel);

    void sendCreateGroupSucMsg(BdQyWxCaseGroupMappingDO caseGroup, String content);
    
    /**
     * 发送卡片消息
     * 
     * @param caseGroup 案例群聊绑定信息
     * @param msgType 消息类型
     * @return 是否发送成功
     */
    BdQyWxMsgRecordDO sendAndRecordCardMessage(BdQyWxCaseGroupMappingDO caseGroup, int msgType, CrowdfundingInfo crowdfundingInfo, String channel, String preMsgId);

    QywxGroupAiShareContentDO createAiShareContent(BdQyWxCaseGroupMappingDO caseGroup, CrowdfundingInfo caseInfo, int generateType, String gratitudeReason);

    AiGenerateForwardParam getAiGenerateForwardParam(BdQyWxCaseGroupMappingDO caseGroup, CrowdfundingInfo caseInfo, int generateType, String gratitudeReason);

}