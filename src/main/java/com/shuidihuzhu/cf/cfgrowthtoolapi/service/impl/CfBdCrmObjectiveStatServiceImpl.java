package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfBdCrmOrgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmObjectiveCompleteDetailModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmObjectiveIndicatorValueModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfBdCrmObjectiveCycleService;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmObjectiveManageConfigMapper;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmObjectiveOrgMemberSnapshotMapper;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmObjectiveStatDetailMapper;
import com.shuidihuzhu.cf.dao.es.EsCfBdCrmObjectiveOrgMemberSnapshotDao;
import com.shuidihuzhu.cf.dao.es.EsCfBdCrmObjectiveStatDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmObjectiveStatMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfBdCrmObjectiveStatService;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.*;
import java.util.stream.Collectors;

import static java.math.RoundingMode.HALF_UP;

/**
 * @author: wanghui
 * @create: 2021/2/23 上午10:39
 */
@Slf4j
@Service
public class CfBdCrmObjectiveStatServiceImpl implements CfBdCrmObjectiveStatService {

    @Resource
    private CfBdCrmObjectiveStatMapper cfBdCrmObjectiveStatMapper;
    @Resource
    private CfBdCrmObjectiveStatDetailMapper cfBdCrmObjectiveStatDetailMapper;
    @Autowired
    private CfBdCrmObjectiveCycleService cfBdCrmObjectiveCycleServiceImpl;
    @Resource
    private CfBdCrmObjectiveManageConfigMapper cfBdCrmObjectiveManageConfigMapper;
    @Resource
    private CfBdCrmObjectiveOrgMemberSnapshotMapper cfBdCrmObjectiveOrgMemberSnapshotMapper;
    @Resource
    private EsCfBdCrmObjectiveOrgMemberSnapshotDao esCfBdCrmObjectiveOrgMemberSnapshotDao;
    @Resource
    private EsCfBdCrmObjectiveStatDao esCfBdCrmObjectiveStatDao;

    @Resource(name = "cfOlapCaseStat")
    private RedissonHandler redissonHandler;
    @Autowired
    private ApolloService apolloService;

    @Override
    public List<CfBdCrmObjectiveIndicatorValueModel> listCountGroupByObjectiveIndicatorId(Long objectiveCycleId, List<String> uniqueCodeList) {
        if (CollectionUtils.isEmpty(uniqueCodeList)) return Lists.newArrayList();
        if (apolloService.isObjectiveManageEs()){
            return esCfBdCrmObjectiveStatDao.listCountGroupByObjectiveIndicatorId(objectiveCycleId, uniqueCodeList.stream().distinct().collect(Collectors.toList()));
        }
        return cfBdCrmObjectiveStatMapper.listCountGroupByObjectiveIndicatorId(objectiveCycleId, uniqueCodeList.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public Map<Integer, List<CfBdCrmObjectiveCompleteDetailModel>> getCountGroupByObjectiveIndicatorIdWithUniqueCode(Long objectiveCycleId, List<String> uniqueCodeList, List<Integer> objectiveIndicatorIdList) {
        if (CollectionUtils.isEmpty(uniqueCodeList) || CollectionUtils.isEmpty(objectiveIndicatorIdList)) return Maps.newHashMap();
        List<CfBdCrmObjectiveManageConfig> uniqueCodeManageConfigList = cfBdCrmObjectiveManageConfigMapper.listByCycleIdWithUniqueCodeList(objectiveCycleId, uniqueCodeList);
        Map<String,List<CfBdCrmObjectiveCompleteDetailModel>> uniqueCodeMapList;
        if (apolloService.isObjectiveManageEs()) {
            uniqueCodeMapList = esCfBdCrmObjectiveStatDao.listCountGroupByObjectiveIndicatorIdWithUniqueCode(objectiveCycleId, uniqueCodeList, objectiveIndicatorIdList).stream().collect(Collectors.groupingBy(CfBdCrmObjectiveCompleteDetailModel::getKey));
        }else {
            uniqueCodeMapList = cfBdCrmObjectiveStatMapper.listCountGroupByObjectiveIndicatorIdWithUniqueCode(objectiveCycleId, uniqueCodeList, objectiveIndicatorIdList).stream().collect(Collectors.groupingBy(CfBdCrmObjectiveCompleteDetailModel::getKey));
        }
        return uniqueCodeManageConfigList.stream().map(manageConfig -> {
            List<CfBdCrmObjectiveCompleteDetailModel> completeDetailModels = Optional.ofNullable(uniqueCodeMapList.get(manageConfig.getUniqueCode())).orElse(Lists.newArrayList());
            Map<Integer, CfBdCrmObjectiveCompleteDetailModel> indicatorIdMapDetail = completeDetailModels.stream().collect(Collectors.toMap(CfBdCrmObjectiveCompleteDetailModel::getObjectiveIndicatorId, Function.identity()));
            List<CfBdCrmObjectiveCompleteDetailModel> result = Lists.newArrayList();
            // 如果没有 配置目标项 则不用回填
            if (CollectionUtils.isEmpty(manageConfig.getConfigJsonModelList())) {
                return result;
            }
            for (CfBdCrmObjectiveIndicatorValueModel indicatorValueModel : manageConfig.getConfigJsonModelList()) {
                result.add(fullObjectValueWithRatio(Optional.ofNullable(indicatorIdMapDetail.get(indicatorValueModel.getObjectiveIndicatorId())).orElse(new CfBdCrmObjectiveCompleteDetailModel()), indicatorValueModel, manageConfig));
            }
            return result;
        }).flatMap(list -> list.stream()).collect(Collectors.groupingBy(CfBdCrmObjectiveCompleteDetailModel::getObjectiveIndicatorId));
    }


    @Override
    public Map<Integer, List<CfBdCrmObjectiveCompleteDetailModel>> getCountGroupByObjectiveIndicatorIdWithOrgId(Long objectiveCycleId, List<Long> orgIdList, String uniqueCode, List<Integer> objectiveIndicatorIdList) {
        if (CollectionUtils.isEmpty(orgIdList) || CollectionUtils.isEmpty(objectiveIndicatorIdList)) return Maps.newHashMap();
        // 获得组织的 目标值
        Map<Long, CfBdCrmObjectiveManageConfig> orgIdMap = cfBdCrmObjectiveManageConfigMapper.listByCycleIdWithOrgIdList(objectiveCycleId, orgIdList)
                .stream().collect(Collectors.toMap(CfBdCrmObjectiveManageConfig::getOrgId, Function.identity(), (before, after) -> before));
        // 获得组织的 信息 比如：orgPath
        Map<Long, CfBdCrmOrgModel> orgIdMapOrgModel = cfBdCrmObjectiveOrgMemberSnapshotMapper.listCfBdCrmOrgModelByCycleIdWithOrgIdList(objectiveCycleId, orgIdList)
                .stream().collect(Collectors.toMap(CfBdCrmOrgModel::getOrgId, Function.identity(), (before, after) -> before));
        // 获得每个组织的 详细统计数据
        Map<String, List<CfBdCrmObjectiveCompleteDetailModel>> orgPathMapCompleteDetail = Maps.newHashMap();
        for (CfBdCrmOrgModel cfBdCrmOrgModel:orgIdMapOrgModel.values()) {
            Map<String, List<CfBdCrmObjectiveCompleteDetailModel>> _orgPathMapCompleteDetail;
            if (apolloService.isObjectiveManageEs()) {
                _orgPathMapCompleteDetail = esCfBdCrmObjectiveStatDao.listCountGroupByObjectiveIndicatorIdWithOrgPath(objectiveCycleId, cfBdCrmOrgModel.getOrgPath(),String.join("-",cfBdCrmOrgModel.getOrgPath(),"%"), uniqueCode, objectiveIndicatorIdList)
                        .stream()
                        .collect(Collectors.groupingBy(CfBdCrmObjectiveCompleteDetailModel::getKey));
            }else {
                _orgPathMapCompleteDetail = cfBdCrmObjectiveStatMapper.listCountGroupByObjectiveIndicatorIdWithOrgPath(objectiveCycleId, cfBdCrmOrgModel.getOrgPath(), uniqueCode, objectiveIndicatorIdList)
                        .stream()
                        .collect(Collectors.groupingBy(CfBdCrmObjectiveCompleteDetailModel::getKey));
            }
            orgPathMapCompleteDetail.putAll(_orgPathMapCompleteDetail);
        }
        List<String> objectiveIndicatorIdWithKeyList = buildObjectiveIndicatorIdWithKeyList(objectiveIndicatorIdList, orgIdList);
        return objectiveIndicatorIdWithKeyList.stream()
                .map(objectiveIndicatorIdWithKey -> buildObjectiveCompleteDetailModel(objectiveIndicatorIdWithKey,orgIdMapOrgModel, orgPathMapCompleteDetail, orgIdMap))
                .collect(Collectors.groupingBy(CfBdCrmObjectiveCompleteDetailModel::getObjectiveIndicatorId));
    }
    @Override
    public void incrCurrentWeekCaseAmountExceed500Num(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo){
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) return;
        CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = orgMemberSnapshotList.get(getMaxPathIndex(orgMemberSnapshotList));
        List<CfBdCrmObjectiveStatDetail> statDetailList = cfBdCrmObjectiveStatDetailMapper.listByIndicatorIdWithCaseIdWithCycleId(CfBdCrmObjectiveIndicatorEnum.WEEK_CHOU_KUAN_500.getId(), crowdfundingInfo.getId(), orgMemberSnapshot.getObjectiveCycleId());
        if (CollectionUtils.isNotEmpty(statDetailList)) {
            log.info("incrCurrentWeekCaseAmountExceed500Num 已统计 statDetail:{}", JSON.toJSONString(statDetailList));
            return;
        }
        boolean b = redissonHandler.setNX("GROWTH_CHOU_KUAN_500_" + crowdfundingInfo.getId(), 1, 5000);
        if (!b) {
            log.info("incrCurrentWeekCaseAmountExceed500Num 该案例已处理 caseId:{}", crowdfundingInfo.getId());
            return;
        }
        handleStatAndDetail(orgMemberSnapshot, crowdfundingInfo, CfBdCrmObjectiveIndicatorEnum.WEEK_CHOU_KUAN_500);
    }

    @Override
    public void incrCurrentMonthCaseAmountExceed5000Num(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo){
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) return;
        CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = orgMemberSnapshotList.get(getMaxPathIndex(orgMemberSnapshotList));
        List<CfBdCrmObjectiveStatDetail> statDetailList = cfBdCrmObjectiveStatDetailMapper.listByIndicatorIdWithCaseIdWithCycleId(CfBdCrmObjectiveIndicatorEnum.MONTH_CHOU_KUAN_5000.getId(), crowdfundingInfo.getId(), orgMemberSnapshot.getObjectiveCycleId());
        if (CollectionUtils.isNotEmpty(statDetailList)) {
            log.info("incrCurrentWeekCaseAmountExceed5000Num 已统计 statDetail:{}", JSON.toJSONString(statDetailList));
            return;
        }
        boolean b = redissonHandler.setNX("GROWTH_MONTH_CHOU_KUAN_5000_" + crowdfundingInfo.getId(), 1, 5000);
        if (!b) {
            log.info("incrCurrentWeekCaseAmountExceed5000Num 该案例已处理 caseId:{}", crowdfundingInfo.getId());
            return;
        }
        handleStatAndDetail(orgMemberSnapshot, crowdfundingInfo, CfBdCrmObjectiveIndicatorEnum.MONTH_CHOU_KUAN_5000);
    }


    @Override
    public void incrCurrentMonthCaseNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo){
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) return;
        CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = orgMemberSnapshotList.get(getMaxPathIndex(orgMemberSnapshotList));
        List<CfBdCrmObjectiveStatDetail> statDetailList = cfBdCrmObjectiveStatDetailMapper.listByIndicatorIdWithCaseIdWithCycleId(CfBdCrmObjectiveIndicatorEnum.MONTH_FA_QI.getId(), crowdfundingInfo.getId(), orgMemberSnapshot.getObjectiveCycleId());
        if (CollectionUtils.isNotEmpty(statDetailList)) {
            log.info("incrCurrentMonthCaseNum 已统计 statDetail:{}", JSON.toJSONString(statDetailList));
            return;
        }
        boolean b = redissonHandler.setNX("GROWTH_FA_QI_" + crowdfundingInfo.getId(), 1, 5000);
        if (!b) {
            log.info("incrCurrentMonthCaseNum 该案例已处理 caseId:{}", crowdfundingInfo.getId());
            return;
        }
        handleStatAndDetail(orgMemberSnapshot, crowdfundingInfo, CfBdCrmObjectiveIndicatorEnum.MONTH_FA_QI);
    }

    @Override
    public void incrCurrentMonthValidCaseNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo){
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) return;
        CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = orgMemberSnapshotList.get(getMaxPathIndex(orgMemberSnapshotList));
        List<CfBdCrmObjectiveStatDetail> statDetailList = cfBdCrmObjectiveStatDetailMapper.listByIndicatorIdWithCaseIdWithCycleId(CfBdCrmObjectiveIndicatorEnum.MONTH_YOU_XIAO_FA_QI.getId(), crowdfundingInfo.getId(), orgMemberSnapshot.getObjectiveCycleId());
        if (CollectionUtils.isNotEmpty(statDetailList)) {
            log.info("incrCurrentMonthValidCaseNum 已统计 statDetail:{}", JSON.toJSONString(statDetailList));
            return;
        }
        boolean b = redissonHandler.setNX("GROWTH_YOU_XIAO_FA_QI_" + crowdfundingInfo.getId(), 1, 5000);
        if (!b) {
            log.info("incrCurrentMonthValidCaseNum 该案例已处理 caseId:{}", crowdfundingInfo.getId());
            return;
        }
        handleValidCaseStatAndDetail(orgMemberSnapshot, crowdfundingInfo, CfBdCrmObjectiveIndicatorEnum.MONTH_YOU_XIAO_FA_QI);
    }


    @Override
    public void incrCurrentWeekCaseNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo){
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) return;
        CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = orgMemberSnapshotList.get(getMaxPathIndex(orgMemberSnapshotList));
        List<CfBdCrmObjectiveStatDetail> statDetailList = cfBdCrmObjectiveStatDetailMapper.listByIndicatorIdWithCaseIdWithCycleId(CfBdCrmObjectiveIndicatorEnum.WEEK_FA_QI.getId(), crowdfundingInfo.getId(), orgMemberSnapshot.getObjectiveCycleId());
        if (CollectionUtils.isNotEmpty(statDetailList)) {
            log.info("incrCurrentWeekCaseNum 已统计 statDetail:{}", JSON.toJSONString(statDetailList));
            return;
        }
        boolean b = redissonHandler.setNX("GROWTH_WEEK_FA_QI_" + crowdfundingInfo.getId(), 1, 5000);
        if (!b) {
            log.info("incrCurrentWeekCaseNum 该案例已处理 caseId:{}", crowdfundingInfo.getId());
            return;
        }
        handleStatAndDetail(orgMemberSnapshot, crowdfundingInfo, CfBdCrmObjectiveIndicatorEnum.WEEK_FA_QI);
    }

    @Override
    public void incrCurrentWeekValidCaseNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CrowdfundingInfo crowdfundingInfo){
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) return;
        CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = orgMemberSnapshotList.get(getMaxPathIndex(orgMemberSnapshotList));
        List<CfBdCrmObjectiveStatDetail> statDetailList = cfBdCrmObjectiveStatDetailMapper.listByIndicatorIdWithCaseIdWithCycleId(CfBdCrmObjectiveIndicatorEnum.WEEK_YOU_XIAO_FA_QI.getId(), crowdfundingInfo.getId(), orgMemberSnapshot.getObjectiveCycleId());
        if (CollectionUtils.isNotEmpty(statDetailList)) {
            log.info("incrCurrentWeekValidCaseNum 已统计 statDetail:{}", JSON.toJSONString(statDetailList));
            return;
        }
        boolean b = redissonHandler.setNX("GROWTH_WEEK_YOU_XIAO_FA_QI_" + crowdfundingInfo.getId(), 1, 5000);
        if (!b) {
            log.info("incrCurrentWeekValidCaseNum 该案例已处理 caseId:{}", crowdfundingInfo.getId());
            return;
        }
        handleValidCaseStatAndDetail(orgMemberSnapshot, crowdfundingInfo, CfBdCrmObjectiveIndicatorEnum.WEEK_YOU_XIAO_FA_QI);
    }

    @Override
    public void incrCurrentMonthDonatedNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList){
        incrIndicator(orgMemberSnapshotList, CfBdCrmObjectiveIndicatorEnum.MONTH_JUAN_DAN);
    }

    @Override
    public void incrCurrentWeekDonatedNum(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList){
        incrIndicator(orgMemberSnapshotList, CfBdCrmObjectiveIndicatorEnum.WEEK_JUAN_DAN);
    }


    public void setCurrentWeekSubmitThroughSuccess(String uniqueCode, long coverValue) {
        covertStatValue(uniqueCode, CfBdCrmObjectiveIndicatorEnum.SUBMIT_THROUGH_SUCCESS, coverValue);
    }

    public void setCurrentMonthSubmitThroughSuccess(String uniqueCode, long coverValue) {
        covertStatValue(uniqueCode, CfBdCrmObjectiveIndicatorEnum.MONTH_SUBMIT_THROUGH_SUCCESS, coverValue);
    }


    private void incrIndicator(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CfBdCrmObjectiveIndicatorEnum indicatorEnum) {
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) {
            return;
        }
        CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = orgMemberSnapshotList.get(getMaxPathIndex(orgMemberSnapshotList));
        CfBdCrmObjectiveStat cfBdCrmObjectiveStat = cfBdCrmObjectiveStatMapper.getByIndicatorIdWithUniqueCodeWithCycleId(indicatorEnum.getId(), orgMemberSnapshot.getUniqueCode(), orgMemberSnapshot.getObjectiveCycleId());
        if (cfBdCrmObjectiveStat == null) {
            cfBdCrmObjectiveStat = CfBdCrmObjectiveStat.build(orgMemberSnapshot, indicatorEnum.getId());
            cfBdCrmObjectiveStatMapper.insertRecord(cfBdCrmObjectiveStat);
        } else {
            cfBdCrmObjectiveStatMapper.updateStatValue(cfBdCrmObjectiveStat.getId());
        }
    }


    private void covertStatValue(String uniqueCode, CfBdCrmObjectiveIndicatorEnum indicatorEnum, long coverValue) {
        CfBdCrmObjectiveCycle objectiveCycle = cfBdCrmObjectiveCycleServiceImpl.getCurrentObjectiveCycleByObjectiveType(DateUtil.getCurrentDateStr(), indicatorEnum.getObjectiveTypeEnum().getType());
        if (objectiveCycle != null) {
            List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList = cfBdCrmObjectiveOrgMemberSnapshotMapper.listByCycleIdWithUniqueCode(objectiveCycle.getId(), uniqueCode);
            if (CollectionUtils.isNotEmpty(orgMemberSnapshotList)) {
                CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = orgMemberSnapshotList.get(getMaxPathIndex(orgMemberSnapshotList));
                CfBdCrmObjectiveStat cfBdCrmObjectiveStat = cfBdCrmObjectiveStatMapper.getByIndicatorIdWithUniqueCodeWithCycleId(indicatorEnum.getId(), orgMemberSnapshot.getUniqueCode(), orgMemberSnapshot.getObjectiveCycleId());
                if (cfBdCrmObjectiveStat == null) {
                    cfBdCrmObjectiveStat = CfBdCrmObjectiveStat.build(orgMemberSnapshot, indicatorEnum.getId());
                    cfBdCrmObjectiveStat.setStatValue(coverValue);
                    cfBdCrmObjectiveStatMapper.insertRecord(cfBdCrmObjectiveStat);
                } else {
                    cfBdCrmObjectiveStatMapper.updateStatValueByCover(cfBdCrmObjectiveStat.getId(), coverValue);
                }
            }
        }
    }


    private void decrIndicator(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, CfBdCrmObjectiveIndicatorEnum indicatorEnum) {
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) {
            return;
        }
        CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = orgMemberSnapshotList.get(getMaxPathIndex(orgMemberSnapshotList));
        CfBdCrmObjectiveStat cfBdCrmObjectiveStat = cfBdCrmObjectiveStatMapper.getByIndicatorIdWithUniqueCodeWithCycleId(indicatorEnum.getId(), orgMemberSnapshot.getUniqueCode(), orgMemberSnapshot.getObjectiveCycleId());
        if (cfBdCrmObjectiveStat != null) {
            cfBdCrmObjectiveStatMapper.decrStatValue(cfBdCrmObjectiveStat.getId());
        }
    }


    @Override
    public void incrIndicator(String uniqueCode, Set<CfBdCrmObjectiveIndicatorEnum> indicatorEnums) {
        BiConsumer<List<CfBdCrmObjectiveOrgMemberSnapshot>, CfBdCrmObjectiveIndicatorEnum> consumer = this::incrIndicator;
        handleIndicatorData(uniqueCode, indicatorEnums, consumer);
    }

    @Override
    public void decrIndicator(String uniqueCode, Set<CfBdCrmObjectiveIndicatorEnum> indicatorEnums) {
        BiConsumer<List<CfBdCrmObjectiveOrgMemberSnapshot>, CfBdCrmObjectiveIndicatorEnum> consumer = this::decrIndicator;
        handleIndicatorData(uniqueCode, indicatorEnums, consumer);
    }


    private void handleIndicatorData(String uniqueCode, Set<CfBdCrmObjectiveIndicatorEnum> indicatorEnums, BiConsumer<List<CfBdCrmObjectiveOrgMemberSnapshot>, CfBdCrmObjectiveIndicatorEnum> consumer) {
        CfBdCrmObjectiveCycle weekCycle = cfBdCrmObjectiveCycleServiceImpl.getCurrentObjectiveCycleByObjectiveType(DateUtil.getCurrentDateStr(), CfBdCrmObjectiveCycle.ObjectiveTypeEnum.WEEK_OBJECTIVE.getType());
        if (weekCycle != null) {
            List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList = cfBdCrmObjectiveOrgMemberSnapshotMapper.listByCycleIdWithUniqueCode(weekCycle.getId(), uniqueCode);
            if (CollectionUtils.isNotEmpty(orgMemberSnapshotList)) {
                indicatorEnums.stream()
                        .filter(item -> Objects.equals(item.getObjectiveTypeEnum(), CfBdCrmObjectiveCycle.ObjectiveTypeEnum.WEEK_OBJECTIVE))
                        .forEach(item -> consumer.accept(orgMemberSnapshotList, item));
            }
        }
        CfBdCrmObjectiveCycle monthCycle = cfBdCrmObjectiveCycleServiceImpl.getCurrentObjectiveCycleByObjectiveType(DateUtil.getCurrentDateStr(), CfBdCrmObjectiveCycle.ObjectiveTypeEnum.MONTH_OBJECTIVE.getType());
        if (monthCycle != null) {
            List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList = cfBdCrmObjectiveOrgMemberSnapshotMapper.listByCycleIdWithUniqueCode(monthCycle.getId(), uniqueCode);
            if (CollectionUtils.isNotEmpty(orgMemberSnapshotList)) {
                indicatorEnums.stream()
                        .filter(item -> Objects.equals(item.getObjectiveTypeEnum(), CfBdCrmObjectiveCycle.ObjectiveTypeEnum.MONTH_OBJECTIVE))
                        .forEach(item -> consumer.accept(orgMemberSnapshotList, item));
            }
        }
    }


    /******************************* private method **********************************/
    private List<String> buildObjectiveIndicatorIdWithKeyList(List<Integer> objectiveIndicatorIdList, List<Long> orgIdList) {
        List<String> result = Lists.newArrayList();
        for (Integer objectiveIndicatorId:objectiveIndicatorIdList) {
            for (Long orgId:orgIdList) {
                result.add(String.format("%s-%s", objectiveIndicatorId, orgId));
            }
        }
        return result;
    }

    private CfBdCrmObjectiveCompleteDetailModel fullObjectValueWithRatio(CfBdCrmObjectiveCompleteDetailModel objectiveCompleteDetailModel, List<CfBdCrmObjectiveIndicatorValueModel> objectiveIndicatorValueModelList){
        Map<Integer, CfBdCrmObjectiveIndicatorValueModel> objectiveIndicatorIdMap = objectiveIndicatorValueModelList.stream().collect(Collectors.toMap(CfBdCrmObjectiveIndicatorValueModel::getObjectiveIndicatorId, Function.identity()));
        objectiveCompleteDetailModel.setObjectiveValue(Optional.ofNullable(objectiveIndicatorIdMap.get(objectiveCompleteDetailModel.getObjectiveIndicatorId())).map(CfBdCrmObjectiveIndicatorValueModel::getObjectiveValue).orElse(null));
        if (objectiveCompleteDetailModel.getObjectiveValue()==null || objectiveCompleteDetailModel.getObjectiveValue()==0) {
            objectiveCompleteDetailModel.setRatio((double) 0);
        }else {
            objectiveCompleteDetailModel.setRatio(new BigDecimal(Optional.ofNullable(objectiveCompleteDetailModel.getRealValue()).orElse(0))
                    .divide(new BigDecimal(objectiveCompleteDetailModel.getObjectiveValue()), 4, HALF_UP).doubleValue());
        }
        return objectiveCompleteDetailModel;
    }

    private CfBdCrmObjectiveCompleteDetailModel fullObjectValueWithRatio(CfBdCrmObjectiveCompleteDetailModel completeDetailModel, CfBdCrmObjectiveIndicatorValueModel indicatorValueModel, CfBdCrmObjectiveManageConfig manageConfig){
        completeDetailModel.setKey(manageConfig.getUniqueCode());
        completeDetailModel.setName(manageConfig.getMisName());
        completeDetailModel.setObjectiveIndicatorId(indicatorValueModel.getObjectiveIndicatorId());
        completeDetailModel.setObjectiveValue(indicatorValueModel.getObjectiveValue());
        if (completeDetailModel.getObjectiveValue()==null || completeDetailModel.getObjectiveValue()==0) {
            completeDetailModel.setRatio((double) 0);
        }else if (completeDetailModel.getRealValue()==null){
            completeDetailModel.setRatio(new BigDecimal(Optional.ofNullable(completeDetailModel.getRealValue()).orElse(0))
                    .divide(new BigDecimal(completeDetailModel.getObjectiveValue()), 4, HALF_UP).doubleValue());
        }
        return completeDetailModel;
    }

    private CfBdCrmObjectiveCompleteDetailModel buildObjectiveCompleteDetailModel(String objectiveIndicatorIdWithKey,
                                                                                  Map<Long, CfBdCrmOrgModel> orgIdMapOrgModel,
                                                                                  Map<String, List<CfBdCrmObjectiveCompleteDetailModel>> orgPathMapCompleteDetail,
                                                                                  Map<Long, CfBdCrmObjectiveManageConfig> orgIdMap){
        Integer objectiveIndicatorId = Integer.valueOf(objectiveIndicatorIdWithKey.split("-")[0]);
        Long orgId = Long.valueOf(objectiveIndicatorIdWithKey.split("-")[1]);
        CfBdCrmOrgModel cfBdCrmOrgModel = orgIdMapOrgModel.get(orgId);
        CfBdCrmObjectiveCompleteDetailModel objectiveCompleteDetailModel = new CfBdCrmObjectiveCompleteDetailModel(orgId.toString(), cfBdCrmOrgModel.getOrgName(), objectiveIndicatorId, null);
        List<CfBdCrmObjectiveCompleteDetailModel> orgObjectiveCompleteDetailModel = Lists.newArrayList();
        for (Map.Entry<String, List<CfBdCrmObjectiveCompleteDetailModel>> entry:orgPathMapCompleteDetail.entrySet()){
            if (entry.getKey().equals(cfBdCrmOrgModel.getOrgPath()) || entry.getKey().contains(cfBdCrmOrgModel.getOrgPath()+"-")) {
                orgObjectiveCompleteDetailModel.addAll(entry.getValue());
            }
        }
        Map<Integer, List<CfBdCrmObjectiveCompleteDetailModel>> objectiveIndicatorIdMapDetailModel = orgObjectiveCompleteDetailModel.stream().collect(Collectors.groupingBy(CfBdCrmObjectiveCompleteDetailModel::getObjectiveIndicatorId));
        objectiveCompleteDetailModel.setRealValue(Optional.ofNullable(objectiveIndicatorIdMapDetailModel.get(objectiveIndicatorId)).orElse(Lists.newArrayList()).stream().map(completeDetailModel -> Optional.ofNullable(completeDetailModel.getRealValue()).orElse(0)).reduce((total, item) -> total += item).orElse(0));
        CfBdCrmObjectiveManageConfig manageConfig = orgIdMap.get(orgId);
        if (manageConfig != null && CollectionUtils.isNotEmpty(manageConfig.getConfigJsonModelList())){
            objectiveCompleteDetailModel = fullObjectValueWithRatio(objectiveCompleteDetailModel, manageConfig.getConfigJsonModelList());
        }
        return objectiveCompleteDetailModel;
    }

    private void handleStatAndDetail(CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot, CrowdfundingInfo crowdfundingInfo, CfBdCrmObjectiveIndicatorEnum objectiveIndicatorEnum) {
        CfBdCrmObjectiveStat cfBdCrmObjectiveStat = cfBdCrmObjectiveStatMapper.getByIndicatorIdWithUniqueCodeWithCycleId(objectiveIndicatorEnum.getId(), orgMemberSnapshot.getUniqueCode(), orgMemberSnapshot.getObjectiveCycleId());
        if (cfBdCrmObjectiveStat==null) {
            cfBdCrmObjectiveStat = CfBdCrmObjectiveStat.build(orgMemberSnapshot, objectiveIndicatorEnum.getId());
            cfBdCrmObjectiveStatMapper.insertRecord(cfBdCrmObjectiveStat);
        }else {
            cfBdCrmObjectiveStatMapper.updateStatValue(cfBdCrmObjectiveStat.getId());
        }
        cfBdCrmObjectiveStatDetailMapper.insertSelective(new CfBdCrmObjectiveStatDetail(orgMemberSnapshot.getObjectiveCycleId(),
                cfBdCrmObjectiveStat.getId(), objectiveIndicatorEnum.getId(),
                (long) crowdfundingInfo.getId(), crowdfundingInfo.getInfoId()));
    }

    private void handleValidCaseStatAndDetail(CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot, CrowdfundingInfo crowdfundingInfo, CfBdCrmObjectiveIndicatorEnum objectiveIndicatorEnum) {

        List<CfBdCrmObjectiveStatDetail> statDetailList = cfBdCrmObjectiveStatDetailMapper.listByIndicatorIdWithCaseIdWithCycleId(objectiveIndicatorEnum.getId(), crowdfundingInfo.getId(), orgMemberSnapshot.getObjectiveCycleId());
//        先去 目标管理统计明细表 查该案例数据 对应案例如果没有相应的目标项对应记录则处理 有则不处理
        if (CollectionUtils.isEmpty(statDetailList)) {
            CfBdCrmObjectiveStat cfBdCrmObjectiveStat = cfBdCrmObjectiveStatMapper.getByIndicatorIdWithUniqueCodeWithCycleId(objectiveIndicatorEnum.getId(), orgMemberSnapshot.getUniqueCode(), orgMemberSnapshot.getObjectiveCycleId());
            if (cfBdCrmObjectiveStat == null) {
                cfBdCrmObjectiveStat = CfBdCrmObjectiveStat.build(orgMemberSnapshot, objectiveIndicatorEnum.getId());
                cfBdCrmObjectiveStatMapper.insertRecord(cfBdCrmObjectiveStat);
            } else {
                cfBdCrmObjectiveStatMapper.updateStatValue(cfBdCrmObjectiveStat.getId());
            }
            cfBdCrmObjectiveStatDetailMapper.insertSelective(new CfBdCrmObjectiveStatDetail(orgMemberSnapshot.getObjectiveCycleId(),
                    cfBdCrmObjectiveStat.getId(), objectiveIndicatorEnum.getId(),
                    (long) crowdfundingInfo.getId(), crowdfundingInfo.getInfoId()));
        }
    }

    private int getMaxPathIndex(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList){
        List<String> orgPathList = orgMemberSnapshotList.stream().map(CfBdCrmObjectiveOrgMemberSnapshot::getOrgPath).collect(Collectors.toList());
        int i = 0;
        int maxLength = 0;
        for (int j=0;j<orgPathList.size();j++) {
            if (maxLength < orgPathList.get(j).split("-").length) {
                maxLength = orgPathList.get(j).split("-").length;
                i = j;
            }
        }
        return i;
    }
}
