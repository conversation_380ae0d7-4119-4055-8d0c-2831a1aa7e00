package com.shuidihuzhu.cf.cfgrowthtoolapi.service.gr;


import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.gr.GrCustomerConnectRecordDO;

import java.util.Date;
import java.util.List;

/**
 * gr拜访记录(GrCustomerConnectRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2021-04-09 15:32:53
 */
public interface GrCustomerConnectRecordService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    GrCustomerConnectRecordDO queryById(long id);

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    List<GrCustomerConnectRecordDO> queryAllByLimit(int offset, int limit);

    /**
     * 新增数据
     *
     * @param grCustomerConnectRecord 实例对象
     * @return 实例对象
     */
    GrCustomerConnectRecordDO insert(GrCustomerConnectRecordDO grCustomerConnectRecord);

    /**
     * 修改数据
     *
     * @param grCustomerConnectRecord 实例对象
     * @return 实例对象
     */
    GrCustomerConnectRecordDO update(GrCustomerConnectRecordDO grCustomerConnectRecord);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(long id);

    int countRecord(int customerId, String purpose);

    List<GrCustomerConnectRecordDO> pageRecord(int customerId, String purpose, int offset, int pageSize);

    List<GrCustomerConnectRecordDO> listByCustomerIds(List<Integer> customerIds, Date startTime, Date endTime);

    int countAddRecordCnt(List<Integer> customerIds, Date startTime, Date endTime);

}