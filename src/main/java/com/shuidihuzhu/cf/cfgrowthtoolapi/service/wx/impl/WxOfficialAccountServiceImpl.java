package com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.wx.CfWxOfficialAccountBaseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.wx.IWxOfficialAccountService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.LocalCacheHelper;
import com.shuidihuzhu.cf.dao.wx.CfWxOfficialAccountBaseDataDao;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-03-26
 */
@Slf4j
@Service
public class WxOfficialAccountServiceImpl implements IWxOfficialAccountService {

    @Autowired
    private CfWxOfficialAccountBaseDataDao cfWxOfficialAccountBaseDataDao;

    private LocalCacheHelper<Integer, Map<Integer, List<CfWxOfficialAccountBaseDataDO>>> wxOfficialAccountBaseDataCache =
            LocalCacheHelper.create("getWxOfficialAccountBaseDataDOCache", this::listWxOfficialAccountBaseDataDO, 5, TimeUnit.MINUTES);

    @Override
    public Map<Integer, List<CfWxOfficialAccountBaseDataDO>> getAllThirdTypeFromCache(Integer thirdType) {
        if (Objects.isNull(thirdType) || thirdType <= 0) {
            thirdType = -1;
        }
        try {
            return wxOfficialAccountBaseDataCache.getCache().get(thirdType);
        } catch (ExecutionException e) {
            log.warn("{} listWxOfficialAccountBaseDataDO err ",this.getClass().getSimpleName(),e);
        }
        return listWxOfficialAccountBaseDataDO(thirdType);
    }

    @Override
    public int updateThumbMediaIdById(String thumbMediaId, Long id) {
        return cfWxOfficialAccountBaseDataDao.updateThumbMediaIdById(thumbMediaId,id);
    }

    @Override
    public int updateTagIdById(Integer tagId, Long id) {
        return cfWxOfficialAccountBaseDataDao.updateTagIdById(tagId,id);
    }

    @Override
    public Map<Integer, List<CfWxOfficialAccountBaseDataDO>> listWxOfficialAccountBaseDataDO(Integer thirdType) {
        if (Objects.isNull(thirdType) || thirdType <= 0) {
            thirdType = -1;
        }
        return cfWxOfficialAccountBaseDataDao.listAllWxOfficialAccountBaseDataByThirdType(thirdType)
                    .stream().collect(Collectors.groupingBy(CfWxOfficialAccountBaseDataDO::getThirdType));
    }
}
