package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IpepDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiBdScoreDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfBdKpiEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CustomPerformanceScoreModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.KPICustomRuleScoreModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiBdScoreService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiBdScoreDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */
@Data
@Primary
@Service
@Slf4j
public class CfKpiBdScoreServiceImpl implements ICfKpiBdScoreService {

    @Autowired
    private CfKpiBdScoreDao cfKpiBdScoreDao;


    @Autowired
    private IpepDelegate pepDelegate;

    @Override
    public OpResult<List<KPICustomRuleScoreModel>> querySuperiorScoreDetail(String monthkey, String uniqueCode) {
        if (StringUtils.isEmpty(monthkey)){
            monthkey = DateUtil.getYYYYMMStr(DateUtil.getCurrentDate());
        }
        CfKpiBdScoreDO cfKpiBdScoreDO = cfKpiBdScoreDao.queryBdScoreList(monthkey,uniqueCode);
        List<KPICustomRuleScoreModel> result = Lists.newArrayList();
        //CfKpiBdScoreDO转KPICustomRuleScoreModel
        if (Objects.isNull(cfKpiBdScoreDO)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.NOT_TIMETO_SCORE);
        }
        try {
            String assessmentScoreJson = cfKpiBdScoreDO.getAssessmentScoreJson();
            List<CustomPerformanceScoreModel> model = JSON.parseArray(assessmentScoreJson, CustomPerformanceScoreModel.class);
            model.forEach(item -> {
                if (CfBdKpiEnums.CustomTypeEnum.GUWEN_SHANG_JI_PINGFEN.getType().equals(item.getCustomType())) {
                    result.addAll(item.getKpiCustomRuleScoreModels());
                }
                if (CfBdKpiEnums.CustomTypeEnum.SHENGJI_SHANG_JI_PINGFEN.getType().equals(item.getCustomType())) {
                    result.addAll(item.getKpiCustomRuleScoreModels());
                }
                if (CfBdKpiEnums.CustomTypeEnum.PARTITION_SHANG_JI_PINGFEN.getType().equals(item.getCustomType())){
                    result.addAll(item.getKpiCustomRuleScoreModels());
                }
                if (CfBdKpiEnums.CustomTypeEnum.AREA_SHANG_JI_PINGFEN.getType().equals(item.getCustomType())){
                    result.addAll(item.getKpiCustomRuleScoreModels());
                }
                if (CfBdKpiEnums.CustomTypeEnum.AREA_PROVINCE_SHANG_JI_PINGFEN.getType().equals(item.getCustomType())){
                    result.addAll(item.getKpiCustomRuleScoreModels());
                }
            });
        } catch (Exception e) {
            log.error("querySuperiorScoreDetail error", e);
        }

        return OpResult.createSucResult(result);
    }

    @Override
    public List<CfKpiBdScoreDO> listAllKpiBdScore(String monthKey) {
        return cfKpiBdScoreDao.listAllKpiBdScore(monthKey);
    }

    @Override
    public List<CfKpiBdScoreDO> getKpiBdList(Integer level, String monthKey, List<String> uniqueCodeList) {
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> cfKpiBdScoreDao.getKpiBdList(level, monthKey, list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
    }

    @Override
    public int batchSaveOrUpdate(List<CfKpiBdScoreDO> kpiBdList, String monthKey, Integer level) {
        List<String> uniqueCodeList = kpiBdList.stream().map(CfKpiBdScoreDO::getUniqueCode).collect(Collectors.toList());
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        List<String> inDbDataList = listList.parallelStream()
                .map(list -> cfKpiBdScoreDao.getUniqueCodeList(level, monthKey, list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
        List<CfKpiBdScoreDO> needUpdateDataList = kpiBdList.stream()
                .filter(cfKpiBdScoreDO -> inDbDataList.contains(cfKpiBdScoreDO.getUniqueCode())).collect(Collectors.toList());

        List<CfKpiBdScoreDO> needInsertDataList = kpiBdList.stream()
                .filter(cfKpiBdScoreDO -> !inDbDataList.contains(cfKpiBdScoreDO.getUniqueCode())).collect(Collectors.toList());
        int result = 0;
        if (CollectionUtils.isNotEmpty(needUpdateDataList)){
            List<List<CfKpiBdScoreDO>> needUpdateDataListList = Lists.partition(needUpdateDataList, GeneralConstant.MAX_PAGE_SIZE);
            result+= needUpdateDataListList.parallelStream()
                    .map(list -> cfKpiBdScoreDao.batchUpdate(list))
                    .reduce((total,item)->total+=item).get();

            log.info(this.getClass().getName()+" batchSaveOrUpdate batchUpdate ret:{}",result);
        }
        if (CollectionUtils.isNotEmpty(needInsertDataList)){
            List<List<CfKpiBdScoreDO>> needInsertDataListList = Lists.partition(needInsertDataList, GeneralConstant.MAX_PAGE_SIZE);
            int insertResult = needInsertDataListList.parallelStream()
                    .map(list -> cfKpiBdScoreDao.batchInsert(list))
                    .reduce((total,item)->total+=item).get();
            result +=insertResult;
            log.info(this.getClass().getName()+" batchSaveOrUpdate batchInsert ret:{}",insertResult);
        }
        log.info(this.getClass().getName()+" batchSaveOrUpdate total ret:{}",result);
        return result;
    }

    @Override
    public List<CustomPerformanceScoreModel> listCustomScore(CfKpiBdScoreDO cfKpiBdScoreDO) {
        List<CustomPerformanceScoreModel> result = Lists.newArrayList();
        if (Objects.isNull(cfKpiBdScoreDO)){
            return result;
        }
        //CfKpiBdScoreDO转KPICustomRuleScoreModel
        try {
            String assessmentScoreJson = cfKpiBdScoreDO.getAssessmentScoreJson();
            List<CustomPerformanceScoreModel> model = JSON.parseArray(assessmentScoreJson, CustomPerformanceScoreModel.class);
            Map<Integer,List<CustomPerformanceScoreModel>> perforManceMap = model.stream().collect(Collectors.groupingBy(CustomPerformanceScoreModel::getCustomType));

            if (CollectionUtils.isNotEmpty(perforManceMap.get(CfBdKpiEnums.CustomTypeEnum.GUWEN_QI_TA_PINGFEN.getType()))){
                result.addAll(perforManceMap.get(CfBdKpiEnums.CustomTypeEnum.GUWEN_QI_TA_PINGFEN.getType()));
            }
            if (CollectionUtils.isNotEmpty(perforManceMap.get(CfBdKpiEnums.CustomTypeEnum.SHENGJI_QI_TA_PINGFEN.getType()))) {
                result.addAll(perforManceMap.get(CfBdKpiEnums.CustomTypeEnum.SHENGJI_QI_TA_PINGFEN.getType()));
            }
            if (CollectionUtils.isNotEmpty(perforManceMap.get(CfBdKpiEnums.CustomTypeEnum.AREA_QI_TA_PINGFEN.getType()))){
                result.addAll(perforManceMap.get(CfBdKpiEnums.CustomTypeEnum.AREA_QI_TA_PINGFEN.getType()));
            }
            if (CollectionUtils.isNotEmpty(perforManceMap.get(CfBdKpiEnums.CustomTypeEnum.PARTITION_QI_TA_PINGFEN.getType()))){
                result.addAll(perforManceMap.get(CfBdKpiEnums.CustomTypeEnum.PARTITION_QI_TA_PINGFEN.getType()));
            }

        } catch (Exception e) {
            log.error("querySuperiorScoreDetail error", e);
        }
        return result;
    }

    @Override
    public OpResult<List<KPICustomRuleScoreModel>> queryTeamRecScoreDetail(String monthKey, String uniqueCode) {
        CfKpiBdScoreDO cfKpiBdScoreDO = cfKpiBdScoreDao.queryBdScoreList(monthKey,uniqueCode);
        List<KPICustomRuleScoreModel> result = Lists.newArrayList();
        //CfKpiBdScoreDO转KPICustomRuleScoreModel
        if (Objects.isNull(cfKpiBdScoreDO)){
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        try {
            String assessmentScoreJson = cfKpiBdScoreDO.getAssessmentScoreJson();
            List<CustomPerformanceScoreModel> model = JSON.parseArray(assessmentScoreJson, CustomPerformanceScoreModel.class);
            model.forEach(item -> {
                if (CfBdKpiEnums.CustomTypeEnum.SHENGJI_TUAN_DUI_ZHAO_PIN.getType().equals(item.getCustomType())) {
                    result.addAll(item.getKpiCustomRuleScoreModels());
                }
            });
        } catch (Exception e) {
            log.error("querySuperiorScoreDetail error", e);
        }
        return OpResult.createSucResult(result);
    }

    @Override
    public List<CfKpiBdScoreDO> listKpiBdByMonthAndUniqueCodes(String monthKey, List<String> uniqueCodeList) {
        if (CollectionUtils.isEmpty(uniqueCodeList)){
            return Lists.newArrayList();
        }
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> cfKpiBdScoreDao.listKpiBdByMonthAndUniqueCodes(monthKey, list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
    }

    @Override
    public int batchSaveOrUpdate(List<CfKpiBdScoreDO> kpiBdList, String monthKey) {
        List<String> uniqueCodeList = kpiBdList.stream().map(CfKpiBdScoreDO::getUniqueCode).collect(Collectors.toList());
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        List<String> inDbDataList = listList.parallelStream()
                .map(list -> cfKpiBdScoreDao.listKpiBdByMonthAndUniqueCodes(monthKey,list).stream().map(CfKpiBdScoreDO::getUniqueCode).collect(Collectors.toList()))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
        List<CfKpiBdScoreDO> needUpdateDataList = kpiBdList.stream()
                .filter(cfKpiBdScoreDO -> inDbDataList.contains(cfKpiBdScoreDO.getUniqueCode())).collect(Collectors.toList());

        List<CfKpiBdScoreDO> needInsertDataList = kpiBdList.stream()
                .filter(cfKpiBdScoreDO -> !inDbDataList.contains(cfKpiBdScoreDO.getUniqueCode())).collect(Collectors.toList());
        int result = 0;
        if (CollectionUtils.isNotEmpty(needUpdateDataList)){
            List<List<CfKpiBdScoreDO>> needUpdateDataListList = Lists.partition(needUpdateDataList, GeneralConstant.MAX_PAGE_SIZE);
            result+= needUpdateDataListList.parallelStream()
                    .map(list -> cfKpiBdScoreDao.batchUpdate(list))
                    .reduce((total,item)->total+=item).get();
            pepDelegate.syncScoreData(needInsertDataList);
            log.info(this.getClass().getName()+" batchSaveOrUpdate batchUpdate ret:{}",result);
        }
        if (CollectionUtils.isNotEmpty(needInsertDataList)){
            List<List<CfKpiBdScoreDO>> needInsertDataListList = Lists.partition(needInsertDataList, GeneralConstant.MAX_PAGE_SIZE);
            int insertResult = needInsertDataListList.parallelStream()
                    .map(list -> cfKpiBdScoreDao.batchInsert(list))
                    .reduce((total,item)->total+=item).get();
            result +=insertResult;
            pepDelegate.syncScoreData(needInsertDataList);
            log.info(this.getClass().getName()+" batchSaveOrUpdate batchInsert ret:{}",insertResult);
        }
        log.info(this.getClass().getName()+" batchSaveOrUpdate total ret:{}",result);
        return result;
    }
    @Override
    public CfKpiBdScoreDO queryBdScoreList(String monthkey, String uniqueCode){
        return cfKpiBdScoreDao.queryBdScoreList(monthkey, uniqueCode);
    }

}
