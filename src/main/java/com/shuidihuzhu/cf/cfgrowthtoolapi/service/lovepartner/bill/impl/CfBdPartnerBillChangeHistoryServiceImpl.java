package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.lovepartner.CfBdPartnerBillChangeHistoryDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner.bill.CfBdPartnerBillChangeHistoryService;
import com.shuidihuzhu.cf.dao.lovepartner.CfBdPartnerBillChangeHistoryDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Yuhang
 * @Date 2023/2/20 11:19
 * 爱心伙伴账单修改增加相应记录和备注(cf_partner_opt_log)表服务接口
 */
@Service
public class CfBdPartnerBillChangeHistoryServiceImpl implements CfBdPartnerBillChangeHistoryService {

    @Autowired
    private CfBdPartnerBillChangeHistoryDao cfBdPartnerBillChangeHistoryDao;


    @Override
    public int insert(CfBdPartnerBillChangeHistoryDO cfBdPartnerBillChangeHistoryDo) {
        if (Objects.isNull(cfBdPartnerBillChangeHistoryDo)) {
            return 0;
        }
        return cfBdPartnerBillChangeHistoryDao.insert(cfBdPartnerBillChangeHistoryDo);
    }

    @Override
    public List<CfBdPartnerBillChangeHistoryDO> getCfBdPartnerBillChangeHistoryInfo(String partnerUniqueCode, int cycleId) {
        if (StringUtils.isEmpty(partnerUniqueCode)) {
            return Lists.newArrayList();
        }
        return cfBdPartnerBillChangeHistoryDao.getCfBdPartnerBillChangeHistoryInfo(partnerUniqueCode, cycleId);
    }
}
