package com.shuidihuzhu.cf.cfgrowthtoolapi.service.snapshot;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.snapshot.BdDailyObjectiveReachDO;

import java.util.List;

/**
 * 日目标完成情况(BdDailyObjectiveReach)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-02 15:00:08
 */
public interface BdDailyObjectiveReachService {

    BdDailyObjectiveReachDO queryById(long id);

    boolean deleteById(long id);

    void addOrUpdate(BdDailyObjectiveReachDO bdDailyObjectiveReach);

    //查询人员的
    BdDailyObjectiveReachDO getByTargetTypeAndUniqueCode(String dateKey, String uniqueCode, int targetType);

    //只查询组织的
    BdDailyObjectiveReachDO getByTargetTypeAndOrgId(String dateKey, long orgId, int targetType);

    //查询组织的
    long sumReachByOrgIds(String dateKey, List<Long> orgIds, int targetType);

    //查询人员
    List<BdDailyObjectiveReachDO> listMemberByOrgId(String dateKey, long orgId, int targetType);

}
