package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.IBdTaskCheckService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.ICrmSelfBuiltOrgReadService;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-09-20 15:50
 **/
@Service
public class CaseValidTaskCheckImpl implements IBdTaskCheckService {

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Override
    public CrmBdTaskDO.TaskTypeEnum getTaskType() {
        return CrmBdTaskDO.TaskTypeEnum.case_valid;
    }

    @Override
    public boolean checkNeedCreateTask(BdTaskContext bdTaskContext) {
        //判断当前案例有没有达到{捐单量}>XX&{捐单金额}>XX的标准
        CfBdCaseInfoDo cfBdCaseInfoDo = bdTaskContext.getCfBdCaseInfoDo();
        Integer amount = cfBdCaseInfoDo.getAmount();
        int donateNum = cfBdCaseInfoDo.getDonateNum();

        List<Long> taskCenterValidCaseOrgList = apolloService.getTaskCenterValidCaseOrg();
        if (CollectionUtils.isNotEmpty(taskCenterValidCaseOrgList)) {
            BdCrmOrganizationDO bdCrmOrganizationDO = taskCenterValidCaseOrgList.stream()
                    .flatMap(item -> crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(item).stream())
                    .filter(item -> Objects.equals(item.getId(), (long) cfBdCaseInfoDo.getOrgId()))
                    .findFirst()
                    .orElse(null);
            if (bdCrmOrganizationDO != null) {
                int taskCenterValidCaseAmount = apolloService.getTaskCenterValidCaseAmount();
                int taskCenterValidCaseDonateNum = apolloService.getTaskCenterValidCaseDonateCount();
                return !(amount >= taskCenterValidCaseAmount && donateNum >= taskCenterValidCaseDonateNum);
            }
        }
        return !(amount >= 300000 && donateNum >= 100);
    }

    @Override
    public boolean checkTaskComplete(BdTaskContext bdTaskContext) {
        return !checkNeedCreateTask(bdTaskContext);
    }

}
