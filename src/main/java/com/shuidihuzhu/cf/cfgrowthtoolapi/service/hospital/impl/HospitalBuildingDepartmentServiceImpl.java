package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.impl;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.cfgrowthtoolapi.configuration.EsDataSoureConfigure;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.impl.AiDeptClassifyDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalAreaBuildingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.HospitalBuildingDepartmentDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.department.BuildingFloorAreaInfoCheckVo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.DepartmentForCModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.HospitalBuildingDepartmentMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalAreaBuildingService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.HospitalBuildingDepartmentService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.EsResultHandlerUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolCrusorQuery;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.hospital.HospitalBuildingDepartmentDao;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.esdk.EsClient;
import com.shuidihuzhu.esdk.utils.ClientTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 楼宇-科室信息(HospitalBuildingDepartment)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-01-04 17:25:57
 */
@Slf4j
@Service
public class HospitalBuildingDepartmentServiceImpl implements HospitalBuildingDepartmentService {

    @Resource
    private HospitalBuildingDepartmentDao hospitalBuildingDepartmentDao;

    @Autowired
    private HospitalAreaBuildingService areaBuildingService;

    @Autowired
    private HospitalBuildingDepartmentMapper buildingDepartmentMapper;

    @Autowired
    private AiDeptClassifyDelegate aiDeptClassifyDelegate;

    @Resource(name = EsDataSoureConfigure.ES_BEAN_NAME)
    private EsClient esClientDepartment;

    private static final String index = "shuidi_cf_clewtrack_hospital_building_department_alias";

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public HospitalBuildingDepartmentDO queryById(long id) {
        return hospitalBuildingDepartmentDao.queryById(id);
    }

    @Override
    public List<HospitalBuildingDepartmentDO> listByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return hospitalBuildingDepartmentDao.listByIds(ids);
    }

    @Override
    public List<HospitalBuildingDepartmentDO> listByBuildingIds(List<Integer> buildingIds) {
        if (CollectionUtils.isEmpty(buildingIds)) {
            return Lists.newArrayList();
        }
        return hospitalBuildingDepartmentDao.listByBuildingIds(buildingIds);
    }

    @Override
    public List<HospitalBuildingDepartmentDO> listByBuildingIdAndName(int buildingId, String departmentName) {
        if (buildingId <= 0) {
            return Lists.newArrayList();
        }
        return hospitalBuildingDepartmentDao.listByBuildingIdAndName(buildingId, departmentName);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    @Override
    public List<HospitalBuildingDepartmentDO> queryAllByLimit(int offset, int limit) {
        return hospitalBuildingDepartmentDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param hospitalBuildingDepartment 实例对象
     * @return 实例对象
     */
    @Override
    public int insert(HospitalBuildingDepartmentDO hospitalBuildingDepartment) {
        return hospitalBuildingDepartmentDao.insert(hospitalBuildingDepartment);
    }

    /**
     * 修改数据
     *
     * @param hospitalBuildingDepartment 实例对象
     * @return 实例对象
     */
    @Override
    public int update(HospitalBuildingDepartmentDO hospitalBuildingDepartment) {
        if (hospitalBuildingDepartment == null) {
            return 0;
        }
        return hospitalBuildingDepartmentDao.update(hospitalBuildingDepartment);
    }

    /**
     * 保存科室归一结果
     */
    @Override
    public void updateClassBuildingDepartment(long id, String classifyBuildingDepartment) {
        hospitalBuildingDepartmentDao.updateClassBuildingDepartment(id, classifyBuildingDepartment);
    }

    @Override
    public int updateBuildingFloorArea(List<Integer> ids, String buildingFloorArea) {
        return hospitalBuildingDepartmentDao.updateBuildingFloorArea(ids, buildingFloorArea);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(long id) {
        return hospitalBuildingDepartmentDao.deleteById(id) > 0;
    }

    @Override
    public boolean deleteByBuildingId(int buildingId) {
        return hospitalBuildingDepartmentDao.deleteByBuildingId(buildingId) > 0;
    }

    @Override
    public List<HospitalBuildingDepartmentDO> listByHospitalCode(String vhospitalCode) {
        if (StringUtils.isBlank(vhospitalCode)) {
            return Lists.newArrayList();
        }
        return hospitalBuildingDepartmentDao.listByHospitalCode(vhospitalCode);
    }

    @Override
    public List<HospitalBuildingDepartmentDO> listByVhospitalCodes(List<String> hospitalCodeList) {
        if (CollectionUtils.isEmpty(hospitalCodeList)) {
            return Lists.newArrayList();
        }
        return hospitalBuildingDepartmentDao.listByVhospitalCodes(hospitalCodeList);
    }

    @Override
    public HospitalBuildingDepartmentDO queryByHospitalCodeAndName(String vhospitalCode, String departmentName) {
        if (StringUtils.isBlank(vhospitalCode) || StringUtils.isBlank(departmentName)) {
            return null;
        }
        return hospitalBuildingDepartmentDao.queryByHospitalCodeAndName(vhospitalCode, departmentName);
    }



    @Override
    public List<DepartmentForCModel> listDepartmentV2(String vhospitalCode, String departmentName) {
        List<HospitalBuildingDepartmentDO> departmentDOList = Lists.newArrayList();
        if (StringUtils.isBlank(vhospitalCode)) {
            return Lists.newArrayList();
        }

        //当科室名称为空时,只需要查找mysql
        if (StringUtils.isNotBlank(departmentName)) {
            //添加es
            BoolQueryBuilder boolQueryBuilder = QueryBuilders
                    .boolQuery()
                    .must(QueryBuilders.termQuery("is_delete", 0))
                    .must(QueryBuilders.termQuery("vhospital_code", vhospitalCode));
            if (StringUtils.isNotBlank(departmentName)) {
                boolQueryBuilder.must(QueryBuilders.matchQuery("building_department", departmentName));
            }
            log.debug("builder:{}", boolQueryBuilder);
            try {
                List<Map<String, Object>> maps = ClientTools.searchResponse2Map(esClientDepartment.pullByQueryBuilders(EsDataSoureConfigure.CLUSTER_NAME, index, boolQueryBuilder, 0, 500));
                departmentDOList = EsResultHandlerUtil.handListResult(maps, HospitalBuildingDepartmentDO.class);
            } catch (Exception e) {
                log.error("从es获取科室失败", e);
            }
        }

        if (CollectionUtils.isEmpty(departmentDOList)) {
            //使用数据库兜底
            departmentDOList = hospitalBuildingDepartmentDao.listByHospitalCode(vhospitalCode);
            //判读下是否是否包含关键词
            if (StringUtils.isNotBlank(departmentName)) {
                departmentDOList = departmentDOList.stream()
                        .filter(item -> {
                            if (StringUtils.isBlank(departmentName)) {
                                return true;
                            }
                            if (StringUtils.isNotBlank(item.getBuildingDepartment())) {
                                return StringUtils.containsAny(item.getBuildingDepartment(), departmentName.toCharArray());
                            }
                            return false;
                        }).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(departmentDOList)) {
            return Lists.newArrayList();
        }

        //找到对应的楼宇id
        Set<Long> departmentIds = departmentDOList.stream().map(item -> (long) (item.getBuildingId())).collect(Collectors.toSet());
        Map<Integer, HospitalAreaBuildingDO> buildIdTDO = areaBuildingService.listByIds(Lists.newArrayList(departmentIds))
                .stream()
                .collect(Collectors.toMap(HospitalAreaBuildingDO::getId, Function.identity(), (before, after) -> before));

        //按照输入的departmentName计算评分
        List<DepartmentForCModel> departmentForCModels = departmentDOList.stream()
                .map(item -> {
                    DepartmentForCModel departmentForCModel = buildingDepartmentMapper.toDto(item);
                    int buildingId = item.getBuildingId();
                    HospitalAreaBuildingDO buildingDO = buildIdTDO.get(buildingId);
                    if (buildingDO != null) {
                        departmentForCModel.setBuildingName(buildingDO.getBuildingName());
                    }
                    int score = 0;
                    List<Integer> highLightIndexes = Lists.newArrayList();
                    //计算评分
                    if (StringUtils.isNotBlank(departmentName)) {
                        for (char c : departmentName.toCharArray()) {
                            if (item.getBuildingDepartment().contains(String.valueOf(c))) {
                                score++;
                                highLightIndexes.add(item.getBuildingDepartment().indexOf(c));
                            }
                        }
                        if (Objects.equals(departmentName, item.getBuildingDepartment())) {
                            departmentForCModel.setFullMatch(true);
                        }
                    }
                    departmentForCModel.setScore(score);
                    departmentForCModel.setHighLightIndexes(highLightIndexes);
                    return departmentForCModel;
                })
                .collect(Collectors.toList());
        //如果能完全匹配的直接完全匹配
        List<DepartmentForCModel> fullMatchModels = departmentForCModels.stream()
                .filter(DepartmentForCModel::isFullMatch)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fullMatchModels)) {
            return fullMatchModels;
        }
        //排序
        Ordering<HospitalBuildingDepartmentDO> ordering = Ordering
                .natural()
                .nullsLast()
                .onResultOf(departmentItem -> {
                    if (departmentItem != null && StringUtils.isNotBlank(departmentItem.getBuildingFloor())) {
                        return GrowthtoolUtil.parseStringToNum(departmentItem.getBuildingFloor().substring(0, Math.min(departmentItem.getBuildingFloor().length(), 2)));
                    }
                    return Integer.MAX_VALUE;
                });
        //排序,按照评分、楼层排序
        departmentForCModels = departmentForCModels.stream()
                .sorted(Comparator.comparing(DepartmentForCModel::getScore).reversed()
                        .thenComparing(ordering))
                .collect(Collectors.toList());
        return departmentForCModels;
    }

    @Override
    public void changeImportantFlag(List<Integer> departmentIds, int importantFlag) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return;
        }
        hospitalBuildingDepartmentDao.changeImportantFlag(departmentIds, importantFlag);
    }

    @Override
    public List<HospitalBuildingDepartmentDO> listByHospitalName(String hospitalName, String hospitalCity) {
        if (StringUtils.isBlank(hospitalName) || StringUtils.isBlank(hospitalCity)) {
            return Lists.newArrayList();
        }
        return hospitalBuildingDepartmentDao.listByHospitalName(hospitalName, hospitalCity);
    }

    @Override
    public  void syncNoClassifyDepartment() {
        GrowthtoolCrusorQuery.queryByCursorWithConsumer(
                (id, limit) -> hospitalBuildingDepartmentDao.listByCursor(id.longValue(), limit), item -> (long)item.getId(),
                item -> {
                    for (HospitalBuildingDepartmentDO departmentDO : item) {
                        if (StringUtils.isBlank(departmentDO.getClassifyBuildingDepartment())) {
                            String classifyBuildingDepartment = aiDeptClassifyDelegate.analyseSingle(departmentDO.getBuildingDepartment());
                            updateClassBuildingDepartment(departmentDO.getId(), classifyBuildingDepartment);
                        }
                    }
                }
            );
    }

    @Override
    public BuildingFloorAreaInfoCheckVo isDepartmentBelongsThisFloorArea(int buildingId, String buildingFloorArea) {
        List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOList = hospitalBuildingDepartmentDao.listByBuildingIds(Lists.newArrayList(buildingId));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(hospitalBuildingDepartmentDOList)) {
            return BuildingFloorAreaInfoCheckVo.builder().build();
        }

        List<HospitalBuildingDepartmentDO> buildingDepartmentDOList = hospitalBuildingDepartmentDOList.stream().filter(item -> Objects.equals(item.getBuildingFloorArea(), buildingFloorArea)).collect(Collectors.toList());
        //科室是否已经所属楼层区域
        boolean departmentBelongsThisFloorArea = CollectionUtils.isNotEmpty(buildingDepartmentDOList);

        //科室是否重复
        boolean repetitionDepartment = false;
        //获取没有楼层区域的科室
        List<HospitalBuildingDepartmentDO> buildingDepartmentDOS = hospitalBuildingDepartmentDOList.stream().filter(v -> StringUtils.isEmpty(v.getBuildingFloorArea())).collect(Collectors.toList());
        for (HospitalBuildingDepartmentDO buildingDepartmentDO : buildingDepartmentDOList) {
            //判断科室是否重复
            boolean duplicateDepartment = buildingDepartmentDOS.stream().anyMatch(item -> (Objects.equals(item.getBuildingFloor(), buildingDepartmentDO.getBuildingFloor())
                    && Objects.equals(item.getBuildingDepartment(), buildingDepartmentDO.getBuildingDepartment()))
                    && item.getId() != buildingDepartmentDO.getId());
            if (duplicateDepartment) {
                repetitionDepartment = true;
                break;
            }
        }

        return BuildingFloorAreaInfoCheckVo.builder()
                .repetitionDepartment(repetitionDepartment)
                .departmentBelongsThisFloorArea(departmentBelongsThisFloorArea)
                .build();
    }

    @Override
    public Response<Void> deleteBuildingFloorArea(int buildingId, String buildingFloorArea) {
        BuildingFloorAreaInfoCheckVo vo = isDepartmentBelongsThisFloorArea(buildingId, buildingFloorArea);
        if (Objects.nonNull(vo.getRepetitionDepartment()) && vo.getRepetitionDepartment()) {
            return NewResponseUtil.makeFail("科室重复，无法删除");
        }

        HospitalAreaBuildingDO hospitalAreaBuildingDO = areaBuildingService.queryById(buildingId);
        if (hospitalAreaBuildingDO == null) {
            return NewResponseUtil.makeFail("楼宇不存在");
        }

        List<String> buildingFloorAreaList = Splitter.on(",").splitToList(hospitalAreaBuildingDO.getBuildingFloorArea());
        if (CollectionUtils.isEmpty(buildingFloorAreaList)) {
            return NewResponseUtil.makeFail("楼层区域不存在");
        }

        List<String> newBuildingFloorAreaList = buildingFloorAreaList.stream().filter(v -> !v.equals(buildingFloorArea)).collect(Collectors.toList());
        String newBuildingFloorArea = CollectionUtils.isEmpty(newBuildingFloorAreaList) ? StringUtils.EMPTY : Joiner.on(",").join(newBuildingFloorAreaList);
        int res = areaBuildingService.updateBuildingFloorArea(buildingId, newBuildingFloorArea);
        if (res > 0) {
            List<HospitalBuildingDepartmentDO> hospitalBuildingDepartmentDOList = hospitalBuildingDepartmentDao.listByBuildingIds(Lists.newArrayList(buildingId));
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(hospitalBuildingDepartmentDOList)) {
                List<Integer> idList = hospitalBuildingDepartmentDOList.stream().filter(v -> buildingFloorArea.equals(v.getBuildingFloorArea())).map(HospitalBuildingDepartmentDO::getId).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(idList)) {
                    hospitalBuildingDepartmentDao.updateBuildingFloorArea(idList, StringUtils.EMPTY);
                }
            }
        }
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public HospitalBuildingDepartmentDO getById(long id) {
        return hospitalBuildingDepartmentDao.getById(id);
    }
}