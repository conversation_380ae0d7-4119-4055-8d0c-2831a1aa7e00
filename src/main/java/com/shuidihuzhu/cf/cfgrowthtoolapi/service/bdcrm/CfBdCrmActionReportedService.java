package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmActionReportedDO;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/5/27 上午10:44
 */
public interface CfBdCrmActionReportedService{


    int insert(CfBdCrmActionReportedDO record);

    long countCurrentDayByUniqueCodeWithIconType(String startTime, String uniqueCode, int iconType);

    List<CfBdCrmActionReportedDO> listCurrentDayByUniqueCodeWithIconType(String currentDateStr, String uniqueCode, int iconType, long num);
}
