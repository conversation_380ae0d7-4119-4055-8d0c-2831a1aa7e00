package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.subtask.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdSubTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdSubTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask.subtask.IBdSubTaskCheckService;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2023-09-20 15:59
 **/
@Service
public class CaseMaterialReviewPassTaskCheckImpl implements IBdSubTaskCheckService {

    @Override
    public CrmBdSubTaskDO.TaskTypeEnum getTaskType() {
        return CrmBdSubTaskDO.TaskTypeEnum.case_material_review_pass;
    }

    @Override
    public boolean checkNeedCreateTask(BdSubTaskContext bdSubTaskContext) {
        return true;
    }

    @Override
    public boolean checkTaskComplete(BdSubTaskContext bdSubTaskContext) {
        return true;
    }

}
