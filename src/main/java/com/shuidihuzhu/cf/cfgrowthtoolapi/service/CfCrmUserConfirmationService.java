package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCrmUserConfirmationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.msg.UserConfirmRemindMessage;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.confirmation.ConfirmationPageParam;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

/**
 * 确权信息表(CfCrmUserConfirmation)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-03 19:28:16
 */
public interface CfCrmUserConfirmationService {

    CfCrmUserConfirmationDO queryById(long id);

    int insert(CfCrmUserConfirmationDO cfCrmUserConfirmation);

    void addOrUpdateByPrepose(PreposeMaterialModel.MaterialInfoVo materialInfoVo, CrowdfundingVolunteer crowdfundingVolunteer);

    void addOrUpdateByDraft(CrowdfundingBaseInfoBackup crowdfundingBaseInfoBackup, CrowdfundingVolunteer crowdfundingVolunteer);

    void sendRemindMsg(UserConfirmRemindMessage confirmRemindMessage);

    int update(CfCrmUserConfirmationDO cfCrmUserConfirmation);

    int updatSanpshotPic(long id, String imageUrl);

    boolean deleteById(long id);

    CfCrmUserConfirmationDO getByDraftId(long draftId);

    CfCrmUserConfirmationDO getByPreposeMaterialId(long preposeMaterialId);

    void updateStatus(long id, int status);

    long countPage(ConfirmationPageParam confirmationPageParam);

    List<CfCrmUserConfirmationDO> listPage(ConfirmationPageParam confirmationPageParam);


}
