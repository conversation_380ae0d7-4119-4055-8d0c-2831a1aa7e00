package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdCropOrgMappingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdCropUserMappingDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.sass.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl.CfCrmUserExtService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.qywechat.QyWechatSignUtil;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum.VolunteerCropTypeEnum.shuidi_chou_support;


/**
 * https://wdh.feishu.cn/wiki/wikcnkd6M62WaHAIn0bmmkN8H9g
 *
 * @author: fengxuan
 * @create 2023-01-12 11:09
 **/
@Slf4j
@Service
public class SyncOrgMemberToQyWechatService {

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private ICrmOrganizationRelationService organizationRelationService;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;

    @Autowired
    private BdCropUserMappingService bdCropUserMappingService;

    @Autowired
    private BdCropOrgMappingService bdCropOrgMappingService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private ShuidiCipher shuidiCipher;

    public static final String NEW_MEMBER_DEPARTMENT_ID = "39";

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private CfCrmUserExtService userExtService;


    public void addMemberWhenVolunteerChange(CrowdfundingVolunteer crowdfundingVolunteer) {
        //非线上不同步数据
        if (!applicationService.isProduction()) {
            return;
        }
        BdCropUserMappingDO bdCropUserMappingDO = bdCropUserMappingService.queryByUniqueCode(crowdfundingVolunteer.getUniqueCode(),
                shuidi_chou_support.getType());
        if (bdCropUserMappingDO == null && rightCropId(crowdfundingVolunteer.getCropId())) {
            log.info("人员变更自动添加企业微信");
            syncMemberByNewAdd(crowdfundingVolunteer);
        }
    }


    private boolean rightCropId(String cropId) {
        return StringUtils.isNotEmpty(cropId) && Objects.equals(cropId, String.valueOf(shuidi_chou_support.getType()));
    }


    public void syncMemberByNewAdd(CrowdfundingVolunteer crowdfundingVolunteer) {
        //非线上不同步数据
        if (!applicationService.isProduction()) {
            return;
        }
        if (rightCropId(crowdfundingVolunteer.getCropId())) {
            syncMember(crowdfundingVolunteer, NEW_MEMBER_DEPARTMENT_ID);
        }
    }


    //离职和解绑会调用
    public void deleteMember(CrowdfundingVolunteer crowdfundingVolunteer) {
        //非线上不同步数据
        if (!applicationService.isProduction()) {
            return;
        }
        CrowdfundingVolunteerEnum.VolunteerCropTypeEnum volunteerCropTypeEnum = shuidi_chou_support;
        //判断人员是否有绑定
        BdCropUserMappingDO bdCropUserMappingDO = bdCropUserMappingService.queryByUniqueCode(crowdfundingVolunteer.getUniqueCode(),
                volunteerCropTypeEnum.getType());
        if (bdCropUserMappingDO != null) {
            QyWechatSignUtil.deleteUser(SassEmployDeleteDto.builder()
                    .saasSystemId(volunteerCropTypeEnum.getAppId())
                    .userId(convertUserId(crowdfundingVolunteer, volunteerCropTypeEnum))
                    .hrStatus("I")
                    .build(), volunteerCropTypeEnum.getAppId());
            //删除数据
            bdCropUserMappingService.deleteById(bdCropUserMappingDO.getId());
        }
    }


    /**
     * 添加新员工 先添加到特定组织
     */
    public void syncMember(CrowdfundingVolunteer crowdfundingVolunteer, String departmentId) {
        CrowdfundingVolunteerEnum.VolunteerCropTypeEnum volunteerCropTypeEnum = shuidi_chou_support;
        if (StringUtils.isBlank(departmentId)) {
            return;
        }
        String userId = convertUserId(crowdfundingVolunteer, volunteerCropTypeEnum);

        //如果有多个组织需要设置对应的主部门
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = crmMemberInfoService.listNotInTestRelation(crowdfundingVolunteer.getUniqueCode());
        //添加判断是否都在蜂鸟中
        if (bdCrmOrgUserRelationDOS.size() > 1) {
            Set<Long> needSyncOrgIds = Sets.newHashSet();
            List<Long> initOrgList = crmSelfBuiltOrgReadService.getOrgInfoList(volunteerCropTypeEnum.getInitOrgIds())
                    .stream()
                    .map(BdCrmOrganizationDO::getId)
                    .collect(Collectors.toList());
            for (Long orgId : initOrgList) {
                needSyncOrgIds.addAll(crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(orgId)
                        .stream()
                        .map(BdCrmOrganizationDO::getId)
                        .collect(Collectors.toList()));
            }
            bdCrmOrgUserRelationDOS = bdCrmOrgUserRelationDOS.stream()
                    .filter(item -> needSyncOrgIds.contains(item.getOrgId()))
                    .collect(Collectors.toList());
            List<BdCrmOrganizationDO> orgInfoList = crmSelfBuiltOrgReadService.getOrgInfoList(bdCrmOrgUserRelationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList()));
            //查找里面是否有归属关系
            List<BdCrmOrganizationDO> bdCrmOrganizationDOS = crmMemberInfoService.listShortestPath(orgInfoList);
            if (CollectionUtils.isNotEmpty(bdCrmOrganizationDOS)) {
                BdCrmOrganizationDO bdCrmOrganizationDO = bdCrmOrganizationDOS.get(0);
                departmentId = convertDepartmentId(String.valueOf(bdCrmOrganizationDO.getId()), volunteerCropTypeEnum);
            }
            for (BdCrmOrganizationDO bdCrmOrganizationDO : orgInfoList) {
                //蜂鸟计划不要设置负责人
                if (initOrgList.contains(bdCrmOrganizationDO.getId())) {
                    continue;
                }
                String orgDepartmentId = convertDepartmentId(String.valueOf(bdCrmOrganizationDO.getId()), volunteerCropTypeEnum);
                //需要调用部门接口设置负责人
                QyWechatSignUtil.setDepartmentLeader(SaasDepartmentDto.builder()
                                .saasSystemId(volunteerCropTypeEnum.getAppId())
                                .departmentId(orgDepartmentId)
                                .departmentName(bdCrmOrganizationDO.getOrgName())
                                .departmentStatus("A")
                                .parentId(getParentId(String.valueOf(bdCrmOrganizationDO.getParentId()), volunteerCropTypeEnum))
                                .adminUserId(userId).build(),
                        volunteerCropTypeEnum.getAppId());
            }
        }

        //设置主部门信息
        String expandData = createExpandData(crowdfundingVolunteer.getUniqueCode());
        int level = crowdfundingVolunteer.getLevel();
        CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(level);
        String jobName = null;
        if (roleEnum != null) {
            jobName = roleEnum.getDesc();
        }
        if (StringUtils.isNotBlank(crowdfundingVolunteer.getMobile()) && crowdfundingVolunteer.getMobile().length() > 11) {
            crowdfundingVolunteer.setMobile(shuidiCipher.decrypt(crowdfundingVolunteer.getMobile()));
        }
        //使用头图
        String photoUrl = null;
        CfCrmUserExtDO cfCrmUserExtDO = userExtService.getCfCrmUserExtDOByUniqueCode(crowdfundingVolunteer.getUniqueCode());
        if (cfCrmUserExtDO != null && StringUtils.isNotBlank(cfCrmUserExtDO.getHeadUrl())) {
            photoUrl = cfCrmUserExtDO.getHeadUrl();
        }
        SaasEmployeeDetailDto saasEmployeeDetailDto = SaasEmployeeDetailDto
                .builder()
                .saasSystemId(volunteerCropTypeEnum.getAppId())
                .userId(userId)
                .name(crowdfundingVolunteer.getVolunteerName())
                .mail(shuidiCipher.decrypt(crowdfundingVolunteer.getEncryptEmail()))
                .mobile(crowdfundingVolunteer.getMobile())
                .entryDate(new DateTime(crowdfundingVolunteer.getEntryTime()).toString(GrowthtoolUtil.ymdfmt))
                .hrStatus("A")
                .jobName(jobName)
                .departmentId(departmentId)
                .expandData(expandData)
                .photoUrl(photoUrl)
                .build();
        QyWechatSignUtil.createOrUpdateUser(saasEmployeeDetailDto, volunteerCropTypeEnum.getAppId());

        //查找下是否存在映射关系,如果不存在需要保存
        BdCropUserMappingDO bdCropUserMappingDO = bdCropUserMappingService.queryByUniqueCode(crowdfundingVolunteer.getUniqueCode(), volunteerCropTypeEnum.getType());

        if (bdCropUserMappingDO == null) {
            bdCropUserMappingDO = new BdCropUserMappingDO();
            bdCropUserMappingDO.setUniqueCode(crowdfundingVolunteer.getUniqueCode());
            bdCropUserMappingDO.setUserId(crowdfundingVolunteer.getUniqueCode());
            bdCropUserMappingDO.setCropId(volunteerCropTypeEnum.getType());
            bdCropUserMappingService.insert(bdCropUserMappingDO);
        }
    }

    public static String createExpandData(String uniqueCode) {
        //个人主页
        String url = "https://www.shuidichou.com/cf/consultant?uniqueCode=" + uniqueCode;
        Map<String, String> map = Maps.newHashMap();
        map.put("title", "我的筹款服务记录~");
        map.put("url", url);
        Map<String, Object> externalAttr = Maps.newHashMap();
        externalAttr.put("name", "个人主页");
        externalAttr.put("type", 1);
        externalAttr.put("web", map);

        //筹款流程
        Map<String, String> map_1 = Maps.newHashMap();
        map_1.put("title", "筹款流程指南");
        map_1.put("url", "https://www.shuidichou.com/luban/wndzkwd6tnhd/1");
        Map<String, Object> externalAttr_1 = Maps.newHashMap();
        externalAttr_1.put("name", "筹款流程");
        externalAttr_1.put("type", 1);
        externalAttr_1.put("web", map_1);

        //问题咨询
        Map<String, String> map_2 = Maps.newHashMap();
        map_2.put("title", "常见问题答疑");
        map_2.put("url", "https://www.shuidihuzhu.com/cs/helpCenter?channel=bd_questionCard");
        Map<String, Object> externalAttr_2 = Maps.newHashMap();
        externalAttr_2.put("name", "问题咨询");
        externalAttr_2.put("type", 1);
        externalAttr_2.put("web", map_2);

        Map<String, Object> externalAttrListMap = Maps.newHashMap();
        externalAttrListMap.put("externalAttr", Lists.newArrayList(externalAttr, externalAttr_1, externalAttr_2));
        Map<String, Object> externalProfile = Maps.newHashMap();
        externalProfile.put("externalProfile", externalAttrListMap);
        return JSON.toJSONString(externalProfile);
    }


    /**
     * 同步所有人、组织
     */
    public void syncAllMember() {
        //非线上不同步数据
        if (!applicationService.isProduction()) {
            return;
        }
        //筹款支持组织
        List<Long> initOrgIds = shuidi_chou_support.getInitOrgIds();
        List<BdCrmOrganizationDO> orgInfoList = crmSelfBuiltOrgReadService.getOrgInfoList(initOrgIds);
        //先判断是否有多余的组织,如果有需要先删除处理
        syncByOrg(orgInfoList);
        deleteDepartmentInQyWechat(orgInfoList);
    }


    public void deleteDepartmentInQyWechat(List<BdCrmOrganizationDO> orgInfoList) {
        CrowdfundingVolunteerEnum.VolunteerCropTypeEnum volunteerCropTypeEnum = shuidi_chou_support;
        List<String> allOrgIds = Lists.newArrayList();
        for (BdCrmOrganizationDO bdCrmOrganizationDO : orgInfoList) {
            allOrgIds.addAll(crmSelfBuiltOrgReadService.listAllSubOrgIncludeSelf(bdCrmOrganizationDO.getId())
                    .stream()
                    .map(item -> String.valueOf(item.getId()))
                    .collect(Collectors.toList()));
        }
        //查找到下面所有的组织
        //为了防止先删除父级部分,需要先做个排序,id比较大的是子部门
        List<BdCropOrgMappingDO> mappingDOList = bdCropOrgMappingService.listCropId(volunteerCropTypeEnum.getType())
                .stream()
                .sorted(Comparator.comparing(BdCropOrgMappingDO::getDepartmentId).reversed())
                .collect(Collectors.toList());

        ImmutableSet<String> needDeleteDepartmentIds = Sets.difference(mappingDOList.stream().map(BdCropOrgMappingDO::getOrgId).collect(Collectors.toSet()),
                Sets.newHashSet(allOrgIds)).immutableCopy();
        List<BdCropOrgMappingDO> needDeleteOrgList = mappingDOList.stream().filter(item -> needDeleteDepartmentIds.contains(item.getOrgId()))
                .collect(Collectors.toList());
        for (BdCropOrgMappingDO bdCropOrgMappingDO : needDeleteOrgList) {
            //删除部门-企业微信
            QyWechatSignUtil.deleteDepartment(SaasDepartmentDto.builder()
                    .saasSystemId(volunteerCropTypeEnum.getAppId())
                    .departmentId(bdCropOrgMappingDO.getDepartmentId())
                    .departmentStatus("I")
                    .build(), volunteerCropTypeEnum.getAppId());
        }
        List<Long> ids = needDeleteOrgList.stream()
                .map(BdCropOrgMappingDO::getId)
                .collect(Collectors.toList());
        bdCropOrgMappingService.deleteRelation(ids);
    }


    //从上往下同步组织
    private void syncByOrg(List<BdCrmOrganizationDO> bdOrgs) {
        CrowdfundingVolunteerEnum.VolunteerCropTypeEnum volunteerCropTypeEnum = shuidi_chou_support;
        for (BdCrmOrganizationDO bdOrg : bdOrgs) {
            String departmentId = convertDepartmentId(String.valueOf(bdOrg.getId()), volunteerCropTypeEnum);
            //后续如果是 "筹款服务" 主体,不要新建和更新对应的departmentId
            QyWechatSignUtil.createOrUpdateDepartment(SaasDepartmentDto.builder()
                    .saasSystemId(volunteerCropTypeEnum.getAppId())
                    .departmentId(departmentId)
                    .departmentName(bdOrg.getOrgName())
                    .departmentStatus("A")
                    .parentId(getParentId(String.valueOf(bdOrg.getParentId()), volunteerCropTypeEnum))
                    .build(), volunteerCropTypeEnum.getAppId());

            BdCropOrgMappingDO bdCropOrgMappingDO = bdCropOrgMappingService.queryByOrgId(String.valueOf(bdOrg.getId()), volunteerCropTypeEnum.getType());
            if (bdCropOrgMappingDO == null) {
                bdCropOrgMappingDO = new BdCropOrgMappingDO();
                bdCropOrgMappingDO.setOrgId(String.valueOf(bdOrg.getId()));
                bdCropOrgMappingDO.setDepartmentId(departmentId);
                bdCropOrgMappingDO.setCropId(volunteerCropTypeEnum.getType());
                bdCropOrgMappingService.insert(bdCropOrgMappingDO);
            }
            List<BdCrmOrgUserRelationDO> relationDOList = organizationRelationService.listRelationByOrgId(bdOrg.getId());
            List<String> uniqueCodeList = relationDOList.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).collect(Collectors.toList());
            List<CrowdfundingVolunteer> volunteers = cfVolunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodeList);
            volunteers.forEach(item -> item.setMobile(shuidiCipher.decrypt(item.getMobile())));
            for (CrowdfundingVolunteer volunteer : volunteers) {
                String cropId = volunteer.getCropId();
                if (StringUtils.isBlank(cropId)) {
                    continue;
                }
                List<CrowdfundingVolunteerEnum.VolunteerCropTypeEnum> cropTypeEnumList = Splitter.on(",").splitToList(cropId)
                        .stream()
                        .map(Integer::parseInt)
                        .map(item -> CrowdfundingVolunteerEnum.cropTypeEnumMap.get(item))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (cropTypeEnumList.contains(volunteerCropTypeEnum)) {
                    syncMember(volunteer, departmentId);
                }
            }
            List<BdCrmOrganizationDO> subOrgList = crmSelfBuiltOrgReadService.findDirectSubOrgByOrgId(bdOrg.getId());
            if (CollectionUtils.isNotEmpty(subOrgList)) {
                syncByOrg(subOrgList);
            }
        }
    }


    public String getParentId(String orgId, CrowdfundingVolunteerEnum.VolunteerCropTypeEnum volunteerCropTypeEnum) {
        //先查找是否有数据
        BdCropOrgMappingDO bdCropOrgMappingDO = bdCropOrgMappingService.queryByOrgId(orgId, volunteerCropTypeEnum.getType());
        if (bdCropOrgMappingDO != null) {
            return bdCropOrgMappingDO.getDepartmentId();
        }
        return String.valueOf(volunteerCropTypeEnum.getInitDepartmentId());
    }


    public String convertDepartmentId(String orgId, CrowdfundingVolunteerEnum.VolunteerCropTypeEnum volunteerCropTypeEnum) {
        int beginQwDepartmentId = apolloService.getBeginQwDepartmentId();
        //先查找是否有数据
        BdCropOrgMappingDO bdCropOrgMappingDO = bdCropOrgMappingService.queryByOrgId(orgId, volunteerCropTypeEnum.getType());
        if (bdCropOrgMappingDO != null) {
            return bdCropOrgMappingDO.getDepartmentId();
        }
        String departmentIdStr = bdCropOrgMappingService.maxDepartmentId(volunteerCropTypeEnum.getType());
        if (StringUtils.isBlank(departmentIdStr)) {
            return String.valueOf(beginQwDepartmentId);
        }
        //必须是数字
        int departmentId = Integer.parseInt(departmentIdStr);
        //如果没有按照转化规则设置
        return String.valueOf(1 + departmentId);
    }


    public String convertUserId(CrowdfundingVolunteer volunteer, CrowdfundingVolunteerEnum.VolunteerCropTypeEnum volunteerCropTypeEnum) {
        //先查找是否有数据
        BdCropUserMappingDO bdCropUserMappingDO = bdCropUserMappingService.queryByUniqueCode(volunteer.getUniqueCode(), volunteerCropTypeEnum.getType());
        if (bdCropUserMappingDO != null) {
            return bdCropUserMappingDO.getUserId();
        }
        return volunteer.getUniqueCode();
    }


}
