package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.calresult.impl;

import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.calresult.EncourageActivityCaseDetailDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity.calresult.EncourageActivityCaseDetailService;
import com.shuidihuzhu.cf.dao.starrocks.EncourageActivityCaseDetailDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 计算结果-案例明细(EncourageActivityCaseDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-23 15:32:57
 */
@Service("encourageActivityCaseDetailService")
public class EncourageActivityCaseDetailServiceImpl implements EncourageActivityCaseDetailService {
   
    @Resource
    private EncourageActivityCaseDetailDao encourageActivityCaseDetailDao;

    @Override
    public List<EncourageActivityCaseDetailDO> listByActivityIdAndUniqueCodeAndRuleIds(long activityId, String uniqueCode, List<Long> ruleIds) {
        return encourageActivityCaseDetailDao.listByActivityIdAndUniqueCode(activityId, uniqueCode)
                .stream()
                .filter(item -> ruleIds.contains(item.getRuleId()))
                .sorted(Ordering.natural().onResultOf(EncourageActivityCaseDetailDO::getCaseRk))
                .collect(Collectors.toList());
    }

    @Override
    public List<EncourageActivityCaseDetailDO> listByActivityIdAndRuleId(long activityId, long ruleId) {
        return encourageActivityCaseDetailDao.listByActivityIdAndRuleId(activityId, ruleId);
    }
}
