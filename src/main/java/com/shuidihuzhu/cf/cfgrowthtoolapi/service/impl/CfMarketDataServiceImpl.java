package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseBaseDataModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OrgDataStatVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDataStatParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCompetitioMarketDataService;
import com.shuidihuzhu.cf.repository.CfCompetitionBaseDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-12-07
 */

@Slf4j
@Service
public class CfMarketDataServiceImpl implements ICfCompetitioMarketDataService {

    @Autowired
    private CfCompetitionBaseDataRepository cfCompetitionBaseDataRepository;

    @Override
    public List<OrgDataStatVO> getCityOverviewData(BdCrmDataStatParam crmDataStatParam, List<Integer> cityIds, boolean allowCityEmpty) {
        List<OrgDataStatVO> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cityIds) || allowCityEmpty){
            cityIds = cityIds.stream().distinct().collect(Collectors.toList());
            result = cfCompetitionBaseDataRepository.getCityOverviewData(crmDataStatParam, cityIds);
        }
        return result;
    }

    @Override
    public List<OrgDataStatVO> listDataGroupByCity(BdCrmDataStatParam crmDataStatParam, List<Integer> cityIds) {
        List<OrgDataStatVO> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cityIds)){
            cityIds = cityIds.stream().distinct().collect(Collectors.toList());
            log.info("cityIds:{}", cityIds);
            result = cfCompetitionBaseDataRepository.listDataGroupByCity(crmDataStatParam,cityIds);
        }
        return result;
    }

    @Override
    public List<CfBdCrmDiagnoseBaseDataModel> listCrmDiagnoseBaseDataByCityAndTheDate(List<String> dayKeys, List<Long> cityIds) {
        List<CfBdCrmDiagnoseBaseDataModel> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cityIds)){
            cityIds = cityIds.stream().distinct().collect(Collectors.toList());
            result = cfCompetitionBaseDataRepository.listCrmDiagnoseBaseDataByCityAndTheDate(dayKeys,cityIds);
        }
        log.info("{} listCrmDiagnoseBaseDataByCityAndTheDate dayKeys:{},cityIds:{},result:{}",this.getClass().getSimpleName(),dayKeys, JSON.toJSONString(cityIds),JSON.toJSONString(result));
        return result;
    }
}
