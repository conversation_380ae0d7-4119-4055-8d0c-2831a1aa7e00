package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.hospital.CfHospitalCollectDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital.CfHospitalCollectService;
import com.shuidihuzhu.cf.dao.hospital.CfHospitalCollectDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2023/6/5 02:49
 */
@Service
public class CfHospitalCollectServiceImpl implements CfHospitalCollectService {

    @Autowired
    private CfHospitalCollectDao cfHospitalCollectDao;

    @Override
    public void insert(CfHospitalCollectDo cfHospitalCollectDo) {
        if (cfHospitalCollectDo == null) {
            return;
        }
        cfHospitalCollectDao.insert(cfHospitalCollectDo);
    }

    @Override
    public void batchInsert(List<CfHospitalCollectDo> cfHospitalCollectDoList) {
        if (CollectionUtils.isEmpty(cfHospitalCollectDoList)) {
            return;
        }
        cfHospitalCollectDao.batchInsert(cfHospitalCollectDoList);
    }

    @Override
    public void update(CfHospitalCollectDo cfHospitalCollectDo) {
        if (cfHospitalCollectDo == null) {
            return;
        }
        cfHospitalCollectDao.update(cfHospitalCollectDo);
    }

    @Override
    public void batchUpdate(List<CfHospitalCollectDo> cfHospitalCollectDo) {
        if (CollectionUtils.isEmpty(cfHospitalCollectDo)) {
            return;
        }
        cfHospitalCollectDao.batchUpdate(cfHospitalCollectDo);
    }

    @Override
    public int cancelCollect(int collectType, String vhospitalCode, int departmentId) {
        return cfHospitalCollectDao.cancelCollect(collectType, vhospitalCode, departmentId);
    }

    @Override
    public int batchCancelCommonlyUsed(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        return cfHospitalCollectDao.batchCancelCommonlyUsed(idList);
    }

    @Override
    public int cancelCommonlyUsed(int id) {
        if (id <= 0) {
            return 0;
        }
        return cfHospitalCollectDao.cancelCommonlyUsed(id);
    }

    @Override
    public int selectHospitalCountByUniqueCode(String uniqueCode, Integer collectType) {
        if (StringUtils.isEmpty(uniqueCode)) {
            return 0;
        }
        return Optional.ofNullable(cfHospitalCollectDao.selectHospitalCountByUniqueCode(uniqueCode, collectType)).orElse(0);
    }

    @Override
    public int selectDepartmentCountByUniqueCode(String uniqueCode, String vHospitalCode, Integer collectType) {
        if (StringUtils.isEmpty(uniqueCode) || StringUtils.isEmpty(vHospitalCode)) {
            return 0;
        }
        return Optional.ofNullable(cfHospitalCollectDao.selectDepartmentCountByUniqueCode(uniqueCode, vHospitalCode, collectType)).orElse(0);
    }

    @Override
    public List<CfHospitalCollectDo> getCollectedDepartmentList(String vhospitalCode, Integer collectType, String uniqueCode) {
        if (StringUtils.isEmpty(uniqueCode) || StringUtils.isEmpty(vhospitalCode)) {
            return Lists.newArrayList();
        }
        return cfHospitalCollectDao.getCollectedDepartmentList(vhospitalCode, collectType, uniqueCode);
    }

    @Override
    public List<CfHospitalCollectDo> getCollectedDepartmentListV2(String vhospitalCode, List<Integer> collectTypeList, String uniqueCode) {
        if (StringUtils.isEmpty(uniqueCode) || StringUtils.isEmpty(vhospitalCode)) {
            return Lists.newArrayList();
        }
        return cfHospitalCollectDao.getCollectedDepartmentListV2(vhospitalCode, collectTypeList, uniqueCode);
    }

    @Override
    public List<CfHospitalCollectDo> getCollectedHospitalList(Integer collectType, String uniqueCode) {
        if (StringUtils.isEmpty(uniqueCode)) {
            return Lists.newArrayList();
        }
        return cfHospitalCollectDao.getCollectedHospitalList(collectType, uniqueCode);
    }

    @Override
    public List<CfHospitalCollectDo> getCollectedHospitalListV2(List<Integer> collectTypeList, String uniqueCode) {
        if (StringUtils.isEmpty(uniqueCode)) {
            return Lists.newArrayList();
        }
        return cfHospitalCollectDao.getCollectedHospitalListV2(collectTypeList, uniqueCode);
    }
}
