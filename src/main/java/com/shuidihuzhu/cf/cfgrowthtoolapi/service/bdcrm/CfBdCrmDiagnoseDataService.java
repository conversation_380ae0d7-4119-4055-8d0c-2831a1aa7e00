package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmDiagnoseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseForCalcModel;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/4/15 下午5:27
 */
public interface CfBdCrmDiagnoseDataService{


    int insertSelective(CfBdCrmDiagnoseDataDO record);

    void batchInsertOrUpdate(List<CfBdCrmDiagnoseDataDO> list);

    int updateByPrimaryKeySelective(CfBdCrmDiagnoseDataDO record);

    List<CfBdCrmDiagnoseDataDO> listByDateTime(String curDateTime,
                                               Integer curDayRange,
                                               String preDateTime,
                                               Integer preDayRange);
}
