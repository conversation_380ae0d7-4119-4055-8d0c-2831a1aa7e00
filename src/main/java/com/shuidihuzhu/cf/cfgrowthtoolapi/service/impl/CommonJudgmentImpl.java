package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ICommonJudgment;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.channel.IVolunteerInviteRecordService;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.api.client.CfCrowdfundingBaseInfoBackupFeignClient;
import com.shuidihuzhu.client.cf.api.client.CfStatBaseInfoPointFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackApiClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import com.shuidihuzhu.wx.grpc.client.WxSubscribeModel;
import com.shuidihuzhu.wx.grpc.client.feign.WxSubscribeEventServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-09-01
 */
@Slf4j
@Service
@RefreshScope
public class CommonJudgmentImpl implements ICommonJudgment {

    @Resource
    private IVolunteerInviteRecordService volunteerInviteRecordServiceImpl;
    @Autowired
    private CfStatBaseInfoPointFeignClient cfStatBaseInfoPointFeignClient;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private CfClewtrackApiClient cfClewtrackApiClient;
    @Autowired
    private CfCrowdfundingBaseInfoBackupFeignClient cfCrowdfundingBaseInfoBackupFeignClient;

    @Autowired
    private WxSubscribeEventServiceClient wxSubscribeEventServiceClient;

    @Autowired
    private ApolloService apolloService;

    /**
     * 是否关注指定公众号
     * true 表示关注 false表示未关注
     * @param userId
     * @return
     */
    @Override
    public boolean checkSubscribeByUserIdAndThridType(long userId, int userThirdType) {
        try {
            boolean response = wxSubscribeEventServiceClient.checkSubscribeByUserId(userId, userThirdType);
            log.info("wxSubscribeEventServiceClient.getSubscribeByUserIds request:{},{},response:{}", userId, userThirdType, response);
            return response;
        }catch (Exception e){
            log.error("wxSubscribeEventServiceClient.getSubscribeByUserIds request:{},{},Exception", userId, userThirdType, e);
        }
        //调用失败，认为是未关注
        return false;
    }

    /**
     * 是否扫过线下筹款顾问二维码
     * @param userId
     * @return
     */
    @Override
    public boolean checkIsExceptSubscribe(long userId) {
        String unqiueCode = volunteerInviteRecordServiceImpl.getlatelyBdVolunteerUniqueCodeByUserId(userId);
        if (StringUtils.isNotEmpty(unqiueCode)){
            log.info("Shield_unqiueCode:{}",unqiueCode);
            return true;
        }
        return false;
    }

    /**
     * 判断是否有在筹案例
     */
    @Override
    public boolean checkIsHaveNoEndCase(long userId){
        FeignResponse<CrowdfundingInfo> feignResponse = crowdfundingFeignClient.getLastByUserId(userId);
        if (feignResponse == null || feignResponse.getCode() !=0 ){
            return true;
        }
        CrowdfundingInfo crowdfundingInfo = feignResponse.getData();
        if (crowdfundingInfo == null){
            //没有案例
            return false;
        }else{
            if (crowdfundingInfo.getEndTime().after(DateUtil.getCurrentTimestamp())){
                //案例结束时间在当前时间之后，说明案例未结束,有在筹案例
                return true;
            }else{
                return false;
            }
        }
    }

    /**
     * 是否填写过任何一项草稿信息
     * true 表示有填写过内容 false表示没有填写过内容
     * @param userId
     * @param startTime 时间格式:yyyy-MM-dd HH:mm:ss
     * @param endTime   时间格式:yyyy-MM-dd HH:mm:ss
     * @return
     */
    @Override
    public boolean checkIsFilled(long userId,String startTime,String endTime){
        Response<Boolean> isUnfilledResponse = cfStatBaseInfoPointFeignClient.checkIfUnfilled(userId,startTime,endTime);
        if (isUnfilledResponse.ok()){
            //有填写过内容
            return isUnfilledResponse.getData();
        }
        //调取feign失败，返回true
        return true;
    }

    /**
     * 检测24小时内是否有过登记
     * true 表示有登记 false表是没有登记
     * @param userId
     * @return
     */
    @Override
    public boolean checkIsWithin24HoursRepetition(long userId) {
        Response<CfClewBaseInfoDO> cfClewBaseInfoDOResponse = cfClewtrackApiClient.getLatestClewbaseByUserId(userId);
        if (cfClewBaseInfoDOResponse.ok()){
            CfClewBaseInfoDO cfClewBaseInfoDO = cfClewBaseInfoDOResponse.getData();
            if (cfClewBaseInfoDO == null){
                return false;
            }
            //判断登记是否在24小时内
            if (isInner24Hours(cfClewBaseInfoDO)){
                //在24小时内有登记
                return true;
            }else{
                return false;
            }
        }
        //fengin调用失败，认为有登记
        return true;
    }

    @Override
    public boolean checkIsHaveDraft(long userId) {
        Response<Boolean> response = cfCrowdfundingBaseInfoBackupFeignClient.checkIsHaveDraftByUserId(userId,0);
        if (response.ok() && response.getData() != null){
            return response.getData();
        }
        //feign调用失败，则认为有前置外呼
        return true;
    }

    private boolean isInner24Hours(CfClewBaseInfoDO cfClewBaseInfoDO) {
        Date createTime = cfClewBaseInfoDO.getCreateTime();
        Date standardTime = DateUtil.addHours(DateUtil.getCurrentTimestamp(),-24);
        if (createTime.after(standardTime)){
            return true;
        }
        return false;
    }
}
