package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.BdCooperationCaseInfoDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.AppealLabelSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.BdCooperationCaseInfoService;
import com.shuidihuzhu.cf.dao.bdcrm.BdCooperationCaseInfoDao;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class BdCooperationCaseInfoServiceImpl implements BdCooperationCaseInfoService {

    @Resource
    private BdCooperationCaseInfoDao bdCooperationCaseInfoDao;


    @Override
    public int insert(BdCooperationCaseInfoDO bdCooperationCaseInfoDO) {
        if (bdCooperationCaseInfoDO == null) {
            return 0;
        }
        return bdCooperationCaseInfoDao.insert(bdCooperationCaseInfoDO);
    }

    @Override
    public BdCooperationCaseInfoDO selectByCaseId(long caseId) {
        return bdCooperationCaseInfoDao.selectByCaseId(caseId);
    }

    @Override
    public void dealCooperationCaseInfo(CfBdCaseInfoDo bdCaseInfoDo) {
        if (bdCaseInfoDo == null) {
            return;
        }
        BdCooperationCaseInfoDO bdCooperationCaseInfoDO = this.selectByCaseId(bdCaseInfoDo.getCaseId());
        if (bdCooperationCaseInfoDO != null) {
            return;
        }
        bdCooperationCaseInfoDO = new BdCooperationCaseInfoDO();
        bdCooperationCaseInfoDO.setCaseId(Optional.ofNullable(bdCaseInfoDo.getCaseId()).orElse(0));
        bdCooperationCaseInfoDO.setPatientName(Optional.ofNullable(bdCaseInfoDo.getPatientName()).orElse(""));
        bdCooperationCaseInfoDO.setRaiserPhone(Optional.ofNullable(bdCaseInfoDo.getRaiserPhone()).orElse(""));
        bdCooperationCaseInfoDO.setVolunteerUniqueCode(Optional.ofNullable(bdCaseInfoDo.getUniqueCode()).orElse(""));
        bdCooperationCaseInfoDO.setDateCreated(bdCaseInfoDo.getDateCreated());
        this.insert(bdCooperationCaseInfoDO);
    }

    @Override
    public void updateLocalCityTag(int caseId, int localCity) {
        bdCooperationCaseInfoDao.updateLocalCityTag(caseId, localCity);
    }

    @Override
    public void updateAdminApproveStatus(int caseId, int approveStatus) {
        bdCooperationCaseInfoDao.updateAdminApproveStatus(caseId, approveStatus);
    }

    @Override
    public void updateApproveTime(int caseId, Date approveTime) {
        bdCooperationCaseInfoDao.updateApproveTime(caseId, approveTime);
    }

    @Override
    public List<BdCooperationCaseInfoDO> getCaseLabelBySearchParam(AppealLabelSearchParam params) {
        if (params == null) {
            return Lists.newArrayList();
        }
        return bdCooperationCaseInfoDao.getCaseLabelBySearchParam(params);
    }

    @Override
    public long getCaseLabelCount(AppealLabelSearchParam params) {
        if (params == null) {
            return 0;
        }
        return bdCooperationCaseInfoDao.getCaseLabelCount(params);
    }
}
