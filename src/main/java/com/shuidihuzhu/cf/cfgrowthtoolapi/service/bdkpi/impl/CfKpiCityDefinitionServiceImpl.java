package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCityDefinitionDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfBdKpiEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiCityDefinitionService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiCityDefinitionDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-08-02
 */

@Service
@Slf4j
public class CfKpiCityDefinitionServiceImpl implements ICfKpiCityDefinitionService {

    @Autowired
    private CfKpiCityDefinitionDao cfKpiCityDefinitionDao;

    @Override
    public List<CfKpiCityDefinitionDO> listAll() {
        return cfKpiCityDefinitionDao.listAll();
    }

    @Override
    public List<String> getCityname(Integer cityType) {
        if (CfBdKpiEnums.CityTypeEnum.DAN_GE.getType().equals(cityType)){
            return Lists.newArrayList();
        }
        return cfKpiCityDefinitionDao.getCityname(cityType).stream().map(CfKpiCityDefinitionDO::getCityName).collect(Collectors.toList());
    }
    @Override
    public int insertCfKpiCityDefinitionDO(CfKpiCityDefinitionDO cfKpiCityDefinitionDO){
        CfKpiCityDefinitionDO inDB= cfKpiCityDefinitionDao.getByCityName(cfKpiCityDefinitionDO.getCityName());
        if (inDB!=null){
            return 0;
        }
        return cfKpiCityDefinitionDao.insert(cfKpiCityDefinitionDO);
    }
    @Override
    public int updateCfKpiCityDefinitionDO(CfKpiCityDefinitionDO cfKpiCityDefinitionDO){
        return cfKpiCityDefinitionDao.update(cfKpiCityDefinitionDO);
    }
}
