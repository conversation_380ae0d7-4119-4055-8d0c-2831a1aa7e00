package com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DateQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.CampHospitalSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.campaignv2.FollowHospitalSearchParam;
import com.shuidihuzhu.cf.dao.campaign.HospitalDateSummaryDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.campaignv2.HospitalDateSummaryDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.campaign.HospitalDateSummaryService;
import com.vladsch.flexmark.util.BiFunction;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 医院维度汇总表(HospitalDateSummary)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-08-27 15:09:09
 */
@Service
public class HospitalDateSummaryServiceImpl implements HospitalDateSummaryService {

    @Resource
    private HospitalDateSummaryDao hospitalDateSummaryDao;

    @Override
    public HospitalDateSummaryDO queryById(long id) {
        return hospitalDateSummaryDao.queryById(id);
    }

    @Override
    public List<HospitalDateSummaryDO> listByOrgIds(List<Integer> orgIds, List<String> hospitalCodes, DateQueryParam dateQueryParam) {
        if (StringUtils.isBlank(dateQueryParam.getStartTime()) || StringUtils.isBlank(dateQueryParam.getEndTime())) {
            return Lists.newArrayList();
        }
        return hospitalDateSummaryDao.listByOrgIds(orgIds, hospitalCodes, dateQueryParam);
    }

    //查找昨天的数据
    @Override
    public int countSearchHospital(FollowHospitalSearchParam searchParam, List<Integer> orgIds) {
        return Optional.ofNullable(hospitalDateSummaryDao.countSearchHospital(searchParam, orgIds)).orElse(0);
    }

    @Override
    public List<HospitalDateSummaryDO> searchHospital(FollowHospitalSearchParam searchParam, List<Integer> orgIds) {
        List<HospitalDateSummaryDO> result = Lists.newArrayList();
        hospitalDateSummaryDao.searchHospital(searchParam, orgIds)
                .stream()
                .collect(Collectors.groupingBy(HospitalDateSummaryDO::getVhospitalCode))
                .forEach((key, value) -> {
                    HospitalDateSummaryDO firstSummary = value.get(0);
                    HospitalDateSummaryDO hospitalDateSummaryDO = new HospitalDateSummaryDO();
                    hospitalDateSummaryDO.setVhospitalCode(firstSummary.getVhospitalCode());
                    hospitalDateSummaryDO.setHospitalName(firstSummary.getHospitalName());
                    hospitalDateSummaryDO.setVvhospitalCode(firstSummary.getVvhospitalCode());
                    hospitalDateSummaryDO.setVvhospitalName(firstSummary.getVvhospitalName());
                    hospitalDateSummaryDO.setCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getCaseCnt).sum());
                    hospitalDateSummaryDO.setValidCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getValidCaseCnt).sum());
                    hospitalDateSummaryDO.setDonateCnt(value.stream().mapToInt(HospitalDateSummaryDO::getDonateCnt).sum());
                    hospitalDateSummaryDO.setDonateAmount(value.stream().mapToLong(HospitalDateSummaryDO::getDonateAmount).sum());
                    hospitalDateSummaryDO.setSevenDayDonateCnt(value.stream().mapToInt(HospitalDateSummaryDO::getSevenDayDonateCnt).sum());
                    hospitalDateSummaryDO.setLastSevDayDonateCnt(value.stream().mapToInt(HospitalDateSummaryDO::getLastSevDayDonateCnt).sum());
                    hospitalDateSummaryDO.setSevenDayCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getSevenDayCaseCnt).sum());
                    hospitalDateSummaryDO.setLastSevDayCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getLastSevDayCaseCnt).sum());
                    result.add(hospitalDateSummaryDO);
                });
        return result;
    }

    @Override
    public List<HospitalDateSummaryDO> hospitalHomeView(CampHospitalSearchParam hospitalSearchParam) {
        if (CollectionUtils.isEmpty(hospitalSearchParam.getDays()) || CollectionUtils.isEmpty(hospitalSearchParam.getVhospitalCodeList())) {
            return Lists.newArrayList();
        }
        List<HospitalDateSummaryDO> result = Lists.newArrayList();
        Map<String, List<HospitalDateSummaryDO>> vhospitalGroup = hospitalDateSummaryDao.campaignHospitalView(hospitalSearchParam)
                .stream()
                .collect(Collectors.groupingBy(HospitalDateSummaryDO::getVhospitalCode));
        for (Map.Entry<String, List<HospitalDateSummaryDO>> entry : vhospitalGroup.entrySet()) {
            Map<String, List<HospitalDateSummaryDO>> collect = entry.getValue()
                    .stream()
                    .collect(Collectors.groupingBy(HospitalDateSummaryDO::getDateKey));
            int size = collect.size();
            int caseCntCount = entry.getValue().stream().mapToInt(HospitalDateSummaryDO::getCaseCnt).sum();
            int validCaseCount = entry.getValue().stream().mapToInt(HospitalDateSummaryDO::getValidCaseCnt).sum();
            int donateCount = entry.getValue().stream().mapToInt(HospitalDateSummaryDO::getDonateCnt).sum();
            long donateAmount = entry.getValue().stream().mapToLong(HospitalDateSummaryDO::getDonateAmount).sum();
            HospitalDateSummaryDO modelSummary = entry.getValue().get(0);
            HospitalDateSummaryDO summaryDO = new HospitalDateSummaryDO();
            summaryDO.setVhospitalCode(modelSummary.getVhospitalCode());
            summaryDO.setHospitalName(modelSummary.getHospitalName());
            summaryDO.setVvhospitalCode(modelSummary.getVvhospitalCode());
            summaryDO.setVvhospitalName(modelSummary.getVvhospitalName());
            summaryDO.setCityId(modelSummary.getCityId());
            summaryDO.setCityName(modelSummary.getCityName());
            summaryDO.setCaseCnt(caseCntCount / size);
            summaryDO.setValidCaseCnt(validCaseCount / size);
            summaryDO.setDonateCnt(donateCount / size);
            summaryDO.setDonateAmount(donateAmount / size);
            result.add(summaryDO);
        }
        return result;
    }


    @Override
    public List<HospitalDateSummaryDO> campaignHospitalView(CampHospitalSearchParam hospitalSearchParam) {
        if (CollectionUtils.isEmpty(hospitalSearchParam.getDays()) || CollectionUtils.isEmpty(hospitalSearchParam.getVhospitalCodeList())) {
            return Lists.newArrayList();
        }
        List<HospitalDateSummaryDO> result = Lists.newArrayList();
        hospitalDateSummaryDao.campaignHospitalView(hospitalSearchParam)
                .stream()
                .collect(Collectors.groupingBy(HospitalDateSummaryDO::getVhospitalCode))
                .forEach((key, value) -> {
                    HospitalDateSummaryDO firstSummary = value.get(0);
                    HospitalDateSummaryDO hospitalDateSummaryDO = new HospitalDateSummaryDO();
                    hospitalDateSummaryDO.setVhospitalCode(firstSummary.getVhospitalCode());
                    hospitalDateSummaryDO.setHospitalName(firstSummary.getHospitalName());
                    hospitalDateSummaryDO.setVvhospitalCode(firstSummary.getVvhospitalCode());
                    hospitalDateSummaryDO.setVvhospitalName(firstSummary.getVvhospitalName());
                    hospitalDateSummaryDO.setCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getCaseCnt).sum());
                    hospitalDateSummaryDO.setValidCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getValidCaseCnt).sum());
                    hospitalDateSummaryDO.setDonateCnt(value.stream().mapToInt(HospitalDateSummaryDO::getDonateCnt).sum());
                    hospitalDateSummaryDO.setDonateAmount(value.stream().mapToLong(HospitalDateSummaryDO::getDonateAmount).sum());
                    hospitalDateSummaryDO.setSevenDayDonateCnt(value.stream().mapToInt(HospitalDateSummaryDO::getSevenDayDonateCnt).sum());
                    hospitalDateSummaryDO.setLastSevDayDonateCnt(value.stream().mapToInt(HospitalDateSummaryDO::getLastSevDayDonateCnt).sum());
                    hospitalDateSummaryDO.setSevenDayCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getSevenDayCaseCnt).sum());
                    hospitalDateSummaryDO.setLastSevDayCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getLastSevDayCaseCnt).sum());
                    result.add(hospitalDateSummaryDO);
                });
        return result;
    }

    @Override
    public List<HospitalDateSummaryDO> highDataCampaignHospitalView(CampHospitalSearchParam hospitalSearchParam) {
        return sameTimeCompareCampaignHospitalView(hospitalSearchParam, Math::max);
    }


    //同比
    private List<HospitalDateSummaryDO> sameTimeCompareCampaignHospitalView(CampHospitalSearchParam hospitalSearchParam, BiFunction<Integer, Integer, Integer> biFunction) {
        Map<String, List<HospitalDateSummaryDO>> dateKeyGroup = hospitalDateSummaryDao.campaignHospitalView(hospitalSearchParam)
                .stream()
                .collect(Collectors.groupingBy(HospitalDateSummaryDO::getDateKey));
        Map<String, HospitalDateSummaryDO> resultMap = Maps.newHashMap();
        for (Map.Entry<String, List<HospitalDateSummaryDO>> entry : dateKeyGroup.entrySet()) {
            entry.getValue()
                    .stream()
                    .collect(Collectors.groupingBy(HospitalDateSummaryDO::getVhospitalCode))
                    .forEach((key, value) -> {
                        HospitalDateSummaryDO firstSummary = value.get(0);
                        HospitalDateSummaryDO hospitalDateSummaryDO = new HospitalDateSummaryDO();
                        hospitalDateSummaryDO.setVhospitalCode(firstSummary.getVhospitalCode());
                        hospitalDateSummaryDO.setHospitalName(firstSummary.getHospitalName());
                        hospitalDateSummaryDO.setVvhospitalCode(firstSummary.getVvhospitalCode());
                        hospitalDateSummaryDO.setVvhospitalName(firstSummary.getVvhospitalName());
                        hospitalDateSummaryDO.setCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getCaseCnt).sum());
                        hospitalDateSummaryDO.setValidCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getValidCaseCnt).sum());
                        hospitalDateSummaryDO.setDonateCnt(value.stream().mapToInt(HospitalDateSummaryDO::getDonateCnt).sum());
                        hospitalDateSummaryDO.setDonateAmount(value.stream().mapToLong(HospitalDateSummaryDO::getDonateAmount).sum());
                        hospitalDateSummaryDO.setSevenDayDonateCnt(value.stream().mapToInt(HospitalDateSummaryDO::getSevenDayDonateCnt).sum());
                        hospitalDateSummaryDO.setLastSevDayDonateCnt(value.stream().mapToInt(HospitalDateSummaryDO::getLastSevDayDonateCnt).sum());
                        hospitalDateSummaryDO.setSevenDayCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getSevenDayCaseCnt).sum());
                        hospitalDateSummaryDO.setLastSevDayCaseCnt(value.stream().mapToInt(HospitalDateSummaryDO::getLastSevDayCaseCnt).sum());
                        if (!resultMap.containsKey(key)) {
                            resultMap.put(key, hospitalDateSummaryDO);
                        } else {
                            HospitalDateSummaryDO existDataSummary = resultMap.get(key);
                            existDataSummary.setCaseCnt(biFunction.apply(existDataSummary.getCaseCnt(), hospitalDateSummaryDO.getCaseCnt()));
                            existDataSummary.setValidCaseCnt(biFunction.apply(existDataSummary.getValidCaseCnt(), hospitalDateSummaryDO.getValidCaseCnt()));
                            existDataSummary.setDonateCnt(biFunction.apply(existDataSummary.getDonateCnt(), hospitalDateSummaryDO.getDonateCnt()));
                            existDataSummary.setSevenDayDonateCnt(biFunction.apply(existDataSummary.getSevenDayDonateCnt(), hospitalDateSummaryDO.getSevenDayDonateCnt()));
                            existDataSummary.setLastSevDayDonateCnt(biFunction.apply(existDataSummary.getLastSevDayDonateCnt(), hospitalDateSummaryDO.getLastSevDayDonateCnt()));
                            existDataSummary.setSevenDayCaseCnt(biFunction.apply(existDataSummary.getSevenDayCaseCnt(), hospitalDateSummaryDO.getSevenDayCaseCnt()));
                            existDataSummary.setLastSevDayCaseCnt(biFunction.apply(existDataSummary.getLastSevDayCaseCnt(), hospitalDateSummaryDO.getLastSevDayCaseCnt()));
                        }
                    });
        }
        return CollectionUtils.isEmpty(resultMap.values()) ? Lists.newArrayList() : Lists.newArrayList(resultMap.values());
    }

    @Override
    public List<HospitalDateSummaryDO> avgDataCampaignHospitalView(CampHospitalSearchParam hospitalSearchParam) {
        return sameTimeCompareCampaignHospitalView(hospitalSearchParam, (a, b) -> {
            if (a > 0 && b > 0) {
                return (a + b) / 2;
            } else {
                return a + b;
            }
        });
    }

}