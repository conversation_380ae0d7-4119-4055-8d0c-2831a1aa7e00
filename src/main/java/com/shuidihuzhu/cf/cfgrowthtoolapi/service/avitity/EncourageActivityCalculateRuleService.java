package com.shuidihuzhu.cf.cfgrowthtoolapi.service.avitity;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.activity.EncourageActivityCalculateRuleDO;

import java.util.List;

/**
 * 激励活动规则表(EncourageActivityCalculateRule)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-23 15:32:56
 */
public interface EncourageActivityCalculateRuleService {

    EncourageActivityCalculateRuleDO queryById(long id);

    int insert(EncourageActivityCalculateRuleDO encourageActivityCalculateRule);

    int update(EncourageActivityCalculateRuleDO encourageActivityCalculateRule);

    boolean deleteById(long id);

    List<EncourageActivityCalculateRuleDO> listByActivityIds(List<Long> activityIds);

    boolean deleteByActivityId(long activityId);

    EncourageActivityCalculateRuleDO queryByActivityIdAndRuleId(long activityId, int ruleId);

    List<EncourageActivityCalculateRuleDO> listByActivityId(long activityId);

}
