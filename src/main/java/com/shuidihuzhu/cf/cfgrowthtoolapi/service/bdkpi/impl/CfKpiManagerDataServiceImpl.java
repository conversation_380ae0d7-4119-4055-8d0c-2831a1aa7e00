package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiManagerDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPartnerDataDO;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiManagerDataDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiManagerDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 管理层kpi基础数据(CfKpiManagerData)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-27 10:48:23
 */
@Service("cfKpiManagerDataService")
public class CfKpiManagerDataServiceImpl implements CfKpiManagerDataService {

    @Resource
    private CfKpiManagerDataDao cfKpiManagerDataDao;


    @Override
    public int insert(CfKpiManagerDataDO cfKpiManagerData) {
        if (cfKpiManagerData == null) {
            return 0;
        }
        //查看数据是否存在
        CfKpiManagerDataDO dataDOInDB = cfKpiManagerDataDao.getByUniqueKey(cfKpiManagerData.getDayKey(),
                cfKpiManagerData.getUniqueCode(), cfKpiManagerData.getUniqueKey(), cfKpiManagerData.getDataType());
        if (dataDOInDB != null) {
            //存在直接覆盖
            cfKpiManagerData.setId(dataDOInDB.getId());
            return cfKpiManagerDataDao.update(cfKpiManagerData);
        }
        return cfKpiManagerDataDao.insert(cfKpiManagerData);
    }

    @Override
    public List<CfKpiManagerDataDO> listByDayKeyAndType(String dayKey, int dataType) {
        return cfKpiManagerDataDao.listByDayKeyAndType(dayKey, dataType);
    }

    @Override
    public int update(CfKpiManagerDataDO cfKpiManagerData) {
        return cfKpiManagerDataDao.update(cfKpiManagerData);
    }
}
