package com.shuidihuzhu.cf.cfgrowthtoolapi.service.menu;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRoleCarteConfigModel;
import com.shuidihuzhu.cf.dao.SeaConfigMenuDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2022-12-05 16:22
 **/
@Service
public class RoleCarteServiceImpl implements IRoleCarteService{

    @Autowired
    private SeaConfigMenuDao seaConfigMenuDao;

    @Override
    public CfRoleCarteConfigModel getByCarteUniqueCode(String carteUniqueCode) {
        if (StringUtils.isBlank(carteUniqueCode)) {
            return null;
        }
        return seaConfigMenuDao.getByCarteUniqueCode(carteUniqueCode);
    }
}
