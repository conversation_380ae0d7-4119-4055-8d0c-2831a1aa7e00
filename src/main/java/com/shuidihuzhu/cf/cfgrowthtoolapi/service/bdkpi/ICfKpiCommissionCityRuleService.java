package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCommissionCityRuleDO;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CommonResultModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKPICommissionDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.CfKPICommissionVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.KpiSearchParam;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-08-03 17:06
 */
public interface ICfKpiCommissionCityRuleService {

    List<CfKpiCommissionCityRuleDO> listAllCommissionRule(String monthKey);

    /**
     * @author: wanghui
     * @time: 2020-08-06 16:27
     * @description: getCommissionCityname 获得 某个月份 下该城市类型中的城市名
     * @param: [monthKey, cityType, commissionTypeList]
     * @return: com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCommissionCityRuleDO
     */
    CfKpiCommissionCityRuleDO getCommissionCityname(String monthKey, Integer cityType,List<Integer> commissionTypeList);

    /**
     * @author: wanghui
     * @time: 2020-08-06 16:27
     * @description: getCommission 查询指定 level+monthKey+cityType 的规则  cityNameStr是当cityType为单个城市时使用
     * @param: [cfKPICommission, commissionType]
     * @return: com.shuidihuzhu.cf.response.OpResult<com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiCommissionCityRuleDO>
     */
    CfKpiCommissionCityRuleDO getCommission(CfKPICommissionDTO cfKPICommission, Integer commissionType);

    OpResult<Long> saveOrUpdateCommission(CfKpiCommissionCityRuleDO data, List<Integer> commissionTypeList);

    /**
     * @author: wanghui
     * @time: 2020-08-06 16:28
     * @description: changeStatusOtherMonthKey 修改除 monthKey以后其他月份规则为失效状态
     * @param: [monthKey]
     * @return: void
     */
    void changeStatusOtherMonthKey(String monthKey);

    CfKpiCommissionCityRuleDO getCommission(Long id);

    CommonResultModel<CfKPICommissionVO> getCommissionList(KpiSearchParam searchParam);

    List<Integer> getCityTypeByMonthKeyWithCommissionType(String monthKey, List<Integer> commissionTypeList);
}
