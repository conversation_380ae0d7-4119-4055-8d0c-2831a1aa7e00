package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CrowdfundingVolunteerCreateCaseRecordExtDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdCrmScanRecordModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingVolunteerCreateCaseRecord;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/6/29.
 */
public interface CrowdfundingVolunteerCreateCaseRecordBiz {

    int insertVolunteerCreateCaseRecord(CrowdfundingVolunteerCreateCaseRecord crowdfundingVolunteerCreateCaseRecord, long userId, String encryptPhone);

    int insertVolunteerCreateCaseRecordByUrl(CrowdfundingVolunteerCreateCaseRecord crowdfundingVolunteerCreateCaseRecord, long userId);

    int insertVolunteerCreateCaseRecordWithPhone(long userId, String volunteerUnique, String phone);

    long getVolunteerKeyTimemills();

    /**
     * 资产转移 造成userid变动  及时更新到缓存中,避免volunteer邀请 受影响
     * @param fromUserId
     * @param toUserId
     * @param channel
     */
    void transferCfVolunteer(long fromUserId, long toUserId, String userMobile, String channel);

    /**
     * 根据userId获取最新一条volunteerUnique
     * @param userId
     * @return
     */
    String getUniqueCodeByUserId(Long userId);

	CrowdfundingVolunteerCreateCaseRecordExtDO getByUserIdWithVolunteerUnique(long userId, String volunteerUnique);

	CrowdfundingVolunteerCreateCaseRecordExtDO getByEncryptPhoneWithVolunteerUnique(String encryptPhone, String volunteerUnique);

    int updateNoClewCreateDesc(long id, String noClewCreateDesc);

    int updateClewId(long id, long clewId);

    CrowdfundingVolunteerCreateCaseRecordExtDO getRecordByUserId(long userId);

    long getScanCount(String phone, String uniqueCode, Date startTime, Date endTime);

    void fillPhone(long id, String encryptPhone, int fillPhoneScene);

    void fillPhoneByJob();

    List<BdCrmScanRecordModel> listScanRecord(String phone, String uniqueCode, Date startTime, Date endTime, int pageNo, int pageSize);

    List<CrowdfundingVolunteerCreateCaseRecordExtDO> listSaomaRecordByUserId(long userId, String startTime, String endTime);
}
