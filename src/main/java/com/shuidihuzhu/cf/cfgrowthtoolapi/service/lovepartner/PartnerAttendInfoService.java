package com.shuidihuzhu.cf.cfgrowthtoolapi.service.lovepartner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.AttendPartnerInfoParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.lovepartner.PartnerAttendInfoDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdLovePartnerParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.DateQueryParam;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: zhengqiu
 * @date: 2021-09-01 16:06
 **/
public interface PartnerAttendInfoService {

     String insert(AttendPartnerInfoParam attendPartnerInfoParam);

     String update(AttendPartnerInfoParam attendPartnerInfoParam, String uniqueCode);

     PartnerAttendInfoDTO getInfo(String uniqueCode, String attendDate);

     List<PartnerAttendInfoDTO> listByPartnerUniqueCode(String partnerUniqueCode, DateQueryParam dateQueryParam);

     void sendApproveAttendMsg(PartnerAttendInfoDTO attendInfoDTO, CrowdfundingVolunteer leader);

     PartnerAttendInfoDTO getAttendInfoById(Long id);
     
     int adjustAttendApproveName(long id, CrowdfundingVolunteer approveVolunteer);

     int countLovePartnerAttend(BdLovePartnerParam bdLovePartnerParam, String uniqueCode);

     List<PartnerAttendInfoDTO> listLovePartnerAttend(BdLovePartnerParam bdLovePartnerParam, String uniqueCode);

     /**
      * 根据考核时间获取出勤记录
      * @param startTime
      * @param endTime
      * @param uniqueCodeList
      * @return
      */
     List<PartnerAttendInfoDTO> listAttendInfoWithCycleTimeAndUniqueCode(Date startTime, Date endTime, List<String> uniqueCodeList);
}
