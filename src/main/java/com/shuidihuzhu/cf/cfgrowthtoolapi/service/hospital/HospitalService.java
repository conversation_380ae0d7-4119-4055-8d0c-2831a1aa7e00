package com.shuidihuzhu.cf.cfgrowthtoolapi.service.hospital;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.hospital.HospitalEditModel;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestParam;

public interface HospitalService {

    Response<String> addWaitHospital(@RequestParam(value = "city") String city,
                                     @RequestParam(value = "isSync",defaultValue = "false") boolean isSync);

    Response<String> addLogicHospital(@ApiParam("医院名称") @RequestParam("name") String name,
                                      @ApiParam("省份名称") @RequestParam("province") String province,
                                      @ApiParam("城市名称") @RequestParam("city") String cityName,
                                      @ApiParam("院区数") @RequestParam("areaCount")  Integer areaCount,
                                      @ApiParam("公章是否独立 1:是 0:否，只有一个院区的默认为1") @RequestParam("independenceFlag")  Integer independenceFlag,
                                      @ApiParam("材料是否独立标识 1:是 0:否，只有一个院区的默认为1") @RequestParam("materialFlag") Integer materialFlag,
                                      @RequestParam(value = "alias",required = false,defaultValue = "") String alias);

    Response<String> addAreaHospital(@RequestParam("logicName") String logicName,
                                     @RequestParam("hospitalName") String hospitalName,
                                     @RequestParam("cityName") String cityName,
                                     @RequestParam("provinceName") String provinceName,
                                     @RequestParam("address") String addrees);

    Response<String> addLogicToAreaHospitalByCity(@RequestParam("cityName") String cityName);

    Response<String> syncManualHospitalByCity(@RequestParam("cityName") String cityName);

    Response<String> syncAreaHospitalAlias(@RequestParam("cityName") String cityName,
                                           @RequestParam("logicName") String logicName,
                                           @RequestParam("hospitalName") String hospitalName,
                                           @RequestParam(value = "alias",defaultValue = "") String alias);

    void deleteHospital(HospitalEditModel hospitalEditModel);
}
