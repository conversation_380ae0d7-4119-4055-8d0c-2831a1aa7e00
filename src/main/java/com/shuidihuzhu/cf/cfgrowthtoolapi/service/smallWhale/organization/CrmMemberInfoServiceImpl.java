package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.cache.*;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfCrowdfundingCityDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfRoleCarteConfigModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpiv2.PepRealTimeLeaderInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.common.MemberOrOrgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.BdCrmMemberModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.LeaderPermissionInfo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientpt.PatientHandlerModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.ApolloService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.menu.IRoleCarteService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.dao.bdcrm.PreVolunteerOrgInfoRelationDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmUserOrgInfo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.comparators.FixedOrderComparator;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-05-19 12:32
 **/
@Service
@Slf4j
public class CrmMemberInfoServiceImpl implements ICrmMemberInfoService {

    @Autowired
    private ICrmSelfBuiltOrgReadService organizationService;

    @Autowired
    private ICrmOrganizationRelationService organizationRelationService;

    @Autowired
    private ICfVolunteerService volunteerService;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired
    private IBdCrmOrganizationPathService organizationPathService;

    @Autowired
    private ApolloService apolloService;

    @Autowired
    private PreVolunteerOrgInfoRelationDao preVolunteerOrgInfoRelationDao;

    @Autowired
    private ICfVolunteerService cfVolunteerServiceImpl;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private ICfCrowdfundingCityDelegate cfCrowdfundingCityDelegate;

    @Autowired
    private IRoleCarteService roleCarteService;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoService;


    LoadingCache<String, LeaderPermissionInfo> leaderDataPermissionCache = CacheBuilder
            .newBuilder()
            .maximumSize(500)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build(new CacheLoader<String, LeaderPermissionInfo>() {
                @Override
                public LeaderPermissionInfo load(String uniqueCode) {
                    log.debug("设置本地uniqueCode权限缓存");
                    return setLeaderPermissionInfo(uniqueCode);
                }
            });


    @Override
    public Map<String, BdCrmMemberModel> listByUniqueCodes(List<String> uniqueCodes) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Maps.newHashMap();
        }
        List<CrowdfundingVolunteer> volunteers = volunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodes);
        if (CollectionUtils.isEmpty(volunteers)) {
            return Maps.newHashMap();
        }
        List<BdCrmOrgUserRelationDO> relationDOList = organizationRelationService.listByUniqueCodes(uniqueCodes);
        return createMemberModel(volunteers, relationDOList);
    }


    private Map<String, BdCrmMemberModel> createMemberModel(List<CrowdfundingVolunteer> volunteers,
                                                            List<BdCrmOrgUserRelationDO> relationDOList) {

        if (CollectionUtils.isEmpty(volunteers)) {
            return Maps.newHashMap();
        }
        Map<String, BdCrmMemberModel> uniqueCodeTMemberModel = Maps.newHashMap();
        Map<String, CrowdfundingVolunteer> uniqueTVolunteer = volunteers.stream().collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity(), (before, after) -> before));
        Map<String, List<BdCrmOrgUserRelationDO>> uniqueCodeGroupRelation = relationDOList.stream().collect(Collectors.groupingBy(BdCrmOrgUserRelationDO::getUniqueCode));
        for (String uniqueCode : uniqueTVolunteer.keySet()) {
            BdCrmMemberModel memberModel = new BdCrmMemberModel();

            memberModel.setVolunteer(uniqueTVolunteer.get(uniqueCode));

            List<BdCrmOrgUserRelationDO> relationDOS = uniqueCodeGroupRelation.getOrDefault(uniqueCode, Lists.newArrayList());
            memberModel.setRelationDOList(relationDOS);

            List<Long> orgIds = relationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList());
            Map<Long, BdCrmOrganizationDO> listOrgInfo = organizationService.listOrgInfo(orgIds);
            //找到一个组织
            if (CollectionUtils.isNotEmpty(orgIds)) {
                Long orgId = orgIds.get(0);
                BdCrmOrganizationDO parentOrg = organizationService.getParentOrg(orgId);
                if (parentOrg != null) {
                    memberModel.setOrgPathForShortShow(parentOrg.getOrgName() + "-" + listOrgInfo.get(orgId).getOrgName());
                }
            }
            memberModel.setOrganizationMap(listOrgInfo);
            uniqueCodeTMemberModel.put(uniqueCode, memberModel);
        }

        return uniqueCodeTMemberModel;
    }


    @Override
    public BdCrmOrganizationDOWithChain getRightBdCaseOrg(String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return null;
        }
        //查询组织信息
        List<BdCrmOrgUserRelationDO> relationDOList = organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode);
        Map<Long, BdCrmOrgUserRelationDO> orgIdTRelation = relationDOList.stream().collect(Collectors.toMap(BdCrmOrgUserRelationDO::getOrgId, Function.identity(), (before, after) -> before));
        List<Long> orgIds = Lists.newArrayList(orgIdTRelation.keySet());
        if (CollectionUtils.isEmpty(orgIds)) {
            return null;
        }
        BdCrmOrganizationDOWithChain rightOrg = null;
        int maxDepth = 0;
        for (Long orgId : orgIds) {
            List<BdCrmOrganizationDO> organizationDOS = organizationService.listParentOrgAsChain(orgId);
            if (CollectionUtils.isEmpty(organizationDOS)) {
                continue;
            }
            int size = organizationDOS.size();

            BdCrmOrganizationDOWithChain chain = BdCrmOrganizationDOWithChain.createByDO(organizationDOS.get(size - 1));
            chain.setChain(organizationDOS.stream().map(item -> String.valueOf(item.getId())).collect(Collectors.joining("-")));
            chain.setChainName(organizationDOS.stream().map(item -> String.valueOf(item.getOrgName())).collect(Collectors.joining("-")));
            if (maxDepth < size) {
                rightOrg = chain;
                maxDepth = size;
            } else if (maxDepth == size) {
                //对比两个关联的时间,早绑定的作为组织
                Long beforeTime = Optional.ofNullable(orgIdTRelation.get(rightOrg.getId()))
                        .map(BdCrmOrgUserRelationDO::getCreateTime)
                        .map(Date::getTime)
                        .orElse(0L);
                Long afterTime = Optional.ofNullable(orgIdTRelation.get(orgId))
                        .map(BdCrmOrgUserRelationDO::getCreateTime)
                        .map(Date::getTime)
                        .orElse(0L);
                rightOrg = afterTime > beforeTime ? rightOrg : chain;
            }
        }
        log.info("人员:{}对应的组织:{}", uniqueCode, JSON.toJSONString(rightOrg));
        return rightOrg;
    }


    @Override
    public List<BdCrmOrganizationDO> listOrgForCByMis(String mis) {
        //查找mis对应的组织
        List<Long> orgIds = organizationRelationService.listMemberOrgRelationByMis(mis)
                .stream()
                .map(BdCrmOrgUserRelationDO::getOrgId)
                .collect(Collectors.toList());
        return this.listShortestPathInner(orgIds);
    }

    @Override
    public List<BdCrmOrganizationDO> listForCByUniqueCode(String uniqueCode) {
        //查找uniqueCode 对应的组织
        List<Long> orgIds = organizationRelationService.listByUniqueCodes(Lists.newArrayList(uniqueCode))
                .stream()
                .map(BdCrmOrgUserRelationDO::getOrgId)
                .collect(Collectors.toList());
        return this.listShortestPathInner(orgIds);
    }

    @Override
    public Set<Long> listAllOrgByMis(String mis) {
        if (apolloService.isUseRedisQuery()) {
            String redisValue = redissonHandler.get(GeneralConstant.VOLUNTEER_VIEW_SUB_ORGS + mis, String.class);
            if (StringUtils.isNotBlank(redisValue)) {
                List<Long> orgIds = JSON.parseArray(redisValue, Long.class);
                return Sets.newHashSet(orgIds);
            }
            log.info(GeneralConstant.VOLUNTEER_VIEW_SUB_ORGS + mis + "redis中无值，需要重新设置");
        }
        List<BdCrmOrganizationDO> bdCrmOrganizationDOS = listOrgForCByMis(mis);
        if (CollectionUtils.isEmpty(bdCrmOrganizationDOS)) {
            return Sets.newHashSet();
        }
        Set<Long> orgIds = Sets.newHashSet();
        for (BdCrmOrganizationDO bdCrmOrganizationDO : bdCrmOrganizationDOS) {
            orgIds.addAll(organizationService.listAllSubOrgIncludeSelf(bdCrmOrganizationDO.getId())
                    .stream()
                    .map(BdCrmOrganizationDO::getId)
                    .collect(Collectors.toList()));
        }
        //同步到redis中
        try {
            if (StringUtils.isNotBlank(mis)) {
                redissonHandler.setEX(GeneralConstant.VOLUNTEER_VIEW_SUB_ORGS + mis, JSON.toJSONString(orgIds), RedissonHandler.ONE_DAY);
            }
        } catch (Exception e) {
            log.error("设置:{}缓存失败", GeneralConstant.VOLUNTEER_VIEW_SUB_ORGS + mis, e);
        }
        return orgIds;
    }

    @Override
    public Set<Long> listAllOrgByUniqueCode(String uniqueCode) {
        if (apolloService.isUseRedisQuery()) {
            String redisValue = redissonHandler.get(GeneralConstant.VOLUNTEER_VIEW_SUB_ORG_UNIQUECODE + uniqueCode, String.class);
            if (StringUtils.isNotBlank(redisValue)) {
                List<Long> orgIds = JSON.parseArray(redisValue, Long.class);
                return Sets.newHashSet(orgIds);
            }
            log.info(GeneralConstant.VOLUNTEER_VIEW_SUB_ORG_UNIQUECODE + uniqueCode + "redis中无值，需要重新设置");
        }
        List<BdCrmOrganizationDO> bdCrmOrganizationDOS = listForCByUniqueCode(uniqueCode);
        if (CollectionUtils.isEmpty(bdCrmOrganizationDOS)) {
            return Sets.newHashSet();
        }
        Set<Long> orgIds = Sets.newHashSet();
        for (BdCrmOrganizationDO bdCrmOrganizationDO : bdCrmOrganizationDOS) {
            orgIds.addAll(organizationService.listAllSubOrgIncludeSelf(bdCrmOrganizationDO.getId())
                    .stream()
                    .map(BdCrmOrganizationDO::getId)
                    .collect(Collectors.toList()));
        }
        //同步到redis中
        try {
            redissonHandler.setEX(GeneralConstant.VOLUNTEER_VIEW_SUB_ORG_UNIQUECODE + uniqueCode, JSON.toJSONString(orgIds), RedissonHandler.ONE_DAY);
        } catch (Exception e) {
            log.error("设置:{}缓存失败", GeneralConstant.VOLUNTEER_VIEW_SUB_ORG_UNIQUECODE + uniqueCode, e);
        }
        return orgIds;
    }


    @Override
    public List<BdCrmOrganizationDO> listAllOrgModelByUniqueCode(String uniqueCode) {
        Set<Long> orgIds = listAllOrgByUniqueCode(uniqueCode);
        return Lists.newArrayList(organizationService.listOrgInfo(Lists.newArrayList(orgIds)).values());
    }

    /**
     * 有风险，空mis的情况要注意
     *
     * @param mis
     * @param inputOrgId
     * @return
     */
    @Override
    public List<Integer> inspectOrgIdByMis(String mis, int inputOrgId) {
        List<Integer> result = Lists.newArrayList();
        //对于超级管理员，应该是传啥能看到啥
        CrowdfundingVolunteer volunteer = volunteerService.getVolunteerByMis(mis);
        if (volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.SUPER_LEADER.getLevel()) {
            if (inputOrgId == 0) {
                result.add(GeneralConstant.CRM_ORG_ID);
            } else {
                result.add(inputOrgId);
            }
            return result;
        }
        List<Integer> directOrgIds = this.listOrgForCByMis(mis)
                .stream()
                .map(item -> (int) item.getId())
                .collect(Collectors.toList());
        //检查当前mis有没有orgId的查看权限
        Set<Long> orgIds = this.listAllOrgByMis(mis);
        //传入的组织为用户所在组织
        if (directOrgIds.contains(inputOrgId)) {
            result = directOrgIds;
            return result;
        }
        if (orgIds.contains((long) inputOrgId)) {
            result.add(inputOrgId);
            return result;
        }
        //兜底
        result = directOrgIds;
        return result;
    }


    @Override
    public List<Integer> inspectOrgIdByUniqueCode(String uniqueCode, int inputOrgId) {
        List<Integer> result = Lists.newArrayList();
        //对于超级管理员，应该是传啥能看到啥
        CrowdfundingVolunteer volunteer = volunteerService.getVolunteerByUniqueCode(uniqueCode);
        if (volunteer.getLevel() == CrowdfundingVolunteerEnum.RoleEnum.SUPER_LEADER.getLevel()) {
            if (inputOrgId == 0) {
                result.add(GeneralConstant.CRM_ORG_ID);
            } else {
                result.add(inputOrgId);
            }
            return result;
        }
        List<Integer> directOrgIds = this.listForCByUniqueCode(uniqueCode)
                .stream()
                .map(item -> (int) item.getId())
                .collect(Collectors.toList());
        //检查当前uniqueCode有没有orgId的查看权限
        Set<Long> orgIds = this.listAllOrgByUniqueCode(uniqueCode);
        //传入的组织为用户所在组织
        if (directOrgIds.contains(inputOrgId)) {
            result = directOrgIds;
            return result;
        }
        if (orgIds.contains((long) inputOrgId)) {
            result.add(inputOrgId);
            return result;
        }
        //兜底
        result = directOrgIds;
        return result;
    }


    @Override
    public long getUniqueCodeAreaOrgId(String uniqueCode) {
        BdCrmOrganizationDOWithChain rightBdCaseOrg = this.getRightBdCaseOrg(uniqueCode);
        try {
            if (rightBdCaseOrg == null) {
                return 0L;
            }
            List<String> orgIds = Splitter.on("-")
                    .splitToList(Optional.ofNullable(rightBdCaseOrg.getChain()).orElse(""));
            if (CollectionUtils.isNotEmpty(orgIds)) {
                int size = orgIds.size();
                if (size == 1) {
                    return Long.parseLong(orgIds.get(0));
                } else {
                    return Long.parseLong(orgIds.get(1));
                }
            }
        } catch (Exception e) {
            log.error("获取人员:{}区域id异常", uniqueCode, e);
        }
        return 0L;
    }

    public List<String> getUniqueCodeSubAreaOrgIds(String uniqueCode) {
        BdCrmOrganizationDOWithChain rightBdCaseOrg = this.getRightBdCaseOrg(uniqueCode);
        try {
            if (rightBdCaseOrg == null) {
                return Lists.newArrayList();
            }
            List<String> orgIds = Splitter.on("-")
                    .splitToList(Optional.ofNullable(rightBdCaseOrg.getChain()).orElse(""));
            if (CollectionUtils.isNotEmpty(orgIds)) {
                return orgIds;
            }
        } catch (Exception e) {
            log.error("获取人员:{}区域id异常", uniqueCode, e);
        }
        return Lists.newArrayList();
    }


    @Override
    public long getOrgAreaWithLevel(String uniqueCode, int level) {
        BdCrmOrganizationDOWithChain rightBdCaseOrg = this.getRightBdCaseOrg(uniqueCode);
        try {
            if (rightBdCaseOrg == null) {
                return 0L;
            }
            List<String> orgIds = Splitter.on("-")
                    .splitToList(Optional.ofNullable(rightBdCaseOrg.getChain()).orElse(""));
            if (CollectionUtils.isNotEmpty(orgIds) && orgIds.size() > level) {
                return Long.parseLong(orgIds.get(level));
            }
        } catch (Exception e) {
            log.error("获取人员:{}区域id异常", uniqueCode, e);
        }
        return 0L;
    }

    @Override
    public BdCrmUserOrgInfo getCrmUserOrgInfo(String uniqueCode) {
        List<BdCrmOrgUserRelationDO> relationDOS = organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode);
        //设置对应的组织信息
        if (CollectionUtils.isEmpty(relationDOS)) {
            return null;
        }
        BdCrmUserOrgInfo bdCrmUserOrgInfo = new BdCrmUserOrgInfo();
        List<BdCrmUserOrgInfo.CityInfo> leafCityInfoList = Lists.newArrayList();
        List<BdCrmUserOrgInfo.CityInfo> belongCityInfoList = Lists.newArrayList();
        for (BdCrmOrgUserRelationDO relationDO : relationDOS) {
            //根据组织获取下级所有组织
            List<BdCrmOrganizationDO> subOrgList = organizationService.listAllSubOrgIncludeSelf(relationDO.getOrgId())
                    .stream()
                    .filter(item -> item.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode())
                    .collect(Collectors.toList());
            for (BdCrmOrganizationDO bdCrmOrganizationDO : subOrgList) {
                boolean containsId = belongCityInfoList.stream()
                        .map(BdCrmUserOrgInfo.CityInfo::getOrgId)
                        .anyMatch(item -> ObjectUtils.nullSafeEquals(item, bdCrmOrganizationDO.getId()));
                if (!containsId) {
                    BdCrmUserOrgInfo.CityInfo cityInfo = new BdCrmUserOrgInfo.CityInfo();
                    cityInfo.setOrgId((int) bdCrmOrganizationDO.getId());
                    cityInfo.setOrgName(bdCrmOrganizationDO.getOrgName());
                    cityInfo.setOrgAttribute(bdCrmOrganizationDO.getOrgAttribute());
                    leafCityInfoList.add(cityInfo);
                }
            }
            BdCrmOrganizationDO currentOrg = organizationService.getCurrentOrgById(relationDO.getOrgId());
            if (currentOrg == null) {
                log.info("relationDO对应的organization no found:{}", relationDO);
                continue;
            }
            BdCrmUserOrgInfo.CityInfo belongCity = new BdCrmUserOrgInfo.CityInfo();
            belongCity.setOrgId((int) relationDO.getOrgId());
            belongCity.setOrgName(currentOrg.getOrgName());
            belongCity.setOrgAttribute(currentOrg.getOrgAttribute());
            List<BdCrmOrganizationDO> bdCrmOrganizationDOS = organizationService.listParentOrgAsChain(currentOrg.getId());
            belongCity.setChainFromRootIds(bdCrmOrganizationDOS.stream().map(item -> (int) item.getId()).collect(Collectors.toList()));
            belongCity.setChainFromRootNames(bdCrmOrganizationDOS.stream().map(BdCrmOrganizationDO::getOrgName).collect(Collectors.toList()));
            belongCityInfoList.add(belongCity);
        }
        bdCrmUserOrgInfo.setLeafCityInfoList(leafCityInfoList);
        bdCrmUserOrgInfo.setBelongCityInfoList(belongCityInfoList);
        return bdCrmUserOrgInfo;
    }

    @Override
    public CrowdfundingVolunteer findApplyLeaderForApprove(String uniqueCode) {
        CrowdfundingVolunteer volunteer = cfVolunteerServiceImpl.getVolunteerByUniqueCode(uniqueCode);
        return listApplyLeaderWithExplicit(uniqueCode, CrowdfundingVolunteerEnum.optAndSuperRoles, CrowdfundingVolunteerEnum.delegateRoles.contains(volunteer.getLevel()) ? CrowdfundingVolunteerEnum.canApproveDelegateLevel : CrowdfundingVolunteerEnum.canApproveLevel, null);
    }

    @Override
    public List<CrowdfundingVolunteer> listLeaderWithDefaultExplicit(String uniqueCode, List<Integer> expectRoles, boolean allowTestOrg) {
        if (StringUtils.isBlank(uniqueCode)) {
            return Lists.newArrayList();
        }
        Supplier<List<BdCrmOrganizationDO>> listSupplier = () -> {
            List<BdCrmOrgUserRelationDO> relationDOS = organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode);
            return organizationService.getOrgInfoList(relationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList()));
        };
        return listLeaderWithOrder(uniqueCode, CrowdfundingVolunteerEnum.optAndSuperRoles, expectRoles, null, allowTestOrg, listSupplier);
    }


    @Override
    public List<CrowdfundingVolunteer> listLeaderByCity(String cityName, List<Integer> expectRoles) {
        //找到对应的城市
        Supplier<List<BdCrmOrganizationDO>> listSupplier = () -> organizationService.listNotTestOrg()
                .stream()
                .filter(item -> item.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode())
                .filter(item -> item.getCityName().contains(cityName))
                .collect(Collectors.toList());

        return listLeaderWithOrder(null, CrowdfundingVolunteerEnum.optAndSuperRoles, expectRoles, null, false, listSupplier);
    }


    /**
     * 找上级,直到找到能审核(或者其他动作)的上级
     * example: 武器库 爆款案例 期望找到 区域经理审批
     *
     * @param uniqueCode:人员uniqueCode
     * @param expectRoles:            期望哪些上级审批
     * @return: 上级
     */
    @Override
    public CrowdfundingVolunteer listApplyLeaderWithDefaultExplicit(String uniqueCode, List<Integer> expectRoles) {
        return listApplyLeaderWithExplicit(uniqueCode, CrowdfundingVolunteerEnum.optAndSuperRoles, expectRoles, null);
    }


    /**
     * 找上级,直到找到能审核(或者其他动作)的上级
     * example: 武器库 爆款案例 期望找到 区域经理审批
     *
     * @param uniqueCode:    人员uniqueCode
     * @param filterRole:    需要过滤的角色, 传空不过滤
     * @param expectRoles:   期望哪些上级审批, 传空为全部
     * @param explicitOrder: 期望的排序  优先级最高的排在前面,如果写漏了默认值为: RoleEnum#sortLevel
     * @return: 上级
     */
    @Override
    public CrowdfundingVolunteer listApplyLeaderWithExplicit(String uniqueCode, List<Integer> filterRole, List<Integer> expectRoles, List<Integer> explicitOrder) {
        if (StringUtils.isBlank(uniqueCode)) {
            return null;
        }
        Supplier<List<BdCrmOrganizationDO>> listSupplier = () -> {
            List<BdCrmOrgUserRelationDO> relationDOS = organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode);
            return organizationService.getOrgInfoList(relationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList()));
        };
        return listLeaderWithOrder(uniqueCode, filterRole, expectRoles, explicitOrder, true, listSupplier)
                .stream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public CrowdfundingVolunteer listApplyLeaderWithExplicitByCaseId(int caseId, String uniqueCode, List<Integer> filterRole, List<Integer> expectRoles, List<Integer> explicitOrder) {
        if (StringUtils.isBlank(uniqueCode) || caseId <= 0) {
            return null;
        }

        Supplier<List<BdCrmOrganizationDO>> listSupplier = () -> {
            List<BdCrmOrgUserRelationDO> relationDOS = organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode);
            return organizationService.getOrgInfoList(relationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList()));
        };

        //找到对应的上级
        List<CrowdfundingVolunteer> crowdfundingVolunteerList = listLeaderWithOrder(uniqueCode, filterRole, expectRoles, explicitOrder, true, listSupplier);
        if (CollectionUtils.isEmpty(crowdfundingVolunteerList)) {
            return null;
        }
        //默认取第一个上级
        CrowdfundingVolunteer crowdfundingVolunteer = crowdfundingVolunteerList.get(0);
        //如果只有一个上级,直接返回
        if (crowdfundingVolunteerList.size() == 1) {
            return crowdfundingVolunteer;
        }

        //获取上级所在的组织
        List<String> uniqueCodeList = crowdfundingVolunteerList.stream().map(CrowdfundingVolunteer::getUniqueCode).collect(Collectors.toList());
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOList = organizationRelationService.listByUniqueCodes(uniqueCodeList);
        if (CollectionUtils.isEmpty(bdCrmOrgUserRelationDOList)) {
            return crowdfundingVolunteer;
        }

        //通过案例id获取案例信息
        CfBdCaseInfoDo cfBdCaseInfoDo = cfBdCaseInfoService.getBdCaseInfoByInfoId(caseId);
        if (cfBdCaseInfoDo == null) {
            return crowdfundingVolunteer;
        }
        //通过案例的uniqueCode获取对应的组织
        List<BdCrmOrgUserRelationDO> userRelationDOList = organizationRelationService.listMemberOrgRelationByUniqueCode(cfBdCaseInfoDo.getUniqueCode());
        if (CollectionUtils.isEmpty(userRelationDOList)) {
            return crowdfundingVolunteer;
        }

        //只有一个组织
        if (userRelationDOList.size() == 1) {
            String leaderUniqueCode = getLeaderUniqueCodeByCaseId(bdCrmOrgUserRelationDOList, userRelationDOList.get(0).getOrgId());
            if (StringUtils.isNotEmpty(leaderUniqueCode)) {
                return crowdfundingVolunteerList.stream()
                        .filter(item -> Objects.equals(item.getUniqueCode(), leaderUniqueCode))
                        .findFirst()
                        .orElse(crowdfundingVolunteer);
            }
        } else {
            //多个组织，并且案例对应的组织，是顾问当前所在的组织
            Set<Long> orgIdSet = userRelationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toSet());
            long orgId = Optional.ofNullable(cfBdCaseInfoDo.getOrgId()).map(Long::valueOf).orElse(0L);
            if (orgIdSet.contains(orgId)) {
                String leaderUniqueCode = getLeaderUniqueCodeByCaseId(bdCrmOrgUserRelationDOList, orgId);
                if (StringUtils.isNotEmpty(leaderUniqueCode)) {
                    return crowdfundingVolunteerList.stream()
                            .filter(item -> Objects.equals(item.getUniqueCode(), leaderUniqueCode))
                            .findFirst()
                            .orElse(crowdfundingVolunteer);
                }
            }
        }
        return crowdfundingVolunteer;
    }

    private String getLeaderUniqueCodeByCaseId(List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOList, long orgId) {
        List<BdCrmOrganizationDO> bdCrmOrganizationDOS = organizationService.listParentOrgAsChainOrder(orgId);
        if (CollectionUtils.isEmpty(bdCrmOrganizationDOS)) {
            return null;
        }
        Set<Long> orgIdSet = bdCrmOrganizationDOS.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toSet());
        Optional<String> optional = bdCrmOrgUserRelationDOList.stream().filter(item -> orgIdSet.contains(item.getOrgId())).map(BdCrmOrgUserRelationDO::getUniqueCode).findFirst();
        return optional.orElse(null);
    }


    private List<CrowdfundingVolunteer> listLeaderWithOrder(String uniqueCode, List<Integer> filterRoles,
                                                            List<Integer> expectRoles, List<Integer> explicitOrder,
                                                            boolean allowTestOrg, Supplier<List<BdCrmOrganizationDO>> listSupplier) {
        filterRoles = Optional.ofNullable(filterRoles).orElse(Lists.newArrayList());
        expectRoles = Optional.ofNullable(expectRoles).orElse(Lists.newArrayList());
        explicitOrder = Optional
                .ofNullable(explicitOrder)
                .orElse(Arrays.stream(CrowdfundingVolunteerEnum.RoleEnum.values())
                        .sorted(Ordering.natural().onResultOf(CrowdfundingVolunteerEnum.RoleEnum::getSortLevel))
                        .map(CrowdfundingVolunteerEnum.RoleEnum::getLevel).collect(Collectors.toList())
                );
        List<BdCrmOrganizationDO> orgInfoList = listSupplier.get();
        if (!allowTestOrg) {
            List<Long> testOrgIds = organizationService.listAllTestOrg();
            orgInfoList = orgInfoList.stream().filter(item -> !testOrgIds.contains(item.getId())).collect(Collectors.toList());
        }
        //找到对应的上级
        List<CrowdfundingVolunteer> volunteerList = Lists.newArrayList();
        recursionFindLeader(volunteerList, orgInfoList, filterRoles, expectRoles, uniqueCode);

        FixedOrderComparator<Integer> ordering = new FixedOrderComparator<Integer>(explicitOrder);
        ordering.setUnknownObjectBehavior(FixedOrderComparator.UnknownObjectBehavior.AFTER);
        return volunteerList.stream()
                .sorted(Ordering.from(ordering).onResultOf(CrowdfundingVolunteer::getLevel))
                .collect(Collectors.toList());
    }


    /**
     * 一级一级往上查找上级直到找到为止
     */
    private void recursionFindLeader(List<CrowdfundingVolunteer> volunteerList, List<BdCrmOrganizationDO> bdCrmOrganizationDOList,
                                     List<Integer> filterRole, List<Integer> expectRoles, String uniqueCode) {
        if (CollectionUtils.isNotEmpty(volunteerList) || CollectionUtils.isEmpty(bdCrmOrganizationDOList)) {
            return;
        }
        for (BdCrmOrganizationDO organizationDO : bdCrmOrganizationDOList) {
            long parentId = organizationDO.getParentId();
            List<String> uniqueCodeList = organizationRelationService.listRelationByOrgId(parentId)
                    .stream()
                    .map(BdCrmOrgUserRelationDO::getUniqueCode)
                    .filter(item -> !Objects.equals(uniqueCode, item))   //过滤查找自己本身
                    .collect(Collectors.toList());
            volunteerList.addAll(volunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodeList)
                    .stream()
                    .filter(item -> !filterRole.contains(item.getLevel()) && (CollectionUtils.isEmpty(expectRoles) || expectRoles.contains(item.getLevel())))
                    .filter(crowdfundingVolunteer -> crowdfundingVolunteer.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.ON_THE_JOB.getValue())
                    .filter(crowdfundingVolunteer -> crowdfundingVolunteer.getAccountStatus() == CrowdfundingVolunteerEnum.AccountStatusEnum.DEFAULT.getValue())
                    .collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(volunteerList)) {
                //需要查找再上一级
                BdCrmOrganizationDO parentOrg = organizationService.getCurrentOrgById(parentId);
                if (parentOrg != null) {
                    recursionFindLeader(volunteerList, Lists.newArrayList(parentOrg), filterRole, expectRoles, uniqueCode);
                }
            }
        }
    }


    @Override
    public OpResult<CrowdfundingVolunteer> listRandomLeaderForKpi(String uniqueCode, Long orgId, boolean needRecursion) {
        //排除蜂鸟计划组织
        List<Long> partnerOrgList = organizationService.listAllPartnerOrg();
        List<Long> testOrgIds = Lists.newArrayList();
        if (!apolloService.isGrowthTestEnv()) {
            testOrgIds = organizationService.listAllTestOrg();
        }
        //查询组织信息
        List<BdCrmOrgUserRelationDO> relationDOS = organizationRelationService.listMemberOrgRelationNoMatterDeleteByUniqueCode(uniqueCode)
                .stream()
                .filter(item -> !partnerOrgList.contains(item.getOrgId()))
                .collect(Collectors.toList());
        List<BdCrmOrganizationDO> bdCrmOrganizationDOS = Lists.newArrayList();
        if (CollectionUtils.isEmpty(relationDOS)) {
            //手动离职情况,使用orgId进行兜底
            if (Objects.isNull(orgId)) {
                return OpResult.createFailResult(CfGrowthtoolErrorCode.VOLUNTEER_NO_ADD_ORG);
            } else {
                bdCrmOrganizationDOS = this.listShortestPathInner(Lists.newArrayList(orgId));
            }
        }
        if (CollectionUtils.isEmpty(bdCrmOrganizationDOS)) {
            bdCrmOrganizationDOS = this.listShortestPathInner(relationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList()));
        }
        //找到对应的上级
        //如果组织在一条链路上，那么直接返回对应的
        List<CrowdfundingVolunteer> leaderVolunteers = Lists.newArrayList();
        recursionFindLeaderForKpi(testOrgIds, bdCrmOrganizationDOS, leaderVolunteers, uniqueCode, needRecursion);

        if (CollectionUtils.isEmpty(leaderVolunteers)) {
            return OpResult.createFailResult(CfGrowthtoolErrorCode.CAN_NOT_FIND);
        }
        FixedOrderComparator<Integer> ordering = new FixedOrderComparator<Integer>
                (CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel(),
                        CrowdfundingVolunteerEnum.RoleEnum.PARTNER_MANAGER.getLevel(),
                        CrowdfundingVolunteerEnum.RoleEnum.PARTITION_LEADER.getLevel(),
                        CrowdfundingVolunteerEnum.RoleEnum.AREA_PROVINCE.getLevel(),
                        CrowdfundingVolunteerEnum.RoleEnum.AREA_LEADER.getLevel(),
                        CrowdfundingVolunteerEnum.RoleEnum.BIG_AREA_LEADER.getLevel());
        ordering.setUnknownObjectBehavior(FixedOrderComparator.UnknownObjectBehavior.AFTER);
        CrowdfundingVolunteer result = leaderVolunteers.stream().min(Ordering.from(ordering).onResultOf(CrowdfundingVolunteer::getLevel)).get();
        return OpResult.createSucResult(result);
    }


    private void recursionFindLeaderForKpi(List<Long> finalTestOrgIds, List<BdCrmOrganizationDO> bdCrmOrganizationDOS,
                                           List<CrowdfundingVolunteer> leaderVolunteers, String uniqueCode, boolean needRecursion) {
        //排除测试区域
        bdCrmOrganizationDOS = bdCrmOrganizationDOS.stream()
                .filter(item -> !finalTestOrgIds.contains(item.getId()))
                .collect(Collectors.toList());

        List<String> uniqueCodeList = Lists.newArrayList();
        for (BdCrmOrganizationDO bdCrmOrganizationDO : bdCrmOrganizationDOS) {
            if (bdCrmOrganizationDO.getParentId() == GeneralConstant.CRM_ORG_ID) {
                continue;
            }
            if (bdCrmOrganizationDO.getParentId() != GeneralConstant.CRM_ORG_ID) {
                long parentId = bdCrmOrganizationDO.getParentId();
                uniqueCodeList.addAll(organizationRelationService.listRelationByOrgId(parentId)
                        .stream()
                        .map(BdCrmOrgUserRelationDO::getUniqueCode)
                        .collect(Collectors.toList()));
            }
        }

        leaderVolunteers.addAll(volunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodeList)
                .stream()
                .filter(item -> !ObjectUtils.nullSafeEquals(item.getUniqueCode(), uniqueCode))
                .filter(crowdfundingVolunteer -> crowdfundingVolunteer.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.ON_THE_JOB.getValue())
                .filter(crowdfundingVolunteer -> crowdfundingVolunteer.getAccountStatus() == CrowdfundingVolunteerEnum.AccountStatusEnum.DEFAULT.getValue())
                .filter(crowdfundingVolunteer -> crowdfundingVolunteer.getLevel() != CrowdfundingVolunteerEnum.RoleEnum.OPERATOR.getLevel())
                .collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(leaderVolunteers) || !needRecursion) {
            log.debug("leaderVolunteers:{},needRecursion:{}", leaderVolunteers, needRecursion);
            return;
        }
        //组织往上提升一级
        bdCrmOrganizationDOS = organizationService.getOrgInfoList(bdCrmOrganizationDOS.stream().map(BdCrmOrganizationDO::getParentId).collect(Collectors.toList()));
        //一定加上这个否则会触发死循环
        if (CollectionUtils.isEmpty(bdCrmOrganizationDOS)) {
            log.debug("bdCrmOrganizationDOS:{}", bdCrmOrganizationDOS);
            return;
        }
        recursionFindLeaderForKpi(finalTestOrgIds, bdCrmOrganizationDOS, leaderVolunteers, uniqueCode, needRecursion);
    }


    @Override
    public LeaderPermissionInfo getLeaderPermissionByVolunteer(CrowdfundingVolunteer cfVolunteer) {
        return getLeaderPermission(cfVolunteer.getUniqueCode());
    }


    @Override
    public LeaderPermissionInfo getLeaderPermission(String uniqueCode) {
        // 参数校验
        if (StringUtils.isBlank(uniqueCode)) {
            log.warn("getLeaderPermission uniqueCode is blank");
            return new LeaderPermissionInfo();
        }

        try {
            return leaderDataPermissionCache.get(uniqueCode);
        } catch (ExecutionException e) {
            log.error("获取本地缓存leaderDataPermissionCache异常, uniqueCode:{}", uniqueCode, e);
            return getLeaderPermissionWithRetry(uniqueCode);
        } catch (Exception e) {
            log.error("获取leader权限信息异常, uniqueCode:{}", uniqueCode, e);
            return new LeaderPermissionInfo();
        }
    }

    private LeaderPermissionInfo getLeaderPermissionWithRetry(String uniqueCode) {
        try {
            return setLeaderPermissionInfo(uniqueCode);
        } catch (Exception e) {
            log.error("重试获取leader权限信息异常, uniqueCode:{}", uniqueCode, e);
            return new LeaderPermissionInfo();
        }
    }

    @Override
    public List<CrowdfundingVolunteerEnum.RoleEnum> listCanViewRole(List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Lists.newArrayList();
        }
        Set<Long> allOrgIds = Sets.newHashSet();
        orgIds.forEach(item -> allOrgIds.addAll(organizationService.listAllSubOrgExcludeSelf(item).stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList())));
        List<BdCrmOrgUserRelationDO> relationDOList = organizationRelationService.listByOrgIds(Lists.newArrayList(allOrgIds));
        Set<String> permissionUniqueCodes = relationDOList.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).collect(Collectors.toSet());
        return volunteerService.getCfVolunteerDOByUniqueCodes(permissionUniqueCodes)
                .stream()
                .map(CrowdfundingVolunteer::getLevel)
                .distinct()
                .map(CrowdfundingVolunteerEnum.RoleEnum::parse)
                .collect(Collectors.toList());
    }

    //过滤 同级别（最高级别）的人员  + 过滤所在（最高）组织
    @Override
    public LeaderPermissionInfo getLeaderPermissionNoShowSameLevel(String uniqueCode) {
        LeaderPermissionInfo leaderPermission = getLeaderPermission(uniqueCode);
        if (leaderPermission == null) {
            return leaderPermission;
        }
        List<BdCrmOrganizationDO> organizationDOS = listForCByUniqueCode(uniqueCode);
        List<Long> bindOrgIds = organizationDOS.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList());

        List<String> sameLevelUniques = organizationRelationService.listByOrgIds(bindOrgIds)
                .stream()
                .map(BdCrmOrgUserRelationDO::getUniqueCode)
                .filter(code -> !Objects.equals(uniqueCode, code))
                .collect(Collectors.toList());
        Set<String> filterUniqueCodes = leaderPermission.getPermissionUniqueCodes()
                .stream()
                .filter(item -> !sameLevelUniques.contains(item))
                .collect(Collectors.toSet());

        LeaderPermissionInfo result = new LeaderPermissionInfo();
        result.setPermissionUniqueCodes(filterUniqueCodes);

        Set<Long> filterOrgIds = leaderPermission.getPermissionOrgIds()
                .stream()
                .filter(item -> !bindOrgIds.contains(item))
                .collect(Collectors.toSet());
        result.setPermissionOrgIds(filterOrgIds);

        return result;
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listAllSubMemberIncludeSelf(int orgId) {
        List<BdCrmOrganizationDO> organizationDOS = organizationService.listAllSubOrgIncludeSelf(orgId);
        return organizationRelationService.listByOrgIdsFromDB(organizationDOS.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList()));
    }

    @Override
    public List<BdCrmOrgUserRelationDO> listNotInTestRelation(String uniqueCode) {
        //排除掉测试大区
        return organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode)
                .stream()
                .filter(item -> !organizationService.listAllTestOrg().contains(item.getOrgId()))
                .collect(Collectors.toList());
    }

    @Override
    public CrowdfundingVolunteer getRandomVolunteerByCityIds(int cityId, String uniqueCode) {
        if (cityId > 0) {
            List<BdCrmOrganizationDO> allOrg = organizationService.getAllOrg();
            BdCrmOrganizationDO organizationDO = allOrg.stream()
                    .filter(item -> Objects.equals(item.getCityId(), cityId))
                    .findFirst()
                    .orElse(null);
            if (organizationDO != null) {
                List<BdCrmOrgUserRelationDO> relationDOList = organizationRelationService.listRelationByOrgId(organizationDO.getId())
                        .stream()
                        .filter(item -> !Objects.equals(uniqueCode, item.getUniqueCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(relationDOList)) {
                    return getValidVolunteer(relationDOList);
                }
            }
        }
        //使用兜底随机找一个非测试的顾问
        List<BdCrmOrganizationDO> leafOrganizationList = organizationService.listNotTestOrg()
                .stream()
                .filter(item -> item.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode())
                .collect(Collectors.toList());
        for (BdCrmOrganizationDO organizationDO : leafOrganizationList) {
            List<BdCrmOrgUserRelationDO> relationDOList = organizationRelationService.listRelationByOrgId(organizationDO.getId())
                    .stream()
                    .filter(item -> !Objects.equals(uniqueCode, item.getUniqueCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(relationDOList)) {
                return getValidVolunteer(relationDOList);
            }
        }
        return null;
    }

    @Override
    public BdCrmOrgUserRelationDO findShortestPathForKpi(String uniqueCode) {
        return findShortestPath(uniqueCode, false);
    }

    @Override
    public BdCrmOrgUserRelationDO findShortestPathForDelegateKpi(String uniqueCode) {
        return findShortestPath(uniqueCode, true);
    }


    public BdCrmOrgUserRelationDO findShortestPath(String uniqueCode, boolean delegateRole) {
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode);
        Map<Long, String> orgMap = organizationService.listChainByOrgIdsWithDefaultSplitter(bdCrmOrgUserRelationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId)
                .collect(Collectors.toList()));
        if (bdCrmOrgUserRelationDOS.size() == 1) {
            return bdCrmOrgUserRelationDOS.get(0);
        }
        //排除掉蜂鸟
        List<Long> testOrgIds = Lists.newArrayList();
        if (!apolloService.isGrowthTestEnv()) {
            testOrgIds = organizationService.listAllTestOrg();
        }
        if (!delegateRole) {
            testOrgIds.addAll(organizationService.listAllPartnerOrg());
        }

        List<Long> finalTestOrgIds = testOrgIds;
        return bdCrmOrgUserRelationDOS.stream()
                .filter(item -> !finalTestOrgIds.contains(item.getOrgId()))
                .min(Comparator.comparing(item -> Optional.ofNullable(orgMap.get(item.getOrgId()))
                        .map(value -> value.split("-").length)
                        .orElse(Integer.MAX_VALUE))).orElse(null);
    }

    @Override
    public Response<Boolean> canAddPartnerManager(CrowdfundingVolunteer crowdfundingVolunteer) {
        //非渠道经理不需要判断
        if (crowdfundingVolunteer == null || !CrowdfundingVolunteerEnum.partnerManager.contains(crowdfundingVolunteer.getLevel())) {
            return NewResponseUtil.makeSuccess(true);
        }
        List<Long> bindOrgIds = organizationRelationService.listMemberOrgRelationByUniqueCode(crowdfundingVolunteer.getUniqueCode())
                .stream()
                .map(BdCrmOrgUserRelationDO::getOrgId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bindOrgIds)) {
            //还没有绑定任何组织
            return NewResponseUtil.makeSuccess(true);
        }
        List<Long> partnerOrgList = organizationService.listAllPartnerOrg();
        if (!partnerOrgList.containsAll(bindOrgIds)) {
            return NewResponseUtil.makeFail("绑定组织中含有非蜂鸟计划组织");
        }
        for (Long bindOrgId : bindOrgIds) {

            Response<Boolean> canAddPartnerManager = canAddPartnerManager(bindOrgId, crowdfundingVolunteer.getLevel());
            if (canAddPartnerManager.notOk()) {
                return canAddPartnerManager;
            }
        }
        return NewResponseUtil.makeSuccess(true);
    }

    @Override
    public Response<Boolean> canAddPartnerManager(long orgId, int level) {
        log.debug("canAddPartnerManager level:{}", level);
        if (!Objects.equals(level, CrowdfundingVolunteerEnum.RoleEnum.PARTNER_MANAGER.getLevel())) {
            return NewResponseUtil.makeSuccess(true);
        }
        //渠道经理下级组织人员只能是兼职
        List<Long> subOrgIds = organizationService.listAllSubOrgExcludeSelf(orgId)
                .stream()
                .map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toList());
        List<String> subUniqueCodeList = organizationRelationService.listByOrgIds(subOrgIds)
                .stream()
                .map(BdCrmOrgUserRelationDO::getUniqueCode)
                .collect(Collectors.toList());
        boolean existNotPartner = volunteerService.getCfVolunteerDOByUniqueCodes(subUniqueCodeList)
                .stream()
                .anyMatch(item -> Objects.equals(item.getPartnerTag(), CrowdfundingVolunteerEnum.PartnerTagEnum.normal.getCode()));
        if (existNotPartner) {
            return NewResponseUtil.makeFail("渠道经理下级只能是兼职顾问");
        }
        return NewResponseUtil.makeSuccess(true);
    }

    @Override
    public void syncOrgPath() {
        //设置orgPath
        List<BdCrmOrganizationDO> organizationDOS = organizationService.getAllOrg();
        Map<Long, String> orgIdTPath = organizationService.listChainByOrgIdsWithDefaultSplitter(organizationDOS.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList()));
        for (BdCrmOrganizationDO organizationDO : organizationDOS) {
            BdCrmOrganizationPathDO organizationPathDO = new BdCrmOrganizationPathDO();
            organizationPathDO.setId(organizationDO.getId());
            organizationPathDO.setOrgName(Optional.ofNullable(organizationDO.getOrgName()).orElse(""));
            organizationPathDO.setOrgPath(orgIdTPath.getOrDefault(organizationDO.getId(), ""));
            organizationPathDO.setOrgTag(GrowthtoolUtil.partnerOrgTag(organizationPathDO.getOrgPath()));
            organizationPathService.updateOrInsert(organizationPathDO);
        }
    }

    @Override
    public void resetStaffStatus(List<? extends MemberOrOrgModel> crmCrowdfundingModels) {
        List<MemberOrOrgModel> volunteerModels = crmCrowdfundingModels.stream()
                .filter(item -> item.getOrgOrMis() == 1)
                .collect(Collectors.toList());
        //根据uniqueCode查找信息
        Map<String, CrowdfundingVolunteer> volunteerMap = volunteerService
                .getCfVolunteerDOByUniqueCodes(volunteerModels.stream().map(MemberOrOrgModel::getUniqueKey).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(CrowdfundingVolunteer::getUniqueCode, Function.identity(), (before, after) -> before));
        try {
            if (CollectionUtils.isEmpty(volunteerModels)) {
                return;
            }
            crmCrowdfundingModels.removeIf(item -> {
                CrowdfundingVolunteer volunteer = volunteerMap.get(item.getUniqueKey());
                if (volunteer != null) {
                    return !CrowdfundingVolunteerEnum.onlyRaiseCaseRole.contains(volunteer.getLevel());
                }
                return false;
            });
            for (MemberOrOrgModel countModel : volunteerModels) {
                CrowdfundingVolunteer volunteer = volunteerMap.get(countModel.getUniqueKey());
                if (volunteer != null) {
                    //设置人员名称,必须设置
                    countModel.setName(volunteer.getVolunteerName());
                } else {
                    log.warn("unique:{}找不到对应的账号信息", countModel.getUniqueKey());
                    continue;
                }
                CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(volunteer.getLevel());
                if (roleEnum != null && !CrowdfundingVolunteerEnum.commonRoles.contains(roleEnum.getLevel())) {
                    countModel.setStaffStatus(Optional.ofNullable(countModel.getStaffStatus()).orElse("") + roleEnum.getDesc());
                }

                //标记兼职
                if (Objects.equals(volunteer.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.PARTNER_COMMON.getLevel())) {
                    countModel.setPartnerTag("筹款伙伴");
                }
                if (Objects.equals(volunteer.getLevel(), CrowdfundingVolunteerEnum.RoleEnum.PARTNER_AID.getLevel())) {
                    countModel.setPartnerTag("小助理");
                }
                //标记下是否离职
                if (volunteer.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue()) {
                    countModel.setStaffStatus(CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getDesc());
                }
                if (StringUtils.isBlank(countModel.getStaffStatus())) {
                    continue;
                }
                if (StringUtils.isBlank(countModel.getUniqueKey())) {
                    continue;
                }
                PreVolunteerOrgInfoRelationDO preVolunteerOrgInfoRelationDO = preVolunteerOrgInfoRelationDao.getPreVolunteerOrgInfoRelationDO(countModel.getUniqueKey());
                if (preVolunteerOrgInfoRelationDO != null) {
                    countModel.setStaffStatus("情景复试");
                }
            }
        } catch (Exception e) {
            log.warn("resetStaffStatus Exception ", e);
        }
    }

    @Override
    public List<CrowdfundingVolunteer> listProvinceRolesVolunteer(String uniqueCode) {
        // 1、根据uniqueCode 查找volunteer
        // 离职 时 查询 bd_crm_org_user_relation 不加is_delete 然后获取id最大的一条记录
        // 在职 时 查询 bd_crm_org_user_relation 加is_delete=0
        // 2、根据parent_org_id查询 组织下的人员
        // 3、过滤角色 只保留 业务经理/渠道经理
        CrowdfundingVolunteer volunteer = volunteerService.getByUniqueCode(uniqueCode);
        if (volunteer == null) {
            log.info("listProvinceRolesVolunteer volunteer is null param:{}", uniqueCode);
            return Lists.newArrayList();
        }
        List<Long> orgIdList = Lists.newArrayList();
        if (volunteer.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue()) {
            Long orgId = dimissionUserInfo(uniqueCode);
            if (Objects.nonNull(orgId)) {
                orgIdList.add(orgId);
            }
        } else {
            List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOList = organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode);
            if (CollectionUtils.isNotEmpty(bdCrmOrgUserRelationDOList)) {
                orgIdList = bdCrmOrgUserRelationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(orgIdList)) {
            log.info("listProvinceRolesVolunteer orgIdList is empty param:{}", uniqueCode);
            return Lists.newArrayList();
        }
        List<BdCrmOrganizationDO> orgInfoList = organizationService.getOrgInfoList(orgIdList);
        return getLeaderUniqueCodeList(orgInfoList, uniqueCode);
    }

    private List<Long> getParentIdList(List<BdCrmOrganizationDO> orgInfoList) {
        return orgInfoList.stream().map(BdCrmOrganizationDO::getParentId).collect(Collectors.toList());
    }

    private List<String> getUniqueCodesByOrgId(Long orgId, String excludeCode) {
        return organizationRelationService.listRelationByOrgId(orgId)
                .stream()
                .map(BdCrmOrgUserRelationDO::getUniqueCode)
                .filter(item -> !Objects.equals(excludeCode, item))
                .collect(Collectors.toList());
    }

    private List<String> getUniqueCodesForParentOrgs(List<Long> parentIdList, String excludeCode) {
        return parentIdList.stream()
                .map(parentId -> getUniqueCodesByOrgId(parentId, excludeCode))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                })
                .orElse(Lists.newArrayList());
    }

    private List<CrowdfundingVolunteer> getVolunteerByUniqueCodes(List<String> uniqueCodeList) {
        if (CollectionUtils.isEmpty(uniqueCodeList)) {
            return Lists.newArrayList();
        }
        return volunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodeList).stream().filter(item -> CrowdfundingVolunteerEnum.volunteerLeaderRoles.contains(item.getLevel())).collect(Collectors.toList());
    }

    private List<CrowdfundingVolunteer> getLeaderUniqueCodeList(List<BdCrmOrganizationDO> orgInfoList, String uniqueCode) {
        List<Long> parentIdList = getParentIdList(orgInfoList);
        List<String> uniqueCodeList = getUniqueCodesForParentOrgs(parentIdList, uniqueCode);
        List<CrowdfundingVolunteer> volunteerByUniqueCodes = getVolunteerByUniqueCodes(uniqueCodeList);

        if (CollectionUtils.isEmpty(volunteerByUniqueCodes)) {
            List<BdCrmOrganizationDO> superiorOrgInfoList = organizationService.getOrgInfoList(parentIdList);
            uniqueCodeList = getUniqueCodesForParentOrgs(getParentIdList(superiorOrgInfoList), uniqueCode);
            volunteerByUniqueCodes = getVolunteerByUniqueCodes(uniqueCodeList);
        }
        return volunteerByUniqueCodes;
    }


    /**
     * 获取有效的顾问 (排除兼职)
     *
     * @param relationDOList
     * @return
     */
    private CrowdfundingVolunteer getValidVolunteer(List<BdCrmOrgUserRelationDO> relationDOList) {
        List<CrowdfundingVolunteer> list = volunteerService.getCfVolunteerDOByUniqueCodes(relationDOList.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).distinct().collect(Collectors.toList()))
                .stream()
                .filter(item -> Objects.equals(item.getPartnerTag(), CrowdfundingVolunteerEnum.PartnerTagEnum.normal.getCode()))
                .collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    private LeaderPermissionInfo setLeaderPermissionInfo(String uniqueCode) {
        //找到所有的下级组织
        LeaderPermissionInfo leaderPermissionInfo = new LeaderPermissionInfo();
        List<BdCrmOrgUserRelationDO> relationDOList = organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode);
        if (CollectionUtils.isEmpty(relationDOList)) {
            return leaderPermissionInfo;
        }
        //如果是顾问,只应该有他自己的权限,不应该有平级的权限
        if (relationDOList.size() == 1) {
            BdCrmOrganizationDO org = organizationService.getCurrentOrgById(relationDOList.get(0).getOrgId());
            if (org.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode()) {
                leaderPermissionInfo.setPermissionOrgIds(Sets.newHashSet(org.getId()));
                leaderPermissionInfo.setPermissionUniqueCodes(Sets.newHashSet(uniqueCode));
                return leaderPermissionInfo;
            }
        }
        //非顾问角色
        Set<Long> permissionOrgIds = Sets.newHashSet();
        List<Long> orgIds = relationDOList.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList());
        for (Long orgId : orgIds) {
            permissionOrgIds.addAll(organizationService.listAllSubOrgIncludeSelf(orgId).stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toSet()));
        }
        Set<String> uniqueCodes = organizationRelationService
                .listByOrgIdsFromDB(Lists.newArrayList(permissionOrgIds))
                .stream()
                .map(BdCrmOrgUserRelationDO::getUniqueCode)
                .collect(Collectors.toSet());
        leaderPermissionInfo.setPermissionOrgIds(permissionOrgIds);
        leaderPermissionInfo.setPermissionUniqueCodes(uniqueCodes);
        return leaderPermissionInfo;
    }


    @Override
    public List<BdCrmOrganizationDO> listShortestPath(List<BdCrmOrganizationDO> organizationDoList) {
        return listShortestPathInner(organizationDoList.stream().map(BdCrmOrganizationDO::getId).collect(Collectors.toList()));
    }

    @Override
    public CrowdfundingVolunteer getVolunteerByPartitionName(String partitionName) {
        // 根据分区名称找到对应分区 只找直营城市
        List<BdCrmOrganizationDO> bdCrmOrganizationDOS = organizationService.listNotTestOrg();
        BdCrmOrganizationDO bdCrmOrganizationDO = bdCrmOrganizationDOS.stream().filter(item -> Objects.equals(item.getOrgName(), "直营城市")).findFirst().orElse(null);
        if (Objects.isNull(bdCrmOrganizationDO)) {
            return null;
        }
        // 找直营城市下对应的分区
        List<BdCrmOrganizationDO> subOrgIncludeSelfList = organizationService.listAllSubOrgIncludeSelf(bdCrmOrganizationDO.getId());
        BdCrmOrganizationDO partitionOrganization = subOrgIncludeSelfList.stream().filter(item -> item.getOrgName().equals(partitionName)).findFirst().orElse(null);
        if (Objects.isNull(partitionOrganization)) {
            return null;
        }
        // 根据组织人员关系找对应的分区经理
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = organizationRelationService.listRelationByOrgId(partitionOrganization.getId());
        if (CollectionUtils.isEmpty(bdCrmOrgUserRelationDOS)) {
            return null;
        }
        return cfVolunteerServiceImpl.getByUniqueCode(bdCrmOrgUserRelationDOS.get(0).getUniqueCode());
    }

    @Override
    public boolean isGreyCity(List<String> cityList, String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return false;
        }
        if (CollectionUtils.isEmpty(cityList)) {
            return false;
        }
        List<Long> orgIds = organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode)
                .stream()
                .map(BdCrmOrgUserRelationDO::getOrgId)
                .collect(Collectors.toList());
        List<BdCrmOrganizationDO> allSubOrgIds = Lists.newArrayList();
        orgIds.forEach(item -> allSubOrgIds.addAll(organizationService.listAllSubOrgIncludeSelf(item)));
        //如果是预绑定的组织
        if (CollectionUtils.isEmpty(allSubOrgIds)) {
            PreVolunteerOrgInfoRelationDO relationDO = preVolunteerOrgInfoRelationDao.getPreVolunteerOrgInfoRelationDO(uniqueCode);
            BdCrmOrganizationDO org = null;
            if (relationDO != null) {
                org = organizationService.getCurrentOrgById(relationDO.getOrgId());
            }
            if (org != null) {
                allSubOrgIds.add(org);
            }
        }
        return allSubOrgIds.stream().anyMatch(item -> cityList.contains(item.getCityName()));
    }

    @Override
    public boolean isGreyPartition(List<Long> partitionList, String uniqueCode) {

        if (StringUtils.isBlank(uniqueCode) || CollectionUtils.isEmpty(partitionList)) {
            return false;
        }

        // 获取用户所在组织的ID集合
        Set<Long> userOrgIds = organizationRelationService.listMemberOrgRelationByUniqueCode(uniqueCode)
                .stream()
                .map(BdCrmOrgUserRelationDO::getOrgId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(userOrgIds)) {
            return false;
        }

        // 检查Apollo配置的每个分区是否包含用户所在的组织
        for (Long partitionId : partitionList) {
            Set<Long> crmOrgIds = organizationService.listAllSubOrgIncludeSelf(partitionId)
                    .stream()
                    .map(BdCrmOrganizationDO::getId)
                    .collect(Collectors.toSet());
            // 如果找到匹配的组织，命中
            if (!CollectionUtils.isEmpty(crmOrgIds) && !Collections.disjoint(userOrgIds, crmOrgIds)) {
                return true;
            }
        }
        // 如果没有找到匹配的组织，未命中
        return false;
    }


    /**
     * 根据orgIds列出每个组织路径中最短的
     * examples: 入参("华北区", 华北区-山西", "华南区-海南") 返回的是（"华北区", "华南区-海南"）的组织数据
     */
    private List<BdCrmOrganizationDO> listShortestPathInner(List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Lists.newArrayList();
        }
        String splitter = "-";
        if (orgIds.size() == 1) {
            BdCrmOrganizationDO currentOrgById = organizationService.getCurrentOrgById(orgIds.get(0));
            if (currentOrgById == null) {
                return Lists.newArrayList();
            }
            Map<Long, String> orgIdsWithPath = organizationService.listChainByOrgIds(orgIds, splitter);
            currentOrgById.setOrgPath(orgIdsWithPath.get(currentOrgById.getId()));
            return Lists.newArrayList(currentOrgById);
        }
        List<Long> shortestOrgIds = Lists.newArrayList();
        Map<Long, String> orgIdsWithPath = organizationService.listChainByOrgIds(orgIds, splitter);
        Collection<String> values = orgIdsWithPath.values();
        for (Map.Entry<Long, String> orgIdPath : orgIdsWithPath.entrySet()) {
            String path = orgIdPath.getValue();
            int level = path.split(splitter).length;
            //怎么定义最短,除了自己本身外其他的都不包含这个组织
            boolean shortestPath = values.stream()
                    .filter(item -> !item.equals(path))
                    .filter(item -> item.split(splitter).length != level)
                    .noneMatch(path::contains);//防止同层级出现包含的情况
            if (shortestPath) {
                shortestOrgIds.add(orgIdPath.getKey());
            }
        }
        log.debug("最短组织id:{}", shortestOrgIds);
        return organizationService.listOrgInfo(shortestOrgIds).values()
                .stream()
                .peek(item -> item.setOrgPath(orgIdsWithPath.get(item.getId())))
                .collect(Collectors.toList());
    }


    //查找能处理的人员 本人 -> 离职前的上级
    @Override
    public CrowdfundingVolunteer getCanHandlerVolunteer(String uniqueCode) {
        CrowdfundingVolunteer volunteer = volunteerService.getByUniqueCode(uniqueCode);
        if (volunteer.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue()) {
            BdCrmOrgUserRelationDO relationDO = organizationRelationService.listMemberOrgRelationNoMatterDelete(volunteer.getUniqueCode())
                    .stream()
                    .max(Ordering.natural().onResultOf(BdCrmOrgUserRelationDO::getId))
                    .orElse(null);
            if (relationDO != null) {
                long orgId = relationDO.getOrgId();
                BdCrmOrganizationDO organizationDO = organizationService.getCurrentOrgById(orgId);
                if (organizationDO != null && organizationDO.getParentId() > 0) {
                    List<BdCrmOrgUserRelationDO> relationDOList = organizationRelationService.listRelationByOrgId(organizationDO.getParentId());
                    if (CollectionUtils.isNotEmpty(relationDOList)) {
                        String leaderUniqueCode = relationDOList.get(0).getUniqueCode();
                        volunteer = volunteerService.getByUniqueCode(leaderUniqueCode);
                    }
                }
            }
        }
        return volunteer;
    }

    @Override
    public List<PepRealTimeLeaderInfo> getRealTimeLeader(String uniqueCode, List<Integer> expectRoles) {
        //获取所有需要获取的职级
        CrowdfundingVolunteer volunteer = volunteerService.getByUniqueCode(uniqueCode);
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOS = listNotInTestRelation(uniqueCode);
        if (CollectionUtils.isEmpty(bdCrmOrgUserRelationDOS)) {
            return Lists.newArrayList();
        }
        //往上找到所有的组织链路
        BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO = bdCrmOrgUserRelationDOS.get(0);
        List<BdCrmOrganizationDO> bdCrmOrganizationDOS = organizationService.listParentOrgAsChain(bdCrmOrgUserRelationDO.getOrgId());
        List<Long> orgIds = bdCrmOrganizationDOS.stream()
                .map(BdCrmOrganizationDO::getId)
                .filter(item -> !Objects.equals(item, bdCrmOrgUserRelationDO.getOrgId()))
                .collect(Collectors.toList());
        List<PepRealTimeLeaderInfo> result = Lists.newArrayList();
        for (Long orgId : orgIds) {
            //找人员,只需要找这么几种角色业务经理、分区经理、省区经理，区域经理
            List<BdCrmOrgUserRelationDO> relationList = organizationRelationService.listRelationByOrgId(orgId);
            List<String> uniqueCodeList = relationList.stream()
                    .map(BdCrmOrgUserRelationDO::getUniqueCode)
                    .collect(Collectors.toList());
            List<String> containsUserIds = result.stream()
                    .map(PepRealTimeLeaderInfo::getLeaderUserId)
                    .collect(Collectors.toList());
            List<CrowdfundingVolunteer> volunteerList = volunteerService.getCfVolunteerDOByUniqueCodes(uniqueCodeList)
                    .stream()
                    .filter(item -> expectRoles.contains(item.getLevel()) && !Objects.equals(volunteer.getLevel(), item.getLevel()))
                    .filter(item -> !Objects.equals(volunteer.getUniqueCode(), item.getUniqueCode()))
                    .filter(item -> !containsUserIds.contains(item.getUniqueCode()))
                    .collect(Collectors.toList());
            Map<Long, String> longStringMap = organizationService.listChainByOrgIdsWithDefaultSplitter(Lists.newArrayList(orgId));
            if (volunteerList.size() > 1 && applicationService.isProduction()) {
                List<String> volunteerNames = volunteerList.stream()
                        .map(CrowdfundingVolunteer::getVolunteerName)
                        .collect(Collectors.toList());
                //发送报警信息
                //AlarmBotService.sentText(GeneralConstant.RECRUIT_LEADER_ALARM, "当前顾问:" + volunteer.getVolunteerName() + "存在多个上级" +
                //        volunteerNames
                //        + ",请核对使用哪个上级计算招募线索发送奖励", null, null);
            }
            if (CollectionUtils.isNotEmpty(volunteerList)) {
                CrowdfundingVolunteer crowdfundingVolunteer = volunteerList.get(0);
                PepRealTimeLeaderInfo pepRealTimeLeaderInfo = new PepRealTimeLeaderInfo();
                pepRealTimeLeaderInfo.setLeaderUserId(crowdfundingVolunteer.getUniqueCode());
                pepRealTimeLeaderInfo.setLeaderRoleLevel(crowdfundingVolunteer.getLevel());
                pepRealTimeLeaderInfo.setUserName(crowdfundingVolunteer.getVolunteerName());
                pepRealTimeLeaderInfo.setOrgId(orgId);
                pepRealTimeLeaderInfo.setRecordTime(DateTime.now().toString(GrowthtoolUtil.ymdfmt));
                pepRealTimeLeaderInfo.setOrgPath(longStringMap.getOrDefault(orgId, ""));
                result.add(pepRealTimeLeaderInfo);
            }
        }
        return result;
    }


    /**
     * 为患者平台查找对应线下人员
     * 规则，如下
     * 1.如果是在岗、二维码未被冻结、非物料的员工记录直接返回人员
     * 2.物料走物料逻辑：通过城市或者省份找对应员工，如果找不到使用兜底逻辑
     * 3.离职或者冻结：判断是否是二次入职，按照城市和省份找对应员工
     * 4.二次入职：身份证是否相同
     * 5.兜底逻辑：随机找一个符合的
     */
    @Override
    public Pair<CrowdfundingVolunteer, String> getVolunteerForPatientPt(PatientHandlerModel patientHandlerModel) {
        if (patientHandlerModel == null) {
            return null;
        }
        String carteTag = "gzt-sdhzfw";
        List<String> limitCitys = Lists.newArrayList();
        CfRoleCarteConfigModel cfRoleCarteConfigModel = roleCarteService.getByCarteUniqueCode(carteTag);
        if (cfRoleCarteConfigModel != null && Objects.equals(cfRoleCarteConfigModel.getIsVisibleByCity(), 1)) {
            limitCitys = Splitter.on(",").splitToList(cfRoleCarteConfigModel.getVisibleCityName());
        }
        CrowdfundingVolunteer cfVolunteer = volunteerService.getVolunteerByUniqueCode(patientHandlerModel.getUniqueCode());

        if (cfVolunteer == null) {
            return getByCityAndProvince(patientHandlerModel.getProvinceName(), patientHandlerModel.getCityName(), limitCitys);
        }
        //是否是统一物料账号
        boolean isUnitaryCount = Objects.equals(cfVolunteer.getAccountType(), com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingVolunteerEnum.AccountTypeEnum.UNITARY_ACCOUNT.getValue());
        if (isUnitaryCount) {
            return getByCityAndProvince(patientHandlerModel.getProvinceName(), patientHandlerModel.getCityName(), limitCitys);
        }
        //离职员工,需要找到新的账号
        if (cfVolunteer.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue()) {
            cfVolunteer = volunteerService.listVolunteerByIdCardListOfIgnoreWorkStatus(Lists.newArrayList(cfVolunteer.getIdCardNumber()))
                    .stream()
                    .filter(item -> item.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.ON_THE_JOB.getValue() &&
                            item.getAccountType() == com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingVolunteerEnum.AccountTypeEnum.INDIVIDUAL.getValue())
                    .min(Ordering.natural().reverse().onResultOf(CrowdfundingVolunteer::getId))
                    .orElse(cfVolunteer);
        }
        //离职或者冻结
        boolean notNormalStatus = cfVolunteer.getQrCodeStatus() == 1 || cfVolunteer.getWorkStatus() == CrowdfundingVolunteerEnum.WorkStatusEnum.DIMISSION.getValue();
        //是否在组织上,未在组织上
        List<BdCrmOrgUserRelationDO> relationDOList = organizationRelationService.listMemberOrgRelationByUniqueCode(cfVolunteer.getUniqueCode());
        notNormalStatus = notNormalStatus || CollectionUtils.isEmpty(relationDOList);
        if (notNormalStatus) {
            return getByCityAndProvince(patientHandlerModel.getProvinceName(), patientHandlerModel.getCityName(), limitCitys);
        }
        return Pair.of(cfVolunteer, "");
    }


    /**
     * 查找兜底方案
     */
    private Pair<CrowdfundingVolunteer, String> getByCityAndProvince(String province, String cityName, List<String> limitCitys) {
        Predicate<CrowdfundingCity> limitCityPredicate = city -> {
            if (city == null) {
                return false;
            }
            return CollectionUtils.isEmpty(limitCitys) || limitCitys.contains(city.getName()) ||
                    city.containNewCityNames(limitCitys);
        };
        CrowdfundingCity crowdfundingCity = null;

        //找到一个有效的城市
        if (StringUtils.isBlank(cityName) && StringUtils.isNotBlank(province)) {
            Optional<CrowdfundingCity> provinceOpt = cfCrowdfundingCityDelegate.getProvince().stream()
                    .filter(item -> Objects.equals(item.getName(), province))
                    .findFirst();
            if (provinceOpt.isPresent()) {
                crowdfundingCity = cfCrowdfundingCityDelegate.getChildren(provinceOpt.get().getId())
                        .stream()
                        .filter(limitCityPredicate)
                        .findFirst()
                        .orElse(null);
                cityName = Optional.ofNullable(crowdfundingCity).map(CrowdfundingCity::getName).orElse("");
            }
        }

        if (StringUtils.isNotBlank(cityName) && limitCityPredicate.test(crowdfundingCity)) {
            List<CrowdfundingVolunteer> volunteers = listLeaderByCity(cityName, CrowdfundingVolunteerEnum.clewProvinceRoles);
            if (CollectionUtils.isEmpty(volunteers)) {
                return Pair.of(getRandomVolunteer(CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel(), limitCitys), "兜底,随机从业务经理和高级渠道经理中找一个");
            }
            return Pair.of(volunteers.get(new Random().nextInt(volunteers.size())), "从城市" + cityName +"中找一个上级");
        } else {
            //直接使用业务经理兜底
            return Pair.of(getRandomVolunteer(CrowdfundingVolunteerEnum.RoleEnum.PROVINCE_LEADER.getLevel(), limitCitys), "兜底,随机从业务经理和高级渠道经理中找一个");
        }
    }


    public CrowdfundingVolunteer getRandomVolunteer(Integer expectRole, List<String> limitCitys) {
        //不限制城市
        if (CollectionUtils.isEmpty(limitCitys)) {
            List<CrowdfundingVolunteer> volunteers = volunteerService.getXianXiaVolunteerListByRole(expectRole);
            if (CollectionUtils.isEmpty(volunteers)) {
                return null;
            }
            List<String> inOrgUniqueCodes = organizationRelationService.listByUniqueCodes(volunteers.stream().map(CrowdfundingVolunteer::getUniqueCode).collect(Collectors.toList()))
                    .stream().map(BdCrmOrgUserRelationDO::getUniqueCode).collect(Collectors.toList());
            volunteers = volunteers.stream().filter(item -> inOrgUniqueCodes.contains(item.getUniqueCode()))
                    .collect(Collectors.toList());
            return volunteers.get(new Random().nextInt(volunteers.size()));
        }
        //限制城市,随机找一个
        String cityName = limitCitys.get(new Random().nextInt(limitCitys.size()));
        List<CrowdfundingVolunteer> volunteers = listLeaderByCity(cityName, CrowdfundingVolunteerEnum.clewProvinceRoles);
        if (CollectionUtils.isNotEmpty(volunteers)) {
            return volunteers.get(new Random().nextInt(volunteers.size()));
        }
        return null;
    }

    private Long dimissionUserInfo(String uniqueCode) {
        //离职 时 查询 bd_crm_org_user_relation 不加is_delete
        List<BdCrmOrgUserRelationDO> bdCrmOrgUserRelationDOList = organizationRelationService.listMemberOrgRelationNoMatterDelete(uniqueCode);
        if (CollectionUtils.isEmpty(bdCrmOrgUserRelationDOList)) {
            return null;
        }
        //获取id最大的一条记录
        Optional<BdCrmOrgUserRelationDO> optional = bdCrmOrgUserRelationDOList.stream().max(Comparator.comparing(BdCrmOrgUserRelationDO::getId));
        if (optional.isEmpty()) {
            return null;
        }
        BdCrmOrgUserRelationDO bdCrmOrgUserRelationDO = optional.get();

        //获取测试大区组织id
        List<Long> listAllTest = organizationService.listAllTestOrg();
        //离职顾问所在组织id不在测试大区组织id中，直接返回
        if (!listAllTest.contains(bdCrmOrgUserRelationDO.getOrgId())) {
            return bdCrmOrgUserRelationDO.getOrgId();
        }

        BdCrmOrganizationDO crmOrganizationDO = organizationService.getCurrentOrgById(bdCrmOrgUserRelationDO.getOrgId());
        //获取非测试大区组织
        List<BdCrmOrganizationDO> bdCrmOrganizationDOList = organizationService.listNotTestOrg();

        //非测试大区组织id，再获取离职顾问所在城市相同城市的组织id
        List<Long> orgIdList = bdCrmOrganizationDOList.stream().filter(v -> v.getCityId() == crmOrganizationDO.getCityId()).map(BdCrmOrganizationDO::getId).collect(Collectors.toList());
        //没有获取到组织，直接返回
        if (CollectionUtils.isEmpty(orgIdList)) {
            return null;
        }

        return orgIdList.get(0);
    }

}
