package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.BdQyWxMsgRecordDO;

import java.util.Date;
import java.util.List;

/**
 * 企业微信消息发送记录(BdQyWxMsgRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-11 11:33:18
 */
public interface BdQyWxMsgRecordService {

    int insert(BdQyWxMsgRecordDO bdQyWxMsgRecord);

    int update(BdQyWxMsgRecordDO bdQyWxMsgRecord);

    BdQyWxMsgRecordDO queryById(long id);

    List<BdQyWxMsgRecordDO> queryByCaseId(int caseId);

    List<BdQyWxMsgRecordDO> queryByCaseIdAndMsgType(int caseId, int msgType);

    BdQyWxMsgRecordDO queryByMsgId(String msgId);

    /**
     * 根据案例ID、消息类型和时间范围查询消息记录
     *
     * @param caseId    案例ID
     * @param msgType   消息类型
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 时间范围内的消息记录列表
     */
    List<BdQyWxMsgRecordDO> queryByCaseIdAndMsgTypeAndTimeRange(int caseId, int msgType, Date startTime, Date endTime);

    /**
     * 更新消息发送状态
     * @param sendStatus 发送状态
     */
    void updateSendStatus(String msgId, int sendStatus);

    void updateRealSendTime(long id, Date realSendTime);

    void updateShareContent(long id, String shareContent);

} 