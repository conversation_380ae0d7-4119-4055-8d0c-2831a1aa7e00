package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdtask;

import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICrowdFundingFeignDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdtask.CrmBdTaskDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdSubTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdtask.BdTaskContext;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfBdCaseInfoService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCaseBaseDataService;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdCaseInfoDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-09-20 14:09
 * 状态流转
 * 进行中 -> 完成
 * 进行中 -> 超时
 * 其他状态无法流转
 **/
@Slf4j
@Service
public class BdTaskFactory {

    @Autowired
    private List<IBdTaskCheckService> bdTaskCheckServices;
    private Map<CrmBdTaskDO.TaskTypeEnum, IBdTaskCheckService> bdTaskCheckServiceMap;

    @Autowired
    private ICrmBdTaskService crmBdTaskService;

    @Autowired
    private ICfBdCaseInfoService cfBdCaseInfoServiceImpl;

    @Autowired
    private ICrowdFundingFeignDelegate crowdFundingFeignDelegateImpl;

    @Autowired
    private ICfCaseBaseDataService cfCaseBaseDataService;


    @PostConstruct
    public void init() {
        bdTaskCheckServiceMap = bdTaskCheckServices.stream()
                .collect(Collectors.toMap(IBdTaskCheckService::getTaskType,
                        bdTaskStatusService -> bdTaskStatusService));
    }

    public void checkAndCreateTask(BdTaskContext bdTaskContext) {
        log.debug("checkAndCreateTask bdTaskContext:{}", bdTaskContext);
        boolean checkAndFillFiled = checkAndFillFiled(bdTaskContext);
        if (!checkAndFillFiled) {
            log.info("填充字段失败");
            return;
        }
        List<CrmBdTaskDO.TaskTypeEnum> taskTypeEnumList = bdTaskContext.getTaskTypeEnumList();
        for (CrmBdTaskDO.TaskTypeEnum taskTypeEnum : taskTypeEnumList) {
            bdTaskContext.setTaskTypeEnum(taskTypeEnum);
            checkAndCreateTaskInner(bdTaskContext);
        }
    }


    private boolean checkAndFillFiled(BdTaskContext bdTaskContext) {
        if (bdTaskContext.getCaseId() <= 0) {
            log.info("没有设置案例id");
            return false;
        }
        if (CollectionUtils.isEmpty(bdTaskContext.getTaskTypeEnumList())) {
            log.info("没有设置对应的任务类型");
            return false;
        }
        CrowdfundingInfo crowdfundingInfo = bdTaskContext.getCrowdfundingInfo();
        if (crowdfundingInfo == null) {
            crowdfundingInfo = crowdFundingFeignDelegateImpl.getCaseInfoById(bdTaskContext.getCaseId());
            if (crowdfundingInfo == null) {
                return false;
            }
            bdTaskContext.setCrowdfundingInfo(crowdfundingInfo);
        }
        CfBdCaseInfoDo cfBdCaseInfoDo = bdTaskContext.getCfBdCaseInfoDo();
        if (cfBdCaseInfoDo == null) {
            cfBdCaseInfoDo = cfBdCaseInfoServiceImpl.getBdCaseInfoByInfoId(bdTaskContext.getCaseId());
            if (cfBdCaseInfoDo == null) {
                log.info("不是线下案例，无法生成任务");
                return false;
            }
            bdTaskContext.setCfBdCaseInfoDo(cfBdCaseInfoDo);
        }
        if (bdTaskContext.getTaskTypeEnumList().contains(CrmBdTaskDO.TaskTypeEnum.case_pic) ||
                bdTaskContext.getTaskTypeEnumList().contains(CrmBdTaskDO.TaskTypeEnum.case_content)) {
            bdTaskContext.setCfCaseBaseDataDo(cfCaseBaseDataService.getByCaseId(bdTaskContext.getCaseId()));
        }
        bdTaskContext.setUniqueCode(cfBdCaseInfoDo.getUniqueCode());
        return true;
    }


    public void updateBdTaskWhenComplete(BdTaskContext bdTaskContext) {
        log.debug("updateBdTaskWhenComplete bdTaskContext:{}", bdTaskContext);
        boolean checkAndFillFiled = checkAndFillFiled(bdTaskContext);
        if (!checkAndFillFiled) {
            log.info("填充字段失败");
            return;
        }
        List<CrmBdTaskDO.TaskTypeEnum> taskTypeEnumList = bdTaskContext.getTaskTypeEnumList();
        for (CrmBdTaskDO.TaskTypeEnum taskTypeEnum : taskTypeEnumList) {
            bdTaskContext.setTaskTypeEnum(taskTypeEnum);
            updateBdTaskWhenCompleteInner(bdTaskContext);
        }
    }


    public void updateBdTaskWhenOverTime(BdTaskContext bdTaskContext) {
        log.debug("updateBdTaskWhenOverTime bdTaskContext:{}", bdTaskContext);
        boolean checkAndFillFiled = checkAndFillFiled(bdTaskContext);
        if (!checkAndFillFiled) {
            log.info("填充字段失败");
            return;
        }
        List<CrmBdTaskDO.TaskTypeEnum> taskTypeEnumList = bdTaskContext.getTaskTypeEnumList();
        for (CrmBdTaskDO.TaskTypeEnum taskTypeEnum : taskTypeEnumList) {
            bdTaskContext.setTaskTypeEnum(taskTypeEnum);
            updateBdTaskWhenOverTimeInner(bdTaskContext);
        }
    }


    private void checkAndCreateTaskInner(BdTaskContext bdTaskContext) {
        CrmBdTaskDO.TaskTypeEnum taskTypeEnum = bdTaskContext.getTaskTypeEnum();
        IBdTaskCheckService bdTaskCheckService = getBdTaskCheckService(taskTypeEnum);
        if (bdTaskCheckService == null) {
            return;
        }
        boolean needCreateTask = bdTaskCheckService.checkNeedCreateTask(bdTaskContext);
        if (!needCreateTask) {
            log.info("无需生成任务");
            return;
        }
        //如果已经存在了就不需要再次生成
        CrmBdTaskDO crmBdTaskDOFromDB = crmBdTaskService.getByTaskTypeAndCaseId(taskTypeEnum.getCode(), bdTaskContext.getCaseId());
        if (crmBdTaskDOFromDB != null) {
            log.info("当前任务不存在,任务类型:{}", taskTypeEnum);
            return;
        }
        CfBdCaseInfoDo cfBdCaseInfoDo = bdTaskContext.getCfBdCaseInfoDo();
        CrmBdTaskDO crmBdTaskDO = new CrmBdTaskDO();
        crmBdTaskDO.setTaskType(taskTypeEnum.getCode());
        crmBdTaskDO.setTaskStatus(CrmBdTaskDO.TaskStatusEnum.start.getCode());
        crmBdTaskDO.setCaseId(bdTaskContext.getCaseId());
        crmBdTaskDO.setUniqueCode(cfBdCaseInfoDo.getUniqueCode());
        crmBdTaskDO.setOrgId(cfBdCaseInfoDo.getOrgId());
        crmBdTaskDO.setOrgPath(cfBdCaseInfoDo.getOrgPath());
        int res = crmBdTaskService.insert(crmBdTaskDO);
        if (res > 0) {
            //父任务创建成功后，创建子任务
            BdSubTaskContext bdSubTaskContext = BdSubTaskContext.builder()
                    .caseId(bdTaskContext.getCaseId())
                    .parentTaskId(crmBdTaskDO.getId())
                    .cfBdCaseInfoDo(cfBdCaseInfoDo)
                    .build();
            bdTaskCheckService.createSubTask(bdSubTaskContext);
        }
    }


    private void updateBdTaskWhenCompleteInner(BdTaskContext bdTaskContext) {
        CrmBdTaskDO.TaskTypeEnum taskTypeEnum = bdTaskContext.getTaskTypeEnum();
        IBdTaskCheckService bdTaskCheckService = getBdTaskCheckService(taskTypeEnum);
        if (bdTaskCheckService == null) {
            return;
        }
        //查找当前类型的任务是否存在
        CrmBdTaskDO crmBdTaskDO = crmBdTaskService.getByTaskTypeAndCaseId(taskTypeEnum.getCode(), bdTaskContext.getCaseId());
        if (crmBdTaskDO == null) {
            log.info("当前任务不存在,任务类型:{}", taskTypeEnum);
            return;
        }
        if (!Objects.equals(CrmBdTaskDO.TaskStatusEnum.start.getCode(), crmBdTaskDO.getTaskStatus())) {
            log.info("非进行中状态无法修改为已完成,任务类型:{}", taskTypeEnum);
            return;
        }
        boolean needCreateTask = bdTaskCheckService.checkTaskComplete(bdTaskContext);
        if (!needCreateTask) {
            log.info("无需更新任务");
            return;
        }
        crmBdTaskService.updateWhenComplete(crmBdTaskDO.getId());
    }


    private void updateBdTaskWhenOverTimeInner(BdTaskContext bdTaskContext) {
        CrmBdTaskDO.TaskTypeEnum taskTypeEnum = bdTaskContext.getTaskTypeEnum();
        IBdTaskCheckService bdTaskCheckService = getBdTaskCheckService(taskTypeEnum);
        if (bdTaskCheckService == null) {
            return;
        }
        //查找当前类型的任务是否存在
        CrmBdTaskDO crmBdTaskDO = crmBdTaskService.getByTaskTypeAndCaseId(taskTypeEnum.getCode(), bdTaskContext.getCaseId());
        if (crmBdTaskDO == null) {
            log.info("当前任务不存在,任务类型:{}", taskTypeEnum);
            return;
        }
        //非进行中的任务不能修改为超时
        if (!Objects.equals(CrmBdTaskDO.TaskStatusEnum.start.getCode(), crmBdTaskDO.getTaskStatus())) {
            log.info("当前任务非进行中,任务类型:{}", taskTypeEnum);
            return;
        }
        crmBdTaskService.updateWhenOverTime(crmBdTaskDO.getId());
    }


    @Nullable
    private IBdTaskCheckService getBdTaskCheckService(CrmBdTaskDO.TaskTypeEnum taskTypeEnum) {
        if (taskTypeEnum == null) {
            log.info("缺少关键参数");
            return null;
        }
        IBdTaskCheckService bdTaskStatusService = bdTaskCheckServiceMap.get(taskTypeEnum);
        if (bdTaskStatusService == null) {
            log.info("当前类型不支持");
            return null;
        }
        return bdTaskStatusService;
    }

}
