package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.qywx.QywxGroupAiShareContentDO;

import java.util.List;

/**
 * ai生成企微转发语信息(QywxGroupAiShareContent)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-13 11:52:39
 */
public interface QywxGroupAiShareContentService {

    QywxGroupAiShareContentDO queryById(long id);

    List<QywxGroupAiShareContentDO> queryByMsgId(long msgId);

    int insert(QywxGroupAiShareContentDO qywxGroupAiShareContent);

    boolean deleteById(long id);

    void bindMsgId(long id, long msgId);

    void fillShareContent(QywxGroupAiShareContentDO aiShareContent);
}
