package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.KpiImportDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.IKpiImportDataService;
import com.shuidihuzhu.cf.dao.bdkpi.KpiImportDataDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 筹绩效导入数据(KpiImportData)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-29 10:49:09
 */
@Service("kpiImportDataService")
public class KpiImportDataServiceImpl implements IKpiImportDataService {
   
    @Resource
    private KpiImportDataDao kpiImportDataDao;

    @Override
    public KpiImportDataDO queryById(long id) {
        return kpiImportDataDao.queryById(id);
    }

    @Override
    public void batchInsert(List<KpiImportDataDO> importDataList, int importType) {
        if (CollectionUtils.isEmpty(importDataList)) {
            return;
        }
        String monthKey = importDataList.get(0).getMonthKey();
        List<KpiImportDataDO> importDataByMonth = kpiImportDataDao.queryMonthKey(monthKey, importType);
        Set<String> uniqueCodeSet = importDataByMonth.stream()
                .map(KpiImportDataDO::getUniqueKey)
                .collect(Collectors.toSet());
        List<KpiImportDataDO> filterImportDataList = importDataList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getUniqueKey()))
                .collect(Collectors.toList());
        for (KpiImportDataDO kpiImportDataDO : filterImportDataList) {
            if (!uniqueCodeSet.contains(kpiImportDataDO.getUniqueKey())) {
                kpiImportDataDao.insert(kpiImportDataDO);
            }
            uniqueCodeSet.add(kpiImportDataDO.getUniqueKey());
        }
    }

    @Override
    public int update(KpiImportDataDO kpiImportData) {
        return kpiImportDataDao.update(kpiImportData);
    }

    @Override
    public boolean deleteById(long id) {
        return kpiImportDataDao.deleteById(id) > 0;
    }

    @Override
    public int countAll(int importType, Date beginTime) {
        return kpiImportDataDao.countAll(importType, beginTime);
    }

    @Override
    public List<KpiImportDataDO> pageList(int importType, Date beginTime, int offset, int limit) {
        return kpiImportDataDao.pageList(importType, beginTime, offset, limit);
    }

    @Override
    public List<KpiImportDataDO> listByMonthKey(String monthKey, int importType) {
        return kpiImportDataDao.listByMonthKey(monthKey, importType);
    }
}
