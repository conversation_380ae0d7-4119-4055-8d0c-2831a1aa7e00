package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgAndCityRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmDiagnoseDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.SdCityHierarchyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseBaseDataModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmDiagnoseForCalcModel;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: wanghui
 * @create: 2021/5/7 下午4:55
 */
@Slf4j
public abstract class AbstractBdCrmDiagnosePrepareDataService extends AbstractBdCrmDiagnoseService{
    @Autowired
    protected CfBdCrmDiagnoseDataService cfBdCrmDiagnoseDataService;
    @Resource(name = "cfOlapCaseStat")
    private RedissonHandler redissonHandler;

    protected Map<Long, Map<Long, Double>> provinceIdMapContribution=null; // 省份下各个城市的贡献度
    protected Map<Long, Map<Integer, Double>> cityIdMapOnlineWithOfflineContribution=null; // 城市下线上以及线下的贡献度
    protected Map<Long, Map<Integer, Double>> orgIdMapOnlineWithOfflineContribution=null; // 城市下线上以及线下的贡献度

    private void resetCache() {
        provinceIdMapContribution=null; // 省份下各个城市的贡献度
        cityIdMapOnlineWithOfflineContribution=null; // 城市下线上以及线下的贡献度
        orgIdMapOnlineWithOfflineContribution=null; // 城市下线上以及线下的贡献度
    }
    /**
     * 预处理 昨天的数据 到 cf_bd_crm_diagnose_data表中
     * @param calcModel
     * @return succ 时 data为1表示正在 处理数据中
     */
    public OpResult<Integer> preprocessBefore1DayData(CfBdCrmDiagnoseForCalcModel calcModel) {
        if (calcModel ==null) return OpResult.createFailResult(CfGrowthtoolErrorCode.OPERATOR_FAIL);
        String redisKey = String.format("growthtool_preprocessdiagnose_%s_%s_%s_%s",calcModel.getCurDateTime(), calcModel.getCurDayRange(), calcModel.getPreDateTime(), calcModel.getPreDayRange());
        if (!redissonHandler.setNX(redisKey, 1, RedissonHandler.ONE_HOUR)) { // 防并发
            return OpResult.createSucResult(1);
        }
        this.calcModel = calcModel;
        resetCache();
        // 计算全国
        CfBdCrmDiagnoseDataDO chinaBdCrmDiagnoseData = calcCfBdCrmDiagnoseDataForSelf(null);
        List<CfBdCrmDiagnoseDataDO> list = Lists.newArrayList(chinaBdCrmDiagnoseData);

        for (BdCrmOrganizationDO organizationDO: this.calcModel.getAllAreaOrgList()){
            CfBdCrmDiagnoseDataDO cfBdCrmDiagnoseDataDO = calcCfBdCrmDiagnoseDataForSelf(organizationDO);
            list.add(cfBdCrmDiagnoseDataDO);
        }
        list = calcSdUpDownContributionForQuanGuo(list);
        cfBdCrmDiagnoseDataService.batchInsertOrUpdate(list);
        // 单独计算下 南大区、北大区 除了贡献度外 其他指标的数据
        for (BdCrmOrganizationDO organizationDO : this.calcModel.getBigAreaOrgList()) {
            CfBdCrmDiagnoseDataDO cfBdCrmDiagnoseDataDO = calcCfBdCrmDiagnoseDataForSelf(organizationDO);
            //  二期 这个地方改为 根据组织id 获取orgLevel
            cfBdCrmDiagnoseDataDO.setOrgLevel(organizationDO.getOrgLevel());
            cfBdCrmDiagnoseDataService.batchInsertOrUpdate(Lists.newArrayList(cfBdCrmDiagnoseDataDO));
        }
        // 计算各个区域
        for (BdCrmOrganizationDO organizationDO: this.calcModel.getAllAreaOrgWithSubOrgList()){
            CfBdCrmDiagnoseDataDO cfBdCrmDiagnoseDataDO = calcCfBdCrmDiagnoseDataForSelf(organizationDO);
            list = calcCfBdCrmDiagnoseData4ProvinceAndCity(organizationDO);
            list.add(cfBdCrmDiagnoseDataDO);
            list = calcSdUpDownContribution(list, organizationDO);
            cfBdCrmDiagnoseDataService.batchInsertOrUpdate(list);
        }
        redissonHandler.del(redisKey);//  跑完即释放
        return OpResult.createSucResult(0);
    }

    private CfBdCrmDiagnoseDataDO calcCfBdCrmDiagnoseDataForSelf(BdCrmOrganizationDO organizationDO) {
        List<BdCrmOrgAndCityRelationDO> chinaCityList;
        CfBdCrmDiagnoseDataDO cfBdCrmDiagnose = new CfBdCrmDiagnoseDataDO();
        cfBdCrmDiagnose.setDateTime(calcModel.getCurDateTime());
        cfBdCrmDiagnose.setCurDayRange(calcModel.getCurDayRange());
        cfBdCrmDiagnose.setPreDateTime(calcModel.getPreDateTime());
        cfBdCrmDiagnose.setPreDayRange(calcModel.getPreDayRange());
        //组织为null,说明是全国
        if (Objects.isNull(organizationDO)){
            cfBdCrmDiagnose.setOrgId((long) GeneralConstant.CRM_ORG_ID);
            cfBdCrmDiagnose.setOrgName("全国");
            cfBdCrmDiagnose.setCityName("");
            //  二期 这个地方改为 根据组织id 获取orgLevel
            cfBdCrmDiagnose.setOrgLevel(CrowdfundingVolunteerEnum.RoleEnum.SUPER_LEADER.getLevel());
            chinaCityList = Optional.ofNullable(calcModel.getOrgIdMapOrgAndCityRelationList().get((long)GeneralConstant.CRM_ORG_ID)).orElse(Lists.newArrayList());
        }else{
            cfBdCrmDiagnose.setOrgId(organizationDO.getId());
            cfBdCrmDiagnose.setOrgName(organizationDO.getOrgName());
            //  二期 这个地方改为 根据组织id 获取orgLevel
            cfBdCrmDiagnose.setOrgLevel(organizationDO.getOrgLevel());
            chinaCityList = Optional.ofNullable(calcModel.getOrgIdMapOrgAndCityRelationList().get(organizationDO.getId())).orElse(Lists.newArrayList());
        }
        CfBdCrmDiagnoseBaseDataModel curBaseDataModel = this.aggregateData(calcModel.getCurDateTime(), chinaCityList);
        CfBdCrmDiagnoseBaseDataModel preBaseDataModel = this.aggregateData(calcModel.getPreDateTime(), chinaCityList);
        cfBdCrmDiagnose.calcRationExcludeSdUpDownContribution(curBaseDataModel,preBaseDataModel);
        return cfBdCrmDiagnose;
    }

    private List<CfBdCrmDiagnoseDataDO> calcCfBdCrmDiagnoseData4ProvinceAndCity(BdCrmOrganizationDO organizationDO){
        Map<Long,CfBdCrmDiagnoseBaseDataModel> curProvinceData = calcModel.getCurProvinceData();
        Map<Long,CfBdCrmDiagnoseBaseDataModel> preProvinceData = calcModel.getPreProvinceData();
        Map<Long,CfBdCrmDiagnoseBaseDataModel> curCityData = calcModel.getCurCityData();
        Map<Long,CfBdCrmDiagnoseBaseDataModel> preCityData = calcModel.getPreCityData();
        List<CfBdCrmDiagnoseDataDO> list = Lists.newArrayList();
        for (BdCrmOrgAndCityRelationDO cityRelationDO : Optional.ofNullable(calcModel.getOrgIdMapOrgAndCityRelationList().get(organizationDO.getId())).orElse(Lists.newArrayList())){
            //province
            if (cityRelationDO.getBindType() == 0){
                CfBdCrmDiagnoseDataDO cfBdCrmDiagnose = new CfBdCrmDiagnoseDataDO();
                cfBdCrmDiagnose.setOrgId(organizationDO.getId());
                cfBdCrmDiagnose.setOrgName(organizationDO.getOrgName());
                cfBdCrmDiagnose.setDateTime(calcModel.getCurDateTime());
                cfBdCrmDiagnose.setCurDayRange(calcModel.getCurDayRange());
                cfBdCrmDiagnose.setPreDateTime(calcModel.getPreDateTime());
                cfBdCrmDiagnose.setPreDayRange(calcModel.getPreDayRange());
                cfBdCrmDiagnose.setOrgLevel(organizationDO.getOrgLevel());
                CfBdCrmDiagnoseBaseDataModel curBaseDataModel = curProvinceData.get((long) cityRelationDO.getCityId());
                CfBdCrmDiagnoseBaseDataModel preBaseDataModel = preProvinceData.get((long) cityRelationDO.getCityId());
                cfBdCrmDiagnose.calcRationExcludeSdUpDownContribution(curBaseDataModel,preBaseDataModel);
                cfBdCrmDiagnose.setCityName(cityRelationDO.getCityName());
                cfBdCrmDiagnose.setCityId((long) cityRelationDO.getCityId());
                cfBdCrmDiagnose.setCityType(2); // cityType 默认0 城市1 省份2
                list.add(cfBdCrmDiagnose);
                List<SdCityHierarchyDO> cityList = Optional.ofNullable(calcModel.getCityListByProvinceId(cityRelationDO.getCityId())).orElse(Lists.newArrayList());
                for (SdCityHierarchyDO sdCityHierarchyDO : cityList){
                    CfBdCrmDiagnoseDataDO cfBdCrmDiagnoseCity = this.getCfCityBdCrmDiagnoseDataDO(organizationDO, curCityData, preCityData, sdCityHierarchyDO.getCityId(), sdCityHierarchyDO.getCityName());
                    cfBdCrmDiagnoseCity.setCityLevel(Optional.ofNullable(SdCityHierarchyDO.CityLevelEnum.parseByDesc(sdCityHierarchyDO.getCityLevelSd())).map(SdCityHierarchyDO.CityLevelEnum::getCityLevel).orElse(0)); // cityType 默认0 城市1 省份2
                    list.add(cfBdCrmDiagnoseCity);
                }
            }
            //city
            if (cityRelationDO.getBindType() == 1){
                CfBdCrmDiagnoseDataDO cfBdCrmDiagnoseCity = this.getCfCityBdCrmDiagnoseDataDO(organizationDO, curCityData, preCityData, cityRelationDO.getCityId(), cityRelationDO.getCityName());
                cfBdCrmDiagnoseCity.setCityLevel(Optional.ofNullable(calcModel.getCityListByCityId(cityRelationDO.getCityId()))
                        .map(item -> Optional.ofNullable(SdCityHierarchyDO.CityLevelEnum.parseByDesc(item.getCityLevelSd())).map(SdCityHierarchyDO.CityLevelEnum::getCityLevel).orElse(0)).orElse(0)); // cityType 默认0 城市1 省份2
                list.add(cfBdCrmDiagnoseCity);
            }
        }
        return list;
    }

    /**
     * 获取城市诊断数据
     * @param organizationDO
     * @param curCityData
     * @param preCityData
     * @param cityId
     * @param cityName
     * @return
     */
    private CfBdCrmDiagnoseDataDO getCfCityBdCrmDiagnoseDataDO(BdCrmOrganizationDO organizationDO,
                                                               Map<Long, CfBdCrmDiagnoseBaseDataModel> curCityData,
                                                               Map<Long, CfBdCrmDiagnoseBaseDataModel> preCityData,
                                                               long cityId,
                                                               String cityName) {
        CfBdCrmDiagnoseDataDO cfBdCrmDiagnoseCity = new CfBdCrmDiagnoseDataDO();
        cfBdCrmDiagnoseCity.setOrgId(organizationDO.getId());
        cfBdCrmDiagnoseCity.setOrgName(organizationDO.getOrgName());
        cfBdCrmDiagnoseCity.setDateTime(calcModel.getCurDateTime());
        cfBdCrmDiagnoseCity.setCurDayRange(calcModel.getCurDayRange());
        cfBdCrmDiagnoseCity.setPreDateTime(calcModel.getPreDateTime());
        cfBdCrmDiagnoseCity.setPreDayRange(calcModel.getPreDayRange());
        //  二期 这个地方改为 根据组织id 获取orgLevel
        cfBdCrmDiagnoseCity.setOrgLevel(organizationDO.getOrgLevel());
        CfBdCrmDiagnoseBaseDataModel curCityBaseDataModel = curCityData.get(cityId);
        CfBdCrmDiagnoseBaseDataModel preCityBaseDataModel = preCityData.get(cityId);
        cfBdCrmDiagnoseCity.calcRationExcludeSdUpDownContribution(curCityBaseDataModel,preCityBaseDataModel);
        cfBdCrmDiagnoseCity.setCityName(cityName);
        cfBdCrmDiagnoseCity.setCityId(cityId);
        cfBdCrmDiagnoseCity.setCityType(1); // cityType 默认0 城市1 省份2
        return cfBdCrmDiagnoseCity;
    }

    /**
     * 计算 组织  下可统计的 省份或城市 的贡献度
     * @param list
     * @param organizationDO
     * @return
     */
    protected abstract List<CfBdCrmDiagnoseDataDO> calcSdUpDownContribution(List<CfBdCrmDiagnoseDataDO> list, BdCrmOrganizationDO organizationDO);

    /**
     * 计算全国 下 各个区域的 贡献度
     * @param list
     * @return
     */
    protected abstract List<CfBdCrmDiagnoseDataDO> calcSdUpDownContributionForQuanGuo(List<CfBdCrmDiagnoseDataDO> list);
}
