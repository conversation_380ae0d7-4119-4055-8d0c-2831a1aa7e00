package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteerCardConfigDO;
import com.shuidihuzhu.common.web.model.Response;

public interface CrowdfundingVolunteerCardConfigService {

    CrowdfundingVolunteerCardConfigDO getCardConfig(CrowdfundingVolunteerCardConfigDO cardConfig);

    Response<String> updateCardConfig(CrowdfundingVolunteerCardConfigDO cardConfig);
}
