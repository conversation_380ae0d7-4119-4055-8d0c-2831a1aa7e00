package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.greenchannel.CfClewGreenChannelApplyDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGreenChannelEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.greenchannel.CfApplyStatusNumModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.GreenChannelApplyParam;

import java.util.List;

public interface GreenChannelApplyService {

    /**
     * 查询申请单
     * @param queryParam
     * @return
     */
    List<CfClewGreenChannelApplyDO> queryApplyList(GreenChannelApplyParam queryParam);

    /**
     * 查询申请单数量
     * @param queryParam
     * @return
     */
    int queryApplyCount(GreenChannelApplyParam queryParam);

    /**
     * 查询申请单详情
     * @param id
     * @return
     */
    CfClewGreenChannelApplyDO queryApplyById(Long id);

    List<CfClewGreenChannelApplyDO> queryApplyByIdList(List<Long> idList);

    /**
     * 查询申请单信息
     * @param caseId
     * @return
     */
    List<CfClewGreenChannelApplyDO> queryApplyByCaseId(Integer caseId);

    /**
     * 分组查询审批状态数量
     * @param queryParam
     * @return
     */
    List<JSONObject> queryApplyNum(GreenChannelApplyParam queryParam);

    void saveApplyRecord(CfClewGreenChannelApplyDO applyDO);

    void modifyApplyRecord(CfClewGreenChannelApplyDO applyDO);

    /**
     * 根据申请id 更新审批状态和，推广渠道
     * @param id
     * @param status
     * @param channeList 推广渠道支持多个，逗号隔开
     */
    void modifyApplyStatusById(Long id, CfGreenChannelEnums.ApplyStatusEnum status , List<CfGreenChannelEnums.PushChannelEnum> channeList);

    int modifyPayProgressById(Long id,Integer progressStatus);

    int modifyRejectReasonById(Long id,String reason, CfGreenChannelEnums.ApplyStatusEnum statusEnum);

    int modifyReviewOpinionById(Long id, String viewPinoin, CfGreenChannelEnums.ApplyStatusEnum applyStatusEnum);

    int modifyPublicCaseId(Long id, String publicCaseId);

    List<CfClewGreenChannelApplyDO> listByPublicCaseIds(List<String> publicCaseIds);

}
