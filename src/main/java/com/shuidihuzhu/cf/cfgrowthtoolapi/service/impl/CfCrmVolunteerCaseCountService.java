package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmVolunteerCaseCountDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmDateTimeCaseCountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmVolunteerCaseCountModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfCrmVolunteerCaseCountSimpleModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmSearchParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCrmVolunteerCaseCountService;
import com.shuidihuzhu.cf.dao.bdcrm.CfCrmVolunteerCaseCountDao;
import com.shuidihuzhu.cf.dao.es.EsCfBdCaseInfoDao;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/11/28 3:51 PM
 */
@Service
@Slf4j
@RefreshScope
public class CfCrmVolunteerCaseCountService extends ESCfBdCaseInfoService implements ICfCrmVolunteerCaseCountService {

    @Autowired
    private CfCrmVolunteerCaseCountDao cfCrmVolunteerCaseCountDao;

    @Autowired
    private EsCfBdCaseInfoDao esCfBdCaseInfoDao;

    @Value("${apollo.bdcrm.case.es:false}")
    private boolean useCaseEs;

    @Override
    public CfCrmVolunteerCaseCountDO getCfCrmVolunteerCaseCountDOByDateTimeWithAmountWithUniqueCode(String dateTime, int amount, String volunteerUniqueCode) {
        return cfCrmVolunteerCaseCountDao.getCfCrmVolunteerCaseCountDOByDateTimeWithAmountWithUniqueCode(dateTime, amount, volunteerUniqueCode);
    }

    @Override
    public List<CfCrmVolunteerCaseCountModel> getCaseCountModelGroupByUniqueCode(List<String> dateTimes, List<Integer> orgIdList, BdCrmSearchParam bdCrmSearchParam) {
        OpResult<List<CfCrmVolunteerCaseCountModel>> fromEs = getCfCrmVolunteerCaseCountModelGroupByFromEs(dateTimes, orgIdList, bdCrmSearchParam);
        return fromEs.isSuccess() ? fromEs.getData() : Lists.newArrayList();
    }


    /**
     * 给具体的人员查看数据
     */
    @Override
    public List<CfCrmVolunteerCaseCountSimpleModel> getCfCrmVolunteerCaseCountSimpleModel(List<String> dateTimes, int amount, String uniqueCode, Integer firstApprovePass) {
        if (useCaseEs) {
            OpResult<List<CfCrmVolunteerCaseCountSimpleModel>> fromEs = getCfCrmVolunteerCaseCountSimpleModelFromEs(dateTimes, amount * 100, uniqueCode, firstApprovePass);
            return fromEs.isSuccess() ? fromEs.getData() : Lists.newArrayList();
        }
        return cfCrmVolunteerCaseCountDao.getCfCrmVolunteerCaseCountSimpleModel(dateTimes, amount, uniqueCode);
    }

    /**
     * 给管理员查看总数
     */
    @Override
    public List<CfCrmDateTimeCaseCountModel> getCfCrmDateTimeCaseCountModel(List<String> dateTimes,
                                                                            int amount,
                                                                            List<Integer> orgIdList,
                                                                            int notFirstApprovePass) {
        OpResult<List<CfCrmDateTimeCaseCountModel>> fromEs = getCfCrmDateTimeCaseCountModelFromEs(dateTimes, amount * 100, orgIdList, notFirstApprovePass);
        return fromEs.isSuccess() ? fromEs.getData() : Lists.newArrayList();
    }


    @Override
    public CfCrmVolunteerCaseCountModel getOrgCrmCrowdfundingModel(List<String> dateTimes, List<Integer> orgIdList, BdCrmSearchParam bdCrmSearchParam) {
        OpResult<CfCrmVolunteerCaseCountModel> fromEs = getOrgCrmCrowdfundingModelFromEs(dateTimes, orgIdList, bdCrmSearchParam);
        return fromEs.isSuccess() ? fromEs.getData() : new CfCrmVolunteerCaseCountModel();
    }

    @Override
    public List<CfCrmVolunteerCaseCountSimpleModel> aggregateByUniqueCodeAndDateTime(List<String> dateTimes, List<String> uniqueCodeList) {
        try {
            return aggregateByUniqueCodeAndDateTimeFromEs(dateTimes, uniqueCodeList);
        } catch (Exception e) {
            log.error("aggregateByUniqueCodeAndDateTime error", e);
            return Lists.newArrayList();
        }
    }


}
