package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.client.cf.growthtool.model.CfTouFangSignDto;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * @Auther: zgq
 * @Date: 2019-05-14 15:24
 * @Description:外部投放接口类
 */
public interface OutsidePutSignInterface {

    Response registerAdd(CfTouFangSignDto cfTouFangSignDto);

    Response registerMobile(CfTouFangSignDto cfTouFangSignDto);

    Response fixAdRegister(String token, Long startId, Long endId, Integer limit);

    Response fixToufangRegister(String token, Long startId, Long endId, Integer limit);

    Response handleToufangRegisterCount(List<String> monitorMobiles,
                                               Double alarmFactor, Integer lowestCount);

}
