package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmBdHomepageCaseDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfCrmBdHonourDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CfCrmHonourVo;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2020-05-14 15:13
 */
public interface IBdCrmHomePageService {

	List<CfCrmBdHomepageCaseDO> getCase(String uniqueCode);

    List<CfCrmBdHomepageCaseDO> getCaseByCaseIdList(List<Long> caseIdList, String uniqueCode);

	List<CfCrmBdHomepageCaseDO> getCaseByCaseIds(List<Long> caseIds);

    List<CfCrmBdHomepageCaseDO> getCaseByCreateTime(String uniqueCode, String createTime);

	void saveCfCrmBdHonour(CfCrmBdHonourDO cfCrmBdHonourDO);

	CfCrmBdHonourDO getCfCrmBdHonourDOById(Long id);

	void updateApproveStatus(Long id, Integer approveStatus);

	List<CfCrmHonourVo> getHonourPicWaitApprove(String approveMis, int status);

	CfCrmBdHonourDO getCfCrmBdHonourDOByUniqueCode(String uniqueCode);


	CfCrmBdHonourDO getCfCrmBdHonourDOByUniqueCodeWithApproveStatus(String uniqueCode, Integer approveStatus);

	List<CfCrmBdHomepageCaseDO> getCaseListByCityId(Integer cityId, int limit);

	List<CfCrmBdHomepageCaseDO> getCaseListByProvinceId(Integer provinceId,int limit);

	int saveCfBdHomepageCase(List<CfCrmBdHomepageCaseDO> cfCrmBdHomepageCaseDOS);

	int updateCfCrmBdHomepageCase(CfCrmBdHomepageCaseDO cfCrmBdHomepageCaseDO);

	CfCrmHonourVo getBdHonourPic(String uniqueCode);

	void updateCfCrmBdHonourDO(CfCrmBdHonourDO cfCrmBdHonourDO);

    int updateIsdelete(long caseId);

    List<Long> getCaseIdListExcludeChangedCaseIdList(String uniqueCode, List<Long> changedCaseIdList);

    long countSelfCase(String uniqueCode);
}
