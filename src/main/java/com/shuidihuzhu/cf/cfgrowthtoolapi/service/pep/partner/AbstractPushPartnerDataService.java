package com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.partner;

import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.CfKpiPartnerDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.pep.AbstractPushDataService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.cf.client.performance.LotInfo;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: fengxuan
 * @create 2023-05-22 19:08
 **/
public abstract class AbstractPushPartnerDataService extends AbstractPushDataService {

    @Autowired
    protected CfKpiPartnerDataService cfKpiPartnerDataService;

}
