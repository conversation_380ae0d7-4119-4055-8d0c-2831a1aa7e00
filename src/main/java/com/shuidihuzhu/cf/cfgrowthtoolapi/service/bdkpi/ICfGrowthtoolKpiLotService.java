package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfGrowthtoolKpiLotDO;

import java.util.List;

/**
 * 批次信息维护(CfGrowthtoolKpiLot)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-04 15:29:35
 */
public interface ICfGrowthtoolKpiLotService {

    List<CfGrowthtoolKpiLotDO> listByTemplateIdAndBizType(long templateId, int bizType, int subBizType);

    int insert(CfGrowthtoolKpiLotDO cfGrowthtoolKpiLot);

    int update(CfGrowthtoolKpiLotDO cfGrowthtoolKpiLot);

    int updatePushStatus(long id, int pushStatus);

    void insertOrUpdate(CfGrowthtoolKpiLotDO cfGrowthtoolKpiLot);

    boolean deleteById(long id);

}
