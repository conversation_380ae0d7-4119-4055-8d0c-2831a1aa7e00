package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IpepDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfCrmMemberSnapshotDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiBdScoreDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdkpi.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi.ICfKpiBdScoreTempService;
import com.shuidihuzhu.cf.dao.bdkpi.CfKpiBdScoreTempDao;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2020-08-03 17:05
 */
@Data
@Service
@Primary
@Slf4j
public class CfKpiBdScoreTempService implements ICfKpiBdScoreTempService {
    @Autowired
    private CfKpiBdScoreTempDao cfKpiBdScoreTempDao;

    @Autowired
    private IpepDelegate pepDelegate;

    @Override
    public List<CfKpiBdScoreDO> getKpiBdList(Integer level, String monthKey, List<String> uniqueCodeList) {
        log.info(this.getClass().getName()+" getKpiBdList level:{} monthKey:{} uniqueCodeList:{}",level,monthKey,JSON.toJSONString(uniqueCodeList));
        if (CollectionUtils.isEmpty(uniqueCodeList)){
            return Lists.newArrayList();
        }
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> cfKpiBdScoreTempDao.getKpiBdList(level, monthKey, list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
    }

    @Override
    public int batchInsert(int level, String monthKey, List<CfCrmMemberSnapshotDO> cfCrmMemberSnapshotDOS, List<CustomPerformanceScoreModel> customPerformanceScoreModels) {
        String assessmentScoreJson = JSON.toJSONString(customPerformanceScoreModels);
        int i = cfKpiBdScoreTempDao.batchInsert(level,monthKey, cfCrmMemberSnapshotDOS, assessmentScoreJson);
        log.info(this.getClass().getName()+" batchInsert level:{} monthKey:{}, uniqueCodeList:{} score:{} result :{}",
                level,
                monthKey,
                cfCrmMemberSnapshotDOS.stream().map(CfCrmMemberSnapshotDO::getUniqueCode).collect(Collectors.toList()),
                assessmentScoreJson,
                i);
        return i;
    }

    @Override
    public int batchUpdate(List<CfKpiBdScoreDO> kpiBdList, List<CustomPerformanceScoreModel> customPerformanceScoreModels) {
        // 查找到当前已经自定义的分，如果重复 则覆盖，如果db中有，现在没有 则保留db
        kpiBdList = kpiBdList.stream().map(cfKpiBdScoreDO -> {
                cfKpiBdScoreDO.setAssessmentScoreJson(CustomPerformanceScoreModel.compareScoreAndReturn(cfKpiBdScoreDO.showAssessmentGrade(),
                        customPerformanceScoreModels));
                return cfKpiBdScoreDO;
        }).collect(Collectors.toList());
        List<List<CfKpiBdScoreDO>> listList = Lists.partition(kpiBdList, GeneralConstant.MAX_PAGE_SIZE);
        Integer i = listList.stream().map(list -> cfKpiBdScoreTempDao.batchUpdate(list)).reduce((total, item) -> total += item).get();
        log.info(this.getClass().getName()+" batchUpdate kpiBdList.size:{} customPerformanceScoreModels:{} result:{}",
                kpiBdList.size(),
                JSON.toJSONString(customPerformanceScoreModels),
                i);
        return i;
    }

    @Override
    public int batchUpdateForC(List<CfKpiBdScoreDO> kpiBdList, CfKPIBdScoreModel cfKPIBdScoreModel) {
        // 查找到当前已经自定义的分，如果重复 则覆盖，如果db中有，现在没有 则保留db
        // 对于可选项金额直接用 入参的值覆盖
        kpiBdList = kpiBdList.stream().map(cfKpiBdScoreDO -> {
                cfKpiBdScoreDO.setAssessmentScoreJson(CustomPerformanceScoreModel.compareScoreAndReturn(cfKpiBdScoreDO.showAssessmentGrade(),
                        cfKPIBdScoreModel.getCustomPerformanceScoreModels()));
                cfKpiBdScoreDO.setAwardAmountJson(JSON.toJSONString(cfKPIBdScoreModel.getAwardCollectModel()));
                return cfKpiBdScoreDO;
        }).collect(Collectors.toList());
        List<List<CfKpiBdScoreDO>> listList = Lists.partition(kpiBdList, GeneralConstant.MAX_PAGE_SIZE);
        Integer i = listList.stream().map(list -> cfKpiBdScoreTempDao.batchUpdate(list)).reduce((total, item) -> total += item).get();
        pepDelegate.syncScoreData(kpiBdList);
        log.info(this.getClass().getName()+" batchUpdate kpiBdList.size:{} customPerformanceScoreModels:{} result:{}",
                kpiBdList.size(),
                JSON.toJSONString(cfKPIBdScoreModel.getCustomPerformanceScoreModels()),
                i);
        return i;
    }

    @Override
    public CfKpiBdScoreDO getScore(String uniqueCode, String monthKey) {
        return cfKpiBdScoreTempDao.getScore(uniqueCode,monthKey);
    }


    @Override
    public OpResult checkIsBdScore(Integer level, String monthKey) {
        long count = cfKpiBdScoreTempDao.getCount(level, monthKey);
        return count > 0 ? OpResult.createFailResult(CfGrowthtoolErrorCode.PERFORMANCE_NOT_ALLOW_MOD) : OpResult.createSucResult(null);
    }

    @Override
    public List<CfKpiBdScoreDO> listKpiBdByMonthAndUniqueCodes(String monthKey, List<String> uniqueCodeList) {
        log.info(this.getClass().getName()+" listKpiBdByMonthAndUniqueCodes monthKey:{} uniqueCodeList:{}",monthKey,JSON.toJSONString(uniqueCodeList));
        if (CollectionUtils.isEmpty(uniqueCodeList)) {
            return Lists.newArrayList();
        }
        List<List<String>> listList = Lists.partition(uniqueCodeList, GeneralConstant.MAX_PAGE_SIZE);
        return listList.parallelStream().map(list -> cfKpiBdScoreTempDao.listKpiBdByMonthAndUniqueCodes(monthKey, list))
                .reduce((total, item) -> {
                    total.addAll(item);
                    return total;
                }).orElse(Lists.newArrayList());
    }

    @Override
    public int batchInsertAndLevel(int level, String monthKey, List<CfCrmMemberSnapshotDO> cfCrmMemberSnapshotDOS, List<CustomPerformanceScoreModel> customPerformanceScoreModels, int circulateLevel, CfKPICommissionAwardCollectModel awardAmounts) {
        String assessmentScoreJson = JSON.toJSONString(customPerformanceScoreModels);
        //发送消息
        int i = cfKpiBdScoreTempDao.batchInsertAndLevel(level,monthKey, cfCrmMemberSnapshotDOS, assessmentScoreJson,circulateLevel,JSON.toJSONString(awardAmounts));
        pepDelegate.syncScoreData(cfCrmMemberSnapshotDOS.get(0).getUniqueCode(), customPerformanceScoreModels, awardAmounts);
        log.debug(this.getClass().getName()+" batchInsert level:{} monthKey:{}, uniqueCodeList:{} score:{} circulateLevel:{} result :{}",
                level,
                monthKey,
                cfCrmMemberSnapshotDOS.stream().map(CfCrmMemberSnapshotDO::getUniqueCode).collect(Collectors.toList()),
                assessmentScoreJson,
                circulateLevel,
                i);
        return i;
    }

    @Override
    public int batchUpdateCirculateLevel(List<CfKpiBdScoreDO> kpiBdList, int circulateLevel) {
        if (CollectionUtils.isEmpty(kpiBdList)){
            return 0;
        }
        return cfKpiBdScoreTempDao.batchUpdateCirculateLevel(kpiBdList,circulateLevel);
    }

    @Override
    public List<CfKpiBdScoreDO> listKpiBdByMonthKey(String monthKey) {
        return cfKpiBdScoreTempDao.listKpiBdByMonthKey(monthKey);
    }
}
