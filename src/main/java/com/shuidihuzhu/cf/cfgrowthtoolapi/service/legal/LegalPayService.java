package com.shuidihuzhu.cf.cfgrowthtoolapi.service.legal;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.legal.BdCrmLegalContractDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.legal.BdCrmLegalContractOrderDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.appmessage.IAppPushCrmCaseMsgService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.client.baseservice.pay.model.PayInnerCallBack;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: fengxuan
 * @create 2022-08-23 20:37
 **/
@Slf4j
@Service
public class LegalPayService {

    @Autowired
    private BdCrmLegalContractOrderService contractOrderService;

    @Autowired
    private BdCrmLegalContractService contractService;

    @Autowired
    private IAppPushCrmCaseMsgService appPushCrmCaseMsgService;

    public void payCallback(PayInnerCallBack payInnerCallBack) {
        String payOrderId = payInnerCallBack.getPayUid();
        String orderId = payInnerCallBack.getOrderId();
        //更新当前订单状态
        BdCrmLegalContractOrderDO contractOrderDO = contractOrderService.getByOrderId(orderId);
        if (contractOrderDO == null) {
            log.info("找不到对应的订单信息,orderId:{}", orderId);
            return;
        }
        BdCrmLegalContractDO bdCrmLegalContractDO = contractService.queryById(contractOrderDO.getContractId());
        if (payInnerCallBack.getRealPayType() != null) {
            contractOrderDO.setPayType(payInnerCallBack.getRealPayType().getCode());
            bdCrmLegalContractDO.setPayTypeDesc(payInnerCallBack.getRealPayType().getDesc());
        }
        contractOrderDO.setPayOrderId(payOrderId);
        bdCrmLegalContractDO.setId(contractOrderDO.getContractId());
        if (payInnerCallBack.getPayStatus() != null) {
            contractOrderDO.setRealPayStatus(payInnerCallBack.getPayStatus().getCode());
            bdCrmLegalContractDO.setPayStatus(payInnerCallBack.getPayStatus().getCode());
        }
        contractOrderService.updatePayInfo(contractOrderDO);
        if (payInnerCallBack.getBusinessTime() != null) {
            String payTime = new DateTime(payInnerCallBack.getBusinessTime()).toString(GrowthtoolUtil.ymdhmsfmt);
            bdCrmLegalContractDO.setPayTime(payTime);
        }
        //更新合同状态
        contractService.updatePayInfo(bdCrmLegalContractDO);
        //发送消息
        appPushCrmCaseMsgService.sendLegalPaySuc(bdCrmLegalContractDO);
    }
}
