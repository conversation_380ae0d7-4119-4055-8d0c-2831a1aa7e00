package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdkpi;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdkpi.CfKpiPartnerDataDO;

import java.util.List;

/**
 * 合作团队kpi基础数据(CfKpiPartnerData)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-22 20:18:57
 */
public interface CfKpiPartnerDataService {

    CfKpiPartnerDataDO queryById(long id);

    int insert(CfKpiPartnerDataDO cfKpiPartnerData);

    int update(CfKpiPartnerDataDO cfKpiPartnerData);

    boolean deleteById(long id);

    List<CfKpiPartnerDataDO> listByDateKeyAndPartnerType(String dateKey, int partnerType);

}
