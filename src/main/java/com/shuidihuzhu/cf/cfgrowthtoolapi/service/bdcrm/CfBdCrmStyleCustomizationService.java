package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmStyleCustomizationParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.CfBdCrmStyleCustomizationVo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2024/9/2  11:30
 */
public interface CfBdCrmStyleCustomizationService {

    Response<CfBdCrmStyleCustomizationVo> showStyleCustomizationInfo(String infoUuid, CrowdfundingVolunteer cfVolunteer);

    Response<Void> addOrUpdateCaseDisplaySetting(CfBdCrmStyleCustomizationParam cfBdCrmStyleCustomizationParam, CrowdfundingVolunteer cfVolunteer);

}
