package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.base.Joiner;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ISeaAccountServiceDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.SeaAdminUserInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.ICfCalcDiseaseCostService;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.dao.bdcrm.DiseaseCostLogDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.CfCalcCostModel;
import com.shuidihuzhu.client.cf.growthtool.model.ClewPreposeMaterialSaveOrUpdateModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseClassifyVO;
import com.shuidihuzhu.client.cf.growthtool.model.vo.DiseaseVO;
import com.shuidihuzhu.client.cf.growthtool.model.vo.InfoReasonableAmountResultVO;
import com.shuidihuzhu.client.cf.growthtool.param.DiseaseParam;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-06-27
 */
@Slf4j
@Service
@RefreshScope
public class CalcDiseaseCostServiceImpl implements ICfCalcDiseaseCostService {

    @Autowired
    private DiseaseCostLogDao diseaseCostLogDao;
    @Autowired
    private ISeaAccountServiceDelegate seaAccountServiceDelegate;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public Integer updateOperatorLog(DiseaseParam diseaseParam, CrowdfundingVolunteer cfVolunteer,InfoReasonableAmountResultVO infoVo) {
        if (Objects.isNull(infoVo)){
            return 0;
        }
        CfCalcCostModel cfCalcCostModel = this.buildUpdateCfCalcCostModel(diseaseParam,infoVo);
        return diseaseCostLogDao.updateCalcCostInfo(cfCalcCostModel);
    }

    @Override
    public Long saveOperatorLog(DiseaseParam diseaseParam, CrowdfundingVolunteer cfVolunteer, DiseaseVO diseaseVO) {
        CfCalcCostModel cfCalcCostModel = this.buildCfCalcCostModel(diseaseParam,cfVolunteer,diseaseVO);
        diseaseCostLogDao.insert(cfCalcCostModel);
        return cfCalcCostModel.getId();
    }

    @Override
    public int recordPreposeId(Long reportId, List<Long> calcCostIds) {
        int ret = 0;
        if (CollectionUtils.isNotEmpty(calcCostIds) && Objects.nonNull(reportId)){
            ret = diseaseCostLogDao.updateReportIdByIds(reportId,calcCostIds);
        }
        return ret;
    }

    @Override
    public OpResult<List<CfCalcCostModel>> queryCalcCostLog(DiseaseParam diseaseParam) {
        List<CfCalcCostModel> result = diseaseCostLogDao.queryLogByTaskId(diseaseParam.getTaskId());
        result.forEach(CfCalcCostModel::parseInfoVo);
        return OpResult.createSucResult(result);
    }

//    @Override
//    public void recordOfflineTreatmentInfoCache(PreposeMaterialModel.MaterialInfoVo materialInfoVo) {
//        String identity = this.calcIdentity(materialInfoVo.getPatientIdCard(),materialInfoVo.getVolunteerUniqueCode());
//        StringBuilder sb = new StringBuilder();
//        if (StringUtils.isNotEmpty(identity)){
//            if (StringUtils.isNotEmpty(materialInfoVo.getTreatmentInfo())){
//                sb.append(materialInfoVo.getTreatmentInfo()).append("^_^");
//            }
//            if (StringUtils.isNotEmpty(materialInfoVo.getRpTreatmentInfo())){
//                sb.append(materialInfoVo.getRpTreatmentInfo());
//            }
//            try{
//                redissonHandler.setEX(String.format(GeneralConstant.GROWTHTOOL_REPORT_DT,identity), sb.toString(), RedissonHandler.ONE_DAY);
//            }catch (Exception e){
//                log.error("recordOfflineTreatmentInfoCache error",e);
//            }
//        }
//    }
//
//    @Override
//    public void setOfflineTreatmentInfoFromCache(PreposeMaterialModel.MaterialInfoVo materialInfoVo) {
//
//        String identity = this.calcIdentity(materialInfoVo.getPatientIdCard(),materialInfoVo.getVolunteerUniqueCode());
//        if (StringUtils.isNotEmpty(identity)){
//            try{
//                String treatmentInfo = redissonHandler.get(String.format(GeneralConstant.GROWTHTOOL_REPORT_DT,identity), String.class);
//                if (StringUtils.isNotEmpty(treatmentInfo)){
//                    List<String> treatmentInfoList = Splitter.on("^_^").trimResults().splitToList(treatmentInfo);
//                    //疾病治疗项回显
//                    int length = treatmentInfoList.size();
//                    if (length == 1){
//                        materialInfoVo.setRpTreatmentInfo(treatmentInfoList.get(0));
//                    }
//                    if (length == 2){
//                        materialInfoVo.setTreatmentInfo(treatmentInfoList.get(0));
//                        materialInfoVo.setRpTreatmentInfo(treatmentInfoList.get(1));
//                    }
//                }
//            }catch (Exception e){
//                log.error("getOfflineTreatmentInfoFromCache error",e);
//            }
//        }
//    }

    @Override
    public void setDiseaseName(PreposeMaterialModel.MaterialInfoVo materialInfoVo) {
        String medicalDisease =  materialInfoVo.getMedicalDisease();
        String patientRpDisease = materialInfoVo.getPatientRpDisease();
        if (StringUtils.isNotEmpty(medicalDisease)){
            String disease= Strings.left(medicalDisease,50);
            materialInfoVo.setDiseaseName(disease);
        }else if (StringUtils.isNotEmpty(patientRpDisease)){
            String disease= Strings.left(patientRpDisease,50);
            materialInfoVo.setDiseaseName(disease);
        }
    }

    @Override
    public void setDiseaseName(ClewPreposeMaterialSaveOrUpdateModel clewPreposeMaterialSaveOrUpdateModel) {
        ClewPreposeMaterialSaveOrUpdateModel.ClewPreposeMaterialModel clewPreposeMaterialModel = clewPreposeMaterialSaveOrUpdateModel.getClewPreposeMaterialModel();
        String medicalDisease =  clewPreposeMaterialModel.getMedicalDisease();
        String patientRpDisease = clewPreposeMaterialModel.getPatientRpDisease();
        if (StringUtils.isNotEmpty(medicalDisease)){
            String disease= Strings.left(medicalDisease,50);
            clewPreposeMaterialModel.setDiseaseName(disease);
        }else if (StringUtils.isNotEmpty(patientRpDisease)){
            String disease= Strings.left(patientRpDisease,50);
            clewPreposeMaterialModel.setDiseaseName(disease);
        }
    }

    private CfCalcCostModel buildCfCalcCostModel(DiseaseParam diseaseParam, CrowdfundingVolunteer cfVolunteer, DiseaseVO diseaseVO){
        String operator = diseaseParam.getOperator();
        if (Objects.nonNull(cfVolunteer)){
            operator = cfVolunteer.getMis();
        }
        String organization = "";
        if (StringUtils.isNotEmpty(operator)){
            SeaAdminUserInfoModel seaAdminUserInfoModel = seaAccountServiceDelegate.getOrgInfoByMisUseCache(operator);
            organization = seaAdminUserInfoModel != null ? seaAdminUserInfoModel.getOrgName() : "";
        }
        CfCalcCostModel calcCostLog = new CfCalcCostModel();
        List<String> submitDiseases = diseaseParam.getSubmitDiseases();
        int size = submitDiseases.size();
        calcCostLog.setDiseaseType(diseaseParam.getDiseaseType());
        calcCostLog.setSubmitDiseaseNames(Joiner.on(",").join(submitDiseases));
        calcCostLog.setOperator(operator);
        calcCostLog.setOrganization(organization);
        calcCostLog.setTaskId(diseaseParam.getTaskId());

        //归一后的疾病名称
        Set<String> classifyDiseaseList = diseaseVO.getDiseaseClassifyList().stream().filter(item->StringUtils.isNotEmpty(item.getNorm()))
                .map(DiseaseClassifyVO::getDisease).collect(Collectors.toSet());
        calcCostLog.setClassifyDiseaseNames(Joiner.on(",").join(classifyDiseaseList));
        submitDiseases.removeAll(classifyDiseaseList);
        if (submitDiseases.size() == 0){
            //1 全部归一
            calcCostLog.setHasDiseaseNoClassify(1);
        }else if (submitDiseases.size() < size){
            //2 部分归一
            calcCostLog.setHasDiseaseNoClassify(2);
        }else{
            //0 全部未归一
            calcCostLog.setHasDiseaseNoClassify(0);
        }
        //未归一的疾病名称
        calcCostLog.setNotClassifyDiseaseNames(Joiner.on(",").join(submitDiseases));
        return calcCostLog;
    }

    private CfCalcCostModel buildUpdateCfCalcCostModel(DiseaseParam diseaseParam,InfoReasonableAmountResultVO infoVo){
        CfCalcCostModel calcCostLog = new CfCalcCostModel();
        calcCostLog.setInfoVo(infoVo);
        calcCostLog.convertInfo();
        calcCostLog.setHasMatchAllDisease(diseaseParam.getClassifyDiseases().size() == infoVo.getDiseaseInfoList().size() ? 1 : 0);
        calcCostLog.setId(diseaseParam.getCalcCostId());
        return calcCostLog;
    }

    /**
     * 根据证件号+uniqueCode计算唯一值
     * @param patientIdCard
     * @param uniqueCode
     * @return
     */
    private String calcIdentity(String patientIdCard, String uniqueCode){
        try{
            if (StringUtils.isNotEmpty(patientIdCard) && StringUtils.isNotEmpty(uniqueCode)){
                return MD5Util.getMD5HashValue(uniqueCode+patientIdCard);
            }
        }catch (Exception e){
            log.warn("calcIdentity error",e);
        }
        return null;
    }
}
