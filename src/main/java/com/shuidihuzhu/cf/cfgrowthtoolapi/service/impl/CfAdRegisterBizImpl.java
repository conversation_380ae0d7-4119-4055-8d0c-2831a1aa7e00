package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.ICfMasterForGrowthtoolDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.ShuidiCipherUtils;
import com.shuidihuzhu.cf.dao.CfAdRegisterDao;
import com.shuidihuzhu.cf.dao.TaskCfAdRegisterDao;
import com.shuidihuzhu.cf.cfgrowthtoolapi.mq.producer.CommonMessageHelperService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.CfAdRegisterBizInterface;
import com.shuidihuzhu.cf.constants.crowdfunding.status.RelationShip;
import com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister;
import com.shuidihuzhu.cf.model.toufang.CfToufangRegister;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.api.client.OutsidePutFeignClient;
import com.shuidihuzhu.client.grpc.account.v1.feign.UserThirdServiceClient;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Created by wangsf on 17/4/27.
 */
@Slf4j
@Service
public class CfAdRegisterBizImpl implements CfAdRegisterBizInterface {

    @Autowired
    private CfAdRegisterDao cfAdRegisterDao;

    @Autowired
    private UserThirdServiceClient userThirdServiceClient;

    @Autowired
    OutsidePutFeignClient outsidePutFeignClient;

    @Autowired
    CommonMessageHelperService commonMessageHelperService;

    @Autowired
    private TaskCfAdRegisterDao taskCfAdRegisterDao;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private ICfMasterForGrowthtoolDelegate cfMasterForGrowthtoolDelegate;

    @Override
    public int add(CfAdRegister adRegister) {
        if (adRegister == null) {
            return -1;
        }

        if (StringUtils.isBlank(adRegister.getMobile())) {
            return -1;
        }

        this.setOpenId(adRegister);

        String userName = adRegister.getUserName();
        userName = userName == null ? "" : userName;
        adRegister.setUserName(userName);

        CfAdRegister cfAdRegister = this.cfAdRegisterDao.selectByMobileAndTypeAndDate(ShuidiCipherUtils.encrypt(adRegister.getMobile()), adRegister.getType(), adRegister.getDayKey());

        if (cfAdRegister != null) {
            return 0;
        }

        adRegister.setCryptoMobile(ShuidiCipherUtils.encrypt(adRegister.getMobile()));
        adRegister.setCryptoInviterMobile(ShuidiCipherUtils.encrypt(adRegister.getInviterMobile()));

        int insertResult = this.cfMasterForGrowthtoolDelegate.insertCfAdRegister(adRegister);
        if (insertResult > 0) {
            delayCheckWithoutSubscribeWxDoSendAdMessage(adRegister);
        }
        return insertResult;
    }

    /**
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=10691233
     * 送延时消息
     *
     * @param adRegister
     * @see com.shuidihuzhu.cf.constants.MQTagCons#CF_DELAY_WITH_REGISTER
     */
    private void delayCheckWithoutSubscribeWxDoSendAdMessage(CfAdRegister adRegister) {
        log.info("send delay CfAdRegister: {}", adRegister);
        if (StringUtils.isEmpty(adRegister.getMobile())) {
            log.info("mobile empty CfAdRegister: {}", adRegister);
            return;
        }
        if (StringUtils.isEmpty(adRegister.getOpenId())) {
            log.info("openId empty CfAdRegister: {}", adRegister);
            return;
        }
        int id = adRegister.getId();
        commonMessageHelperService.send(commonMessageHelperService.getCfRegisterDelayMessage(id));
    }

    @Override
    public List<CfAdRegister> findByTime(Timestamp start, Timestamp end, int offset, int limit) {
        if (start == null || end == null || start.compareTo(end) >= 0 || offset < 0 || limit <= 0) {
            return Collections.EMPTY_LIST;
        }

        List<CfAdRegister> cfAdRegisters = cfAdRegisterDao.selectByTime(start, end, offset, limit);

        List<CfAdRegister> cfAdRegisterList = Lists.newArrayList();
        for (CfAdRegister cfAdRegister : cfAdRegisters){
            cfAdRegister.setMobile(shuidiCipher.decrypt(cfAdRegister.getCryptoMobile()));
            cfAdRegister.setInviterMobile(shuidiCipher.decrypt(cfAdRegister.getCryptoInviterMobile()));
            cfAdRegisterList.add(cfAdRegister);
        }

        return cfAdRegisterList;
    }

    @Override
    public CfAdRegister selectByChannelAndMobile(String mobile, List<String> listChannel) {
        if (StringUtils.isEmpty(mobile) || CollectionUtils.isEmpty(listChannel)) {
            return null;
        }

        CfAdRegister cfAdRegister = cfAdRegisterDao.selectByChannelAndMobile(ShuidiCipherUtils.encrypt(mobile), listChannel);
        if(Objects.nonNull(cfAdRegister)){
            cfAdRegister.setMobile(shuidiCipher.decrypt(cfAdRegister.getCryptoMobile()));
            cfAdRegister.setInviterMobile(shuidiCipher.decrypt(cfAdRegister.getCryptoInviterMobile()));
        }

        return cfAdRegister;
    }

    @Override
    public int onlyAdd(CfAdRegister cfAdRegister) {
        if (cfAdRegister == null) {
            return -1;
        }

        if (StringUtils.isBlank(cfAdRegister.getMobile())) {
            return -1;
        }

        this.setOpenId(cfAdRegister);
        this.checkNullOrEmpty(cfAdRegister);

        cfAdRegister.setCryptoMobile(ShuidiCipherUtils.encrypt(cfAdRegister.getMobile()));
        cfAdRegister.setCryptoInviterMobile(ShuidiCipherUtils.encrypt(cfAdRegister.getInviterMobile()));

        return this.cfMasterForGrowthtoolDelegate.insertCfAdRegister(cfAdRegister);
    }

    private void setOpenId(CfAdRegister cfAdRegister) {
        if (null == cfAdRegister) {
            return;
        }
        long userId = cfAdRegister.getUserId();
        if (userId > 0) {
            UserThirdModel userNewThirdModel = userThirdServiceClient.getThirdModelWithUserId(userId, GeneralConstant.FUNDRAISER_THIRD_TYPE);
            String openId = "";
            if (Objects.nonNull(userNewThirdModel)) {
                openId = userNewThirdModel.getOpenId();
            }
            openId = openId == null ? "" : openId;
            cfAdRegister.setOpenId(openId);
        } else {
            String openId = cfAdRegister.getOpenId();
            openId = openId == null ? "" : openId;
            cfAdRegister.setOpenId(openId);
        }
    }

    private void checkNullOrEmpty(CfAdRegister cfAdRegister) {
        if (null == cfAdRegister) {
            return;
        }

        String userName = cfAdRegister.getUserName();
        userName = userName == null ? "" : userName;
        cfAdRegister.setUserName(userName);

        String channel = cfAdRegister.getChannel();
        channel = channel == null ? "" : channel;
        cfAdRegister.setChannel(channel);

        String disease = cfAdRegister.getDisease();
        disease = disease == null ? "" : disease;
        cfAdRegister.setDisease(disease);

        String relation = cfAdRegister.getRelation();
        relation = relation == null ? RelationShip.OTHER.getDescription() : relation;
        cfAdRegister.setRelation(relation);

        String help = cfAdRegister.getHelp();
        help = help == null ? "" : help;
        cfAdRegister.setHelp(help);

        String note = cfAdRegister.getNote();
        note = note == null ? "" : note;
        cfAdRegister.setNote(note);

        String type = cfAdRegister.getType();
        type = type == null ? "" : type;
        cfAdRegister.setType(type);

        String selfTag = cfAdRegister.getSelfTag();
        selfTag = selfTag == null ? "" : selfTag;
        cfAdRegister.setSelfTag(selfTag);
    }

    @Override
    public CfAdRegister findByMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return null;
        }
        CfAdRegister cfAdRegister = cfAdRegisterDao.selectByMobile(ShuidiCipherUtils.encrypt(mobile));

        if(Objects.nonNull(cfAdRegister)){
            cfAdRegister.setMobile(shuidiCipher.decrypt(cfAdRegister.getCryptoMobile()));
            cfAdRegister.setCryptoInviterMobile(shuidiCipher.decrypt(cfAdRegister.getCryptoInviterMobile()));
        }

        return cfAdRegister;
    }

    @Override
    public CfAdRegister getById(long id) {
        CfAdRegister cfAdRegister = cfAdRegisterDao.getById(id);

        if(Objects.nonNull(cfAdRegister)){
            cfAdRegister.setMobile(shuidiCipher.decrypt(cfAdRegister.getCryptoMobile()));
            cfAdRegister.setCryptoInviterMobile(shuidiCipher.decrypt(cfAdRegister.getCryptoInviterMobile()));
        }

        return cfAdRegister;
    }


    @Override
    public List<CfToufangRegister> getBetween(long startId, long endId, long anchorId, int limit) {

        List<CfToufangRegister> cfToufangRegisters = taskCfAdRegisterDao.getBetween(startId, endId, anchorId, limit);

        List<CfToufangRegister> cfToufangRegisterList = Lists.newArrayList();
        for (CfToufangRegister cfToufangRegister : cfToufangRegisters){
            cfToufangRegister.setMobile(shuidiCipher.decrypt(cfToufangRegister.getCryptoMobile()));
            cfToufangRegisterList.add(cfToufangRegister);
        }

        return cfToufangRegisterList;
    }
}
