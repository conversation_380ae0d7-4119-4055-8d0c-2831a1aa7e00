package com.shuidihuzhu.cf.cfgrowthtoolapi.service.experiment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2024/7/31 3:04 PM
 */
@Configuration
public class StrategyConfig {

    @Autowired
    private List<SceneStrategy> strategyList; // 自动注入所有SceneStrategy实现类

    @Bean
    public Map<Integer, SceneStrategy> strategyMap() {
        Map<Integer, SceneStrategy> map = new HashMap<>();
        for (SceneStrategy strategy : strategyList) {
            // 假设每个策略都有一个方法来返回其对应的场景ID
            Integer sceneId = strategy.getSceneId();
            map.put(sceneId, strategy);
        }
        return map;
    }
}
