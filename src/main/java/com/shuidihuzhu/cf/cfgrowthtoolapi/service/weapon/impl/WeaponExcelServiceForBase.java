package com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.export.impl.CfBdCrmExcelExpoetUtil;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.export.BudgetDetailExportModelForBase;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.weapon.AbstractWeaponExcelService;
import com.shuidihuzhu.client.cf.growthtool.enums.WeaponActivityResourceType;
import com.shuidihuzhu.client.cf.growthtool.enums.WeaponActivityTypeEnum;
import com.shuidihuzhu.common.web.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-12-09 10:09 下午
 **/
@Slf4j
@Service
public class WeaponExcelServiceForBase extends AbstractWeaponExcelService {

    @Autowired
    private CfBdCrmExcelExpoetUtil cfBdCrmExcelExpoetUtil;


    @Override
    public boolean supportActivityType(int activityType) {
        WeaponActivityTypeEnum typeEnum = WeaponActivityTypeEnum.findByCode(activityType);
        if (typeEnum == null) {
            return false;
        }
        if (ObjectUtils.nullSafeEquals(activityType, WeaponActivityTypeEnum.person_to_public.getCode())) {
            return true;
        }
        return ObjectUtils.nullSafeEquals(typeEnum.getResourceType(), WeaponActivityResourceType.c_duan);
    }


    @Override
    protected void doExportExcel(HttpServletResponse response, List<BudgetDetailExportModelForBase> baseList, long adminLongUserId) throws IOException {

        //构造表头
        List<String> headerList = Lists.newArrayList();
        headerList.add("activityName");
        headerList.add("applyName");
        headerList.add("applyTime");
        headerList.add("caseTitle");
        headerList.add("caseId");
        headerList.add("caseLink");
        headerList.add("applyReason");
        headerList.add("applyStatus");
        headerList.add("caseDuplicate");
        headerList.add("rejectReason");
        headerList.add("orgName");
        headerList.add("applyMoney");


        //调用导出
        cfBdCrmExcelExpoetUtil.exportV3(adminLongUserId, baseList, headerList, BudgetDetailExportModelForBase.class, "预算使用明细");

    }
}
