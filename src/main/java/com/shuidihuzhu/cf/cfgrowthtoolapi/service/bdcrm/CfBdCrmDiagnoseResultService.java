package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmDiagnoseResultDO;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2021/4/26 下午5:17
 */
public interface CfBdCrmDiagnoseResultService{


    int insert(CfBdCrmDiagnoseResultDO record);

    void batchInsert(List<CfBdCrmDiagnoseResultDO> records);

    CfBdCrmDiagnoseResultDO selectByPrimaryKey(long id);

    int updateByPrimaryKeySelective(CfBdCrmDiagnoseResultDO record);

    CfBdCrmDiagnoseResultDO getCfBdCrmDiagnoseResultDO(long orgId, String dateTime,int curDayRange,String preDateTime,int preDayRange, String dataId, int dataType);

    List<CfBdCrmDiagnoseResultDO> listCfBdCrmDiagnoseResultByOrgIdWithDateTimes(long orgId, List<String> dateTimes, String dataId, Integer dataType);

    List<CfBdCrmDiagnoseResultDO> getSubList(String dateTime, CfBdCrmDiagnoseResultDO diagnoseResultDO, boolean needSort);

    void updateParentId(Long newParentId, Long oldParentId, List<Long> dataIdList, int dataType);
}
