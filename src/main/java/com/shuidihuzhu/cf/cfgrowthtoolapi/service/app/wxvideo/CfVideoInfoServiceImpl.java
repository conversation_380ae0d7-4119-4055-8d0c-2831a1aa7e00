package com.shuidihuzhu.cf.cfgrowthtoolapi.service.app.wxvideo;


import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfWxOfficialAccountVideoInfoModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.VideoSnapModel;
import com.shuidihuzhu.cf.dao.bdcrm.CfVideoInfoDao;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class CfVideoInfoServiceImpl implements CfVideoInfoService{

    @Autowired
    private CfVideoInfoDao cfVideoInfoDao;
    @Override
    public VideoSnapModel getVideoInfoByUserId(String userId) {
        if(Strings.isEmpty(userId)){
            return null;
        }
        CfWxOfficialAccountVideoInfoModel cfWxOfficialAccountVideoInfoModel = cfVideoInfoDao.getVideoInfo(userId);
        VideoSnapModel videoSnapModel = new VideoSnapModel();
        //获取视频号名称及id
        if (Objects.isNull(cfWxOfficialAccountVideoInfoModel)){
            return null;
        }
        videoSnapModel.setNickname(cfWxOfficialAccountVideoInfoModel.getVideoName());
        videoSnapModel.setUsername(cfWxOfficialAccountVideoInfoModel.getVideoId());
        return videoSnapModel;
    }

    @Override
    public int updateOrInsertVideoInfo(String videoNickName, String videoId, int bizType,String userId,String infoUuid) {
       int result =-1;
        if(bizType == 0 ){
            VideoSnapModel videoSnapModel = getVideoInfoByUserId(userId);
            if (Objects.isNull(videoSnapModel)) {
                result = cfVideoInfoDao.insertVideoWithUserId(videoNickName, videoId, bizType, userId);
                return result;
            }//以存在顾问视频号信息，则更新
                 result = cfVideoInfoDao.updateVideoWithUserId(videoNickName, videoId, bizType, userId);
        }else{
            //患者视频号只有新插入信息操作
            result = cfVideoInfoDao.insertVideoWithInfoUuid(videoNickName, videoId, bizType, infoUuid);
        }
        return result;
    }


}
