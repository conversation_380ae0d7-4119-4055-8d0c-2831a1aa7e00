package com.shuidihuzhu.cf.cfgrowthtoolapi.service.volunteer;

import com.shuidihuzhu.cf.cfgrowthtoolapi.model.xrxs.*;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-11-17 5:47 下午
 *
 * 人员同步逻辑
 **/
public interface VolunteerSyncService {

    /**
     * 同步案例发起数据
     */
    void syncBDCaseInfo();


    /**
     * 从薪人薪事获取人员的信息
     */
    void syncMemberInfoFromXrxs();


    /**
     * 同步单个人员的信息
     * @param volunteer
     * @param needPublishEvent
     */
    void syncOneVolunteerFromXrxs(CrowdfundingVolunteer volunteer, Boolean needPublishEvent);


    List<CrowdfundingVolunteer> listAllOnWorkVolunteer();


    List<CrowdfundingVolunteer> getNearlyLeaveVolunteer();

    /**
     * 同步人员状态
     */
    void syncMemberWorkStatus();


    /**
     * 同步所有qr人员,如果转入到其他组,qr身份也对应修改
     */
    void syncAllGrMember();


    void alarmNoRaiseVolunteer();


    void addPartnerToSnapshot(String monthKey);
}
