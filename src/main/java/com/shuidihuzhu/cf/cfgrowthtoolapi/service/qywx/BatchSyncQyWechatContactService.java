package com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.BdCorpFriendDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.qywechat.QyWechatHandlerModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.volunteer.VolunteerSyncService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolUtil;
import com.shuidihuzhu.client.account.wecom.feign.AccessTokenClient;
import com.shuidihuzhu.client.account.wecom.model.Response;
import com.shuidihuzhu.client.account.wecom.model.WxAccessToken;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.qywechat.QywxSdkClient;
import com.shuidihuzhu.client.model.qy.param.BatchGetExternalContactParam;
import com.shuidihuzhu.client.model.qy.response.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2023-03-15 10:51
 **/
@Slf4j
@Service
public class BatchSyncQyWechatContactService {

    @Autowired
    private QywxSdkClient qywxSdkClient;

    @Autowired
    private AccessTokenClient accessTokenClient;

    @Autowired
    private BdCorpFriendService bdCorpFriendService;

    @Autowired
    private QyWechatHandler qyWechatHandler;

    @Autowired
    private VolunteerSyncService volunteerSyncService;

    public static final int LIMIT_CALL_TIMES = 200;


    public void syncAllUser() {
        List<CrowdfundingVolunteer> volunteerList = volunteerSyncService.listAllOnWorkVolunteer();
        qyWechatHandler.handle(volunteerList, null, this::syncContact);
    }


    public void syncSingleVolunteer(CrowdfundingVolunteer volunteer) {
        qyWechatHandler.handle(Lists.newArrayList(volunteer), null, this::syncContact);
    }

    public void syncContact(QyWechatHandlerModel<Void> qyWechatHandlerModel) {
        Map<String, CrowdfundingVolunteer> volunteerMap = qyWechatHandlerModel.getVolunteerMap();
        for (String userId : qyWechatHandlerModel.getUserIds()) {
            CrowdfundingVolunteerEnum.VolunteerCropTypeEnum volunteerCropTypeEnum = qyWechatHandlerModel.getVolunteerCropTypeEnum();
            Response<WxAccessToken> wxAccessTokenResponse = accessTokenClient.getTokenSelfBuildApp(volunteerCropTypeEnum.getCorpId(), volunteerCropTypeEnum.getAppSecret());
            log.info("getTokenSelfBuildApp :{}", wxAccessTokenResponse);
            String accessToken = "";
            if (wxAccessTokenResponse.getData() != null) {
                accessToken = wxAccessTokenResponse.getData().getAccessToken();
            }
            if (StringUtils.isBlank(accessToken)) {
                return;
            }
            String uniqueCode = Optional.ofNullable(volunteerMap.get(userId)).map(CrowdfundingVolunteer::getUniqueCode).orElse("");
            batchSyncContact(userId, volunteerCropTypeEnum.getCorpId(), accessToken, uniqueCode, "", 0);
        }
    }


    //批量获取企业微信客户信息
    public void batchSyncContact(String userId, String corpId, String token, String uniqueCode, String cursor, int callTimes) {
        BatchGetExternalContactParam batchGetExternalContactParam = new BatchGetExternalContactParam();
        batchGetExternalContactParam.setUserid_list(Lists.newArrayList(userId));
        BatchExternalContactResponse batchExternalContactResponse = qywxSdkClient.batchExternalContactByUser(token, batchGetExternalContactParam);
        if (batchExternalContactResponse != null) {
            List<ExternalContactModel> externalContactList = batchExternalContactResponse.getExternal_contact_list();
            if (CollectionUtils.isEmpty(externalContactList)) {
                return;
            }
            List<BdCorpFriendDO> lastFriends = Lists.newArrayList();
            for (ExternalContactModel externalContactModel : externalContactList) {
                String remark = "";
                String addTime = "";
                //保存下备注
                BatchFollowUserVO followInfo = externalContactModel.getFollow_info();
                if (followInfo != null) {
                    //添加时的备注
                    remark = Optional.ofNullable(followInfo.getRemark()).orElse("");
                    //添加时间
                    Long createTime = followInfo.getCreatetime();
                    if (createTime != null && createTime > 0) {
                        addTime = new DateTime(createTime * 1000).toString(GrowthtoolUtil.ymdhmsfmt);
                    }
                }
                //客户信息
                ExternalContactVO externalContact = externalContactModel.getExternal_contact();
                if (externalContact != null && StringUtils.isNotBlank(externalContact.getExternal_userid())) {
                    lastFriends.add(BdCorpFriendDO.builder()
                            .userId(userId)
                            .corpId(corpId)
                            .externalUserId(externalContact.getExternal_userid())
                            .externalContact(JSON.toJSONString(externalContactModel))
                            .contactType(Optional.ofNullable(externalContact.getType()).orElse(1))
                            .contactName(Optional.ofNullable(externalContact.getName()).orElse(""))
                            .contactCorpName(Optional.ofNullable(externalContact.getCorp_name()).orElse(""))
                            .contactCorpFullName(Optional.ofNullable(externalContact.getCorp_full_name()).orElse(""))
                            .remark(remark)
                            .uniqueCode(uniqueCode)
                            .addTime(addTime)
                            .build());
                }
            }
            handleData(userId, corpId, lastFriends);
            //当存在下一条时需要更新,为了防止一直轮训导致数据库压力过大,限制次数,根据最新情况做多不会超过2w,也就是200次
            if (StringUtils.isNotBlank(batchExternalContactResponse.getNext_cursor()) && callTimes <= LIMIT_CALL_TIMES) {
                batchSyncContact(userId, corpId, token, uniqueCode, batchExternalContactResponse.getNext_cursor(), callTimes + 1);
            }
        }
    }


    private void handleData(String userId, String corpId, List<BdCorpFriendDO> lastFriends) {
        //同步数据到表中
        List<BdCorpFriendDO> friendsInDb = bdCorpFriendService.listFriendByUserId(corpId, userId);
        //对比两边的数据
        List<String> externalUserIdsInDb = friendsInDb.stream()
                .map(BdCorpFriendDO::getExternalUserId)
                .collect(Collectors.toList());
        List<String> externalUserIdsLast = lastFriends.stream()
                .map(BdCorpFriendDO::getExternalUserId)
                .collect(Collectors.toList());
        //需要添加的
        ImmutableSet<String> needAddUserIds = Sets.difference(Sets.newHashSet(externalUserIdsLast), Sets.newHashSet(externalUserIdsInDb))
                .immutableCopy();
        ImmutableSet<String> needDeleteUserIds = Sets.difference(Sets.newHashSet(externalUserIdsInDb), Sets.newHashSet(externalUserIdsLast))
                .immutableCopy();
        if (CollectionUtils.isNotEmpty(needAddUserIds)) {
            List<BdCorpFriendDO> needAddFriends = lastFriends.stream()
                    .filter(item -> needAddUserIds.contains(item.getExternalUserId()))
                    .collect(Collectors.toList());
            bdCorpFriendService.batchInsert(needAddFriends);
        }
        if (CollectionUtils.isNotEmpty(needDeleteUserIds)) {
            List<Long> deleteIds = friendsInDb.stream()
                    .filter(item -> needDeleteUserIds.contains(item.getExternalUserId()))
                    .map(BdCorpFriendDO::getId)
                    .collect(Collectors.toList());
            bdCorpFriendService.deleteByIds(deleteIds);
        }
    }


}
