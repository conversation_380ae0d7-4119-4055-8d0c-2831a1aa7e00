package com.shuidihuzhu.cf.cfgrowthtoolapi.service;

import com.shuidihuzhu.cf.domain.dedicated.CfToufangInviteCaseRelationDO;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInvitorVisitDO;
import com.shuidihuzhu.client.cf.growthtool.model.PageReturnModel;

import java.util.List;

public interface ICfToufangInviteService {
    String INVITE_CHANNE="cf_crowdfund_invite";
    /**
     * 添加被邀请人访问记录
     * @param cfToufangInvitorVisitDO
     */
    int add(CfToufangInvitorVisitDO cfToufangInvitorVisitDO);

    /**
     * 根据userId 和 channel 获取关注数据
     * @param userId
     * @param channel
     * @return
     */
    CfToufangInvitorVisitDO getCfToufangInvitorVisitDOByUserId(Long userId, String channel);

    /**
     * 添加被邀请人案例和邀请人的关联关系
     * @param caseRelationDO
     * @return
     */
    int addInviteRelation(CfToufangInviteCaseRelationDO caseRelationDO);

    /**
     * 查询推荐人的关联案例
     * @param userId
     * @return
     */
    List<CfToufangInviteCaseRelationDO> getInviteCaseRelation(long userId);

    /**
     * 校验是否达到今日推荐人计数上限
     * @param sourceUserId
     * @return
     */
    boolean checkIfReachInvitorLimtForSourceUserId(long sourceUserId);

    CfToufangInviteCaseRelationDO getInviteCaseRelationByUserIdAndInfoId(long userId, String infoUuid);

    PageReturnModel<CfToufangInviteCaseRelationDO> listRelationPaging(long userId, int current, int pageSize);
}
