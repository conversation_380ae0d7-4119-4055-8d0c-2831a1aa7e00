package com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm;

import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfCaseBaseDataDo;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CaseStatisticDataDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.OrgDataStatVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.BdCrmDataStatParam;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020-07-03
 */
public interface ICfCaseBaseDataService {

    /**
     * 获取概览数据
     * @param crmDataStatParam
     * @param orgIdList
     * @return
     */
    List<OrgDataStatVO> getOrgOverviewData(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList);

    /**
     * 按人统计数据
     * @param crmDataStatParam
     * @param orgIdList
     * @return
     */
    List<OrgDataStatVO> getOrgDetailData4Person(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList);

    /**
     * 按组织统计数据
     * @param crmDataStatParam
     * @param orgIdList
     * @return
     */
    List<OrgDataStatVO> getOrgDetailData4Org(BdCrmDataStatParam crmDataStatParam, List<Long> orgIdList);

    /**
     * 获取发起数据
     * @param crmDataStatParam
     * @param queryActCityIdList
     * @return
     */
    List<OrgDataStatVO> getOrgOverviewData4Init(BdCrmDataStatParam crmDataStatParam, List<Integer> queryActCityIdList);

    OrgDataStatVO getOrgDetailData4OrgCase(BdCrmDataStatParam crmDataStatParam, List<Integer> queryActCityList);

    OrgDataStatVO getOrgDetailData4Market(BdCrmDataStatParam crmDataStatParam, List<Integer> queryActCityList);

    CfCaseBaseDataDo getByCaseId(int caseId);

    List<CaseStatisticDataDO> getOrgDetailData4OrgCaseAndMarket(Set<Integer> cityIds, String start, String end);
}
