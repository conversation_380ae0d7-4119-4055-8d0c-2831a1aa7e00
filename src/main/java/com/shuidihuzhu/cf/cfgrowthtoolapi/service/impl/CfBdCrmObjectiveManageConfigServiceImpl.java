package com.shuidihuzhu.cf.cfgrowthtoolapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveCycle;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveOrgMemberSnapshot;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmObjectiveIndicatorValueModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmObjectiveStatusBaseModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmObjectiveStatusDetailModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.CfBdCrmTeamObjectiveDTO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.GrowthtoolCrusorQuery;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmObjectiveCycleMapper;
import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmObjectiveOrgMemberSnapshotMapper;
import com.shuidihuzhu.cf.response.OpResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import com.shuidihuzhu.cf.dao.bdcrm.CfBdCrmObjectiveManageConfigMapper;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.CfBdCrmObjectiveManageConfig;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.bdcrm.CfBdCrmObjectiveManageConfigService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2021/2/23 上午10:39
 */
@Service
public class CfBdCrmObjectiveManageConfigServiceImpl implements CfBdCrmObjectiveManageConfigService {

    @Resource
    private CfBdCrmObjectiveManageConfigMapper cfBdCrmObjectiveManageConfigMapper;
    @Resource
    private CfBdCrmObjectiveOrgMemberSnapshotMapper cfBdCrmObjectiveOrgMemberSnapshotMapper;
    @Resource
    private CfBdCrmObjectiveCycleMapper cfBdCrmObjectiveCycleMapper;

    @Override
    public CfBdCrmObjectiveManageConfig getObjectiveManageConfigByCycleIdWithOrgId(Long objectiveCycleId, Long orgId) {
        return cfBdCrmObjectiveManageConfigMapper.getByCycleIdWithOrgId(objectiveCycleId, orgId);
    }
    @Override
    public List<CfBdCrmObjectiveManageConfig> listObjectiveManageConfigByCycleIdListWithOrgIdList(List<Long> objectiveCycleIdList, List<Long> orgIdList) {
        if (CollectionUtils.isEmpty(orgIdList)) return Lists.newArrayList();
        return cfBdCrmObjectiveManageConfigMapper.listByCycleIdListWithOrgIdList(objectiveCycleIdList, orgIdList);
    }

    @Override
    public CfBdCrmObjectiveManageConfig getObjectiveManageConfigByCycleIdWithUniqueCode(Long objectiveCycleId, String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) return null;
        return cfBdCrmObjectiveManageConfigMapper.getByCycleIdWithUniqueCode(objectiveCycleId, uniqueCode);
    }
    @Override
    public List<CfBdCrmObjectiveManageConfig> listObjectiveManageConfigByCycleIdListWithUniqueCode(List<Long> objectiveCycleIdList, String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) return Lists.newArrayList();
        return cfBdCrmObjectiveManageConfigMapper.listByCycleIdListWithUniqueCode(objectiveCycleIdList, uniqueCode);
    }

    @Override
    public CfBdCrmObjectiveStatusBaseModel getMyObjectiveIndicatorValueStatus(List<Long> orgIdList, String uniqueCode, List<CfBdCrmObjectiveCycle> objectiveCycleList, int roleCode) {
        List<Long> objectiveCycleIdList = objectiveCycleList.stream().map(CfBdCrmObjectiveCycle::getId).collect(Collectors.toList());
        List<CfBdCrmObjectiveManageConfig> manageConfigList;
        if (CrowdfundingVolunteerEnum.commonRoles.contains(roleCode)) {
            manageConfigList = cfBdCrmObjectiveManageConfigMapper.listByCycleIdListWithUniqueCode(objectiveCycleIdList, uniqueCode).stream()
                    .filter(cfBdCrmObjectiveManageConfig -> cfBdCrmObjectiveManageConfig.getObjectiveIndicatorValueStatus() == CfBdCrmObjectiveManageConfig.CommitStatusEnum.NO_COMMIT.getStatus())
                    .collect(Collectors.toList());
        }else {
            manageConfigList = CollectionUtils.isEmpty(orgIdList)?Lists.newArrayList():cfBdCrmObjectiveManageConfigMapper.listByCycleIdListWithOrgIdList(objectiveCycleIdList, orgIdList).stream()
                    .filter(cfBdCrmObjectiveManageConfig -> cfBdCrmObjectiveManageConfig.getObjectiveIndicatorValueStatus() == CfBdCrmObjectiveManageConfig.CommitStatusEnum.NO_COMMIT.getStatus())
                    .collect(Collectors.toList());
        }
        return buildCfBdCrmObjectiveStatusBaseModel(manageConfigList, orgIdList);
    }

    @Override
    public CfBdCrmObjectiveStatusBaseModel getTeamObjectiveIndicatorStatus(List<Long> orgIdList, List<CfBdCrmObjectiveCycle> objectiveCycleList, int roleCode) {
        List<Long> objectiveCycleIdList = objectiveCycleList.stream().map(CfBdCrmObjectiveCycle::getId).collect(Collectors.toList());
        List<Long> subOrgIdList = cfBdCrmObjectiveOrgMemberSnapshotMapper.listSubOrgByOrgIdListWithCycleIdList(orgIdList, objectiveCycleIdList);
        List<CfBdCrmObjectiveManageConfig> manageConfigList = CollectionUtils.isEmpty(subOrgIdList)?Lists.newArrayList():cfBdCrmObjectiveManageConfigMapper.listByCycleIdListWithOrgIdList(objectiveCycleIdList, subOrgIdList)
                .stream()
                .filter(cfBdCrmObjectiveManageConfig -> cfBdCrmObjectiveManageConfig.getCommitStatus() == CfBdCrmObjectiveManageConfig.CommitStatusEnum.NO_COMMIT.getStatus())
                .collect(Collectors.toList());
        return buildCfBdCrmObjectiveStatusBaseModel(manageConfigList, subOrgIdList);
    }

    @Override
    public CfBdCrmObjectiveStatusBaseModel getTeamObjectiveIndicatorValueStatus(List<Long> orgIdList, List<CfBdCrmObjectiveCycle> objectiveCycleList, int roleCode) {
        List<Long> objectiveCycleIdList = objectiveCycleList.stream().map(CfBdCrmObjectiveCycle::getId).collect(Collectors.toList());
        List<Long> subOrgIdList = cfBdCrmObjectiveOrgMemberSnapshotMapper.listSubOrgByOrgIdListWithCycleIdList(orgIdList, objectiveCycleIdList);
        List<CfBdCrmObjectiveManageConfig> manageConfigList = CollectionUtils.isEmpty(subOrgIdList)?Lists.newArrayList():cfBdCrmObjectiveManageConfigMapper.listByCycleIdListWithOrgIdList(objectiveCycleIdList, subOrgIdList)
                .stream()
                .filter(cfBdCrmObjectiveManageConfig -> cfBdCrmObjectiveManageConfig.getObjectiveIndicatorValueStatus() == CfBdCrmObjectiveManageConfig.CommitStatusEnum.NO_COMMIT.getStatus())
                .collect(Collectors.toList());
        return buildCfBdCrmObjectiveStatusBaseModel(manageConfigList, subOrgIdList);
    }

    @Override
    public void updateConfigJsonWithCommitStatus(List<Long> orgIdList, List<Long> objectiveCycleIdList, String configJson, int commitStatus) {
        if (CollectionUtils.isEmpty(orgIdList) || CollectionUtils.isEmpty(objectiveCycleIdList)) return;
        objectiveCycleIdList.stream().forEach(objectiveCycleId -> cfBdCrmObjectiveManageConfigMapper.updateConfigJsonWithCommitStatusByOrgIdListWithCycleId(orgIdList, objectiveCycleId, configJson, commitStatus));
    }

    @Override
    public OpResult insertOrUpdateObjectiveManageConfig(List<CfBdCrmObjectiveOrgMemberSnapshot> orgMemberSnapshotList, Long objectiveCycleId, List<CfBdCrmObjectiveIndicatorValueModel> objectiveIndicatorValueModelList) {
        if (CollectionUtils.isEmpty(orgMemberSnapshotList)) return OpResult.createSucResult();
        CfBdCrmObjectiveCycle cfBdCrmObjectiveCycle = cfBdCrmObjectiveCycleMapper.selectByPrimaryKey(objectiveCycleId);
        if (cfBdCrmObjectiveCycle==null) return OpResult.createFailResult(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        List<CfBdCrmObjectiveManageConfig> cfBdCrmObjectiveManageConfigList = orgMemberSnapshotList.stream()
                .filter(orgMemberSnapshot -> StringUtils.isBlank(orgMemberSnapshot.getMis()) || CrowdfundingVolunteerEnum.commonRoles.contains(orgMemberSnapshot.getLevel()))
                .map(orgMemberSnapshot -> new CfBdCrmObjectiveManageConfig(cfBdCrmObjectiveCycle.getId(), cfBdCrmObjectiveCycle.getObjectiveType(),
                        orgMemberSnapshot.getOrgId(), orgMemberSnapshot.getOrgName(), orgMemberSnapshot.getMis(), orgMemberSnapshot.getUniqueCode(),
                        orgMemberSnapshot.getMisName(), orgMemberSnapshot.getNewStaff(), CfBdCrmObjectiveManageConfig.CommitStatusEnum.COMMIT.getStatus(),
                        CfBdCrmObjectiveManageConfig.CommitStatusEnum.NO_COMMIT.getStatus(), JSON.toJSONString(objectiveIndicatorValueModelList))).collect(Collectors.toList());
        List<Long> orgIdList = orgMemberSnapshotList.stream().map(CfBdCrmObjectiveOrgMemberSnapshot::getOrgId).distinct().collect(Collectors.toList());
        List<CfBdCrmObjectiveManageConfig> manageConfigListInDb = cfBdCrmObjectiveManageConfigMapper.listByCycleIdWithOrgIdList(cfBdCrmObjectiveCycle.getId(), orgIdList);
        List<String> uniqueKeyListInDb = manageConfigListInDb.stream().map(CfBdCrmObjectiveManageConfig::showUniqueKey).collect(Collectors.toList());
        List<CfBdCrmObjectiveManageConfig> needInsertList = cfBdCrmObjectiveManageConfigList.stream().filter(objectiveManageConfig -> !uniqueKeyListInDb.contains(objectiveManageConfig.showUniqueKey())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needInsertList)) {
            cfBdCrmObjectiveManageConfigMapper.batchInsertObjectiveManageConfig(needInsertList);
        }
        if (CollectionUtils.isNotEmpty(manageConfigListInDb)) {
            cfBdCrmObjectiveManageConfigMapper.updateConfigJsonWithCommitStatusByIdList(manageConfigListInDb.stream().map(CfBdCrmObjectiveManageConfig::getId).collect(Collectors.toList()),
                    JSON.toJSONString(objectiveIndicatorValueModelList), CfBdCrmObjectiveManageConfig.CommitStatusEnum.COMMIT.getStatus());
        }
        return OpResult.createSucResult();
    }

    @Override
    public void batchInsertObjectiveManageConfig(List<CfBdCrmObjectiveManageConfig> cfBdCrmObjectiveManageConfigList){
        cfBdCrmObjectiveManageConfigMapper.batchInsertObjectiveManageConfig(cfBdCrmObjectiveManageConfigList);
    }

    @Override
    public void insertOrUpdateObjectiveManageConfig(List<CfBdCrmObjectiveManageConfig> manageConfigList, CfBdCrmObjectiveCycle objectiveCycle) {
        Lists.partition(manageConfigList,100).stream().forEach(list -> {
            List<String> uniqueKeyList = cfBdCrmObjectiveManageConfigMapper.listByCycleIdWithOrgIdList(objectiveCycle.getId(),
                    list.stream().map(CfBdCrmObjectiveManageConfig::getOrgId).collect(Collectors.toList()))
                    .stream().map(CfBdCrmObjectiveManageConfig::showUniqueKey).collect(Collectors.toList());
            list = list.stream().filter(item -> !uniqueKeyList.contains(item.showUniqueKey())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            cfBdCrmObjectiveManageConfigMapper.batchInsertObjectiveManageConfigWithCycleId(list, objectiveCycle.getId(),objectiveCycle.getObjectiveType());
        });
    }

    @Override
    public List<Long> listObjectiveCycleIdByOrgIdListWithObjectiveType(List<Long> subOrgIdList, Integer objectiveType) {
        if (CollectionUtils.isEmpty(subOrgIdList)) return Lists.newArrayList();
        return cfBdCrmObjectiveManageConfigMapper.listObjectiveCycleIdByOrgIdListWithObjectiveType(subOrgIdList, objectiveType);
    }

    @Override
    public List<CfBdCrmObjectiveManageConfig> listObjectiveManageConfigByOrgIdList(List<Long> subOrgIdList, Integer objectiveType, List<Long> objectiveCycleIdList) {
        if (CollectionUtils.isEmpty(subOrgIdList) || CollectionUtils.isEmpty(objectiveCycleIdList)) return Lists.newArrayList();
        return cfBdCrmObjectiveManageConfigMapper.listByOrgIdWithObjectiveType(subOrgIdList, objectiveType, objectiveCycleIdList);
    }

    @Override
    public long countObjectiveManageConfigByUniqueCode(String uniqueCode, Integer objectiveType) {
        return cfBdCrmObjectiveManageConfigMapper.countObjectiveManageConfigByUniqueCode(uniqueCode, objectiveType);
    }

    @Override
    public List<CfBdCrmObjectiveManageConfig> listObjectiveManageConfigByUniqueCode(String uniqueCode, Integer objectiveType, Integer pageSize) {
        return cfBdCrmObjectiveManageConfigMapper.listByUniqueCodeWithObjectiveType(uniqueCode, objectiveType, pageSize);
    }

    @Override
    public int updateConfigJsonWithObjectiveIndicatorValueStatus(Long objectiveCycleId, String uniqueCode, List<CfBdCrmObjectiveIndicatorValueModel> valueModelList) {
        boolean unCommit = valueModelList.stream().anyMatch(indicatorValueModel -> indicatorValueModel.getObjectiveValue() == null);
        return cfBdCrmObjectiveManageConfigMapper.updateObjectiveIndicatorValueStatusByCycleIdWithUniqueCode(objectiveCycleId, uniqueCode, JSON.toJSONString(valueModelList), unCommit? CfBdCrmObjectiveManageConfig.CommitStatusEnum.NO_COMMIT.getStatus(): CfBdCrmObjectiveManageConfig.CommitStatusEnum.COMMIT.getStatus());
    }
    @Override
    public int updateConfigJsonWithObjectiveIndicatorValueStatus(Long objectiveCycleId, Long orgId,List<CfBdCrmObjectiveIndicatorValueModel> valueModelList) {
        boolean unCommit = valueModelList.stream().anyMatch(indicatorValueModel -> indicatorValueModel.getObjectiveValue() == null);
        return cfBdCrmObjectiveManageConfigMapper.updateObjectiveIndicatorValueStatusByCycleIdWithOrgId(objectiveCycleId, orgId, JSON.toJSONString(valueModelList), unCommit? CfBdCrmObjectiveManageConfig.CommitStatusEnum.NO_COMMIT.getStatus(): CfBdCrmObjectiveManageConfig.CommitStatusEnum.COMMIT.getStatus());
    }

    @Override
    public List<CfBdCrmObjectiveManageConfig> listObjectiveManageConfigByCycleIdWithUniqueCodeList(Long objectiveCycleId, Collection<String> uniqueCodeList) {
        if (CollectionUtils.isEmpty(uniqueCodeList)) return Lists.newArrayList();
        return cfBdCrmObjectiveManageConfigMapper.listByCycleIdWithOrgIdWithUniqueCodeList(objectiveCycleId, uniqueCodeList);
    }

    @Override
    public int batchUpdateObjectiveIndicatorValueStatus(List<CfBdCrmObjectiveManageConfig> objectiveManageConfigList) {
        return cfBdCrmObjectiveManageConfigMapper.batchUpdateObjectiveIndicatorValueStatus(objectiveManageConfigList);
    }

    @Override
    public List<CfBdCrmObjectiveManageConfig> listByCycleIdWithOrgIdList(Long objectiveCycleId, List<Long> orgIdList) {
        if (CollectionUtils.isEmpty(orgIdList)) return Lists.newArrayList();
        return cfBdCrmObjectiveManageConfigMapper.listByCycleIdWithOrgIdList(objectiveCycleId, orgIdList);
    }

    @Override
    public List<CfBdCrmObjectiveManageConfig> listOrgObjectiveManageConfigByOrgIdListWithObjectiveType(List<Long> orgIdList, Integer objectiveType) {
        if (CollectionUtils.isEmpty(orgIdList)) return Lists.newArrayList();
        List<Long> idList = cfBdCrmObjectiveManageConfigMapper.listLatelyRecordIdGroupByOrgId(orgIdList, objectiveType, CfBdCrmObjectiveManageConfig.CommitStatusEnum.COMMIT.getStatus());
        if (CollectionUtils.isEmpty(idList)) return Lists.newArrayList();
        return cfBdCrmObjectiveManageConfigMapper.listByIdList(idList);
    }

    @Override
    public void updateObjectiveIndicatorValueStatus(Long objectiveCycleId, List<Long> orgIdList) {
        if (CollectionUtils.isEmpty(orgIdList)) return;
        cfBdCrmObjectiveManageConfigMapper.updateObjectiveIndicatorValueStatus(objectiveCycleId, orgIdList);
    }

    @Override
    public List<Long> listUnCommitOrgIdByCycleId(Long objectiveCycleId) {
        return cfBdCrmObjectiveManageConfigMapper.listUnCommitOrgIdByCycleId(objectiveCycleId);
    }

    @Override
    public void updateIsDelete(List<Long> objectiveCycleIdList, String uniqueCode,Long orgId, int isDelete) {
        if (StringUtils.isBlank(uniqueCode)) return;
        cfBdCrmObjectiveManageConfigMapper.updateIsDelete(objectiveCycleIdList, uniqueCode, orgId, isDelete);
    }

    @Override
    public void updateIsDelete(List<Long> orgIdList, int isDelete) {
        if (CollectionUtils.isEmpty(orgIdList)) return;
        cfBdCrmObjectiveManageConfigMapper.updateIsDeleteByOrgIdList(orgIdList, isDelete);
    }

    @Override
    public List<CfBdCrmObjectiveManageConfig> listByCycleIdWithUniqueCodeList(Long objectiveCycleId, List<String> uniqueCodeList) {
        if (CollectionUtils.isEmpty(uniqueCodeList)) return Lists.newArrayList();
        return cfBdCrmObjectiveManageConfigMapper.listByCycleIdWithUniqueCodeList(objectiveCycleId, uniqueCodeList);
    }

    @Override
    public void updateOrgNameWithCycleIdList(List<Long> objectiveCycleIdList, String newOrgName, long orgId) {
        if (CollectionUtils.isEmpty(objectiveCycleIdList)) return;
        cfBdCrmObjectiveManageConfigMapper.updateOrgNameWithCycleIdList(objectiveCycleIdList, newOrgName, orgId);
    }

    @Async
    @Override
    public void coverConfigJsonForTopOrg(long cycleId, long orgId) {
        if (orgId!=GeneralConstant.CRM_ORG_ID) {
            CfBdCrmObjectiveOrgMemberSnapshot orgMemberSnapshot = cfBdCrmObjectiveOrgMemberSnapshotMapper.getByCycleIdWithOrgId(cycleId, orgId);
            if (orgMemberSnapshot==null || orgMemberSnapshot.getParentOrgId()!= GeneralConstant.CRM_ORG_ID) return;
        }
        List<Long> orgIdList = cfBdCrmObjectiveOrgMemberSnapshotMapper.listSubOrgIdByCycleIdWithOrgId(cycleId, Long.valueOf(GeneralConstant.CRM_ORG_ID));
        List<CfBdCrmObjectiveManageConfig> objectiveManageConfigList = cfBdCrmObjectiveManageConfigMapper.listByCycleIdWithOrgIdList(cycleId, orgIdList).stream().filter(item -> StringUtils.isBlank(item.getMis())).collect(Collectors.toList());
        Map<Integer, List<CfBdCrmObjectiveIndicatorValueModel>> indicatorIdMapList = objectiveManageConfigList.stream().map(CfBdCrmObjectiveManageConfig::getConfigJsonModelList).flatMap(list -> list.stream()).collect(Collectors.groupingBy(CfBdCrmObjectiveIndicatorValueModel::getObjectiveIndicatorId));
        CfBdCrmObjectiveManageConfig topOrgManageConfig = cfBdCrmObjectiveManageConfigMapper.getByCycleIdWithOrgId(cycleId, Long.valueOf(GeneralConstant.CRM_ORG_ID));
        List<CfBdCrmObjectiveIndicatorValueModel> valueModels = Lists.newArrayList();
        for (Map.Entry<Integer, List<CfBdCrmObjectiveIndicatorValueModel>> entry:indicatorIdMapList.entrySet()) {
            CfBdCrmObjectiveIndicatorValueModel valueModel = new CfBdCrmObjectiveIndicatorValueModel();
            valueModel.setObjectiveIndicatorId(entry.getKey());
            valueModel.setObjectiveValue(Optional.ofNullable(entry.getValue()).orElse(Lists.newArrayList()).stream().map(CfBdCrmObjectiveIndicatorValueModel::getObjectiveValue).filter(item -> item!=null && item>=0).reduce((total, item)->total+=item).orElse(null));
            valueModels.add(valueModel);
        }
        topOrgManageConfig.setObjectiveIndicatorValueStatus(CfBdCrmObjectiveManageConfig.CommitStatusEnum.COMMIT.getStatus());
        topOrgManageConfig.setConfigJson(JSON.toJSONString(valueModels));
        cfBdCrmObjectiveManageConfigMapper.batchUpdateObjectiveIndicatorValueStatus(Lists.newArrayList(topOrgManageConfig));
    }


    /************************** private method ***************************/

    private CfBdCrmObjectiveStatusBaseModel buildCfBdCrmObjectiveStatusBaseModel(List<CfBdCrmObjectiveManageConfig> manageConfigList, List<Long> orgIdList){
        CfBdCrmObjectiveStatusBaseModel cfBdCrmObjectiveStatusBaseModel = new CfBdCrmObjectiveStatusBaseModel();
        CfBdCrmObjectiveStatusDetailModel weekObjectiveStatusModel = CfBdCrmObjectiveStatusDetailModel.builder().objectiveType(CfBdCrmObjectiveCycle.ObjectiveTypeEnum.WEEK_OBJECTIVE.getType()).build();
        CfBdCrmObjectiveStatusDetailModel monthObjectiveStatusModel = CfBdCrmObjectiveStatusDetailModel.builder().objectiveType(CfBdCrmObjectiveCycle.ObjectiveTypeEnum.MONTH_OBJECTIVE.getType()).build();
        cfBdCrmObjectiveStatusBaseModel.setWeekObjectiveStatusModel(this.buildCfBdCrmObjectiveStatusDetailModel(weekObjectiveStatusModel, CfBdCrmObjectiveCycle.ObjectiveTypeEnum.WEEK_OBJECTIVE, manageConfigList, orgIdList));
        cfBdCrmObjectiveStatusBaseModel.setMonthObjectiveStatusModel(this.buildCfBdCrmObjectiveStatusDetailModel(monthObjectiveStatusModel, CfBdCrmObjectiveCycle.ObjectiveTypeEnum.MONTH_OBJECTIVE, manageConfigList, orgIdList));
        return cfBdCrmObjectiveStatusBaseModel;
    }

    public CfBdCrmObjectiveStatusDetailModel buildCfBdCrmObjectiveStatusDetailModel(CfBdCrmObjectiveStatusDetailModel objectiveStatusModel,
                                                                                    CfBdCrmObjectiveCycle.ObjectiveTypeEnum objectiveTypeEnum,
                                                                                    List<CfBdCrmObjectiveManageConfig> manageConfigList,
                                                                                    List<Long> orgIdList){
        Long orgId = 0L;
        if (CollectionUtils.isNotEmpty(orgIdList)) {
            orgId = orgIdList.get(0);
        }
        Map<Integer, List<CfBdCrmObjectiveManageConfig>> objectiveTypeMapManageConfigList = manageConfigList.stream().collect(Collectors.groupingBy(CfBdCrmObjectiveManageConfig::getObjectiveType));
        if (CollectionUtils.isNotEmpty(manageConfigList)) {
            List<CfBdCrmObjectiveManageConfig> objectiveManageConfigList = objectiveTypeMapManageConfigList.get(objectiveTypeEnum.getType());
            objectiveStatusModel.setCommitStatus(CollectionUtils.isNotEmpty(objectiveManageConfigList) ? CfBdCrmObjectiveManageConfig.CommitStatusEnum.NO_COMMIT.getStatus() : CfBdCrmObjectiveManageConfig.CommitStatusEnum.COMMIT.getStatus());
            objectiveStatusModel.setOrgId(CollectionUtils.isNotEmpty(objectiveManageConfigList) ? objectiveManageConfigList.get(0).getOrgId() : orgId);
        } else {
            objectiveStatusModel.setCommitStatus(CfBdCrmObjectiveManageConfig.CommitStatusEnum.COMMIT.getStatus());
            objectiveStatusModel.setOrgId(orgId);
        }
        return objectiveStatusModel;
    }
}
