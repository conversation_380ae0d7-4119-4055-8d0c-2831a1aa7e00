package com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.check.organization;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationMemberOptEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.OrgOptParam;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum.excludeOwnOrg;
import static com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum.excludePartnerOrg;

/**
 * @author: fengxuan
 * @create 2020-05-13 10:38
 **/
@Service
@Slf4j
public class MoveOrgLimitImpl extends AbstractOptOrgLimit {

    @Override
    public Response<Boolean> doCanOptCheck(OrgOptParam orgOptParam) {
        log.info("开始移动组织校验");
        Response<Boolean> basicAdd = basicAdd(orgOptParam);
        if (basicAdd.notOk()) {
            return basicAdd;
        }
        //当前组织要存在
        Optional<BdCrmOrganizationDO> currentOrgOpt = isExistOrgInfo(orgOptParam.getCurrentOptOrg().getId());
        if (currentOrgOpt.isEmpty()) {
            return NewResponseUtil.makeFail("移动组织时,移动组织id错误");
        }
        BdCrmOrganizationDO currentOrg = currentOrgOpt.get();
        // ***将currentOrg填充到orgOptParam中***
        orgOptParam.setCurrentOptOrg(currentOrg);
        //当前节点不能为顶层节点
        if (currentOrg.getParentId() == 0) {
            return NewResponseUtil.makeFail("当前组织为最高层级，不可以移动");
        }
        //当前节点不能移动到自己本身
        BdCrmOrganizationDO addToOrg = orgOptParam.getAddToOrg();
        if (currentOrg.getId() == addToOrg.getId()) {
            return NewResponseUtil.makeFail(String.format("%s已存在，不可以移动", currentOrg.getOrgName()));
        }
        //当前节点不能移动到自己的子节点下
        List<Long> subOrgIds = crmOrganizationService.listAllSubOrgExcludeSelf(currentOrg.getId())
                .stream()
                .map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toList());
        if (subOrgIds.contains(addToOrg.getId())) {
            log.info("{}不能移动到子节点{}下", currentOrg.getOrgName(), addToOrg.getOrgName());
            return NewResponseUtil.makeFail(String.format("%s不能移动到子节点%s下", currentOrg.getOrgName(), addToOrg.getOrgName()));
        }
        //当前节点移动到节点最好不要在原来的父节点上
        if (currentOrg.getParentId() == addToOrg.getId()) {
            return NewResponseUtil.makeFail(String.format("%s不能移动到同路径下", currentOrg.getOrgName()));
        }
        //当前组织移动到的新组织不能有重名的
        boolean presentSameName = allOrg.stream()
                .filter(item -> item.getParentId() == addToOrg.getId())
                .anyMatch(item -> ObjectUtils.nullSafeEquals(item.getOrgName(), currentOrg.getOrgName()));
        if (presentSameName) {
            return NewResponseUtil.makeFail(String.format("已经存在同名的组织:%s", currentOrg.getOrgName()));
        }
        List<Long> partnerOrgIds = crmOrganizationService.listAllPartnerOrg();
        List<Long> ownOrgIds = crmOrganizationService.listAllOwnOrg();

        Response<Boolean> response = NewResponseUtil.makeSuccess(true);
        subOrgIds.add(currentOrg.getId());
        //从蜂鸟上移动到正式组织，需要判断人员职级是否已经都修改了
        if (partnerOrgIds.contains(currentOrg.getId()) && ownOrgIds.contains(addToOrg.getId())){
            response = containExcludeRole(subOrgIds,excludeOwnOrg,String.format("该组织下包含%s角色，不能直接移动，先修改人员角色再进行移动操作",
                    excludeOwnOrg.stream().map(CrowdfundingVolunteerEnum.RoleEnum::getDesc).collect(Collectors.joining(","))));
            if (response.notOk()){
                return response;
            }
        }
        //从直营组织移动蜂鸟组织，需要判断人员职级是否已经都修改了
        if (ownOrgIds.contains(currentOrg.getId()) && partnerOrgIds.contains(addToOrg.getId())){
            response = containExcludeRole(subOrgIds, excludePartnerOrg, String.format("该组织下包含%s角色，不能直接移动，先修改人员角色再进行移动操作",
                    excludePartnerOrg.stream().map(CrowdfundingVolunteerEnum.RoleEnum::getDesc).collect(Collectors.joining(","))));
            if (response.notOk()) {
                return response;
            }
        }
        return response;
    }

    /**
     * 判断组织下的所有人，都满足对应职级要求
     * @param orgIds
     * @param excludeRoleList
     * @param errMsg
     * @return
     */
    private Response<Boolean> containExcludeRole(List<Long> orgIds, List<CrowdfundingVolunteerEnum.RoleEnum> excludeRoleList,String errMsg) {
        List<BdCrmOrgUserRelationDO> allUser = organizationRelationService.listByOrgIds(orgIds);
        if (CollectionUtils.isEmpty(allUser)){
            return NewResponseUtil.makeSuccess(true);
        }
        List<CrowdfundingVolunteer> volunteerList = volunteerService.getCfVolunteerDOByUniqueCodes(allUser.stream().map(BdCrmOrgUserRelationDO::getUniqueCode).distinct().collect(Collectors.toList()));
        boolean result = volunteerList.stream().anyMatch(item ->{
             CrowdfundingVolunteerEnum.RoleEnum roleEnum = CrowdfundingVolunteerEnum.RoleEnum.parse(item.getLevel());
             return excludeRoleList.contains(roleEnum);
        });
        return result ? NewResponseUtil.makeFail(errMsg) : NewResponseUtil.makeSuccess(true);
    }

    @Override
    public List<OrganizationMemberOptEnum> getNeedCheckOptEnums() {
        return Lists.newArrayList(OrganizationMemberOptEnum.move_node);
    }

}
