package com.shuidihuzhu.cf.cfgrowthtoolapi.fallback;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.cfgrowthtoolapi.delegate.IQywxSdkDelegate;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.qywx.QywxCallbackService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.qywechat.AesException;
import com.shuidihuzhu.cf.cfgrowthtoolapi.util.qywechat.WXBizMsgCrypt;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewQyWxCorpDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * @author: wanghui
 * @create: 2019/5/27 12:08 PM
 */
@Controller
@Slf4j
public class QyWechatFallback {

    public static final String CORPID = "wwb8cc2f5c0fc58917";
    public static final String TOKEN = "T0NViSyv4lm11bXFD9UEsR4r6PsDe";
    public static final String ENCODING_AES_KEY = "Pp1pVbyqm89AtxvWypgSGIUqwpXktshYAaokPCgcjvs";

    @Autowired
    private QywxCallbackService qywxCallbackService;

    @Autowired
    private IQywxSdkDelegate qywxSdkDelegate;


    /**
     * 客户群事件：https://work.weixin.qq.com/api/doc/90000/90135/92130
     * 企业客户事件（外部联系人事件）：https://work.weixin.qq.com/api/doc/90000/90135/92130
     * 成员事件：https://work.weixin.qq.com/api/doc/90000/90135/90970
     * @param request
     * @param echostr
     * @param msgSig
     * @param timestamp
     * @param nonce
     * @return
     */
    @RequestMapping("/api/growthtool/common-fallback")
    @ResponseBody
    @Deprecated
    public String commonFallback(HttpServletRequest request,
                                 @RequestParam(name = "echostr",required = false) String echostr,
                                 @RequestParam(name = "msg_signature",required = false) String msgSig,
                                 @RequestParam(name = "timestamp",required = false) String timestamp,
                                 @RequestParam(name = "nonce",required = false) String nonce) throws IOException {

        InputStream inputStream = request.getInputStream();
        String postData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        log.info("{}, param:{} , body:{}",request.getRequestURL().toString(), JSON.toJSONString(request.getParameterMap()), postData);
        postData = postData.replaceAll("u003c","<").replaceAll("u003e",">");
        log.info("decodeReqData:{}",postData);
        WXBizMsgCrypt wxcpt = null;
        try {
            wxcpt = new WXBizMsgCrypt(TOKEN, ENCODING_AES_KEY, CORPID);
        } catch (AesException e) {
            log.error(this.getClass().getSimpleName()+" new WXBizMsgCrypt err:",e);
        }
        // 验证URL有效性
        if (StringUtils.isNotEmpty(echostr)){
            return this.verifyURL(wxcpt,echostr, msgSig, timestamp, nonce);
        }else {
            //历史逻辑，只有水滴筹主体会调用此接口
            //根据callbackId = 112 获取CfClewWxCorpMsgDO
            CfClewQyWxCorpDO cfClewQyWxCorpDO = qywxSdkDelegate.getWxCorpMsgByCallbackId(112);
            if (cfClewQyWxCorpDO == null){
                return "";
            }
            return qywxCallbackService.handleEventByInterface(wxcpt, msgSig, timestamp, nonce, postData, cfClewQyWxCorpDO);
        }

    }

    @RequestMapping("/api/growthtool/qywx-fallback/{callbackId}")
    @ResponseBody
    @Deprecated
    public String commonFallback(HttpServletRequest request,
                                 @PathVariable("callbackId") int callbackId,
                                 @RequestParam(name = "echostr",required = false) String echostr,
                                 @RequestParam(name = "msg_signature",required = false) String msgSig,
                                 @RequestParam(name = "timestamp",required = false) String timestamp,
                                 @RequestParam(name = "nonce",required = false) String nonce) throws IOException {
        //根据callbackId 获取CfClewWxCorpMsgDO
        CfClewQyWxCorpDO cfClewQyWxCorpDO = qywxSdkDelegate.getWxCorpMsgByCallbackId(callbackId);
        if (cfClewQyWxCorpDO == null){
            return "";
        }
        InputStream inputStream = request.getInputStream();
        String postData = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        log.info("{}, param:{} , body:{}",request.getRequestURL().toString(), JSON.toJSONString(request.getParameterMap()), postData);
        postData = postData.replaceAll("u003c","<").replaceAll("u003e",">");
        log.info("decodeReqData:{}",postData);
        WXBizMsgCrypt wxcpt = null;
        try {
            wxcpt = new WXBizMsgCrypt(cfClewQyWxCorpDO.getToken(), cfClewQyWxCorpDO.getAesKey(), cfClewQyWxCorpDO.getCorpId());
        } catch (AesException e) {
            log.error(this.getClass().getSimpleName()+" new WXBizMsgCrypt err:",e);
        }
        // 验证URL有效性
        if (StringUtils.isNotEmpty(echostr)){
            return this.verifyURL(wxcpt,echostr, msgSig, timestamp, nonce);
        }else {
            return qywxCallbackService.handleEventByInterface(wxcpt, msgSig, timestamp, nonce, postData, cfClewQyWxCorpDO);
        }

    }

    @Deprecated
    public String verifyURL(WXBizMsgCrypt wxcpt,String echostr,String msg_signature,String timestamp,String nonce){
        String sEchoStr = null;
        try {
            sEchoStr = wxcpt.VerifyURL(msg_signature, timestamp,
                    nonce, echostr);
            log.info("verifyurl echostr: {}", sEchoStr);
            // 验证URL成功，将sEchoStr返回
            // HttpUtils.SetResponse(sEchoStr);
        } catch (Exception e) {
            //验证URL失败，错误原因请查看异常
            log.error(this.getClass().getSimpleName()+" commonFallback err:",e);
        }
        return sEchoStr;
    }

}
