package com.shuidihuzhu.cf.controller.api.admin;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.cfgrowthtoolapi.constants.GeneralConstant;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.CfOperatingRecordDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrgUserRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.BdCrmOrganizationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.domain.bdcrm.PreVolunteerOrgInfoRelationDO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CfGrowthtoolErrorCode;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.CrowdfundingVolunteerEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OperateTypeEnum;
import com.shuidihuzhu.cf.cfgrowthtoolapi.enums.OrganizationUserEnums;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.CrowdfundingVolunteerAndOrgModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.BdCfCommonPageModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.bdcrm.vo.VolunteerOrgRelationVO;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.organization.BdCrmMemberModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.model.weapon.WeaponOptLogModel;
import com.shuidihuzhu.cf.cfgrowthtoolapi.param.OperatingRecordQueryParam;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.IOperateLogService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.cfgrowthtoolapi.service.smallWhale.organization.*;
import com.shuidihuzhu.cf.dao.bdcrm.PreVolunteerOrgInfoRelationDao;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerSearchModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2020-10-30 4:34 下午
 **/
@RestController
@RequestMapping("admin/cf/growthtool/volunteer")
public class AdminVolunteerController {

    @Autowired
    private ICfVolunteerService cfVolunteerService;

    @Autowired
    private ICrmSelfBuiltOrgReadService crmSelfBuiltOrgReadService;

    @Autowired
    private ICrmOrganizationRelationService relationService;

    @Autowired
    private ICrmMemberInfoService crmMemberInfoService;

    @Autowired
    private PreVolunteerOrgInfoRelationDao preVolunteerOrgInfoRelationDao;

    @Autowired
    private IOperateLogService operateLogService;

    @ApiOperation("模糊匹配顾问")
    @PostMapping("get-volunteer")
    public Response<List<CrowdfundingVolunteerAndOrgModel>> getVolunteer(@RequestParam(name = "mis") String misOrOrgName) {
        List<CrowdfundingVolunteer> volunteers = cfVolunteerService.fuzzyQueryByMis(misOrOrgName);
        List<CrowdfundingVolunteer> partnerManagerList = cfVolunteerService.getXianXiaVolunteerListByRoles(Lists.newArrayList(CrowdfundingVolunteerEnum.RoleEnum.PARTNER_MANAGER.getLevel()));
        //只返回正式顾问的
        List<CrowdfundingVolunteer> normalVolunteerList = volunteers
                .stream()
                .filter(item -> CrowdfundingVolunteerEnum.commonRoles.contains(item.getLevel()) || item.getLevel()==CrowdfundingVolunteerEnum.RoleEnum.PARTNER_MANAGER.getLevel())
                .filter(item -> item.getAccountStatus() == CrowdfundingVolunteerEnum.AccountStatusEnum.DEFAULT.getValue())
                .filter(item -> Objects.equals(item.getPartnerTag(), CrowdfundingVolunteerEnum.PartnerTagEnum.normal.getCode()))
                .collect(Collectors.toList());

        normalVolunteerList.addAll(partnerManagerList);
        List<CrowdfundingVolunteerAndOrgModel> volunteerAndOrgModelList = Lists.newArrayList();
        //找到这些人所在的组织
        for (CrowdfundingVolunteer volunteer : normalVolunteerList) {
            CrowdfundingVolunteerAndOrgModel volunteerAndOrgModel = buildByCrowdfundingVolunteer(volunteer);
            if (volunteerAndOrgModel != null) {
                volunteerAndOrgModelList.add(volunteerAndOrgModel);
            }
        }

        //通过组织名称查找顾问
        List<Long> orgIds = crmSelfBuiltOrgReadService.getAllOrg()
                .stream()
                .filter(item -> item.getOrgAttribute() == OrganizationUserEnums.OrgNodeAttributeEnum.leaf.getCode())
                .filter(item -> item.getOrgName().contains(misOrOrgName))
                .map(BdCrmOrganizationDO::getId)
                .collect(Collectors.toList());

        List<BdCrmOrgUserRelationDO> relationDOList = relationService.listByOrgIdsFromDB(orgIds);
        List<CrowdfundingVolunteerAndOrgModel> volunteerQueryByOrgName = buildByRelationList(relationDOList);
        volunteerAndOrgModelList.addAll(volunteerQueryByOrgName);

        //去重
        Map<String, CrowdfundingVolunteerAndOrgModel> uniqueCodeToModel = volunteerAndOrgModelList.stream()
                .collect(Collectors.toMap(CrowdfundingVolunteerAndOrgModel::getUniqueCode, Function.identity(), (before, after) -> before));

        //设置路径
        List<CrowdfundingVolunteerAndOrgModel> result = Lists.newArrayList(uniqueCodeToModel.values());
        List<Long> uniqueCodeOrgIds = result.stream().map(CrowdfundingVolunteerAndOrgModel::getOrgId).collect(Collectors.toList());
        Map<Long, String> orgIdTPath = crmSelfBuiltOrgReadService.listChainByOrgIdsWithDefaultSplitter(uniqueCodeOrgIds);
        for (CrowdfundingVolunteerAndOrgModel volunteerAndOrgModel : result) {
            String orgPath = orgIdTPath.getOrDefault(volunteerAndOrgModel.getOrgId(), "");
            volunteerAndOrgModel.setOrgPath(orgPath);
        }
        return NewResponseUtil.makeSuccess(result);
    }


    private CrowdfundingVolunteerAndOrgModel buildByCrowdfundingVolunteer(CrowdfundingVolunteer volunteer) {
        if (volunteer == null) {
            return null;
        }
        CrowdfundingVolunteerAndOrgModel volunteerAndOrgModel = new CrowdfundingVolunteerAndOrgModel(volunteer);
        List<BdCrmOrgUserRelationDO> relationDOList = relationService.listMemberOrgRelationByUniqueCode(volunteerAndOrgModel.getUniqueCode());
        if (CollectionUtils.isEmpty(relationDOList)) {
            return null;
        }
        //顾问只会在一个组织下,所以取第一个就行
        long orgId = relationDOList.get(0).getOrgId();
        volunteerAndOrgModel.setOrgId(orgId);
        return volunteerAndOrgModel;
    }


    private List<CrowdfundingVolunteerAndOrgModel> buildByRelationList(List<BdCrmOrgUserRelationDO> relationDOList) {
        if (CollectionUtils.isEmpty(relationDOList)) {
            return Lists.newArrayList();
        }
        List<CrowdfundingVolunteerAndOrgModel> result = Lists.newArrayList();
        Map<String, BdCrmOrgUserRelationDO> uniqueCodeTRelation = relationDOList.stream().collect(Collectors.toMap(BdCrmOrgUserRelationDO::getUniqueCode, Function.identity(), (before, after) -> before));
        List<String> uniqueCodes = relationDOList
                .stream()
                .map(BdCrmOrgUserRelationDO::getUniqueCode)
                .collect(Collectors.toList());
        List<CrowdfundingVolunteer> volunteerList = Lists.newArrayList();
        Lists.partition(uniqueCodes, 500)
                .forEach(item -> volunteerList.addAll(cfVolunteerService.getCfVolunteerDOByUniqueCodes(item)));
        for (CrowdfundingVolunteer volunteer : volunteerList) {
            CrowdfundingVolunteerAndOrgModel volunteerAndOrgModel = new CrowdfundingVolunteerAndOrgModel(volunteer);
            BdCrmOrgUserRelationDO relationDO = uniqueCodeTRelation.get(volunteerAndOrgModel.getUniqueCode());
            //只加在组织上的且非兼职
            if (relationDO != null && Objects.equals(volunteer.getPartnerTag(), CrowdfundingVolunteerEnum.PartnerTagEnum.normal.getCode())) {
                volunteerAndOrgModel.setOrgId(relationDO.getOrgId());
                result.add(volunteerAndOrgModel);
            }
        }
        return result;
    }


    //查找gps上报人员信息
    @ApiOperation("根据人员模糊查询")
    @PostMapping("list-by-mis-name")
    public Response<List<CrowdfundingVolunteerAndOrgModel>> listByMisName(@RequestParam String misName) {
        if (StringUtils.isBlank(misName)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        VolunteerSearchModel volunteerSearchModel = new VolunteerSearchModel();
        volunteerSearchModel.setVolunteerType(1);
        volunteerSearchModel.setVolunteerName(misName);

        List<CrowdfundingVolunteer> volunteerList = cfVolunteerService.getVolunteer(volunteerSearchModel, false, null, null)
                .getList();
        return NewResponseUtil.makeSuccess(listVolunteerInfo(volunteerList));

    }

    @NotNull
    private List<CrowdfundingVolunteerAndOrgModel> listVolunteerInfo(List<CrowdfundingVolunteer> volunteerList) {
        //找到人员组织
        Map<String, BdCrmMemberModel> crmMemberModelMap = crmMemberInfoService.listByUniqueCodes(volunteerList.stream().map(CrowdfundingVolunteer::getUniqueCode).collect(Collectors.toList()));

        return volunteerList.stream()
                .map(item -> {
                    CrowdfundingVolunteerAndOrgModel model = new CrowdfundingVolunteerAndOrgModel();
                    model.setUniqueCode(item.getUniqueCode());
                    model.setMis(item.getMis());
                    model.setVolunteerName(item.getVolunteerName());
                    BdCrmMemberModel bdCrmMemberModel = crmMemberModelMap.get(item.getUniqueCode());
                    if (bdCrmMemberModel != null) {
                        model.setOrgPath(bdCrmMemberModel.getOrgPathForShortShow());
                    }
                    return model;
                }).collect(Collectors.toList());
    }


    //查找gps上报人员信息
    @ApiOperation("根据uniqueCode查询")
    @PostMapping("list-by-uniqueCode")
    public Response<CrowdfundingVolunteerAndOrgModel> listByUniqueCode(@RequestParam String uniqueCode) {
        if (StringUtils.isBlank(uniqueCode)) {
            return NewResponseUtil.makeSuccess(null);
        }
        CrowdfundingVolunteer volunteer = cfVolunteerService.getByUniqueCode(uniqueCode);
        if (volunteer == null) {
            return  NewResponseUtil.makeSuccess(null);
        }
        List<CrowdfundingVolunteerAndOrgModel> volunteerInfos = listVolunteerInfo(Lists.newArrayList(volunteer));
        if (CollectionUtils.isEmpty(volunteerInfos)) {
            return NewResponseUtil.makeSuccess(null);
        }
        //找到人员组织
        return NewResponseUtil.makeSuccess(volunteerInfos.get(0));
    }



    @ApiOperation("获取人员绑定的组织架构")
    @PostMapping("get-volunteer-org-relation")
    public Response<VolunteerOrgRelationVO> getVolunteerOrgRelation(@RequestParam(name="uniqueCode") String uniqueCode){
        VolunteerOrgRelationVO volunteerOrgRelationVO = new VolunteerOrgRelationVO();
        volunteerOrgRelationVO.setRelatedOrg(Lists.newArrayList());
        List<BdCrmOrgUserRelationDO> relationDOS = relationService.listMemberOrgRelationByUniqueCode(uniqueCode);
        if(CollectionUtils.isNotEmpty(relationDOS)){
            volunteerOrgRelationVO.setPreBinding(false);
            List<Long> orgIdList = relationDOS.stream().map(BdCrmOrgUserRelationDO::getOrgId).collect(Collectors.toList());
            Map<Long, String> mapStr = crmSelfBuiltOrgReadService.listChainByOrgIds(orgIdList,"/");
            for(Map.Entry<Long,String> entry: mapStr.entrySet()){
                volunteerOrgRelationVO.getRelatedOrg().add(entry.getValue());
            }
            return NewResponseUtil.makeSuccess(volunteerOrgRelationVO);
        }else{
            PreVolunteerOrgInfoRelationDO relationDO = preVolunteerOrgInfoRelationDao.getPreVolunteerOrgInfoRelationDO(uniqueCode);
            if(relationDO!=null){
                volunteerOrgRelationVO.setPreBinding(true);
                Map<Long, String> mapStr = crmSelfBuiltOrgReadService.listChainByOrgIds(Lists.newArrayList(Long.valueOf(relationDO.getOrgId())),"/");
                for(Map.Entry<Long,String> entry: mapStr.entrySet()){
                    volunteerOrgRelationVO.getRelatedOrg().add(entry.getValue());
                }
            }
            return NewResponseUtil.makeSuccess(volunteerOrgRelationVO);
        }
    }

    @ApiOperation("获取人员绑定的组织架构")
    @PostMapping("bind-volunteer-pre-org-relation")
    public Response<Void> bindVolunteerPreOrgRelation(@RequestParam(name="uniqueCode") String uniqueCode,
                                                                        @RequestParam(name="orgId") Long orgId,
                                                                        @RequestParam(name="orgName") String orgName){
        CrowdfundingVolunteer crowdfundingVolunteer = cfVolunteerService.getByUniqueCode(uniqueCode);
        if(crowdfundingVolunteer==null){
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<BdCrmOrgUserRelationDO> relationDOS = relationService.listMemberOrgRelationByUniqueCode(uniqueCode);
        if(CollectionUtils.isNotEmpty(relationDOS)){
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.ORG_HAS_BIND);
        }
        PreVolunteerOrgInfoRelationDO preVolunteerOrgInfoRelationDO = preVolunteerOrgInfoRelationDao.getPreVolunteerOrgInfoRelationDO(uniqueCode);
        if(preVolunteerOrgInfoRelationDO!=null){
            return NewResponseUtil.makeError(CfGrowthtoolErrorCode.ORG_HAS_BIND);
        }
        PreVolunteerOrgInfoRelationDO add = new PreVolunteerOrgInfoRelationDO();
        add.setOrgId(orgId.intValue());
        add.setOrgName(orgName);
        add.setUniqueCode(uniqueCode);
        preVolunteerOrgInfoRelationDao.add(add);
        return NewResponseUtil.makeSuccess(null);
    }

    @Getter
    public enum SceneEnum {
        default_scene(0, "默认", true),
        pre_assign_clew(1, "预分配线索", CrowdfundingVolunteerEnum.onlyRaiseCaseRole),
        ;
        int code;

        String desc;

        boolean partnerFilter;

        List<Integer> levelFilter = Lists.newArrayList();

        SceneEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        SceneEnum(int code, String desc, boolean partnerFilter) {
            this.code = code;
            this.desc = desc;
            this.partnerFilter = partnerFilter;
        }

        SceneEnum(int code, String desc, List<Integer> levelFilter) {
            this.code = code;
            this.desc = desc;
            this.levelFilter = levelFilter;
        }
    }

    public static SceneEnum parseSceneId(int sceneId) {
        for (SceneEnum value : SceneEnum.values()) {
            if (Objects.equals(value.code, sceneId)) {
                return value;
            }
        }
        return SceneEnum.default_scene;
    }




    @ApiOperation("模糊匹配顾问")
    @PostMapping("get-volunteer-by-name")
    public Response<List<CrowdfundingVolunteerAndOrgModel>> getVolunteerByName(@RequestParam(value = "name",required = false,defaultValue = "") String name,
                                                                               @RequestParam(value = "onwork",required = false,defaultValue = "0") int onwork,
                                                                               @RequestParam(value = "sceneId", required = false, defaultValue = "0") int sceneId) {
        List<CrowdfundingVolunteer> volunteers;
        if (StringUtils.isEmpty(name)) {
            volunteers = cfVolunteerService.listAllVolunteer();
        } else {
            volunteers = cfVolunteerService.fuzzyAllQueryByVolunteerName(name);
        }
        SceneEnum sceneEnum = parseSceneId(sceneId);
        //只返回正式顾问的
        List<CrowdfundingVolunteer> normalVolunteerList = volunteers
                .stream()
                .filter(item -> item.getAccountStatus() == CrowdfundingVolunteerEnum.AccountStatusEnum.DEFAULT.getValue())
                .filter(item -> {
                    boolean filterByScene = true;
                    if (sceneEnum.partnerFilter) {
                        filterByScene = Objects.equals(item.getPartnerTag(), CrowdfundingVolunteerEnum.PartnerTagEnum.normal.getCode());
                    }
                    if (CollectionUtils.isNotEmpty(sceneEnum.getLevelFilter())) {
                        filterByScene &= sceneEnum.getLevelFilter().contains(item.getLevel());
                    }
                    return filterByScene;
                })
                .collect(Collectors.toList());

        List<CrowdfundingVolunteerAndOrgModel> volunteerAndOrgModelList = generateByRelationList(normalVolunteerList);
        //按照uniqueCode分组
        Map<String, List<CrowdfundingVolunteerAndOrgModel>> uniqueCodeToModel = volunteerAndOrgModelList
                .stream()
                .collect(Collectors.groupingBy(CrowdfundingVolunteerAndOrgModel::getUniqueCode));
        //设置路径
        List<CrowdfundingVolunteerAndOrgModel> result = uniqueCodeToModel.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        List<Long> uniqueCodeOrgIds = result.stream().map(CrowdfundingVolunteerAndOrgModel::getOrgId).distinct().collect(Collectors.toList());
        Map<Long, String> orgIdToPath = crmSelfBuiltOrgReadService.listChainByOrgIdsWithDefaultSplitter(uniqueCodeOrgIds);
        for (CrowdfundingVolunteerAndOrgModel volunteerAndOrgModel : volunteerAndOrgModelList) {
            String orgPath = orgIdToPath.getOrDefault(volunteerAndOrgModel.getOrgId(), "");
            volunteerAndOrgModel.setOrgPath(orgPath);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    private List<CrowdfundingVolunteerAndOrgModel> generateByRelationList(List<CrowdfundingVolunteer> normalVolunteerList) {
        if (CollectionUtils.isEmpty(normalVolunteerList)) {
            return Lists.newArrayList();
        }
        List<CrowdfundingVolunteerAndOrgModel> result = Lists.newArrayList();
        List<BdCrmOrgUserRelationDO> relationList = Lists.newArrayList();
        List<String> uniqueCodeList = normalVolunteerList.stream().map(CrowdfundingVolunteer::getUniqueCode).collect(Collectors.toList());
        List<List<String>> onceQueryMisList = Lists.partition(uniqueCodeList, 500);
        onceQueryMisList.parallelStream().forEach(list -> relationList.addAll(relationService.listByUniqueCodes(list)));
        Map<String,List<BdCrmOrgUserRelationDO>> relationMap = relationList.stream().collect(Collectors.groupingBy(BdCrmOrgUserRelationDO::getUniqueCode));

        //只加在组织上的且非兼职
        for (CrowdfundingVolunteer volunteer : normalVolunteerList) {
            List<BdCrmOrgUserRelationDO> list = relationMap.get(volunteer.getUniqueCode());
            if (CollectionUtils.isNotEmpty(list)){
                list.forEach(item ->{
                    CrowdfundingVolunteerAndOrgModel volunteerAndOrgModel = new CrowdfundingVolunteerAndOrgModel(volunteer);
                    volunteerAndOrgModel.setOrgId(item.getOrgId());
                    result.add(volunteerAndOrgModel);
                });
            }else{
                CrowdfundingVolunteerAndOrgModel volunteerAndOrgModel = new CrowdfundingVolunteerAndOrgModel(volunteer);
                result.add(volunteerAndOrgModel);
            }
        }
        return result;
    }

    @ApiOperation("查看人员操作日志")
    @PostMapping("list-opt-log")
    public Response<BdCfCommonPageModel<WeaponOptLogModel>> listOptLog(@RequestParam(name = "id") long id,
                                                                       @RequestParam(name = "pageNum", defaultValue = "1") int pageNum,
                                                                       @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        List<Integer> enumList = OperateTypeEnum.getEnumListByAttriButeType(960000);

        BdCfCommonPageModel<WeaponOptLogModel> bdCfCommonPageModel = new BdCfCommonPageModel<WeaponOptLogModel>();
        OperatingRecordQueryParam queryParam = new OperatingRecordQueryParam();
        queryParam.setOperateTypeList(enumList);
        queryParam.setOperateKey(String.valueOf(id));
        queryParam.setOffset((pageNum - 1) * pageSize);
        queryParam.setPageSize(pageSize);
        int count = operateLogService.countRecordByQueryParam(queryParam);
        bdCfCommonPageModel.setCount(count);
        if (count == 0) {
            bdCfCommonPageModel.setList(Lists.newArrayList());
            return NewResponseUtil.makeSuccess(bdCfCommonPageModel);
        }
        OpResult<List<CfOperatingRecordDO>> opResult = operateLogService.listRecordByQueryParam(queryParam);
        if (opResult.isSuccess()) {
            List<WeaponOptLogModel> collect = org.apache.commons.collections4.CollectionUtils.isNotEmpty(opResult.getData()) ? opResult.getData().stream()
                    .map(WeaponOptLogModel::operateLogConvertModel).collect(Collectors.toList()) : Lists.newArrayList();
            bdCfCommonPageModel.setList(collect);
            return NewResponseUtil.makeSuccess(bdCfCommonPageModel);
        } else {
            return NewResponseUtil.makeFail(opResult.getMessage());
        }
    }
}
